
# User-specific files
*.suo
*.user
.github
.gflows
gflows
.git
*Dockerfile
# Build results
**/[Bb]in/
**/[Oo]bj/
**/[Oo]ut/

.editorconfig

# Visual Studio 2015 cache/options directory
.vs/
**/*.*proj.user

# Visual Studio Code
.vscode/

# JetBrains Rider
.idea/
*.sln.iml
*.DotSettings
_ReSharper.Caches/

tools
.dotnet

# .env file contains default environment variables for docker
.env

# Git
.git
.gitignore
.run/


# MacOS-specific
**/.DS_Store
.DS_Store

#CI-CD Jenkins
Jenkinsfile
Jenkinsfile*

#DockerFile itself
**/.dockerignore
#**/docker-compose*
**/docker-compose*.yml
**/Dockerfile*

#Readme and docs
*.md

# NPM
**/node_modules
**/npm-debug.log

helm/
