#!/bin/bash

#Global Variable

DNS_Server=$1
DNS_Namespace=$3
DNS_KEY=$RMHK_KEY

#Sanity Check

usage()
{
   echo "Usage: updateNS.sh <NS> <hostname> <zone> <add|del>"
   exit 1
}
if [ ! $# == 4 ]; then usage; fi

#Define Functions

AddHostToDNS () {
# First parameter : Hostname
# Second parameter : IP

local NewHost=$1
local NewIP=$2
local OutputFileTemp=$(mktemp)
cat >${OutputFileTemp} <<__TEMPEND__
server ${DNS_Server}
zone ${DNS_Namespace}
update delete ${NewHost}.${DNS_Namespace} A
send
answer
server ${DNS_Server}
zone ${DNS_Namespace}
update add ${NewHost}.${DNS_Namespace} 300 A ${NewIP}
send
answer
__TEMPEND__
echo "DNSUpdate: Adding ${NewHost}.${DNS_Namespace}"
echo "DNSUpdate: Generating commands on temporary file (${OutputFileTemp})"
nsupdate -y $DNS_KEY ${OutputFileTemp}
code=$?
echo "DNSUpdate: NSUpdate done."
rm ${OutputFileTemp}
exit $code
} 

RemoveHostFromDNS () {
# First parameter : Hostname

local NewHost=$1
local OutputFileTemp=$(mktemp)
cat >${OutputFileTemp} <<__TEMPEND__
server ${DNS_Server}
zone ${DNS_Namespace}
update delete ${NewHost}.${DNS_Namespace} A
send
answer
__TEMPEND__
echo "DNSUpdate: Generating commands on temporary file (${OutputFileTemp})"
nsupdate -y $DNS_KEY ${OutputFileTemp}
code=$?
echo "DNSUpdate: NSUpdate done (Entry removed)"
rm ${OutputFileTemp}
exit $code
}

#Main

if [ $4 == "add" ]; then
   myIpAddress="$(ip a sh eth0 | grep "inet\b" | awk '{print $2}' | cut -d/ -f1)"
   echo "DNSUpdate: Calling function AddHostToDNS"
   AddHostToDNS $2 $myIpAddress
elif [ $4 == "del" ]; then
   echo "DNSUpdate: Calling function RemoveHostFromDNS"
   RemoveHostFromDNS $2
else
   usage; exit 1
fi
