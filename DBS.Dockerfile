## Build

FROM registry.access.redhat.com/ubi8/dotnet-60 AS build
ARG GH_ACCOUNT
ARG GH_TOKEN
USER 0
WORKDIR ${HOME}

COPY ./nuget.config .
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text

COPY ./*.sln .
COPY ./src/Common/*.csproj ./src/Common/
COPY ./src/CoverGo.Gateway.Application/*.csproj ./src/CoverGo.Gateway.Application/
COPY ./src/CoverGo.Gateway.Domain/*.csproj ./src/CoverGo.Gateway.Domain/
COPY ./src/CoverGo.Gateway.Infrastructure/*.csproj ./src/CoverGo.Gateway.Infrastructure/
COPY ./src/CoverGo.Gateway.Interfaces/*.csproj ./src/CoverGo.Gateway.Interfaces/

RUN dotnet restore ./src/Common/Common.csproj
RUN dotnet restore ./src/CoverGo.Gateway.Application/Application.csproj
RUN dotnet restore ./src/CoverGo.Gateway.Domain/Domain.csproj
RUN dotnet restore ./src/CoverGo.Gateway.Infrastructure/Infrastructure.csproj
RUN dotnet restore ./src/CoverGo.Gateway.Interfaces/Interfaces.csproj

COPY ./src ./src
ARG BUILDCONFIG=Release
ARG VERSION=1.0.0-dbs
RUN dotnet publish ./src/CoverGo.Gateway.Application/Application.csproj -c $BUILDCONFIG -o ./out /p:Version=$VERSION

FROM registry.access.redhat.com/ubi8/dotnet-60-runtime:latest AS publish

ARG APP_DLL=CoverGo.Gateway.Application.dll

USER root
RUN yum install bind-utils -y
RUN yum install iproute -y
RUN yum install gnupg -y
RUN yum update -y

WORKDIR ${HOME}
COPY --from=build ${HOME}/out ./CoverGo.Gateway.Application/

WORKDIR ${HOME}/CoverGo.Gateway.Application

COPY ./DBS/entrypoint.sh entrypoint.sh
COPY ./DBS/updateNS.sh updateNS.sh
RUN chmod 777 entrypoint.sh
RUN chmod 777 updateNS.sh

EXPOSE 443
ENV APP_DLL $APP_DLL
ENV ASPNETCORE_URLS https://*:443
ENV GPG_PRIVATE_KEY -----BEGIN PGP PRIVATE KEY BLOCK-----\\n \\n lQOXBGByy+8BCADKGfsk9oImaZm/iKMY6t5047snte6n5q5mEBLB8JmDvXxVql4O\\n b0YbZLs2IqXZ0tV8ftKRM4euGh/ue1sB/AMUs5aQPKHoTfRhhfoCDMaI86/uruwC\\n UgtBeZ4x9oZNIt0A4UPgPpcaAW8VMxuC+SxeLSDP+sxwhqlUflkGMXUnjvCHEYgU\\n sfYIEBYSFpVydD9vckMFC7ktRBktFjw8j4ILClCMsTjYdaqcHnct3zsoBs2ni6zj\\n NCv7h9fxlN7ehwGxP6oGeJeZWkhutrvFxj1mzLWJns+lnnXSn7PuUUJOa8DRMJGk\\n je27oZQVwyNZdkptTPsieuGaURP2a7PzeL+TABEBAAEAB/dgLlKCGiQrzmsykVRi\\n sg7XPN9U3BXi0hCC13jAfkCQ8cdk/YTFry8uL5eTq+mXlemugKy1baX6Csonijfw\\n OdSS2uT3ao5N1XZNnfUyrzciS4GZlfsRH6U7rnrFobfzUyQvxwUGiMMLHU5ASJJP\\n QpuwNh88257FQ/unZSSjoztIwhAubrM2bKGetkYpXbUIz1jSUST9mTl4Akmc2NrQ\\n r40FGFJb3TkeHeCTOidoeYKepUDUstPOSH4Hk1ybLqKh4q4lxKeYbzr/sesQNRaM\\n bOel0eawYV8CJUGCfDhCoNFMMmz3KLoClqVB7I50WTevno8XmU1xi9h0+Gc96hXS\\n jwUEAN/Fv/ZYb+aCEKyjcKHre8ocv7xYTFie8GSC2bYT+M3TJ6wA7u0KrYstyCVy\\n k1lkYn+A1ch/l0iXCchaMtj6S90WsGdiTYQmznZRCii138NLfN3qnwPAE/wQXdIF\\n yXp4q0HiJXU6xzaQVGGMvD54cUrC+lBjQPX8n53weoFKPgtPBADnNT7oSBQpWHMz\\n xCdz6Vec+FmlMl2RcXhJ8nIsg+g7qnI1oLseqmW50ZN5XuwY5fwfn6HyvQh5rH3w\\n ZCkh8ou70rNlmR/ijxHGBDII5Mv1k09jrzez913LYXG6hQqYAz+AV+1KSOU03cKO\\n i1Ptj8OyLPfVu7EVSKFLjYElNKimfQP5AX+R/j3T0CucxcFaryKqU2GZY/6fpmQW\\n birVEWdnW9iUa1tMKkQYwA+6bJCfqmNhCIJfHa74Bl/6x8d1YRt/GRfnohT+CRdI\\n 80cKCZfemVcFBSEwMqw/xzIvIdOcrGl6p2Ldn0zIOa509QQHCsFkHhBSVaw5wCAI\\n r79+W2fN+DA7zrQaY292ZXJnbyA8dGVzdEBjb3ZlcmdvLmNvbT6JAVQEEwEIAD4W\\n IQS1qCCiDLGsXEIMyKy/IzxMcqIu8wUCYHLL7wIbAwUJA8JnAAULCQgHAgYVCgkI\\n CwIEFgIDAQIeAQIXgAAKCRC/IzxMcqIu87eeCACkGh4oj7NJ1jvKnh/TAEgcwd+U\\n F/e2j3OAyHtInkDqTUaeX95ZC6XP2oRD9lb+dVZn2chssx6JXzZj+2Xo9PDENwXd\\n PUylYiqXrVOC512LY5aIJEQsfieBQ3oJDa8NQ4Ee+2H08vHuZYJG5nueRc45KfNm\\n xW1EbCFtCRiorXZQn3Zx5oSPYFQA8JXUK+TK8ZPbYESflrXqh0kukC42aMnFG2P6\\n eKoJteL5NDfAtVEbCAflD/psscmtA7HunY+taubFjkDuo3T/b58yMAwBa1cgVd/F\\n EbHQ/QYW49AdqOQRwZJ1yesy5wLdh3CqYlzPQ7tGlO/5+V1YACq6C3WX0eGWnQOY\\n BGByy+8BCACqSZDO2wlLUkJ4bSAtiO72Pxd8fQ/ZhbDh5kyn4ZPoNqBJ3TjZUf7z\\n m5P7t5/h1OlML8HYuq7HPpmwNyT7HBQru3zt5QCb5lOD378TFpDG+gc1BX2NyMSC\\n gInJnXMzKZtw+paJxZ0Xu9it8z2p8acSCSQwP/0DaII+V49KbeZkgcO7Eax8YoB7\\n fxswU5p9wGunCGQ7ZT1wASEYxfJYy0MRJBk7Syvtsy9Jd19jYpgswQTGs7270pN5\\n dTDIOsKQqMZ7kKHHYZVvdSIJFTg60GFMsFVhzI64h4QVori4rHlcbGlxPWPExo+l\\n EknPKkxg/f8GG6PzpykCFZlJd//oQ5kTABEBAAEAB/0RaKvxOdtdY+9/uEhN323u\\n psyT4YU5S72lmpHDqAAjAVKpuoARV5yk8wbR+RfTUpz3OPqsWYl8vR2hMkIJmflF\\n 70j6WSsSxR1Mohc6sFfkv7L07LbFQ/0zCkkL35jjZiGKD1RMiXNh9cZVIkTvltDp\\n ux2EAUawjoKiNPllNo8TPzY+IsitdO1qImvgBm08FasQBHxfZi0CdNBfaGzyHKA1\\n 1jWiEf0OlNVXYpuE/89INIwao1WQcWwX4PnKsB6kkD4fSRdPCeh3RruBp7IXR96w\\n g8iYf9j6RULttsQfD7lrXMbQ51o98uZLNh3bsTC1Nt5SZXWcu6wQ+TnDMe+mMTOh\\n BADPATR1QgbvMpdnxtkmZfbzFjwB0TTeKXITzjNejBONJijnXrRbMslCD+nBOUHn\\n cpxvHi7gPSp4AEQMHu4zqbBAtNs9tUoaF4L3adW7CKnG221+DYcNwEpc3Q/h2aEY\\n HOPWTRtyJKggJej4I0Lktkgn3TsRE7lNbkC32zJwxDYtJwQA0peW9ONVMQi4ZdSu\\n XpmWJpTKE7VwjC9pMYFB3dP+Yoc9ZJestEfotPkO9XLnNpxYsJaonv5QNMtgdV8Z\\n xBAgT2J6czJLKMRWUgn1OHV5+fM62eqlH2Emyxmi0SWB9kxJrQ8ceXJgmqsplXYk\\n XwQirWMdtS3TihdftCzel4q5wDUD/3IwRuRf1E77hEKnN1fxE40R3RLni7C6NDEj\\n TqbvncTErDspIfjiKliqxbFydQDkZdj/sJhp20z6hB4aR3x77IB8vbDMGU9FAEW+\\n I0P3/f8kbVs1w356x3PP+80YK4h3cbLPTOkNTQooVG+FiOGn0/F+IFjpMflXPQdL\\n 0TjovFzgOOuJATwEGAEIACYWIQS1qCCiDLGsXEIMyKy/IzxMcqIu8wUCYHLL7wIb\\n DAUJA8JnAAAKCRC/IzxMcqIu8z7OCACphUNU9dElpQOsNrqj2+NtDaGArGpnTqSa\\n jY+mzL0weFMRjCesYlmFEX4RssAsG9on4/Gsu5VUGUDFvc33O7lCThAhSDnhKmiG\\n K2iHytFzh7JeTpNrdo+eZM051KeE75Uc8Sy5ckD3FciLuJh4n+VgP1agx+nyF4Ea\\n gf6IprjBql5Pms21AvpxnEURU4IrBaoP/6KuOSOQRVDNGarszPcOP8M4oyb7Lv81\\n TE2VBADH4Q4ZEPMg/wC2sq1xju6riLbDnDSFvj+oqiUtX4kgKv/gRv4ftyT2GpQX\\n OP5j421AngTjkgBrsQQ696y5iUaXZC//GEj4iWvkyPcCjngn6PRI\\n =eML8\\n -----END PGP PRIVATE KEY BLOCK-----
ENV datacenterId dbs-hk
ENV ASPNETCORE_Kestrel__Certificates__Default__KeyPath ${HOME}/CoverGo.Gateway.Application/server.key
ENV ASPNETCORE_Kestrel__Certificates__Default__Path ${HOME}/CoverGo.Gateway.Application/server.crt

ENTRYPOINT ["/bin/bash", "entrypoint.sh"]
# ENTRYPOINT ["dotnet", "CoverGo.Gateway.Application.dll"]
