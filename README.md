# Gateway

[![Vulnerabilities](https://sonarcloud.io/api/project_badges/measure?project=CoverGo_Gateway&metric=vulnerabilities&token=6845321decb7b7e26a307ff24e913e1d86c26335)](https://sonarcloud.io/summary/new_code?id=CoverGo_Gateway)
[![Bugs](https://sonarcloud.io/api/project_badges/measure?project=CoverGo_Gateway&metric=bugs&token=6845321decb7b7e26a307ff24e913e1d86c26335)](https://sonarcloud.io/summary/new_code?id=CoverGo_Gateway)
[![Code Smells](https://sonarcloud.io/api/project_badges/measure?project=CoverGo_Gateway&metric=code_smells&token=6845321decb7b7e26a307ff24e913e1d86c26335)](https://sonarcloud.io/summary/new_code?id=CoverGo_Gateway)
[![Coverage](https://sonarcloud.io/api/project_badges/measure?project=CoverGo_Gateway&metric=coverage&token=6845321decb7b7e26a307ff24e913e1d86c26335)](https://sonarcloud.io/summary/new_code?id=CoverGo_Gateway)
[![Technical Debt](https://sonarcloud.io/api/project_badges/measure?project=CoverGo_Gateway&metric=sqale_index&token=6845321decb7b7e26a307ff24e913e1d86c26335)](https://sonarcloud.io/summary/new_code?id=CoverGo_Gateway)

Before the first launch need to execute https://github.com/CoverGo/CoverGo.Workspace/blob/master/scripts/configure-private-feeds.ps1 or https://github.com/CoverGo/CoverGo.Workspace/blob/master/scripts/configure-private-feeds.sh depends on OS.
