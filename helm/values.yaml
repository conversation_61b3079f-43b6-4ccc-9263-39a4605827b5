covergo-app:
  namespace: "default"
  fullnameOverride: "covergo-gateway"
  labels:
    team: "backend"
  imagePullSecrets:
    - name: ghcr.io
  image:
    repository: ghcr.io/covergo/gateway
  deployment:
    name: gateway
    create: true
    env:
      - name: datacenterId
        value: 12factor
      - name: terminationTimeout
        value: "30"
      - name: ASPNETCORE_ENVIRONMENT
        value: "Development"
    startupProbe:
      enabled: true
      httpGet:
        path: /startupz
        port: 8080
    readinessProbe:
      enabled: true
      httpGet:
        path: /readyz
        port: 8080
    livenessProbe:
      enabled: true
      httpGet:
        path: /healthz
        port: 8080
    resources: 
      limits:
        cpu: 2000m
        memory: 2000M
      requests:
        cpu: 500m
        memory: 1500M
    ports:
      containerPort: 8080
  service:
    create: true
    type: ClusterIP
    port: 8080
    targetPort: 8080
  ports:
    containerPort: 8080
  autoscaling:
    enabled: true
    cooldownPeriod: 300
    maxReplicaCount: 6
    minReplicaCount: 2
    pollingInterval: 30
    triggers:
      - typeName: Utilization
        typeValue: "75"
        type: cpu
      - typeName: Utilization
        typeValue: "75"
        type: memory
