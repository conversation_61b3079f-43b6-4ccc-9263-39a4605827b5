{"profiles": {"Docker Compose": {"commandName": "DockerCompose", "commandVersion": "1.0", "composeLaunchAction": "None", "composeLaunchServiceName": "gateway", "serviceActions": {"auth": "DoNotStart", "claim-investigation": "DoNotStart", "claims": "DoNotStart", "coverhealth-admin": "DoNotStart", "coverhealth-portal-hr": "DoNotStart", "coverhealth-portal-member": "DoNotStart", "eventstore": "DoNotStart", "gateway": "StartDebugging", "l10n": "DoNotStart", "migration-tool": "DoNotStart", "mongo": "DoNotStart", "mongo-express": "DoNotStart", "notifications": "DoNotStart", "policies": "DoNotStart", "policies-predeployment": "DoNotStart", "pricing": "DoNotStart", "product-builder": "DoNotStart", "products": "DoNotStart", "redis": "DoNotStart", "scheduler": "DoNotStart", "scripts": "DoNotStart", "transactions": "DoNotStart", "users": "DoNotStart", "cases": "DoNotStart", "file-system": "DoNotStart", "minio": "DoNotStart", "mongo-secondary": "DoNotStart", "templates": "DoNotStart"}}}}