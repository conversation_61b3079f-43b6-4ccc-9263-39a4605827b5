<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <CoverGoInternalsVersion>3.74.10</CoverGoInternalsVersion>
    <HotChocolateVersion>12.19.2</HotChocolateVersion>
  </PropertyGroup>
  <ItemGroup>
    <!-- General -->
    <PackageVersion Include="AutoMapper" Version="13.0.1" />
    <PackageVersion Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageVersion Include="ClosedXML" Version="0.100.3" />
    <PackageVersion Include="Contrib-2.Microsoft.AspNetCore.Identity.MongoDB" Version="2.0.6" />
    <PackageVersion Include="CoverGo.FeatureManagement" Version="3.74.10" />
    <PackageVersion Include="CsvHelper" Version="27.1.1" />
    <PackageVersion Include="Fluid.Core" Version="1.0.0-beta-9637" />
    <PackageVersion Include="FormatWith" Version="2.2.1" />
    <PackageVersion Include="HtmlSanitizer" Version="5.0.372" />
    <PackageVersion Include="IpSet" Version="0.1.1" />
    <PackageVersion Include="JsonLogic.Net" Version="1.1.7" />
    <PackageVersion Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.13.1" />
    <PackageVersion Include="Microsoft.ApplicationInsights.Kubernetes" Version="1.1.1" />
    <PackageVersion Include="Microsoft.AspNetCore.DataProtection.StackExchangeRedis" Version="7.0.12" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.7" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="6.0.33" />
    <PackageVersion Include="Microsoft.AspNetCore.HeaderPropagation" Version="6.0.7" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Abstractions" Version="6.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Memory" Version="6.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="7.0.13" />
    <PackageVersion Include="Microsoft.Extensions.Http.Polly" Version="6.0.7" />
    <PackageVersion Include="Microsoft.FeatureManagement" Version="2.5.1" />
    <PackageVersion Include="Microsoft.FeatureManagement.AspNetCore" Version="2.5.1" />
    <PackageVersion Include="Microsoft.Identity.Web" Version="1.14.0" />
    <PackageVersion Include="Microsoft.IO.RecyclableMemoryStream" Version="3.0.1" />
    <PackageVersion Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.17.2" />
    <PackageVersion Include="morelinq" Version="3.2.0" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="NetEscapades.Configuration.Yaml" Version="2.2.0" />
    <PackageVersion Include="NodaTime" Version="3.0.9" />
    <PackageVersion Include="NWebsec.AspNetCore.Middleware" Version="2.0.0" />
    <PackageVersion Include="Pipedrive.net" Version="0.5.18" />
    <PackageVersion Include="ProxyKit" Version="2.3.2" />
    <PackageVersion Include="RestSharp" Version="106.12.0" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="7.2.0" />
    <PackageVersion Include="System.ServiceModel.Http" Version="4.9.0" />
    <PackageVersion Include="Otp.NET" Version="1.2.2" />
    <PackageVersion Include="OpenSSL.PrivateKeyDecoder" Version="1.3.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="6.3.1" />
    <PackageVersion Include="Stripe.net" Version="41.7.0" />
    <PackageVersion Include="System.Diagnostics.DiagnosticSource" Version="8.0.1" />
    <PackageVersion Include="System.Linq.Dynamic.Core" Version="1.2.7" />
    <!-- DI -->
    <PackageVersion Include="BouncyCastle" Version="1.8.9" />
    <PackageVersion Include="Scrutor" Version="5.0.1" />
    <!-- Logging -->
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="6.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Configuration" Version="6.0.0" />
    <PackageVersion Include="Serilog" Version="2.10.0" />
    <PackageVersion Include="Serilog.AspNetCore" Version="4.1.0" />
    <PackageVersion Include="Serilog.Enrichers.ClientInfo" Version="1.1.4" />
    <PackageVersion Include="Serilog.Extensions.Logging" Version="3.0.1" />
    <PackageVersion Include="Serilog.Formatting.Compact" Version="1.1.0" />
    <PackageVersion Include="Serilog.Settings.Configuration" Version="3.2.0" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="4.0.0" />
    <PackageVersion Include="Serilog.Sinks.ElasticSearch" Version="8.4.1" />
    <PackageVersion Include="Serilog.Sinks.SyslogMessages" Version="2.0.8" />
    <PackageVersion Include="Serilog.Expressions" Version="3.2.0" />
    <PackageVersion Include="Serilog.Enrichers.Span" Version="1.3.0" />
    <!-- Auth -->
    <PackageVersion Include="ComponentPro.Saml" Version="7.2.238" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Auth" Version="1.2.7" />
    <PackageVersion Include="IdentityModel" Version="6.0.0" />
    <PackageVersion Include="IdentityServer4.AccessTokenValidation" Version="2.7.0" />
    <!-- GraphQL -->
    <PackageVersion Include="GraphQL" Version="2.4.0" />
    <PackageVersion Include="GraphQL.Authorization" Version="2.1.29" />
    <PackageVersion Include="GraphQL.Client" Version="5.1.0" />
    <PackageVersion Include="GraphQL.Client.Serializer.Newtonsoft" Version="5.1.0" />
    <PackageVersion Include="GraphQL.Server.Transports.AspNetCore" Version="3.4.0" />
    <PackageVersion Include="GraphQL.Server.Transports.WebSockets" Version="3.4.0" />
    <PackageVersion Include="GraphQL.Server.Ui.GraphiQL" Version="3.4.0" />
    <PackageVersion Include="GraphQL.Server.Ui.Playground" Version="3.4.0" />
    <PackageVersion Include="GraphQL.Server.Ui.Voyager" Version="3.4.0" />
    <PackageVersion Include="Yarp.ReverseProxy" Version="2.0.0" />
    <PackageVersion Include="HotChocolate" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.AspNetCore" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.AspNetCore.Authorization" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.Subscriptions.Redis" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.Stitching" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.Types" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.Types.Scalars" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="StrawberryShake.Server" Version="13.5.1" />
    <!-- Testing -->
    <PackageVersion Include="AutoFixture" Version="4.17.0" />
    <PackageVersion Include="AutoFixture.AutoMoq" Version="4.17.0" />
    <PackageVersion Include="AutoFixture.Xunit2" Version="4.17.0" />
    <PackageVersion Include="FluentAssertions" Version="6.11.0" />
    <PackageVersion Include="JunitXml.TestLogger" Version="3.0.114" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.7.1" />
    <PackageVersion Include="Moq" Version="4.18.1" />
    <PackageVersion Include="xunit" Version="2.5.0" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.5.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageVersion>
    <PackageVersion Include="coverlet.collector" Version="6.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageVersion>
    <!-- CoverGo -->
    <PackageVersion Include="CoverGo.Applications.Clients" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Applications.HealthCheck" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Applications.Http.GraphQl.Schemas" Version="3.57.0-AzureADJWT2.10" />
    <PackageVersion Include="CoverGo.Applications.Http.GraphQl.Services" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Applications.Startup" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.DateUtils" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Extensions.Hosting.Abstractions" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.GraphQLGenerators" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.GraphQL.Client" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.HotChocolate.SchemaFederations" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.HotChocolate.TypeExtensions" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.JsonUtils" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.MongoUtils" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Proxies.Product" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.RateLimiting" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Sentry" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.SettableValues" Version="3.74.8" />
    <PackageVersion Include="CoverGo.Threading.Tasks" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.CryptoUtils" Version="$(CoverGoInternalsVersion)" />
    <!-- CoverGo clients -->
    <PackageVersion Include="CoverGo.Users.Client" Version="2.45.0" />
    <PackageVersion Include="CoverGo.Policies.Client" Version="2.346.0-rc.7" />
  </ItemGroup>
</Project>
