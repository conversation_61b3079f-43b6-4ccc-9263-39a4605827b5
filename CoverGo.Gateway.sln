﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.33516.290
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{2EA55BB6-D866-4640-BAED-39FED45A615A}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		docker-compose.yml = docker-compose.yml
		Dockerfile = Dockerfile
		nuget.config = nuget.config
		README.md = README.md
		Directory.Packages.props = Directory.Packages.props
		Directory.Build.props = Directory.Build.props
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Domain", "src\CoverGo.Gateway.Domain\Domain.csproj", "{36C7B863-BB9D-447E-A307-61FA41EAA395}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Infrastructure", "src\CoverGo.Gateway.Infrastructure\Infrastructure.csproj", "{A93EDBD8-BCCD-437F-AD07-FBD24DF430D8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Interfaces", "src\CoverGo.Gateway.Interfaces\Interfaces.csproj", "{14328EA3-BB6F-4C33-ABAF-255F050BE9EA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Application", "src\CoverGo.Gateway.Application\CoverGo.Gateway.Application.csproj", "{9039053E-BDA2-43AF-A16A-67D4DF2CB403}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Tests.Integration", "test\CoverGo.Gateway.Tests.Integration\Tests.Integration.csproj", "{AC348E4C-571F-4DC7-9752-420E7F50BE3D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Tests.Unit", "test\CoverGo.Gateway.Tests.Unit\Tests.Unit.csproj", "{94F83352-4B81-4256-96B7-E6ED1A25E2A9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Client", "src\CoverGo.Gateway.Client\Client.csproj", "{268E6ADB-9BC8-4A5F-9118-13B5821963B1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Common", "src\Common\Common.csproj", "{ADF50325-B85F-4228-B7A0-CEBD1AF63DBB}"
EndProject
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose\docker-compose.dcproj", "{504F3BEB-D0A9-45F6-8520-B691DCE047FE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{3DD4242F-A6F9-4DDF-9E0C-65FE046C2AA8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.Gateway.Tests.GatewayClient", "test\CoverGo.Gateway.Tests.GatewayClient\CoverGo.Gateway.Tests.GatewayClient.csproj", "{CA10C5A4-CC67-4E8A-8DF1-77014DD4EEC5}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{36C7B863-BB9D-447E-A307-61FA41EAA395}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{36C7B863-BB9D-447E-A307-61FA41EAA395}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{36C7B863-BB9D-447E-A307-61FA41EAA395}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{36C7B863-BB9D-447E-A307-61FA41EAA395}.Release|Any CPU.Build.0 = Release|Any CPU
		{A93EDBD8-BCCD-437F-AD07-FBD24DF430D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A93EDBD8-BCCD-437F-AD07-FBD24DF430D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A93EDBD8-BCCD-437F-AD07-FBD24DF430D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A93EDBD8-BCCD-437F-AD07-FBD24DF430D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{14328EA3-BB6F-4C33-ABAF-255F050BE9EA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{14328EA3-BB6F-4C33-ABAF-255F050BE9EA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{14328EA3-BB6F-4C33-ABAF-255F050BE9EA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{14328EA3-BB6F-4C33-ABAF-255F050BE9EA}.Release|Any CPU.Build.0 = Release|Any CPU
		{9039053E-BDA2-43AF-A16A-67D4DF2CB403}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9039053E-BDA2-43AF-A16A-67D4DF2CB403}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9039053E-BDA2-43AF-A16A-67D4DF2CB403}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9039053E-BDA2-43AF-A16A-67D4DF2CB403}.Release|Any CPU.Build.0 = Release|Any CPU
		{AC348E4C-571F-4DC7-9752-420E7F50BE3D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AC348E4C-571F-4DC7-9752-420E7F50BE3D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AC348E4C-571F-4DC7-9752-420E7F50BE3D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AC348E4C-571F-4DC7-9752-420E7F50BE3D}.Release|Any CPU.Build.0 = Release|Any CPU
		{94F83352-4B81-4256-96B7-E6ED1A25E2A9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{94F83352-4B81-4256-96B7-E6ED1A25E2A9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{94F83352-4B81-4256-96B7-E6ED1A25E2A9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{94F83352-4B81-4256-96B7-E6ED1A25E2A9}.Release|Any CPU.Build.0 = Release|Any CPU
		{268E6ADB-9BC8-4A5F-9118-13B5821963B1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{268E6ADB-9BC8-4A5F-9118-13B5821963B1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{268E6ADB-9BC8-4A5F-9118-13B5821963B1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{268E6ADB-9BC8-4A5F-9118-13B5821963B1}.Release|Any CPU.Build.0 = Release|Any CPU
		{ADF50325-B85F-4228-B7A0-CEBD1AF63DBB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ADF50325-B85F-4228-B7A0-CEBD1AF63DBB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ADF50325-B85F-4228-B7A0-CEBD1AF63DBB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ADF50325-B85F-4228-B7A0-CEBD1AF63DBB}.Release|Any CPU.Build.0 = Release|Any CPU
		{504F3BEB-D0A9-45F6-8520-B691DCE047FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{504F3BEB-D0A9-45F6-8520-B691DCE047FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{504F3BEB-D0A9-45F6-8520-B691DCE047FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{504F3BEB-D0A9-45F6-8520-B691DCE047FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA10C5A4-CC67-4E8A-8DF1-77014DD4EEC5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA10C5A4-CC67-4E8A-8DF1-77014DD4EEC5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA10C5A4-CC67-4E8A-8DF1-77014DD4EEC5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA10C5A4-CC67-4E8A-8DF1-77014DD4EEC5}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {38C9FAFF-9304-45B9-B58D-F381924CA0F1}
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{CA10C5A4-CC67-4E8A-8DF1-77014DD4EEC5} = {3DD4242F-A6F9-4DDF-9E0C-65FE046C2AA8}
	EndGlobalSection
EndGlobal
