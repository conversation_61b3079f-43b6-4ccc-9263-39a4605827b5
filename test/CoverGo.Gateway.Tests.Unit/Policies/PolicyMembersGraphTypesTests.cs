using System;
using System.Collections.Generic;
using CoverGo.Gateway.Domain.Context;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Policies.Client;
using GraphQL.Language.AST;
using GraphQL.Types;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Policies
{
    // Test implementation of CoverGoContext with a mock App property
    public class TestCoverGoContext : CoverGoContext
    {
        private TestApp _app = new TestApp();
        
        public new TestApp App
        {
            get { return _app; }
            set { _app = value; }
        }
    }
    
    // Test implementation of App class
    public class TestApp
    {
        public string DefaultTimeZone { get; set; } = "UTC";
    }

    public class PolicyMembersGraphTypesTests
    {
        #region PolicyMembersGraph Tests

        [Fact]
        public void PolicyMembersGraph_Constructor_SetsPropertiesCorrectly()
        {
            // Arrange
            string policyId = "policy123";
            var endorsementIds = new List<string> { "endorsement1", "endorsement2" };
            DateTime asOf = DateTime.Now;

            // Act
            var graph = new PolicyMembersGraph(policyId, endorsementIds, asOf);

            // Assert
            Assert.Equal(policyId, graph.PolicyId);
            Assert.Equal(endorsementIds, graph.EndorsementId_In);
            Assert.Equal(asOf, graph.AsOf);
            Assert.Null(graph.List);
        }

        #endregion

        #region PolicyMemberGraphType Tests

        [Fact]
        public void PolicyMemberGraphType_Constructor_RegistersAllFields()
        {
            // Arrange & Act
            var graphType = new PolicyMemberGraphType();

            // Assert
            Assert.True(graphType.HasField("Id"));
            Assert.True(graphType.HasField("MemberId"));
            Assert.True(graphType.HasField("CreatedAt"));
            Assert.True(graphType.HasField("LastModifiedAt"));
            Assert.True(graphType.HasField("DependentOf"));
            Assert.True(graphType.HasField("DependentOfPolicyMemberId"));
            Assert.True(graphType.HasField("InternalCode"));
            Assert.True(graphType.HasField("PolicyId"));
            Assert.True(graphType.HasField("EndorsementId"));
            Assert.True(graphType.HasField("Fields"));
            Assert.True(graphType.HasField("PlanId"));
            Assert.True(graphType.HasField("StartDate"));
            Assert.True(graphType.HasField("EndDate"));
            Assert.True(graphType.HasField("Timestamp"));
            Assert.True(graphType.HasField("CreatedById"));
            Assert.True(graphType.HasField("IsRemoved"));
            Assert.True(graphType.HasField("IsPrinted"));
            Assert.True(graphType.HasField("IsRenewed"));
            Assert.True(graphType.HasField("UnderwritingResult"));
            Assert.True(graphType.HasField("ValidationResult"));
            Assert.True(graphType.HasField("CertificateNumber"));
        }

        #endregion

        #region CustomDateGraphType Tests

        [Fact]
        public void CustomDateGraphType_ParseLiteral_HandlesISOFormat()
        {
            // Arrange
            var graphType = new CustomDateGraphType();
            var isoDateString = "2023-01-15";
            var stringValue = new StringValue(isoDateString);

            // Act
            var result = graphType.ParseLiteral(stringValue);

            // Assert
            Assert.IsType<DateTime>(result);
            Assert.Equal(new DateTime(2023, 1, 15), result);
        }

        [Fact]
        public void CustomDateGraphType_ParseLiteral_HandlesDDMMYYYYFormat()
        {
            // Arrange
            var graphType = new CustomDateGraphType();
            var ddmmyyyyDateString = "15/01/2023";
            var stringValue = new StringValue(ddmmyyyyDateString);

            // Act
            var result = graphType.ParseLiteral(stringValue);

            // Assert
            Assert.IsType<DateTime>(result);
            Assert.Equal(new DateTime(2023, 1, 15), result);
        }

        [Fact]
        public void CustomDateGraphType_ParseValue_HandlesISOFormat()
        {
            // Arrange
            var graphType = new CustomDateGraphType();
            var isoDateString = "2023-01-15";

            // Act
            var result = graphType.ParseValue(isoDateString);

            // Assert
            Assert.IsType<DateTime>(result);
            Assert.Equal(new DateTime(2023, 1, 15), result);
        }

        [Fact]
        public void CustomDateGraphType_ParseValue_HandlesDDMMYYYYFormat()
        {
            // Arrange
            var graphType = new CustomDateGraphType();
            var ddmmyyyyDateString = "15/01/2023";

            // Act
            var result = graphType.ParseValue(ddmmyyyyDateString);

            // Assert
            Assert.IsType<DateTime>(result);
            Assert.Equal(new DateTime(2023, 1, 15), result);
        }

        #endregion

        #region TimeZoneDateTimeConverter Tests

        [Fact]
        public void TimeZoneDateTimeConverter_CanConvert_ReturnsTrueForDateTimeTypes()
        {
            // Arrange
            var converter = new TimeZoneDateTimeConverter("UTC");

            // Act & Assert
            Assert.True(converter.CanConvert(typeof(DateTime)));
            Assert.True(converter.CanConvert(typeof(DateTime?)));
            Assert.False(converter.CanConvert(typeof(string)));
        }

        [Fact]
        public void TimeZoneDateTimeConverter_ReadJson_HandlesStringValue()
        {
            // Arrange
            var converter = new TimeZoneDateTimeConverter("UTC");
            var dateTimeString = "2023-01-15T12:30:45Z";
            var reader = new JsonTextReader(new System.IO.StringReader($"\"{dateTimeString}\""));
            reader.Read(); // Move to the value

            // Act
            var result = converter.ReadJson(reader, typeof(DateTime), null, new JsonSerializer());

            // Assert
            Assert.IsType<DateTime>(result);
            var dateTime = (DateTime)result;
            Assert.Equal(DateTimeKind.Utc, dateTime.Kind);
            Assert.Equal(new DateTime(2023, 1, 15, 12, 30, 45, DateTimeKind.Utc), dateTime);
        }

        [Fact]
        public void TimeZoneDateTimeConverter_ReadJson_HandlesNullValue()
        {
            // Arrange
            var converter = new TimeZoneDateTimeConverter("UTC");
            var reader = new JsonTextReader(new System.IO.StringReader("null"));
            reader.Read(); // Move to the value

            // Act
            var result = converter.ReadJson(reader, typeof(DateTime?), null, new JsonSerializer());

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void TimeZoneDateTimeConverter_ReadJson_HandlesInvalidValue()
        {
            // Arrange
            var converter = new TimeZoneDateTimeConverter("UTC");
            var reader = new JsonTextReader(new System.IO.StringReader("\"not-a-date\""));
            reader.Read(); // Move to the value

            // Act
            var result = converter.ReadJson(reader, typeof(DateTime?), null, new JsonSerializer());

            // Assert
            Assert.Null(result);
        }

        #endregion

        #region PolicyMemberInputGraph Tests

        [Fact]
        public void PolicyMemberInputGraph_Constructor_RegistersAllFields()
        {
            // Arrange & Act
            var graphType = new PolicyMemberInputGraph();

            // Assert
            Assert.True(graphType.HasField("MemberId"));
            Assert.True(graphType.HasField("Network"));
            Assert.True(graphType.HasField("NetworkID"));
            Assert.True(graphType.HasField("PlanId"));
            Assert.True(graphType.HasField("StartDate"));
            Assert.True(graphType.HasField("EndDate"));
            Assert.True(graphType.HasField("Fields"));
            Assert.True(graphType.HasField("CreatedAt"));
            Assert.True(graphType.HasField("DependentOf"));
            Assert.True(graphType.HasField("DependentOfPolicyMemberId"));
            Assert.True(graphType.HasField("InternalCode"));
            Assert.True(graphType.HasField("UnderwritingResult"));
            Assert.True(graphType.HasField("ValidationResult"));
        }

        #endregion

        #region PolicyMembersFilterInputGraph Tests

        [Fact]
        public void PolicyMembersFilterInputGraph_Constructor_RegistersAllFields()
        {
            // Arrange & Act
            var graphType = new PolicyMembersFilterInputGraph();

            // Assert
            Assert.True(graphType.HasField("IsRemoved"));
            Assert.True(graphType.HasField("MemberId_in"));
            Assert.True(graphType.HasField("PlanId_contains"));
            Assert.True(graphType.HasField("PlanId_in"));
            Assert.True(graphType.HasField("HavingStartDate"));
            Assert.True(graphType.HasField("IsTerminated"));
            Assert.True(graphType.HasField("IsPrinted"));
            Assert.True(graphType.HasField("IsRenewed"));
            Assert.True(graphType.HasField("InternalCode"));
            Assert.True(graphType.HasField("InternalCode_in"));
            Assert.True(graphType.HasField("InternalCode_contains"));
            Assert.True(graphType.HasField("Fields"));
            Assert.True(graphType.HasField("LastModifiedAt_lt"));
            Assert.True(graphType.HasField("LastModifiedAt_gt"));
            Assert.True(graphType.HasField("CreatedAt_lt"));
            Assert.True(graphType.HasField("CreatedAt_gt"));
            Assert.True(graphType.HasField("HavingMovementType"));
            Assert.True(graphType.HasField("HavingEndDate"));
            Assert.True(graphType.HasField("UnderwritingResult"));
            Assert.True(graphType.HasField("UnderwritingResult_in"));
            Assert.True(graphType.HasField("ValidationResult"));
            Assert.True(graphType.HasField("ValidationResult_in"));
            Assert.True(graphType.HasField("EndorsementId"));
            Assert.True(graphType.HasField("EndorsementId_in"));
        }

        #endregion
    }
}
