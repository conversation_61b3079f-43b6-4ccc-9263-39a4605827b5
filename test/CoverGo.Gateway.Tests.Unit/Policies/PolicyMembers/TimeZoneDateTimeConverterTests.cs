using System;
using CoverGo.Gateway.Interfaces.Policies;
using FluentAssertions;
using Newtonsoft.Json;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Policies.PolicyMembers
{
    public class TimeZoneDateTimeConverterTests
    {
        [Fact]
        public void GIVEN_object_containing_date_without_timezone_WHEN_deserializing_it_using_specific_time_zone_THEN_deserialize_to_utc_time_using_time_zone_provided()
        {
            TimeZoneDateTimeConverter converter = new("Asia/Hong_Kong"); // +8 hrs
            string jsonInput = @"{ ""field"": ""2021-09-28"" }";
            ObjectWithField<DateTime> deserializedObject = JsonConvert.DeserializeObject<ObjectWithField<DateTime>>(jsonInput, converter);

            string dateTime = deserializedObject.Field.ToString("o");

            dateTime.Should().Be("2021-09-27T16:00:00.0000000Z"); // 2021-09-28T00:00:00 - 8 hrs = 2021-09-27T16:00:00
        }

        [Fact]
        public void GIVEN_object_containing_date_time_without_timezone_WHEN_deserializing_it_using_specific_time_zone_THEN_deserialize_to_utc_time_using_time_zone_provided()
        {
            TimeZoneDateTimeConverter converter = new("Asia/Hong_Kong"); // + 8 hrs
            string jsonInput = @"{ ""field"": ""2021-09-28T18:00:00"" }";
            ObjectWithField<DateTime> deserializedObject = JsonConvert.DeserializeObject<ObjectWithField<DateTime>>(jsonInput, converter);

            string dateTime = deserializedObject.Field.ToString("o");

            dateTime.Should().Be("2021-09-28T10:00:00.0000000Z"); // 2021-09-28T18:00:00 - 8 hrs = 2021-09-28T10:00:00
        }

        [Fact]
        public void GIVEN_object_containing_date_time_and_timezone_WHEN_deserializing_it_using_specific_time_zone_THEN_deserialize_to_utc_time_using_time_zone_provided()
        {
            TimeZoneDateTimeConverter converter = new("Asia/Hong_Kong"); // + 8 hrs
            string jsonInput = @"{ ""field"": ""2021-09-28T18:00:00.000+05:00"" }"; // +5 hrs
            ObjectWithField<DateTime> deserializedObject = JsonConvert.DeserializeObject<ObjectWithField<DateTime>>(jsonInput, converter);

            string dateTime = deserializedObject.Field.ToString("o");

            dateTime.Should().Be("2021-09-28T13:00:00.0000000Z"); // 2021-09-28T18:00:00 - 5 hrs = 2021-09-28T13:00:00
        }

        [Fact]
        public void GIVEN_object_containing_date_time_and_utc_timezone_WHEN_deserializing_it_using_specific_time_zone_THEN_deserialize_to_utc_time_zone()
        {
            TimeZoneDateTimeConverter converter = new("Asia/Hong_Kong"); // + 8 hrs
            string jsonInput = @"{ ""field"": ""2021-09-28T13:00:00.0000000Z"" }"; // +0 hrs
            ObjectWithField<DateTime> deserializedObject = JsonConvert.DeserializeObject<ObjectWithField<DateTime>>(jsonInput, converter);

            string dateTime = deserializedObject.Field.ToString("o");

            dateTime.Should().Be("2021-09-28T13:00:00.0000000Z"); // 2021-09-28T18:00:00 - 0 hrs = 2021-09-28T18:00:00
        }

        [Fact]
        public void GIVEN_json_input_with_incorrect_date_format_WHEN_deserializong_it_to_DateTime_THEN_field_is_set_to_DateTime_Min_value()
        {
            TimeZoneDateTimeConverter converter = new("Asia/Hong_Kong"); // + 8 hrs
            string jsonInput = @"{ ""field"": ""something wrong"" }"; // wrong input
            ObjectWithField<DateTime> deserializedObject = JsonConvert.DeserializeObject<ObjectWithField<DateTime>>(jsonInput, converter);

            string dateTime = deserializedObject.Field.ToString("o");

            dateTime.Should().Be("0001-01-01T00:00:00.0000000"); // 2021-09-28T18:00:00 - 0 hrs = 2021-09-28T18:00:00
        }

        [Fact]
        public void GIVEN_json_input_with_тгдд_WHEN_deserializong_it_to_DateTime_THEN_field_is_set_to_DateTime_Min_value()
        {
            TimeZoneDateTimeConverter converter = new("Asia/Hong_Kong"); // + 8 hrs
            string jsonInput = @"{ ""field"": null }"; // wrong input
            ObjectWithField<DateTime> deserializedObject = JsonConvert.DeserializeObject<ObjectWithField<DateTime>>(jsonInput, converter);

            string dateTime = deserializedObject.Field.ToString("o");

            dateTime.Should().Be("0001-01-01T00:00:00.0000000"); // 2021-09-28T18:00:00 - 0 hrs = 2021-09-28T18:00:00
        }

        [Fact]
        public void GIVEN_json_input_with_incorrect_date_format_WHEN_deserializong_it_to_DateTimeNullable_THEN_field_is_set_to_null()
        {
            TimeZoneDateTimeConverter converter = new("Asia/Hong_Kong"); // + 8 hrs
            string jsonInput = @"{ ""field"": ""something wrong"" }"; // wrong input
            ObjectWithField<DateTime?> deserializedObject = JsonConvert.DeserializeObject<ObjectWithField<DateTime?>>(jsonInput, converter);

            deserializedObject.Field.Should().BeNull();
        }

        [Fact]
        public void GIVEN_json_input_with_null_value_WHEN_deserializong_it_to_DateTimeNullable_THEN_field_is_set_to_null()
        {
            TimeZoneDateTimeConverter converter = new("Asia/Hong_Kong"); // + 8 hrs
            string jsonInput = @"{ ""field"": null }"; // wrong input
            ObjectWithField<DateTime?> deserializedObject = JsonConvert.DeserializeObject<ObjectWithField<DateTime?>>(jsonInput, converter);

            deserializedObject.Field.Should().BeNull();
        }
    }
}