using CoverGo.Gateway.Interfaces.Policies.Pricing;
using Newtonsoft.Json.Linq;
using Xunit;
using GraphQL.Language.AST;

namespace CoverGo.Gateway.Tests.Unit.Policies
{
    public class AnyTypeTests
    {
        private readonly AnyType _anyType = new AnyType();
        [Fact]
        public void ShouldSerializeJsonObject()
        {
            // Arrange
            object meta = new SomeMeta
            {
                netPremium = 123.4,
                insureds = new[] {
                    new SomeInsured { memberId = "merber1", netPremium = "71" },
                    new SomeInsured { memberId = "merber2", netPremium = "52" }
                }
            };

            // Act
            var result = _anyType.Serialize(meta);

            // Assert
            Assert.IsType<JObject>(result);
            Assert.Equal(new JObject
            {
                { "netPremium", 123.4 },
                { "insureds", new JArray {
                    new JObject{{ "memberId", "merber1" },{ "netPremium", "71" }},
                    new JObject{{ "memberId", "merber2" },{ "netPremium", "52" }},
                } }
            }.ToString(), result.ToString());
        }


        private string json = new JObject {
            { "i", 1 }, { "f", 3.14}, { "b", true}, { "s", "abc" },
            { "a", new JObject { {"b","c"} }},
            { "xs", new JArray { 1, 2 }},
            }.ToString();
        [Fact]
        public void ShouldParseJsonString()
        {
            // Act
            var result = _anyType.ParseValue(json);

            // Assert
            Assert.IsType<JObject>(result);
            Assert.Equal(json, result.ToString());
        }

        [Fact]
        public void ShouldParseLiteralObject()
        {
            // Arrange
            ObjectValue value = new(new ObjectField[]
            {
                new("i", new IntValue(1)),
                new("f", new FloatValue(3.14)),
                new("b", new BooleanValue(true)),
                new("s", new StringValue("abc")),
                new("a", new ObjectValue(new ObjectField[]{ new ("b", new StringValue("c")) })),
                new("xs", new ListValue(new IntValue[] { new(1), new(2) })),
            });

            // Act
            var result = _anyType.ParseLiteral(value);

            // Assert
            Assert.IsType<JObject>(result);
            Assert.Equal(json, result.ToString());
        }
    }

    class SomeMeta
    {
        public double netPremium { get; set; }
        public SomeInsured[] insureds { get; set; }
    }
    class SomeInsured
    {
        public string memberId { get; set; }
        public string netPremium { get; set; }
    }
}
