using FluentAssertions;
using FluentAssertions.Numeric;
using Microsoft.Extensions.Caching.Memory;

namespace CoverGo.Gateway.Tests.Unit.Spies;

public class MemoryCacheSpy : IMemoryCache
{
    private readonly MemoryCache _cache = new(new MemoryCacheOptions());
    private int _cacheLoadCount;        

    public AndConstraint<NumericAssertions<int>> ShouldLoadCache(int times)
    {
        return _cacheLoadCount.Should().Be(times);
    }

    public ICacheEntry CreateEntry(object key)
    {
        _cacheLoadCount++;
        return _cache.CreateEntry(key);
    }

    public void Remove(object key)
    {
        _cache.Remove(key);
    }

    public bool TryGetValue(object key, out object value)
    {
        return _cache.TryGetValue(key, out value);
    }

    public void Dispose()
    {
        _cache.Dispose();
    }
}