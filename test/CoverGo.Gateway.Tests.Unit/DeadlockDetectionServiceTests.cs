﻿using System;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Gateway.Application.DeadlockDetection;
using FluentAssertions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit;

public class DeadlockDetectionServiceTests
{
    private readonly Mock<ILogger<DeadlockDetectionService>> _mockLogger;
    private readonly Mock<IHost> _mockLifetime;
    private readonly Mock<IRequestTracker> _mockRequestTracker;
    private readonly DeadlockDetectionOptions _options;

    public DeadlockDetectionServiceTests()
    {
        _mockLogger = new Mock<ILogger<DeadlockDetectionService>>();
        _mockLifetime = new Mock<IHost>();
        _mockRequestTracker = new Mock<IRequestTracker>();
        _options = new DeadlockDetectionOptions
        {
            QueryTimeoutMinutes = 10,
            QueryCountThreshold = 10,
            AllowHealthCheckUpdate = true,
        };
    }

    [Fact]
    public async Task GIVEN_TenSlowRequests_WHEN_NoStop_AND_ServiceStart_THEN_status_should_be_uhealthy()
    {
        // Arrange
        _options.AllowHealthCheckUpdate = true;
        _mockRequestTracker.Setup(tracker => tracker.GetStuckRequestCount(It.IsAny<TimeSpan>()))
                           .Returns(10);  // Always returns 10, meeting threshold

        var options = Options.Create(_options);
        var status = new DeadlockHealthStatus { IsUnhealthy = false };
        var service = new DeadlockDetectionService(_mockLogger.Object, options, _mockLifetime.Object, _mockRequestTracker.Object, status);

        using var cancellationTokenSource = new CancellationTokenSource();

        // Act
        await service.StartAsync(cancellationTokenSource.Token);
        cancellationTokenSource.Cancel();

        // Assert
        status.IsUnhealthy.Should().BeTrue();
    }

    [Fact]
    public async Task GIVEN_LessThanThreshold_WHEN_ServiceStart_THEN_status_should_be_healthy()
    {
        // Arrange
        _options.AllowHealthCheckUpdate = true;
        _mockRequestTracker.Setup(tracker => tracker.GetStuckRequestCount(It.IsAny<TimeSpan>()))
                           .Returns(8);  // Always below threshold

        var options = Options.Create(_options);
        var status = new DeadlockHealthStatus { IsUnhealthy = false };
        var service = new DeadlockDetectionService(_mockLogger.Object, options, _mockLifetime.Object, _mockRequestTracker.Object, status);

        using var cancellationTokenSource = new CancellationTokenSource();

        // Act
        await service.StartAsync(cancellationTokenSource.Token);
        cancellationTokenSource.Cancel();

        // Assert
        status.IsUnhealthy.Should().BeFalse();
    }

    [Fact]
    public async Task GIVEN_TenSlowRequests_WHEN_AllowHealthCheckUpdateFalse_THEN_status_should_be_healthy()
    {
        // Arrange
        _options.AllowHealthCheckUpdate = false;
        _mockRequestTracker.Setup(tracker => tracker.GetStuckRequestCount(It.IsAny<TimeSpan>()))
                           .Returns(10);  // Meets threshold but should not restart

        var options = Options.Create(_options);
        var status = new DeadlockHealthStatus { IsUnhealthy = false };
        var service = new DeadlockDetectionService(_mockLogger.Object, options, _mockLifetime.Object, _mockRequestTracker.Object, status);

        using var cancellationTokenSource = new CancellationTokenSource();

        // Act
        await service.StartAsync(cancellationTokenSource.Token);
        cancellationTokenSource.Cancel();

        // Assert
        status.IsUnhealthy.Should().BeFalse();
    }
}
