using System;
using CoverGo.Gateway.Common;
using CoverGo.Gateway.Interfaces;
using FluentAssertions;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Common;

public class UrlValidatorTests
{
    [Theory]
    [InlineData("http://example.com", true)] // Valid HTTP URL
    [InlineData("https://example.com", true)] // Valid HTTPS URL
    [InlineData("ftp://example.com", false)] // Invalid FTP URL
    [InlineData("invalid-url", false)] // Invalid URL without scheme
    [InlineData("", false)] // Empty URL
    [InlineData(null, false)] // Null URL
    public void GIVEN_return_url_WHEN_IsReturnUrlValid_invoked_THEN_returns_expected_result(string returnUrl, bool expectedResult)
    {
        bool isValid = UrlValidator.IsReturnUrlValid(returnUrl);

        isValid.Should().Be(expectedResult);
    }

    public static object[][] ValidateURLTestData = new object[][]
    {
        new[] {
            new[]
            {
                "https://api.dev.covergo.cloud",
                "https://cover-health-admin-dev.quote.hk/"
            },
            Array.Empty<string>()
        },
        new[]
        {
            new[]
            {
                "https://admin.msig.dev.ap-east-1.covergo.cloud",
                "http://admin.msig.dev.ap-east-1.covergo.cloud"
            },
            new[]
            {
                "Unauthorized url http://admin.msig.dev.ap-east-1.covergo.cloud"
            }
        },
        new[]
        {
            new[]
            {
                "http://localhost:80",
                "http://localhost:9999",
                "https://bad.website.com"
            },
            new[]
            {
                "Unauthorized url http://localhost:9999",
                "Unauthorized url https://bad.website.com"
            }
        },
    };

    public static object[][] ValidateDomainURLTestData = new object[][]
    {
        new[]
        {
            new[]
            {
                "https://pentest-blaze-member.asia.covergo.cloud",
                "https://pentest-blaze-member.asia.covergo.cloud?q=123",
                "https://<EMAIL>"
            },
            new[]
            {
                "Unauthorized url https://<EMAIL>"
            }
        }
    };


    [Theory]
    [MemberData(nameof(ValidateURLTestData))]
    public void GIVEN_urls_WHEN_validate_urls_with_whitelist_THEN_should_return_unauthorized_urls(string[] urls, string[] expected_authorized_urls)
    {
        var actual_unauthorized_urls = Tools.ValidateUrls(urls);
        actual_unauthorized_urls.Should().BeEquivalentTo(expected_authorized_urls);
    }

    [Theory]
    [MemberData(nameof(ValidateDomainURLTestData))]
    public void GIVEN_urls_WHEN_validate_urls_with_whitelist_domain_THEN_should_return_unauthorized_urls(string[] urls, string[] expected_authorized_urls)
    {
        var actual_unauthorized_urls = Tools.ValidateUrls(urls);
        actual_unauthorized_urls.Should().BeEquivalentTo(expected_authorized_urls);
    }
}