using System;
using System.Collections.Generic;
using CoverGo.Gateway.Common;
using FluentAssertions;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Common;

public class ObjectExtensionTests
{
    [Fact]
    public void GIVEN_null_object_WHEN_checked_if_all_properties_are_default_THEN_returns_true()
    {
        TestClass testObject = null;

        bool result = testObject.AreAllPropertiesDefault();

        result.Should().BeTrue();
    }

    [Fact]
    public void GIVEN_object_with_all_properties_default_WHEN_checked_if_all_properties_are_default_THEN_returns_true()
    {
        TestClass testObject = new();

        bool result = testObject.AreAllPropertiesDefault();

        result.Should().BeTrue();
    }

    [Fact]
    public void GIVEN_object_with_empty_string_property_WHEN_checked_if_all_properties_are_default_THEN_returns_true()
    {
        TestClass testObject = new() { StringProperty = string.Empty };

        bool result = testObject.AreAllPropertiesDefault();

        result.Should().BeTrue();
    }

    [Fact]
    public void GIVEN_object_with_empty_list_property_WHEN_checked_if_all_properties_are_default_THEN_returns_true()
    {
        TestClass testObject = new() { ListProperty = new List<int>() };

        bool result = testObject.AreAllPropertiesDefault();

        result.Should().BeTrue();
    }

    [Fact]
    public void GIVEN_object_with_non_empty_string_property_WHEN_checked_if_all_properties_are_default_THEN_returns_false()
    {
        TestClass testObject = new() { StringProperty = "Not empty" };

        bool result = testObject.AreAllPropertiesDefault();

        result.Should().BeFalse();
    }

    [Fact]
    public void GIVEN_object_with_non_empty_list_property_WHEN_checked_if_all_properties_are_default_THEN_returns_false()
    {
        TestClass testObject = new() { ListProperty = new List<int> { 1 } };

        bool result = testObject.AreAllPropertiesDefault();

        result.Should().BeFalse();
    }

    [Fact]
    public void GIVEN_object_with_non_default_int_property_WHEN_checked_if_all_properties_are_default_THEN_returns_false()
    {
        TestClass testObject = new() { IntProperty = 1 };

        bool result = testObject.AreAllPropertiesDefault();

        result.Should().BeFalse();
    }

    public class TestClass
    {
        public int IntProperty { get; set; }
        public string StringProperty { get; set; }
        public DateTime DateTimeProperty { get; set; }
        public List<int> ListProperty { get; set; }
    }

}
