﻿using System.Collections.Generic;
using CoverGo.Gateway.Infrastructure.RemoteServices.InMemory;

using Xunit;

namespace CoverGo.Gateway.Tests.Unit.RemoteServices;

public class InMemoryRemoteServicesFilterTest
{
    private string service = "service";
    private string tenantId = "test";

    [Fact]
    public void GIVEN_null_filters_THEN_true()
    {
        var filter = new InMemoryRemoteServicesFilter(null);

        var result = filter.CanUseService(service, tenantId);

        Assert.True(result);
    }

    [Fact]
    public void GIVEN_no_filters_THEN_true()
    {
        var filter = new InMemoryRemoteServicesFilter(new Dictionary<string, RemoteServiceFilterDefinition>());

        var result = filter.CanUseService(service, tenantId);

        Assert.True(result);
    }

    [Fact]
    public void GIVEN_tenant_listed_WHEN_enableOn_THEN_true()
    {
        var filters = new Dictionary<string, RemoteServiceFilterDefinition>()
        {
            [service] = new(
                FilterMode.EnableOn,
                new HashSet<string?> { tenantId, "tenantId2" })
        };

        var filter = new InMemoryRemoteServicesFilter(filters);

        var result = filter.CanUseService(service, tenantId);

        Assert.True(result);
    }

    [Fact]
    public void GIVEN_tenant_not_listed_WHEN_enableOn_THEN_false()
    {
        var filters = new Dictionary<string, RemoteServiceFilterDefinition>()
        {
            [service] = new(
                FilterMode.EnableOn,
                new HashSet<string?> { "tenantId1", "tenantId2" })
        };

        var filter = new InMemoryRemoteServicesFilter(filters);

        var result = filter.CanUseService(service, tenantId);

        Assert.False(result);
    }

    [Fact]
    public void GIVEN_tenant_listed_WHEN_disableOn_THEN_false()
    {
        var filters = new Dictionary<string, RemoteServiceFilterDefinition>()
        {
            [service] = new(
                FilterMode.DisableOn,
                new HashSet<string?> { tenantId, "tenantId2" })
        };

        var filter = new InMemoryRemoteServicesFilter(filters);

        var result = filter.CanUseService(service, tenantId);

        Assert.False(result);
    }

    [Fact]
    public void GIVEN_tenant_not_listed_WHEN_disableOn_THEN_true()
    {
        var filters = new Dictionary<string, RemoteServiceFilterDefinition>()
        {
            ["service"] = new(
                FilterMode.DisableOn,
                new HashSet<string?> { "tenantId1", "tenantId2" })
        };

        var filter = new InMemoryRemoteServicesFilter(filters);

        var result = filter.CanUseService(service, tenantId);

        Assert.True(result);
    }
}
