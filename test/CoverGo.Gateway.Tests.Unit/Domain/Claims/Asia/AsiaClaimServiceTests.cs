using System;
using System.Collections.Generic;
using CoverGo.Gateway.Domain.Claims;
using CoverGo.Gateway.Domain.Claims.Asia;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Domain.Claims.Asia;

public class AsiaClaimServiceTests
{
    private readonly AsiaClaimService _asiaClaimService;

    public AsiaClaimServiceTests()
    {
        _asiaClaimService = new AsiaClaimService();
    }

    [Fact]
    public void GetClaimApprovalAmount_WhenBillingDetailIsNull_ReturnsZero()
    {
        // Arrange
        var claim = new Claim
        {
            Fields = new JObject()
        };

        // Act
        var result = _asiaClaimService.GetClaimApprovalAmount(claim);

        // Assert
        Assert.Equal(0m, result);
    }

    [Fact]
    public void GetClaimApprovalAmount_WhenBillingDetailIsEmptyArray_ReturnsZero()
    {
        // Arrange
        var claim = new Claim
        {
            Fields = new JObject
            {
                ["billingDetail"] = new JArray()
            }
        };

        // Act
        var result = _asiaClaimService.GetClaimApprovalAmount(claim);

        // Assert
        Assert.Equal(0m, result);
    }

    [Fact]
    public void GetClaimApprovalAmount_WhenBenefitListIsEmpty_ReturnsZero()
    {
        // Arrange
        var claim = new Claim
        {
            Fields = new JObject
            {
                ["billingDetail"] = new JArray
                {
                    new JObject
                    {
                        ["benefitList"] = new JArray()
                    }
                }
            }
        };

        // Act
        var result = _asiaClaimService.GetClaimApprovalAmount(claim);

        // Assert
        Assert.Equal(0m, result);
    }

    [Fact]
    public void GetClaimApprovalAmount_WhenBenefitValuesAreZero_ReturnsZero()
    {
        // Arrange
        var claim = new Claim
        {
            Fields = new JObject
            {
                ["billingDetail"] = new JArray
                {
                    new JObject
                    {
                        ["benefitList"] = new JArray
                        {
                            new JObject
                            {
                                ["assessedAmount"] = 0m,
                                ["exGratiaAmount"] = 0m,
                                ["exclusionAmount"] = 0m
                            }
                        }
                    }
                }
            }
        };

        // Act
        var result = _asiaClaimService.GetClaimApprovalAmount(claim);

        // Assert
        Assert.Equal(0m, result);
    }

    [Fact]
    public void GetClaimApprovalAmount_WithOneBenefit_CalculatesCorrectly()
    {
        // Arrange
        var claim = new Claim
        {
            Fields = new JObject
            {
                ["billingDetail"] = new JArray
                {
                    new JObject
                    {
                        ["benefitList"] = new JArray
                        {
                            new JObject
                            {
                                ["assessedAmount"] = 100.5m,
                                ["exGratiaAmount"] = 50.3m,
                                ["exclusionAmount"] = 25.8m
                            }
                        }
                    }
                }
            }
        };

        // Act
        var result = _asiaClaimService.GetClaimApprovalAmount(claim);

        // Assert
        Assert.Equal(125m, result); // 100.5 + 50.3 - 25.8 = 125, rounded to 2 decimal places
    }

    [Fact]
    public void GetClaimApprovalAmount_WithMultipleBenefits_CalculatesCorrectly()
    {
        // Arrange
        var claim = new Claim
        {
            Fields = new JObject
            {
                ["billingDetail"] = new JArray
                {
                    new JObject
                    {
                        ["benefitList"] = new JArray
                        {
                            new JObject
                            {
                                ["assessedAmount"] = 100.5m,
                                ["exGratiaAmount"] = 50.3m,
                                ["exclusionAmount"] = 25.8m
                            },
                            new JObject
                            {
                                ["assessedAmount"] = 200m,
                                ["exGratiaAmount"] = 75m,
                                ["exclusionAmount"] = 50m
                            }
                        }
                    }
                }
            }
        };

        // Act
        var result = _asiaClaimService.GetClaimApprovalAmount(claim);

        // Assert
        Assert.Equal(350m, result); // (100.5 + 50.3 - 25.8) + (200 + 75 - 50) = 350, rounded to 2 decimal places
    }

    [Fact]
    public void GetClaimApprovalAmount_WithMultipleBillingDetails_CalculatesCorrectly()
    {
        // Arrange
        var claim = new Claim
        {
            Fields = new JObject
            {
                ["billingDetail"] = new JArray
                {
                    new JObject
                    {
                        ["benefitList"] = new JArray
                        {
                            new JObject
                            {
                                ["assessedAmount"] = 100m,
                                ["exGratiaAmount"] = 50m,
                                ["exclusionAmount"] = 25m
                            }
                        }
                    },
                    new JObject
                    {
                        ["benefitList"] = new JArray
                        {
                            new JObject
                            {
                                ["assessedAmount"] = 200m,
                                ["exGratiaAmount"] = 75m,
                                ["exclusionAmount"] = 50m
                            }
                        }
                    }
                }
            }
        };

        // Act
        var result = _asiaClaimService.GetClaimApprovalAmount(claim);

        // Assert
        Assert.Equal(350m, result); // (100 + 50 - 25) + (200 + 75 - 50) = 350
    }

    [Fact]
    public void GetClaimApprovalAmount_WithNullValues_IgnoresNulls()
    {
        // Arrange
        var claim = new Claim
        {
            Fields = new JObject
            {
                ["billingDetail"] = new JArray
                {
                    new JObject
                    {
                        ["benefitList"] = new JArray
                        {
                            new JObject
                            {
                                ["assessedAmount"] = 100m,
                                // exGratiaAmount is missing
                                ["exclusionAmount"] = 25m
                            }
                        }
                    }
                }
            }
        };

        // Act
        var result = _asiaClaimService.GetClaimApprovalAmount(claim);

        // Assert
        Assert.Equal(75m, result); // 100 + 0 - 25 = 75
    }

    [Fact]
    public void GetClaimApprovalAmount_WithMixedDecimalValues_RoundsToTwoDecimalPlaces()
    {
        // Arrange
        var claim = new Claim
        {
            Fields = new JObject
            {
                ["billingDetail"] = new JArray
                {
                    new JObject
                    {
                        ["benefitList"] = new JArray
                        {
                            new JObject
                            {
                                ["assessedAmount"] = 100.333m,
                                ["exGratiaAmount"] = 50.667m,
                                ["exclusionAmount"] = 25.555m
                            }
                        }
                    }
                }
            }
        };

        // Act
        var result = _asiaClaimService.GetClaimApprovalAmount(claim);

        // Assert
        Assert.Equal(125.44m, result); // 100.333 + 50.667 - 25.555 = 125.445, rounded to 125.44 using banker's rounding
    }
}
