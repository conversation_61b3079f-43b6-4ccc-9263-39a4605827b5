using System;
using CoverGo.Gateway.Domain.Claims;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Domain.Claims;

public class DefaultTenantSpecificClaimServiceTests
{
    private readonly DefaultTenantSpecificClaimService _claimService;

    public DefaultTenantSpecificClaimServiceTests()
    {
        _claimService = new DefaultTenantSpecificClaimService();
    }

    [Fact]
    public void GetClaimApprovalAmount_ThrowsNotImplementedException()
    {
        // Arrange
        var claim = new Claim
        {
            Fields = new JObject()
        };

        // Act & Assert
        Assert.Throws<NotImplementedException>(() => _claimService.GetClaimApprovalAmount(claim));
    }
}
