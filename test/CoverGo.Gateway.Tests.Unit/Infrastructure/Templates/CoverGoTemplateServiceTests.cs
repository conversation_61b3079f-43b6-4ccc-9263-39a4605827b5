using System;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Infrastructure;
using CoverGo.Gateway.Infrastructure.Templates;
using FluentAssertions;
using Moq;
using Moq.Protected;
using Newtonsoft.Json;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Infrastructure.Templates
{
    public class CoverGoTemplateServiceTests
    {
        private readonly Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private readonly HttpClient _httpClient;
        private readonly CoverGoTemplateService _templateService;

        public CoverGoTemplateServiceTests()
        {
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object)
            {
                BaseAddress = new Uri("https://api.example.com/")
            };
            _templateService = new CoverGoTemplateService(_httpClient);
        }

        #region GetJobStatusAsync

        [Fact]
        public async Task GetJobStatusAsync_WhenJobExists_ShouldReturnSuccessResult()
        {
            // Arrange
            string jobId = "job-123";
            string expectedStatus = "completed";
            Result<string> successResponse = Result<string>.Success(expectedStatus);

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri != null &&
                        req.RequestUri.ToString().EndsWith($"api/v1/backgroundjobs/{jobId}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(successResponse))
                });

            // Act
            Result<string> result = await _templateService.GetJobStatusAsync(jobId);

            // Assert
            result.Should().NotBeNull();
            result.Status.Should().Be("success");
            result.Value.Should().Be(expectedStatus);
        }

        [Fact]
        public async Task GetJobStatusAsync_WhenJobDoesNotExist_ShouldReturnFailureResult()
        {
            // Arrange
            string jobId = "non-existent-job";
            Result<string> failureResponse = Result<string>.Failure("Job not found");

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri != null &&
                        req.RequestUri.ToString().EndsWith($"api/v1/backgroundjobs/{jobId}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(failureResponse))
                });

            // Act
            Result<string> result = await _templateService.GetJobStatusAsync(jobId);

            // Assert
            result.Should().NotBeNull();
            result.Status.Should().Be("failure");
            result.Errors.Should().Contain("Job not found");
        }

        [Fact]
        public async Task GetJobStatusAsync_WhenHttpRequestFails_ShouldThrowApiException()
        {
            // Arrange
            string jobId = "job-123";

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Get &&
                        req.RequestUri != null &&
                        req.RequestUri.ToString().EndsWith($"api/v1/backgroundjobs/{jobId}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    Content = new StringContent("Internal server error")
                });

            // Act & Assert
            await Assert.ThrowsAsync<ApiException>(() => _templateService.GetJobStatusAsync(jobId));
        }

        [Fact]
        public async Task GetJobStatusAsync_ShouldCallCorrectEndpoint()
        {
            // Arrange
            string jobId = "job-456";
            Result<string> successResponse = Result<string>.Success("in-progress");

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(successResponse))
                });

            // Act
            await _templateService.GetJobStatusAsync(jobId);

            // Assert
            _mockHttpMessageHandler.Protected().Verify(
                "SendAsync",
                Times.Once(),
                ItExpr.Is<HttpRequestMessage>(req =>
                    req.Method == HttpMethod.Get &&
                    req.RequestUri != null &&
                    req.RequestUri.ToString() == $"https://api.example.com/api/v1/backgroundjobs/{jobId}"),
                ItExpr.IsAny<CancellationToken>());
        }

        #endregion

        #region RenderWkhtmltopdfAsync

        [Fact]
        public async Task RenderWkhtmltopdfAsync_WithOutputFilePath_WhenSuccessful_ShouldReturnSuccessResult()
        {
            // Arrange
            string tenantId = "test-tenant";
            string templateId = "template-123";
            string outputFilePath = "output/path/file.pdf";
            RenderParameters renderParameters = new()
            {
                Name = "Test Template",
                ContentJsonString = "{\"data\": \"test data\"}"
            };

            Result<string> successResponse = Result<string>.Success("file-path-result");

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Post &&
                        req.RequestUri != null &&
                        req.RequestUri.ToString().EndsWith($"{tenantId}/api/v1/templates/wkhtmltopdf/render/{templateId}?p={outputFilePath}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(successResponse))
                });

            // Act
            Result<string> result = await _templateService.RenderWkhtmltopdfAsync(tenantId, templateId, outputFilePath, renderParameters);

            // Assert
            result.Should().NotBeNull();
            result.Status.Should().Be("success");
            result.Value.Should().Be("file-path-result");
        }

        [Fact]
        public async Task RenderWkhtmltopdfAsync_WithOutputFilePath_WhenFailure_ShouldReturnFailureResult()
        {
            // Arrange
            string tenantId = "test-tenant";
            string templateId = "invalid-template";
            string outputFilePath = "output/path/file.pdf";
            RenderParameters renderParameters = new()
            {
                Name = "Test Template",
                ContentJsonString = "{\"data\": \"test data\"}"
            };

            Result<string> failureResponse = Result<string>.Failure("Template not found");

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Post &&
                        req.RequestUri != null &&
                        req.RequestUri.ToString().EndsWith($"{tenantId}/api/v1/templates/wkhtmltopdf/render/{templateId}?p={outputFilePath}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(failureResponse))
                });

            // Act
            Result<string> result = await _templateService.RenderWkhtmltopdfAsync(tenantId, templateId, outputFilePath, renderParameters);

            // Assert
            result.Should().NotBeNull();
            result.Status.Should().Be("failure");
            result.Errors.Should().Contain("Template not found");
        }

        [Fact]
        public async Task RenderWkhtmltopdfAsync_WithOutputFilePath_WhenHttpRequestFails_ShouldThrowApiException()
        {
            // Arrange
            string tenantId = "test-tenant";
            string templateId = "template-123";
            string outputFilePath = "output/path/file.pdf";
            RenderParameters renderParameters = new()
            {
                Name = "Test Template",
                ContentJsonString = "{\"data\": \"test data\"}"
            };

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.Is<HttpRequestMessage>(req =>
                        req.Method == HttpMethod.Post &&
                        req.RequestUri != null &&
                        req.RequestUri.ToString().EndsWith($"{tenantId}/api/v1/templates/wkhtmltopdf/render/{templateId}?p={outputFilePath}")),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.InternalServerError,
                    Content = new StringContent("Internal server error")
                });

            // Act & Assert
            await Assert.ThrowsAsync<ApiException>(() =>
                _templateService.RenderWkhtmltopdfAsync(tenantId, templateId, outputFilePath, renderParameters));
        }

        [Fact]
        public async Task RenderWkhtmltopdfAsync_WithOutputFilePath_ShouldCallCorrectEndpoint()
        {
            // Arrange
            string tenantId = "test-tenant";
            string templateId = "template-123";
            string outputFilePath = "output/path/file.pdf";
            RenderParameters renderParameters = new()
            {
                Name = "Test Template",
                ContentJsonString = "{\"data\": \"test data\"}"
            };

            Result<string> successResponse = Result<string>.Success("file-path-result");

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(successResponse))
                });

            // Act
            await _templateService.RenderWkhtmltopdfAsync(tenantId, templateId, outputFilePath, renderParameters);

            // Assert
            _mockHttpMessageHandler.Protected().Verify(
                "SendAsync",
                Times.Once(),
                ItExpr.Is<HttpRequestMessage>(req =>
                    req.Method == HttpMethod.Post &&
                    req.RequestUri != null &&
                    req.RequestUri.ToString() == $"https://api.example.com/{tenantId}/api/v1/templates/wkhtmltopdf/render/{templateId}?p={outputFilePath}"),
                ItExpr.IsAny<CancellationToken>());
        }

        [Fact]
        public async Task RenderWkhtmltopdfAsync_WithOutputFilePath_ShouldSendCorrectBody()
        {
            // Arrange
            string tenantId = "test-tenant";
            string templateId = "template-123";
            string outputFilePath = "output/path/file.pdf";
            RenderParameters renderParameters = new()
            {
                Name = "Test Template",
                ContentJsonString = "{\"data\": \"test data\"}"
            };

            Result<string> successResponse = Result<string>.Success("file-path-result");
            string? capturedContent = null;
            HttpRequestMessage? capturedRequest = null;

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .Callback<HttpRequestMessage, CancellationToken>(async (req, _) =>
                {
                    capturedRequest = req;
                    // Capture the content as a string before it's disposed
                    if (req.Content != null)
                    {
                        capturedContent = await req.Content.ReadAsStringAsync(CancellationToken.None);
                    }
                })
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonConvert.SerializeObject(successResponse))
                });

            // Act
            await _templateService.RenderWkhtmltopdfAsync(tenantId, templateId, outputFilePath, renderParameters);

            // Assert
            capturedRequest.Should().NotBeNull();
            capturedRequest!.Method.Should().Be(HttpMethod.Post);
            capturedRequest.RequestUri.Should().NotBeNull();
            capturedRequest.RequestUri!.ToString().Should().EndWith($"{tenantId}/api/v1/templates/wkhtmltopdf/render/{templateId}?p={outputFilePath}");

            capturedContent.Should().NotBeNull();

            // Deserialize the actual content to verify the structure
            RenderParameters? actualParams = JsonConvert.DeserializeObject<RenderParameters>(capturedContent!);
            actualParams.Should().NotBeNull();
            actualParams!.Name.Should().Be("Test Template");
            actualParams.ContentJsonString.Should().Be("{\"data\": \"test data\"}");

            // Also verify the content type is correct
            capturedRequest!.Content.Should().NotBeNull();
            capturedRequest.Content!.Headers.ContentType.Should().NotBeNull();
            capturedRequest.Content.Headers.ContentType!.MediaType.Should().Be("application/json");
        }

        #endregion
    }
}
