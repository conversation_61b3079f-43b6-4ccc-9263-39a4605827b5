﻿using AutoFixture;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands;
using FluentAssertions;
using Microsoft.Extensions.Logging.Abstractions;
using MongoDB.Bson.IO;
using Moq;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Infrastructure.TcbServices
{
    public class TcbIntegrateTests
    {
        private readonly IFixture _fixture = new Fixture();

        [Fact]
        public async void GIVEN_Tcb_Deployment_username_password_valid_then_access_token_is_returned()
        {
            string tenantId = _fixture.Create<string>();
            string environmentUsername = _fixture.Create<string>();
            string environmentPassword = _fixture.Create<string>();

            Environment.SetEnvironmentVariable("TCB_DEPLOYMENT_USERNAME", environmentUsername);
            Environment.SetEnvironmentVariable("TCB_DEPLOYMENT_PASSWORD", environmentPassword);

            Mock<IAuthService> authService = new Mock<IAuthService>();

            Login login = _fixture.Build<Login>()
                .With(x => x.TargettedPermissions, new Dictionary<string, IEnumerable<string>>()
                {
                    { "clientId",new List<string>(){ "test_client"} }
                })
                .Create();

            authService.Setup(x => x.GetLoginByNameAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(login);

            authService.Setup(x => x.CreateLoginAsync(It.IsAny<string>(), It.IsAny<CreateLoginCommand>()))
                .ReturnsAsync(_fixture.Build<Result<CreatedStatus>>().Create());

            authService.Setup(x => x.GetWholeAccessTokenAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(_fixture.Build<Token>()
                .Create());

            authService.Setup(x => x.GetPermissionGroupsAsync(It.IsAny<string>(), It.IsAny<PermissionGroupWhere>()))
                .ReturnsAsync(new List<PermissionGroup>()
                {
                    new PermissionGroup()
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = "deployment"
                    }

                });

            tcbDeploymentToken sut = new(
                authService.Object,
                NullLogger<tcbDeploymentToken>.Instance);

            var jsonInput = JObject.Parse($@"{{""tcbDeploymentUsername"":""{environmentUsername}"",""tcbDeploymentPassword"":""{environmentPassword}""}}");

            Result<string> accessToken = await sut.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            accessToken.Should().NotBeNull();
            accessToken.Value.Should().NotBeNull();
            accessToken.IsSuccess.Should().BeTrue();
        }

        [Fact]
        public async void GIVEN_Tcb_Deployment_WHEN_user_create_fails_then_error_is_expected()
        {
            string tenantId = _fixture.Create<string>();
            string environmentUsername = _fixture.Create<string>();
            string environmentPassword = _fixture.Create<string>();

            Environment.SetEnvironmentVariable("TCB_DEPLOYMENT_USERNAME", environmentUsername);
            Environment.SetEnvironmentVariable("TCB_DEPLOYMENT_PASSWORD", environmentPassword);

            Mock<IAuthService> authService = new Mock<IAuthService>();

            Login login = _fixture.Build<Login>()
                .With(x => x.TargettedPermissions, new Dictionary<string, IEnumerable<string>>()
                {
                    { "clientId",new List<string>(){ "test_client"} }
                })
                .Create();

            authService.Setup(x => x.GetLoginByNameAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync((Login)null);

            authService.Setup(x => x.CreateLoginAsync(It.IsAny<string>(), It.IsAny<CreateLoginCommand>()))
                .ReturnsAsync(_fixture.Build<Result<CreatedStatus>>().Create());

            authService.Setup(x => x.GetWholeAccessTokenAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(_fixture.Build<Token>()
                .Create());

            tcbDeploymentToken sut = new(
                authService.Object,
                NullLogger<tcbDeploymentToken>.Instance);

            var jsonInput = JObject.Parse($@"{{""tcbDeploymentUsername"":""{environmentUsername}"",""tcbDeploymentPassword"":""{environmentPassword}""}}");

            Result<string> accessToken = await sut.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            accessToken.IsSuccess.Should().BeFalse();
            accessToken.Errors.First().Should().Be("Create login failed");
        }

        [Fact]
        public async void GIVEN_Tcb_Deployment_username_password_WHEN_permission_group_not_exists_then_it_returns_failure()
        {
            string tenantId = _fixture.Create<string>();
            string environmentUsername = _fixture.Create<string>();
            string environmentPassword = _fixture.Create<string>();

            Environment.SetEnvironmentVariable("TCB_DEPLOYMENT_USERNAME", environmentUsername);
            Environment.SetEnvironmentVariable("TCB_DEPLOYMENT_PASSWORD", environmentPassword);

            Mock<IAuthService> authService = new Mock<IAuthService>();

            Login login = _fixture.Build<Login>()
                .With(x => x.TargettedPermissions, new Dictionary<string, IEnumerable<string>>()
                {
                    { "clientId",new List<string>(){ "test_client"} }
                })
                .Create();

            authService.Setup(x => x.GetLoginByNameAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(login);

            authService.Setup(x => x.CreateLoginAsync(It.IsAny<string>(), It.IsAny<CreateLoginCommand>()))
                .ReturnsAsync(_fixture.Build<Result<CreatedStatus>>().Create());

            authService.Setup(x => x.GetWholeAccessTokenAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(_fixture.Build<Token>()
                .Create());

            tcbDeploymentToken sut = new(
                authService.Object,
                NullLogger<tcbDeploymentToken>.Instance);

            var jsonInput = JObject.Parse($@"{{""tcbDeploymentUsername"":""{environmentUsername}"",""tcbDeploymentPassword"":""{environmentPassword}""}}");

            Result<string> accessToken = await sut.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            accessToken.IsSuccess.Should().BeFalse();
            accessToken.Errors.Should().Contain("deployment Permission group is not found.");
            
        }

        [Fact]
        public async void GIVEN_iTcbLife_IAM_username_password_valid_then_access_token_is_returned()
        {
            string tenantId = _fixture.Create<string>();
            string environmentUsername = _fixture.Create<string>();
            string environmentPassword = _fixture.Create<string>();

            Environment.SetEnvironmentVariable("TCB_IAM_USERNAME", environmentUsername);
            Environment.SetEnvironmentVariable("TCB_IAM_PASSWORD", environmentPassword);

            Mock<IAuthService> authService = new Mock<IAuthService>();

            Login login = _fixture.Build<Login>()
                .With(x => x.TargettedPermissions, new Dictionary<string, IEnumerable<string>>()
                {
                    { "clientId",new List<string>(){ "test_client"} }
                })
                .Create();

            authService.Setup(x => x.GetLoginByNameAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(login);

            authService.Setup(x => x.CreateLoginAsync(It.IsAny<string>(), It.IsAny<CreateLoginCommand>()))
                .ReturnsAsync(_fixture.Build<Result<CreatedStatus>>().Create());

            authService.Setup(x => x.GetWholeAccessTokenAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(_fixture.Build<Token>()
                .Create());

            ItcbLifeToken sut = new(
                authService.Object,
                NullLogger<ItcbLifeToken>.Instance);

            var jsonInput = JObject.Parse($@"{{""iamUsername"":""{environmentUsername}"",""iamPassword"":""{environmentPassword}""}}");

            Result<string> accessToken = await sut.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            accessToken.Should().NotBeNull();
            accessToken.Value.Should().NotBeNull();
            accessToken.IsSuccess.Should().BeTrue();
        }

        [Fact]
        public async void GIVEN_CheckConsent_WHEN_CitizenId_not_Valid_THEN_API_should_return_failure()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse(@"{
        ""cusId"": """",
        ""leadId"": """",
        ""citizenId"": """",
        ""citizenType"": ""01"",
        ""poName"": ""Nguyen Tien Dat""
    }");

            CheckConsent sut = new(
               tenantId,
               NullLogger<CheckConsent>.Instance);

            Result<string> result = await sut.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("inputJson.citizenId is required");

        }

        [Fact]
        public async void GIVEN_CheckConsent_WHEN_CitizenType_not_Valid_THEN_API_should_return_failure()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse(@"{
        ""cusId"": """",
        ""leadId"": """",
        ""citizenId"": ""001096010398"",
        ""citizenType"": """",
        ""poName"": ""Nguyen Tien Dat""
    }");

            CheckConsent sut = new(
               tenantId,
               NullLogger<CheckConsent>.Instance);

            Result<string> result = await sut.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("inputJson.citizenType is required");

        }

        [Fact]
        public async void GIVEN_CheckConsent_WHEN_Name_not_Valid_THEN_API_should_return_failure()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse(@"{
        ""cusId"": """",
        ""leadId"": """",
        ""citizenId"": ""001096010398"",
        ""citizenType"": ""01"",
        ""poName"": """"
    }");

            CheckConsent sut = new(
               tenantId,
               NullLogger<CheckConsent>.Instance);

            Result<string> result = await sut.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("inputJson.poName is required");

        }

        [Fact]
        public async void GIVEN_CheckConsent_WHEN_InputJson_Valid_THEN_API_should_return_response_with_SUCCESS()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse(@"{
        ""cusId"": """",
        ""leadId"": """",
        ""citizenId"": ""001096010398"",
        ""citizenType"": ""01"",
        ""poName"": ""Nguyen Tien Dat""
    }");


            Mock<CheckConsent> Sut = new Mock<CheckConsent>(It.IsAny<string>(), NullLogger<CheckConsent>.Instance)
            {
                CallBase = true
            };
            
            Mock<IRestResponse> mockResponse = new Mock<IRestResponse>();
            mockResponse.Setup(x => x.IsSuccessful).Returns(true);
            mockResponse.Setup(x => x.Content).Returns(@"{
    ""headerResponse"": {
        ""messageId"": ""58d026f0-e128-11eb-b3ca-005056b85ba1"",
        ""createDate"": ""2024-05-22T08:43:12Z"",
        ""fromNm"": ""BANCA"",
        ""toNm"": ""ITCBLIFE""
    },
    ""responseStatus"": {
        ""errorCode"": ""0"",
        ""errorMessage"": ""Success""
    },
    ""document"": {
        ""data"": {
            ""citizenId"": ""001096010398"",
            ""leadId"": null,
            ""consentResult"": ""N""
        }
    }
}");

            Sut.Setup(x => x.ExecuteApi(It.IsAny<CheckConsentRequest>()))
                .ReturnsAsync(mockResponse.Object);

            Result<string> result = await Sut.Object.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeTrue();

            JObject resultJson = JObject.Parse(result.Value);
            resultJson.Should().NotBeNull();
            resultJson.SelectToken("citizenId").Value<string>().Should().Be("001096010398");
            resultJson.SelectToken("leadId").Value<string>().Should().BeNull();
            resultJson.SelectToken("consentResult").Value<string>().Should().Be("N");

        }

        [Fact]
        public async void GIVEN_CheckConsent_WHEN_InputJson_Valid_AND_API_Failed_THEN_API_should_return_response_with_FAILURE()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse(@"{
        ""cusId"": """",
        ""leadId"": """",
        ""citizenId"": ""001096010398"",
        ""citizenType"": ""01"",
        ""poName"": ""Nguyen Tien Dat""
    }");


            Mock<CheckConsent> Sut = new Mock<CheckConsent>(It.IsAny<string>(), NullLogger<CheckConsent>.Instance)
            {
                CallBase = true
            };

            Mock<IRestResponse> mockResponse = new Mock<IRestResponse>();
            mockResponse.Setup(x => x.IsSuccessful).Returns(false);

            Sut.Setup(x => x.ExecuteApi(It.IsAny<CheckConsentRequest>()))
                .ReturnsAsync(mockResponse.Object);

            Result<string> result = await Sut.Object.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeFalse();
            result.Errors.Any(x => x.Contains("Execute TCB checkConsent API failure")).Should().BeTrue();
        }

        [Fact]
        public async void GIVEN_CheckConsent_WHEN_InputJson_not_Valid_THEN_API_should_return_error_with_FAILURE()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = null;

            CheckConsent sut = new(
                tenantId,
                NullLogger<CheckConsent>.Instance);

            Result<string> result = await sut.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeFalse();
            result.Errors.Any(x => x.Contains("inputJson is not valid")).Should().BeTrue();
        }


        [Fact]
        public async void GIVEN_GenerateOtpConsent_WHEN_RequestType_Is_01_And_SendMethod_Is_EMAIL_And_Email_Not_Provided_THEN_API_should_return_failure()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse(@"{
        ""requestId"": ""iTCBlifeConsent_b3ae176c-70ec-40cd-b89f-1e39965af5aa"",
        ""cusId"": """",
        ""leadId"": """",
        ""citizenId"": ""001096010398"",
        ""citizenType"": ""01"",
        ""poName"": ""Nguyen Tien Dat"",
        ""conversationId"": ""ITCBL001"",
        ""sendMethod"": ""EMAIL"",
        ""poEmail"": """",
        ""poPhone"": ""0979781996"",
        ""saleUserName"": ""itcblife_rbo1"",
        ""saleAgentName"": ""itcblife_rbo1"",
        ""saleAgentCode"": ""B2092"",
        ""source"": """",
        ""partner"": """",
        ""requestType"": ""01""
    }");

            GenerateOTPConsent sut = new(
               tenantId,
               NullLogger<GenerateOTPConsent>.Instance);

            Result<string> result = await sut.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("inputJson.poEmail is required");

        }

        [Theory]
        [InlineData("Tuan@.")]
        [InlineData("Tuan@a")]
        [InlineData("Tuan@.com")]
        [InlineData("T$@^.^")]
        [InlineData("$*@hehe.)")]
        [InlineData("$*@%#@&)")]
        [InlineData("$*%#@&)")]
        [InlineData("$Tuan*QA%#@&)DLVN")]
        public async void GIVEN_GenerateOtpConsent_WHEN_RequestType_Is_01_And_SendMethod_Is_EMAIL_And_Invalid_Format_Email__Provided_THEN_API_should_return_failure(string email)
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse($@"{{
        ""requestId"": ""iTCBlifeConsent_b3ae176c-70ec-40cd-b89f-1e39965af5aa"",
        ""cusId"": """",
        ""leadId"": """",
        ""citizenId"": ""001096010398"",
        ""citizenType"": ""01"",
        ""poName"": ""Nguyen Tien Dat"",
        ""conversationId"": ""ITCBL001"",
        ""sendMethod"": ""EMAIL"",
        ""poEmail"": ""{{{email}}}"",
        ""poPhone"": ""0979781996"",
        ""saleUserName"": ""itcblife_rbo1"",
        ""saleAgentName"": ""itcblife_rbo1"",
        ""saleAgentCode"": ""B2092"",
        ""source"": """",
        ""partner"": """",
        ""requestType"": ""01""
    }}");

            GenerateOTPConsent sut = new(
               tenantId,
               NullLogger<GenerateOTPConsent>.Instance);

            Result<string> result = await sut.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("inputJson.poEmail is Invalid");

        }


        [Fact]
        public async void GIVEN_GenerateOtpConsent_WHEN_RequestType_Is_01_And_SendMethod_Is_Not_Provided_THEN_API_should_return_failure()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse(@"{
        ""requestId"": ""iTCBlifeConsent_b3ae176c-70ec-40cd-b89f-1e39965af5aa"",
        ""cusId"": """",
        ""leadId"": """",
        ""citizenId"": ""001096010398"",
        ""citizenType"": ""01"",
        ""poName"": ""Nguyen Tien Dat"",
        ""conversationId"": ""ITCBL001"",
        ""sendMethod"": """",
        ""poEmail"": ""<EMAIL>"",
        ""poPhone"": """",
        ""saleUserName"": ""itcblife_rbo1"",
        ""saleAgentName"": ""itcblife_rbo1"",
        ""saleAgentCode"": ""B2092"",
        ""source"": """",
        ""partner"": """",
        ""requestType"": ""01""
    }");

            GenerateOTPConsent sut = new(
               tenantId,
               NullLogger<GenerateOTPConsent>.Instance);

            Result<string> result = await sut.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("inputJson.sendMethod is required");

        }



        [Fact]
        public async void GIVEN_GenerateOtpConsent_WHEN_RequestType_Is_01_And_SendMethod_Is_SMS_And_PhoneNumber_Not_Provided_THEN_API_should_return_failure()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse(@"{
        ""requestId"": ""iTCBlifeConsent_b3ae176c-70ec-40cd-b89f-1e39965af5aa"",
        ""cusId"": """",
        ""leadId"": """",
        ""citizenId"": ""001096010398"",
        ""citizenType"": ""01"",
        ""poName"": ""Nguyen Tien Dat"",
        ""conversationId"": ""ITCBL001"",
        ""sendMethod"": ""SMS"",
        ""poEmail"": ""<EMAIL>"",
        ""poPhone"": """",
        ""saleUserName"": ""itcblife_rbo1"",
        ""saleAgentName"": ""itcblife_rbo1"",
        ""saleAgentCode"": ""B2092"",
        ""source"": """",
        ""partner"": """",
        ""requestType"": ""01""
    }");

            GenerateOTPConsent sut = new(
               tenantId,
               NullLogger<GenerateOTPConsent>.Instance);

            Result<string> result = await sut.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("inputJson.poPhone is required");

        }

        [Fact]
        public async void GIVEN_GenerateOtpConsent_WHEN_RequestType_Is_01_And_SendMethod_Is_SMS_And_Invalid_PhoneNumber_Provided_THEN_API_should_return_failure()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse($@"{{
        ""requestId"": ""iTCBlifeConsent_b3ae176c-70ec-40cd-b89f-1e39965af5aa"",
        ""cusId"": """",
        ""leadId"": """",
        ""citizenId"": ""001096010398"",
        ""citizenType"": ""01"",
        ""poName"": ""Nguyen Tien Dat"",
        ""conversationId"": ""ITCBL001"",
        ""sendMethod"": ""SMS"",
        ""poEmail"": ""<EMAIL>"",
        ""poPhone"": ""1927654321"",
        ""saleUserName"": ""itcblife_rbo1"",
        ""saleAgentName"": ""itcblife_rbo1"",
        ""saleAgentCode"": ""B2092"",
        ""source"": """",
        ""partner"": """",
        ""requestType"": ""01""
    }}");

            GenerateOTPConsent sut = new(
               tenantId,
               NullLogger<GenerateOTPConsent>.Instance);

            Result<string> result = await sut.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("inputJson.poPhone is Invalid");

        }

        [Fact]
        public async void GIVEN_GenerateOtpConsent_WHEN_InputJson_Valid_THEN_API_should_return_response_with_SUCCESS()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse(@"{
        ""requestId"": ""iTCBlifeConsent_b3ae176c-70ec-40cd-b89f-1e39965af5aa"",
        ""cusId"": """",
        ""leadId"": """",
        ""citizenId"": ""001096010398"",
        ""citizenType"": ""01"",
        ""poName"": ""Nguyen Tien Dat"",
        ""conversationId"": ""ITCBL001"",
        ""sendMethod"": ""SMS"",
        ""poEmail"": ""<EMAIL>"",
        ""poPhone"": ""0979781996"",
        ""saleUserName"": ""itcblife_rbo1"",
        ""saleAgentName"": ""itcblife_rbo1"",
        ""saleAgentCode"": ""B2092"",
        ""source"": """",
        ""partner"": """",
        ""requestType"": ""01""
    }");


            Mock<GenerateOTPConsent> Sut = new Mock<GenerateOTPConsent>(It.IsAny<string>(), NullLogger<GenerateOTPConsent>.Instance)
            {
                CallBase = true
            };

            Mock<IRestResponse> mockResponse = new Mock<IRestResponse>();
            mockResponse.Setup(x => x.IsSuccessful).Returns(true);
            mockResponse.Setup(x => x.Content).Returns(@"{
    ""headerResponse"": {
        ""messageId"": ""58d026f0-e128-11eb-b3ca-005056b85ba1"",
        ""createDate"": ""2024-05-22T08:43:12Z"",
        ""fromNm"": ""BANCA"",
        ""toNm"": ""ITCBLIFE""
    },
    ""responseStatus"": {
        ""errorCode"": ""0"",
        ""errorMessage"": ""Success""
    },
    ""document"": {
        ""data"": {
            ""statusCode"": ""OTP00000"",
            ""message"": ""SUCCESS"",
            ""data"": {
                ""id"": ""664db141b2f66a050feeca44"",
            },
            ""numberRequest"": 1,
            ""numberRequestLimit"": 5,
            ""sendRequestMethod"": ""SMS""
        }
    }
}");

            Sut.Setup(x => x.ExecuteApi(It.IsAny<GenerateOTPConsentRequest>()))
                .ReturnsAsync(mockResponse.Object);

            Result<string> result = await Sut.Object.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeTrue();

            JObject resultJson = JObject.Parse(result.Value);
            resultJson.Should().NotBeNull();
            resultJson.SelectToken("statusCode").Value<string>().Should().Be("OTP00000");
            resultJson.SelectToken("message").Value<string>().Should().Be("SUCCESS");
            resultJson.SelectToken("sendRequestMethod").Value<string>().Should().Be("SMS");

        }

        [Fact]
        public async void GIVEN_VerifyOtpConsent_WHEN_RequestId_Not_Provided_THEN_API_should_return_response_with_FAILURE()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse(@"{
        ""requestId"": """",
        ""id"": ""664db141b2f66a050feeca44"",
        ""otp"": ""12345678""
    }");


            Mock<VerifyOtpConsent> Sut = new Mock<VerifyOtpConsent>(It.IsAny<string>(), NullLogger<VerifyOtpConsent>.Instance)
            {
                CallBase = true
            };

            Mock<IRestResponse> mockResponse = new Mock<IRestResponse>();
            mockResponse.Setup(x => x.IsSuccessful).Returns(true);
            mockResponse.Setup(x => x.Content).Returns(@"{
    ""headerResponse"": {
        ""messageId"": ""58d026f0-e128-11eb-b3ca-005056b85ba1"",
        ""createDate"": ""2024-05-22T08:49:23Z"",
        ""fromNm"": ""BANCA"",
        ""toNm"": ""ITCBLIFE""
    },
    ""responseStatus"": {
        ""errorCode"": ""0"",
        ""errorMessage"": ""Success""
    },
    ""document"": {
        ""data"": {
            ""statusCode"": ""OTP00000"",
            ""message"": ""SUCCESS"",
            ""data"": {
                ""verifyFailCount"": 1,
                ""verifyLimit"": 5
            }
        }
    }
}");

            Sut.Setup(x => x.ExecuteApi(It.IsAny<VerifyOtpConsentRequest>()))
                .ReturnsAsync(mockResponse.Object);

            Result<string> result = await Sut.Object.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("inputJson.requestId is required");
        }

        [Fact]
        public async void GIVEN_VerifyOtpConsent_WHEN_Id_Not_Provided_THEN_API_should_return_response_with_FAILURE()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse(@"{
        ""requestId"": ""iTCBlifeConsent_b3ae176c-70ec-40cd-b89f-1e39965af5aa"",
        ""id"": """",
        ""otp"": ""12345678""
    }");


            Mock<VerifyOtpConsent> Sut = new Mock<VerifyOtpConsent>(It.IsAny<string>(), NullLogger<VerifyOtpConsent>.Instance)
            {
                CallBase = true
            };

            Mock<IRestResponse> mockResponse = new Mock<IRestResponse>();
            mockResponse.Setup(x => x.IsSuccessful).Returns(true);
            mockResponse.Setup(x => x.Content).Returns(@"{
    ""headerResponse"": {
        ""messageId"": ""58d026f0-e128-11eb-b3ca-005056b85ba1"",
        ""createDate"": ""2024-05-22T08:49:23Z"",
        ""fromNm"": ""BANCA"",
        ""toNm"": ""ITCBLIFE""
    },
    ""responseStatus"": {
        ""errorCode"": ""0"",
        ""errorMessage"": ""Success""
    },
    ""document"": {
        ""data"": {
            ""statusCode"": ""OTP00000"",
            ""message"": ""SUCCESS"",
            ""data"": {
                ""verifyFailCount"": 1,
                ""verifyLimit"": 5
            }
        }
    }
}");

            Sut.Setup(x => x.ExecuteApi(It.IsAny<VerifyOtpConsentRequest>()))
                .ReturnsAsync(mockResponse.Object);

            Result<string> result = await Sut.Object.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("inputJson.id is required");
        }

        [Fact]
        public async void GIVEN_VerifyOtpConsent_WHEN_otp_Not_Provided_THEN_API_should_return_response_with_FAILURE()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse(@"{
        ""requestId"": ""iTCBlifeConsent_b3ae176c-70ec-40cd-b89f-1e39965af5aa"",
        ""id"": ""664db141b2f66a050feeca44"",
        ""otp"": """"
    }");


            Mock<VerifyOtpConsent> Sut = new Mock<VerifyOtpConsent>(It.IsAny<string>(), NullLogger<VerifyOtpConsent>.Instance)
            {
                CallBase = true
            };

            Mock<IRestResponse> mockResponse = new Mock<IRestResponse>();
            mockResponse.Setup(x => x.IsSuccessful).Returns(true);
            mockResponse.Setup(x => x.Content).Returns(@"{
    ""headerResponse"": {
        ""messageId"": ""58d026f0-e128-11eb-b3ca-005056b85ba1"",
        ""createDate"": ""2024-05-22T08:49:23Z"",
        ""fromNm"": ""BANCA"",
        ""toNm"": ""ITCBLIFE""
    },
    ""responseStatus"": {
        ""errorCode"": ""0"",
        ""errorMessage"": ""Success""
    },
    ""document"": {
        ""data"": {
            ""statusCode"": ""OTP00000"",
            ""message"": ""SUCCESS"",
            ""data"": {
                ""verifyFailCount"": 1,
                ""verifyLimit"": 5
            }
        }
    }
}");

            Sut.Setup(x => x.ExecuteApi(It.IsAny<VerifyOtpConsentRequest>()))
                .ReturnsAsync(mockResponse.Object);

            Result<string> result = await Sut.Object.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("inputJson.otp is required");
        }

        [Fact]
        public async void GIVEN_VerifyOtpConsent_WHEN_InputJson_not_Valid_THEN_API_should_return_error_with_FAILURE()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = null;

            VerifyOtpConsent sut = new(
                tenantId,
                NullLogger<VerifyOtpConsent>.Instance);

            Result<string> result = await sut.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeFalse();
            result.Errors.Any(x => x.Contains("inputJson is not valid")).Should().BeTrue();
        }

        [Fact]
        public async void GIVEN_VerifyOtpConsent_WHEN_InputJson_Valid_THEN_API_should_return_response_with_SUCCESS()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse(@"{
        ""requestId"": ""iTCBlifeConsent_b3ae176c-70ec-40cd-b89f-1e39965af5aa"",
        ""id"": ""664db141b2f66a050feeca44"",
        ""otp"": ""12345678""
    }");


            Mock<VerifyOtpConsent> Sut = new Mock<VerifyOtpConsent>(It.IsAny<string>(), NullLogger<VerifyOtpConsent>.Instance)
            {
                CallBase = true
            };

            Mock<IRestResponse> mockResponse = new Mock<IRestResponse>();
            mockResponse.Setup(x => x.IsSuccessful).Returns(true);
            mockResponse.Setup(x => x.Content).Returns(@"{
    ""headerResponse"": {
        ""messageId"": ""58d026f0-e128-11eb-b3ca-005056b85ba1"",
        ""createDate"": ""2024-05-22T08:49:23Z"",
        ""fromNm"": ""BANCA"",
        ""toNm"": ""ITCBLIFE""
    },
    ""responseStatus"": {
        ""errorCode"": ""0"",
        ""errorMessage"": ""Success""
    },
    ""document"": {
        ""data"": {
            ""statusCode"": ""OTP00000"",
            ""message"": ""SUCCESS"",
            ""data"": {
                ""verifyFailCount"": 1,
                ""verifyLimit"": 5
            }
        }
    }
}");

            Sut.Setup(x => x.ExecuteApi(It.IsAny<VerifyOtpConsentRequest>()))
                .ReturnsAsync(mockResponse.Object);

            Result<string> result = await Sut.Object.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeTrue();

            JObject resultJson = JObject.Parse(result.Value);
            resultJson.Should().NotBeNull();
            resultJson.SelectToken("statusCode").Value<string>().Should().Be("OTP00000");
            resultJson.SelectToken("message").Value<string>().Should().Be("SUCCESS");
            resultJson.SelectToken("data.verifyFailCount").Value<string>().Should().Be("1");
            resultJson.SelectToken("data.verifyLimit").Value<string>().Should().Be("5");

        }

        [Fact]
        public async void GIVEN_VerifyOtpConsent_WHEN_InputJson_Valid_AND_API_Failed_THEN_API_should_return_response_with_FAILURE()
        {
            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse(@"{
        ""requestId"": ""iTCBlifeConsent_b3ae176c-70ec-40cd-b89f-1e39965af5aa"",
        ""id"": ""664db141b2f66a050feeca44"",
        ""otp"": ""12345678""
    }");


            Mock<VerifyOtpConsent> Sut = new Mock<VerifyOtpConsent>(It.IsAny<string>(), NullLogger<VerifyOtpConsent>.Instance)
            {
                CallBase = true
            };

            Mock<IRestResponse> mockResponse = new Mock<IRestResponse>();
            mockResponse.Setup(x => x.IsSuccessful).Returns(false);

            Sut.Setup(x => x.ExecuteApi(It.IsAny<VerifyOtpConsentRequest>()))
                .ReturnsAsync(mockResponse.Object);

            Result<string> result = await Sut.Object.ExecuteAsync(tenantId, jsonInput, _fixture.Create<string>());

            result.IsSuccess.Should().BeFalse();
            result.Errors.Any(x => x.Contains("Execute TCB verifyOtpConsent API failure")).Should().BeTrue();
        }

        [Fact]
        public async void GIVEN_VerifyOtpConsent_WHEN_inputJson_Valid_THEN_ExecureApi_should_return_response_with_SUCCESS()
        {

            string tenantId = _fixture.Create<string>();

            JObject jsonInput = JObject.Parse(@"{
    ""headerRequest"": {
        ""messageId"": ""58d026f0-e128-11eb-b3ca-005056b85ba1"",
        ""createDate"": ""2021-10-18T12:00:32"",
        ""fromNm"": ""ITCBLIFE"",
        ""toNm"": ""BANCA""
    },
    ""document"": {
        ""requestId"": ""iTCBlifeConsent_b3ae176c-70ec-40cd-b89f-1e39965af5aa"",
        ""id"": ""664db141b2f66a050feeca44"",
        ""otp"": ""12345678""
    }
}");

            Mock<VerifyOtpConsent> Sut = new Mock<VerifyOtpConsent>(It.IsAny<string>(), NullLogger<VerifyOtpConsent>.Instance)
            {
                CallBase = true
            };


            Mock<IRestResponse> mockResponse = new Mock<IRestResponse>();
            mockResponse.Setup(x => x.IsSuccessful).Returns(true);
            mockResponse.Setup(x => x.Content).Returns(@"{
    ""headerResponse"": {
        ""messageId"": ""58d026f0-e128-11eb-b3ca-005056b85ba1"",
        ""createDate"": ""2024-05-22T08:49:23Z"",
        ""fromNm"": ""BANCA"",
        ""toNm"": ""ITCBLIFE""
    },
    ""responseStatus"": {
        ""errorCode"": ""0"",
        ""errorMessage"": ""Success""
    },
    ""document"": {
        ""data"": {
            ""statusCode"": ""OTP00000"",
            ""message"": ""SUCCESS"",
            ""data"": {
                ""verifyFailCount"": 1,
                ""verifyLimit"": 5
            }
        }
    }
}");

            Sut.Setup(x => x.ExecuteApi(It.IsAny<VerifyOtpConsentRequest>()))
               .ReturnsAsync(mockResponse.Object);

            VerifyOtpConsentRequest request = jsonInput.ToObject<VerifyOtpConsentRequest>();


            IRestResponse response  = await Sut.Object.ExecuteApi(request);

            response.IsSuccessful.Should().BeTrue();
        }
    }

}
