﻿using System;
using CoverGo.Gateway.Infrastructure.TcbServices;
using FluentAssertions;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Infrastructure.TcbServices
{
    public class EncryptionHelperTest
    {
        [Fact]
        public void Encrypt_Then_Decrypt_ShouldReturn_Original_Value()
        {
            Environment.SetEnvironmentVariable("TCB_API_TOKEN", "0092B0D6625340FD2D3C444B7D4A82F6423D0B7CBAEA0072697133852F93F765");

            string plainText = "PlainText_Test";
            var encrypted = EncryptionHelper.Encrypt(plainText);

            encrypted?.Value.Should().NotBe(plainText);

            var decrypted = EncryptionHelper.Decrypt(encrypted.Value);

            decrypted?.Value.Should().Be(plainText);
        }

        [Fact]
        public void Sign_ShouldReturn_Success()
        {
            Environment.SetEnvironmentVariable("TCB_API_TOKEN", "0092B0D6625340FD2D3C444B7D4A82F6423D0B7CBAEA0072697133852F93F765");
            Environment.SetEnvironmentVariable("TCB_API_PRIVATE_KEY", "\n        MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCJj9F3k9Z9w73R\n        PfEY0E9RM1tV8WzD/p5vtVenCs8ErIlQtkEQ59sy2L8Q+fQ4fEwRfGJ00Z+lhzr+\n        D8PcZpMLkHB4WX7tQdYnp0lSOKi+os/e0SWeIPKufUyBzvuG92KZf16yKXYrAuqJ\n        yY3V/3qnNGMBUaXLNFQXhWjIuLGsMS5YOl69Au0M0exox5MAvdwBN05z8cvVw8M5\n        Rz9PiJEm1b9Uvpo08cuUgEbIyCwdX+Wq+071h+RRjY9r+EeADV/1GTmUdOx2yvFf\n        es7ta8I7aHBaM9kj25+0Q1h+jsoyJlbR87obsCXdIkKRqW5LxzXewgzhdGhCEFcU\n        oBXDkPSNAgMBAAECggEAeQt4QkRwhRHswWZD9JW4dbc/M4zRLFWsjJeBnZdBOten\n        rMHy1zFuDHv1BLctU1bsiKZu7GRYmos4Ob8dlZ8c3RwoG36E1bkM/K+TMSpyMe2K\n        BIL9GvvbD/dpjv/XDJg6xt7xMMyTEp3qS3xztk6sWt0aEVsLDJfrNxdJcPY0QVNp\n        0w95KuvRdBwLyhBQH6l+BwJJ6dKn9yQn3A8hwepAwxihBmKDuO7QoWDeAMvDCSlq\n        3fEpXR/UE2Bj2pZwLsLyNsQF/L/DNUo3SJgY2AOr7wuMMsCbc9+o7uDHi6CNvhBE\n        nZZvqQt19Z/qUHr//VjYxmPmsIFYOAAiYdAjC8lvgQKBgQDNqv9of/PO3QE3D3AI\n        V+uT08Zo/cSWO7sEWq99EUPvOnvKWk8sB8Bhts1oxtQPyub1Q7ojKeUBA189sqVT\n        c5h848mGLe5OkT7/2zJsvWqIW00CPQ3iaDlnHCXJPeEHMRdNYN9DSYra6p0Qs9S+\n        kmb0khtvlBVqgRxetK8+MtDmGQKBgQCrOgAmeSBAZ4wYtO5gRihJ2CvfGqxXGc/J\n        QZv6EOY+P428O1syLb/+ay2oiNOUtZI5gtAiX8s535bkfRCjFwskDMqKAYRu0ZTo\n        d04l6576VFh2sqzDhSjimrxiLx3oXuB3dGeYlDIwSh/EpIGHd8dS73E7kSGDkCfc\n        4OpEqeRIlQKBgCGHSvQCSADlTplnTuBNVP5Ye/QJys6jFi3zisBc3DJX2m52oomP\n        CfHkF0CJiXC5p0U8sU+iOM8deZ1ZTlJ4edeCWwwg2MS+MQMM/P91MbThDYDb6zOJ\n        wjHpCM/x4omFm0LmE/+oMZbWwJxEdN5Mh7Q/TXG3NnaSbcmSsy70odGhAoGAIEuT\n        Ra5o59zf7rUJlP0Gkk8Ej1MPmGoU5+fJDJRtaOZBzHQBkmhQoyMB/Jy6E31ftBs5\n        OJKK2UKTykrQfYJWJpmHBhqdKO28wU/nXH2hgaOw/8pZjZMrD/qRd49de7uRaNZ6\n        x6uD/9mrPkKs76km3jytRcfs40yoSB/f781/cjUCgYA7N1rQQxvNpFrG/ZzrkVOV\n        yABUkoW7V10sauEoelcI6u4pMNOgFj0EHrcZdSb4wcecr8t3p0vjwTZXHfNkWmRP\n        B+4Ki+qAuoJr6TCba8ECje8Y8X0HHxWIVn1SrTimh6dFADHT+9M7s7eUTs8JHX8u\n        RPjQ12NqseYB0h9KjjJH3Q==");

            string plainText = "PlainText_Test";
            var signed = EncryptionHelper.Sign(plainText);

            signed?.Value.Should().NotBeNullOrEmpty();
        }
    }
}
