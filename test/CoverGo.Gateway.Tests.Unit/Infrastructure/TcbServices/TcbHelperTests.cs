﻿using AutoFixture;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Infrastructure.TcbServices;
using CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands;
using CoverGo.Users.Domain.Individuals;
using FluentAssertions;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using ZstdSharp.Unsafe;

namespace CoverGo.Gateway.Tests.Unit.Infrastructure.TcbServices
{

    public class TcbHelperTests
    {
        private readonly IFixture _fixture = new Fixture();

        [Fact]
        public void WHEN_DateOfBirth_Present_in_ProposerInfo_should_be_used()
        {
            string proposerDateOfBirth = "1953-12-09";

            var customerDateOfBirth = TcbHelper.GetCustomerDateOfBirth(proposerBirthDay: proposerDateOfBirth, namedDriverBirthDay: null);

            customerDateOfBirth.Should().NotBeNull();
            customerDateOfBirth.Should().Be(new DateTime(1953, 12, 9));
        }

        [Fact]
        public void WHEN_ProposerDateOfBirth_Not_Present_and_NamedDriverBirthDay_Present_THEN_NamedDriverBirthDay_should_be_used()
        {
            string namedDriverBirthDay = "1953-12-09";

            var customerDateOfBirth = TcbHelper.GetCustomerDateOfBirth(proposerBirthDay: null, namedDriverBirthDay: namedDriverBirthDay);

            customerDateOfBirth.Should().NotBeNull();
            customerDateOfBirth.Should().Be(new DateTime(1953, 12, 9));
        }

        [Fact]
        public void WHEN_ProposerDateOfBirth_Not_Present_and_NamedDriverBirthDay_Not_Present_THEN_CustomerDateOfBirth_Should_Be_null()
        {

            var customerDateOfBirth = TcbHelper.GetCustomerDateOfBirth(proposerBirthDay: null, namedDriverBirthDay: null);

            customerDateOfBirth.Should().BeNull();
        }

        [Fact]
        public async Task WHEN_PermissionGroup_is_already_present_THEN_Id_is_returned()
        {
            Mock<IAuthService> authService = new Mock<IAuthService>();
            string permissionGroupName = _fixture.Create<string>();
            string tenantId = _fixture.Create<string>();
            var permissionGroups = new List<PermissionGroup>()
            {
                new PermissionGroup() { Id = _fixture.Create<string>(), Name=permissionGroupName }
            };

            authService.Setup(x => x.GetPermissionGroupsAsync(It.IsAny<string>(), It.IsAny<PermissionGroupWhere>()))
                .ReturnsAsync(permissionGroups);

            var result = await TcbHelper.GetOrCreatePermissionGroup(tenantId, permissionGroupName, authService.Object);


            result.Value.Item1.Should().Be(permissionGroups.First().Id);
            result.Value.Item2.Should().Be(true);

        }

        [Fact]
        public async Task WHEN_PermissionGroup_is_not_present_THEN_It_is_created_and_Id_is_returned()
        {
            Mock<IAuthService> authService = new Mock<IAuthService>();
            string permissionGroupName = _fixture.Create<string>();
            string tenantId = _fixture.Create<string>();
            var permissionGroups = new List<PermissionGroup>()
            {
                new PermissionGroup() { Id = _fixture.Create<string>(), Name=permissionGroupName }
            };

            var sucessResult = Result.Success();

            authService.Setup(x => x.CreatePermissionGroupAsync(It.IsAny<string>(), It.IsAny<CreatePermissionGroupCommand>()))
                .ReturnsAsync(sucessResult);

            authService.Setup(x => x.GetPermissionGroupsAsync(It.IsAny<string>(), It.IsAny<PermissionGroupWhere>()))
               .ReturnsAsync(permissionGroups);

            var result = await TcbHelper.GetOrCreatePermissionGroup(tenantId, permissionGroupName, authService.Object);


            result.Value.Item1.Should().Be(permissionGroups.First().Id);
            result.Value.Item2.Should().Be(true);

        }

        #region ReferLead Validation Tests

        [Fact]
        public void WHEN_ReferLeadRequestInput_All_Mandatory_Fields_Present_THEN_Validation_Should_PASS()
        {

            var validationResult = TcbHelper.Validate(GetValidReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeTrue();
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_BankerCode_NOT_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetInValidBankerCodeReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.bankerCode is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_BankerName_NOT_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetInValidBankerNameReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.bankStaffName is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_ConsultChannel_NOT_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetInValidConsultChannelReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.consultChannel is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_Financial_NOT_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetInvalidFinancialIncomeReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.fnData.financial.income.salaryAndBonus is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_Financial_Object_Saving_TargetAmount_NOT_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetInValidFinancialObjectiveSavingTargetAmountReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.fnData.objective.saving.targetAmount is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_Financial_Object_Saving_TargetDuration_NOT_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetInValidFinancialObjectiveSavingTargetDurationReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.fnData.objective.saving.targetDuration is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_Financial_LifeProtection_TargetAmount_NOT_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetInValidFinancialLifeProtectionTargetAmountReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.fnData.objective.lifeProtection.targetAmount is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_Objective_Education_TargetAmount_Invalid_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetInvalidObjectiveEductionTargetAmountReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.fnData.objective.education.targetAmount is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_Objective_Education_TargetDuration_NOT_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetInvalidObjectiveEductionTargetDurationReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.fnData.objective.education.targetDuration is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_Financial_TotalExpenses_NOT_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetInValidFinancialTotalExpensesReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.fnData.financial.totalExpenses is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_Financial_NetDisposableAmount_NOT_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetInValidFinancialNetDisposableIncomeReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.fnData.financial.netDisposableIncome is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_Financial_Income_SalaryAndBonus_NOT_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetInValidFinancialIncomeSalaryAndBonusReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.fnData.financial.income.salaryAndBonus is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_Financial_Multiple_Income_SalaryAndBonus_AND_One_NOT_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetMultipleInValidFinancialIncomeSalaryAndBonusReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.fnData.financial.income.salaryAndBonus is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_Financial_Multiple_Income_SalaryAndBonus_Present_THEN_Validation_Should_PASS()
        {

            var validationResult = TcbHelper.Validate(GetMultipleValidReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeTrue();
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_Financial_Expense_LivingStandard_NOT_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetInValidFinanciaExpenseLivingStandardReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.fnData.financial.expense.livingStandard is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_Objective_Eduction_TargetDuration_NOT_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetInvalidObjectiveEductionTargetDurationReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.fnData.objective.education.targetDuration is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_Objective_LifeProtection_TargetAmount_Invalid_Present_THEN_Validation_Should_FAIL()
        {

            var validationResult = TcbHelper.Validate(GetInValidObjectiveLifeProtectionTargetAmountReferLeadRequestInputJson());

            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.fnData.objective.lifeProtection.targetAmount is required");
        }

        [Fact]
        public void WHEN_ReferLeadRequestInput_Objective_Retirement_TargetAmount_Invalid_Present_THEN_Validation_Should_FAIL()
        {
            var validationResult = TcbHelper.Validate(GetInValidObjectiveRetirementTargetAmountReferLeadRequestInputJson());
            validationResult.IsSuccess.Should().BeFalse();
            validationResult.Errors.First().Should().Be("inputJson.referLeadInput.fnData.objective.retirement.targetAmount is required");
        }

        #endregion ReferLead Validation Tests

        #region Inquiry Policy Tests
        [Fact]
        public async Task WHEN_InquiryPolicy_BY_normal_USER_WITH_no_branchCode_THEN_BranchCode_Extracted_From_User()
        {
            string tenantId = _fixture.Create<string>();
            string loginId = _fixture.Create<string>();
            string entityId = _fixture.Create<Guid>().ToString();
            Mock<IAuthService> authService = new Mock<IAuthService>();

            authService.Setup(x => x.GetLoginById(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(new Login()
                {
                    Id = loginId,
                    EntityId = entityId

                });

            Mock<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>> individualService = new Mock<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>>();

            individualService.Setup(x => x.GetAsync(It.IsAny<string>(), It.IsAny<CoverGo.Gateway.Domain.QueryArguments>()))
                .ReturnsAsync(new List<Individual>()
                {
                    new Individual() { Source = "north-region_1-HDG-RBO", Id= entityId }
                });

            string branchCode = await TcbHelper.ExtractBranchCodeFromUserSource(tenantId, loginId, authService.Object, individualService.Object);
            branchCode.Should().NotBeNullOrEmpty();
            branchCode.Should().Be("HDG");

        }

        [Fact]
        public async Task WHEN_InquiryPolicy_BY_Branch_Manager_WITH_no_branchCode_THEN_BranchCode_Extracted_From_User()
        {
            string tenantId = _fixture.Create<string>();
            string loginId = _fixture.Create<string>();
            string entityId = _fixture.Create<Guid>().ToString();
            Mock<IAuthService> authService = new Mock<IAuthService>();

            authService.Setup(x => x.GetLoginById(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(new Login()
                {
                    Id = loginId,
                    EntityId = entityId

                });

            Mock<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>> individualService = new Mock<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>>();

            individualService.Setup(x => x.GetAsync(It.IsAny<string>(), It.IsAny<CoverGo.Gateway.Domain.QueryArguments>()))
                .ReturnsAsync(new List<Individual>()
                {
                    new Individual() { Source = "north-region_1_HDG-branchManager", Id= entityId }
                });

            string branchCode = await TcbHelper.ExtractBranchCodeFromUserSource(tenantId, loginId, authService.Object, individualService.Object);
            branchCode.Should().NotBeNullOrEmpty();
            branchCode.Should().Be("HDG");

        }

        [Fact]
        public async Task WHEN_InquiryPolicy_BY_Lead_Department_WITH_no_branchCode_THEN_BranchCode_Extracted_From_User()
        {
            string tenantId = _fixture.Create<string>();
            string loginId = _fixture.Create<string>();
            string entityId = _fixture.Create<Guid>().ToString();
            Mock<IAuthService> authService = new Mock<IAuthService>();

            authService.Setup(x => x.GetLoginById(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(new Login()
                {
                    Id = loginId,
                    EntityId = entityId

                });

            Mock<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>> individualService = new Mock<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>>();

            individualService.Setup(x => x.GetAsync(It.IsAny<string>(), It.IsAny<CoverGo.Gateway.Domain.QueryArguments>()))
                .ReturnsAsync(new List<Individual>()
                {
                    new Individual() { Source = "north-region_1-HDG-TIS.PRM", Id= entityId }
                });

            string branchCode = await TcbHelper.ExtractBranchCodeFromUserSource(tenantId, loginId, authService.Object, individualService.Object);
            branchCode.Should().NotBeNullOrEmpty();
            branchCode.Should().Be("HDG");

        }

        [Fact]
        public async Task WHEN_InquiryPolicy_BY_Location_Manager_WITH_no_branchCode_THEN_BranchCode_Extracted_From_User()
        {
            string tenantId = _fixture.Create<string>();
            string loginId = _fixture.Create<string>();
            string entityId = _fixture.Create<Guid>().ToString();
            Mock<IAuthService> authService = new Mock<IAuthService>();

            authService.Setup(x => x.GetLoginById(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(new Login()
                {
                    Id = loginId,
                    EntityId = entityId

                });

            Mock<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>> individualService = new Mock<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>>();

            individualService.Setup(x => x.GetAsync(It.IsAny<string>(), It.IsAny<CoverGo.Gateway.Domain.QueryArguments>()))
                .ReturnsAsync(new List<Individual>()
                {
                    new Individual() { Source = "north-areaManager", Id= entityId }
                });

            string branchCode = await TcbHelper.ExtractBranchCodeFromUserSource(tenantId, loginId, authService.Object, individualService.Object);
            branchCode.Should().BeNull();
        }

        [Fact]
        public async Task WHEN_InquiryPolicy_BY_User_HAVING_Empty_Source_WITH_no_branchCode_THEN_BranchCode_Extracted_From_User()
        {
            string tenantId = _fixture.Create<string>();
            string loginId = _fixture.Create<string>();
            string entityId = _fixture.Create<Guid>().ToString();
            Mock<IAuthService> authService = new Mock<IAuthService>();

            authService.Setup(x => x.GetLoginById(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(new Login()
                {
                    Id = loginId,
                    EntityId = entityId

                });

            Mock<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>> individualService = new Mock<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>>();

            individualService.Setup(x => x.GetAsync(It.IsAny<string>(), It.IsAny<CoverGo.Gateway.Domain.QueryArguments>()))
                .ReturnsAsync(new List<Individual>()
                {
                    new Individual() { Source = "", Id= entityId }
                });

            string branchCode = await TcbHelper.ExtractBranchCodeFromUserSource(tenantId, loginId, authService.Object, individualService.Object);
            branchCode.Should().BeNull();
        }

        //north-areaManager
        // north-region_1-HDG-TIS.PRM

        #endregion

        #region Arrange

        private ReferLeadRequest GetValidReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"": ""01"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}
}";


            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        private ReferLeadRequest GetInValidObjectiveLifeProtectionTargetAmountReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"":""001"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": *********
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}
}";

            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        private ReferLeadRequest GetInValidObjectiveRetirementTargetAmountReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"":""001"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": *********,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 9999999
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}
}";

            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        private ReferLeadRequest GetInValidFinanciaExpenseLivingStandardReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"":""001"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}
}";

            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        private ReferLeadRequest GetInValidFinancialIncomeSalaryAndBonusReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"":""001"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""salaryAndBonus"":*********1,
              ""investmentAndBusiness"": 0,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}
}";

            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        private ReferLeadRequest GetMultipleInValidFinancialIncomeSalaryAndBonusReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"":""001"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 0,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            },
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": *********,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}}";

            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        private ReferLeadRequest GetMultipleValidReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"":""001"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            },
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}}";

            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }


        private ReferLeadRequest GetInvalidObjectiveEductionTargetDurationReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"":""001"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 100000
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}}";

            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        private ReferLeadRequest GetInvalidObjectiveEductionTargetAmountReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"":""001"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetDuration"": 10,
              ""targetAmount"": **********
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}}";

            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        private ReferLeadRequest GetInValidFinancialObjectiveSavingTargetAmountReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"":""001"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetDuration"": 10,
              ""targetAmount"": 50000001
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}}";



            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        private ReferLeadRequest GetInValidFinancialObjectiveSavingTargetDurationReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"":""001"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 100000
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}}";



            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        private ReferLeadRequest GetInValidFinancialLifeProtectionTargetAmountReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"":""001"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 100000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": **********
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}}";



            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        private ReferLeadRequest GetInValidFinancialTotalExpensesReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"":""001"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 100000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}}";



            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        private ReferLeadRequest GetInValidFinancialNetDisposableIncomeReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"":""001"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 100000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}}";



            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }
        private ReferLeadRequest GetInvalidFinancialIncomeReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"":""001"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}}";

            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        private ReferLeadRequest GetInValidBankerCodeReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""poWardCode"": ""04"",
  ""bankerCode"": """",
  ""isAgentPO"": false,
  ""consultChannel"": ""01"",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}}";

            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        private ReferLeadRequest GetInValidBankerNameReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""isAgentPO"": false,
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}}";

            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        private static ReferLeadRequest GetInValidConsultChannelReferLeadRequestInputJson()
        {
            string inputJson = @"{
  ""referLeadinput"" : {
  ""bankerCode"": ""B1640"",
  ""bankStaffName"": ""Hồ Viết Tuệ1"",
  ""isAgentPO"": false,
  ""consultChannel"":"""",
  ""fnaData"": [
    {
      ""financial"": [
        {
          ""totalIncome"": 1000000,
          ""totalExpenses"": 100000,
          ""netDisposableIncome"": 900000,
          ""incomes"": [
            {
              ""investmentAndBusiness"": 0,
              ""salaryAndBonus"": 1000000,
              ""realEstateIncome"": 0,
              ""bankInterest"": 0,
              ""sharesAndDividends"": 0,
              ""other"": 0
            }
          ],
          ""expenses"": [
            {
              ""livingStandard"": 100000,
              ""education"": 0,
              ""emergency"": 0,
              ""socializingAndEntertainment"": 0,
              ""insuranceFee"": 0,
              ""liabilities"": 0,
              ""other"": 0
            }
          ]
        }
      ],
      ""objective"": [
        {
          ""education"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""retirement"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""savings"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000,
              ""targetDuration"": 10
            }
          ],
          ""lifeProtection"": [
            {
              ""isSelected"": true,
              ""targetAmount"": 1000000
            }
          ],
          ""healthProtection"": [
            {
              ""isSelected"": true
            }
          ]
        }
      ],
      ""affordability"": [
        {
          ""financialAffordability"": 30,
          ""estimatedPremiumDuration"": 20,
          ""financialAffordabilityAmount"": 300000,
          ""investmentProfile"": ""Low""
        }
      ]
    }
  ]
}}";

            ReferLeadRequest referLeadRequest = Newtonsoft.Json.JsonConvert.DeserializeObject<ReferLeadRequest>(inputJson);

            return referLeadRequest;
        }

        #endregion Arrange

    }

}
