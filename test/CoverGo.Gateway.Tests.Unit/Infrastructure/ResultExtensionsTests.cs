﻿using System.Collections.Generic;
using System.Linq;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Infrastructure;
using FluentAssertions;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Infrastructure;

public class ResultExtensionsTests
{
    [Fact]
    public void GetAllErrors_WhenErrorsAndErrorsAreNull_ShouldReturnEmptyErrors()
    {
        string result = ResultExtensions.GetAllErrors(null, null);
        result.Should().Be(string.Empty);
    }

    [Fact]
    public void GetAllErrors_WhenErrorsAndErrors2_ShouldReturnAllErrorsEachInNewLine()
    {
        IEnumerable<string> errors = new[] { "error1", "error2" };
        IEnumerable<Error> errors2 = new[]
        {
            new Error(){Code = "code3", Message = "error3"},
            new Error(){Code = "code4", Message = "error4"}
        };

        string result = ResultExtensions.GetAllErrors(errors, errors2);
        result.Should().Be(@"error1
error2
code3: error3
code4: error4");
    }

    [Fact]
    public void GetAllErrors_WhenOnlyErrors_ShouldReturnAllErrorsEachInNewLine()
    {
        IEnumerable<string> errors = new[] { "error1", "error2" };

        string result = ResultExtensions.GetAllErrors(errors, Enumerable.Empty<Error>());
        result.Should().Be(@"error1
error2");
    }

    [Fact]
    public void GetAllErrors_WhenOnlyErrors2_ShouldReturnAllErrorsEachInNewLine()
    {
        IEnumerable<Error> errors2 = new[]
        {
            new Error(){Code = "code3", Message = "error3"},
            new Error(){Code = "code4", Message = "error4"}
        };

        string result = ResultExtensions.GetAllErrors(Enumerable.Empty<string>(), errors2);
        result.Should().Be(@"code3: error3
code4: error4");
    }

    [Fact]
    public void GetAllErrors_WhenCalledAsExtensionOnResult_ShouldReturnAllErrorsEachInNewLine()
    {
        IEnumerable<Error> errors2 = new[]
        {
            new Error(){Code = "code3", Message = "error3"},
            new Error(){Code = "code4", Message = "error4"}
        };

        Result failure = Result.Failure(errors2);

        string result = failure.GetAllErrors();
        result.Should().Be(@"code3: error3
code4: error4");
    }

    [Fact]
    public void GetAllErrors_WhenResultIsNull_ShouldReturnEmptyString()
    {
        string result = ResultExtensions.GetAllErrors(null);
        result.Should().Be(string.Empty);
    }

    [Fact]
    public void GetAllErrors_WhenResultIsSuccess_ShouldReturnEmptyString()
    {
        string result = Result.Success().GetAllErrors();
        result.Should().Be(string.Empty);
    }
}