﻿using System.Net.Http;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Users;
using Xunit;
using Moq;
using System;
using CoverGo.Gateway.Infrastructure.Policies;
using Moq.Protected;
using System.Threading.Tasks;
using System.Net;
using System.Threading;

namespace CoverGo.Gateway.Tests.Unit.Infrastructure.Policies
{
    public class CoverGoPolicyServiceTests
    {
        [Fact]
        public void CheckIssuedStatusTest()
        {
            // Arrange
            string tenantId = Guid.NewGuid().ToString("N");
            PolicyWhere where = new PolicyWhere();

            var mockMessageHandler = new Mock<HttpMessageHandler>();
            mockMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent("{}")
                });

            // Inject the handler or client into your application code
            var client = new HttpClient(mockMessageHandler.Object);

            var policyService = new CoverGoPolicyService(client);
            // Act
            var result = policyService.CheckIssuedStatusAsync(tenantId, where);
            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public void CheckForAddEndorsementTest()
        {
            // Arrange
            string tenantId = Guid.NewGuid().ToString("N");
            string type = "test";
            PolicyWhere where = new PolicyWhere();
            var mockMessageHandler = new Mock<HttpMessageHandler>();
            mockMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent("{}")
                });

            // Inject the handler or client into your application code
            var client = new HttpClient(mockMessageHandler.Object);

            var policyService = new CoverGoPolicyService(client);
            // Act
            var result = policyService.CheckForAddEndorsement(tenantId, type, where);
            // Assert
            Assert.NotNull(result);
        }
    }
}