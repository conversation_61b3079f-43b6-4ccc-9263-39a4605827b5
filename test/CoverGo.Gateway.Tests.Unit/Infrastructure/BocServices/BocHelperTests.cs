﻿using CoverGo.Gateway.Infrastructure.BOCServices;
using DocumentFormat.OpenXml.Office2010.ExcelAc;
using FluentAssertions;

using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Infrastructure.BocServices
{
    public class BocHelperTests
    {
        [Fact]
        public void WHEN_InceptionDate_Criteria_not_Met_THEN_MibEffective_should_be_false()
        {
            string inceptionDate = "2022-09-30";

            bool isMibEffective = BocHelper.MapIsMibEffective(inceptionDate);

            isMibEffective.Should().BeFalse();
        }

        [Fact]
        public void WHEN_InceptionDate_Criteria_Met_THEN_MibEffective_should_be_true()
        {
            string inceptionDate = "2022-10-01";

            bool isMibEffective = BocHelper.MapIsMibEffective(inceptionDate);

            isMibEffective.Should().BeTrue();
        }

        [Fact]
        public void WHEN_ExcessList_Available_As_String_in_Metadata_THEN_Can_be_Mapped_To_ExcessList()
        {
            string excessString = @"[{""seqNo"":""1"",""excessCode"":""1J"",""excessEName"":""Own Damage"",""excessTName"":""車身損毀自負額"",""excessRate"":"""",""amount"":""5000""},{""seqNo"":""2"",""excessCode"":""1B"",""excessEName"":""Theft"",""excessTName"":""盜竊自負額"",""excessRate"":"""",""amount"":""5000""},{""seqNo"":""3"",""excessCode"":""2A"",""excessEName"":""Young Driver"",""excessTName"":""年輕駕駛者自負額"",""excessRate"":""0"",""amount"":""5000""},{""seqNo"":""4"",""excessCode"":""3B"",""excessEName"":""Inexperienced Driver"",""excessTName"":""經驗不足駕駛者自負額"",""excessRate"":""0"",""amount"":""5000""},{""seqNo"":""5"",""excessCode"":""2A"",""excessEName"":""Third Parties Property Damage"",""excessTName"":""第三者財物損失自負額"",""excessRate"":""0"",""amount"":""3000""},{""seqNo"":""6"",""excessCode"":""1D"",""excessEName"":""Unnamed Driver"",""excessTName"":""不記名司機自負額"",""excessRate"":""0"",""amount"":""5000""},{""seqNo"":""7"",""excessCode"":""1C"",""excessEName"":""Parking Excess"",""excessTName"":""停泊時車身損毀自負額"",""excessRate"":""0"",""amount"":""2000""}]";

            var excessList = BocHelper.MapExcessList(excessString);

            excessList.Should().HaveCount(7);
        }

        [Fact]
        public void WHEN_ExcessList_Available_As_String_in_Metadata_THEN_Can_be_Mapped_To_ExcessList_As_JToken()
        {
            string excessString = @"[{""seqNo"":""1"",""excessCode"":""1J"",""excessEName"":""Own Damage"",""excessTName"":""車身損毀自負額"",""excessRate"":"""",""amount"":""5000""},{""seqNo"":""2"",""excessCode"":""1B"",""excessEName"":""Theft"",""excessTName"":""盜竊自負額"",""excessRate"":"""",""amount"":""5000""},{""seqNo"":""3"",""excessCode"":""2A"",""excessEName"":""Young Driver"",""excessTName"":""年輕駕駛者自負額"",""excessRate"":""0"",""amount"":""5000""},{""seqNo"":""4"",""excessCode"":""3B"",""excessEName"":""Inexperienced Driver"",""excessTName"":""經驗不足駕駛者自負額"",""excessRate"":""0"",""amount"":""5000""},{""seqNo"":""5"",""excessCode"":""2A"",""excessEName"":""Third Parties Property Damage"",""excessTName"":""第三者財物損失自負額"",""excessRate"":""0"",""amount"":""3000""},{""seqNo"":""6"",""excessCode"":""1D"",""excessEName"":""Unnamed Driver"",""excessTName"":""不記名司機自負額"",""excessRate"":""0"",""amount"":""5000""},{""seqNo"":""7"",""excessCode"":""1C"",""excessEName"":""Parking Excess"",""excessTName"":""停泊時車身損毀自負額"",""excessRate"":""0"",""amount"":""2000""}]";

            var excessList = BocHelper.MapExcessListAsJToken(excessString);

            excessList.Should().NotBeNull();
        }
    }
}
