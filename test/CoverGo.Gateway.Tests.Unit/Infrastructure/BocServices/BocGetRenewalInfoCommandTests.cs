﻿using AutoFixture;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Infrastructure.BOCServices.BocCommands;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;
using Newtonsoft.Json;
using Moq;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Microsoft.Extensions.Logging.Abstractions;
using static CoverGo.Gateway.Infrastructure.BOCServices.BocCommands.BocGetRenewalInfo2Command;
using FluentAssertions;
using CoverGo.Gateway.Domain;
using static CoverGo.Gateway.Infrastructure.BOCServices.BocCommands.BocGetRenewalPolicyCommand;

namespace CoverGo.Gateway.Tests.Unit.Infrastructure.BocServices
{
    public class BocGetRenewalInfoCommandTests
    {
        [Fact]
        public async Task GIVEN_command_with_recreateRenewalPolicy_WHEN_executeAsync_THEN_returns_new_policyId()
        {
            TestContext context = new();
            context.MockGetRenewalInfo();
            context.MockGetExistingPolicy();
            context.MockDeletePolicy();
            context.MockCreatePolicy();
            context.MockPolicyFactBatch();

            var input = context.MockInput();

            Result<string> result = await context.Sut.Object.ExecuteAsync(context.TenantId, input);

            BOCGetRenewalPolicyResponse getRenewalPolicyResponse = JsonConvert.DeserializeObject<BOCGetRenewalPolicyResponse>(result.Value);

            result.IsSuccess.Should().BeTrue();
            getRenewalPolicyResponse.policyId.Should().NotBeNullOrEmpty();
        }


        private sealed class TestContext
        {
            public Mock<IPolicyService> PolicyServiceMock { get; set; }
            public Mock<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>> IndividualServiceMock { get; set; }
            public Mock<IEntityService<CoverGo.Users.Domain.Objects.Object, CreateObjectCommand, UpdateObjectCommand>> ObjectServiceMock { get; set; }

            public Mock<IPricingService> PricingServiceMock { get; set; }

            public Mock<BocGetRenewalPolicyCommand> Sut { get; set; }
            public string TenantId { get; }

            private readonly IFixture _fixture;
            public TestContext()
            {
                _fixture = new Fixture();

                PolicyServiceMock = new Mock<IPolicyService>();
                IndividualServiceMock = new Mock<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>>();
                ObjectServiceMock = new Mock<IEntityService<Users.Domain.Objects.Object, CreateObjectCommand, UpdateObjectCommand>>();
                PricingServiceMock = new Mock<IPricingService>();

                Sut = new Mock<BocGetRenewalPolicyCommand>(
                    "",
                    PolicyServiceMock.Object,
                    IndividualServiceMock.Object,
                    ObjectServiceMock.Object,
                    PricingServiceMock.Object,
                    NullLogger<BocGetRenewalPolicyCommand>.Instance
                    )
                {
                    CallBase = true
                };

                TenantId = _fixture.Create<string>();


            }

            public void MockGetExistingPolicy() =>
            PolicyServiceMock.Setup(x => x.GetAsync(TenantId, It.IsAny<PolicyWhere>()))
                .ReturnsAsync(_fixture.CreateMany<Policy>(1).ToList());


            public void MockDeletePolicy() =>
                PolicyServiceMock.Setup(x => x.DeletePolicyAsync(TenantId, It.IsAny<string>(), It.IsAny<string>()))
                    .ReturnsAsync(_fixture.Create<Result>);

            public void MockCreatePolicy() =>
                PolicyServiceMock.Setup(x => x.CreatePolicyAsync(TenantId, It.IsAny<CreatePolicyCommand>()))
                    .ReturnsAsync(Result<PolicyStatus>.Success(new PolicyStatus() {
                        Id = _fixture.Create<Guid>().ToString(),
                        Status = _fixture.Create<string>()
                    }));

            public void MockPolicyFactBatch() =>
                PolicyServiceMock.Setup(x => x.PolicyFactBatch(TenantId, It.IsAny<string>(), It.IsAny<FactCommandBatch>()))
                    .ReturnsAsync(_fixture.Create<Result>());

            public void MockGetRenewalInfo() =>
                Sut.Setup(x => x.GetRenewalInfo2(It.IsAny<Gateway.Infrastructure.BOCServices.BocCommands.BocGetRenewalInfo2Command.GetRenewalInfo2Request>()))
                    .ReturnsAsync(
                    JsonConvert.DeserializeObject<GetRenewalInfo2Result>(@"{""rtnCode"":""0000"",""rtnMsg"":""Success"",""content"":{""proposedBaseInfo"":{""proposedType"":""CO"",""proposedCode"":""MPE"",""agentNo"":null,""inceptionDate"":""2023-12-20"",""expiryDate"":""2024-12-19"",""rebateRate"":null,""ncd"":""20"",""preCreditCardNo"":null,""promotionCode"":null,""b2bPolicyNo"":""BCN-MPE-10001332-0""},""namedDriverInfo"":[{""drivingExperience"":""5"",""namedDriverBirthDay"":""1995-12-20"",""namedDriverGender"":""M"",""namedDriverIdCard"":""********"",""namedDriverName"":""KIN HEI FUNG"",""namedDriverOccupation"":""030"",""namedDriverBirthday"":null,""relWithProposer"":null}],""hzmbInsuranceInfo"":{""expiryDate"":""2023-12-19"",""flag"":""Y"",""mibPremium02"":""0.0"",""mibPremium03"":""0.0"",""mibPremiumBZ"":""0.0"",""planType"":null,""premium02"":""1128.0"",""premium03"":""1835.0"",""premiumBZ"":""894.0"",""startDate"":""2022-12-20"",""sumInsured02"":""2000000.0"",""assigned_to_specific_email"":false,""specific_email"":null},""feeInfo"":{""commissionRate"":null,""commissionPremium"":null,""discountRate"":null,""discountPremium"":null,""mibRate"":null,""mibPremium"":null,""iaRate"":null,""iaPremium"":null,""grossPremium"":""7827.07"",""netPremium"":""7827.07"",""totalPremium"":""8069.71""},""proposerInfo"":{""proposerIdentity"":""P"",""clientGroup"":""Direct"",""proposerName"":""KIN HEI FUNG"",""proposerGender"":""M"",""proposerIdCard"":""********"",""proposerBirthDay"":""1995-12-20"",""proposerOccupation"":""030"",""proposerOccupationN"":""Account"",""proposerMobile"":""********"",""proposerEmail"":""<EMAIL>"",""proposerAddress"":""ROOM J FLOOR 12 BLOCK N\nGGGGG TAI WAI\nSouthern District HK ISLAND"",""proposerDistrict"":null,""directSalePromotion"":null,""directSaleEmail"":null,""directSaleSMS"":null,""directSaleMail"":null,""directSalePhone"":null},""carInfo"":{""manufacturer"":""ACURA"",""model"":""146"",""modelDesc"":""146"",""manufactureYear"":null,""vehicleAge"":""1"",""vehicleValue"":""126000.00"",""licenseNo"":""CC1111"",""seatCapacity"":""4"",""engineCapacity"":""1400"",""chassisNo"":""QASDDD"",""engineNo"":""WQERTYU"",""vehicleClass"":""P1"",""bodyType"":""SALOON"",""hirePurchaseCode"":null,""insuranceCertificate"":null,""modelLoadRate"":""0""},""uwQuestions"":{""accidentNum"":""0"",""drivingOffenceFlag"":""N""},""assigned_to_specific_email"":false,""specific_email"":"""",""excessList"":[{""seqNo"":""1"",""excessCode"":""1J"",""excessEName"":null,""excessTName"":null,""excessRate"":""0"",""amount"":""5000.00""},{""seqNo"":""2"",""excessCode"":""1B"",""excessEName"":null,""excessTName"":null,""excessRate"":""0"",""amount"":""5000.00""},{""seqNo"":""3"",""excessCode"":""3A"",""excessEName"":null,""excessTName"":null,""excessRate"":""0"",""amount"":""5000.00""},{""seqNo"":""4"",""excessCode"":""3B"",""excessEName"":null,""excessTName"":null,""excessRate"":""0"",""amount"":""5000.00""},{""seqNo"":""5"",""excessCode"":""2A"",""excessEName"":null,""excessTName"":null,""excessRate"":""0"",""amount"":""3000.00""},{""seqNo"":""6"",""excessCode"":""1D"",""excessEName"":null,""excessTName"":null,""excessRate"":""0"",""amount"":""5000.00""},{""seqNo"":""7"",""excessCode"":""1C"",""excessEName"":null,""excessTName"":null,""excessRate"":""0"",""amount"":""2000.00""}]}}")
                    );


            public JObject MockInput()
            {
                return JObject.FromObject(new
                {
                    policyNo = _fixture.Create<string>(),
                    licenseNo = _fixture.Create<string>(),
                    clientId = _fixture.Create<string>(),
                    loginIdFromToken = _fixture.Create<string>(),
                    recreateRenewal = true,
                });
            }

        }
    }
}
