using System.Collections.Generic;
using System.Threading;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.Notifications;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Policies.Client;
using Moq;
using Xunit;
using Policy = CoverGo.Policies.Client.Policy;
using Transaction = CoverGo.Gateway.Domain.Policies.Transaction;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;
using PolicyMember = CoverGo.Policies.Client.PolicyMember;
using PolicyMembersWhere = CoverGo.Policies.Client.PolicyMembersWhere;
using IssuePolicyCommand = CoverGo.Policies.Client.IssuePolicyCommand;
using UpdatePolicyCommand = CoverGo.Policies.Client.UpdatePolicyCommand;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Infrastructure.InsuredNomadsServices.InsuredNomadsServicesCommands;
using Microsoft.Extensions.Logging.Abstractions;
using Newtonsoft.Json.Linq;
using System.Threading.Tasks;
using Pipedrive;
using FluentAssertions;

namespace CoverGo.Gateway.Tests.Unit.Infrastructure.InsuredNomadsServices;

public class ProcessPolicyAndMembersCommandTests
{
    private readonly Mock<IAuthService> _mockAuthService;
    private readonly Mock<IEntityService> _mockEntityService;
    private readonly Mock<IFileSystemService> _mockFileSystemService;
    private readonly Mock<INotificationService> _mockNotificationService;
    private readonly Mock<IPoliciesClient> _mockPoliciesClient;
    private readonly Mock<ITemplateService> _mockTemplateService;
    private readonly Mock<ITransactionService> _mockTransactionService;
    public ProcessPolicyAndMembersCommandTests()
    {
        _mockAuthService = new Mock<IAuthService>();
        _mockEntityService = new Mock<IEntityService>();
        _mockFileSystemService = new Mock<IFileSystemService>();
        _mockNotificationService = new Mock<INotificationService>();
        _mockPoliciesClient = new Mock<IPoliciesClient>();
        _mockTemplateService = new Mock<ITemplateService>();
        _mockTransactionService = new Mock<ITransactionService>();
        SetupMockedServices();
    }

    [Fact]
    public async Task GIVEN_valid_input_json_WHEN_ExecuteAsync_THEN_populate_input_json_safeture_integration()
    {
        // Arrange
        ProcessPolicyAndMembers processPolicyAndMembers = new(
            _mockAuthService.Object,
            _mockEntityService.Object,
            _mockFileSystemService.Object,
            _mockNotificationService.Object,
            _mockPoliciesClient.Object,
            _mockTemplateService.Object,
            _mockTransactionService.Object,
            NullLogger<ProcessPolicyAndMembers>.Instance);
        string tenantId = "test-tenant-id";
        JObject inputJson = JObject.Parse(@"{""transactionId"": ""test-transactionId""}");
        string expectedSubscriptionId = "MEM-100";
        string expectedProductDescription = "Product: , Type: Individual(s), # Policy Members: 2, Primary Policy Number: IN100-0E7UGVGT";

        // Act
        var result = await processPolicyAndMembers.ExecuteAsync(tenantId, inputJson);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be("IN100-0E7UGVGT");

        // Extract safetureSubscriptionBatchAdd from inputJson
        var safetureSubscriptionBatchAdd = inputJson["safetureSubscriptionBatchAdd"];
        safetureSubscriptionBatchAdd.Should().NotBeNull();

        var batchInputs = safetureSubscriptionBatchAdd["Inputs"];
        var input = batchInputs[0];
        expectedSubscriptionId.Should().Be(input["appsubscriptionid"].ToString());
        expectedProductDescription.Should().Be(input["description"].ToString());
    }


    private void SetupMockedServices()
    {
        _mockTransactionService.Setup(s => s.GetAsync(It.IsAny<string>(), It.IsAny<QueryArguments>()))
            .ReturnsAsync(GetMockedTransactions);
        _mockPoliciesClient.Setup(s => s.Policy_GetPoliciesAsync(It.IsAny<string>(), It.IsAny<QueryArgumentsOfPolicyWhere>()))
            .ReturnsAsync(GetMockedPolicies());
        _mockPoliciesClient.Setup(s => s.PolicyMembers_QueryAsync(It.IsAny<string>(), It.IsAny<PolicyMembersWhere>()))
            .ReturnsAsync(GetMockedPolicyMembers());
        _mockPoliciesClient.Setup(s => s.Policy_IssuePolicyAsync(It.IsAny<string>(), It.IsAny<IssuePolicyCommand>(), It.IsAny<string>()))
            .ReturnsAsync(new ResultOfPolicyStatus() { Status = "success" });
        _mockPoliciesClient.Setup(s => s.Policy_UpdatePolicyPUTAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<UpdatePolicyCommand>()))
            .ReturnsAsync(Result.Success());
        _mockAuthService.Setup(s => s.GetLoginByNameAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(new Login());
        _mockTemplateService.Setup(s => s.RenderEmailAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<CoverGo.Gateway.Domain.Templates.RenderParameters>()))
            .ReturnsAsync(Result<EmailRendered>.Success(new EmailRendered()));
        _mockNotificationService.Setup(s => s.SendAsync(It.IsAny<string>(), It.IsAny<SendNotificationCommand>()))
            .ReturnsAsync(Result.Success());
        _mockFileSystemService.Setup(s => s.UploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<UploadFileCommand>()))
            .ReturnsAsync(Result.Success());
    }

    private List<Transaction> GetMockedTransactions() => new() { new Transaction() { PolicyId = GetGuidString() } };
    private List<Policy> GetMockedPolicies() => new() { new Policy() {
        Id = GetGuidString(),
        IssuerNumber = "IN100-0E7UGVGT",
        StartDate = new System.DateTime(2023, 11, 1),
        EndDate = new System.DateTime(2023, 11, 8),
        ContractHolder = new CoverGo.Policies.Client.Entity{
            Id = GetGuidString(),
            Name = "Test Client",
            Fields = JToken.Parse(@"{
                    ""firstName"": ""Test"",
                    ""lastName"": ""Client"",
                    ""email"": """",
                    ""clientType"": ""individual"",
                    }")
        },
        Fields = JToken.Parse(@"{
                    ""clientType"": ""individual"",
                    }"),
        ExtraFields = JToken.Parse(@"{
                ""clientName"": ""Test Client"",
                ""clientEmail"": ""<EMAIL>"",
                ""clientType"": ""individual"",
                ""source"": ""distribution"",
                ""productTypeId"": ""tv"",
                ""productName"": ""Test Product 1"",
                ""fields"": ""{\""startDate\"":\""2023-11-01\"", \""endDate\"":\""2023-11-08\"", \""dataInput\"":{}}""
            }")
            }
        };

    private List<PolicyMember> GetMockedPolicyMembers() => new() {
        new PolicyMember() {
            Id = GetGuidString(),
            MemberId = "MEM-100",
            Fields = JToken.Parse(@"{
                    ""memberType"": ""primary"",
                    ""name"": ""Member 100"",
                    ""email"": ""<EMAIL>"",
            }")
        },
        new PolicyMember() {
            Id = GetGuidString(),
            MemberId = "MEM-101",
            Fields = JToken.Parse(@"{
                    ""memberType"": ""dependent"",
                    ""name"": ""Member 101"",
                    ""email"": ""<EMAIL>"",
                    ""DependentOf"": ""MEM-100""
            }")
        }
    };



    private string GetGuidString() => System.Guid.NewGuid().ToString();

}