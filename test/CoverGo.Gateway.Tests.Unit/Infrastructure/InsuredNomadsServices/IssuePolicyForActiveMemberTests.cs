using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using AutoFixture;
using CoverGo.CryptoUtils;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Infrastructure.InsuredNomadsServices.InsuredNomadsServicesCommands;
using CoverGo.Policies.Client;
using FluentAssertions;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using Xunit;
using static CoverGo.Gateway.Infrastructure.InsuredNomadsServices.InsuredNomadsServicesCommands.IssuePolicyForActiveMember;

namespace CoverGo.Gateway.Tests.Unit.Infrastructure.InsuredNomadsServices;

public sealed class IssuePolicyForActiveMemberTests
{
    private readonly IFixture _fixture = new Fixture();

    [Fact]
    public async Task GIVEN_mainInsuredId_is_empty_WHEN_ExecuteAsync_THEN_return_failure()
    {
        TestContext testContext = new();
        testContext.InitializeSut(_fixture);
        IssuePolicyForActiveMemberInput issuePolicyForActiveMemberInput = _fixture.Build<IssuePolicyForActiveMemberInput>()
            .Without(x => x.MainInsuredId)
            .Create();
        JObject inputJson = JObject.FromObject(issuePolicyForActiveMemberInput);

        Result<string> result = await testContext.Sut.Object.ExecuteAsync(testContext.TenantId, inputJson);

        result.IsSuccess.Should().BeFalse();
    }


    [Fact]
    public async Task GIVEN_memberStatus_is_not_update_WHEN_ExecuteAsync_THEN_return_success()
    {
        TestContext testContext = new();
        testContext.InitializeSut(_fixture);
        IssuePolicyForActiveMemberInput issuePolicyForActiveMemberInput = _fixture.Create<IssuePolicyForActiveMemberInput>();
        JObject inputJson = JObject.FromObject(issuePolicyForActiveMemberInput);

        Result<string> result = await testContext.Sut.Object.ExecuteAsync(testContext.TenantId, inputJson);

        result.IsSuccess.Should().BeTrue();
    }

    [Fact]
    public async Task GIVEN_mainInsured_is_not_yet_active_and_environment_variables_are_missing_WHEN_ExecuteAsync_THEN_throws_exception()
    {
        TestContext testContext = new();
        testContext.InitializeSut(_fixture);
        IssuePolicyForActiveMemberInput issuePolicyForActiveMemberInput = _fixture.Build<IssuePolicyForActiveMemberInput>()
            .With(x => x.Status, "update")
            .Create();
        JObject inputJson = JObject.FromObject(issuePolicyForActiveMemberInput);

        Func<Task<Result<string>>> action = async () => await testContext.Sut.Object.ExecuteAsync(testContext.TenantId, inputJson);

        await action.Should().ThrowAsync<InvalidOperationException>();
    }

    [Fact]
    public async Task GIVEN_mainInsured_is_not_yet_active_and_environment_variables_are_set_WHEN_ExecuteAsync_THEN_policy_is_not_issued()
    {
        TestContext testContext = new();
        testContext.InitializeSut(_fixture);
        IssuePolicyForActiveMemberInput issuePolicyForActiveMemberInput = _fixture.Build<IssuePolicyForActiveMemberInput>()
            .With(x => x.Status, "update")
            .Create();
        JObject inputJson = JObject.FromObject(issuePolicyForActiveMemberInput);

        testContext.InitializeEnvVariables();

        testContext.Sut.Setup(x => x.ExecuteAsync($"{testContext.ExpaTpaBaseUrl}/v1/core/auth", It.IsAny<RestRequest>()))
            .ReturnsAsync(Result<string>.Success(JsonConvert.SerializeObject(new { token = _fixture.Create<string>() })));

        string encryptedInput = Encrypt(testContext, new { policyStatus = _fixture.Create<string>() });
        testContext.Sut.Setup(x => x.ExecuteAsync($"{testContext.ExpaTpaBaseUrl}/v1/core/policymemberdetail", It.IsAny<RestRequest>()))
            .ReturnsAsync(Result<string>.Success(encryptedInput));

        Result<string> result = await testContext.Sut.Object.ExecuteAsync(testContext.TenantId, inputJson);

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        testContext.PoliciesClientMock.Verify(x => x.Policy_IssuePolicyAsync(It.IsAny<string>(), It.IsAny<IssuePolicyCommand>(), It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task GIVEN_primaryInsured_is_not_found_for_active_member_WHEN_ExecuteAsync_THEN_policy_is_not_issued()
    {
        TestContext testContext = new();
        testContext.InitializeSut(_fixture);
        IssuePolicyForActiveMemberInput issuePolicyForActiveMemberInput = _fixture.Build<IssuePolicyForActiveMemberInput>()
            .With(x => x.Status, "update")
            .Create();
        JObject inputJson = JObject.FromObject(issuePolicyForActiveMemberInput);

        testContext.InitializeEnvVariables();

        testContext.Sut.Setup(x => x.ExecuteAsync($"{testContext.ExpaTpaBaseUrl}/v1/core/auth", It.IsAny<RestRequest>()))
            .ReturnsAsync(Result<string>.Success(JsonConvert.SerializeObject(new { token = _fixture.Create<string>() })));

        string encryptedInput = Encrypt(testContext, new { policyStatus = "active" });
        testContext.Sut.Setup(x => x.ExecuteAsync($"{testContext.ExpaTpaBaseUrl}/v1/core/policymemberdetail", It.IsAny<RestRequest>()))
            .ReturnsAsync(Result<string>.Success(encryptedInput));
        testContext.PoliciesClientMock.Setup(x => x.PolicyMembers_QueryAsync(testContext.TenantId, It.IsAny<PolicyMembersWhere>()))
            .ReturnsAsync(new List<PolicyMember>());

        Result<string> result = await testContext.Sut.Object.ExecuteAsync(testContext.TenantId, inputJson);

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        testContext.PoliciesClientMock.Verify(x => x.Policy_IssuePolicyAsync(It.IsAny<string>(), It.IsAny<IssuePolicyCommand>(), It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task GIVEN_primaryInsured_is_found_for_active_member_WHEN_ExecuteAsync_THEN_policy_is_issued_and_crm_notified()
    {
        TestContext testContext = new();
        testContext.InitializeSut(_fixture);
        IssuePolicyForActiveMemberInput issuePolicyForActiveMemberInput = _fixture.Build<IssuePolicyForActiveMemberInput>()
            .With(x => x.Status, "update")
            .Create();
        JObject inputJson = JObject.FromObject(issuePolicyForActiveMemberInput);

        testContext.InitializeEnvVariables();

        testContext.Sut.Setup(x => x.ExecuteAsync($"{testContext.ExpaTpaBaseUrl}/v1/core/auth", It.IsAny<RestRequest>()))
            .ReturnsAsync(Result<string>.Success(JsonConvert.SerializeObject(new { token = _fixture.Create<string>() })));

        string encryptedInput = Encrypt(testContext, new { policyStatus = "active" });
        testContext.Sut.Setup(x => x.ExecuteAsync($"{testContext.ExpaTpaBaseUrl}/v1/core/policymemberdetail", It.IsAny<RestRequest>()))
            .ReturnsAsync(Result<string>.Success(encryptedInput));
        string policyId = _fixture.Create<string>();
        bool issuedPolicy = true;
        bool notifiedCrm = false;
        testContext.PoliciesClientMock.Setup(x => x.PolicyMembers_QueryAsync(testContext.TenantId, It.IsAny<PolicyMembersWhere>()))
            .ReturnsAsync(_fixture.Build<PolicyMember>().With(pm => pm.PolicyId, policyId).Without(pm => pm.Activity).CreateMany(1).ToList());
        testContext.PoliciesClientMock.Setup(x => x.Policy_IssuePolicyAsync(testContext.TenantId, new IssuePolicyCommand { PolicyId = policyId }, null))
            .ReturnsAsync(new ResultOfPolicyStatus { Status = "success" })
            .Callback<string, IssuePolicyCommand, string>((tenantId, command, correlationId) =>
            {
                issuedPolicy = true;
            });
        testContext.PoliciesClientMock.Setup(x => x.Policy_GetPolicyAsync(testContext.TenantId, policyId, null))
            .ReturnsAsync(_fixture.Build<Policy>().With(x => x.Id, policyId).CreateMany(1).ToList().FirstOrDefault());
        testContext.Sut.Setup(x => x.ExecuteAsync(testContext.CrmBaseUrl, It.IsAny<RestRequest>()))
            .ReturnsAsync(Result<string>.Success("success"))
            .Callback<string, RestRequest>((url, request) =>
            {
                notifiedCrm = true;
            });

        Result<string> result = await testContext.Sut.Object.ExecuteAsync(testContext.TenantId, inputJson);

        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        issuedPolicy.Should().BeTrue();
        notifiedCrm.Should().BeTrue();
    }

    private string Encrypt(TestContext testContext, object serializableObject)
    {
        using Aes aesAlg = Aes.Create();
        aesAlg.Key = Convert.FromBase64String(testContext.AesKey);
        aesAlg.IV = new byte[16];
        string encryptedInput = Convert.ToBase64String(AesHelpers.Encrypt(
            JsonConvert.SerializeObject(serializableObject), aesAlg));

        return encryptedInput;
    }

    private sealed class TestContext
    {
        private IFixture _fixture;
        public string TenantId { get; set; }
        public Mock<IPoliciesClient> PoliciesClientMock { get; set; }
        public Mock<IssuePolicyForActiveMember> Sut { get; set; }
        public string AesKey { get; set; }
        public string ExpaTpaBaseUrl { get; set; }
        public string CrmBaseUrl { get; set; }

        public void InitializeSut(IFixture fixture)
        {
            _fixture = fixture;
            TenantId = _fixture.Create<string>();
            PoliciesClientMock = new();
            Sut = new(NullLogger<IssuePolicyForActiveMember>.Instance, PoliciesClientMock.Object);
            Sut.CallBase = true;
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList().ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());
        }

        public void InitializeEnvVariables()
        {
            ExpaTpaBaseUrl = _fixture.Create<string>();
            CrmBaseUrl = _fixture.Create<string>();
            AesKey = "Nx1KRdkvMFFGvjzTwwj7qn3IsjjBbVM7Tcnz0nA5UUQ=";
            Environment.SetEnvironmentVariable($"{TenantId}_ExpaTPA_Base_Url", ExpaTpaBaseUrl);
            Environment.SetEnvironmentVariable($"{TenantId}_ExpaTPA_Ccode", _fixture.Create<string>());
            Environment.SetEnvironmentVariable($"{TenantId}_ExpaTPA_Skey", _fixture.Create<string>());
            Environment.SetEnvironmentVariable($"{TenantId}_ExpaTPA_Owner", _fixture.Create<string>());
            Environment.SetEnvironmentVariable($"{TenantId}_ExpaTPA_AesKey", AesKey);
            Environment.SetEnvironmentVariable($"{TenantId}_CRM_Base_Url", CrmBaseUrl);
        }
    }
}
