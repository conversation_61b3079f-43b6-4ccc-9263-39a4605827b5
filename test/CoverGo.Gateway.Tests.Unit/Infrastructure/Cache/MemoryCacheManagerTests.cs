﻿using System;
using System.Threading.Tasks;
using CoverGo.Gateway.Infrastructure.Cache;
using FluentAssertions;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Infrastructure.Cache;

public class MemoryCacheManagerTests
{
    private readonly MemoryCacheManager _cacheManager;
    private readonly IMemoryCache _memoryCache;
    
    public MemoryCacheManagerTests()
    {
        ServiceCollection services = new();
        services.AddMemoryCache();
        ServiceProvider serviceProvider = services.BuildServiceProvider();

        _memoryCache = serviceProvider.GetService<IMemoryCache>();
        _cacheManager = new MemoryCacheManager(_memoryCache);
    }
    
    [Theory]
    [InlineData(1, 2)]
    [InlineData("1", "2")]
    public async Task GIVEN_cache_hit_WHEN_GetOrSet_THEN_return_cached_data(object cachedValue, object newValue)
    {
        _memoryCache.Set("key", cachedValue);
        
        object result = await _cacheManager.GetOrSet("key", async () => await Task.FromResult(newValue), TimeSpan.FromMinutes(10));

        result.Should().Be(cachedValue);
    }


    [Theory]
    [InlineData(1)]
    [InlineData("1")]
    public async Task GIVEN_cache_miss_WHEN_GetOrSet_THEN_return_callback_result(object newValue)
    {
        object result = await _cacheManager.GetOrSet("key", async () => await Task.FromResult(newValue), TimeSpan.FromMinutes(10));
        
        result.Should().Be(newValue);
    }

    [Theory(Skip = "Temporarily disable due to unstable CI")]
    [InlineData(1000, 500, 1, 2, 1)]
    [InlineData(1000, 1500, 1, 2, 2)]
    public async Task GIVEN_cache_duration_WHEN_GetOrSet_THEN_respect_cache_duration(int cacheMilliseconds, int delayMilliseconds, int value1, int value2, int expectedValue)
    {
        await _cacheManager.GetOrSet("key", async () => await Task.FromResult(value1), TimeSpan.FromMilliseconds(cacheMilliseconds));
        
        await Task.Delay(delayMilliseconds);
        
        int cachedValue = await _cacheManager.GetOrSet("key", async () => await Task.FromResult(value2), TimeSpan.FromMinutes(1));

        cachedValue.Should().Be(expectedValue);
    }

    [Fact]
    public async Task GIVEN_negative_cache_duration_WHEN_GetOrSet_THEN_throw_exception()
    {
        Mock<IMemoryCache> mockMemoryCache = new();
        MemoryCacheManager cacheManager = new(mockMemoryCache.Object);

        Func<Task<string>> action = () => cacheManager.GetOrSet("key", async () => await Task.FromResult("666"), TimeSpan.FromMinutes(-1));

        await action.Should().ThrowAsync<ArgumentOutOfRangeException>();
    }

    [Fact]
    public async Task GIVEN_invalid_cache_key_WHEN_GetOrSet_THEN_throw_exception()
    {
        Func<Task<string>> action = () =>  _cacheManager.GetOrSet(null, async () => await Task.FromResult("666"), TimeSpan.FromMinutes(10));
        await action.Should().ThrowAsync<ArgumentNullException>()
            .WithMessage("Value cannot be null. (Parameter 'key')");
    }

    [Fact]
    public async Task GIVEN_exception_in_callback_WHEN_GetOrSet_THEN_throw_exception()
    {
        Func<Task<string>> action = () =>  _cacheManager.GetOrSet("key", async () => await Task.FromException<string>(new InvalidOperationException()), TimeSpan.FromMinutes(10));
        await action.Should().ThrowAsync<InvalidOperationException>();
    }
}