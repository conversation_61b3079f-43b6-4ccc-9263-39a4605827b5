using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using AutoFixture;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Infrastructure.DLVNServices.DLVNCommands;
using FluentAssertions;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using Xunit;
using static CoverGo.Gateway.Infrastructure.DLVNServices.DLVNCommands.DLVNGetAgentInfoCommand;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Tests.Unit.Infrastructure.DLVNServices;

public sealed class DLVNGetAgentInfoCommandTests
{
    [Fact]
    public async Task GIVEN_input_without_agentId_WHEN_executeAsync_THEN_returns_failure()
    {
        TestContext context = new();

        Result<string> result = await context.Sut.Object.ExecuteAsync(context.TenantId, JObject.Parse("{}"));

        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_input_with_only_agentId_WHEN_executeAsync_THEN_returns_success()
    {
        TestContext context = new();
        CommandInput input = context.GetCommandInputWithoutProductPlanId();
        context.MockValidFileSystemConfigForTenant();
        context.MockDlvnServiceValidAuthTokenResult();
        context.MockDlvnValidUnderwritingEndpoint();
        context.MockDlvnServiceValidGetAgentApiResult();

        Result<string> result = await context.Sut.Object.ExecuteAsync(context.TenantId, JObject.FromObject(input));

        result.IsSuccess.Should().BeTrue();
        result.Value.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_valid_input_WHEN_executeAsync_THEN_returns_success()
    {
        TestContext context = new();
        CommandInput input = context.GetCommandInput();
        context.MockFoundProductResult();
        context.MockValidFileSystemConfigForTenant();
        context.MockDlvnServiceValidAuthTokenResult();
        context.MockDlvnServiceValidGetAgentApiResult();

        Result<string> result = await context.Sut.Object.ExecuteAsync(context.TenantId, JObject.FromObject(input));

        result.IsSuccess.Should().BeTrue();
        result.Value.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_valid_input_WHEN_executeAsync_without_tenant_bucketName_THEN_returns_failure()
    {
        TestContext context = new();
        CommandInput input = context.GetCommandInput();
        context.MockFoundProductResult();
        context.MockNoFileSystemConfigForTenant();

        Result<string> result = await context.Sut.Object.ExecuteAsync(context.TenantId, JObject.FromObject(input));

        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_valid_input_WHEN_executeAsync_without_token_THEN_returns_failure()
    {
        TestContext context = new();
        CommandInput input = context.GetCommandInput();
        context.MockFoundProductResult();
        context.MockValidFileSystemConfigForTenant();
        context.MockDlvnServiceNoAuthTokenFound();

        Result<string> result = await context.Sut.Object.ExecuteAsync(context.TenantId, JObject.FromObject(input));

        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_valid_input_WHEN_executeAsync_is_unable_to_retrieve_agent_info_THEN_returns_failure()
    {
        TestContext context = new();
        CommandInput input = context.GetCommandInput();
        context.MockFoundProductResult();
        context.MockValidFileSystemConfigForTenant();
        context.MockDlvnServiceValidAuthTokenResult();
        context.MockDlvnGetAgentApiResultWithFailure();

        Result<string> result = await context.Sut.Object.ExecuteAsync(context.TenantId, JObject.FromObject(input));

        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().NotBeNullOrEmpty();
    }

    private sealed class TestContext
    {
        public Mock<IFileSystemService> FileSystemMock { get; set; }
        public Mock<IProductService> ProductServiceMock { get; set; }
        public string TenantId { get; set; }
        public Mock<DLVNGetAgentInfoCommand> Sut { get; set; }
        private readonly IFixture _fixture;

        public TestContext()
        {
            _fixture = new Fixture();
            FileSystemMock = new Mock<IFileSystemService>();
            ProductServiceMock = new Mock<IProductService>();
            TenantId = _fixture.Create<string>();

            Sut = new Mock<DLVNGetAgentInfoCommand>(
                FileSystemMock.Object,
                NullLogger<DLVNGetAgentInfoCommand>.Instance,
                ProductServiceMock.Object)
            {
                CallBase = true
            };
        }

        public CommandInput GetCommandInputWithoutProductPlanId() =>
            _fixture.Build<CommandInput>()
                .Without(x => x.ProductIdPlan)
                .Create();

        public CommandInput GetCommandInput() => _fixture.Create<CommandInput>();

        public void MockValidFileSystemConfigForTenant() =>
            FileSystemMock.Setup(x => x.GetConfigsAsync(TenantId, It.IsAny<QueryArguments>()))
                .ReturnsAsync(_fixture.CreateMany<FileSystemConfig>(1));

        public void MockNoFileSystemConfigForTenant() =>
            FileSystemMock.Setup(x => x.GetConfigsAsync(TenantId, It.IsAny<QueryArguments>()))
                .ReturnsAsync(new List<FileSystemConfig>());

        public void MockDlvnServiceValidAuthTokenResult()
        {
            string tokenCachedInfo = JsonConvert.SerializeObject(new
            {
                tokenCreatedAt = DateTime.Now,
                tokenInfo = new
                {
                    message = new
                    {
                        code = _fixture.Create<string>(),
                        description = _fixture.Create<string>()
                    },
                    data = new
                    {
                        token = _fixture.Create<string>()
                    }
                }
            });
            FileSystemMock.Setup(x => x.GetFileAsync(TenantId, It.IsAny<string>(), It.IsAny<GetFileCommand>()))
                .ReturnsAsync(Result<byte[]>.Success(Encoding.UTF8.GetBytes(tokenCachedInfo)));
        }

        public void MockDlvnServiceNoAuthTokenFound()
        {
            FileSystemMock.Setup(x => x.GetFileAsync(TenantId, It.IsAny<string>(), It.IsAny<GetFileCommand>()))
                .ReturnsAsync(Result<byte[]>.Failure("File not found"));
        }

        public void MockFoundProductResult()
        {
            Product2 product = _fixture.Build<Product2>()
                .Without(x => x.Benefits)
                .Without(x => x.RejectionSettings)
                .Without(x => x.ExclusionSettings)
                .Without(x => x.ClaimSettings)
                .Without(x => x.LoadingSettings)
                .With(x => x.Facts, new List<Fact>
                {
                    _fixture.Build<Fact>()
                        .With(f => f.Type, "tokenDomain")
                        .With(f => f.Value, _fixture.Create<Uri>().AbsoluteUri)
                        .Create(),
                    _fixture.Build<Fact>()
                        .With(f => f.Type, "tokenPath")
                        .With(f => f.Value, _fixture.Create<string>())
                        .Create()
                })
                .Create();
            ProductServiceMock.Setup(x => x.GetAsync(TenantId, null, It.IsAny<ProductQuery>()))
                .ReturnsAsync(new List<Product2> { product });
        }

        public void MockDlvnValidUnderwritingEndpoint() =>
            Environment.SetEnvironmentVariable("DLVN_UAT_UW_API_ENDPOINT", _fixture.Create<Uri>().AbsoluteUri);

        public void MockDlvnServiceValidGetAgentApiResult() =>
            Sut.Setup(x => x.ExecuteAsync<JObject>(It.IsAny<string>(), It.IsAny<RestRequest>(), false))
                .ReturnsAsync(Result<JObject>.Success(JObject.FromObject(new
                {
                    message = new
                    {
                        code = "00",
                        description = "success"
                    },
                    data = new
                    {
                        branchList = new[]
                        {
                            new
                            {
                                branchCode = _fixture.Create<string>(),
                                branchName = _fixture.Create<string>(),
                            }
                        }
                    }
                })));

        public void MockDlvnGetAgentApiResultWithFailure() =>
            Sut.Setup(x => x.ExecuteAsync<JObject>(It.IsAny<string>(), It.IsAny<RestRequest>(), false))
                .ReturnsAsync(Result<JObject>.Failure("Unable to retrieve agent info"));
    }
}