﻿using System.Linq;
using System.Text;
using CoverGo.Gateway.Infrastructure;
using CoverGo.Gateway.Infrastructure.Auth;
using FluentAssertions;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Newtonsoft.Json;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Infrastructure.Auth;

public class DistributedCacheStateDataFormatterTests
{
    readonly Mock<IHttpContextAccessor> _mockHttpContextAccessor;
    readonly Mock<IDistributedCache> _mockDistributedCache;
    readonly Mock<IDataProtectionProvider> _mockDataProtectionProvider;
    readonly Mock<IDataProtector> _mockDataProtector;
    private readonly ServiceCollection _services;
    private const string Key = "test-key";
    private const string ProtectedKey = "dGVzdC1rZXk";
    
    public DistributedCacheStateDataFormatterTests()
    {
        _mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
        _mockDistributedCache = new Mock<IDistributedCache>();
        _mockDataProtectionProvider = new Mock<IDataProtectionProvider>();
        _mockDataProtector = new Mock<IDataProtector>();
        _services = new ServiceCollection();
        SetupRequiredServices();
    }
    
    [Fact]
    public void GIVEN_valid_parameters_WHEN_Protect_THEN_serialized_authProps_should_be_added_into_cache_AND_return_protected_key()
    {
        DistributedCacheStateDataFormatter formatter = new(_mockHttpContextAccessor.Object, "test-scheme");
        AuthenticationProperties authProps = new() { Items = { ["Property"] = "Value" } };
        
        byte[] expectedValueInCache =  Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(authProps, new JsonSerializerSettings
        {
            DefaultValueHandling = DefaultValueHandling.Ignore,
            NullValueHandling = NullValueHandling.Ignore
        }));
        
        string protectedKey = formatter.Protect(authProps);
        
        _mockDistributedCache.Verify(cache => cache.Set(
            It.Is<string>(key => key.StartsWith("DistributedCacheStateDataFormatter-test-scheme--")),
            It.Is<byte[]>(value => value.SequenceEqual(expectedValueInCache)),
            It.Is<DistributedCacheEntryOptions>(options => options.SlidingExpiration == Constants.DefaultCacheDuration)), Times.Once);

        protectedKey.Should().Be(ProtectedKey);
    }
    
    [Fact]
    public void GIVEN_valid_parameters_WHEN_UnProtect_THEN_return_expected_data_THEN_data_should_get_from_cache_with_correct_key()
    {
        DistributedCacheStateDataFormatter formatter = new(_mockHttpContextAccessor.Object, "test-scheme");
        
        formatter.Unprotect(ProtectedKey);
        
        _mockDistributedCache.Verify(cache => cache.Get(
            It.Is<string>(key => key.StartsWith("DistributedCacheStateDataFormatter-test-scheme--test-key"))), Times.Once);
    }

    private void SetupRequiredServices()
    {
        _mockDataProtector
            .Setup(protector => protector.Protect(It.IsAny<byte[]>()))
            .Returns(Encoding.UTF8.GetBytes(Key));
        _mockDataProtector
            .Setup(protector => protector.Unprotect(It.IsAny<byte[]>()))
            .Returns(Encoding.UTF8.GetBytes(Key));
        _mockDataProtector
            .Setup(protector => protector.CreateProtector(It.IsAny<string>()))
            .Returns(_mockDataProtector.Object);
        _mockDataProtectionProvider
            .Setup(provider => provider.CreateProtector(It.IsAny<string>()))
            .Returns(_mockDataProtector.Object);
        
        _services.AddSingleton(_mockDistributedCache.Object);
        _services.AddSingleton(_mockDataProtectionProvider.Object);
        
        DefaultHttpContext httpContext = new()
        {
            RequestServices = _services.BuildServiceProvider()
        };
        
        _mockHttpContextAccessor.Setup(h => h.HttpContext).Returns(httpContext);

    }
}