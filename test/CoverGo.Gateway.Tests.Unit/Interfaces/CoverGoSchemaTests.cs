using CoverGo.Gateway.Interfaces;
using CoverGo.Gateway.Interfaces.Types;
using GraphQL;
using Moq;
using Newtonsoft.Json.Linq;
using System;
using System.Globalization;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Interfaces
{
    public class CoverGoSchemaTests
    {
        private readonly Mock<IDependencyResolver> _mockResolver;
        private readonly CoverGoSchema _schema;

        public CoverGoSchemaTests()
        {
            // Setup
            _mockResolver = new Mock<IDependencyResolver>();
            _mockResolver.Setup(r => r.Resolve<CoverGoQuery>()).Returns((CoverGoQuery)null);
            _mockResolver.Setup(r => r.Resolve<CoverGoMutation>()).Returns((CoverGoMutation)null);
            _mockResolver.Setup(r => r.Resolve<CoverGoSubscriptions>()).Returns((CoverGoSubscriptions)null);

            // Create schema instance
            _schema = new CoverGoSchema(_mockResolver.Object);
        }

        [Fact]
        public void Constructor_ShouldSetQueryMutationAndSubscription()
        {
            // Verify resolver was called
            _mockResolver.Verify(r => r.Resolve<CoverGoQuery>(), Times.Once);
            _mockResolver.Verify(r => r.Resolve<CoverGoMutation>(), Times.Once);
            _mockResolver.Verify(r => r.Resolve<CoverGoSubscriptions>(), Times.Once);
        }

        [Fact]
        public void Constructor_ShouldRegisterValueConverters_WithoutErrors()
        {
            var schema = new CoverGoSchema(_mockResolver.Object);
            
            // Assert
            Assert.NotNull(schema);
        }

        [Fact]
        public void Constructor_ShouldRegisterByteValueConverter()
        {
            // Arrange
            var byteValue = (byte)42;
            var byteGraphType = new ByteGraphType();
            
            // Act - Create a new schema to ensure ByteValueConverter is registered
            var schema = new CoverGoSchema(_mockResolver.Object);
            
            // Assert
            Assert.NotNull(schema);
            
            // Verify ByteValueConverter functionality
            var byteValueConverter = new ByteValueConverter();
            Assert.True(byteValueConverter.Matches(byteValue, byteGraphType));
            var result = byteValueConverter.Convert(byteValue, byteGraphType);
            Assert.IsType<ByteValue>(result);
            Assert.Equal(byteValue, ((ByteValue)result).Value);
        }

        [Fact]
        public void Constructor_ShouldRegisterJsonValueConverter()
        {
            // Arrange
            var jsonObject = new JObject();
            jsonObject["test"] = "value";
            
            // Act - Create a new schema to ensure JsonValueConverter is registered
            var schema = new CoverGoSchema(_mockResolver.Object);
            
            // Assert
            Assert.NotNull(schema);
        }

        [Fact]
        public void Constructor_ShouldRegisterLongToDoubleConverter()
        {
            // Arrange
            long longValue = 42L;
            
            // Act - Create a new schema to ensure long to double converter is registered
            var schema = new CoverGoSchema(_mockResolver.Object);
            
            // Assert
            Assert.NotNull(schema);
            
            // Test conversion from long to double
            double convertedValue = Convert.ToDouble(longValue, NumberFormatInfo.InvariantInfo);
            Assert.Equal(42.0, convertedValue);
        }

        [Fact]
        public void Constructor_ShouldRegisterLongToDecimalConverter()
        {
            // Arrange
            long longValue = 42L;
            
            // Act - Create a new schema to ensure long to decimal converter is registered
            var schema = new CoverGoSchema(_mockResolver.Object);
            
            // Assert
            Assert.NotNull(schema);
            
            // Test conversion from long to decimal
            decimal convertedValue = Convert.ToDecimal(longValue, NumberFormatInfo.InvariantInfo);
            Assert.Equal(42.0m, convertedValue);
        }

        [Fact]
        public void Constructor_ShouldSetUpCompleteSchema()
        {
            Assert.NotNull(_schema);
        }
    }
}
