using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Interfaces;

public class FiLifeWebhookControllerTests
{
    private readonly Mock<ITransactionService> _transactionServiceMock;
    private readonly Mock<ILogger<FiLifeWebhookController>> _loggerMock;
    private readonly FiLifeWebhookController _controller;

    public FiLifeWebhookControllerTests()
    {
        _transactionServiceMock = new Mock<ITransactionService>();
        _loggerMock = new Mock<ILogger<FiLifeWebhookController>>();
        _controller = new FiLifeWebhookController(_transactionServiceMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task GetTransaction_ReturnsNotFound_WhenNoTransactionsFound()
    {
        // Arrange
        _transactionServiceMock
            .Setup(service => service.GetAsync(It.IsAny<string>(), It.IsAny<CoverGo.Gateway.Domain.QueryArguments>()))
            .ReturnsAsync(new List<Transaction>());

        // Act
        var result = await _controller.GetTransaction("tenant1", "reference1");

        // Assert
        Assert.IsType<ActionResult<string>>(result);
        Assert.Null(result.Value);
    }

    [Fact]
    public async Task GetTransaction_ReturnsOk_WhenTransactionStatusIsFailed()
    {
        // Arrange
        var transactions = new List<Transaction>
            {
                new Transaction { Status = TransactionStatus.Failed }
            };
        _transactionServiceMock
            .Setup(service => service.GetAsync(It.IsAny<string>(), It.IsAny<CoverGo.Gateway.Domain.QueryArguments>()))
            .ReturnsAsync(transactions);

        // Act
        var result = await _controller.GetTransaction("tenant1", "reference1");

        // Assert
        Assert.IsType<ActionResult<string>>(result);
        Assert.Null(result.Value);
    }

    [Fact]
    public async Task GetTransaction_ReturnsOk_WhenTransactionStatusIsSucceed()
    {
        // Arrange
        var transactions = new List<Transaction>
            {
                new Transaction { Status = TransactionStatus.Succeed }
            };
        _transactionServiceMock
            .Setup(service => service.GetAsync(It.IsAny<string>(), It.IsAny<CoverGo.Gateway.Domain.QueryArguments>()))
            .ReturnsAsync(transactions);

        // Act
        var result = await _controller.GetTransaction("tenant1", "reference1");

        // Assert
        Assert.IsType<ActionResult<string>>(result);
        Assert.Equal("RECEIVEOK", result.Value);
    }

    [Fact]
    public async Task GetTransaction_ReturnsBadRequest_WhenTransactionStatusIsNotFailedOrSucceed()
    {
        // Arrange
        var transactions = new List<Transaction>
            {
                new Transaction { Status = TransactionStatus.Pending }
            };
        _transactionServiceMock
            .Setup(service => service.GetAsync(It.IsAny<string>(), It.IsAny<CoverGo.Gateway.Domain.QueryArguments>()))
            .ReturnsAsync(transactions);

        // Act
        var result = await _controller.GetTransaction("tenant1", "reference1");

        // Assert
        Assert.IsType<ActionResult<string>>(result);
        Assert.Equal("RECEIVEOK", result.Value);
    }
}
