using System.Threading;
using System.Threading.Tasks;
using CoverGo.Gateway.Interfaces;
using FluentAssertions;
using HotChocolate.Execution;
using HotChocolate.Subscriptions;
using Moq;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Interfaces;

public class CoverGoSubscription_BackgroundJobsTests
{
    private readonly CoverGoSubscription_BackgroundJobs _subscription;

    public CoverGoSubscription_BackgroundJobsTests()
    {
        _subscription = new CoverGoSubscription_BackgroundJobs();
    }

    [Fact]
    public async Task OnJobStatusChangedResolver_ShouldSubscribe_ToJobStatusChangedTopic()
    {
        // Arrange
        var receiverMock = new Mock<ITopicEventReceiver>();
        var sourceStreamMock = new Mock<ISourceStream<JToken>>();
        var cancellationToken = CancellationToken.None;

        receiverMock
            .Setup(r => r.SubscribeAsync<string, JToken>("JobStatusChanged", cancellationToken))
            .ReturnsAsync(sourceStreamMock.Object);

        // Act
        var result = await _subscription.OnJobStatusChangedResolver(receiverMock.Object, cancellationToken);

        // Assert
        result.Should().Be(sourceStreamMock.Object);
        receiverMock.Verify(r => r.SubscribeAsync<string, JToken>("JobStatusChanged", cancellationToken), Times.Once);
    }

    [Fact]
    public void OnJobStatusChanged_NoJobIdProvided_ShouldReturnPayload()
    {
        // Arrange
        var jobId = "job123";
        var status = "Processing";
        var messageJson = $"{{ \"JobId\": \"{jobId}\", \"Status\": \"{status}\" }}";
        var message = JToken.Parse(messageJson);

        // Act
        var result = _subscription.OnJobStatusChanged(message);

        // Assert
        result.Should().NotBeNull();
        result.JobId.Should().Be(jobId);
        result.Status.Should().Be(status);
    }

    [Fact]
    public void OnJobStatusChanged_JobIdMatchesPayload_ShouldReturnPayload()
    {
        // Arrange
        var jobId = "job123";
        var status = "Succeeded";
        var messageJson = $"{{ \"JobId\": \"{jobId}\", \"Status\": \"{status}\" }}";
        var message = JToken.Parse(messageJson);

        // Act
        var result = _subscription.OnJobStatusChanged(message, jobId);

        // Assert
        result.Should().NotBeNull();
        result.JobId.Should().Be(jobId);
        result.Status.Should().Be(status);
    }

    [Fact]
    public void OnJobStatusChanged_JobIdDoesNotMatchPayload_ShouldReturnNull()
    {
        // Arrange
        var jobId = "job123";
        var status = "Failed";
        var messageJson = $"{{ \"JobId\": \"{jobId}\", \"Status\": \"{status}\" }}";
        var message = JToken.Parse(messageJson);
        var requestedJobId = "differentJobId";

        // Act
        var result = _subscription.OnJobStatusChanged(message, requestedJobId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void OnJobStatusChanged_EmptyJobId_ShouldReturnPayload()
    {
        // Arrange
        var jobId = "job123";
        var status = "Processing";
        var messageJson = $"{{ \"JobId\": \"{jobId}\", \"Status\": \"{status}\" }}";
        var message = JToken.Parse(messageJson);
        var emptyJobId = "";

        // Act
        var result = _subscription.OnJobStatusChanged(message, emptyJobId);

        // Assert
        result.Should().NotBeNull();
        result.JobId.Should().Be(jobId);
        result.Status.Should().Be(status);
    }

    [Fact]
    public void OnJobStatusChanged_NullMessage_ShouldReturnNull()
    {
        // Arrange
        JToken message = JToken.Parse("null");
        
        // Act
        var result = _subscription.OnJobStatusChanged(message);

        // Assert
        result.Should().BeNull();
    }
}
