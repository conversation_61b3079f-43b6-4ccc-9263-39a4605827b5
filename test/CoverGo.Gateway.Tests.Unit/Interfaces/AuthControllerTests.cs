using System;
using System.Net;
using System.Net.Http;
using System.Net.Http.Json;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Cache;
using CoverGo.Gateway.Infrastructure;
using CoverGo.Gateway.Infrastructure.Auth;
using CoverGo.Gateway.Interfaces;
using FluentAssertions;
using IdentityModel.Client;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Routing;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Interfaces;

public class AuthControllerTests
{
    private readonly Mock<IAuthService> _authServiceMock;
    private readonly Mock<ICacheManager> _cacheManagerMock;
    private readonly Mock<ITokenService> _tokenServiceMock;
    private readonly AuthController _controller;

    public AuthControllerTests()
    {
        _authServiceMock = new Mock<IAuthService>();
        _cacheManagerMock = new Mock<ICacheManager>();
        _tokenServiceMock = new Mock<ITokenService>();
        _controller = new AuthController(_authServiceMock.Object, _cacheManagerMock.Object, _tokenServiceMock.Object);
    }

    [Fact]
    public async Task GIVEN_valid_inputs_WHEN_Login_invoked_THEN_returns_challenge_result()
    {
        const string callbackUrl = "auth/covergo/callback?clientId=admin_portal&returnUrl=https%3a%2f%2fexample.com";
        Mock<IUrlHelper> urlHelperMock = new(MockBehavior.Strict);

        urlHelperMock
            .Setup(urlHelper => urlHelper.Action(It.IsAny<UrlActionContext>()))
            .Returns(callbackUrl);

        _cacheManagerMock
            .Setup(cache => cache.GetOrSet("SSOConfig_covergo_admin_portal", It.IsAny<Func<Task<SSOConfig>>>(),
                Constants.DefaultCacheDuration))
            .ReturnsAsync(new SSOConfig { ClientId = "admin_portal", ProviderId = "KeyCloak" });

        _controller.Url = urlHelperMock.Object;
        _controller.ControllerContext = new ControllerContext { HttpContext = new DefaultHttpContext() };

        ChallengeResult result = await _controller.Login("covergo", "admin_portal", "https://example.com") as ChallengeResult;

        result.Should().NotBeNull();
        result!.Properties!.RedirectUri.Should().Be(callbackUrl);
        result.Properties.Items.Should().HaveCountGreaterThan(2);
        result.Properties.Items.Should().ContainKey("tenantId").WhoseValue.Should().Be("covergo");
        result.Properties.Items.Should().ContainKey("clientId").WhoseValue.Should().Be("admin_portal");
        result.AuthenticationSchemes.Should().Contain(KeyCloakDefaults.AuthenticationScheme);

        ValidateHttpContextItems(_controller.ControllerContext.HttpContext);
    }

    [Fact]
    public async Task GIVEN_valid_inputs_AND_tenant_settings_does_not_exist_WHEN_Login_invoked_THEN_returns_bad_request_result()
    {
        _authServiceMock.Setup(svc => svc.GetTenantSettingsAsync(It.IsAny<string>())).ReturnsAsync((TenantSettings)null);
        _controller.ControllerContext = new ControllerContext { HttpContext = new DefaultHttpContext() };

        IActionResult result = await _controller.Login("covergo", "admin_portal", "https://example.com");

        result.Should().NotBeNull().And.BeOfType<BadRequestObjectResult>();

        BadRequestObjectResult badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().Be("This Tenant or Client is Not Supported");
    }

    [Theory]
    [InlineData("", "admin_portal", "https://example.com", "Invalid tenantId")]
    [InlineData("covergo", "", "https://example.com", "Invalid clientId")]
    [InlineData("covergo", "admin_portal", "", "Invalid returnUrl")]
    public async Task GIVEN_invalid_inputs_WHEN_Login_invoked_THEN_returns_bad_request_result(string tenantId, string clientId, string returnUrl, string errorMessage)
    {
        _cacheManagerMock
            .Setup(cache => cache.GetOrSet("SSOConfig_covergo_admin_portal", It.IsAny<Func<Task<SSOConfig>>>(),
                Constants.DefaultCacheDuration))
            .ReturnsAsync(new SSOConfig { ClientId = "admin_portal", ProviderId = "KeyCloak" });

        _controller.ControllerContext = new ControllerContext { HttpContext = new DefaultHttpContext() };

        IActionResult result = await _controller.Login(tenantId, clientId, returnUrl);

        result.Should().NotBeNull().And.BeOfType<BadRequestObjectResult>();

        BadRequestObjectResult badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().Be(errorMessage);
    }

    [Fact]
    public async Task GIVEN_returnUrl_invalid_format_WHEN_Login_invoked_THEN_returns_bad_request_result()
    {
        _cacheManagerMock
            .Setup(cache => cache.GetOrSet("SSOConfig_covergo_admin_portal", It.IsAny<Func<Task<SSOConfig>>>(),
                Constants.DefaultCacheDuration))
            .ReturnsAsync(new SSOConfig { ClientId = "admin_portal", ProviderId = "KeyCloak" });

        _controller.ControllerContext = new ControllerContext { HttpContext = new DefaultHttpContext() };

        IActionResult result = await _controller.Login("covergo", "admin_portal", "invalid_url");

        result.Should().NotBeNull().And.BeOfType<BadRequestObjectResult>();

        BadRequestObjectResult badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().Be("Invalid returnUrl");
    }

    [Fact]
    public async Task GIVEN_valid_access_token_AND_refresh_token_AND_expires_at_WHEN_Callback_invoked_THEN_returns_redirect_result()
    {
        _controller.ControllerContext = new ControllerContext { HttpContext = new DefaultHttpContext() };

        AuthenticateResult authenticateResult =
            AuthenticateResult.Success(new AuthenticationTicket(new ClaimsPrincipal(),
                KeyCloakDefaults.AuthenticationScheme));
        authenticateResult.Properties!.StoreTokens(new[]
        {
            new AuthenticationToken { Name = AuthTokenNames.AccessToken, Value = "dummy_access_token" },
            new AuthenticationToken { Name = AuthTokenNames.IdentityToken, Value = "dummy_id_token" },
            new AuthenticationToken { Name = AuthTokenNames.RefreshToken, Value = "dummy_refresh_token" },
            new AuthenticationToken { Name = AuthTokenNames.ExpiresAt, Value = "dummy_expires_at" }
        });
        Mock<IAuthenticationService> mockTokenService = new();
        mockTokenService.Setup(x => x.AuthenticateAsync(_controller.HttpContext, KeyCloakDefaults.AuthenticationScheme))
            .ReturnsAsync(authenticateResult);

        _controller.HttpContext.RequestServices = new ServiceCollection().AddSingleton(mockTokenService.Object).BuildServiceProvider();

        IActionResult result = await _controller.Callback("https://example.com");

        result.Should().NotBeNull().And.BeOfType<RedirectResult>();
        RedirectResult redirectResult = (RedirectResult)result;
        redirectResult.Url.Should().Be("https://example.com/#access_token=dummy_access_token&id_token=dummy_id_token&refresh_token=dummy_refresh_token&expires_at=dummy_expires_at");
    }

    [Fact]
    public async Task GIVEN_missing_access_token_WHEN_Callback_invoked_THEN_returns_unauthorized_result()
    {
        _controller.ControllerContext = new ControllerContext { HttpContext = new DefaultHttpContext() };

        AuthenticateResult authenticateResult =
            AuthenticateResult.Success(new AuthenticationTicket(new ClaimsPrincipal(),
                KeyCloakDefaults.AuthenticationScheme));
        authenticateResult.Properties!.StoreTokens(new[]
        {
            new AuthenticationToken { Name = AuthTokenNames.IdentityToken, Value = "dummy_id_token" },
            new AuthenticationToken { Name = AuthTokenNames.RefreshToken, Value = "dummy_refresh_token" },
            new AuthenticationToken { Name = AuthTokenNames.ExpiresAt, Value = "dummy_expires_at" }
        });
        Mock<IAuthenticationService> mockTokenService = new();
        mockTokenService.Setup(x => x.AuthenticateAsync(_controller.HttpContext, KeyCloakDefaults.AuthenticationScheme))
            .ReturnsAsync(authenticateResult);

        _controller.HttpContext.RequestServices = new ServiceCollection().AddSingleton(mockTokenService.Object).BuildServiceProvider();

        IActionResult result = await _controller.Callback("https://example.com");

        result.Should().NotBeNull().And.BeOfType<UnauthorizedObjectResult>();
        UnauthorizedObjectResult unauthorizedResult = (UnauthorizedObjectResult)result;
        unauthorizedResult.Value.Should().Be("Tokens or expiration details are missing");
    }

    [Fact]
    public async Task GIVEN_missing_expires_at_WHEN_Callback_invoked_THEN_returns_unauthorized_result()
    {
        _controller.ControllerContext = new ControllerContext { HttpContext = new DefaultHttpContext() };

        AuthenticateResult authenticateResult = AuthenticateResult.Success(new AuthenticationTicket(new ClaimsPrincipal(), KeyCloakDefaults.AuthenticationScheme));
        authenticateResult.Properties!.StoreTokens(new[]
        {
            new AuthenticationToken { Name = AuthTokenNames.AccessToken, Value = "dummy_access_token" },
            new AuthenticationToken { Name = AuthTokenNames.IdentityToken, Value = "dummy_id_token" },
            new AuthenticationToken { Name = AuthTokenNames.RefreshToken, Value = "dummy_refresh_token" }
        });
        Mock<IAuthenticationService> mockTokenService = new();
        mockTokenService.Setup(x => x.AuthenticateAsync(_controller.HttpContext, KeyCloakDefaults.AuthenticationScheme))
            .ReturnsAsync(authenticateResult);

        _controller.HttpContext.RequestServices =
            new ServiceCollection().AddSingleton(mockTokenService.Object).BuildServiceProvider();

        IActionResult result = await _controller.Callback("https://example.com");

        result.Should().NotBeNull().And.BeOfType<UnauthorizedObjectResult>();
        UnauthorizedObjectResult unauthorizedResult = (UnauthorizedObjectResult)result;
        unauthorizedResult.Value.Should().Be("Tokens or expiration details are missing");
    }

    [Fact]
    public async Task GIVEN_missing_refresh_token_WHEN_Callback_invoked_THEN_returns_unauthorized_result()
    {
        _controller.ControllerContext = new ControllerContext { HttpContext = new DefaultHttpContext() };

        AuthenticateResult authenticateResult =
            AuthenticateResult.Success(new AuthenticationTicket(new ClaimsPrincipal(),
                KeyCloakDefaults.AuthenticationScheme));
        authenticateResult.Properties!.StoreTokens(new[]
        {
            new AuthenticationToken { Name = AuthTokenNames.AccessToken, Value = "dummy_access_token" },
            new AuthenticationToken { Name = AuthTokenNames.IdentityToken, Value = "dummy_id_token" },
            new AuthenticationToken { Name = AuthTokenNames.ExpiresAt, Value = "dummy_expires_at" }
        });
        Mock<IAuthenticationService> mockTokenService = new();
        mockTokenService.Setup(x => x.AuthenticateAsync(_controller.HttpContext, KeyCloakDefaults.AuthenticationScheme))
            .ReturnsAsync(authenticateResult);

        _controller.HttpContext.RequestServices =
            new ServiceCollection().AddSingleton(mockTokenService.Object).BuildServiceProvider();

        IActionResult result = await _controller.Callback("https://example.com");

        result.Should().NotBeNull().And.BeOfType<UnauthorizedObjectResult>();
        UnauthorizedObjectResult unauthorizedResult = (UnauthorizedObjectResult)result;
        unauthorizedResult.Value.Should().Be("Tokens or expiration details are missing");
    }

    [Fact]
    public async Task GIVEN_missing_id_token_WHEN_Callback_invoked_THEN_returns_unauthorized_result()
    {
        _controller.ControllerContext = new ControllerContext { HttpContext = new DefaultHttpContext() };

        AuthenticateResult authenticateResult =
            AuthenticateResult.Success(new AuthenticationTicket(new ClaimsPrincipal(),
                KeyCloakDefaults.AuthenticationScheme));
        authenticateResult.Properties!.StoreTokens(new[]
        {
            new AuthenticationToken { Name = AuthTokenNames.AccessToken, Value = "dummy_access_token" },
            new AuthenticationToken { Name = AuthTokenNames.RefreshToken, Value = "dummy_refresh_token" },
            new AuthenticationToken { Name = AuthTokenNames.ExpiresAt, Value = "dummy_expires_at" }
        });
        Mock<IAuthenticationService> mockTokenService = new();
        mockTokenService.Setup(x => x.AuthenticateAsync(_controller.HttpContext, KeyCloakDefaults.AuthenticationScheme))
            .ReturnsAsync(authenticateResult);

        _controller.HttpContext.RequestServices =
            new ServiceCollection().AddSingleton(mockTokenService.Object).BuildServiceProvider();

        IActionResult result = await _controller.Callback("https://example.com");

        result.Should().NotBeNull().And.BeOfType<UnauthorizedObjectResult>();
        UnauthorizedObjectResult unauthorizedResult = (UnauthorizedObjectResult)result;
        unauthorizedResult.Value.Should().Be("Tokens or expiration details are missing");
    }

    [Fact]
    public async Task GIVEN_valid_access_token_AND_refresh_token_AND_expires_at_WHEN_Callback_invoked_WITH_null_return_url_THEN_returns_BadRequestObjectResult()
    {
        _controller.ControllerContext = new ControllerContext { HttpContext = new DefaultHttpContext() };
        Mock<IAuthenticationService> authenticationService = new();
        AuthenticateResult authenticateResult = AuthenticateResult.Success(new AuthenticationTicket(new ClaimsPrincipal(), KeyCloakDefaults.AuthenticationScheme));
        authenticateResult.Properties!.StoreTokens(new[]
        {
            new AuthenticationToken { Name = AuthTokenNames.AccessToken, Value = "dummy_access_token" },
            new AuthenticationToken { Name = AuthTokenNames.IdentityToken, Value = "dummy_id_token" },
            new AuthenticationToken { Name = AuthTokenNames.RefreshToken, Value = "dummy_refresh_token" },
            new AuthenticationToken { Name = AuthTokenNames.ExpiresAt, Value = "dummy_expires_at" }
        });
        authenticationService
            .Setup(x => x.AuthenticateAsync(_controller.HttpContext, KeyCloakDefaults.AuthenticationScheme))
            .ReturnsAsync(authenticateResult);
        _controller.HttpContext.RequestServices = new ServiceCollection().AddSingleton(authenticationService.Object)
            .BuildServiceProvider();

        IActionResult result = await _controller.Callback(returnUrl: null);

        result.Should().NotBeNull().And.BeOfType<BadRequestObjectResult>();
        BadRequestObjectResult badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().Be("Invalid returnUrl");
    }

    [Theory]
    [InlineData("", "admin_portal", "valid-refresh-token", "Invalid tenantId")]
    [InlineData(null, "admin_portal", "valid-refresh-token", "Invalid tenantId")]
    [InlineData("covergo", "", "valid-refresh-token", "Invalid clientId")]
    [InlineData("covergo", null, "valid-refresh-token", "Invalid clientId")]
    [InlineData("covergo", "admin_portal", "", "Invalid refreshToken")]
    [InlineData("covergo", "admin_portal", null, "Invalid refreshToken")]
    public async Task GIVEN_invalid_inputs_WHEN_RefreshToken_invoked_THEN_returns_bad_request_result(string tenantId, string clientId, string refreshToken, string errorMessage)
    {
        IActionResult result = await _controller.RefreshToken(tenantId, clientId, refreshToken, CancellationToken.None);

        result.Should().BeOfType<BadRequestObjectResult>();
        result.As<BadRequestObjectResult>().Value.Should().Be(errorMessage);
    }

    [Theory]
    [InlineData("covergo", "member_portal")]
    [InlineData("covergo1", "admin_portal")]
    public async Task GIVEN_valid_inputs_AND_sso_config_not_found_WHEN_RefreshToken_invoked_THEN_returns_bad_request_result(string tenantId, string clientId)
    {
        _cacheManagerMock
            .Setup(cache => cache.GetOrSet("SSOConfig_covergo_admin_portal", It.IsAny<Func<Task<SSOConfig>>>(),
                Constants.DefaultCacheDuration))
            .ReturnsAsync(new SSOConfig { ClientId = "admin_portal", ProviderId = "KeyCloak" });

        IActionResult result = await _controller.RefreshToken(tenantId, clientId, "valid-refresh-token", CancellationToken.None);

        result.Should().BeOfType<BadRequestObjectResult>();
        result.As<BadRequestObjectResult>().Value.Should().Be("This Tenant or Client is Not Supported");
    }

    [Fact]
    public async Task GIVEN_valid_inputs_AND_refresh_token_failed_WHEN_RefreshToken_invoked_THEN_returns_bad_request_result()
    {
        const string validTenantId = "covergo";
        const string validClientId = "admin_portal";
        const string validRefreshToken = "refresh_token";

        _cacheManagerMock
            .Setup(cache => cache.GetOrSet($"SSOConfig_{validTenantId}_{validClientId}", It.IsAny<Func<Task<SSOConfig>>>(),
                Constants.DefaultCacheDuration))
            .ReturnsAsync(new SSOConfig { ClientId = validClientId, ProviderId = "KeyCloak" });

        TokenResponse tokenResponse = await ProtocolResponse.FromHttpResponseAsync<TokenResponse>(new HttpResponseMessage(HttpStatusCode.BadRequest)
        {
            Content = new StringContent("Error message")
        });

        _tokenServiceMock
            .Setup(svc => svc.RefreshToken(It.IsAny<SSOConfig>(), validRefreshToken, It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result<TokenResponse>.Success(tokenResponse));

        IActionResult result = await _controller.RefreshToken(validTenantId, validClientId, validRefreshToken, CancellationToken.None);

        result.Should().BeOfType<BadRequestObjectResult>();
        result.As<BadRequestObjectResult>().Value.Should().Be("Error while refreshing access token");
    }

    [Fact]
    public async Task GIVEN_valid_inputs_WHEN_RefreshToken_invoked_THEN_returns_ok_result_with_access_token()
    {
        const string validTenantId = "covergo";
        const string validClientId = "admin_portal";
        const string validRefreshToken = "refresh_token";

        _cacheManagerMock
            .Setup(cache => cache.GetOrSet($"SSOConfig_{validTenantId}_{validClientId}", It.IsAny<Func<Task<SSOConfig>>>(),
                Constants.DefaultCacheDuration))
            .ReturnsAsync(new SSOConfig { ClientId = validClientId, ProviderId = "KeyCloak" });


        TokenResponse tokenResponse = await ProtocolResponse.FromHttpResponseAsync<TokenResponse>(new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = JsonContent.Create(new { access_token = "token", expires_at = 5000 })
        });

        _tokenServiceMock
            .Setup(svc => svc.RefreshToken(It.IsAny<SSOConfig>(), validRefreshToken, It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result<TokenResponse>.Success(tokenResponse));

        IActionResult result = await _controller.RefreshToken(validTenantId, validClientId, validRefreshToken, CancellationToken.None);

        result.Should().BeOfType<OkObjectResult>();
        result.As<OkObjectResult>().Value.Should().BeOfType<TokenResponseResult>();
    }

    private static void ValidateHttpContextItems(HttpContext httpContext)
    {
        httpContext.Items.Should().HaveCount(2);
        httpContext.Items.Should().ContainKey("tenantId");
        httpContext.Items.Should().ContainKey("clientId");
        httpContext.Items["tenantId"].Should().Be("covergo");
        httpContext.Items["clientId"].Should().Be("admin_portal");
    }
}