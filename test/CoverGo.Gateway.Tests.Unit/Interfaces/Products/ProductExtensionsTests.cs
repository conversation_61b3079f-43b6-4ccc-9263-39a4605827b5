using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Tests.Unit.AutoFixture;
using FluentAssertions;
using Xunit;
using CoverGo.Gateway.Interfaces.Products;

namespace CoverGo.Gateway.Tests.Unit.Interfaces.Products;

[Trait("Ticket", "CH-17428")]
public class ProductExtensionsTests
{
    [Theory, MoqAutoData]
    public void ToGraphTest(Product2 dto)
    {
        // Act (When)
        var graph = dto.ToGraph();

        // Assert (Then)
        graph.Should().NotBeNull();
    }
}