using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Tests.Unit.AutoFixture;
using FluentAssertions;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Interfaces.Products;

[Trait("Ticket", "CH-17286")]
public class ProductGraphTypeTests
{
    [Fact]
    public void UpdateProductInputGraphTypeTest()
    {
        // Act (When)
        var type = new UpdateProductInputGraphType();

        // Assert (Then)
        type.Should().NotBeNull();
    }

    [Fact]
    public void ProductUpdatesInputGraphTypeTest()
    {
        // Act (When)
        var type = new ProductUpdatesInputGraphType();

        // Assert (Then)
        type.Should().NotBeNull();
    }

    [Fact]
    public void ProductUpdatesGraphTypeTest()
    {
        // Act (When)
        var type = new ProductUpdatesGraphType();

        // Assert (Then)
        type.Should().NotBeNull();
    }

    [Fact]
    public void UpdateProductInputGraphTest()
    {
        // Arrange (Given)
        var sut = new UpdateProductInputGraph();

        // Act (When)
        sut.UpdateTypes = new CoverGo.Gateway.Domain.Products.ProductUpdateTypes{
            Plans = true,
            BenefitsOrLimits = true,
            Pricing = true,
            OtherMinorChanges = true
        };

        // Assert (Then)
        sut.UpdateTypes.Plans.Should().Be(true);
        sut.UpdateTypes.BenefitsOrLimits.Should().Be(true);
        sut.UpdateTypes.Pricing.Should().Be(true);
        sut.UpdateTypes.OtherMinorChanges.Should().Be(true);
    }

    [Theory, MoqAutoData]
    public void ProductGraphTypeTest(ProductGraphType sut)
    {
        // Assert (Then)
        sut.Should().NotBeNull();
    }
}