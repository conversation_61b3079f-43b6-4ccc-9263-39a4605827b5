using CoverGo.Gateway.Domain.Products;
using FluentAssertions;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Interfaces.Products;

[Trait("Ticket", "CH-17286")]
public class ProductTests
{
    [Fact]
    public void UpdateProductCommandTest()
    {
        // Arrange (Given)
        var sut = new UpdateProductCommand();

        // Act (When)
        sut.UpdateTypes = new ProductUpdateTypes{Plans = true};

        // Assert (Then)
        sut.UpdateTypes.Plans.Should().Be(true);
    }
}