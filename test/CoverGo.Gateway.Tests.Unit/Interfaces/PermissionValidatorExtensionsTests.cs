using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Claims;
using CoverGo.Gateway.Interfaces;
using GraphQL.Types;
using GraphQL.Validation;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json.Linq;
using Xunit;

using DomainClaim = CoverGo.Gateway.Domain.Claims.Claim;

namespace CoverGo.Gateway.Tests.Unit.Interfaces
{
    // Create a testable version of PermissionValidator that allows us to override the GetPermittedTargetIds method
    public class TestablePermissionValidator : PermissionValidator
    {
        private readonly Dictionary<string, IEnumerable<string>> _targetIdsMap = new();

        public TestablePermissionValidator(LazyPermissionValidator lazyPermissionValidator, ILogger<PermissionValidator> logger)
            : base(lazyPermissionValidator, logger)
        {
        }

        public void SetupGetPermittedTargetIds<T>(PermissionRequest permissionRequest, IEnumerable<string> returnValue)
        {
            string key = $"{string.Join(",", permissionRequest.AllowedClaimTypes)}";
            _targetIdsMap[key] = returnValue;
        }

        public override Task<IEnumerable<string>> GetPermittedTargetIds<T>(ResolveFieldContext<T> context, PermissionRequest permissionRequest)
        {
            string key = $"{string.Join(",", permissionRequest.AllowedClaimTypes)}";
            if (_targetIdsMap.TryGetValue(key, out var result))
            {
                return Task.FromResult(result);
            }

            // Default fallback for any request not explicitly set up
            return Task.FromResult(Enumerable.Empty<string>());
        }
    }

    public class PermissionValidatorExtensionsTests
    {
        private readonly TestablePermissionValidator _validator;
        private readonly Mock<ITenantSpecificClaimService> _mockClaimService;
        private readonly ResolveFieldContext<object> _context;

        public PermissionValidatorExtensionsTests()
        {
            // Setup HttpContextAccessor with mock HttpContext
            Mock<IHttpContextAccessor> mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            Mock<HttpContext> mockHttpContext = new Mock<HttpContext>();
            Dictionary<object, object?> mockItems = new();
            mockHttpContext.Setup(c => c.Items).Returns(mockItems);
            mockHttpContextAccessor.Setup(a => a.HttpContext).Returns(mockHttpContext.Object);

            // Create a real PermissionCache with the mock HttpContextAccessor
            PermissionCache permissionCache = new PermissionCache(mockHttpContextAccessor.Object);

            // Create LazyPermissionValidator with the real PermissionCache
            Mock<IAuthService> mockAuthService = new Mock<IAuthService>();
            LazyPermissionValidator lazyPermissionValidator = new LazyPermissionValidator(
                mockAuthService.Object,
                permissionCache);

            // Create the testable PermissionValidator
            _validator = new TestablePermissionValidator(
                lazyPermissionValidator,
                new Mock<ILogger<PermissionValidator>>().Object);

            _mockClaimService = new Mock<ITenantSpecificClaimService>();

            _context = new ResolveFieldContext<object>
            {
                UserContext = new GraphQLUserContext
                {
                    User = new ClaimsPrincipal(new ClaimsIdentity(new[]
                    {
                        new System.Security.Claims.Claim("tenantId", "test_tenant")
                    }))
                }
            };
        }

        [Fact]
        public async Task AuthorizeClaimAmountAsync_WhenClaimTypeIsNull_DoesNotThrow()
        {
            // Arrange
            DomainClaim claim = new DomainClaim
            {
                Fields = new JObject()
            };

            // Act & Assert
            await _validator.AuthorizeClaimAmountAsync(_context, claim, _mockClaimService.Object);
            // No exception means test passes
        }

        [Fact]
        public async Task AuthorizeClaimAmountAsync_WhenClaimTypeIsInPatient_GetsCorrectLimits()
        {
            // Arrange
            DomainClaim claim = new DomainClaim
            {
                Fields = new JObject
                {
                    ["claimType"] = "in_patient"
                }
            };

            string[] limits = ["1000", "2000", "3000"];
            PermissionRequest permissionRequest = new PermissionRequest("ipClaimApprovalLimit");
            _validator.SetupGetPermittedTargetIds<object>(permissionRequest, limits);

            _mockClaimService.Setup(s => s.GetClaimApprovalAmount(claim))
                .Returns(2500m);

            // Act & Assert
            await _validator.AuthorizeClaimAmountAsync(_context, claim, _mockClaimService.Object);
        }

        [Fact]
        public async Task AuthorizeClaimAmountAsync_WhenClaimTypeIsOutPatient_GetsCorrectLimits()
        {
            // Arrange
            DomainClaim claim = new DomainClaim
            {
                Fields = new JObject
                {
                    ["claimType"] = "out_patient"
                }
            };

            string[] limits = ["1000", "2000", "3000"];
            PermissionRequest permissionRequest = new PermissionRequest("opClaimApprovalLimit");
            _validator.SetupGetPermittedTargetIds<object>(permissionRequest, limits);

            _mockClaimService.Setup(s => s.GetClaimApprovalAmount(claim))
                .Returns(2500m);

            // Act & Assert
            await _validator.AuthorizeClaimAmountAsync(_context, claim, _mockClaimService.Object);
        }

        [Fact]
        public async Task AuthorizeClaimAmountAsync_WhenClaimAmountExceedsLimit_ThrowsValidationError()
        {
            // Arrange
            DomainClaim claim = new DomainClaim
            {
                Fields = new JObject
                {
                    ["claimType"] = "in_patient"
                }
            };

            string[] limits = ["1000", "2000", "3000"];
            decimal maxLimit = limits.Select(x => decimal.Parse(x)).Max();
            PermissionRequest permissionRequest = new PermissionRequest("ipClaimApprovalLimit");
            _validator.SetupGetPermittedTargetIds<object>(permissionRequest, limits);

            _mockClaimService.Setup(s => s.GetClaimApprovalAmount(claim))
                .Returns(3500m);

            // Act & Assert
            ValidationError exception = await Assert.ThrowsAsync<ValidationError>(() =>
                _validator.AuthorizeClaimAmountAsync(_context, claim, _mockClaimService.Object));

            Assert.Equal("claim_approval_limit", exception.Code);
            Assert.Contains($"You are not authorized to approve in_patient claims larger than {maxLimit:N2}", exception.Message);
        }

        [Fact]
        public async Task AuthorizeClaimAmountAsync_WhenClaimAmountWithinLimit_DoesNotThrow()
        {
            // Arrange
            DomainClaim claim = new DomainClaim
            {
                Fields = new JObject
                {
                    ["claimType"] = "in_patient"
                }
            };

            string[] limits = ["1000", "2000", "3000"];
            PermissionRequest permissionRequest = new PermissionRequest("ipClaimApprovalLimit");
            _validator.SetupGetPermittedTargetIds<object>(permissionRequest, limits);

            _mockClaimService.Setup(s => s.GetClaimApprovalAmount(claim))
                .Returns(2500m);

            // Act & Assert
            await _validator.AuthorizeClaimAmountAsync(_context, claim, _mockClaimService.Object);
            // No exception means test passes
        }

        [Fact]
        public async Task AuthorizeClaimAmountAsync_WhenNoLimitsFound_DoesNotThrow()
        {
            // Arrange
            DomainClaim claim = new DomainClaim
            {
                Fields = new JObject
                {
                    ["claimType"] = "in_patient"
                }
            };

            IEnumerable<string> limits = [];
            PermissionRequest permissionRequest = new PermissionRequest("ipClaimApprovalLimit");
            _validator.SetupGetPermittedTargetIds<object>(permissionRequest, limits);

            _mockClaimService.Setup(s => s.GetClaimApprovalAmount(claim))
                .Returns(5000m);

            // Act & Assert
            await _validator.AuthorizeClaimAmountAsync(_context, claim, _mockClaimService.Object);
            // No exception means test passes
        }

        [Fact]
        public async Task AuthorizeClaimAmountAsync_WhenLimitsContainNonNumericValues_IgnoresThem()
        {
            // Arrange
            DomainClaim claim = new DomainClaim
            {
                Fields = new JObject
                {
                    ["claimType"] = "in_patient"
                }
            };

            string[] limits = ["1000", "invalid", "3000"];
            PermissionRequest permissionRequest = new PermissionRequest("ipClaimApprovalLimit");
            _validator.SetupGetPermittedTargetIds<object>(permissionRequest, limits);

            _mockClaimService.Setup(s => s.GetClaimApprovalAmount(claim))
                .Returns(2500m);

            // Act & Assert
            await _validator.AuthorizeClaimAmountAsync(_context, claim, _mockClaimService.Object);
            // No exception means test passes
        }

        [Fact]
        public async Task AuthorizeClaimAmountAsync_WhenLimitsContainUnderscores_RemovesThem()
        {
            // Arrange
            DomainClaim claim = new DomainClaim
            {
                Fields = new JObject
                {
                    ["claimType"] = "in_patient"
                }
            };

            string[] limits = ["1_000", "2_000", "3_000"];
            PermissionRequest permissionRequest = new PermissionRequest("ipClaimApprovalLimit");
            _validator.SetupGetPermittedTargetIds<object>(permissionRequest, limits);

            _mockClaimService.Setup(s => s.GetClaimApprovalAmount(claim))
                .Returns(2500m);

            // Act & Assert
            await _validator.AuthorizeClaimAmountAsync(_context, claim, _mockClaimService.Object);
            // No exception means test passes
        }
    }
}
