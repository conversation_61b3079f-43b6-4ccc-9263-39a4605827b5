using System;
using System.IO;
using System.IO.Compression;
using System.Text;
using System.Threading.Tasks;
using CoverGo.Gateway.Application;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Application
{
    public class RequestDecompressionTests
    {
        private readonly Mock<RequestDelegate> _nextMock;
        private readonly RequestDecompression _middleware;
        private readonly Mock<ILogger<RequestDecompression>> _loggerMock;
        private readonly Mock<IServiceProvider> _serviceProviderMock;

        public RequestDecompressionTests()
        {
            _nextMock = new Mock<RequestDelegate>();
            _middleware = new RequestDecompression(_nextMock.Object);
            _loggerMock = new Mock<ILogger<RequestDecompression>>();
            _serviceProviderMock = new Mock<IServiceProvider>();
            
            // Setup service provider to return logger when requested
            _serviceProviderMock
                .Setup(sp => sp.GetService(typeof(ILogger<RequestDecompression>)))
                .Returns(_loggerMock.Object);
        }

        [Fact]
        public async Task Invoke_WithNoContentEncoding_ShouldCallNextWithOriginalBody()
        {
            // Arrange
            var context = new DefaultHttpContext();
            var originalBody = new MemoryStream(Encoding.UTF8.GetBytes("original content"));
            context.Request.Body = originalBody;

            // Act
            await _middleware.Invoke(context);

            // Assert
            Assert.Same(originalBody, context.Request.Body);
            _nextMock.Verify(next => next(context), Times.Once);
        }

        [Theory]
        [InlineData("gzip")]
        [InlineData("br")]
        [InlineData("deflate")]
        public async Task Invoke_WithValidEncoding_ShouldDecompressBody(string encoding)
        {
            // Arrange
            var context = new DefaultHttpContext
            {
                RequestServices = _serviceProviderMock.Object
            };
            
            string originalContent = "Test content for compression";
            var compressedContent = CompressString(originalContent, encoding);
            context.Request.Body = new MemoryStream(compressedContent);
            context.Request.Headers["Content-Encoding"] = encoding;
            context.Request.Headers["Content-Length"] = compressedContent.Length.ToString();

            // Act
            await _middleware.Invoke(context);

            // Assert
            using var memoryStream = new MemoryStream();
            context.Request.Body.CopyTo(memoryStream);
            Assert.NotSame(compressedContent, memoryStream.ToArray());
            Assert.False(context.Request.Headers.ContainsKey("Content-Encoding"));
            
            // Reset stream position to read content
            context.Request.Body.Position = 0;
            using var reader = new StreamReader(context.Request.Body);
            var decompressedContent = await reader.ReadToEndAsync();
            Assert.Equal(originalContent, decompressedContent);
            
            // Verify Content-Length was updated
            Assert.Equal(originalContent.Length.ToString(), context.Request.Headers["Content-Length"]);
            
            _nextMock.Verify(next => next(context), Times.Once);
        }

        [Fact]
        public async Task Invoke_WithUnsupportedEncoding_ShouldCallNextWithOriginalBody()
        {
            // Arrange
            var context = new DefaultHttpContext();
            var originalBody = new MemoryStream(Encoding.UTF8.GetBytes("original content"));
            context.Request.Body = originalBody;
            context.Request.Headers["Content-Encoding"] = "unsupported";

            // Act
            await _middleware.Invoke(context);

            // Assert
            Assert.Same(originalBody, context.Request.Body);
            _nextMock.Verify(next => next(context), Times.Once);
        }

        [Theory]
        [InlineData("gzip")]
        [InlineData("br")]
        [InlineData("deflate")]
        public async Task Invoke_WithExceptionDuringDecompression_ShouldLogErrorAndUseOriginalBody(string encoding)
        {
            // Arrange
            var context = new DefaultHttpContext
            {
                RequestServices = _serviceProviderMock.Object
            };
            
            // Create an invalid compressed content that will cause an exception during decompression
            byte[] invalidCompressedContent = Encoding.UTF8.GetBytes("This is not a valid compressed content");
            var originalRequestBody = new MemoryStream(invalidCompressedContent);
            context.Request.Body = originalRequestBody;
            context.Request.Headers["Content-Encoding"] = encoding;

            // Act
            await _middleware.Invoke(context);

            // Assert
            Assert.Same(originalRequestBody, context.Request.Body); // Body should be reset to original
            _loggerMock.Verify(
                logger => logger.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains($"RequestDecompression: Error decompressing request body with encoding: {encoding}")),
                    It.IsAny<Exception>(),
                    (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()
                ),
                Times.Once
            );
            _nextMock.Verify(next => next(context), Times.Once);
        }

        private byte[] CompressString(string content, string encoding)
        {
            var contentBytes = Encoding.UTF8.GetBytes(content);
            using var outputStream = new MemoryStream();
            
            Stream? compressionStream = null;
            try
            {
                switch (encoding.ToLower())
                {
                    case "gzip":
                        compressionStream = new GZipStream(outputStream, CompressionMode.Compress, true);
                        break;
                    case "br":
                        compressionStream = new BrotliStream(outputStream, CompressionMode.Compress, true);
                        break;
                    case "deflate":
                        compressionStream = new DeflateStream(outputStream, CompressionMode.Compress, true);
                        break;
                    default:
                        throw new ArgumentException($"Unsupported encoding: {encoding}");
                }

                using (compressionStream)
                {
                    compressionStream.Write(contentBytes, 0, contentBytes.Length);
                }

                return outputStream.ToArray();
            }
            finally
            {
                compressionStream?.Dispose();
            }
        }
    }
}
