﻿using System;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Gateway.Application;
using CoverGo.Gateway.Domain;
using CoverGo.Proxies.Auth;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Primitives;
using Microsoft.IdentityModel.JsonWebTokens;
using Moq;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Application;

public class KeyCloakTokenExchangeMiddlewareTest
{
    private readonly Mock<IAuthService> _authServiceMock = new();
    private readonly Mock<IDistributedCache> _cacheMock = new();
    private readonly Mock<IDateTimeProvider> _dateTimeProviderMock = new();
    private readonly Mock<IOptions<CacheSettings>> _cacheSettingsOptionsMock = new();

    [Fact]
    public async Task GIVEN_no_authorization_header_WHEN_Invoke_invoked_THEN_next_middleware_should_be_called()
    {
        KeyCloakTokenExchangeMiddleware middleware = CreateMiddleware();
        DefaultHttpContext context = new();

        Func<Task> act = async () => await middleware.Invoke(context, NullLogger<KeyCloakTokenExchangeMiddleware>.Instance);

        await act.Should().NotThrowAsync();
    }

    [Fact]
    public async Task GIVEN_uncached_valid_access_token_WHEN_Invoke_invoked_THEN_internal_access_token_should_be_generated_AND_added_into_cache()
    {
        string username = Guid.NewGuid().ToString();
        const string clientId = "admin_portal";
        const string tenantId = "covergo";
        string loginId = Guid.NewGuid().ToString();

        string token = JwtTokenGenerator.GenerateToken(
            Guid.NewGuid().ToString(),
            $"http://localhost:62222/realms/{tenantId}",
            "account",
            DateTime.UtcNow.AddHours(1),
            new Claim(JwtRegisteredClaimNames.Sub, username),
            new Claim(JwtRegisteredClaimNames.Azp, clientId));

        string internalToken = JwtTokenGenerator.GenerateToken(
            Guid.NewGuid().ToString(),
            $"http://localhost:60000",
            "",
            DateTime.UtcNow.AddHours(1));

        _cacheSettingsOptionsMock.SetupGet(x => x.Value).Returns(new CacheSettings());
        _dateTimeProviderMock.SetupGet(x => x.UtcNow).Returns(DateTime.UtcNow);
        _cacheMock.Setup(cache => cache.GetAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Encoding.UTF8.GetBytes(string.Empty));
        _authServiceMock.Setup(svc => svc.GetLoginsAsync(tenantId, It.IsAny<LoginWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new []{new Login { Id = loginId }});
        _authServiceMock.Setup(svc => svc.GetInternalAccessTokenAsync(tenantId, loginId, clientId,
                 It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Token
            {
                AccessToken = internalToken
            });

        KeyCloakTokenExchangeMiddleware middleware = CreateMiddleware();
        DefaultHttpContext context = new();

        context.Request.Headers.Add(HttpRequestHeader.Authorization.ToString(), $"Bearer {token}");

        Func<Task> act = async () => await middleware.Invoke(context, NullLogger<KeyCloakTokenExchangeMiddleware>.Instance);

        await act.Should().NotThrowAsync();

        context.Request.Headers[HttpRequestHeader.Authorization.ToString()].First().Should().Be($"Bearer {internalToken}");

        _cacheMock.Verify(
            cache => cache.SetAsync(It.IsAny<string>(), It.IsAny<byte[]>(), It.IsAny<DistributedCacheEntryOptions>(),
                It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GIVEN_cached_valid_access_token_WHEN_Invoke_invoked_THEN_should_used_cached_internal_token()
    {
        string username = Guid.NewGuid().ToString();
        const string clientId = "admin_portal";
        const string tenantId = "covergo";
        string loginId = Guid.NewGuid().ToString();

        string token = JwtTokenGenerator.GenerateToken(
            Guid.NewGuid().ToString(),
            $"http://localhost:62222/realms/{tenantId}",
            "account",
            DateTime.UtcNow.AddHours(1),
            new Claim(JwtRegisteredClaimNames.Sub, username),
            new Claim(JwtRegisteredClaimNames.Azp, clientId));

        string internalToken = JwtTokenGenerator.GenerateToken(
            Guid.NewGuid().ToString(),
            $"http://localhost:60000",
            "",
            DateTime.UtcNow.AddHours(1));

        _cacheSettingsOptionsMock.SetupGet(x => x.Value).Returns(new CacheSettings());
        _dateTimeProviderMock.SetupGet(x => x.UtcNow).Returns(DateTime.UtcNow);
        _cacheMock.Setup(cache => cache.GetAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Encoding.UTF8.GetBytes(internalToken));

        KeyCloakTokenExchangeMiddleware middleware = CreateMiddleware();
        DefaultHttpContext context = new();

        context.Request.Headers.Add(HttpRequestHeader.Authorization.ToString(), $"Bearer {token}");

        await middleware.Invoke(context, NullLogger<KeyCloakTokenExchangeMiddleware>.Instance);

        context.Request.Headers[HttpRequestHeader.Authorization.ToString()].First().Should().Be($"Bearer {internalToken}");

        _authServiceMock.Verify(svc => svc.GetLoginsAsync(tenantId, It.IsAny<LoginWhere>(), It.IsAny<CancellationToken>()), Times.Exactly(1));
        _authServiceMock.Verify(
            svc => svc.GetInternalAccessTokenAsync(tenantId, loginId, clientId,
                It.IsAny<CancellationToken>()), Times.Never);
        _cacheMock.Verify(
            cache => cache.SetAsync(It.IsAny<string>(), It.IsAny<byte[]>(), It.IsAny<DistributedCacheEntryOptions>(),
                It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GIVEN_invalid_access_token_WHEN_Invoke_invoked_THEN_next_middleware_should_be_called_AND_token_should_not_be_replaced()
    {
        KeyCloakTokenExchangeMiddleware middleware = CreateMiddleware();
        DefaultHttpContext context = new ();

        context.Request.Headers.Add("Authorization", "Bearer invalid_access_token");

        Func<Task> act = async () => await middleware.Invoke(context, NullLogger<KeyCloakTokenExchangeMiddleware>.Instance);

        await act.Should().NotThrowAsync();
        context.Request.Headers[HttpRequestHeader.Authorization.ToString()].First().Should().Be($"Bearer invalid_access_token");
    }

    [Fact]
    public async Task
        GIVEN_expired_access_token_WHEN_Invoke_invoked_THEN_next_middleware_should_be_called_AND_token_should_not_be_replaced()
    {
        string username = Guid.NewGuid().ToString();
        const string clientId = "admin_portal";
        const string tenantId = "covergo";

        string token = JwtTokenGenerator.GenerateToken(
            Guid.NewGuid().ToString(),
            $"http://localhost:62222/realms/{tenantId}",
            "account",
            DateTime.UtcNow.AddHours(-1),
            new Claim(JwtRegisteredClaimNames.Sub, username),
            new Claim(JwtRegisteredClaimNames.Azp, clientId));

        _dateTimeProviderMock.SetupGet(x => x.UtcNow).Returns(DateTime.UtcNow);

        KeyCloakTokenExchangeMiddleware middleware = CreateMiddleware();
        DefaultHttpContext context = new();

        context.Request.Headers.Add("Authorization", $"Bearer {token}");


        Func<Task> act = async () => await middleware.Invoke(context, NullLogger<KeyCloakTokenExchangeMiddleware>.Instance);

        await act.Should().NotThrowAsync();
        context.Request.Headers[HttpRequestHeader.Authorization.ToString()].First().Should().Be($"Bearer {token}");
    }

    [Fact]
    public async Task GIVEN_multiple_authorization_headers_WHEN_Invoke_invoked_THEN_token_should_be_replaced_correctly()
    {
        string username = Guid.NewGuid().ToString();
        const string clientId = "admin_portal";
        const string tenantId = "covergo";
        string loginId = Guid.NewGuid().ToString();

        string token = JwtTokenGenerator.GenerateToken(
            Guid.NewGuid().ToString(),
            $"http://localhost:62222/realms/{tenantId}",
            "account",
            DateTime.UtcNow.AddHours(1),
            new Claim(JwtRegisteredClaimNames.Sub, username),
            new Claim(JwtRegisteredClaimNames.Azp, clientId));

        string internalToken = JwtTokenGenerator.GenerateToken(
            Guid.NewGuid().ToString(),
            $"http://localhost:60000",
            "",
            DateTime.UtcNow.AddHours(1));

        _cacheSettingsOptionsMock.SetupGet(x => x.Value).Returns(new CacheSettings());
        _dateTimeProviderMock.SetupGet(x => x.UtcNow).Returns(DateTime.UtcNow);
        _cacheMock.Setup(cache => cache.GetAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Encoding.UTF8.GetBytes(string.Empty));
        _authServiceMock.Setup(svc => svc.GetLoginsAsync(tenantId, It.IsAny<LoginWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new []{new Login { Id = loginId }});
        _authServiceMock.Setup(svc => svc.GetInternalAccessTokenAsync(tenantId, loginId, clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Token
            {
                AccessToken = internalToken
            });

        KeyCloakTokenExchangeMiddleware middleware = CreateMiddleware();
        DefaultHttpContext context = new();

        context.Request.Headers.Add(HttpRequestHeader.Authorization.ToString(),
            new StringValues(new[]
            {
                $"Basic {Convert.ToBase64String(Encoding.UTF8.GetBytes("test:test"))}",
                $"Bearer {token}"
            }));

        Func<Task> act = async () => await middleware.Invoke(context, NullLogger<KeyCloakTokenExchangeMiddleware>.Instance);

        await act.Should().NotThrowAsync();

        context.Request.Headers[HttpRequestHeader.Authorization.ToString()].First(x => x.StartsWith("Bearer ")).Should().Be($"Bearer {internalToken}");

        _cacheMock.Verify(
            cache => cache.SetAsync(It.IsAny<string>(), It.IsAny<byte[]>(), It.IsAny<DistributedCacheEntryOptions>(),
                It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GIVEN_authorization_headers_without_bearer_WHEN_Invoke_invoked_THEN_next_middleware_should_be_called()
    {
        KeyCloakTokenExchangeMiddleware middleware = CreateMiddleware();
        DefaultHttpContext context = new();

        context.Request.Headers.Add(HttpRequestHeader.Authorization.ToString(),
            new StringValues(new[]
            {
                $"Basic {Convert.ToBase64String(Encoding.UTF8.GetBytes("test:test"))}",
            }));

        Func<Task> act = async () => await middleware.Invoke(context, NullLogger<KeyCloakTokenExchangeMiddleware>.Instance);

        await act.Should().NotThrowAsync();
    }

    private KeyCloakTokenExchangeMiddleware CreateMiddleware() =>
        new(
            next: _ => Task.CompletedTask,
            authService: _authServiceMock.Object,
            cache: _cacheMock.Object,
            dateTimeProvider: _dateTimeProviderMock.Object,
           _cacheSettingsOptionsMock.Object
        );
}