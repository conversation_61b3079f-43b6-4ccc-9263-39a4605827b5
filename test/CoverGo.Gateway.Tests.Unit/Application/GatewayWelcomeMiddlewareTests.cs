using CoverGo.FeatureManagement;
using CoverGo.Gateway.Application;
using CoverGo.RateLimiting;
using GraphQL.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.AspNetCore.Server.Kestrel.Core.Features;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.IO;
using System.Net;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;
using Xunit;
namespace CoverGo.Gateway.Tests.Unit.Application
{
    public class GatewayWelcomeMiddlewareTests
    {
        private readonly Mock<IRateLimitingService> _rateLimitingServiceMock;
        private readonly Mock<ILogger<GatewayWelcomeMiddleware>> _loggerMock;
        private readonly Mock<IDocumentWriter> _documentWriterMock;
        private readonly Mock<RequestDelegate> _nextMock;
        private readonly Mock<IMultiTenantFeatureManager> _multiTenantFeatureManagerMock;
        private readonly GatewayWelcomeMiddleware _middleware;

        public GatewayWelcomeMiddlewareTests()
        {
            _rateLimitingServiceMock = new Mock<IRateLimitingService>();
            _loggerMock = new Mock<ILogger<GatewayWelcomeMiddleware>>();
            _documentWriterMock = new Mock<IDocumentWriter>();
            _nextMock = new Mock<RequestDelegate>();
            _multiTenantFeatureManagerMock = new Mock<IMultiTenantFeatureManager>();
            _middleware = new GatewayWelcomeMiddleware(_nextMock.Object);
        }

        [Fact]
        public async Task Invoke_Should_Call_Next_If_Not_GraphQlEndpoint()
        {
            var context = new DefaultHttpContext();
            context.Request.Path = "/not-graphql";

            await _middleware.Invoke(context, null, _rateLimitingServiceMock.Object, _loggerMock.Object, _documentWriterMock.Object, _multiTenantFeatureManagerMock.Object);

            _nextMock.Verify(next => next(context), Times.Once);
        }

        [Fact]
        public async Task Invoke_Should_Call_Next_If_OperationName_Is_Empty()
        {
            var context = new DefaultHttpContext();
            context.Request.Path = "/graphql";
            context.Request.Body = new MemoryStream();
            _rateLimitingServiceMock.Setup(service => service.TryConsumeResources(It.IsAny<ConsumeResourceRequest[]>(), It.IsAny<CheckResourceBalanceRequest[]>()))
                .ReturnsAsync(true);

            await _middleware.Invoke(context, null, _rateLimitingServiceMock.Object, _loggerMock.Object, _documentWriterMock.Object, _multiTenantFeatureManagerMock.Object);

            _nextMock.Verify(next => next(context), Times.Once);
        }

        [Fact]
        public async Task Invoke_Should_Call_Next_If_OperationName_Is_IntrospectionQuery()
        {
            var context = new DefaultHttpContext();
            context.Request.Path = "/graphql";
            var requestBody = new
            {
                Query = "{ someQuery }",
                OperationName = "TestOperation"
            };
            var requestBodyJson = JsonSerializer.Serialize(requestBody);
            context.Request.Body = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(requestBodyJson));
            context.Request.ContentType = "application/json";
            _rateLimitingServiceMock.Setup(service => service.TryConsumeResources(It.IsAny<ConsumeResourceRequest[]>(), It.IsAny<CheckResourceBalanceRequest[]>()))
                .ReturnsAsync(true);

            await _middleware.Invoke(context, null, _rateLimitingServiceMock.Object, _loggerMock.Object, _documentWriterMock.Object, _multiTenantFeatureManagerMock.Object);

            _nextMock.Verify(next => next(context), Times.Once);
        }

        [Fact]
        public async Task Invoke_Should_Return_TooManyRequests_If_Rate_Limited_On_GraphQLRequest()
        {
            var context = new DefaultHttpContext();
            context.Request.Path = "/graphql";
            var requestBody = new
            {
                query = "{ someQuery }",
                operationName = "TestOperation"
            };
            var requestBodyJson = JsonSerializer.Serialize(requestBody);
            context.Request.Body = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(requestBodyJson));
            context.Request.ContentType = "application/json";
            _rateLimitingServiceMock.Setup(service => service.TryConsumeResources(It.Is<ConsumeResourceRequest[]>(x => x.Length == 1 && x[0].Resource.Name == "GraphQLRequest"), It.IsAny<CheckResourceBalanceRequest[]>()))
                .ReturnsAsync(false);

            await _middleware.Invoke(context, null, _rateLimitingServiceMock.Object, _loggerMock.Object, _documentWriterMock.Object, _multiTenantFeatureManagerMock.Object);

            Assert.Equal((int)HttpStatusCode.TooManyRequests, context.Response.StatusCode);
        }

        [Fact]
        public async Task Invoke_Should_Return_TooManyRequests_If_Rate_Limited()
        {
            var context = new DefaultHttpContext();
            context.Request.Path = "/graphql";
            var requestBody = new
            {
                query = "{ someQuery }",
                operationName = "TestOperation"
            };
            var requestBodyJson = JsonSerializer.Serialize(requestBody);
            context.Request.Body = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(requestBodyJson));
            context.Request.ContentType = "application/json";
            _rateLimitingServiceMock.Setup(service => service.TryConsumeResources(It.Is<ConsumeResourceRequest[]>(x => x.Length == 1 && x[0].Resource.Name == "GraphQLRequest"), It.IsAny<CheckResourceBalanceRequest[]>()))
                .ReturnsAsync(true);
            _rateLimitingServiceMock.Setup(service => service.TryConsumeResources(It.Is<ConsumeResourceRequest[]>(x => x.Length == 1 && x[0].Resource.Name != "GraphQLRequest"), It.IsAny<CheckResourceBalanceRequest[]>()))
                .ReturnsAsync(false);

            await _middleware.Invoke(context, null, _rateLimitingServiceMock.Object, _loggerMock.Object, _documentWriterMock.Object, _multiTenantFeatureManagerMock.Object);

            Assert.Equal((int)HttpStatusCode.TooManyRequests, context.Response.StatusCode);
        }

        [Fact]
        public async Task Invoke_Should_Return_TooManyRequests_If_Rate_Limited_On_Punishment()
        {
            var context = new DefaultHttpContext();
            context.Request.Path = "/graphql";
            var requestBody = new
            {
                Query = "{ someQuery }",
                OperationName = "TestOperation"
            };
            var requestBodyJson = JsonSerializer.Serialize(requestBody);
            context.Request.Body = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(requestBodyJson));
            context.Request.ContentType = "application/json";
            _rateLimitingServiceMock.Setup(service => service.TryConsumeResources(It.IsAny<ConsumeResourceRequest[]>(), It.Is<CheckResourceBalanceRequest[]>(x => x.Length == 1 && x[0].Resource.Name != "GraphQLRequestsOperation__TESTOPERATION_PUNISHMENT")))
                .ReturnsAsync(true);
            _rateLimitingServiceMock.Setup(service => service.TryConsumeResources(It.IsAny<ConsumeResourceRequest[]>(), It.Is<CheckResourceBalanceRequest[]>(x => x.Length == 1 && x[0].Resource.Name == "GraphQLRequestsOperation__TESTOPERATION_PUNISHMENT")))
                .ReturnsAsync(false);

            await _middleware.Invoke(context, null, _rateLimitingServiceMock.Object, _loggerMock.Object, _documentWriterMock.Object, _multiTenantFeatureManagerMock.Object);

            Assert.Equal((int)HttpStatusCode.TooManyRequests, context.Response.StatusCode);
        }

        [Fact]
        public async Task Invoke_Should_Call_Next_If_Rate_Limit_Not_Exceeded()
        {
            var context = new DefaultHttpContext();
            context.Request.Path = "/graphql";
            context.Request.Body = new MemoryStream();
            _rateLimitingServiceMock.Setup(service => service.TryConsumeResources(It.Is<ConsumeResourceRequest[]>(x => x.Length == 1 && x[0].Resource.Name == "GraphQLRequest"), It.IsAny<CheckResourceBalanceRequest[]>()))
                .ReturnsAsync(true);
            _rateLimitingServiceMock.Setup(service => service.TryConsumeResources(It.Is<ConsumeResourceRequest[]>(x => x.Length == 1 && x[0].Resource.Name != "GraphQLRequest"), It.IsAny<CheckResourceBalanceRequest[]>()))
                .ReturnsAsync(true);

            await _middleware.Invoke(context, null, _rateLimitingServiceMock.Object, _loggerMock.Object, _documentWriterMock.Object, _multiTenantFeatureManagerMock.Object);

            _nextMock.Verify(next => next(context), Times.Once);
        }

        [Fact]
        public async Task Invoke_Should_Extract_OperationName_From_Query()
        {
            var context = new DefaultHttpContext();
            context.Request.Path = "/graphql";
            var requestBody = new
            {
                Query = "\n\t\tfragment result on result {\n\t\t\tstatus\n\t\t\terrors\n\t\t\terrors_2 {\n\t\t\t\tcode\n\t\t\t\tmessage\n\t\t\t}\n\t\t}\n\t \n\t\tmutation forgotPassword($tenantId: String!, $input: forgotPasswordInput!) {\n\t\t\tforgotPassword(tenantId: $tenantId, forgotPasswordInput: $input) {\n\t\t\t\t...result\n\t\t\t}\n\t\t}\n\t"
            };
            var requestBodyJson = JsonSerializer.Serialize(requestBody);
            context.Request.Body = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(requestBodyJson));
            context.Request.ContentType = "application/json";
            _rateLimitingServiceMock.Setup(service => service.TryConsumeResources(It.IsAny<ConsumeResourceRequest[]>(), It.Is<CheckResourceBalanceRequest[]>(x => x.Length == 1 && x[0].Resource.Name != "GraphQLRequestsOperation__FORGOTPASSWORD_PUNISHMENT")))
                .ReturnsAsync(true);
            _rateLimitingServiceMock.Setup(service => service.TryConsumeResources(It.IsAny<ConsumeResourceRequest[]>(), It.Is<CheckResourceBalanceRequest[]>(x => x.Length == 1 && x[0].Resource.Name == "GraphQLRequestsOperation__FORGOTPASSWORD_PUNISHMENT")))
                .ReturnsAsync(false);

            await _middleware.Invoke(context, null, _rateLimitingServiceMock.Object, _loggerMock.Object, _documentWriterMock.Object, _multiTenantFeatureManagerMock.Object);

            Assert.Equal((int)HttpStatusCode.TooManyRequests, context.Response.StatusCode);
        }

        [Fact]
        public async Task Invoke_SetsMinDataRate_WhenUseCustomDataRateLimitIsEnabled()
        {
            // Arrange
            var mockRequestDelegate = new Mock<RequestDelegate>();
            var mockRateLimitingService = new Mock<IRateLimitingService>();
            var mockLogger = new Mock<ILogger<GatewayWelcomeMiddleware>>();
            var mockDocumentWriter = new Mock<IDocumentWriter>();
            var mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
            var mockHttpContext = new DefaultHttpContext();
            var mockHttpMinRequestBodyDataRateFeature = new Mock<IHttpMinRequestBodyDataRateFeature>();

            mockHttpContext.Features.Set(mockHttpMinRequestBodyDataRateFeature.Object);
            mockHttpContext.Request.Path = "/graphql";
            mockHttpContext.User = new ClaimsPrincipal(new ClaimsIdentity(new[] { new Claim("tenantId", "testTenant") }));

            mockFeatureManager
                .Setup(m => m.IsEnabled("UseCustomDataRateLimit", "testTenant"))
                .ReturnsAsync(true);

            var middleware = new GatewayWelcomeMiddleware(mockRequestDelegate.Object);

            // Act
            await middleware.Invoke(mockHttpContext, null, mockRateLimitingService.Object, mockLogger.Object, mockDocumentWriter.Object, mockFeatureManager.Object);

            // Assert
            mockHttpMinRequestBodyDataRateFeature.VerifySet(f => f.MinDataRate = It.Is<MinDataRate>(rate => rate.BytesPerSecond == 240 && rate.GracePeriod == TimeSpan.FromSeconds(10)), Times.Once);
        }

        [Fact]
        public async Task Invoke_DoesNotSetMinDataRate_WhenUseCustomDataRateLimitIsDisabled()
        {
            // Arrange
            var mockRequestDelegate = new Mock<RequestDelegate>();
            var mockRateLimitingService = new Mock<IRateLimitingService>();
            var mockLogger = new Mock<ILogger<GatewayWelcomeMiddleware>>();
            var mockDocumentWriter = new Mock<IDocumentWriter>();
            var mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
            var mockHttpContext = new DefaultHttpContext();
            var mockHttpMinRequestBodyDataRateFeature = new Mock<IHttpMinRequestBodyDataRateFeature>();

            mockHttpContext.Features.Set(mockHttpMinRequestBodyDataRateFeature.Object);
            mockHttpContext.Request.Path = "/graphql";
            mockHttpContext.User = new ClaimsPrincipal(new ClaimsIdentity(new[] { new Claim("tenantId", "testTenant") }));

            mockFeatureManager
                .Setup(m => m.IsEnabled("UseCustomDataRateLimit", "testTenant"))
                .ReturnsAsync(false);

            var middleware = new GatewayWelcomeMiddleware(mockRequestDelegate.Object);

            // Act
            await middleware.Invoke(mockHttpContext, null, mockRateLimitingService.Object, mockLogger.Object, mockDocumentWriter.Object, mockFeatureManager.Object);

            // Assert
            mockHttpMinRequestBodyDataRateFeature.VerifySet(f => f.MinDataRate = It.IsAny<MinDataRate>(), Times.Never);
        }
    }
}

