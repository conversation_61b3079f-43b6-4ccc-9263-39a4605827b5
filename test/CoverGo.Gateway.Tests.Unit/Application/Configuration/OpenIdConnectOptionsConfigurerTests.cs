﻿using System;
using CoverGo.Gateway.Application.Configuration;
using CoverGo.Gateway.Infrastructure.Auth;
using FluentAssertions;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Http;
using Moq;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Application.Configuration;

public class OpenIdConnectOptionsConfigurerTests
{
    private readonly Mock<IHttpContextAccessor> _mockHttpContextAccessor;
    public OpenIdConnectOptionsConfigurerTests()
    {
        _mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
    }
    
    [Fact]
    public void GIVEN_null_schemes_WHEN_constructing_OpenIdConnectOptionsConfigurer_THEN_throw_argument_null_exception()
    {
        Action action = () => new OpenIdConnectOptionsConfigurer(null, _mockHttpContextAccessor.Object);

        action.Should().Throw<ArgumentNullException>().WithMessage("*schemes*");
    }

    [Fact]
    public void GIVEN_null_httpContextAccessor_WHEN_constructing_OpenIdConnectOptionsConfigurer_THEN_throw_argument_null_exception()
    {
        Action action = () => new OpenIdConnectOptionsConfigurer(new[] { "scheme" }, null);

        action.Should().Throw<ArgumentNullException>().WithMessage("*httpContextAccessor*");
    }

    [Fact]
    public void GIVEN_valid_parameters_WHEN_PostConfigure_THEN_set_state_data_format()
    {
        OpenIdConnectOptionsConfigurer configurer = new(new[] { "scheme" },  _mockHttpContextAccessor.Object);
        OpenIdConnectOptions options = new();

        configurer.PostConfigure("scheme", options);

        options.StateDataFormat.Should().BeOfType<DistributedCacheStateDataFormatter>();
    }

    [Fact]
    public void GIVEN_empty_schemes_WHEN_PostConfigure_THEN_set_state_data_format()
    {
        OpenIdConnectOptionsConfigurer configurer = new(new string[] { }, _mockHttpContextAccessor.Object);
        OpenIdConnectOptions options = new ();

        configurer.PostConfigure("scheme", options);

        options.StateDataFormat.Should().BeOfType<DistributedCacheStateDataFormatter>();
    }

    [Fact]
    public void GIVEN_invalid_scheme_WHEN_PostConfigure_THEN_do_not_set_state_data_format()
    {
        OpenIdConnectOptionsConfigurer configurer = new(new[] { "valid-scheme" }, _mockHttpContextAccessor.Object);
        OpenIdConnectOptions options = new();

        configurer.PostConfigure("invalid-scheme", options);

        options.StateDataFormat.Should().BeNull();
    }
}
