using System;
using System.Security.Claims;
using CoverGo.Gateway.Application.Services;
using CoverGo.Gateway.Domain.Claims;
using CoverGo.Gateway.Domain.Claims.Asia;
using Microsoft.AspNetCore.Http;
using Moq;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit.Application.Services
{
    public class LazyResolvedTenantSpecificClaimServiceTests
    {
        private readonly Mock<IHttpContextAccessor> _mockHttpContextAccessor;
        private readonly Mock<HttpContext> _mockHttpContext;
        private readonly Mock<ClaimsPrincipal> _mockClaimsPrincipal;
        private readonly Mock<IServiceProvider> _mockServiceProvider;
        private readonly Mock<ITenantSpecificClaimService> _mockDefaultService;
        private readonly Mock<ITenantSpecificClaimService> _mockAsiaService;
        private readonly CoverGo.Gateway.Domain.Claims.Claim _testClaim;

        public LazyResolvedTenantSpecificClaimServiceTests()
        {
            _mockHttpContextAccessor = new Mock<IHttpContextAccessor>();
            _mockHttpContext = new Mock<HttpContext>();
            _mockClaimsPrincipal = new Mock<ClaimsPrincipal>();
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockDefaultService = new Mock<ITenantSpecificClaimService>();
            _mockAsiaService = new Mock<ITenantSpecificClaimService>();
            _testClaim = new CoverGo.Gateway.Domain.Claims.Claim { Fields = new JObject() };

            // Setup default behavior
            _mockHttpContextAccessor.Setup(x => x.HttpContext).Returns(_mockHttpContext.Object);
            _mockHttpContext.Setup(c => c.User).Returns(_mockClaimsPrincipal.Object);
        }

        [Fact]
        public void GetClaimApprovalAmount_WithDefaultTenant_UsesDefaultService()
        {
            // Arrange
            const decimal expectedAmount = 123.45m;
            SetupTenantId("default_tenant");
            _mockDefaultService.Setup(s => s.GetClaimApprovalAmount(It.IsAny<CoverGo.Gateway.Domain.Claims.Claim>()))
                .Returns(expectedAmount);

            // Setup service provider to return default service
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(DefaultTenantSpecificClaimService)))
                .Returns(_mockDefaultService.Object);

            LazyResolvedTenantSpecificClaimService service = new(_mockHttpContextAccessor.Object, _mockServiceProvider.Object);

            // Act
            decimal result = service.GetClaimApprovalAmount(_testClaim);

            // Assert
            Assert.Equal(expectedAmount, result);
            _mockDefaultService.Verify(s => s.GetClaimApprovalAmount(It.IsAny<CoverGo.Gateway.Domain.Claims.Claim>()), Times.Once);
        }

        [Fact]
        public void GetClaimApprovalAmount_WithAsiaTenant_UsesAsiaService()
        {
            // Arrange
            const decimal expectedAmount = 456.78m;
            SetupTenantId("asia_tenant");
            _mockAsiaService.Setup(s => s.GetClaimApprovalAmount(It.IsAny<CoverGo.Gateway.Domain.Claims.Claim>()))
                .Returns(expectedAmount);

            // Setup service provider to return Asia service
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(AsiaClaimService)))
                .Returns(_mockAsiaService.Object);

            LazyResolvedTenantSpecificClaimService service = new(_mockHttpContextAccessor.Object, _mockServiceProvider.Object);

            // Act
            decimal result = service.GetClaimApprovalAmount(_testClaim);

            // Assert
            Assert.Equal(expectedAmount, result);
            _mockAsiaService.Verify(s => s.GetClaimApprovalAmount(It.IsAny<CoverGo.Gateway.Domain.Claims.Claim>()), Times.Once);
        }

        [Fact]
        public void GetClaimApprovalAmount_WhenHttpContextIsNull_UsesDefaultService()
        {
            // Arrange
            const decimal expectedAmount = 100m;
            _mockHttpContextAccessor.Setup(x => x.HttpContext).Returns((HttpContext?)null);
            _mockDefaultService.Setup(s => s.GetClaimApprovalAmount(It.IsAny<CoverGo.Gateway.Domain.Claims.Claim>()))
                .Returns(expectedAmount);

            // Setup service provider to return default service
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(DefaultTenantSpecificClaimService)))
                .Returns(_mockDefaultService.Object);

            LazyResolvedTenantSpecificClaimService service = new(_mockHttpContextAccessor.Object, _mockServiceProvider.Object);

            // Act
            decimal result = service.GetClaimApprovalAmount(_testClaim);

            // Assert
            Assert.Equal(expectedAmount, result);
            _mockDefaultService.Verify(s => s.GetClaimApprovalAmount(It.IsAny<CoverGo.Gateway.Domain.Claims.Claim>()), Times.Once);
        }

        [Fact]
        public void GetClaimApprovalAmount_WhenTenantIdIsNull_UsesDefaultService()
        {
            // Arrange
            const decimal expectedAmount = 100m;
            SetupTenantId(null);
            _mockDefaultService.Setup(s => s.GetClaimApprovalAmount(It.IsAny<CoverGo.Gateway.Domain.Claims.Claim>()))
                .Returns(expectedAmount);

            // Setup service provider to return default service
            _mockServiceProvider.Setup(sp => sp.GetService(typeof(DefaultTenantSpecificClaimService)))
                .Returns(_mockDefaultService.Object);

            LazyResolvedTenantSpecificClaimService service = new(_mockHttpContextAccessor.Object, _mockServiceProvider.Object);

            // Act
            decimal result = service.GetClaimApprovalAmount(_testClaim);

            // Assert
            Assert.Equal(expectedAmount, result);
            _mockDefaultService.Verify(s => s.GetClaimApprovalAmount(It.IsAny<CoverGo.Gateway.Domain.Claims.Claim>()), Times.Once);
        }

        private void SetupTenantId(string? tenantId)
        {
            if (tenantId != null)
            {
                Mock<System.Security.Claims.Claim> mockClaim = new("tenantId", tenantId);
                _mockClaimsPrincipal.Setup(cp => cp.FindFirst("tenantId")).Returns(mockClaim.Object);
            }
            else
            {
                _mockClaimsPrincipal.Setup(cp => cp.FindFirst("tenantId")).Returns((System.Security.Claims.Claim?)null);
            }
        }
    }
}
