﻿using CoverGo.Gateway.Application.Logging;
using FluentAssertions;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using Xunit;
using Inputs = GraphQL.Inputs;

namespace CoverGo.Gateway.Tests.Unit;

public class LogTests
{
    private const string TargetIdsKey = "TargetIds";
    private const string PagingVariablesKey = "PagingVariables";

    [Fact]
    public void GIVEN_input_with_id_WHEN_add_targetIds_to_log_THEN_it_is_found()
    {
        string guid = Guid.NewGuid().ToString();
        string key = "id";
        Inputs inputs = new(
            new Dictionary<string, object>()
            {
                { key, guid }
            });

        Dictionary<string, object> loggingProperties = new();

        GraphQlQueryLoggingExtensions.AddTargetIdsToLoggingProperties(inputs, loggingProperties);

        object targetIds = loggingProperties[TargetIdsKey];
        targetIds.Should().NotBeNull();
        ToDictionary<object>(targetIds)[key].Should().Be(guid);
    }
    
    [Fact]
    public void GIVEN_input_with_paging_WHEN_add_paging_variables_to_log_THEN_it_is_found()
    {
        int skip = 50;
        int limit = 10;
        int policiesLimit = 44;
        string skipVariable = "skip";
        string limitVariable = "limit";
        string policiesLimitVariable = "policiesLimit";
        Inputs inputs = new(
            new Dictionary<string, object>
            {
                { skipVariable, skip },
                { limitVariable, limit },
                { policiesLimitVariable, policiesLimit }
            });

        Dictionary<string, object> loggingProperties = new();

        GraphQlQueryLoggingExtensions.AddPagingVariablesToLoggingProperties(inputs, loggingProperties);

        object pagingVariables = loggingProperties[PagingVariablesKey];
        pagingVariables.Should().NotBeNull();
        ToDictionary<object>(pagingVariables)[skipVariable].Should().Be(skip);
        ToDictionary<object>(pagingVariables)[limitVariable].Should().Be(limit);
        ToDictionary<object>(pagingVariables)[policiesLimitVariable].Should().Be(policiesLimit);
    }
    
    [Fact]
    public void GIVEN_no_paging_WHEN_add_paging_variables_to_log_THEN_it_is_not_found()
    {
        Inputs inputs = new(new Dictionary<string, object>());
        Dictionary<string, object> loggingProperties = new();

        GraphQlQueryLoggingExtensions.AddPagingVariablesToLoggingProperties(inputs, loggingProperties);

        loggingProperties.ContainsKey(PagingVariablesKey).Should().BeFalse();
    }
    
    [Fact]
    public void GIVEN_input_with_input_object_paging_WHEN_add_paging_variables_to_log_THEN_it_is_found()
    {
        int skip = 50;
        string skipVariable = "skip";
        Inputs inputs = new(
            new Dictionary<string, object>
            {
                { "input", new Dictionary<string, object>
                    {
                        { skipVariable, skip }
                    }
                }
            });

        Dictionary<string, object> loggingProperties = new();

        GraphQlQueryLoggingExtensions.AddPagingVariablesToLoggingProperties(inputs, loggingProperties);

        object pagingVariables = loggingProperties[PagingVariablesKey];
        pagingVariables.Should().NotBeNull();
        ToDictionary<object>(pagingVariables)[$"input.{skipVariable}"].Should().Be(skip);
    }

    [Fact]
    public void GIVEN_input_with_property_containing_id_WHEN_add_targetIds_to_log_THEN_it_is_found()
    {
        string guid = Guid.NewGuid().ToString();
        string key = "policyId";
        Inputs inputs = new(
            new Dictionary<string, object>()
            {
                { key, guid }
            });

        Dictionary<string, object> loggingProperties = new();

        GraphQlQueryLoggingExtensions.AddTargetIdsToLoggingProperties(inputs, loggingProperties);

        object targetIds = loggingProperties[TargetIdsKey];
        targetIds.Should().NotBeNull();
        ToDictionary<object>(targetIds)[key].Should().Be(guid);
    }

    [Fact]
    public void GIVEN_input_with_property_containing_multipleIds_WHEN_add_targetIds_to_log_THEN_it_is_found()
    {
        List<string> guidList = new() { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() };
        string key = "policyIds";
        Inputs inputs = new(
            new Dictionary<string, object>()
            {
                { key, guidList }
            });

        Dictionary<string, object> loggingProperties = new();

        GraphQlQueryLoggingExtensions.AddTargetIdsToLoggingProperties(inputs, loggingProperties);

        object targetIds = loggingProperties[TargetIdsKey];
        targetIds.Should().NotBeNull();
        ToDictionary<List<string>>(targetIds)[key].Should().BeEquivalentTo(guidList);
    }
    
    [Fact]
    public void GIVEN_input_with_multiple_properties_containing_id_WHEN_add_targetIds_to_log_THEN_it_is_found()
    {
        string guid1 = Guid.NewGuid().ToString();
        string guid2 = Guid.NewGuid().ToString();
        string key1 = "policyId";
        string key2 = "holderId";
        Inputs inputs = new(
            new Dictionary<string, object>()
            {
                { key1, guid1 },
                { key2, guid2}
            });

        Dictionary<string, object> loggingProperties = new();

        GraphQlQueryLoggingExtensions.AddTargetIdsToLoggingProperties(inputs, loggingProperties);

        object targetIds = loggingProperties[TargetIdsKey];
        targetIds.Should().NotBeNull();
        ToDictionary<object>(targetIds)[key1].Should().Be(guid1);
        ToDictionary<object>(targetIds)[key2].Should().Be(guid2);
    }

    [Fact]
    public void GIVEN_input_with_input_object_containing_id_WHEN_add_targetIds_to_log_THEN_it_is_found()
    {
        string guid = Guid.NewGuid().ToString();
        string key = "id";
        Inputs inputs = new(
            new Dictionary<string, object>()
            {
                { "input", new Dictionary<string, object>()
                    {
                        { key, guid }
                    }
                }
            });

        Dictionary<string, object> loggingProperties = new();

        GraphQlQueryLoggingExtensions.AddTargetIdsToLoggingProperties(inputs, loggingProperties);

        object targetIds = loggingProperties[TargetIdsKey];
        targetIds.Should().NotBeNull();
        ToDictionary<object>(targetIds)[$"input.{key}"].Should().Be(guid);
    }

    [Fact]
    public void GIVEN_input_with_input_object_containing_property_containing_id_WHEN_add_targetIds_to_log_THEN_it_is_found()
    {
        string guid = Guid.NewGuid().ToString();
        string key = "policyId";
        Inputs inputs = new(
            new Dictionary<string, object>()
            {
                { "input", new Dictionary<string, object>()
                    {
                        { key, guid }
                    }
                }
            });

        Dictionary<string, object> loggingProperties = new();

        GraphQlQueryLoggingExtensions.AddTargetIdsToLoggingProperties(inputs, loggingProperties);

        object targetIds = loggingProperties[TargetIdsKey];
        targetIds.Should().NotBeNull();
        ToDictionary<object>(targetIds)[$"input.{key}"].Should().Be(guid);
    }

    [Fact]
    public void GIVEN_input_with_input_object_containing_multiple_properties_containing_id_WHEN_add_targetIds_to_log_THEN_it_is_found()
    {
        string guid1 = Guid.NewGuid().ToString();
        string guid2 = Guid.NewGuid().ToString();
        string key1 = "policyId";
        string key2 = "insuredId";
        Inputs inputs = new(
            new Dictionary<string, object>()
            {
                { "input", new Dictionary<string, object>()
                    {
                        { key1, guid1 },
                        { key2, guid2 }
                    }
                }
            });

        Dictionary<string, object> loggingProperties = new();

        GraphQlQueryLoggingExtensions.AddTargetIdsToLoggingProperties(inputs, loggingProperties);

        object targetIds = loggingProperties[TargetIdsKey];
        targetIds.Should().NotBeNull();
        ToDictionary<object>(targetIds)[$"input.{key1}"].Should().Be(guid1);
        ToDictionary<object>(targetIds)[$"input.{key2}"].Should().Be(guid2);
    }

    [Fact]
    public void GIVEN_input_with_id_and_input_object_containing_property_containing_id_WHEN_add_targetIds_to_log_THEN_it_is_found()
    {
        string idGuid = Guid.NewGuid().ToString();
        string entityIdGuid = Guid.NewGuid().ToString();
        string key = "id";
        string inputKey = "entityId";
        Inputs inputs = new(
            new Dictionary<string, object>()
            {
                { key, idGuid},
                { "input", new Dictionary<string, object>()
                    {
                        { inputKey, entityIdGuid }
                    }
                }
            });

        Dictionary<string, object> loggingProperties = new();

        GraphQlQueryLoggingExtensions.AddTargetIdsToLoggingProperties(inputs, loggingProperties);

        object targetIds = loggingProperties[TargetIdsKey];
        targetIds.Should().NotBeNull();
        Dictionary<string, object> targetIdsDict = ToDictionary<object>(targetIds);
        targetIdsDict[key].Should().Be(idGuid);
        targetIdsDict[$"input.{inputKey}"].Should().Be(entityIdGuid);
    }

    [Fact]
    public void GIVEN_input_property_with_id_and_input_object_containing_property_containing_id_WHEN_add_targetIds_to_log_THEN_it_is_found()
    {
        string caseIdGuid = Guid.NewGuid().ToString();
        string proposalIdGuid = Guid.NewGuid().ToString();
        string key = "caseId";
        string inputKey = "proposalId";
        Inputs inputs = new(
            new Dictionary<string, object>()
            {
                { key, caseIdGuid},
                { "input", new Dictionary<string, object>()
                    {
                        { inputKey, proposalIdGuid }
                    }
                }
            });

        Dictionary<string, object> loggingProperties = new();

        GraphQlQueryLoggingExtensions.AddTargetIdsToLoggingProperties(inputs, loggingProperties);

        object targetIds = loggingProperties[TargetIdsKey];
        targetIds.Should().NotBeNull();
        Dictionary<string, object> targetIdsDict = ToDictionary<object>(targetIds);
        targetIdsDict[key].Should().Be(caseIdGuid);
        targetIdsDict[$"input.{inputKey}"].Should().Be(proposalIdGuid);
    }

    public static Dictionary<string, TValue> ToDictionary<TValue>(object obj)
    {
        string json = JsonConvert.SerializeObject(obj);
        Dictionary<string, TValue> dictionary = JsonConvert.DeserializeObject<Dictionary<string, TValue>>(json);
        return dictionary;
    }
}
