<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>

    <AssemblyName>CoverGo.Gateway.Tests.Unit</AssemblyName>
    <RootNamespace>CoverGo.Gateway.Tests.Unit</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoFixture" />
    <PackageReference Include="AutoFixture.AutoMoq" />
    <PackageReference Include="AutoFixture.Xunit2" />
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="JunitXml.TestLogger" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Moq" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="CoverGo.CryptoUtils" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\CoverGo.Gateway.Application\CoverGo.Gateway.Application.csproj" />
    <ProjectReference Include="..\..\src\CoverGo.Gateway.Infrastructure\Infrastructure.csproj" />
    <ProjectReference Include="..\..\src\CoverGo.Gateway.Interfaces\Interfaces.csproj" />
  </ItemGroup>

</Project>
