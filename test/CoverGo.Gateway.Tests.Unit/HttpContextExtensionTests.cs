using AutoFixture;
using CoverGo.Gateway.Common;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using System.Net;
using Xunit;

namespace CoverGo.Gateway.Tests.Unit;

public class HttpContextExtensionTests
{
    private readonly Fixture _fixture = new();

    [Fact]
    public void GIVEN_HttpContext_with_no_forwarded_header_WHEN_clientIpAddress_is_retrieved_THEN_connection_ip_address_is_returned()
    {
        var ipAddress = _fixture.Create<IPAddress>().ToString();
        var httpContext = new DefaultHttpContext();
        httpContext.Connection.RemoteIpAddress = IPAddress.Parse(ipAddress);
    
        var result = httpContext.GetClientIpAddress();

        result.Should().Be(ipAddress);
    }

    [Fact]
    public void GIVEN_HttpContext_with_single_ipAddress_forwarded_header_WHEN_clientIpAddress_is_retrieved_THEN_forwarded_ip_address_is_returned()
    {
        var headerIpAddress = new Fixture().Create<IPAddress>().ToString();
        var httpContext = new DefaultHttpContext();
        httpContext.Request.Headers["X-forwarded-for"] = headerIpAddress;

        var result = httpContext.GetClientIpAddress();

        result.Should().Be(headerIpAddress);
    }

    [Fact]
    public void GIVEN_HttpContext_with_multiple_ipAddress_forwarded_header_WHEN_clientIpAddress_is_retrieved_THEN_first_forwarded_ip_address_is_returned()
    {
        var firstIpAddress = new Fixture().Create<IPAddress>().ToString();
        var httpContext = new DefaultHttpContext();
        httpContext.Request.Headers["X-forwarded-for"] = string.Join(",", firstIpAddress, new Fixture().Create<IPAddress>().ToString());

        var result = httpContext.GetClientIpAddress();

        result.Should().Be(firstIpAddress);
    }
}