fragment productResult on product {
    productId {
      plan
      type
      version
    }
    policyIssuanceMethod
    offerValidityPeriod
}

mutation CreateProduct($input: createProductInput!) {
  createProduct(product: $input) {
    ...productResult
  }
}

mutation UpdateProduct($productId: productIdInput!, $input: updateProductInput!) {
  updateProduct(
    productId: $productId
    input: $input) {
    ...productResult
  }
}
