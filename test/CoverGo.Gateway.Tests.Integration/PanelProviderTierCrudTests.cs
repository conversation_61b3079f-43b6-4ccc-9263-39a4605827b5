﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class PanelProviderTierCrudTests : TestsBase
    {
        public PanelProviderTierCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_panelProviderTier_WHEN_create_panelProviderTier_THEN_succeed_and_created()
        {
            string organizationId = await Entity.CreateOrganization(new createOrganizationInput());
            string serviceItemId = await ServiceItem.Create(new createServiceItemInput { name = "name" });
            string serviceItemAgreedFeeId = await ServiceItemAgreedFee.Create(new createServiceItemAgreedFeeInput { serviceItemId = serviceItemId, rate = 10, currency = "USD" });
            createPanelProviderTierInput input = new()
            {
                panelId = organizationId,
                description = "description",
                fields = "{}",
                name = "name",
                serviceItemAgreedFeeIds = new List<string?>
                {
                    serviceItemAgreedFeeId
                }
            };
            string id = await PanelProviderTier.Create(input);

            panelProviderTier? panelProviderTier = await PanelProviderTier.Search(new panelProviderTierWhereInput { id = id });

            panelProviderTier.ShouldHaveSameValuesAsInput(input);
        }

        [Fact]
        public async Task GIVEN_a_created_panelProviderTier_WHEN_update_this_panelProviderTier_THEN_succeed_and_updated()
        {
            string organizationId = await Entity.CreateOrganization(new createOrganizationInput());
            string serviceItemId = await ServiceItem.Create(new createServiceItemInput { name = "name" });
            string serviceItemAgreedFeeId = await ServiceItemAgreedFee.Create(new createServiceItemAgreedFeeInput { serviceItemId = serviceItemId, rate = 10, currency = "USD" });
            createPanelProviderTierInput createInput = new()
            {
                panelId = organizationId,
                description = "description",
                fields = "{}",
                name = "name",
                serviceItemAgreedFeeIds = new List<string?>
                {
                    serviceItemAgreedFeeId
                }
            };
            string id = await PanelProviderTier.Create(createInput);

            updatePanelProviderTierInput updateInput = new()
            {
                description = "updated-description",
                name = "updated-name",
            };
            await PanelProviderTier.Update(id, updateInput);

            panelProviderTier? panelProviderTier = await PanelProviderTier.Search(new panelProviderTierWhereInput { id = id });

            panelProviderTier!.description.Should().Be(updateInput.description);
            panelProviderTier.name.Should().Be(updateInput.name);
        }

        [Fact]
        public async Task GIVEN_panelProviderTier_added_WHEN_delete_this_panelProviderTier_THEN_succeed_and_deleted()
        {
            string id = await PanelProviderTier.Create(new createPanelProviderTierInput());

            await PanelProviderTier.Delete(id);

            panelProviderTier? panelProviderTier = await PanelProviderTier.Search(new panelProviderTierWhereInput { id = id });
            panelProviderTier.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_panelProviderTier_added_WHEN_add_serviceItemAgreedFees_THEN_succeed_and_added()
        {
            string organizationId = await Entity.CreateOrganization(new createOrganizationInput());

            createPanelProviderTierInput createInput = new()
            {
                panelId = organizationId,
                description = "description",
                fields = "{}",
                name = "name"
            };
            string id = await PanelProviderTier.Create(createInput);

            string serviceItemId = await ServiceItem.Create(new createServiceItemInput { name = "name" });
            string serviceItemAgreedFeeId = await ServiceItemAgreedFee.Create(new createServiceItemAgreedFeeInput { serviceItemId = serviceItemId, rate = 10, currency = "USD" });

            await PanelProviderTier.AddServiceItemAgreedFee(id, serviceItemAgreedFeeId);

            panelProviderTier? panelProviderTier = await PanelProviderTier.Search(new panelProviderTierWhereInput { id = id });
            panelProviderTier!.serviceItemAgreedFees.Should().HaveCount(1);
            panelProviderTier.serviceItemAgreedFees!.First()!.id.Should().Be(serviceItemAgreedFeeId);
        }

        [Fact]
        public async Task GIVEN_panelProviderTier_added_WHEN_remove_serviceItemAgreedFees_THEN_succeed_and_removed()
        {
            string organizationId = await Entity.CreateOrganization(new createOrganizationInput());
            string serviceItemId = await ServiceItem.Create(new createServiceItemInput { name = "name" });
            string serviceItemAgreedFeeId = await ServiceItemAgreedFee.Create(new createServiceItemAgreedFeeInput { serviceItemId = serviceItemId, rate = 10, currency = "USD" });

            createPanelProviderTierInput createInput = new()
            {
                panelId = organizationId,
                description = "description",
                fields = "{}",
                name = "name",
                serviceItemAgreedFeeIds = new List<string?>
                {
                    serviceItemAgreedFeeId
                }
            };
            string id = await PanelProviderTier.Create(createInput);

            await PanelProviderTier.RemoveServiceItemAgreedFee(id, serviceItemAgreedFeeId);

            panelProviderTier? panelProviderTier = await PanelProviderTier.Search(new panelProviderTierWhereInput { id = id });
            panelProviderTier!.serviceItemAgreedFees.Should().HaveCount(0);
        }
    }
}

