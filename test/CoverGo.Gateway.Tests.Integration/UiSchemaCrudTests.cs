﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class UiSchemaCrudTests : TestsBase
    {
        public UiSchemaCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_uiSchema_WHEN_create_THEN_returns_Id()
        {
            (string id, _) = await UiSchema.Create();

            id.Should().NotBeNull();
        }

        [Fact]
        public async Task GIVEN_uiSchema_added_WHEN_request_this_uiSchema_THEN_returns_uiSchema()
        {
            (string id, createUiSchemaInput input) = await UiSchema.Create();

            uiSchema uiSchema = (await UiSchema.Search(new uiSchemaWhereInput { id = id }))!;

            uiSchema.ShouldHaveSameValuesAsInput(input);
        }

        [Fact]
        public async Task GIVEN_uiSchema_added_WHEN_requested_with_Name_THEN_returns_uiSchema()
        {
            string uiSchemaName = Guid.NewGuid().ToString();
            await UiSchema.Create(uiSchemaName);

            uiSchema uiSchema = (await UiSchema.Search(new uiSchemaWhereInput { name = uiSchemaName }))!;

            uiSchema.name.Should().Be(uiSchemaName);
        }

        [Fact]
        public async Task GIVEN_uiSchema_added_WHEN_requested_In_UiSchemaNames_THEN_returns_uiSchema()
        {
            string uiSchemaName1 = Guid.NewGuid().ToString();
            string uiSchemaName2 = Guid.NewGuid().ToString();
            List<string> uiSchemaNames = new() { uiSchemaName1, uiSchemaName2 };
            await UiSchema.Create(uiSchemaName1);

            uiSchema uiSchema = (await UiSchema.Search(new uiSchemaWhereInput { name_in = uiSchemaNames! }))!;

            uiSchema.name.Should().BeOneOf(uiSchemaNames);
        }

        [Fact]
        public async Task GIVEN_dataSchema_added_WHEN_update_this_dataSchema_THEN_succeed_and_updated()
        {
            (string id, _) = await UiSchema.Create();

            updateUiSchemaInput updateUiSchemaInput = new()
            {
                id = id,
                name = Guid.NewGuid().ToString(),
                schema = @" {  ""field1"": ""Value1"" }".Escape(),
                standard = new uiSchemaStandardInputGraphType
                {
                    type = uiSchemaStandardTypeEnum.JSON_SCHEMA,
                    version = Guid.NewGuid().ToString(),
                },
            };
            const string expectedFields = @"{""field1"":""Value1""}";
            await UiSchema.Update(updateUiSchemaInput);
            uiSchema? uiSchema = await UiSchema.Search(new uiSchemaWhereInput { id = id });
            uiSchema.Should().NotBeNull();
            uiSchema!.schema!.RemoveWhitespaces().Should().Be(expectedFields);
            uiSchema!.id!.Should().Be(updateUiSchemaInput.id);
            uiSchema!.standard!.ShouldHaveSameValuesAsInput(updateUiSchemaInput.standard);
        }

        [Fact]
        public async Task GIVEN_uiSchema_added_WHEN_delete_this_uiSchema_THEN_succeed_and_deletes()
        {
            (string id, _) = await UiSchema.Create();

            deleteUiSchemaInput deleteUiSchemaInput = new() { uiSchemaId = id };
            await UiSchema.Delete(deleteUiSchemaInput);

            uiSchema noUiSchema = (await UiSchema.Search(new uiSchemaWhereInput { id = id }))!;
            noUiSchema.Should().BeNull();
        }
    }
}

