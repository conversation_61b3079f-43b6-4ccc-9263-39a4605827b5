using CoverGo.Gateway.Client;
using CoverGo.Gateway.Domain.Policies;
using GraphQL.Client.Http;
using System;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class Endorsement : CrudBase
    {
        public Endorsement(GraphQLHttpClient client) : base(client) { }

        public async Task<string> Add(
            string policyId,
            string? type = null,
            string? reasonOfChange = null,
            DateTime? effectiveDate = null,
            string? cancellationMotive = null,
            endorsementSource? source = null,
            bool? isDraft = null,
            memberMovementVersions? memberMovementVersions = null,
            underwritingStatus? underwritingStatus = null)
        {
            createdStatusResult result = await TryAdd(policyId, type, reasonOfChange, effectiveDate, cancellationMotive, source, isDraft, memberMovementVersions, underwritingStatus);
            return result.TryGetId();
        }

        public Task<createdStatusResult> TryAdd(
            string policyId,
            string? type = null,
            string? reasonOfChange = null,
            DateTime? effectiveDate = null,
            string? cancellationMotive = null,
            endorsementSource? source = null,
            bool? isDraft = null,
            memberMovementVersions? memberMovementVersions = null,
            underwritingStatus? underwritingStatus = null)
        {
            string mutation = new MutationBuilder()
                .addEndorsement(
                    new MutationBuilder.addEndorsementArgs(
                        policyId,
                        type,
                        reasonOfChange,
                        effectiveDate,
                        cancellationMotive,
                        source,
                        isDraft,
                        memberMovementVersions,
                        underwritingStatus
                    ),
                    new createdStatusResultBuilder().WithAllFields()
                )
                .Build();

            return _client.SendMutationAsync<createdStatusResult>(mutation);
        }
        public Task Accept(string policyId, string endorsementId)
        {
            string mutation = new MutationBuilder()
                .acceptEndorsement(new MutationBuilder.acceptEndorsementArgs(policyId, endorsementId), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task<result> TryAccept(string policyId, string endorsementId)
        {
            string mutation = new MutationBuilder()
                .acceptEndorsement(new MutationBuilder.acceptEndorsementArgs(policyId, endorsementId), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAsync<result>(mutation);
        }

        public Task Reject(string policyId, string endorsementId)
        {
            string mutation = new MutationBuilder()
                .rejectEndorsement(new MutationBuilder.rejectEndorsementArgs(policyId, endorsementId), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task Update(string policyId, string endorsementId, updateEndorsementInput input)
        {
            string mutation = new MutationBuilder()
                .updateEndorsement(new MutationBuilder.updateEndorsementArgs(policyId, endorsementId, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task<result> TryUpdate(string policyId, string endorsementId, updateEndorsementInput input)
        {
            string mutation = new MutationBuilder()
                .updateEndorsement(new MutationBuilder.updateEndorsementArgs(policyId, endorsementId, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAsync<result>(mutation);
        }
    }
}