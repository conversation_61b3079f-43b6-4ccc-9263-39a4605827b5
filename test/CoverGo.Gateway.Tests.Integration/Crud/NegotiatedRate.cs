using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class NegotiatedRate : CrudBase
    {
        public NegotiatedRate(GraphQLHttpClient client) : base(client) { }


        public async Task NegotiatedItemsBatch(users_GenericNegotiatedItem3BatchInput batch)
        {
            string mutation = new MutationBuilder()
                .negotiatedItemMutationBatch(new MutationBuilder.negotiatedItemMutationBatchArgs(batch: batch), new users_ResultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task AgreedFeesBatch(users_GenericAgreedFee3BatchInput batch)
        {
            string mutation = new MutationBuilder()
                .agreedFeeMutationBatch(new MutationBuilder.agreedFeeMutationBatchArgs(batch: batch), new users_ResultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task AgreedFeeGroupsBatch(users_GenericAgreedFeeGroup3BatchInput batch)
        {
            string mutation = new MutationBuilder()
                .agreedFeeGroupMutationBatch(new MutationBuilder.agreedFeeGroupMutationBatchArgs(batch: batch), new users_ResultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task OrganizationNegotiatedItemsBatch(users_GenericOrganizationNegotiatedItem3BatchInput batch)
        {
            string mutation = new MutationBuilder()
                .organizationNegotiatedItemMutationBatch(new MutationBuilder.organizationNegotiatedItemMutationBatchArgs(batch: batch), new users_ResultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }


        public async Task OrganizationProviderTiersBatch(users_GenericOrganizationProviderTier3BatchInput batch)
        {
            string mutation = new MutationBuilder()
                .organizationProviderTierMutationBatch(new MutationBuilder.organizationProviderTierMutationBatchArgs(batch: batch), new users_ResultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }
    }
}