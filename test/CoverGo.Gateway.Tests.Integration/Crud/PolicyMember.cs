using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class PolicyMember : CrudBase
    {
        public PolicyMember(GraphQLHttpClient client) : base(client) { }

        public Task Batch(string policyId, string endorsementId, policyMembersBatchInput batchInput)
        {
            var mutation = new MutationBuilder()
                .policyMembersBatch(
                    new MutationBuilder.policyMembersBatchArgs(policyId, batchInput, endorsementId), new resultBuilder()
                        .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }
    }
}