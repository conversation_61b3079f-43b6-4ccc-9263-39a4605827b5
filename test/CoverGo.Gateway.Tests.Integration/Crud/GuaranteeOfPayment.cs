using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class GuaranteeOfPayment : CrudBase
    {
        public GuaranteeOfPayment(GraphQLHttpClient client) : base(client) { }

        public async Task<Guid> Create(createGOPInput input)
        {
            string mutation = new MutationBuilder()
                .createGOP(new MutationBuilder.createGOPArgs(input), new createdStatusResultBuilder()
                    .status()
                    .errors()
                    .createdStatus(new createdStatusBuilder()
                        .id()))
                .Build();
            string createId = await _client.CreateAndReturnId(mutation);
            Guid.TryParse(createId, out Guid gopId);
            return gopId;
        }

        public async Task Update(Guid gopId, updateGOPInput input)
        {
            string mutation = new MutationBuilder()
                .updateGOP(new MutationBuilder.updateGOPArgs(gopId, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task Delete(Guid gopId)
        {
            string mutation = new MutationBuilder()
                .deleteGOP(new MutationBuilder.deleteGOPArgs(gopId), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task Reject(Guid gopId, rejectGOPInput input)
        {
            string mutation = new MutationBuilder()
                .rejectGOP(new MutationBuilder.rejectGOPArgs(gopId, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task Approve(Guid gopId, approveGOPInput input)
        {
            string mutation = new MutationBuilder()
                .approveGOP(new MutationBuilder.approveGOPArgs(gopId, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task AddNote(Guid gopId, addNoteInput input)
        {
            string mutation = new MutationBuilder()
                .addGOPNote(new MutationBuilder.addGOPNoteArgs(gopId, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task UpdateNote(Guid gopId, updateNoteInput input)
        {
            string mutation = new MutationBuilder()
                .updateGOPNote(new MutationBuilder.updateGOPNoteArgs(gopId, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task RemoveNote(Guid gopId, Guid noteId)
        {
            string mutation = new MutationBuilder()
                .removeGOPNote(new MutationBuilder.removeGOPNoteArgs(gopId, noteId), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task AddAttachment(Guid gopId, attachmentInput input)
        {
            string mutation = new MutationBuilder()
                .addGOPAttachment(new MutationBuilder.addGOPAttachmentArgs(gopId, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task RemoveAttachment(Guid gopId, string path)
        {
            string mutation = new MutationBuilder()
                .removeGOPAttachment(new MutationBuilder.removeGOPAttachmentArgs(gopId, path), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task<List<guaranteeOfPayment>> Query(gopWhereInput? input = null)
        {
            string query = new QueryBuilder()
                .gops(new QueryBuilder.gopsArgs(where: input), new guaranteeOfPaymentsBuilder()
                    .list(new guaranteeOfPaymentBuilder()
                        .id()
                        .notes(new noteBuilder()
                            .id()
                            .title()
                            .content())
                    ))
                .Build();

            guaranteeOfPayments guaranteeOfPayments = await _client.SendQueryAsync<guaranteeOfPayments>(query);
            return guaranteeOfPayments.list!.ToList()!;
        }
    }
}