using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Web;
using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class FileSystem : CrudBase
    {
        public FileSystem(GraphQLHttpClient client) : base(client)
        {
        }

        public async Task Upload(string path, string localPath, int? ttlSeconds = default, HttpClient? client = null)
        {
            using var multipartFormContent = new MultipartFormDataContent();
            using var fileStreamContent = new StreamContent(File.OpenRead(localPath));
            fileStreamContent.Headers.ContentType = GetContentType(localPath);
            multipartFormContent.Add(fileStreamContent, name: "file", fileName: Path.GetFileName(path));
            var pathWithoutFileName = Path.GetDirectoryName(path).Replace(@"\", "/");
            client ??= _client.HttpClient;
            var response = await client.PostAsync(
                $"api/v1/files/{HttpUtility.UrlEncode(pathWithoutFileName)}?ttlSeconds={ttlSeconds}",
                multipartFormContent);
            response.EnsureSuccessStatusCode();
        }

        public async Task<HttpResponseMessage> DownloadWithRoute(string filePath)
        {
            HttpClient httpClient = _client.HttpClient;
            var response = await httpClient.GetAsync($"api/v1/files/{WebUtility.UrlEncode(filePath)}?tenantIdIfNoToken=covergo");

            return response;
        }

        public async Task<HttpResponseMessage> DownloadWithQuery(string filePath)
        {
            HttpClient httpClient = _client.HttpClient;
            var response = await httpClient.GetAsync($"api/v1/files?key={WebUtility.UrlEncode(filePath)}&tenantIdIfNoToken=covergo");

            return response;
        }

        public async Task InitializeTenant(initializeTenantFileSystemInput? initializeTenantFileSystemInput = default)
        {
            var input = initializeTenantFileSystemInput ?? new initializeTenantFileSystemInput()
            {
                config = new fileSystemConfigInput()
                {
                    providerId = "local",
                    accessKeyId = "-",
                    accessKeySecret = "-",
                    bucketName = "-",
                    endpoint = "-"
                }
            };
            var mutation = new MutationBuilder()
                        .initializeFileSystem(new MutationBuilder.initializeFileSystemArgs(GraphQLClientConfig.Local.TenantId, input), new resultBuilder()
                            .WithAllFields());
            await _client.SendMutationAsync<result>(mutation);
        }
        public Task<result> CopyFiles(string[] keys, string[] newKeys)
        {
            var copyFileInputs = new List<copyFileInput>();

            for (var i = 0; i < keys.Length; i++)
            {
                copyFileInputs.Add(new copyFileInput()
                {
                    key = keys[i],
                    newKey = newKeys[i]
                });
            }
            var mutation = new MutationBuilder()
                .copyFiles(new MutationBuilder.copyFilesArgs(copyFileInputs),
                    new resultBuilder().WithAllFields());
            return _client.SendMutationAsync<result>(mutation);
        }

        public async Task<fileSummary?> GetFile(string fileName, string? tenantId = null)
        {
            var query = new QueryBuilder().fileListing(
                new QueryBuilder.fileListingArgs(prefix: fileName, tenantId: tenantId),
                new fileListingBuilder().WithAllFields());

            var result = await _client.SendQueryAsync<fileListing>(query);

            return result.objectSummaries?.FirstOrDefault();
        }

        public Task<ICollection<fileSystemConfig?>> GetFileSystemConfigs()
        {
            var query = new QueryBuilder().fileSystemConfigs(
                new QueryBuilder.fileSystemConfigsArgs(),
                new fileSystemConfigBuilder()
                    .WithAllFields());
            return _client.SendQueryAsync<ICollection<fileSystemConfig?>>(query);
        }

        public Task CreateFileSystemConfig(fileSystemConfigInput input)
        {
            string mutation = new MutationBuilder()
                .createFileSystemConfig(new MutationBuilder.createFileSystemConfigArgs(input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task<result> LockFile(string key)
        {
            var mutation = new MutationBuilder().lockFile(
                new MutationBuilder.lockFileArgs(new[] { key }),
                new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAsync<result>(mutation);
        }

        public Task<result> UnlockFile(string key)
        {
            var mutation = new MutationBuilder().unlockFile(
                    new MutationBuilder.unlockFileArgs(new[] { key }),
                    new resultBuilder()
                        .WithAllFields())
                .Build();

            return _client.SendMutationAsync<result>(mutation);
        }

        public Task<result> DeleteFile(string key)
        {
            var mutation = new MutationBuilder().deleteFile(
                new MutationBuilder.deleteFileArgs(key),
                new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAsync<result>(mutation);
        }

        private MediaTypeHeaderValue? GetContentType(string localPath) =>
            Path.GetExtension(localPath) switch
            {
                ".txt" => new MediaTypeHeaderValue("text/plain"),
                _ => null
            };
    }
}