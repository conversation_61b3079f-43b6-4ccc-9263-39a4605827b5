using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class PanelProviderTier : CrudBase
    {
        public PanelProviderTier(GraphQLHttpClient client) : base(client) { }

        public async Task<string> Create(createPanelProviderTierInput input)
        {
            string mutation = new MutationBuilder()
                .createPanelProviderTier(new MutationBuilder.createPanelProviderTierArgs(input), new createdStatusResultBuilder()
                    .createdStatus(new createdStatusBuilder()
                        .id()))
                .Build();

            return await _client.CreateAndReturnId(mutation);
        }

        public async Task Update(string panelProviderTierId, updatePanelProviderTierInput input)
        {
            string mutation = new MutationBuilder()
                .updatePanelProviderTier(new MutationBuilder.updatePanelProviderTierArgs(panelProviderTierId, input), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task Delete(string panelProviderTierId)
        {
            string mutation = new MutationBuilder()
                .deletePanelProviderTier(new MutationBuilder.deletePanelProviderTierArgs(panelProviderTierId), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task AddServiceItemAgreedFee(string panelProviderTierId, string serviceItemAgreeFeeId)
        {
            string mutation = new MutationBuilder()
                .addPanelProviderTierServiceItemAgreedFee(new MutationBuilder.addPanelProviderTierServiceItemAgreedFeeArgs(panelProviderTierId, serviceItemAgreeFeeId), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task RemoveServiceItemAgreedFee(string panelProviderTierId, string serviceItemAgreeFeeId)
        {
            string mutation = new MutationBuilder()
                .removePanelProviderTierServiceItemAgreedFee(new MutationBuilder.removePanelProviderTierServiceItemAgreedFeeArgs(panelProviderTierId, serviceItemAgreeFeeId), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task ServiceItemAgreedFeeBatch(string panelProviderTierId, serviceItemAgreedFeeBatchInput input)
        {
            string mutation = new MutationBuilder()
                .panelProviderTierServiceItemAgreedFeeBatch(new MutationBuilder.panelProviderTierServiceItemAgreedFeeBatchArgs(panelProviderTierId, input), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task AddAttachment(string panelProviderTierId, attachmentInput input)
        {
            string mutation = new MutationBuilder()
                .addPanelProviderTierAttachment(new MutationBuilder.addPanelProviderTierAttachmentArgs(panelProviderTierId, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task RemoveAttachment(string panelProviderTierId, string path)
        {
            string mutation = new MutationBuilder()
                .removePanelProviderTierAttachment(new MutationBuilder.removePanelProviderTierAttachmentArgs(panelProviderTierId, path), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task<panelProviderTier?> Search(panelProviderTierWhereInput where)
        {
            string query = new QueryBuilder()
                .panelProviderTiers(new QueryBuilder.panelProviderTiersArgs(where: where), new panelProviderTiersBuilder()
                    .list(new panelProviderTierBuilder()
                        .id()
                        .name()
                        .description()
                        .fields()
                        .serviceItemAgreedFees(new serviceItemAgreedFeeBuilder()
                            .id()
                            .currency()
                            .rate()
                            .serviceItem(new serviceItemBuilder()
                                .id()
                                .name()
                                .description()))))
                .Build();

            panelProviderTiers tiers = await _client.SendQueryAsync<panelProviderTiers>(query);
            return tiers.list?.SingleOrDefault();
        }

        public async Task<IEnumerable<panelProviderTier?>?> SearchMany(panelProviderTierWhereInput where)
        {
            string query = new QueryBuilder()
                .panelProviderTiers(new QueryBuilder.panelProviderTiersArgs(where: where), new panelProviderTiersBuilder()
                    .list(new panelProviderTierBuilder()
                        .id()
                        .name()
                        .description()
                        .serviceItemAgreedFees(new serviceItemAgreedFeeBuilder()
                            .id()
                            .currency()
                            .rate()
                            .serviceItem(new serviceItemBuilder()
                                .id()
                                .name()
                                .description()))))
                .Build();

            panelProviderTiers tiers = await _client.SendQueryAsync<panelProviderTiers>(query);
            return tiers.list?.DefaultIfEmpty();
        }
    }
}