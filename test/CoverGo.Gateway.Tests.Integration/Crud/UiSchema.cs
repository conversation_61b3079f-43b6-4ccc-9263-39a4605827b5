﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System;
using System.Linq;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public sealed class UiSchema : CrudBase
    {
        public UiSchema(GraphQLHttpClient client) : base(client) { }

        public async Task<(string id, createUiSchemaInput input)> Create(string? uiSchemaName = null)
        {
            createUiSchemaInput input = new()
            {
                name = uiSchemaName ?? Guid.NewGuid().ToString(),
                schema = "{}",
                standard = new uiSchemaStandardInputGraphType
                {
                    type = uiSchemaStandardTypeEnum.JSON_SCHEMA,
                    version = Guid.NewGuid().ToString()
                },
            };
            return (await Create(input), input);
        }

        public async Task<string> Create(createUiSchemaInput input)
        {
            string mutation = new MutationBuilder()
                .createUiSchema(new MutationBuilder.createUiSchemaArgs(input), new createdStatusResultBuilder()
                    .createdStatus(new createdStatusBuilder()
                        .id()))
                .Build();

            return await _client.CreateAndReturnId(mutation);
        }

        public async Task Update(updateUiSchemaInput input)
        {
            string mutation = new MutationBuilder()
                .updateUiSchema(new MutationBuilder.updateUiSchemaArgs(input), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAsync<result>(mutation);
        }

        public async Task Delete(deleteUiSchemaInput input)
        {
            string mutation = new MutationBuilder()
                .deleteUiSchema(new MutationBuilder.deleteUiSchemaArgs(input), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAsync<result>(mutation);
        }

        public async Task<uiSchema?> Search(uiSchemaWhereInput where)
        {
            string query = new QueryBuilder()
                .uiSchemas(new QueryBuilder.uiSchemasArgs(where: where), new uiSchemasBuilder()
                    .list(new uiSchemaBuilder()
                        .id()
                        .name()
                        .schema()
                        .standard(new uiSchemaStandardGraphTypeBuilder()
                            .type()
                            .version())))
                .Build();

            uiSchemas uiSchemas = await _client.SendQueryAsync<uiSchemas>(query);
            return uiSchemas.list?.SingleOrDefault();
        }
    }
}