using System;
using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class Token : CrudBase
    {
        public Token(GraphQLHttpClient client) : base(client) { }

        public async Task<string> Fetch(QueryBuilder.token_2Args tokenArgs)
        {
            string query = new QueryBuilder()
                .token_2(tokenArgs, new tokenBuilder()
                    .error()
                    .errorDescription()
                    .accessToken())
                .Build();

            token token = await _client.SendQueryAsync<token>(query);

            if (token.error != null)
                throw new Exception(token.errorDescription);

            return token.accessToken!;
        }
    }
}