﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class Entity : CrudBase
    {
        public Entity(GraphQLHttpClient client) : base(client) { }

        public Task<string> CreateIndividual(createIndividualInput input)
        {
            string mutation = new MutationBuilder()
                .createIndividual(new MutationBuilder.createIndividualArgs(input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            return _client.CreateAndReturnId(mutation);
        }

        public Task<string> CreateInternal(createInternalInput input)
        {
            string mutation = new MutationBuilder()
                .createInternal(new MutationBuilder.createInternalArgs(input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            return _client.CreateAndReturnId(mutation);
        }

        public Task<string> CreateCompany(createCompanyInput input)
        {
            string mutation = new MutationBuilder()
                .createCompany(new MutationBuilder.createCompanyArgs(input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            return _client.CreateAndReturnId(mutation);
        }

        public Task<string> CreateOrganization(createOrganizationInput input)
        {
            string mutation = new MutationBuilder()
                .createOrganization(new MutationBuilder.createOrganizationArgs(input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            return _client.CreateAndReturnId(mutation);
        }

        public Task<string> CreateObject(createObjectInput input)
        {
            string mutation = new MutationBuilder()
                .createObject(new MutationBuilder.createObjectArgs(input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            return _client.CreateAndReturnId(mutation);
        }

        public Task<string> CreateIndividual() =>
            CreateIndividual(new createIndividualInput { email = CreateNewGuid() + "@covergo.com" });

        public Task<string> CreateInternal() =>
            CreateInternal(new createInternalInput { email = CreateNewGuid() + "@covergo.com" });

        public Task<string> CreateCompany() =>
            CreateCompany(new createCompanyInput { email = CreateNewGuid() + "@covergo.com" });

        public Task<string> CreateOrganization() =>
            CreateOrganization(new createOrganizationInput { type = CreateNewGuid() });

        public Task<string> CreateObject() =>
            CreateObject(new createObjectInput { email = CreateNewGuid() + "@covergo.com" });

        public Task UpdateIndividual(string id, updateIndividualInput input)
        {
            string mutation = new MutationBuilder()
                .updateIndividual(new MutationBuilder.updateIndividualArgs(id, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task UpdateInternal(string id, updateInternalInput input)
        {
            string mutation = new MutationBuilder()
                .updateInternal(new MutationBuilder.updateInternalArgs(id, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task UpdateCompany(string id, updateCompanyInput input)
        {
            string mutation = new MutationBuilder()
                .updateCompany(new MutationBuilder.updateCompanyArgs(id, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task UpdateOrganization(string id, updateOrganizationInput input)
        {
            string mutation = new MutationBuilder()
                .updateOrganization(new MutationBuilder.updateOrganizationArgs(id, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task UpdateObject(string id, updateObjectInput input)
        {
            string mutation = new MutationBuilder()
                .updateObject(new MutationBuilder.updateObjectArgs(id, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task<individuals> FindIndividual(individualWhereInput where)
        {
            string query = new QueryBuilder()
                .individuals(new QueryBuilder.individualsArgs(where: where, checkActivity: true), new individualsBuilder()
                    .list(new individualBuilder()
                        .tags()
                        .internalCode()
                        .status()
                        .hasActivePolicy()
                        .WithAllScalarFields()))
                .Build();

            return _client.SendQueryAsync<individuals>(query);
        }

        public Task<internals> FindInternal(internalWhereInput where)
        {
            string query = new QueryBuilder()
                .internals(new QueryBuilder.internalsArgs(where: where), new internalsBuilder()
                    .list(new internalBuilder()
                        .tags()
                        .status()
                        .WithAllScalarFields()))
                .Build();

            return _client.SendQueryAsync<internals>(query);
        }

        public Task<companies> FindCompany(companyWhereInput where, string? specialty = null)
        {
            string query = new QueryBuilder()
                .companies(new QueryBuilder.companiesArgs(where: where, specialty: specialty), new companiesBuilder()
                    .list(new companyBuilder()
                        .tags()
                        .status()
                        .relationships(new companyBuilder.relationshipsArgs(), new relationshipBuilder()
                            .entity(new entityInterfaceBuilder()
                                .entityType()))
                        .WithAllScalarFields()))
                .Build();

            return _client.SendQueryAsync<companies>(query);
        }

        public Task<organizations> FindOrganization(organizationWhereInput where)
        {
            string query = new QueryBuilder()
                .organizations(new QueryBuilder.organizationsArgs(where: where), new organizationsBuilder()
                    .list(new organizationBuilder()
                        .tags()
                        .status()
                        .WithAllScalarFields()))
                .Build();

            return _client.SendQueryAsync<organizations>(query);
        }

        public Task<objects> FindObject(objectWhereInput where)
        {
            string query = new QueryBuilder()
                .objects(new QueryBuilder.objectsArgs(where: where), new objectsBuilder()
                    .list(new objectBuilder()
                        .tags()
                        .status()
                        .WithAllScalarFields()))
                .Build();

            return _client.SendQueryAsync<objects>(query);
        }

        public async Task<IReadOnlyCollection<string?>?> FindAssociatedLoginIds(individualWhereInput where)
        {
            IReadOnlyCollection<login?>? logins = await FindAssociatedLogins(where);
            return logins!.Select(l => l!.id).ToArray();
        }

        public async Task<IReadOnlyCollection<login?>?> FindAssociatedLogins(individualWhereInput where)
        {
            string query = new QueryBuilder()
                .individuals(new QueryBuilder.individualsArgs(where: where), new individualsBuilder()
                    .list(new individualBuilder()
                        .id()
                        .associatedLogin(new loginBuilder()
                            .id()
                            .email()
                            .username()
                            .isEmailConfirmed())))
                .Build();

            individuals response = await _client.SendQueryAsync<individuals>(query);

            return response.list?.Select(l => l?.associatedLogin).ToArray();
        }



        public Task<string> CreateWithAssociatedLogin(string name, string? password = null)
        {
            registerIndividualInput input = new()
            {
                englishFirstName = name,
                username = name,
                isEmailConfirmed = true,
                password = password ?? CreateNewGuid(),
                email = $"{name}@covergo.com"
            };
            string mutation = new MutationBuilder()
                .registerIndividual(new MutationBuilder.registerIndividualArgs("covergo", "admin", input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            return _client.CreateAndReturnId(mutation);
        }

        public Task<string> AddLink(string sourceId, string targetId, string linkType)
        {
            createLinkInput input = new()
            {
                sourceId = sourceId,
                targetId = targetId,
                link = linkType
            };
            string mutation = new MutationBuilder()
                .addLink(new MutationBuilder.addLinkArgs(input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            return _client.CreateAndReturnId(mutation);
        }


        public Task AddDisabilityToIndividual(string disabilityId, string individualId)
        {
            string mutation = new MutationBuilder()
                .addDisabilityToIndividual(
                    new MutationBuilder.addDisabilityToIndividualArgs(disabilityId, individualId),
                    new resultBuilder()
                        .status())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task RemoveDiagnosisFromDisability(string disabilityId, string individualId)
        {
            string mutation = new MutationBuilder()
                .removeDisabilityFromIndividual(
                    new MutationBuilder.removeDisabilityFromIndividualArgs(disabilityId, individualId),
                    new resultBuilder()
                        .status())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task IndividualDisabilityBatch(string individualId, individualDisabilityBatchInput input)
        {
            string mutation = new MutationBuilder()
                .individualDisabilityBatch(
                    new MutationBuilder.individualDisabilityBatchArgs(individualId, input),
                    new resultBuilder()
                        .status())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }
    }
}