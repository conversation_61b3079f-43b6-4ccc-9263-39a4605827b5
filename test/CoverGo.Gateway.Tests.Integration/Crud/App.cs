using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using GraphQL.Client.Http;

namespace CoverGo.Gateway.Tests.Integration.Crud;

public class App : CrudBase
{
    public App(GraphQLHttpClient client) : base(client) { }

    public async Task<string> Create()
    {
        string appId = CreateNewGuid();
        string mutation = new MutationBuilder()
            .createApp(new MutationBuilder.createAppArgs(new createAppInput
            {
                appId = appId,
                appName = CreateNewGuid(),
                redirectUris = new[] { $"http://test.test/{appId}" }
            }), new resultBuilder()
                .WithAllFields())
            .Build();

        await _client.SendMutationAndEnsureSuccessAsync(mutation);

        return appId;
    }
}