using System;
using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class Template : CrudBase
    {
        public Template(GraphQLHttpClient client)
            : base(client)
        {
        }

        public Task<string> CreateDynamicTemplate(createDynamicTemplateInput input)
            => _client.CreateAndReturnId(new MutationBuilder()
                .createDynamicTemplate(new MutationBuilder.createDynamicTemplateArgs(input),
                    new createdStatusResultBuilder().WithAllFields()).Build());

        public async Task<string> CreateClauseHtmlTemplate(createClauseHtmlTemplateInput input)
        {
            string mutation = new MutationBuilder()
                .createClauseHtmlTemplate(new MutationBuilder.createClauseHtmlTemplateArgs(input), new createdStatusResultBuilder()
                    .status()
                    .errors()
                    .createdStatus(new createdStatusBuilder()
                        .id()))
                .Build();

            return await _client.CreateAndReturnId(mutation);
        }

        public async System.Threading.Tasks.Task UpdateClauseHtmlTemplate(string templateId, updateClauseHtmlTemplateInput input)
        {
            string mutation = new MutationBuilder()
                .updateClauseHtmlTemplate(new MutationBuilder.updateClauseHtmlTemplateArgs(templateId, input), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task<string> CreateWkhtmltopdfTemplate(createWkhtmltopdfTemplateInput input)
        {
            string mutation = new MutationBuilder()
                .createWkhtmltopdfTemplate(new MutationBuilder.createWkhtmltopdfTemplateArgs(input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            return _client.CreateAndReturnId(mutation);
        }

        public async System.Threading.Tasks.Task UpdateWkhtmltopdfTemplate(string templateId, updateWkhtmltopdfTemplateInput input)
        {
            string mutation = new MutationBuilder()
                .updateWkhtmltopdfTemplate(new MutationBuilder.updateWkhtmltopdfTemplateArgs(templateId, input), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task<string> AddPageObject(string templateId, addPageObjectToWkhtmltopdfTemplateInput input)
        {
            string mutation = new MutationBuilder()
                .addPageObjectToWkhtmltopdfTemplate(new MutationBuilder.addPageObjectToWkhtmltopdfTemplateArgs(templateId, input), new stringResultBuilder()
                    .status()
                    .value()
                    .errors())
                .Build();

            stringResult result = await _client.SendMutationAsync<stringResult>(mutation);

            if (result.value == null)
                throw new Exception("created id equals null");
            if (result.errors?.Any() == true)
                throw new Exception(result.errors.First());

            return result.value;
        }

        public async System.Threading.Tasks.Task UpdatePageObject(string templateId, updatePageObjectOfWkhtmltopdfTemplateInput input)
        {
            string mutation = new MutationBuilder()
                .updatePageObjectOfWkhtmltopdfTemplate(new MutationBuilder.updatePageObjectOfWkhtmltopdfTemplateArgs(templateId, input), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async System.Threading.Tasks.Task RemovePageObject(string templateId, string pageObjectId)
        {
            string mutation = new MutationBuilder()
                .removePageObjectFromWkhtmltopdfTemplate(new MutationBuilder.removePageObjectFromWkhtmltopdfTemplateArgs(templateId, pageObjectId), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task<pdfRenderedResult> RenderWkhtmltopdfTemplate(string templateId, templateRenderParametersInput input)
        {
            string query = new QueryBuilder()
                .renderWkhtmltopdfTemplate(new QueryBuilder.renderWkhtmltopdfTemplateArgs(templateId, input), new pdfRenderedResultBuilder()
                    .status()
                    .value())
                .Build();

            return await _client.SendQueryAsync<pdfRenderedResult>(query);
        }

        public async Task<pdfRenderedResult> RenderWkhtmltopdf(string html, marginSettingsInput marginSettingsInput, headerFooterInput headerSettingsInput, headerFooterInput footerSettingsInput, orientationEnumeration? orientation = null, templateRenderParametersInput? input = null, string? password = null)
        {
            string query = new QueryBuilder()
                .renderWkhtmltopdf(new QueryBuilder.renderWkhtmltopdfArgs(html, marginSettingsInput, headerSettingsInput, footerSettingsInput, orientation, input, password), new pdfRenderedResultBuilder()
                    .status()
                    .value())
                .Build();

            return await _client.SendQueryAsync<pdfRenderedResult>(query);
        }

        public async Task<string> RenderWkhtmltopdfTemplateV2(string templateId, templateRenderParametersInput input, string outputFilePath)
        {
            string query = new QueryBuilder()
                .renderWkhtmltopdfTemplateV2(new QueryBuilder.renderWkhtmltopdfTemplateV2Args(templateId, outputFilePath, input), new stringResultBuilder()
                    .status()
                    .value())
                .Build();

            var result = await _client.SendQueryAsync<stringResult>(query);

            return result.value!;
        }

        public async Task<pdfRenderedResult> RenderWkhtmltopdf2(List<pageObjectInput> pageObjectInputs, marginSettingsInput marginSettingsInput)
        {
            string query = new QueryBuilder()
                .renderWkhtmltopdf2(new QueryBuilder.renderWkhtmltopdf2Args(pageObjectInputs, marginSettingsInput), new pdfRenderedResultBuilder()
                    .status()
                    .value())
                .Build();

            return await _client.SendQueryAsync<pdfRenderedResult>(query);
        }


        public System.Threading.Tasks.Task CreateSmsTemplate(createSmsTemplateInput input)
            => _client.SendMutationAndEnsureSuccessAsync(new MutationBuilder()
                .createSmsTemplate(new MutationBuilder.createSmsTemplateArgs(input),
                    new resultBuilder()
                        .status()
                        .errors())
                .Build());

        public System.Threading.Tasks.Task UpdateSmsTemplate(string templateId, updateSmsTemplateInput input)
            => _client.SendMutationAndEnsureSuccessAsync(new MutationBuilder()
                .updateSmsTemplate(new MutationBuilder.updateSmsTemplateArgs(templateId, input),
                    new resultBuilder()
                        .status()
                        .errors())
                .Build());

        public System.Threading.Tasks.Task CreateEmailMjmlTemplate(createEmailMjmlTemplateInput input)
            => _client.SendMutationAndEnsureSuccessAsync(new MutationBuilder()
                .createEmailMjmlTemplate(new MutationBuilder.createEmailMjmlTemplateArgs(input),
                    new resultBuilder()
                        .status()
                        .errors())
                .Build());

        public System.Threading.Tasks.Task UpdateEmailMjmlTemplate(string templateId, updateEmailMjmlTemplateInput input)
            => _client.SendMutationAndEnsureSuccessAsync(new MutationBuilder()
                .updateEmailMjmlTemplate(new MutationBuilder.updateEmailMjmlTemplateArgs(templateId, input),
                    new resultBuilder()
                        .status()
                        .errors())
                .Build());

        public System.Threading.Tasks.Task AddEmailAttachmentTemplate(string templateId, addEmailAttachmentTemplateInput input)
            => _client.SendMutationAndEnsureSuccessAsync(new MutationBuilder()
                .addEmailAttachmentTemplate(new MutationBuilder.addEmailAttachmentTemplateArgs(templateId, input),
                    new resultBuilder()
                        .status()
                        .errors())
                .Build());

        public System.Threading.Tasks.Task AddEmailAttachmentTemplate(string templateId, addEmailAttachmentReferenceInput input)
            => _client.SendMutationAndEnsureSuccessAsync(new MutationBuilder()
                .addEmailAttachmentReference(new MutationBuilder.addEmailAttachmentReferenceArgs(templateId, input),
                    new resultBuilder()
                        .status()
                        .errors())
                .Build());

        public Task<string> CreateFunctionTemplate(createFunctionTemplateInput input)
            => _client.CreateAndReturnId(new MutationBuilder()
                .createFunctionTemplate(new MutationBuilder.createFunctionTemplateArgs(input),
                    new createdStatusResultBuilder().WithAllFields()).Build());

        public System.Threading.Tasks.Task UpdateFunctionTemplate(string templateId, updateFunctionTemplateInput input)
            => _client.SendMutationAndEnsureSuccessAsync(new MutationBuilder()
                .updateFunctionTemplate(new MutationBuilder.updateFunctionTemplateArgs(templateId, input),
                    new resultBuilder()
                        .status()
                        .errors())
                .Build());

        public Task<string> CreateNotificationTemplate(createNotificationTemplateInput input)
            => _client.CreateAndReturnId(new MutationBuilder()
                .createNotificationTemplate(new MutationBuilder.createNotificationTemplateArgs(input),
                    new createdStatusResultBuilder().WithAllFields()).Build());

        public System.Threading.Tasks.Task UpdateNotificationTemplate(string templateId, updateNotificationTemplateInput input)
            => _client.SendMutationAndEnsureSuccessAsync(new MutationBuilder()
                .updateNotificationTemplate(new MutationBuilder.updateNotificationTemplateArgs(templateId, input),
                    new resultBuilder()
                        .status()
                        .errors())
                .Build());
        
        public async Task<emailRenderedResult> RenderEmailTemplate(string templateId, templateRenderParametersInput input, bool includeAttachments)
        {
            string query = new QueryBuilder()
                .renderEmailMjmlTemplate(new QueryBuilder.renderEmailMjmlTemplateArgs(templateId, input, includeAttachments), new emailRenderedResultBuilder()
                    .status()
                    .value(new emailRenderedBuilder()
                        .html()
                        .attachments(new emailAttachmentBuilder()
                            .fileName()
                            .bytes())))
                .Build();

            return await _client.SendQueryAsync<emailRenderedResult>(query);
        }
    }
}