﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System;
using System.Linq;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public sealed class ServiceItemAgreedFee : CrudBase
    {
        public ServiceItemAgreedFee(GraphQLHttpClient client) : base(client) { }

        public async Task<(string id, createServiceItemAgreedFeeInput input)> Create(string? serviceItemId = null, string? currency = null)
        {
            createServiceItemAgreedFeeInput input = new()
            {
                currency = currency ?? Guid.NewGuid().ToString(),
                rate = new Random().Next(),
                serviceItemId = serviceItemId ?? Guid.NewGuid().ToString()
            };
            return (await Create(input), input);
        }

        public async Task<string> Create(createServiceItemAgreedFeeInput input)
        {
            string mutation = new MutationBuilder()
                .createServiceItemAgreedFee(new MutationBuilder.createServiceItemAgreedFeeArgs(input), new createdStatusResultBuilder()
                    .createdStatus(new createdStatusBuilder()
                        .id()))
                .Build();

            return await _client.CreateAndReturnId(mutation);
        }

        public async Task Update(updateServiceItemAgreedFeeInput input)
        {
            string mutation = new MutationBuilder()
                .updateServiceItemAgreedFee(new MutationBuilder.updateServiceItemAgreedFeeArgs(input), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task Delete(deleteServiceItemAgreedFeeInput input)
        {
            string mutation = new MutationBuilder()
                .deleteServiceItemAgreedFee(new MutationBuilder.deleteServiceItemAgreedFeeArgs(input), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task<serviceItemAgreedFee?> Search(serviceItemAgreedFeeWhereInput where)
        {
            string query = new QueryBuilder()
                .serviceItemAgreedFee(new QueryBuilder.serviceItemAgreedFeeArgs(where: where), new serviceItemAgreedFeesBuilder()
                    .list(new serviceItemAgreedFeeBuilder()
                        .id()
                        .rate()
                        .currency()
                        .serviceItem(new serviceItemBuilder()
                            .id()
                            .name()
                            .description())))
                .Build();

            serviceItemAgreedFees fees = await _client.SendQueryAsync<serviceItemAgreedFees>(query);
            return fees.list?.SingleOrDefault();
        }
    }
}