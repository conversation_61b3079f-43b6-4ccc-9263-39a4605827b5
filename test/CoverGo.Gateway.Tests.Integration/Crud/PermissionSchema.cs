﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class PermissionSchema : CrudBase
    {
        public PermissionSchema(GraphQLHttpClient client) : base(client) { }

        public Task<string> Create(createPermissionSchemaInput input)
        {
            string mutation = new MutationBuilder()
                .createPermissionSchema(new MutationBuilder.createPermissionSchemaArgs(input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            return _client.CreateAndReturnId(mutation);
        }

        public async Task<(string id, createPermissionSchemaInput input)> Create()
        {
            createPermissionSchemaInput input = new()
            {
                name = CreateNewGuid(),
                objectType = CreateNewGuid(),
                actionType = permissionSchemaActionType.READ,
                description = CreateNewGuid(),
                schema = "",
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.EQUALS,
                    path = "foo.bar.bar",
                    value = new scalarValueInput { stringValue = CreateNewGuid() }
                },
                updateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.EQUALS,
                    path = "foo.bar.test.test",
                    value = new scalarValueInput { stringValue = CreateNewGuid() }
                }
            };

            return (await Create(input), input);
        }
    }
}