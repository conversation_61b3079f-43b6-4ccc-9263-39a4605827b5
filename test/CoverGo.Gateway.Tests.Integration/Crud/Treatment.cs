using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class Treatment : CrudBase
    {
        public Treatment(GraphQLHttpClient client) : base(client) { }

        public async Task<string> Create(treatmentInput input)
        {
            string mutation = new MutationBuilder()
                .createTreatment(new MutationBuilder.createTreatmentArgs(input), new createdStatusResultBuilder()
                    .status()
                    .errors()
                    .createdStatus(new createdStatusBuilder()
                        .id()))
                .Build();

            return await _client.CreateAndReturnId(mutation);
        }


        public async Task Update(string treatmentId, treatmentInput input)
        {
            string mutation = new MutationBuilder()
                .updateTreatment(new MutationBuilder.updateTreatmentArgs(treatmentId, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task Delete(string treatmentId)
        {
            string mutation = new MutationBuilder()
                .deleteTreatment(new MutationBuilder.deleteTreatmentArgs(treatmentId), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task Batch(treatmentBatchInput input)
        {
            string mutation = new MutationBuilder()
                .treatmentBatch(new MutationBuilder.treatmentBatchArgs(input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task<treatment?> FindById(string id)
        {
            string? query = new QueryBuilder().treatments(new QueryBuilder.treatmentsArgs(where: new treatmentFilterInput { where = new treatmentFilterWhereInput { id = id } }),
                    new treatmentsBuilder()
                        .list(new treatmentBuilder()
                            .id()
                            .name()
                            .category()
                            .code()
                            .system()
                            .subSystem()))
                .Build();

            treatments treatments = await _client.SendQueryAsync<treatments>(query);

            return treatments!.list!.FirstOrDefault();
        }

        public async Task<treatment?> FindByName(string name)
        {
            string? query = new QueryBuilder().treatments(new QueryBuilder.treatmentsArgs(where: new treatmentFilterInput { where = new treatmentFilterWhereInput { name = name } }),
                    new treatmentsBuilder()
                        .list(new treatmentBuilder()
                            .id()
                            .name()
                            .category()
                            .code()
                            .system()
                            .subSystem()))
                .Build();

            treatments treatments = await _client.SendQueryAsync<treatments>(query);

            return treatments!.list!.FirstOrDefault();
        }

        public async Task<List<treatment>> Filter(treatmentFilterInput input)
        {
            string? query = new QueryBuilder().treatments(new QueryBuilder.treatmentsArgs(where: input),
                    new treatmentsBuilder()
                        .list(new treatmentBuilder()
                            .id()
                            .name()
                            .category()
                            .code()
                            .system()
                            .subSystem()))
                .Build();

            treatments treatments = await _client.SendQueryAsync<treatments>(query);

            return treatments!.list!.ToList()!;
        }
    }
}