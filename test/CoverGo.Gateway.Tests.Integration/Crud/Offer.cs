﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class Offer : CrudBase
    {
        public Offer(GraphQLHttpClient client) : base(client) { }

        public async Task<string> <PERSON><PERSON>(string caseId, string proposalId, addOfferInput input)
        {
            string mutation = new MutationBuilder()
                .addOfferToProposal(new MutationBuilder.addOfferToProposalArgs(caseId, proposalId, input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            return await _client.CreateAndReturnId(mutation);
        }

        public Task Update(string caseId, string proposalId, string offerId, updateOfferInput input)
        {
            string mutation = new MutationBuilder()
                .updateOfferOfProposal(new MutationBuilder.updateOfferOfProposalArgs(caseId, proposalId, offerId, input), new resultBuilder()
                    .status())
                .Build();
            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }
    }
}