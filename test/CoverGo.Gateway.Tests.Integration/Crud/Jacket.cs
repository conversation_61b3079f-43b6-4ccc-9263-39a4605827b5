using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class Jacket : CrudBase
    {
        public Jacket(GraphQLHttpClient client)
            : base(client)
        {
        }

        public async Task<string> Create(createJacketInput input)
        {
            string mutation = new MutationBuilder()
                .createJacket(new MutationBuilder.createJacketArgs(input), new createdStatusResultBuilder()
                    .status()
                    .errors()
                    .createdStatus(new createdStatusBuilder()
                        .id()))
                .Build();

            return await _client.CreateAndReturnId(mutation);
        }

        public async Task Update(string jacketId, updateJacketInput input)
        {
            string mutation = new MutationBuilder()
                .updateJacket(new MutationBuilder.updateJacketArgs(jacketId, input), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task Delete(string jacketId)
        {
            string mutation = new MutationBuilder()
                .deleteJacket(new MutationBuilder.deleteJacketArgs(jacketId), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }
    }
}