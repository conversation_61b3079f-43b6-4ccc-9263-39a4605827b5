using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class Transaction : CrudBase
    {
        public Transaction(GraphQLHttpClient client) : base(client) { }

        public Task<string> Create(createTransactionInput input)
        {
            if (input.amount == null)
                input = input with { amount = 100 };

            string mutation = new MutationBuilder()
                .createTransaction(new MutationBuilder.createTransactionArgs(input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            return _client.CreateAndReturnId(mutation);
        }

        public Task<string> Create()
        {
            createTransactionInput input = new()
            {
                amount = 100
            };

            return Create(input);
        }

        public async Task<transaction?> Find(transactionWhereInput where, transactionBuilder fieldsToFetch)
        {
            string query = new QueryBuilder()
                .transactions(new QueryBuilder.transactionsArgs(where: where), new transactionsBuilder()
                    .list(fieldsToFetch))
                .Build();

            return (await _client.SendQueryAsync<transactions>(query)).list!.SingleOrDefault();
        }

        public Task<string> CreatePaymentMethod(string entityId, bankPaymentMethodInput? bankInput = default)
        {
            var mutation = new MutationBuilder()
                .createPaymentMethod(new MutationBuilder.createPaymentMethodArgs(entityId, bankInput: bankInput),
                    new createdStatusResultBuilder()
                        .createdStatus(new createdStatusBuilder()
                            .WithAllFields()))
                .Build();

            return _client.CreateAndReturnId(mutation);
        }
    }
}