﻿using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using GraphQL.Client.Http;

namespace CoverGo.Gateway.Tests.Integration.Crud;

public class Pricing : CrudBase
{
    public Pricing(GraphQLHttpClient client) : base(client)
    {
    }

    public async Task<string> CalculatePolicyPricing(string policyId, string? endorsementId = default)
    {
        QueryBuilder query = new QueryBuilder()
            .policyPricing(
                new QueryBuilder.policyPricingArgs(policyId, endorsementId), new policies_PolicyPricingResultBuilder()
                    .WithAllFields());
        policies_PolicyPricingResult result = await _client.SendQueryAsync<policies_PolicyPricingResult>(query);

        return result.pricing ?? "";
    }

    public async Task<(string PolicyId, string Result)[]> CalculatePoliciesPricing(string[] policyIds)
    {
        QueryBuilder query = new QueryBuilder()
            .policiesPricing(
                new QueryBuilder.policiesPricingArgs(policyIds), new policies_PoliciesPricingResultBuilder()
                    .WithAllFields());
        
        policies_PoliciesPricingResult result = await _client.SendQueryAsync<policies_PoliciesPricingResult>(query);

        return result.list!.Select(i => (i!.policyId!, i.pricing ?? "")).ToArray();
    }

    public async Task<string> GetPolicyPricingFromCache(string policyId)
    {
        QueryBuilder query = new QueryBuilder()
            .policies(
                new QueryBuilder.policiesArgs(where: new policyWhereInput(){id = policyId}), 
                new policiesBuilder().list(new policyBuilder().pricing()));

        policies result = await _client.SendQueryAsync<policies>(query);

        return result.list!.Single()!.pricing ?? "";
    }
}