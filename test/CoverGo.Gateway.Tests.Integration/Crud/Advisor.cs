using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud;

public class Advisor : CrudBase
{
    public Advisor(GraphQLHttpClient client) : base(client)
    {
    }

    public Task<string> CreateReview(createReviewInput input)
    {
        var mutation = new MutationBuilder()
            .createReview(new MutationBuilder.createReviewArgs(input), new createdStatusResultBuilder()
                .status()
                .errors()
                .createdStatus(new createdStatusBuilder()
                    .id()))
            .Build();
        return _client.CreateAndReturnId(mutation);
    }

    public Task DeleteReview(string id)
    {
        var mutation = new MutationBuilder()
            .deleteReview(new MutationBuilder.deleteReviewArgs(id), new resultBuilder()
                .WithAllFields())
            .Build();

        return _client.SendMutationAndEnsureSuccessAsync(mutation);
    }
}