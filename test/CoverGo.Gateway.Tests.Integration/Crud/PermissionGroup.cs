﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class PermissionGroup : CrudBase
    {
        public PermissionGroup(GraphQLHttpClient client) : base(client) { }

        public async Task<string> Create(createPermissionGroupInput input)
        {
            string mutation = new MutationBuilder()
                .createPermissionGroup(new MutationBuilder.createPermissionGroupArgs(input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);

            permissionGroup group = await Find(new permissionGroupWhereInput { name = input.name });
            return group.id!;
        }

        public async Task<string> Update(string id, updatePermissionGroupInput input)
        {
            string mutation = new MutationBuilder()
                .updatePermissionGroup(new MutationBuilder.updatePermissionGroupArgs(input, id), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);

            return id;
        }

        public Task AddPermission(string permissionGroupId, string permission, string value)
        {
            string mutation = new MutationBuilder().addPermissionToPermissionGroup(
                new MutationBuilder.addPermissionToPermissionGroupArgs(permissionGroupId, permission, value),
                new resultBuilder()
                    .status()
                    .errors()).Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task<string> Create(string name) =>
            Create(new createPermissionGroupInput { name = name, description = CreateNewGuid() });

        public Task<string> Create() =>
            Create(CreateNewGuid());

        public async Task<permissionGroup> Find(permissionGroupWhereInput where)
        {
            string query = new QueryBuilder()
                .permissionGroups(new QueryBuilder.permissionGroupsArgs(where), new permissionGroupBuilder()
                    .id()
                    .name()
                    .description()
                    .productTypes()
                    .logins(new loginBuilder()
                        .id()
                        .email()
                        .username()))
                .Build();

            IReadOnlyCollection<permissionGroup> response
                = await _client.SendQueryAsync<IReadOnlyCollection<permissionGroup>>(query);

            return response.First();
        }
    }
}