using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Linq;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class Disability : CrudBase
    {
        public Disability(GraphQLHttpClient client) : base(client) { }

        public async Task<string> Create(disabilityInput input)
        {
            string mutation = new MutationBuilder()
                .createDisability(new MutationBuilder.createDisabilityArgs(input), new createdStatusResultBuilder()
                    .status()
                    .errors()
                    .createdStatus(new createdStatusBuilder()
                        .id()))
                .Build();

            return await _client.CreateAndReturnId(mutation);
        }


        public async Task Update(string disabilityId, disabilityInput input)
        {
            string mutation = new MutationBuilder()
                .updateDisability(new MutationBuilder.updateDisabilityArgs(disabilityId, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task Delete(string disabilityId)
        {
            string mutation = new MutationBuilder()
                .deleteDisability(new MutationBuilder.deleteDisabilityArgs(disabilityId), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task Batch(disabilityBatchInput input)
        {
            string mutation = new MutationBuilder()
                .disabilityBatch(new MutationBuilder.disabilityBatchArgs(input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task AddDiagnosisToDisability(string diagnosisId, string disabilityId)
        {
            string mutation = new MutationBuilder()
                .addDiagnosisToDisability(
                    new MutationBuilder.addDiagnosisToDisabilityArgs(diagnosisId, disabilityId),
                    new resultBuilder()
                        .status())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task RemoveDiagnosisFromDisability(string diagnosisId, string disabilityId)
        {
            string mutation = new MutationBuilder()
                .removeDiagnosisFromDisability(
                    new MutationBuilder.removeDiagnosisFromDisabilityArgs(diagnosisId, disabilityId),
                    new resultBuilder()
                        .status())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task<disability> FindById(string id)
        {
            string? query = new QueryBuilder().disabilities(new QueryBuilder.disabilitiesArgs(where: new disabilityFilterInput { where = new disabilityFilterWhereInput { id = id } }),
                    new disabilitiesBuilder()
                        .list(new disabilityBuilder()
                            .id()
                            .name()))
                .Build();

            disabilities disabilities = await _client.SendQueryAsync<disabilities>(query);

            return disabilities!.list!.FirstOrDefault();
        }

        public async Task<disability> FindByName(string name)
        {
            string? query = new QueryBuilder().disabilities(new QueryBuilder.disabilitiesArgs(where: new disabilityFilterInput { where = new disabilityFilterWhereInput { name = name } }),
                    new disabilitiesBuilder()
                        .list(new disabilityBuilder()
                            .id()
                            .name()))
                .Build();

            disabilities disabilities = await _client.SendQueryAsync<disabilities>(query);

            return disabilities!.list!.FirstOrDefault();
        }

        public async Task<disability> Filter(disabilityFilterWhereInput filterInput)
        {
            string? query = new QueryBuilder().disabilities(new QueryBuilder.disabilitiesArgs(where: new disabilityFilterInput { where = filterInput }),
                    new disabilitiesBuilder()
                        .list(new disabilityBuilder()
                            .id()
                            .name()))
                .Build();

            disabilities disabilities = await _client.SendQueryAsync<disabilities>(query);

            return disabilities!.list!.FirstOrDefault();
        }
    }
}