﻿using System;
using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Threading.Tasks;
using System.Linq;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class Product : CrudBase
    {
        public Product(GraphQLHttpClient client) : base(client) { }

        public Task<product> Update(productIdInput productId, updateProductInput update, Func<productBuilder, productBuilder> builder)
        {
            string? mutation = new MutationBuilder().updateProduct(
                new MutationBuilder.updateProductArgs(productId, update),
                builder(new productBuilder().productId(new productIdBuilder().plan().version().type())))
                .Build();

            return _client.SendMutationAsync<product>(mutation);
        }
        public Task<product> Update(productId productId, updateProductInput update, Func<productBuilder, productBuilder> builder) => Update(ProductIdToInput(productId), update, builder);
        public Task<product> Update(productIdInput productId, updateProductInput update) => Update(productId, update, builder => builder);

        public async Task<productId> Create(createProductInput input)
        {
            string mutation = new MutationBuilder()
                .createProduct(new MutationBuilder.createProductArgs(input), new productBuilder().productId(new productIdBuilder().WithAllFields()))
                .Build();

            product response = await _client.SendMutationAsync<product>(mutation);
            return response.productId!;
        }

        public Task<productId> Create(bool? autoRenewal = null, bool? renewalNotification = null) =>
            Create(new createProductInput
            {
                productId = new productIdInput
                {
                    plan = CreateNewGuid(),
                    type = CreateNewGuid(),
                    version = CreateNewGuid(),
                },
                autoRenewal = autoRenewal,
                renewalNotification = renewalNotification
            });

        public async Task<(productId, string expectedOutput)> CreateWithScript(scriptTypeEnum scriptType)
        {
            string sourceCode = """
                export function execute({representation, dataInput}) {
                    return dataInput + " and " + representation + " and output";
                }
                """.Replace("\r\n", "\\n").Replace("\"", "\\\"");

            createScriptInput input = new()
            {
                name = CreateNewGuid(),
                inputSchema = "{}",
                outputSchema = "{}",
                sourceCode = sourceCode,
                type = scriptType
            };

            string scriptId = await new Script(_client).Create(input);

            createProductInput createProductInput = new()
            {
                productId = new productIdInput
                {
                    plan = CreateNewGuid(),
                    type = CreateNewGuid()
                },
                representation = CreateNewGuid()
            };

            productId productId = await Create(createProductInput);

            await AddScript(ProductIdToInput(productId), scriptId);

            string expectedOutput = @$"{{0}} and {createProductInput.representation} and output";
            return (productId, expectedOutput);
        }

        public Task AddScript(productIdInput productId, string scriptId)
        {
            string mutation = new MutationBuilder()
                .addScriptToProduct(new MutationBuilder.addScriptToProductArgs(new addScriptToProductInput { productId = productId, scriptId = scriptId }), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task<product?> FindById(productId id, productBuilder fields)
        {
            string query = new QueryBuilder()
                .products_2(new(where: new() { productId = new() { plan = id.plan, type = id.type, version = id.version } }),
                new productsBuilder().list(fields))
                .Build();

            var res = await _client.SendQueryAsync<products>(query);
            return res.list?.FirstOrDefault();
        }
        public Task<product?> FindById(productId id) => FindById(id, productBuilder => productBuilder.representation().fields());
        public Task<product?> FindById(productId id, Func<productBuilder, productBuilder> builder) => FindById(id, builder(new productBuilder().productId(new productIdBuilder().type().version().plan())));

        public productIdInput ProductIdToInput(productId productId) => new()
        {
            plan = productId.plan,
            type = productId.type,
            version = productId.version
        };

        public string ProductIdToString(productId productId) =>
            $"{productId.plan}|{productId.version}|{productId.type}";

        public Task<string> CreateDiscountCode()
        {
            var input = new products_DiscountCodeUpsertInput()
            {
                id = CreateNewGuid(),
                name = $"COVERGO-{CreateNewGuid()}",
                type = products_DiscountType.AMOUNT,
                value = 100,
                description = "Covergo discount code",
                validFrom = DateTime.UtcNow.AddDays(-1),
                validTo = DateTime.UtcNow.AddDays(1),
                productTypeId = "ih"

            };

            return CreateDiscountCode(input);
        }

        public async Task<string> CreateDiscountCode(products_DiscountCodeUpsertInput input)
        {
            string mutation = new MutationBuilder()
                .discountCodesMutationCreate(
                    new MutationBuilder.discountCodesMutationCreateArgs(create: input),
                    new products_ResultOfCreatedStatusBuilder()
                        .status()
                        .errors()
                        .value(new products_CreatedStatusBuilder()
                            .id()))
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);

            return input.id;
        }

        public async Task<products_GenericDiscountCode8QueryInterface> GetDiscountCode(string id)
        {
            string query = new QueryBuilder()
                .discountCodesQuery(new QueryBuilder.discountCodesQueryArgs(where: new products_GenericDiscountCodeQueryInput { where = new products_GenericDiscountCodeFilterInput { where = new products_DiscountCodeFilterInput { id = id } } }), new products_GenericDiscountCode8QueryInterfaceBuilder()
                    .totalCount()
                    .list(new products_DiscountCodeBuilder()
                        .WithAllFields()))
                .Build();

            return await _client.SendQueryAsync<products_GenericDiscountCode8QueryInterface>(query);
        }
    }
}
