using CoverGo.DomainUtils;
using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class Proposal : CrudBase
    {
        public Proposal(GraphQLHttpClient client) : base(client) { }

        public Task<string> Create(string caseId, addProposalInput input)
        {
            string mutation = new MutationBuilder()
               .addProposal(new MutationBuilder.addProposalArgs(caseId, input), new createdStatusResultBuilder()
                   .WithAllFields())
               .Build();

            return _client.CreateAndReturnId(mutation);
        }

        public Task Update(string caseId, string proposalId, updateProposalInput input)
        {
            string mutation = new MutationBuilder()
                .updateProposal(new MutationBuilder.updateProposalArgs(caseId, proposalId, input), new resultBuilder().status())
                .Build();
            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task AddOfferToProposal(string caseId, string proposalId, addOfferInput input)
        {
            string mutation = new MutationBuilder()
                .addOfferToProposal(new MutationBuilder.addOfferToProposalArgs(caseId, proposalId, input), new createdStatusResultBuilder().status())
                .Build();
            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public Task<string> Create(string caseId) =>
            Create(caseId, new addProposalInput { name = Guid.NewGuid().ToString() });

        //public async Task<string> AdminAddAgentProposal(string caseId, cases_AdminAddAgentProposalInput adminAddAgentProposalInput)
        //{
        //    string mutation = new MutationBuilder()
        //        .salesAdminMutationAddAgentProposal(
        //            new MutationBuilder.salesAdminMutationAddAgentProposalArgs(caseId, adminAddAgentProposalInput),
        //            new cases_ResultOfProposalAndOfferIdsBuilder()
        //            .WithAllFields())
        //        .Build();
        //    return (await _client.SendMutationAsync<Result<string>>(mutation)).Value;
        //}
    }
}