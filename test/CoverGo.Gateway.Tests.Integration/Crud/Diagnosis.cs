using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class Diagnosis : CrudBase
    {
        public Diagnosis(GraphQLHttpClient client) : base(client) { }

        public async Task<string> Create(diagnosisInput input)
        {
            string mutation = new MutationBuilder()
                .createDiagnosis(new MutationBuilder.createDiagnosisArgs(input), new createdStatusResultBuilder()
                    .status()
                    .errors()
                    .createdStatus(new createdStatusBuilder()
                        .id()))
                .Build();

            return await _client.CreateAndReturnId(mutation);
        }


        public async Task Update(string diagnosisId, diagnosisInput input)
        {
            string mutation = new MutationBuilder()
                .updateDiagnosis(new MutationBuilder.updateDiagnosisArgs(diagnosisId, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task Delete(string diagnosisId)
        {
            string mutation = new MutationBuilder()
                .deleteDiagnosis(new MutationBuilder.deleteDiagnosisArgs(diagnosisId), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task Batch(diagnosisBatchInput input)
        {
            string mutation = new MutationBuilder()
                .diagnosisBatch(new MutationBuilder.diagnosisBatchArgs(input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task<diagnosis?> FindById(string id)
        {
            string? query = new QueryBuilder().diagnoses(new QueryBuilder.diagnosesArgs(where: new diagnosisFilterInput { where = new diagnosisFilterWhereInput { id = id } }),
                    new diagnosesBuilder()
                        .list(new diagnosisBuilder()
                            .id()
                            .title()
                            .chapter()
                            .code()
                            .classKind()
                            .depthInKind()
                            .fields()))
                .Build();

            diagnoses diagnoses = await _client.SendQueryAsync<diagnoses>(query);

            return diagnoses!.list!.FirstOrDefault();
        }

        public async Task<diagnosis?> FindByTitle(string title)
        {
            string? query = new QueryBuilder().diagnoses(new QueryBuilder.diagnosesArgs(where: new diagnosisFilterInput { where = new diagnosisFilterWhereInput { title = title } }),
                    new diagnosesBuilder()
                        .list(new diagnosisBuilder()
                            .id()
                            .title()
                            .chapter()
                            .code()
                            .classKind()
                            .depthInKind()
                            .fields()))
                .Build();

            diagnoses diagnoses = await _client.SendQueryAsync<diagnoses>(query);

            return diagnoses!.list!.FirstOrDefault();
        }

        public async Task<List<diagnosis>> Filter(diagnosisFilterInput input)
        {
            string? query = new QueryBuilder().diagnoses(new QueryBuilder.diagnosesArgs(where: input),
                    new diagnosesBuilder()
                        .list(new diagnosisBuilder()
                            .id()
                            .title()
                            .chapter()
                            .code()
                            .classKind()
                            .depthInKind()
                            .fields()))
                .Build();

            diagnoses diagnoses = await _client.SendQueryAsync<diagnoses>(query);

            return diagnoses!.list!.ToList()!;
        }
    }
}