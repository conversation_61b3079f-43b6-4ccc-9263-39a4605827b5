﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class Case : CrudBase
    {
        public Case(GraphQLHttpClient client) : base(client) { }

        public Task<string> Create(createCaseInput input)
        {
            string mutation = new MutationBuilder()
                .createCase(new MutationBuilder.createCaseArgs(input), new createdStatusResultBuilder()
                    .status()
                    .errors()
                    .createdStatus(new createdStatusBuilder()
                        .id()))
                .Build();

            return _client.CreateAndReturnId(mutation);
        }

        public Task Update(string caseId, updateCaseInput update)
        {
            string? mutation = new MutationBuilder()
                .updateCase(new MutationBuilder.updateCaseArgs(caseId, update), new resultBuilder().WithAllFields())
            .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task<@case?> FindById(string caseId)
        {
            string? query = new QueryBuilder()
                .cases(
                    new QueryBuilder.casesArgs(new caseWhere { id = caseId }),
                    new casesBuilder().list(new caseBuilder()
                        .id()
                        .proposals(
                            new caseBuilder.proposalsArgs(),
                            new proposalBuilder()
                                .isApprovalNeeded()
                                .renewalHistory(new renewalHistoryBuilder()
                                    .renewedTo(new proposalBuilder()
                                        .id()))
                                .id().basket(
                                new proposalBuilder.basketArgs(),
                                new offerBuilder()
                                    .id()
                                    .clauses(
                                        new offerBuilder.clausesArgs(),
                                        new clauseBuilder()
                                            .id()
                                            .renderedHtmlResult(new stringResultBuilder().value())
                                            .template(new templateInterfaceBuilder().clauseHtmlFragment(new clauseHtmlBuilder()
                                                .id()
                                                .html())))
                                    .jackets(
                                        new offerBuilder.jacketsArgs(),
                                        new jacketInstanceBuilder()
                                            .id()
                                            .jacket(new jacketBuilder()
                                                .title()
                                                .id()
                                                .clauses(new jacketBuilder.clausesArgs(), new clauseBuilder()
                                                    .id()
                                                    .renderedHtmlResult(new stringResultBuilder().value())
                                                    .template(new templateInterfaceBuilder().clauseHtmlFragment(new clauseHtmlBuilder()
                                                        .id()
                                                        .html())))))))
                        .fields()))
                .Build();

            cases cases = await _client.SendQueryAsync<cases>(query);

            return cases.list!.FirstOrDefault();
        }

        public async Task<@case?> FindByIdIsApprovalNeeded(string caseId)
        {
            string? query = new QueryBuilder()
                .cases(
                    new QueryBuilder.casesArgs(new caseWhere { id = caseId }),
                    new casesBuilder().list(new caseBuilder()
                        .id()
                        .proposals(
                            new caseBuilder.proposalsArgs(),
                            new proposalBuilder()
                                .proposalNumber()
                                .isApprovalNeeded())
                        .fields()))
                .Build();

            cases cases = await _client.SendQueryAsync<cases>(query);

            return cases.list!.FirstOrDefault();
        }

        public async Task<ICollection<@case?>> FindByAprovalNeeded(bool isApprovalNeeded)
        {
            string? query = new QueryBuilder()
                .cases(
                    new QueryBuilder.casesArgs(new caseWhere { proposal = new proposalWhere() { isApprovalNeeded = isApprovalNeeded } }),
                    new casesBuilder().list(new caseBuilder()
                        .id()
                        .proposals(
                            new caseBuilder.proposalsArgs(),
                            new proposalBuilder()
                                .proposalNumber()
                                .isApprovalNeeded())
                        .fields()))
                .Build();

            cases cases = await _client.SendQueryAsync<cases>(query);

            return cases.list!;
        }

        public Task<string> Create() =>
            Create(new createCaseInput { name = Guid.NewGuid().ToString() });
        public Task<string> CreateWithSource(string source) =>
            Create(new createCaseInput { name = Guid.NewGuid().ToString(), source = source });

        public Task AddStakeHolder(string caseId, addStakeholderInput input)
        {
            var mutation = new MutationBuilder()
                .addStakeholderToCase(new MutationBuilder.addStakeholderToCaseArgs(caseId, input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();
            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task<string> AgenmtCreateCase(cases_AgentCreateCaseInput input)
        {
            string mutation = new MutationBuilder()
                .agentMutationAgentCreateCase(
                    new MutationBuilder.agentMutationAgentCreateCaseArgs(input),
                    new cases_ResultOfStringBuilder().WithAllFields())
                .Build();
            return (await _client.SendMutationAsync<Result<string>>(mutation)).Value;
        }
    }
}