using System;
using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class Policy : CrudBase
    {
        public Endorsement Endorsement { get; private set; }
        public PolicyMember PolicyMember { get; }

        public Policy(GraphQLHttpClient client) : base(client)
        {
            Endorsement = new Endorsement(client);
            PolicyMember = new PolicyMember(client);
        }

        public Task<string> Create() => Create(new initializePolicyInput
        {
            fields = "{}",
            description = CreateNewGuid(),
            fieldsSchemaId = CreateNewGuid(),
        });

        public async Task<string> Create(initializePolicyInput input)
        {
            string mutation = new MutationBuilder()
                .initializePolicy(new MutationBuilder.initializePolicyArgs(input), new policyResultBuilder()
                    .policyStatus(new policyStatusBuilder()
                        .id()))
                .Build();

            policyResult response = await _client.SendMutationAsync<policyResult>(mutation);
            return response!.policyStatus!.id!;
        }

        public async Task<string> GenerateFromProposal(string caseId, string proposalId)
        {
            string mutation = new MutationBuilder()
                .generatePoliciesFromProposal(new MutationBuilder.generatePoliciesFromProposalArgs(caseId, proposalId), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            createdStatusResult response = await _client.SendMutationAsync<createdStatusResult>(mutation);
            return response!.createdStatus!.ids!.FirstOrDefault();
        }

        public async Task<policy?> FindById(string policyId, endorsementWhereInput? endorsementWhere = null)
        {
            var builder = new policiesBuilder()
                        .list(new policyBuilder()
                            .id()
                            .issuerNumber()
                            .fields()
                            .cancelledBy(new loginBuilder().id())
                            .tags(new tagBuilder().id())
                            .status()
                            .insured(new entityInterfaceBuilder()
                                .id())
                            .endorsements(
                                new policyBuilder.endorsementsArgs(endorsementWhere),
                                new endorsementBuilder()
                                    .id()
                                    .status()
                                    .source()
                                    .transactions(new transactionBuilder()
                                        .id())));

            string ? query = new QueryBuilder()
                .policies(
                    new QueryBuilder.policiesArgs(where: new policyWhereInput { id = policyId }),
                    builder)
                .Build();

            policies policies = await _client.SendQueryAsync<policies>(query);
            return policies!.list!.FirstOrDefault();
        }

        public async Task<List<policy>?> FindList(policyWhereInput where)
        {
            string? query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: where),
                new policiesBuilder()
                    .list(new policyBuilder()
                        .id()
                        .fields()
                        .tags(
                            new tagBuilder()
                            .WithAllFields()
                            )
                        )
                    )
            .Build();

            policies policies = await _client.SendQueryAsync<policies>(query);
            return policies!.list!.ToList()!;
        }

        public async Task Update(string policyId, updatePolicyInput input, string? endorsementId = null)
        {
            string mutation = new MutationBuilder()
                .updatePolicy2(new MutationBuilder.updatePolicy2Args(policyId, input, endorsementId), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAsync<createdStatusResult>(mutation);
        }

        public Task Issue(string policyId, string? status = null)
        {
            string mutation = new MutationBuilder()
                .issuePolicy(new MutationBuilder.issuePolicyArgs(policyId, status: status), new policyResultBuilder()
                    .policyStatus(new policyStatusBuilder()
                        .status()))
                .Build();

            return _client.SendMutationAsync<policyResult>(mutation);
        }

        public Task UpdateProductAsync(string policyId, productId productId, string? endorsementId = null)
        {
            string? mutation = new MutationBuilder()
                .updatePolicyProduct(new MutationBuilder.updatePolicyProductArgs(
                        policyId,
                        new productIdInput { plan = productId.plan, type = productId.type, version = productId.version },
                        endorsementId),
                    new createdStatusResultBuilder().WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task<string> AddContractInsured(string policyId, string entityId, string? endorsementId = null)
        {
            string mutation = new MutationBuilder()
                .addContractInsured(new MutationBuilder.addContractInsuredArgs(policyId, entityId, endorsementId),
                    new createdStatusResultBuilder()
                        .WithAllFields())
                .Build();

            createdStatusResult response = await _client.SendMutationAsync<createdStatusResult>(mutation);
            return response!.createdStatus!.id!;
        }

        public async Task<string> RemoveContractInsured(string policyId, string entityId, string? endorsementId = null)
        {
            string mutation = new MutationBuilder()
                .removeContractInsured(new MutationBuilder.removeContractInsuredArgs(policyId, entityId, endorsementId),
                    new createdStatusResultBuilder()
                        .WithAllFields())
                .Build();

            createdStatusResult response = await _client.SendMutationAsync<createdStatusResult>(mutation);
            return response!.createdStatus!.id!;
        }

        public async Task<string> AddTag(string policyId, tagInput input)
        {
            string mutation = new MutationBuilder()
                .addTagToPolicy(new MutationBuilder.addTagToPolicyArgs(policyId, input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            createdStatusResult response = await _client.SendMutationAsync<createdStatusResult>(mutation);
            return response!.createdStatus!.id!;
        }

        public async Task RemoveTag(string policyId, string tagId)
        {
            string mutation = new MutationBuilder()
                .removeTagFromPolicy(new MutationBuilder.removeTagFromPolicyArgs(policyId, tagId), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAsync<createdStatusResult>(mutation);
        }

        public Task CancelPolicy(string policyId)
        {
            var mutation = new MutationBuilder()
                .cancelPolicy(new MutationBuilder.cancelPolicyArgs(policyId, Guid.NewGuid().ToString()), new resultBuilder()
                    .WithAllFields())
                .Build();
            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }


        public async Task<policy> GetWithMembers(
            string policyId,
            DateTime? asOf = null,
            policyMembersFilterAggregateInput? where = null,
            sortInput? sort = null,
            List<sortInput>? sort2 = null,
            bool? isTerminated = null,
            bool? filterOutTerminatedMemberMovements = null)
        {
            string query = new QueryBuilder()
                .policies(
                    new QueryBuilder.policiesArgs(where: new policyWhereInput { id = policyId }, asOf: asOf),
                    new policiesBuilder()
                        .list(PolicyWithMembersQueryBuild(where, sort, sort2, isTerminated, filterOutTerminatedMemberMovements)))
                .Build();

            policies response = await _client.SendQueryAsync<policies>(query);
            return response.list!.Single()!;
        }

        private static policyBuilder PolicyWithMembersQueryBuild(
            policyMembersFilterAggregateInput where = null!,
            sortInput? sort = null,
            List<sortInput>? sort2 = null,
            bool? isTerminated = null,
            bool? filterOutTerminatedMemberMovements = null) =>
               new policyBuilder()
                .id()
                .members(new policyBuilder.membersArgs(filter: where, sort: sort, sort2: sort2, isTerminated: isTerminated, filterOutTerminatedMemberMovements: filterOutTerminatedMemberMovements), new membersBuilder()
                    .list(PolicyMemberBuilder())
                    .totalCount())
                .membersMovements(new policyBuilder.membersMovementsArgs(filter: where, sort: sort, sort2: sort2),
                    new membersMovementsBuilder()
                        .list(PolicyMemberBuilder())
                        .totalCount())
                .membersActivity(new policyBuilder.membersActivityArgs(filter: where, sort: sort, sort2: sort2),
                    new membersActivityBuilder()
                        .list(PolicyMemberBuilder())
                        .totalCount());

        private static policyMemberBuilder PolicyMemberBuilder() =>
            new policyMemberBuilder()
                .id()
                .memberId()
                .policyId()
                .createdAt()
                .lastModifiedAt()
                .internalCode()
                .dependentOf()
                .timestamp()
                .endorsementId()
                .planId()
                .startDate()
                .endDate()
                .fields()
                .createdById()
                .isRemoved()
                .isPrinted()
                .underwritingResult()
                .validationResult();
    }
}