﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class Claim : CrudBase
    {
        public Claim(GraphQLHttpClient client) : base(client) { }

        public async Task<string> Create(createClaimInput input)
        {
            string mutation = new MutationBuilder()
                .createClaim(new MutationBuilder.createClaimArgs(input), new createdStatusResultBuilder()
                    .status()
                    .errors()
                    .createdStatus(new createdStatusBuilder()
                        .id()))
                .Build();

            return await _client.CreateAndReturnId(mutation);
        }

        public Task AddAttachment(string claimId, attachmentInput input)
        {
            string claimAddAttachmentMutation = new MutationBuilder()
                .addClaimAttachment(
                    new MutationBuilder.addClaimAttachmentArgs(Guid.Parse(claimId), input),
                    new resultBuilder()
                        .status())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(claimAddAttachmentMutation);
        }

        public Task RemoveAttachment(string claimId, string path)
        {
            string claimRemoveAttachmentMutation = new MutationBuilder()
                .removeClaimAttachment(
                    new MutationBuilder.removeClaimAttachmentArgs(Guid.Parse(claimId), path),
                    new resultBuilder()
                        .status())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(claimRemoveAttachmentMutation);
        }

        public async Task Update(Guid claimId, updateClaimInput input)
        {
            string mutation = new MutationBuilder()
                .updateClaim(new MutationBuilder.updateClaimArgs(claimId, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        public async Task<ICollection<string?>?> Batch(claimBatchInput input)
        {
            string mutation = new MutationBuilder()
                .claimBatchV2(new MutationBuilder.claimBatchV2Args(input), new createdStatusResultBuilder()
                    .status()
                    .errors()
                    .createdStatus(new createdStatusBuilder()
                        .ids())
                    .WithAllFields())
                .Build();

            return await _client.CreateAndReturnIds(mutation);
        }

        public async Task<claim> FindById(string claimId, DateTimeOffset? asOf = null)
        {
            string query = new QueryBuilder()
                .claims(new QueryBuilder.claimsArgs(where: new claimWhereInput { id = claimId }, asOf: asOf), new claimsBuilder()
                    .list(new claimBuilder()
                        .id()
                        .status()
                        .fields()
                        .remark()
                        .claimApprover(new loginBuilder()
                            .id()
                            .email()
                            .associatedUser(new entityInterfaceBuilder()
                                .id()
                                .name()))
                        .claimHandler(new loginBuilder()
                            .id()
                            .email()
                            .associatedUser(new entityInterfaceBuilder()
                                .id()
                                .name()))
                        .claimant(new customerInterfaceBuilder()
                            .id())
                        .events(new claimEventLogBuilder()
                            .updateType())
                        .rejectionReasons(new rejectionReasonBuilder()
                            .description())
                        .panel(new organizationBuilder()
                            .id())
                        .provider(new entityInterfaceBuilder()
                            .id())
                        .reversals(new claimReversalBuilder()
                            .reason()
                            .reversedAt()
                            .reversedBy(new loginBuilder()
                                .id()))
                        .policyMember(new policyMemberBuilder()
                            .memberId())
                        .policy(new policyBuilder()
                            .id())))
                .Build();

            claims response = await _client.SendQueryAsync<claims>(query);
            return response.list!.First()!;
        }

        public async Task<IReadOnlyList<claim?>> Find(claimWhereInput where)
        {
            string query = new QueryBuilder()
                .claims(new QueryBuilder.claimsArgs(where: where), new claimsBuilder()
                    .list(new claimBuilder()
                        .id()
                        .fields()
                        .status()
                        .events(new claimEventLogBuilder()
                            .updateType())
                        .diagnosisCodes(new diagnosisCodeNameBuilder()
                            .code())
                        .operationCodes(new operationCodeNameBuilder()
                            .code())
                        .importBatchId(new batchIdBuilder()
                            .id()
                            .name())
                        .exportBatchIds(new batchIdBuilder()
                            .id()
                            .name())))
                .Build();

            claims response = await _client.SendQueryAsync<claims>(query);
            return response.list!.ToArray();
        }
    }
}