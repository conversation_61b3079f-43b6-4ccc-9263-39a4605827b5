﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class DataSchema : CrudBase
    {
        public DataSchema(GraphQLHttpClient client) : base(client) { }

        public async Task<(string, createDataSchemaInput)> Create(List<string?>? tags = default)
        {
            createDataSchemaInput input = new()
            {
                name = Guid.NewGuid().ToString(),
                description = Guid.NewGuid().ToString(),
                schema = "{}",
                standard = new dataSchemaStandardInput
                {
                    type = dataSchemaStandardTypeEnum.STATE_CHART,
                    version = Guid.NewGuid().ToString()
                },
                type = Guid.NewGuid().ToString(),
                tags = tags ?? new List<string?>()
            };
            return (await Create(input), input);
        }

        public async Task<string> Create(createDataSchemaInput input)
        {
            string mutation = new MutationBuilder()
                .createDataSchema(new MutationBuilder.createDataSchemaArgs(input), new createdStatusResultBuilder()
                    .createdStatus(new createdStatusBuilder()
                        .id()))
                .Build();

            return await _client.CreateAndReturnId(mutation);
        }
    }
}