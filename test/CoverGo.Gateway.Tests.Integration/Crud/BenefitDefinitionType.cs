﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public class BenefitDefinitionType : CrudBase
    {
        public BenefitDefinitionType(GraphQLHttpClient client) : base(client) { }

        public Task<string> Create(createBenefitDefinitionTypeInput input)
        {
            string mutation = new MutationBuilder()
                .createBenefitDefinitionType(new MutationBuilder.createBenefitDefinitionTypeArgs(input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            return _client.CreateAndReturnId(mutation);
        }

        public async Task<(string, createBenefitDefinitionTypeInput)> Create()
        {
            createBenefitDefinitionTypeInput input = new()
            {
                name = CreateNewGuid(),
                description = "new description",
                businessId = CreateNewGuid(),
                status = CreateNewGuid()
            };

            string id = await <PERSON><PERSON>(input);

            return (id, input);
        }

        public async Task Batch(batchBenefitDefinitionTypeInput input)
        {
            string mutation = new MutationBuilder()
                .batchBenefitDefinitionType(new MutationBuilder.batchBenefitDefinitionTypeArgs(input), new resultBuilder().WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }
    }
}