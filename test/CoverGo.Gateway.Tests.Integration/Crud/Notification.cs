using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud;

public class Notification : CrudBase
{
    public Notification(GraphQLHttpClient client) : base(client)
    {
    }

    public async Task<notificationSubscription?> FindNotificationSubscriptionById(string id)
    {
        var where = new notificationSubscriptionWhereInput() { id = id };
        var query = new QueryBuilder()
            .notificationSubscriptions(new QueryBuilder.notificationSubscriptionsArgs(where), new notificationSubscriptionsBuilder()
                .list(new notificationSubscriptionBuilder()
                    .id()))
            .Build();

        return (await _client.SendQueryAsync<notificationSubscriptions>(query)).list!.SingleOrDefault();
    }

    public Task<string> CreateNotificationSubscription(string? topicName = default)
    {
        var mutation = new MutationBuilder()
            .createNotificationSubscription(new MutationBuilder.createNotificationSubscriptionArgs(topicName ?? CreateNewGuid()),
                new createdStatusResultBuilder()
                    .WithAllFields())
            .Build();

        return _client.CreateAndReturnId(mutation);
    }

    public Task AddEntityToNotificationSubscription(string id, string entityId)
    {
        var mutation = new MutationBuilder()
            .addEntityToNotificationSubscription(new MutationBuilder.addEntityToNotificationSubscriptionArgs(entityId, id), new resultBuilder()
                .WithAllFields())
            .Build();
        return _client.SendMutationAndEnsureSuccessAsync(mutation);
    }

    public Task SendNotification(sendNotificationInput input)
    {
        var mutation = new MutationBuilder()
            .sendNotification(new MutationBuilder.sendNotificationArgs(input), new resultBuilder()
                .WithAllFields())
            .Build();

        return _client.SendMutationAndEnsureSuccessAsync(mutation);
    }

    public async Task<ICollection<notification?>?> GetNotifications(notificationWhere where)
    {
        var query = new QueryBuilder()
            .notifications(new QueryBuilder.notificationsArgs(where: where), new notificationsBuilder()
                .list(new notificationBuilder()
                    .id()))
            .Build();

        return (await _client.SendQueryAsync<notifications>(query)).list;
    }
}