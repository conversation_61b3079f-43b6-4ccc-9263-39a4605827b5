﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System;
using System.Linq;
using System.Threading.Tasks;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Crud
{
    public sealed class ServiceItem : CrudBase
    {
        public ServiceItem(GraphQLHttpClient client) : base(client) { }

        public async Task<(string id, createServiceItemInput input)> Create(string? ServiceItemName = null)
        {
            createServiceItemInput input = new()
            {
                name = ServiceItemName ?? Guid.NewGuid().ToString(),
                description = Guid.NewGuid().ToString()
            };
            return (await Create(input), input);
        }

        public async Task<string> Create(createServiceItemInput input)
        {
            string mutation = new MutationBuilder()
                .createServiceItem(new MutationBuilder.createServiceItemArgs(input), new createdStatusResultBuilder()
                    .createdStatus(new createdStatusBuilder()
                        .id()))
                .Build();

            return await _client.CreateAndReturnId(mutation);
        }

        public async Task Update(updateServiceItemInput input)
        {
            string mutation = new MutationBuilder()
                .updateServiceItem(new MutationBuilder.updateServiceItemArgs(input), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAsync<result>(mutation);
        }

        public async Task Delete(deleteServiceItemInput input)
        {
            string mutation = new MutationBuilder()
                .deleteServiceItem(new MutationBuilder.deleteServiceItemArgs(input), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAsync<result>(mutation);
        }

        public async Task<serviceItem?> Search(serviceItemWhereInput where)
        {
            string query = new QueryBuilder()
                .serviceItems(new QueryBuilder.serviceItemsArgs(where: where), new serviceItemsBuilder()
                    .list(new serviceItemBuilder()
                        .id()
                        .name()
                        .description()))
                .Build();

            serviceItems ServiceItems = await _client.SendQueryAsync<serviceItems>(query);
            return ServiceItems.list?.SingleOrDefault();
        }
    }
}