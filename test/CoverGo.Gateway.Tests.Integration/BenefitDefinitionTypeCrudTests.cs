﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class BenefitDefinitionTypeCrudTests : TestsBase
    {
        public BenefitDefinitionTypeCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_benefitDefinitionType_WHEN_create_THEN_returns_id()
        {
            createBenefitDefinitionTypeInput createBenefitDefinitionTypeCommand = new()
            {
                name = "new benefit type",
                description = "new description",
                status = "new status",
                businessId = CreateNewGuid()
            };

            string id = await BenefitDefinitionType.Create(createBenefitDefinitionTypeCommand);

            id.Should().NotBeNullOrWhiteSpace();
        }


        [Fact]
        public async Task GIVEN_multiple_BenefitDefinitionTypes_WHEN_batch_create_THEN_created_and_succeed()
        {
            string businessId1 = Guid.NewGuid().ToString();
            string businessId2 = Guid.NewGuid().ToString();
            batchBenefitDefinitionTypeInput input = new()
            {
                createBenefitDefinitionTypeInputs = new List<createBenefitDefinitionTypeInput>
                {
                    new()
                    {
                        name = "new benefit 1",
                        description = "new description 1",
                        status = "new status 1",
                        businessId = businessId1
                    },
                    new()
                    {
                        name = "new benefit 2",
                        description = "new description 2",
                        status = "new status 2",
                        businessId = businessId2
                    }
                }
            };

            await BenefitDefinitionType.Batch(input);

            benefitDefinitionType? benefitDefinitionType1 = await SearchBenefitDefinitionType(new benefitDefinitionTypeWhereInput { businessId_in = new[] { businessId1 } });

            benefitDefinitionType1.Should().NotBeNull();
            benefitDefinitionType1!.name.Should().Be("new benefit 1");
            benefitDefinitionType1.description.Should().Be("new description 1");
            benefitDefinitionType1.status.Should().Be("new status 1");
            benefitDefinitionType1.businessId.Should().Be(businessId1);

            benefitDefinitionType? benefitDefinitionType2 = await SearchBenefitDefinitionType(new benefitDefinitionTypeWhereInput { businessId_in = new[] { businessId2 } });
            benefitDefinitionType2.Should().NotBeNull();
            benefitDefinitionType2!.name.Should().Be("new benefit 2");
            benefitDefinitionType2.description.Should().Be("new description 2");
            benefitDefinitionType2.status.Should().Be("new status 2");
            benefitDefinitionType2.businessId.Should().Be(businessId2);
        }

        [Fact]
        public async Task GIVEN_a_created_BenefitDefinitionType_WHEN_batch_update_this_BenefitDefinitionType_THEN_updated_and_succeed()
        {
            createBenefitDefinitionTypeInput input = new()
            {
                name = "new benefit",
                description = "new description",
                status = "new status",
                businessId = Guid.NewGuid().ToString()
            };

            string id = await BenefitDefinitionType.Create(input);

            batchBenefitDefinitionTypeInput batchInput = new()
            {
                updateBenefitDefinitionTypeInputs = new List<updateBenefitDefinitionTypeInput>
                {
                    new()
                    {
                        benefitDefinitionTypeId = id,
                        name = "new benefit 1",
                        description = "new description 1",
                        status = "new status 1",
                    }
                }
            };

            await BenefitDefinitionType.Batch(batchInput);

            benefitDefinitionType? benefitDefinitionType = await SearchBenefitDefinitionType(new benefitDefinitionTypeWhereInput { id_in = new[] { id } });
            benefitDefinitionType!.name.Should().Be("new benefit 1");
            benefitDefinitionType.description.Should().Be("new description 1");
            benefitDefinitionType.status.Should().Be("new status 1");
        }

        [Fact]
        public async Task GIVEN_multiple_BenefitDefinitionTypes_have_same_businessId_WHEN_batch_create_THEN_return_failure_result()
        {
            string businessId = Guid.NewGuid().ToString();
            batchBenefitDefinitionTypeInput input = new()
            {
                createBenefitDefinitionTypeInputs = new List<createBenefitDefinitionTypeInput>
                {
                    new()
                    {
                        name = "new benefit 1",
                        description = "new description 1",
                        status = "new status 1",
                        businessId = businessId
                    },
                    new()
                    {
                        name = "new benefit 2",
                        description = "new description 2",
                        status = "new status 2",
                        businessId = businessId
                    }
                }
            };

            Func<Task> task = async () => await BenefitDefinitionType.Batch(input);

            await task.Should().ThrowAsync<Exception>();
        }

        [Fact]
        public async Task GIVEN_a_BenefitDefinitionType_has_existing_businessId_WHEN_batch_create_THEN_return_failure_result()
        {
            string businessId = Guid.NewGuid().ToString();

            createBenefitDefinitionTypeInput input = new()
            {
                name = "new benefit",
                description = "new description",
                status = "new status",
                businessId = businessId
            };

            string id = await BenefitDefinitionType.Create(input);

            batchBenefitDefinitionTypeInput batchInput = new()
            {
                createBenefitDefinitionTypeInputs = new List<createBenefitDefinitionTypeInput>
                {
                    new()
                    {
                        name = "new benefit 1",
                        description = "new description 1",
                        status = "new status 1",
                        businessId = businessId
                    }
                }
            };

            Func<Task> task = async () => await BenefitDefinitionType.Batch(batchInput);

            await task.Should().ThrowAsync<Exception>();
        }

        [Fact]
        public async Task GIVEN_benefitDefinitionType_added_WHEN_request_this_benefitDefinitionType_THEN_returns_benefitDefinitionType()
        {
            (string id, createBenefitDefinitionTypeInput createBenefitDefinitionTypeCommand) = await BenefitDefinitionType.Create();

            benefitDefinitionType? benefitDefinitionType = await SearchBenefitDefinitionTypeById(id);

            benefitDefinitionType.ShouldHaveSameValuesAsInput(createBenefitDefinitionTypeCommand);
        }

        [Fact]
        public async Task GIVEN_benefitDefinitionType_added_WHEN_request_this_benefitDefinitionType_by_businessId_THEN_returns_benefitDefinitionType()
        {
            (_, createBenefitDefinitionTypeInput input) = await BenefitDefinitionType.Create();

            benefitDefinitionType? benefitDefinitionType = await SearchBenefitDefinitionType(new benefitDefinitionTypeWhereInput { businessId_in = new[] { input.businessId } });

            benefitDefinitionType.ShouldHaveSameValuesAsInput(input);
        }

        [Theory]
        [InlineData("valueId", "businessId")]
        [InlineData("valueId", "nameContains")]
        [InlineData("valueId", "businessIdContains")]
        [InlineData("nameContains", "businessIdContains", "valueId")]
        public async Task GIVEN_benefitDefinitionTypes_WHEN_request_it_by_and_condition_THEN_returns_one(params string[] andConditions)
        {
            (string id, createBenefitDefinitionTypeInput benefitDefinitionType) = await BenefitDefinitionType.Create();
            await BenefitDefinitionType.Create();
            List<benefitDefinitionTypeWhereInput?> andCondition = new();

            if (andConditions.Contains("valueId")) andCondition.Add(new benefitDefinitionTypeWhereInput { id_in = new[] { id } });
            if (andConditions.Contains("businessId")) andCondition.Add(new benefitDefinitionTypeWhereInput { businessId_in = new[] { benefitDefinitionType.businessId } });
            if (andConditions.Contains("nameContains")) andCondition.Add(new benefitDefinitionTypeWhereInput { name_contains = benefitDefinitionType.name });
            if (andConditions.Contains("businessIdContains")) andCondition.Add(new benefitDefinitionTypeWhereInput { businessId_contains = benefitDefinitionType.businessId });

            IReadOnlyCollection<benefitDefinitionType?>? benefitDefinitionTypes = await SearchMultipleBenefitDefinitionType(
                new benefitDefinitionTypeWhereInput { and = andCondition });

            benefitDefinitionTypes!.Count.Should().Be(1);
            benefitDefinitionTypes!.First()?.id.Should().Be(id);
        }

        [Theory]
        [InlineData("id", "id")]
        [InlineData("id", "businessId")]
        [InlineData("id", "nameContains")]
        [InlineData("id", "businessIdContains")]
        [InlineData("nameContains", "businessIdContains")]
        public async Task GIVEN_two_benefitDefinitionTypes_WHEN_request_it_by_or_condition_THEN_returns_two(params string[] orConditions)
        {
            (string id, createBenefitDefinitionTypeInput benefitDefinitionType) = await BenefitDefinitionType.Create();
            (string id2, createBenefitDefinitionTypeInput benefitDefinitionType2) = await BenefitDefinitionType.Create();

            List<benefitDefinitionTypeWhereInput?> orCondition = new();

            if (orConditions[0] == "id") orCondition.Add(new benefitDefinitionTypeWhereInput { id_in = new[] { id } });
            if (orConditions[0] == "businessId") orCondition.Add(new benefitDefinitionTypeWhereInput { businessId_in = new[] { benefitDefinitionType.businessId } });
            if (orConditions[0] == "nameContains") orCondition.Add(new benefitDefinitionTypeWhereInput { name_contains = benefitDefinitionType.name });
            if (orConditions[0] == "businessIdContains") orCondition.Add(new benefitDefinitionTypeWhereInput { businessId_contains = benefitDefinitionType.businessId });

            if (orConditions[1] == "id") orCondition.Add(new benefitDefinitionTypeWhereInput { id_in = new[] { id2 } });
            if (orConditions[1] == "businessId") orCondition.Add(new benefitDefinitionTypeWhereInput { businessId_in = new[] { benefitDefinitionType2.businessId } });
            if (orConditions[1] == "nameContains") orCondition.Add(new benefitDefinitionTypeWhereInput { name_contains = benefitDefinitionType2.name });
            if (orConditions[1] == "businessIdContains") orCondition.Add(new benefitDefinitionTypeWhereInput { businessId_contains = benefitDefinitionType2.businessId });

            IReadOnlyCollection<benefitDefinitionType?>? benefitDefinitionTypes = await SearchMultipleBenefitDefinitionType(
                new benefitDefinitionTypeWhereInput { or = orCondition });

            benefitDefinitionTypes!.Count.Should().Be(2);
            benefitDefinitionTypes.First(c => c!.id == id).Should().NotBe(null);
            benefitDefinitionTypes.First(c => c!.id == id2).Should().NotBe(null);
        }

        [Fact]
        public async Task GIVEN_benefitDefinitionType_WHEN_search_it_by_name_contains_THEN_returns_it()
        {
            (string id, createBenefitDefinitionTypeInput createBenefitDefinitionInput) = await BenefitDefinitionType.Create();

            benefitDefinitionType? benefitDefinitionTypes = await SearchBenefitDefinitionType(new benefitDefinitionTypeWhereInput { name_contains = createBenefitDefinitionInput.name });

            benefitDefinitionTypes!.id.Should().Be(id);
        }

        [Fact]
        public async Task GIVEN_benefitDefinitionType_WHEN_search_it_by_businessId_contains_THEN_returns_it()
        {
            (string id, createBenefitDefinitionTypeInput createBenefitDefinitionInput) = await BenefitDefinitionType.Create();

            benefitDefinitionType? benefitDefinitionTypes = await SearchBenefitDefinitionType(new benefitDefinitionTypeWhereInput { businessId_contains = createBenefitDefinitionInput.businessId });

            benefitDefinitionTypes!.id.Should().Be(id);
        }

        [Fact]
        public async Task GIVEN_benefitDefinitionType_WHEN_search_it_by_status_THEN_returns_it()
        {
            (string id, createBenefitDefinitionTypeInput createBenefitDefinitionInput) = await BenefitDefinitionType.Create();

            benefitDefinitionType? benefitDefinitionType = await SearchBenefitDefinitionType(new benefitDefinitionTypeWhereInput { status = createBenefitDefinitionInput.status });

            benefitDefinitionType!.id.Should().Be(id);
        }

        [Theory]
        [InlineData("asc")]
        [InlineData("desc")]
        public async Task GIVEN_benefitDefinitionTypes_WHEN_order_it_by_businessId_THEN_returns_sorted_list(string sortType)
        {
            (string id, createBenefitDefinitionTypeInput createBenefitDefinitionTypeInput) = await BenefitDefinitionType.Create();
            (string id2, createBenefitDefinitionTypeInput createBenefitDefinitionTypeInput2) = await BenefitDefinitionType.Create();

            sortInput sort = new() { fieldName = "businessId", type = sortType };
            IReadOnlyCollection<benefitDefinitionType?>? benefitDefinitionTypes = await SearchMultipleBenefitDefinitionType(new benefitDefinitionTypeWhereInput { id_in = new[] { id, id2 } }, sort);

            List<createBenefitDefinitionTypeInput> sortedByBusinessId = new() { createBenefitDefinitionTypeInput, createBenefitDefinitionTypeInput2 };
            if (sortType == "asc")
                sortedByBusinessId.Sort((a, b) => string.Compare(a.businessId!, b.businessId, StringComparison.Ordinal));
            else
                sortedByBusinessId.Sort((a, b) => string.Compare(b.businessId!, a.businessId, StringComparison.Ordinal));

            benefitDefinitionTypes!.First()!.businessId.Should().Be(sortedByBusinessId.First().businessId);
            benefitDefinitionTypes!.Skip(1).First()!.businessId.Should().Be(sortedByBusinessId.Skip(1).First().businessId);
        }

        [Fact]
        public async Task GIVEN_three_benefitDefinitionTypes_WHEN_order_it_by_id_asc_and_limit_it_by_one_THEN_returns_one()
        {
            (string id, _) = await BenefitDefinitionType.Create();
            (string id2, _) = await BenefitDefinitionType.Create();
            (string id3, _) = await BenefitDefinitionType.Create();

            const int limitNumber = 1;
            sortInput sort = new() { fieldName = "id", type = "asc" };
            IReadOnlyCollection<benefitDefinitionType?>? benefitDefinitionTypes = await SearchMultipleBenefitDefinitionType(
                new benefitDefinitionTypeWhereInput { id_in = new[] { id, id2, id3 } },
                sort,
                limitNumber);

            List<string> sortedById = new() { id, id2, id3 };
            sortedById.Sort((a, b) => string.Compare(a, b, StringComparison.Ordinal));

            benefitDefinitionTypes!.Count.Should().Be(limitNumber);
            benefitDefinitionTypes!.First()?.id.Should().Be(sortedById.First());
        }

        [Fact]
        public async Task GIVEN_three_benefitDefinitionTypes_WHEN_skip_it_by_one_THEN_returns_two()
        {
            (string id, _) = await BenefitDefinitionType.Create();
            (string id2, _) = await BenefitDefinitionType.Create();
            (string id3, _) = await BenefitDefinitionType.Create();

            sortInput sort = new() { fieldName = "id", type = "asc" };
            IReadOnlyCollection<benefitDefinitionType?>? benefitDefinitionTypes = await SearchMultipleBenefitDefinitionType(
                new benefitDefinitionTypeWhereInput { id_in = new[] { id, id2, id3 } },
                sort,
                null,
                1);

            List<string> sortedById = new() { id, id2, id3 };
            sortedById.Sort((a, b) => string.Compare(a, b, StringComparison.Ordinal));

            benefitDefinitionTypes!.Count.Should().Be(2);
            benefitDefinitionTypes!.First()?.id.Should().Be(sortedById.Skip(1).First());
            benefitDefinitionTypes!.Skip(1).First()?.id.Should().Be(sortedById.Skip(2).First());
        }

        [Fact]
        public async Task GIVEN_benefitDefinitionType_added_WHEN_update_this_benefitDefinitionType_THEN_succeed_and_updated()
        {
            (string id, _) = await BenefitDefinitionType.Create();

            updateBenefitDefinitionTypeInput updateBenefitDefinitionTypeCommand = new()
            {
                benefitDefinitionTypeId = id,
                name = "updated benefit type",
                description = "updated description",
                status = "updated status",
                businessId = CreateNewGuid()
            };

            await UpdateBenefitDefinitionType(updateBenefitDefinitionTypeCommand);

            benefitDefinitionType? benefitDefinitionType = await SearchBenefitDefinitionTypeById(id);

            benefitDefinitionType.ShouldHaveSameValuesAsInput(updateBenefitDefinitionTypeCommand);
        }

        [Fact]
        public async Task GIVEN_benefitDefinitionType_added_WHEN_delete_this_benefitDefinitionType_THEN_succeed_and_deletes()
        {
            (string id, _) = await BenefitDefinitionType.Create();

            deleteBenefitDefinitionTypeInput removeBenefitDefinitionTypeCommand = new() { benefitDefinitionTypeId = id };

            await DeleteBenefitDefinitionType(removeBenefitDefinitionTypeCommand);

            benefitDefinitionType? removedBenefitDefinitionType = await SearchBenefitDefinitionTypeById(id);

            removedBenefitDefinitionType.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_benefitDefinitionType_WHEN_create_another_one_THEN_success()
        {
            createBenefitDefinitionTypeInput input = new()
            {
                name = "new benefit type",
                description = "new description",
                businessId = CreateNewGuid()
            };
            await BenefitDefinitionType.Create(input);

            string id = await BenefitDefinitionType.Create(input with { businessId = CreateNewGuid() });

            benefitDefinitionType? secondBenefitDefinitionType = await SearchBenefitDefinitionTypeById(id);

            secondBenefitDefinitionType.Should().NotBeNull();
        }

        [Fact]
        public async Task GIVEN_benefitDefinitionType_WHEN_create_another_one_with_same_businessId_THEN_failure()
        {
            createBenefitDefinitionTypeInput input = new()
            {
                name = "new benefit type",
                description = "new description",
                businessId = CreateNewGuid()
            };
            await BenefitDefinitionType.Create(input);

            await input
                .Invoking(_ => BenefitDefinitionType.Create(input))
                .Should()
                .ThrowAsync<Exception>();
        }

        [Fact]
        public async Task GIVEN_benefitDefinition_WHEN_create_benefitDefinitionType_with_same_businessId_THEN_failure()
        {
            createBenefitDefinitionInput input1 = new()
            {
                name = "new benefit",
                description = "new description",
                businessId = CreateNewGuid()
            };
            await BenefitDefinition.Create(input1);

            createBenefitDefinitionTypeInput input2 = new()
            {
                name = "new benefit",
                description = "new description",
                businessId = input1.businessId
            };

            await input2
                .Invoking(_ => BenefitDefinitionType.Create(input2))
                .Should()
                .ThrowAsync<Exception>();
        }

        async Task UpdateBenefitDefinitionType(updateBenefitDefinitionTypeInput input)
        {
            string mutation = new MutationBuilder()
                .updateBenefitDefinitionType(new MutationBuilder.updateBenefitDefinitionTypeArgs(input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        async Task DeleteBenefitDefinitionType(deleteBenefitDefinitionTypeInput input)
        {
            string mutation = new MutationBuilder()
                .deleteBenefitDefinitionType(new MutationBuilder.deleteBenefitDefinitionTypeArgs(input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        Task<benefitDefinitionType?> SearchBenefitDefinitionTypeById(string id) =>
            SearchBenefitDefinitionType(new benefitDefinitionTypeWhereInput { id_in = new[] { id } });

        async Task<benefitDefinitionType?> SearchBenefitDefinitionType(benefitDefinitionTypeWhereInput where, sortInput? sort = null, int? limit = null, int? skip = null)
        {
            string query = CreateQuery(where, sort, limit, skip);

            benefitDefinitionTypes response = await _client.SendQueryAsync<benefitDefinitionTypes>(query);
            return response.list!.SingleOrDefault();
        }

        async Task<IReadOnlyCollection<benefitDefinitionType?>?> SearchMultipleBenefitDefinitionType(benefitDefinitionTypeWhereInput where, sortInput? sort = null, int? limit = null, int? skip = null)
        {
            string query = CreateQuery(where, sort, limit, skip);

            benefitDefinitionTypes response = await _client.SendQueryAsync<benefitDefinitionTypes>(query);
            return response.list?.ToArray();
        }

        private static string CreateQuery(benefitDefinitionTypeWhereInput where, sortInput? sort = null, int? limit = null, int? skip = null) =>
            new QueryBuilder()
                .benefitDefinitionTypes(new QueryBuilder.benefitDefinitionTypesArgs(where: where, sort: sort, limit: limit, skip: skip), new benefitDefinitionTypesBuilder()
                    .list(new benefitDefinitionTypeBuilder()
                        .id()
                        .name()
                        .status()
                        .description()
                        .businessId()))
                .Build();
    }
}