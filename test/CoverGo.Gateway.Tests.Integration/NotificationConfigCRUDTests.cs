﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class NotificationConfigCRUDTests : TestsBase
    {
        public NotificationConfigCRUDTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_notification_config_WHEN_create_THEN_it_is_queryable()
        {
            string clientIdGuid = CreateNewGuid();
            string emailName = "testName";
            string emailSender = "<EMAIL>";
            List<string> ccs = new() { "<EMAIL>", "<EMAIL>" };
            List<string> bccs = new() { "<EMAIL>", "<EMAIL>" };

            createNotificationConfigInput input = new()
            {
                clientId = clientIdGuid,
                emailConfig = new emailConfigInput
                {
                    emailName = emailName,
                    emailSender = emailSender,
                    ccs = ccs!,
                    bccs = bccs!
                }
            };
            await CreateNotificationConfig(input);
            notificationConfig? config = await SearchNotificationConfig(clientIdGuid);

            config!.clientId.Should().Be(clientIdGuid);
            config.emailConfig!.emailName.Should().Be(emailName);
            config.emailConfig.emailSender.Should().Be(emailSender);
            config.emailConfig.ccs.Should().BeEquivalentTo(ccs);
            config.emailConfig.bccs.Should().BeEquivalentTo(bccs);
        }

        [Fact]
        public async Task GIVEN_notification_config_WHEN_delete_THEN_it_is_not_queryable()
        {
            string clientIdGuid = CreateNewGuid();
            createNotificationConfigInput input = new() { clientId = clientIdGuid };

            await CreateNotificationConfig(input);
            await DeleteNotificationConfig(clientIdGuid);
            notificationConfig? config = await SearchNotificationConfig(clientIdGuid);

            config.Should().BeNull();
        }

        async Task CreateNotificationConfig(createNotificationConfigInput input)
        {
            string mutation = new MutationBuilder()
                .createNotificationConfig(new MutationBuilder.createNotificationConfigArgs(input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        async Task DeleteNotificationConfig(string clientId)
        {
            string mutation = new MutationBuilder()
                .deleteNotificationConfig(new MutationBuilder.deleteNotificationConfigArgs(clientId), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        async Task<notificationConfig?> SearchNotificationConfig(string? clientId = null)
        {
            notificationConfigWhere where = new()
            {
                clientId = clientId
            };
            string query = new QueryBuilder()
                .notificationConfigs(new QueryBuilder.notificationConfigsArgs(where), new notificationConfigBuilder()
                    .clientId()
                    .emailConfig(new emailConfigBuilder()
                        .emailName()
                        .emailSender()
                        .ccs()
                        .bccs()
                    )
                )
                .Build();

            IEnumerable<notificationConfig> response = await _client.SendQueryAsync<IEnumerable<notificationConfig>>(query);
            return response.FirstOrDefault();
        }
    }
}
