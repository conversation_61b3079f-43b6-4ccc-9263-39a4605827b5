using System.Net.Http.Headers;
using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.FubonIntegrate
{
    public class FubonFpsTests : TestsBase
    {
        public FubonFpsTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_input_json_WHEN_query_fubon_qr_code_integrate_THEN_returns_success()
        {
            _client.HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", await GetFubonUatLoginToken());

            stringResult result = await GenerateFpsQrAsync();
            result.errors.Should().BeNull();
            result.status.Should().Be("success");
        }

        Task<stringResult> GenerateFpsQrAsync()
        {
            string query = new QueryBuilder()
                .integrate(
                    new QueryBuilder.integrateArgs(
                        "fubonFpsQRCode",
                        @"{\""imageWidth\"":100,\""imageHeight\"":100,\""txnAmount\"":101.19,\""billNumber\"":\""string\""}"),
                    new stringResultBuilder()
                        .status()
                        .errors()
                        .value()
                    )
                .Build();

            return _client.SendQueryAsync<stringResult>(query);
        }

        async Task<string> GetFubonUatLoginToken()
        {
            string token = await Token.Fetch(new QueryBuilder.token_2Args("fubon_uat", "covergo_crm", "<EMAIL>", "123123123"));

            return token;
        }
    }
}