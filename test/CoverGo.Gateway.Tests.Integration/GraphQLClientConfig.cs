using System;
using Microsoft.Extensions.Configuration;

namespace CoverGo.Gateway.Tests.Integration
{
    public class GraphQLClientConfig
    {
        private Uri _uri;
        private string _gatewayGraphQlUrl;

        public string GatewayGraphQLUrl
        {
            get => _gatewayGraphQlUrl;
            set
            {
                _gatewayGraphQlUrl = value;
                _uri = new Uri(value);
            }
        }

        public string TenantId { get; set; }
        public string ClientId { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }

        public string BaseUrl => $"{_uri.Scheme}://{_uri.Authority}";

        public static readonly GraphQLClientConfig Local = new()
        {
            GatewayGraphQLUrl = "http://localhost:60060/graphql",
            TenantId = "covergo",
            ClientId = "admin",
            Username = "<EMAIL>",
            Password = "V9K&KobcZO3"
        };
        private const string EnvironmentVariablePrefix = "GATEWAY_INTEGRATION_TEST-";
        public GraphQLClientConfig Load(string prefix = EnvironmentVariablePrefix)
        {
            ConfigurationBuilder builder = new();
            builder.AddEnvironmentVariables(source =>
            {
                source.Prefix = prefix;
            });
            builder.Build().Bind(this);
            return this;
        }
    }

}