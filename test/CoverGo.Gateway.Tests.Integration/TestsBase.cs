using System;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;

using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using CoverGo.Gateway.Tests.Integration.Permissions;

using FluentAssertions;

using GraphQL.Client.Http;
using GraphQL.Client.Serializer.Newtonsoft;

using Xunit;
using Xunit.Abstractions;
using Entity = CoverGo.Gateway.Tests.Integration.Crud.Entity;
using Login = CoverGo.Gateway.Tests.Integration.Crud.Login;
using Task = System.Threading.Tasks.Task;
using UiSchema = CoverGo.Gateway.Tests.Integration.Crud.UiSchema;

// Temporary disable Parallel to make tests stable.
[assembly: CollectionBehavior(DisableTestParallelization = true)]

#pragma warning disable 8618
namespace CoverGo.Gateway.Tests.Integration
{
    public abstract class TestsBase : IAsyncLifetime
    {
        public TestsBase(ITestOutputHelper output)
        {
            TestContext.AsyncLocalTestContext.Value = output;
        }

        public async Task InitializeAsync()
        {
            _client = await Setup.CreateGraphQLHttpClient();

            BenefitDefinitionType = new BenefitDefinitionType(_client);
            BenefitDefinition = new BenefitDefinition(_client);
            Case = new Case(_client);
            DataSchema = new DataSchema(_client);
            Offer = new Offer(_client);
            Proposal = new Proposal(_client);
            Script = new Script(_client);
            Product = new Product(_client);
            PermissionGroup = new PermissionGroup(_client);
            Login = new Login(_client);
            Token = new Token(_client);
            Entity = new Entity(_client);
            ProductType = new ProductType(_client);
            Template = new Template(_client);
            Policy = new Policy(_client);
            Claim = new Claim(_client);
            Jacket = new Jacket(_client);
            GuaranteeOfPayment = new GuaranteeOfPayment(_client);
            UiSchema = new UiSchema(_client);
            PermissionSchema = new PermissionSchema(_client);
            ServiceItem = new ServiceItem(_client);
            ServiceItemAgreedFee = new ServiceItemAgreedFee(_client);
            PanelProviderTier = new PanelProviderTier(_client);
            Transaction = new Transaction(_client);
            Disability = new Disability(_client);
            Diagnosis = new Diagnosis(_client);
            Treatment = new Treatment(_client);
            NegotiatedRate = new NegotiatedRate(_client);
            Pricing = new Pricing(_client);
            FileSystem = new FileSystem(_client);
            Advisor = new Advisor(_client);
            Notification = new Notification(_client);
            App = new App(_client);
        }

        public Task DisposeAsync() => Task.CompletedTask;

        protected GraphQLHttpClient _client;

        protected static string CreateNewGuid() =>
            Guid.NewGuid().ToString();

        protected static async Task<GraphQLHttpClient> CreateGraphQLHttpClient(string clientId, string username, string password)
        {
            GraphQLClientConfig config = new()
            {
                GatewayGraphQLUrl = "http://localhost:60060/graphql",
                TenantId = "covergo",
                ClientId = clientId!,
                Username = username!,
                Password = password!
            };
            config = config.Load();

            QueryBuilder.token_2Args tokenArgs = new(config.TenantId, config.ClientId, config.Username, config.Password);
            HttpClient httpClient = new()
            {
                Timeout = TimeSpan.FromMinutes(5),
            };
            GraphQLHttpClient client =
                new(new GraphQLHttpClientOptions { EndPoint = new Uri(config.GatewayGraphQLUrl) },
                    new NewtonsoftJsonSerializer(), httpClient);
            client.HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", await new Token(client).Fetch(tokenArgs));
            return client;
        }

        protected static async Task<HttpClient> CreateRestHttpClient(string clientId, string username, string password)
        {
            GraphQLClientConfig config = new()
            {
                GatewayGraphQLUrl = "http://localhost:60060/graphql",
                TenantId = "covergo",
                ClientId = clientId!,
                Username = username!,
                Password = password!
            };
            config = config.Load();

            QueryBuilder.token_2Args tokenArgs = new(config.TenantId, config.ClientId, config.Username, config.Password);
            GraphQLHttpClient client = new(config.GatewayGraphQLUrl, new NewtonsoftJsonSerializer());
            AuthenticationHeaderValue? authHeader = new AuthenticationHeaderValue("Bearer", await new Token(client).Fetch(tokenArgs));
            HttpClient? restClient = new ();
            restClient.DefaultRequestHeaders.Authorization = authHeader;
            restClient.BaseAddress = new Uri(client!.Options!.EndPoint!.ToString().Split("/graphql")[0]);
            return restClient;
        }

        protected async Task<GraphQLHttpClient> BuildClientWithoutPermission()
        {
            var context = await new PermissionTestContextBuilder()
                .Build();

            return context.Client;
        }

        protected async Task ShouldNotHavePermission(Func<Task> action, string permission)
        {
            (await action.Should().ThrowAsync<Exception>())
                .Where(e => e.Message.Contains($"missing '{permission}' permission."));
        }

        protected static string GetResource(string name)
        {
            string? directory = Path.GetDirectoryName(new Uri(typeof(TestsBase).Assembly.Location).LocalPath);
            string path = Path.Combine(directory!, "Resources", name + ".json");

            return File.ReadAllText(path);
        }
        protected static string GetPricingScript()
        {
            string? directory = Path.GetDirectoryName(new Uri(typeof(TestsBase).Assembly.Location).LocalPath);
            string path = Path.Combine(directory!, "Resources", "PricingScript.txt");
            return File.ReadAllText(path).Replace(Environment.NewLine, "\\n").Replace("\"", "\\\"");
        }

        protected BenefitDefinitionType BenefitDefinitionType { get; private set; }
        protected BenefitDefinition BenefitDefinition { get; private set; }
        protected Case Case { get; private set; }
        protected DataSchema DataSchema { get; private set; }
        protected Offer Offer { get; private set; }
        protected Proposal Proposal { get; private set; }
        protected Script Script { get; private set; }
        protected Product Product { get; private set; }
        protected PermissionGroup PermissionGroup { get; private set; }
        protected Login Login { get; private set; }
        protected Token Token { get; private set; }
        protected Entity Entity { get; private set; }
        protected ProductType ProductType { get; private set; }
        protected Template Template { get; private set; }
        protected Policy Policy { get; private set; }
        protected Claim Claim { get; private set; }
        protected Jacket Jacket { get; private set; }
        protected GuaranteeOfPayment GuaranteeOfPayment { get; private set; }
        protected UiSchema UiSchema { get; private set; }
        protected PermissionSchema PermissionSchema { get; private set; }
        protected ServiceItem ServiceItem { get; private set; }
        protected ServiceItemAgreedFee ServiceItemAgreedFee { get; private set; }
        protected PanelProviderTier PanelProviderTier { get; private set; }
        protected Transaction Transaction { get; private set; }
        protected Disability Disability { get; private set; }
        protected Diagnosis Diagnosis { get; private set; }
        protected Treatment Treatment { get; private set; }
        protected NegotiatedRate NegotiatedRate { get; private set; }
        protected Crud.Pricing Pricing { get; private set; }
        protected FileSystem FileSystem { get; private set; }
        protected Advisor Advisor { get; private set; }
        protected Notification Notification { get; private set; }
        protected App App { get; private set; }
    }
}
#pragma warning restore 8618