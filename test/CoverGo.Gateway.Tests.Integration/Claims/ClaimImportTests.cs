﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Claims
{
    public class ClaimImportTests : TestsBase
    {
        public ClaimImportTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact(Skip = "This test fails due to mongoinit.js")]
        public async Task GIVEN_importClaimsCommand_WHEN_import_THEN_claims_are_imported()
        {

            var importClaimsCommand = new claims_ImportClaimsCommandInput()
            {
                importClaimCommands = new List<claims_ImportClaimCommandInput?>()
                {
                    new()
                    {
                        claimId = Guid.NewGuid().ToString(),
                        createClaimEventCommands = new List<claims_CreateClaimEventCommandInput?>()
                        {
                            new()
                            {
                                eventType = claims_ClaimEventType.CREATE,
                                claimantId = Guid.NewGuid().ToString(),
                                status = "CREATED",
                                fields = "{}",
                                timeStamp = new DateTime(2021,01,01,01,00,00, DateTimeKind.Utc)
                            },
                            new()
                            {
                                eventType = claims_ClaimEventType.UPDATE,
                                updateType = "approved",
                                status = "APPROVED",
                                timeStamp = new DateTime(2021,01,01,02,00,00, DateTimeKind.Utc)
                            }
                        }
                    },
                    new()
                    {
                        claimId = Guid.NewGuid().ToString(),
                        createClaimEventCommands = new List<claims_CreateClaimEventCommandInput?>()
                        {
                            new()
                            {
                                eventType = claims_ClaimEventType.CREATE,
                                claimantId = Guid.NewGuid().ToString(),
                                status = "CREATED",
                                fields = "{}",
                                timeStamp = new DateTime(2021,01,01,01,00,00, DateTimeKind.Utc)
                            },
                            new()
                            {
                                eventType = claims_ClaimEventType.UPDATE,
                                updateType = "approved",
                                status = "APPROVED",
                                timeStamp = new DateTime(2021,01,01,02,00,00, DateTimeKind.Utc)
                            },
                            new()
                            {
                                eventType = claims_ClaimEventType.UPDATE,
                                updateType = "settled",
                                status = "SETTLED",
                                timeStamp = new DateTime(2021,01,01,03,00,00, DateTimeKind.Utc)
                            }
                        }
                    }
                }
            };

            await ClaimImport(importClaimsCommand);

            claim claim1 = await Claim.FindById(importClaimsCommand.importClaimCommands.First()?.claimId!);
            claim claim2 = await Claim.FindById(importClaimsCommand.importClaimCommands.Last()?.claimId!);

            claim1.Should().NotBeNull();
            claim1.status.Should().Be("APPROVED");
            claim1.fields.Should().Be("{}");

            claim2.Should().NotBeNull();
            claim2.status.Should().Be("SETTLED");
            claim2.fields.Should().Be("{}");
        }

        private async Task ClaimImport(claims_ImportClaimsCommandInput input)
        {
            string? mutation = new MutationBuilder()
                .claimMutationImport(new MutationBuilder.claimMutationImportArgs(input),
                    new claims_ResultBuilder().status())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }
    }
}