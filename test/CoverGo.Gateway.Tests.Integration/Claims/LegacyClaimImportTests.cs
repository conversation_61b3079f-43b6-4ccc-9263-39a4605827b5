using CoverGo.Gateway.Client;
using FluentAssertions;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Claims
{
    public class LegacyClaimImportTests : TestsBase
    {
        private static readonly MongoClient _mongoClient = new(Environment.GetEnvironmentVariable("DATABASE_CONNECT_STRING") ?? "mongodb://localhost:27017/?readPreference=primary&appname=MongoDB%20Compass&ssl=false");

        public LegacyClaimImportTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_claim_inserted_through_db_WHEN_query_by_id_THEN_claim_found()
        {
            string claimId = CreateNewGuid();
            IMongoCollection<BsonDocument>? db = _mongoClient.GetDatabase("claims").GetCollection<BsonDocument>("covergo-claims");

            string claimJson = $@"{{""_id"":""{claimId}"",
            ""id"":""{claimId}"",
            ""createdAt"":""2021-12-30T03:30:19.647Z"",
            ""lastModifiedAt"":""2022-01-04T06:52:44.489Z"",
            ""createdById"":""60bf193eda157cda7753cb83"",
            ""lastModifiedById"":""60bf193eda157cda7753cb83"",
            ""issuerNumber"":""AK52T6B2PB"",
            ""policyId"":""6e84ee1a-a373-45ae-8d7a-75ca0b6145f4"",
            ""claimantId"":""7da73143-7694-4e9b-8bf4-6be29d8be862"",
            ""benefitClaims"":[],
            ""facts"":[],
            ""status"":""SETTLED"",
            ""guaranteeOfPaymentIds"":[],
            ""notes"":[],
            ""stakeholders"":[],
            ""attachments"":[],
            ""fields"":{{""claimType"":""panel_claim"",
            ""importBatchId"":""CE993261315"",
            ""invoiceNo"":""000344"",
            ""attendingDoctor"":""Dr. Chan X"",
            ""diagnosis"":[{{""disability"":{{""id"":""d3b2fa93-fcc5-4ee5-b658-dd9f37fbedde"",
            ""name"":""B""}},
            ""treatment"":{{""id"":""baa573a0-2aab-624f-8df2-3cc8de1ffa75"",
            ""name"":""Excision of oesophageal lesion / destruction of lesion or tissue of oesophagus,
             cervical approach"",
            ""code"":""A101""}},
            ""referralLetters"":[]}}],
            ""incurredDate"":""2021-07-13"",
            ""nameOfInsuranceCompany"":""Apollo 1172"",
            ""claimApprover"":{{""id"":""614d4656612dac309b8731e3"",
            ""email"":""<EMAIL>"",
            ""associatedUser"":{{""id"":""72927a77-dbb8-49f9-9638-153c382e9a64"",
            ""name"":""APOLLO 113301 ""}},
            ""name"":""APOLLO 113301 ""}},
            ""claimHandler"":{{""email"":""<EMAIL>"",
            ""id"":""60bf193eda157cda7753cb83"",
            ""associatedUser"":null,
            ""name"":""admin""}},
            ""formReceivedDate"":""2021-12-30"",
            ""billingDetail"":[{{""name"":""GP Visit"",
            ""billedAmount"":300,
            ""currency"":""HK$"",
            ""benefitList"":[{{""disability"":{{""id"":""d3b2fa93-fcc5-4ee5-b658-dd9f37fbedde"",
            ""name"":""B""}},
            ""benefitNode"":[{{""name"":""GP Visit"",
            ""value"":""plan|OP|GP"",
            ""amount"":5000,
            ""limitType"":""amountLimit"",
            ""limitPerTime"":""perVisit""}}],
            ""assessedAmount"":300,
            ""exGratiaAmount"":null,
            ""incurredAmount"":300,
            ""showExGratia"":false}}]}}],
            ""dateOfAdvice"":""2022-01-04T06:52:43.266Z"",
            ""asOf"":""2022-01-04T06:52:32.648Z"",
            ""claimAmountSummary"":[{{""asOf"":""2022-01-04T06:52:32.648Z"",
            ""exportBatchId"":{{""id"":""b284850c-40ee-4488-b938-a329fbf05513"",
            ""name"":""CE596913352""}},
            ""isSettled"":true,
            ""settledDate"":""2022-01-04T06:52:43.266Z""}}],
            ""lastSettledDate"":""2022-01-04T06:52:43.266Z"",
            ""settledPanel"":true,
            ""settledMember"":true}},
            ""importBatchId"":{{""_id"":""530c5ad3-4e8f-4bae-9e8e-c44e898a22b3"",
            ""id"":""530c5ad3-4e8f-4bae-9e8e-c44e898a22b3"",
            ""name"":""CE993261315""}},
            ""exportBatchIds"":[{{""_id"":""b284850c-40ee-4488-b938-a329fbf05513"",
            ""id"":""b284850c-40ee-4488-b938-a329fbf05513"",
            ""name"":""CE596913352""}}],
            ""panelId"":""90de6482-51f0-4a5a-ae0e-4beb1cd85270"",
            ""lock"":false}}";

            await db.InsertOneAsync(BsonDocument.Parse(claimJson));

            claim claim = await Claim.FindById(claimId);
            claim!.id.Should().Be(claimId);
        }

        [Fact]
        public async Task GIVEN_claim_inserted_through_db_WHEN_query_with_asOf_THEN_claim_found()
        {
            string claimId = CreateNewGuid();
            IMongoDatabase? db = _mongoClient.GetDatabase("claims");
            IMongoCollection<BsonDocument>? claimsCollection = db.GetCollection<BsonDocument>("covergo-claims");
            var claimJson = $@"{{""_id"":""{claimId}"",
            ""id"":""{claimId}"",
            ""createdAt"":""2021-12-30T03:30:19.647Z"",
            ""lastModifiedAt"":""2022-01-04T06:52:44.489Z"",
            ""createdById"":""60bf193eda157cda7753cb83"",
            ""lastModifiedById"":""60bf193eda157cda7753cb83"",
            ""issuerNumber"":""AK52T6B2PB"",
            ""policyId"":""6e84ee1a-a373-45ae-8d7a-75ca0b6145f4"",
            ""claimantId"":""7da73143-7694-4e9b-8bf4-6be29d8be862"",
            ""benefitClaims"":[],
            ""facts"":[],
            ""status"":""SETTLED"",
            ""guaranteeOfPaymentIds"":[],
            ""notes"":[],
            ""stakeholders"":[],
            ""attachments"":[],
            ""fields"":{{""claimType"":""panel_claim"",
            ""importBatchId"":""CE993261315"",
            ""invoiceNo"":""000344"",
            ""attendingDoctor"":""Dr. Chan X"",
            ""diagnosis"":[{{""disability"":{{""id"":""d3b2fa93-fcc5-4ee5-b658-dd9f37fbedde"",
            ""name"":""B""}},
            ""treatment"":{{""id"":""baa573a0-2aab-624f-8df2-3cc8de1ffa75"",
            ""name"":""Excision of oesophageal lesion / destruction of lesion or tissue of oesophagus,
             cervical approach"",
            ""code"":""A101""}},
            ""referralLetters"":[]}}],
            ""incurredDate"":""2021-07-13"",
            ""nameOfInsuranceCompany"":""Apollo 1172"",
            ""claimApprover"":{{""id"":""614d4656612dac309b8731e3"",
            ""email"":""<EMAIL>"",
            ""associatedUser"":{{""id"":""72927a77-dbb8-49f9-9638-153c382e9a64"",
            ""name"":""APOLLO 113301 ""}},
            ""name"":""APOLLO 113301 ""}},
            ""claimHandler"":{{""email"":""<EMAIL>"",
            ""id"":""60bf193eda157cda7753cb83"",
            ""associatedUser"":null,
            ""name"":""admin""}},
            ""formReceivedDate"":""2021-12-30"",
            ""billingDetail"":[{{""name"":""GP Visit"",
            ""billedAmount"":300,
            ""currency"":""HK$"",
            ""benefitList"":[{{""disability"":{{""id"":""d3b2fa93-fcc5-4ee5-b658-dd9f37fbedde"",
            ""name"":""B""}},
            ""benefitNode"":[{{""name"":""GP Visit"",
            ""value"":""plan|OP|GP"",
            ""amount"":5000,
            ""limitType"":""amountLimit"",
            ""limitPerTime"":""perVisit""}}],
            ""assessedAmount"":300,
            ""exGratiaAmount"":null,
            ""incurredAmount"":300,
            ""showExGratia"":false}}]}}],
            ""dateOfAdvice"":""2022-01-04T06:52:43.266Z"",
            ""asOf"":""2022-01-04T06:52:32.648Z"",
            ""claimAmountSummary"":[{{""asOf"":""2022-01-04T06:52:32.648Z"",
            ""exportBatchId"":{{""id"":""b284850c-40ee-4488-b938-a329fbf05513"",
            ""name"":""CE596913352""}},
            ""isSettled"":true,
            ""settledDate"":""2022-01-04T06:52:43.266Z""}}],
            ""lastSettledDate"":""2022-01-04T06:52:43.266Z"",
            ""settledPanel"":true,
            ""settledMember"":true}},
            ""importBatchId"":{{""_id"":""530c5ad3-4e8f-4bae-9e8e-c44e898a22b3"",
            ""id"":""530c5ad3-4e8f-4bae-9e8e-c44e898a22b3"",
            ""name"":""CE993261315""}},
            ""exportBatchIds"":[{{""_id"":""b284850c-40ee-4488-b938-a329fbf05513"",
            ""id"":""b284850c-40ee-4488-b938-a329fbf05513"",
            ""name"":""CE596913352""}}],
            ""panelId"":""90de6482-51f0-4a5a-ae0e-4beb1cd85270"",
            ""lock"":false}}";
            await claimsCollection.InsertOneAsync(BsonDocument.Parse(claimJson));

            IMongoCollection<BsonDocument>? eventsCollection = db.GetCollection<BsonDocument>("covergo-events");
            string claimEventJson = $@"{{""claimId"":""{claimId}"",
            ""type"":""legacyImport"",
            ""values"":{{""_id"":""{claimId}"",
            ""id"":""{claimId}"",
            ""createdAt"":""2021-12-30T03:30:19.647Z"",
            ""lastModifiedAt"":""2022-01-04T06:52:44.489Z"",
            ""createdById"":""60bf193eda157cda7753cb83"",
            ""lastModifiedById"":""60bf193eda157cda7753cb83"",
            ""issuerNumber"":""AK52T6B2PB"",
            ""policyId"":""6e84ee1a-a373-45ae-8d7a-75ca0b6145f4"",
            ""claimantId"":""7da73143-7694-4e9b-8bf4-6be29d8be862"",
            ""benefitClaims"":[],
            ""facts"":[],
            ""status"":""SETTLED"",
            ""guaranteeOfPaymentIds"":[],
            ""notes"":[],
            ""stakeholders"":[],
            ""attachments"":[],
            ""fields"":{{""claimType"":""panel_claim"",
            ""importBatchId"":""CE993261315"",
            ""invoiceNo"":""000344"",
            ""attendingDoctor"":""Dr. Chan X"",
            ""diagnosis"":[{{""disability"":{{""id"":""d3b2fa93-fcc5-4ee5-b658-dd9f37fbedde"",
            ""name"":""B""}},
            ""treatment"":{{""id"":""baa573a0-2aab-624f-8df2-3cc8de1ffa75"",
            ""name"":""Excision of oesophageal lesion / destruction of lesion or tissue of oesophagus,
             cervical approach"",
            ""code"":""A101""}},
            ""referralLetters"":[]}}],
            ""incurredDate"":""2021-07-13"",
            ""nameOfInsuranceCompany"":""Apollo 1172"",
            ""claimApprover"":{{""id"":""614d4656612dac309b8731e3"",
            ""email"":""<EMAIL>"",
            ""associatedUser"":{{""id"":""72927a77-dbb8-49f9-9638-153c382e9a64"",
            ""name"":""APOLLO 113301 ""}},
            ""name"":""APOLLO 113301 ""}},
            ""claimHandler"":{{""email"":""<EMAIL>"",
            ""id"":""60bf193eda157cda7753cb83"",
            ""associatedUser"":null,
            ""name"":""admin""}},
            ""formReceivedDate"":""2021-12-30"",
            ""billingDetail"":[{{""name"":""GP Visit"",
            ""billedAmount"":300,
            ""currency"":""HK$"",
            ""benefitList"":[{{""disability"":{{""id"":""d3b2fa93-fcc5-4ee5-b658-dd9f37fbedde"",
            ""name"":""B""}},
            ""benefitNode"":[{{""name"":""GP Visit"",
            ""value"":""plan|OP|GP"",
            ""amount"":5000,
            ""limitType"":""amountLimit"",
            ""limitPerTime"":""perVisit""}}],
            ""assessedAmount"":300,
            ""exGratiaAmount"":null,
            ""incurredAmount"":300,
            ""showExGratia"":false}}]}}],
            ""dateOfAdvice"":""2022-01-04T06:52:43.266Z"",
            ""asOf"":""2022-01-04T06:52:32.648Z"",
            ""claimAmountSummary"":[{{""asOf"":""2022-01-04T06:52:32.648Z"",
            ""exportBatchId"":{{""id"":""b284850c-40ee-4488-b938-a329fbf05513"",
            ""name"":""CE596913352""}},
            ""isSettled"":true,
            ""settledDate"":""2022-01-04T06:52:43.266Z""}}],
            ""lastSettledDate"":""2022-01-04T06:52:43.266Z"",
            ""settledPanel"":true,
            ""settledMember"":true}},
            ""importBatchId"":{{""_id"":""530c5ad3-4e8f-4bae-9e8e-c44e898a22b3"",
            ""id"":""530c5ad3-4e8f-4bae-9e8e-c44e898a22b3"",
            ""name"":""CE993261315""}},
            ""exportBatchIds"":[{{""_id"":""b284850c-40ee-4488-b938-a329fbf05513"",
            ""id"":""b284850c-40ee-4488-b938-a329fbf05513"",
            ""name"":""CE596913352""}}],
            ""panelId"":""90de6482-51f0-4a5a-ae0e-4beb1cd85270"",
            ""lock"":false}}}}";

            BsonDocument claimEventDocument = BsonDocument.Parse(claimEventJson);
            claimEventDocument["timestamp"] = DateTime.UtcNow;
            await eventsCollection.InsertOneAsync(claimEventDocument);

            claim claim = await Claim.FindById(claimId, DateTimeOffset.UtcNow.AddDays(1));
            claim!.id.Should().Be(claimId);
        }
    }
}