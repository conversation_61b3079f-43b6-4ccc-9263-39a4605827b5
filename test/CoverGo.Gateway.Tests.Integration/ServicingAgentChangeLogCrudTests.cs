using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration;

public class ServicingAgentChangeLogCrudTests : TestsBase
{
    public ServicingAgentChangeLogCrudTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task GIVEN_servicing_agent_change_log_input_WHEN_create_THEN_created_successfully()
    {
        string id = CreateNewGuid();
        users_ServicingAgentChangeLogUpsertInput createInput =
            new()
            {
                id = id,
                policyName = "policyName",
                policyNo = "policyNo",
                currentServicingAgentCode = "currentServicingAgentCode",
                currentServicingAgentName = "currentServicingAgentName",
                newServicingAgentCode = "newServicingAgentCode",
                newServicingAgentName = "newServicingAgentName",
                status = "success",
                errorCode = "0",
                message = "message"
            };
        users_GenericServicingAgentChangeLog3BatchInput batch = new()
        {
            create = new List<users_ServicingAgentChangeLogUpsertInput?> { createInput }
        };

        await ServicingAgentChangeLogsBatch(batch);

        users_GenericServicingAgentChangeLog8QueryInterface created = await ServicingAgentChangeLogsGet(id);

        created.totalCount.Should().Be(1);
        created.list.Should().NotBeEmpty();

        users_ServicingAgentChangeLog? servicingAgentChangeLog = created.list!.ElementAt(0);

        servicingAgentChangeLog!.id.Should().Be(id);
        servicingAgentChangeLog.policyName.Should().Be("policyName");
        servicingAgentChangeLog.policyNo.Should().Be("policyNo");
        servicingAgentChangeLog.currentServicingAgentCode.Should().Be("currentServicingAgentCode");
        servicingAgentChangeLog.currentServicingAgentName.Should().Be("currentServicingAgentName");
        servicingAgentChangeLog.newServicingAgentCode.Should().Be("newServicingAgentCode");
        servicingAgentChangeLog.newServicingAgentName.Should().Be("newServicingAgentName");
        servicingAgentChangeLog.status.Should().Be("success");
        servicingAgentChangeLog.errorCode.Should().Be("0");
        servicingAgentChangeLog.message.Should().Be("message");
    }

    [Fact]
    public async Task GIVEN_servicing_agent_change_log_input_WHEN_update_THEN_updated_successfully()
    {
        string id = CreateNewGuid();
        users_ServicingAgentChangeLogUpsertInput createInput = new()
        {
            id = id,
            policyName = "policyName",
            policyNo = "policyNo",
            currentServicingAgentCode = "currentServicingAgentCode",
            currentServicingAgentName = "currentServicingAgentName",
            newServicingAgentCode = "newServicingAgentCode",
            newServicingAgentName = "newServicingAgentName",
            status = "success",
            errorCode = "0",
            message = "message"
        };

        users_GenericServicingAgentChangeLog3BatchInput batch = new()
        {
            create = new List<users_ServicingAgentChangeLogUpsertInput?> { createInput }
        };

        await ServicingAgentChangeLogsBatch(batch);

        users_GenericServicingAgentChangeLog8QueryInterface servicingAgentChangeLogs1 =
            await ServicingAgentChangeLogsGet(id);

        users_ServicingAgentChangeLogUpsertInput updateInput = new()
        {
            id = id,
            policyName = "policyName-updated",
            policyNo = "policyNo-updated",
            currentServicingAgentCode = "currentServicingAgentCode-updated",
            currentServicingAgentName = "currentServicingAgentName-updated",
            newServicingAgentCode = "newServicingAgentCode-updated",
            newServicingAgentName = "newServicingAgentName-updated",
            status = "failed",
            errorCode = "1",
            message = "message-updated"
        };
        batch = new users_GenericServicingAgentChangeLog3BatchInput
        {
            update = new List<users_ServicingAgentChangeLogUpsertInput?> { updateInput }
        };

        await ServicingAgentChangeLogsBatch(batch);

        users_GenericServicingAgentChangeLog8QueryInterface servicingAgentChangeLogs =
            await ServicingAgentChangeLogsGet(id);

        servicingAgentChangeLogs.totalCount.Should().Be(1);
        servicingAgentChangeLogs.list.Should().NotBeEmpty();

        users_ServicingAgentChangeLog? servicingAgentChangeLog = servicingAgentChangeLogs.list!.ElementAt(0);

        servicingAgentChangeLog!.id.Should().Be(id);
        servicingAgentChangeLog.policyName.Should().Be("policyName-updated");
        servicingAgentChangeLog.policyNo.Should().Be("policyNo-updated");
        servicingAgentChangeLog.currentServicingAgentCode.Should().Be("currentServicingAgentCode-updated");
        servicingAgentChangeLog.currentServicingAgentName.Should().Be("currentServicingAgentName-updated");
        servicingAgentChangeLog.newServicingAgentCode.Should().Be("newServicingAgentCode-updated");
        servicingAgentChangeLog.newServicingAgentName.Should().Be("newServicingAgentName-updated");
        servicingAgentChangeLog.status.Should().Be("failed");
        servicingAgentChangeLog.errorCode.Should().Be("1");
        servicingAgentChangeLog.message.Should().Be("message-updated");
    }

    [Fact]
    public async Task GIVEN_servicing_agent_change_log_input_WHEN_delete_THEN_success()
    {
        string id = CreateNewGuid();
        users_ServicingAgentChangeLogUpsertInput createInput = new() { id = id };
        users_GenericServicingAgentChangeLog3BatchInput batch = new()
        {
            create = new List<users_ServicingAgentChangeLogUpsertInput?> { createInput }
        };

        await ServicingAgentChangeLogsBatch(batch);

        users_GenericServicingAgentChangeLog8QueryInterface servicingAgentChangeLogs =
            await ServicingAgentChangeLogsGet(id);

        servicingAgentChangeLogs.totalCount.Should().Be(1);
        servicingAgentChangeLogs.list.Should().NotBeEmpty();

        users_ServicingAgentChangeLogUpsertInput deleteInput = new() { id = id };
        batch = new users_GenericServicingAgentChangeLog3BatchInput
        {
            delete = new List<users_ServicingAgentChangeLogUpsertInput?> { deleteInput }
        };
        await ServicingAgentChangeLogsBatch(batch);

        servicingAgentChangeLogs = await ServicingAgentChangeLogsGet(id);
        servicingAgentChangeLogs.totalCount.Should().Be(0);
        servicingAgentChangeLogs.list.Should().BeEmpty();
    }

    private async Task<users_GenericServicingAgentChangeLog8QueryInterface> ServicingAgentChangeLogsGet(string id)
    {
        string query = new QueryBuilder()
            .servicingAgentChangeLogsQuery(
                new QueryBuilder.servicingAgentChangeLogsQueryArgs(
                    where: new users_GenericServicingAgentChangeLogQueryInput
                    {
                        where = new users_GenericServicingAgentChangeLogFilterInput
                        {
                            where = new users_ServicingAgentChangeLogFilterInput { id = id }
                        }
                    }), new users_GenericServicingAgentChangeLog8QueryInterfaceBuilder()
                    .totalCount()
                    .list(new users_ServicingAgentChangeLogBuilder().WithAllFields()))
            .Build();

        return await _client.SendQueryAsync<users_GenericServicingAgentChangeLog8QueryInterface>(query);
    }


    private async Task ServicingAgentChangeLogsBatch(users_GenericServicingAgentChangeLog3BatchInput batch)
    {
        string mutation = new MutationBuilder()
            .servicingAgentChangeLogMutationBatch(new MutationBuilder.servicingAgentChangeLogMutationBatchArgs(batch: batch), new users_ResultBuilder()
                .status()
                .errors())
            .Build();

        await _client.SendMutationAndEnsureSuccessAsync(mutation);
    }
}