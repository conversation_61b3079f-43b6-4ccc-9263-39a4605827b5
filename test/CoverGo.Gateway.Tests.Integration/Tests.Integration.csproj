<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>

    <RootNamespace>CoverGo.Gateway.Tests.Integration</RootNamespace>
    <AssemblyName>CoverGo.Gateway.Tests.Integration</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoFixture.Xunit2" />
    <PackageReference Include="AutoFixture" />
    <PackageReference Include="CoverGo.MongoUtils" />
    <PackageReference Include="CoverGo.GraphQL.Client" />
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="JunitXml.TestLogger" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Moq" />
    <PackageReference Include="xunit" />
    <PackageReference Include="GraphQL.Client" />
    <PackageReference Include="GraphQL.Client.Serializer.Newtonsoft" />
    <PackageReference Include="xunit.runner.visualstudio">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Contrib-2.Microsoft.AspNetCore.Identity.MongoDB" />
    <PackageReference Include="Scrutor" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\CoverGo.Gateway.Client\Client.csproj" />
    <ProjectReference Include="..\..\src\CoverGo.Gateway.Application\CoverGo.Gateway.Application.csproj" />
    <ProjectReference Include="..\..\test\CoverGo.Gateway.Tests.GatewayClient\CoverGo.Gateway.Tests.GatewayClient.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Resources\PermissionsJsonSchema_Field1.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\PermissionsJsonSchema.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Attachment.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <None Remove="testsettings.json" />
    <None Remove="testsettings.Development.Tests.json" />
    <None Remove="testsettings.Staging.CI.json" />
    <Content Include="testsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="testsettings.Development.Tests.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <DependentUpon>testsettings.json</DependentUpon>
    </Content>
    <Content Include="testsettings.Staging.CI.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <DependentUpon>testsettings.json</DependentUpon>
    </Content>
  </ItemGroup>

</Project>
