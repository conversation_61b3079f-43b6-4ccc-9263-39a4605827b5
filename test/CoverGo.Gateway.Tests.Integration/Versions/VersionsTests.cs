using System.Collections.Generic;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Versions;

public class VersionsTests : TestsBase
{
    public VersionsTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task WHEN_get_app_versions_THEN_should_get_app_versions()
    {
        var versions = await GetVersions();

        versions.Should().HaveCountGreaterOrEqualTo(2);
        versions.Should().NotContain(v => string.IsNullOrEmpty(v.application));
    }

    public async Task<IEnumerable<applicationVersionInfo>> GetVersions()
    {
        var query = new QueryBuilder().versions(
                new ApplicationVersionInfoListGraphTypeBuilder()
                    .WithAllFields())
            .Build();

        var result = await _client.SendQueryAsync<ApplicationVersionInfoListGraphType>(query);

        return result!.list!;
    }
}