using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.NegotiatedRates
{
    public class NegotiatedRatesTests : TestsBase
    {
        public NegotiatedRatesTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_negotiated_rates_inputs_WHEN_create_THEN_created_successfully()
        {
            string organizationId = await CreateOrganization();
            string negotiatedItemId = await CreateNegotiatedItem();
            string organizationNegotiatedItemId = await CreateOrganizationNegotiatedItem(organizationId, negotiatedItemId);
            string agreedFeeId = await CreateAgreedFees(organizationNegotiatedItemId);
            string agreedFeeGroupId = await CreateAgreedFeeGroup(agreedFeeId);
            string organizationProviderTierId = await CreateOrganizationProviderTier(organizationId, agreedFeeGroupId);

            users_GenericOrganizationProviderTier8QueryInterface organizationProviderTiers = await OrganizationProviderTiersGet(organizationProviderTierId);

            organizationProviderTiers.list.Should().HaveCount(1);
            users_OrganizationProviderTier? organizationProviderTier = organizationProviderTiers.list!.Single();

            organizationProviderTier!.id.Should().Be(organizationProviderTierId);
            organizationProviderTier.name.Should().Be("organizationProviderTierName");
            organizationProviderTier.description.Should().Be("organizationProviderTierDescription");
            organizationProviderTier.fields.Should().Be("{}");

            organizationProviderTier.organization!.name.Should().Be("org-1");
            organizationProviderTier.organization.fields.Should().Be("{}");

            organizationProviderTier.agreedFeeGroups.Should().HaveCount(1);
            users_AgreedFeeGroup? agreedFeeGroup = organizationProviderTier.agreedFeeGroups!.First();
            agreedFeeGroup!.id.Should().Be(agreedFeeGroupId);
            agreedFeeGroup.from.Should().Be(new DateTime(2021, 10, 10).ToUniversalTime());
            agreedFeeGroup.to.Should().Be(new DateTime(2021, 11, 11).ToUniversalTime());
            agreedFeeGroup.remarks.Should().Be("remarks");

            agreedFeeGroup.agreedFees.Should().HaveCount(1);
            users_AgreedFee? agreedFee = agreedFeeGroup.agreedFees!.First();
            agreedFee!.id.Should().Be(agreedFeeId);
            agreedFee!.rate.Should().Be(10);
            agreedFee!.currency.Should().Be("HKD");

            agreedFee.organizationNegotiatedItem!.id.Should().Be(organizationNegotiatedItemId);
            agreedFee.organizationNegotiatedItem!.organizationCode.Should().Be("organizationCode");
            agreedFee.organizationNegotiatedItem.organization!.entityId.Should().Be(organizationId);
            agreedFee.organizationNegotiatedItem.organization!.name.Should().Be("org-1");
            agreedFee.organizationNegotiatedItem.organization!.fields.Should().Be("{}");

            agreedFee.organizationNegotiatedItem.negotiatedItem!.id.Should().Be(negotiatedItemId);
            agreedFee.organizationNegotiatedItem.negotiatedItem!.fields.Should().Be("{}");
        }

        private async Task<string> CreateOrganizationProviderTier(string organizationId, string agreedFeeGroupId)
        {
            string id = CreateNewGuid();

            var createInput = new users_OrganizationProviderTierUpsertInput { id = id, name = "organizationProviderTierName", description = "organizationProviderTierDescription", fields = "{}", organizationId = organizationId, agreedFeeGroupIds = new List<string?>{agreedFeeGroupId}};
            var batch = new users_GenericOrganizationProviderTier3BatchInput { create = new List<users_OrganizationProviderTierUpsertInput?> { createInput } };
            await NegotiatedRate.OrganizationProviderTiersBatch(batch);

            return id;
        }

        private async Task<string> CreateAgreedFeeGroup(string agreedFeeId)
        {
            string id = CreateNewGuid();

            DateTime from = new DateTime(2021, 10, 10).ToUniversalTime();
            DateTime to = new DateTime(2021, 11, 11).ToUniversalTime();
            var createInput = new users_AgreedFeeGroupUpsertInput { id = id, from = from, to = to, remarks = "remarks",agreedFeeIds = new List<string?>{agreedFeeId}};
            var batch = new users_GenericAgreedFeeGroup3BatchInput { create = new List<users_AgreedFeeGroupUpsertInput?> { createInput } };

            await NegotiatedRate.AgreedFeeGroupsBatch(batch);

            return id;
        }

        private async Task<string> CreateAgreedFees(string organizationNegotiatedItemId)
        {
            string id = CreateNewGuid();

            var createInput = new users_AgreedFeeUpsertInput { id = id, rate = 10, currency = "HKD", organizationNegotiatedItemId = organizationNegotiatedItemId};
            var batch = new users_GenericAgreedFee3BatchInput { create = new List<users_AgreedFeeUpsertInput?> { createInput } };
            await NegotiatedRate.AgreedFeesBatch(batch);

            return id;
        }

        private async Task<string> CreateOrganizationNegotiatedItem(string organizationId, string negotiatedItemId)
        {
            string id = CreateNewGuid();

            var createInput = new users_OrganizationNegotiatedItemUpsertInput { id = id, organizationCode = "organizationCode", organizationId = organizationId, negotiatedItemId = negotiatedItemId};
            var batch = new users_GenericOrganizationNegotiatedItem3BatchInput { create = new List<users_OrganizationNegotiatedItemUpsertInput?> { createInput } };
            await NegotiatedRate.OrganizationNegotiatedItemsBatch(batch);

            return id;
        }

        private async Task<string> CreateOrganization() => await Entity.CreateOrganization(new createOrganizationInput {nameFormat = "org-1", fields = "{}"});

        private async Task<string> CreateNegotiatedItem()
        {
            string id = CreateNewGuid();

            var createInput = new users_NegotiatedItemUpsertInput { id = id, fields = @"{}".Escape() };
            var batch = new users_GenericNegotiatedItem3BatchInput { create = new List<users_NegotiatedItemUpsertInput?> { createInput } };
            await NegotiatedRate.NegotiatedItemsBatch(batch);

            return id;
        }

        private async Task<users_GenericOrganizationProviderTier8QueryInterface> OrganizationProviderTiersGet(string id)
        {
            string query = new QueryBuilder()
                .organizationProviderTiersQuery(new QueryBuilder.organizationProviderTiersQueryArgs(where: new users_GenericOrganizationProviderTierQueryInput { where = new users_GenericOrganizationProviderTierFilterInput { where = new users_OrganizationProviderTierFilterInput { id = id } } }), new users_GenericOrganizationProviderTier8QueryInterfaceBuilder()
                    .totalCount()
                    .list(new users_OrganizationProviderTierBuilder()
                        .id()
                        .name()
                        .description()
                        .fields()
                        .organization(new users_OrganizationBuilder()
                            .entityId()
                            .name()
                            .fields())
                        .agreedFeeGroups(new users_AgreedFeeGroupBuilder()
                            .id()
                            .@from()
                            .to()
                            .remarks()
                            .agreedFees(new users_AgreedFeeBuilder()
                                .id()
                                .rate()
                                .currency()
                                .organizationNegotiatedItem(new users_OrganizationNegotiatedItemBuilder()
                                    .id()
                                    .organizationCode()
                                    .organization(new users_OrganizationBuilder()
                                        .entityId()
                                        .name()
                                        .fields())
                                    .negotiatedItem(new users_NegotiatedItemBuilder()
                                        .id()
                                        .fields()))))))
                .Build();

            return await _client.SendQueryAsync<users_GenericOrganizationProviderTier8QueryInterface>(query);
        }
    }
}