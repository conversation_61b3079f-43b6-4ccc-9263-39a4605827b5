using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.NegotiatedRates
{
    public class OrganizationProviderTierCrudTests : TestsBase
    {
        public OrganizationProviderTierCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_organization_provider_tier_input_WHEN_create_THEN_created_successfully()
        {
            string id = CreateNewGuid();
            var createInput = new users_OrganizationProviderTierUpsertInput { id = id, name = "name", description = "description", fields = "{}"};
            var batch = new users_GenericOrganizationProviderTier3BatchInput { create = new List<users_OrganizationProviderTierUpsertInput?> { createInput } };

            await NegotiatedRate.OrganizationProviderTiersBatch(batch);

            users_GenericOrganizationProviderTier8QueryInterface? created = await OrganizationProviderTiersGet(id);

            created.totalCount.Should().Be(1);
            created.list.Should().NotBeEmpty();

            users_OrganizationProviderTier? organizationProviderTier = created.list!.ElementAt(0);

            organizationProviderTier!.id.Should().Be(id);
            organizationProviderTier!.name.Should().Be("name");
            organizationProviderTier!.description.Should().Be("description");
            organizationProviderTier!.fields.Should().Be("{}");
        }

        [Fact]
        public async Task GIVEN_organization_provider_tier_input_WHEN_update_THEN_updated_successfully()
        {
            string id = CreateNewGuid();
            var createInput = new users_OrganizationProviderTierUpsertInput() { id = id, name = "name", description = "description", fields = "{}"};
            var batch = new users_GenericOrganizationProviderTier3BatchInput
            {
                create = new List<users_OrganizationProviderTierUpsertInput?>
                {
                    createInput
                }
            };

            await NegotiatedRate.OrganizationProviderTiersBatch(batch);

            var updateInput = new users_OrganizationProviderTierUpsertInput
            {
                id = id,
                name = "name-updated",
                description = "description-updated",
                fields = @"{""test"":""test""}".Escape()
            };
            batch = new users_GenericOrganizationProviderTier3BatchInput { update = new List<users_OrganizationProviderTierUpsertInput?> { updateInput } };

            await NegotiatedRate.OrganizationProviderTiersBatch(batch);

            users_GenericOrganizationProviderTier8QueryInterface? organizationProviderTiers = await OrganizationProviderTiersGet(id);

            organizationProviderTiers.totalCount.Should().Be(1);
            organizationProviderTiers.list.Should().NotBeEmpty();

            users_OrganizationProviderTier? organizationProviderTier = organizationProviderTiers.list!.ElementAt(0);

            organizationProviderTier!.id.Should().Be(id);
            organizationProviderTier!.name.Should().Be(updateInput.name);
            organizationProviderTier!.description.Should().Be(updateInput.description);
            organizationProviderTier!.fields.Should().Be("{\"test\":\"test\"}");
        }

        [Fact]
        public async Task GIVEN_organization_provider_tier_input_WHEN_delete_THEN_success()
        {
            string id = CreateNewGuid();
            var createInput = new users_OrganizationProviderTierUpsertInput { id = id };
            var batch = new users_GenericOrganizationProviderTier3BatchInput { create = new List<users_OrganizationProviderTierUpsertInput?> { createInput } };

            await NegotiatedRate.OrganizationProviderTiersBatch(batch);

            users_GenericOrganizationProviderTier8QueryInterface? organizationProviderTiers = await OrganizationProviderTiersGet(id);

            organizationProviderTiers.totalCount.Should().Be(1);
            organizationProviderTiers.list.Should().NotBeEmpty();

            var deleteInput = new users_OrganizationProviderTierUpsertInput { id = id };
            batch = new users_GenericOrganizationProviderTier3BatchInput { delete = new List<users_OrganizationProviderTierUpsertInput?> { deleteInput } };
            await NegotiatedRate.OrganizationProviderTiersBatch(batch);

            organizationProviderTiers = await OrganizationProviderTiersGet(id);
            organizationProviderTiers.totalCount.Should().Be(0);
            organizationProviderTiers.list.Should().BeEmpty();
        }

        private async Task<users_GenericOrganizationProviderTier8QueryInterface> OrganizationProviderTiersGet(string id)
        {
            string query = new QueryBuilder()
                .organizationProviderTiersQuery(new QueryBuilder.organizationProviderTiersQueryArgs(where: new users_GenericOrganizationProviderTierQueryInput { where = new users_GenericOrganizationProviderTierFilterInput { where = new users_OrganizationProviderTierFilterInput { id = id } } }), new users_GenericOrganizationProviderTier8QueryInterfaceBuilder()
                    .totalCount()
                    .list(new users_OrganizationProviderTierBuilder()
                        .id()
                        .name()
                        .description()
                        .fields()))
                .Build();

            return await _client.SendQueryAsync<users_GenericOrganizationProviderTier8QueryInterface>(query);
        }
    }
}