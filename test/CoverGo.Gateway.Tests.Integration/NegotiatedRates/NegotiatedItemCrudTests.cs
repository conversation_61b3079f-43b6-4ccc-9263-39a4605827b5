using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.NegotiatedRates
{
    public class NegotiatedItemCrudTests : TestsBase
    {
        public NegotiatedItemCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_negotiated_item_input_WHEN_create_THEN_created_successfully()
        {
            string id = CreateNewGuid();
            var createInput = new users_NegotiatedItemUpsertInput { id = id, fields = @"{""test"":""test""}".Escape() };
            var batch = new users_GenericNegotiatedItem3BatchInput { create = new List<users_NegotiatedItemUpsertInput?> { createInput } };

            await NegotiatedRate.NegotiatedItemsBatch(batch);

            users_GenericNegotiatedItem8QueryInterface? created = await NegotiatedItemsGet(id);

            created.totalCount.Should().Be(1);
            created.list.Should().NotBeEmpty();

            users_NegotiatedItem? negotiatedItem = created.list!.ElementAt(0);

            negotiatedItem!.id.Should().Be(id);
            negotiatedItem!.fields.Should().Be("{\"test\":\"test\"}");
        }

        [Fact]
        public async Task GIVEN_negotiated_item_input_WHEN_update_THEN_updated_successfully()
        {
            string id = CreateNewGuid();
            var createInput = new users_NegotiatedItemUpsertInput() { id = id, fields = "{}" };
            var batch = new users_GenericNegotiatedItem3BatchInput
            {
                create = new List<users_NegotiatedItemUpsertInput?>
                {
                    createInput
                }
            };

            await NegotiatedRate.NegotiatedItemsBatch(batch);

            var updateInput = new users_NegotiatedItemUpsertInput
            {
                id = id,
                fields = @"{""test"":""test""}".Escape()
            };
            batch = new users_GenericNegotiatedItem3BatchInput { update = new List<users_NegotiatedItemUpsertInput?> { updateInput } };

            await NegotiatedRate.NegotiatedItemsBatch(batch);

            users_GenericNegotiatedItem8QueryInterface? negotiatedItems = await NegotiatedItemsGet(id);

            negotiatedItems.totalCount.Should().Be(1);
            negotiatedItems.list.Should().NotBeEmpty();

            users_NegotiatedItem? negotiatedItem = negotiatedItems.list!.ElementAt(0);

            negotiatedItem!.id.Should().Be(id);
            negotiatedItem!.fields.Should().Be("{\"test\":\"test\"}");
        }

        [Fact]
        public async Task GIVEN_negotiated_item_input_WHEN_delete_THEN_success()
        {
            string id = CreateNewGuid();
            var createInput = new users_NegotiatedItemUpsertInput { id = id, fields = "{}" };
            var batch = new users_GenericNegotiatedItem3BatchInput { create = new List<users_NegotiatedItemUpsertInput?> { createInput } };

            await NegotiatedRate.NegotiatedItemsBatch(batch);

            users_GenericNegotiatedItem8QueryInterface? negotiatedItems = await NegotiatedItemsGet(id);

            negotiatedItems.totalCount.Should().Be(1);
            negotiatedItems.list.Should().NotBeEmpty();

            var deleteInput = new users_NegotiatedItemUpsertInput { id = id };
            batch = new users_GenericNegotiatedItem3BatchInput { delete = new List<users_NegotiatedItemUpsertInput?> { deleteInput } };
            await NegotiatedRate.NegotiatedItemsBatch(batch);

            negotiatedItems = await NegotiatedItemsGet(id);
            negotiatedItems.totalCount.Should().Be(0);
            negotiatedItems.list.Should().BeEmpty();
        }

        private async Task<users_GenericNegotiatedItem8QueryInterface> NegotiatedItemsGet(string id)
        {
            string query = new QueryBuilder()
                .negotiatedItemsQuery(new QueryBuilder.negotiatedItemsQueryArgs(where: new users_GenericNegotiatedItemQueryInput { where = new users_GenericNegotiatedItemFilterInput { where = new users_NegotiatedItemFilterInput { id = id } } }), new users_GenericNegotiatedItem8QueryInterfaceBuilder()
                    .totalCount()
                    .list(new users_NegotiatedItemBuilder()
                        .id()
                        .fields()))
                .Build();

            return await _client.SendQueryAsync<users_GenericNegotiatedItem8QueryInterface>(query);
        }


    }
}