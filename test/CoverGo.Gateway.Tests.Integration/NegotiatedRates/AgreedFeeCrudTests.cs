using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.NegotiatedRates
{
    public class AgreedFeeCrudTests : TestsBase
    {
        public AgreedFeeCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_agreed_fee_input_WHEN_create_THEN_created_successfully()
        {
            string id = CreateNewGuid();
            var createInput = new users_AgreedFeeUpsertInput { id = id, rate = 10, currency = "HKD"};
            var batch = new users_GenericAgreedFee3BatchInput { create = new List<users_AgreedFeeUpsertInput?> { createInput } };

            await NegotiatedRate.AgreedFeesBatch(batch);

            users_GenericAgreedFee8QueryInterface? created = await AgreedFeesGet(id);

            created.totalCount.Should().Be(1);
            created.list.Should().NotBeEmpty();

            users_AgreedFee? agreedFee = created.list!.ElementAt(0);

            agreedFee!.id.Should().Be(id);
            agreedFee!.rate.Should().Be(10);
            agreedFee!.currency.Should().Be("HKD");
        }

        [Fact]
        public async Task GIVEN_agreed_fee_input_WHEN_update_THEN_updated_successfully()
        {
            string id = CreateNewGuid();
            var createInput = new users_AgreedFeeUpsertInput() { id = id, rate = 10, currency = "HKD"};
            var batch = new users_GenericAgreedFee3BatchInput
            {
                create = new List<users_AgreedFeeUpsertInput?>
                {
                    createInput
                }
            };

            await NegotiatedRate.AgreedFeesBatch(batch);

            var updateInput = new users_AgreedFeeUpsertInput
            {
                id = id,
                rate = 20, currency = "USD"
            };
            batch = new users_GenericAgreedFee3BatchInput { update = new List<users_AgreedFeeUpsertInput?> { updateInput } };

            await NegotiatedRate.AgreedFeesBatch(batch);

            users_GenericAgreedFee8QueryInterface? agreedFees = await AgreedFeesGet(id);

            agreedFees.totalCount.Should().Be(1);
            agreedFees.list.Should().NotBeEmpty();

            users_AgreedFee? agreedFee = agreedFees.list!.ElementAt(0);

            agreedFee!.id.Should().Be(id);
            agreedFee!.rate.Should().Be(20);
            agreedFee!.currency.Should().Be("USD");
        }

        [Fact]
        public async Task GIVEN_agreed_fee_input_WHEN_delete_THEN_success()
        {
            string id = CreateNewGuid();
            var createInput = new users_AgreedFeeUpsertInput { id = id };
            var batch = new users_GenericAgreedFee3BatchInput { create = new List<users_AgreedFeeUpsertInput?> { createInput } };

            await NegotiatedRate.AgreedFeesBatch(batch);

            users_GenericAgreedFee8QueryInterface? agreedFees = await AgreedFeesGet(id);

            agreedFees.totalCount.Should().Be(1);
            agreedFees.list.Should().NotBeEmpty();

            var deleteInput = new users_AgreedFeeUpsertInput { id = id };
            batch = new users_GenericAgreedFee3BatchInput { delete = new List<users_AgreedFeeUpsertInput?> { deleteInput } };
            await NegotiatedRate.AgreedFeesBatch(batch);

            agreedFees = await AgreedFeesGet(id);
            agreedFees.totalCount.Should().Be(0);
            agreedFees.list.Should().BeEmpty();
        }

        private async Task<users_GenericAgreedFee8QueryInterface> AgreedFeesGet(string id)
        {
            string query = new QueryBuilder()
                .agreedFeesQuery(new QueryBuilder.agreedFeesQueryArgs(where: new users_GenericAgreedFeeQueryInput { where = new users_GenericAgreedFeeFilterInput { where = new users_AgreedFeeFilterInput { id = id } } }), new users_GenericAgreedFee8QueryInterfaceBuilder()
                    .totalCount()
                    .list(new users_AgreedFeeBuilder()
                        .id()
                        .rate()
                        .currency()))
                .Build();

            return await _client.SendQueryAsync<users_GenericAgreedFee8QueryInterface>(query);
        }
    }
}