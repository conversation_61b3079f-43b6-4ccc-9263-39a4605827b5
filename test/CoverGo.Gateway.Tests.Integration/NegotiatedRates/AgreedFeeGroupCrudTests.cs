using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.NegotiatedRates
{
    public class AgreedFeeGroupCrudTests : TestsBase
    {
        public AgreedFeeGroupCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_agreed_fee_group_input_WHEN_create_THEN_created_successfully()
        {
            string id = CreateNewGuid();
            DateTime from = new DateTime(2021, 10, 10).ToUniversalTime();
            DateTime to = new DateTime(2021, 11, 11).ToUniversalTime();
            var createInput = new users_AgreedFeeGroupUpsertInput { id = id, from = from, to = to};
            var batch = new users_GenericAgreedFeeGroup3BatchInput { create = new List<users_AgreedFeeGroupUpsertInput?> { createInput } };

            await NegotiatedRate.AgreedFeeGroupsBatch(batch);

            users_GenericAgreedFeeGroup8QueryInterface? created = await AgreedFeeGroupsGet(id);

            created.totalCount.Should().Be(1);
            created.list.Should().NotBeEmpty();

            users_AgreedFeeGroup? agreedFeeGroup = created.list!.ElementAt(0);

            agreedFeeGroup!.id.Should().Be(id);
            agreedFeeGroup!.from.Should().Be(from);
            agreedFeeGroup!.to.Should().Be(to);
        }

        [Fact]
        public async Task GIVEN_agreed_fee_group_input_WHEN_update_THEN_updated_successfully()
        {
            string id = CreateNewGuid();
            DateTime from = new DateTime(2021, 10, 10).ToUniversalTime();
            DateTime to = new DateTime(2021, 11, 11).ToUniversalTime();
            var createInput = new users_AgreedFeeGroupUpsertInput() { id = id,  from = from, to = to};
            var batch = new users_GenericAgreedFeeGroup3BatchInput
            {
                create = new List<users_AgreedFeeGroupUpsertInput?>
                {
                    createInput
                }
            };

            await NegotiatedRate.AgreedFeeGroupsBatch(batch);

            DateTime newFrom = new DateTime(2022, 10, 10).ToUniversalTime();
            DateTime newTo = new DateTime(2022, 11, 11).ToUniversalTime();
            var updateInput = new users_AgreedFeeGroupUpsertInput
            {
                id = id,
                from = newFrom,
                to = newTo
            };
            batch = new users_GenericAgreedFeeGroup3BatchInput { update = new List<users_AgreedFeeGroupUpsertInput?> { updateInput } };

            await NegotiatedRate.AgreedFeeGroupsBatch(batch);

            users_GenericAgreedFeeGroup8QueryInterface? agreedFeeGroups = await AgreedFeeGroupsGet(id);

            agreedFeeGroups.totalCount.Should().Be(1);
            agreedFeeGroups.list.Should().NotBeEmpty();

            users_AgreedFeeGroup? agreedFeeGroup = agreedFeeGroups.list!.ElementAt(0);

            agreedFeeGroup!.id.Should().Be(id);
            agreedFeeGroup!.from.Should().Be(newFrom);
            agreedFeeGroup!.to.Should().Be(newTo);
        }

        [Fact]
        public async Task GIVEN_agreed_fee_group_input_WHEN_delete_THEN_success()
        {
            string id = CreateNewGuid();
            var createInput = new users_AgreedFeeGroupUpsertInput { id = id };
            var batch = new users_GenericAgreedFeeGroup3BatchInput { create = new List<users_AgreedFeeGroupUpsertInput?> { createInput } };

            await NegotiatedRate.AgreedFeeGroupsBatch(batch);

            users_GenericAgreedFeeGroup8QueryInterface? agreedFeeGroups = await AgreedFeeGroupsGet(id);

            agreedFeeGroups.totalCount.Should().Be(1);
            agreedFeeGroups.list.Should().NotBeEmpty();

            var deleteInput = new users_AgreedFeeGroupUpsertInput { id = id };
            batch = new users_GenericAgreedFeeGroup3BatchInput { delete = new List<users_AgreedFeeGroupUpsertInput?> { deleteInput } };
            await NegotiatedRate.AgreedFeeGroupsBatch(batch);

            agreedFeeGroups = await AgreedFeeGroupsGet(id);
            agreedFeeGroups.totalCount.Should().Be(0);
            agreedFeeGroups.list.Should().BeEmpty();
        }

        private async Task<users_GenericAgreedFeeGroup8QueryInterface> AgreedFeeGroupsGet(string id)
        {
            string query = new QueryBuilder()
                .agreedFeeGroupsQuery(new QueryBuilder.agreedFeeGroupsQueryArgs(where: new users_GenericAgreedFeeGroupQueryInput { where = new users_GenericAgreedFeeGroupFilterInput { where = new users_AgreedFeeGroupFilterInput { id = id } } }), new users_GenericAgreedFeeGroup8QueryInterfaceBuilder()
                    .totalCount()
                    .list(new users_AgreedFeeGroupBuilder()
                        .id()
                        .from()
                        .to()))
                .Build();

            return await _client.SendQueryAsync<users_GenericAgreedFeeGroup8QueryInterface>(query);
        }
    }
}