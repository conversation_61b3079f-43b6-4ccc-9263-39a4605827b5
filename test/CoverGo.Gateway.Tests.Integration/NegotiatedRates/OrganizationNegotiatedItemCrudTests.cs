using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.NegotiatedRates
{
    public class OrganizationNegotiatedItemCrudTests : TestsBase
    {
        public OrganizationNegotiatedItemCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_organization_negotiated_item_input_WHEN_create_THEN_created_successfully()
        {
            string id = CreateNewGuid();
            var createInput = new users_OrganizationNegotiatedItemUpsertInput { id = id, organizationCode = "test"};
            var batch = new users_GenericOrganizationNegotiatedItem3BatchInput { create = new List<users_OrganizationNegotiatedItemUpsertInput?> { createInput } };

            await NegotiatedRate.OrganizationNegotiatedItemsBatch(batch);

            users_GenericOrganizationNegotiatedItem8QueryInterface? created = await OrganizationNegotiatedItemsGet(id);

            created.totalCount.Should().Be(1);
            created.list.Should().NotBeEmpty();

            users_OrganizationNegotiatedItem? organizationNegotiatedItem = created.list!.ElementAt(0);

            organizationNegotiatedItem!.id.Should().Be(id);
            organizationNegotiatedItem!.organizationCode.Should().Be("test");
        }

        [Fact]
        public async Task GIVEN_organization_negotiated_item_input_WHEN_update_THEN_updated_successfully()
        {
            string id = CreateNewGuid();
            var createInput = new users_OrganizationNegotiatedItemUpsertInput() { id = id, organizationCode = "test" };
            var batch = new users_GenericOrganizationNegotiatedItem3BatchInput
            {
                create = new List<users_OrganizationNegotiatedItemUpsertInput?>
                {
                    createInput
                }
            };

            await NegotiatedRate.OrganizationNegotiatedItemsBatch(batch);

            var updateInput = new users_OrganizationNegotiatedItemUpsertInput
            {
                id = id,
                organizationCode = "test-updated"
            };
            batch = new users_GenericOrganizationNegotiatedItem3BatchInput { update = new List<users_OrganizationNegotiatedItemUpsertInput?> { updateInput } };

            await NegotiatedRate.OrganizationNegotiatedItemsBatch(batch);

            users_GenericOrganizationNegotiatedItem8QueryInterface? organizationNegotiatedItems = await OrganizationNegotiatedItemsGet(id);

            organizationNegotiatedItems.totalCount.Should().Be(1);
            organizationNegotiatedItems.list.Should().NotBeEmpty();

            users_OrganizationNegotiatedItem? organizationNegotiatedItem = organizationNegotiatedItems.list!.ElementAt(0);

            organizationNegotiatedItem!.id.Should().Be(id);
            organizationNegotiatedItem!.organizationCode.Should().Be(updateInput.organizationCode);
        }

        [Fact]
        public async Task GIVEN_organization_negotiated_item_input_WHEN_delete_THEN_success()
        {
            string id = CreateNewGuid();
            var createInput = new users_OrganizationNegotiatedItemUpsertInput { id = id };
            var batch = new users_GenericOrganizationNegotiatedItem3BatchInput { create = new List<users_OrganizationNegotiatedItemUpsertInput?> { createInput } };

            await NegotiatedRate.OrganizationNegotiatedItemsBatch(batch);

            users_GenericOrganizationNegotiatedItem8QueryInterface? organizationNegotiatedItems = await OrganizationNegotiatedItemsGet(id);

            organizationNegotiatedItems.totalCount.Should().Be(1);
            organizationNegotiatedItems.list.Should().NotBeEmpty();

            var deleteInput = new users_OrganizationNegotiatedItemUpsertInput { id = id };
            batch = new users_GenericOrganizationNegotiatedItem3BatchInput { delete = new List<users_OrganizationNegotiatedItemUpsertInput?> { deleteInput } };
            await NegotiatedRate.OrganizationNegotiatedItemsBatch(batch);

            organizationNegotiatedItems = await OrganizationNegotiatedItemsGet(id);
            organizationNegotiatedItems.totalCount.Should().Be(0);
            organizationNegotiatedItems.list.Should().BeEmpty();
        }

        private async Task<users_GenericOrganizationNegotiatedItem8QueryInterface> OrganizationNegotiatedItemsGet(string id)
        {
            string query = new QueryBuilder()
                .organizationNegotiatedItemsQuery(new QueryBuilder.organizationNegotiatedItemsQueryArgs(where: new users_GenericOrganizationNegotiatedItemQueryInput { where = new users_GenericOrganizationNegotiatedItemFilterInput { where = new users_OrganizationNegotiatedItemFilterInput { id = id } } }), new users_GenericOrganizationNegotiatedItem8QueryInterfaceBuilder()
                    .totalCount()
                    .list(new users_OrganizationNegotiatedItemBuilder()
                        .id()
                        .organizationCode()
                        .organization(new users_OrganizationBuilder()
                            .entityId())
                        .negotiatedItem(new users_NegotiatedItemBuilder()
                            .id())))
                .Build();

            return await _client.SendQueryAsync<users_GenericOrganizationNegotiatedItem8QueryInterface>(query);
        }
    }
}