using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Templates;

public class EmailTemplateTests : TestsBase
{
    public EmailTemplateTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task GIVEN_an_email_template_with_templating_attachments_WHEN_render_template_THEN_should_render_email_contains_correct_attachments_successfully()
    {
        string emailName = $"email-{CreateNewGuid()}";
        await Template.CreateEmailMjmlTemplate(new createEmailMjmlTemplateInput() { name = emailName, mjml = $"<mjml> <mj-body> <mj-section> <mj-column> <mj-text> {emailName} </mj-text> </mj-column> </mj-section> </mj-body> </mjml>"});

        templateInterface createdEmailTemplate = await Find(new templateWhereInput() { name = emailName });

        string attachmentName = $"attachment-{CreateNewGuid()}";
        await Template.CreateWkhtmltopdfTemplate(
                new createWkhtmltopdfTemplateInput { html = "attachment", name = attachmentName });

        templateInterface createdAttachmentTemplate = await Find(new templateWhereInput() { name = attachmentName });

        await Template.AddEmailAttachmentTemplate(createdEmailTemplate.id!,
            new addEmailAttachmentTemplateInput
            {
                templateId = createdAttachmentTemplate.id, fileName = $"{attachmentName}.pdf"
            });

        emailRenderedResult renderedEmail = await Template.RenderEmailTemplate(createdEmailTemplate.id!, new templateRenderParametersInput { name = "name" }, true);

        renderedEmail.Should().NotBeNull();
        renderedEmail.value.Should().NotBeNull();
        renderedEmail.value!.html.Should().NotBeNull();
        renderedEmail.value!.html.Should().Contain(emailName);
        renderedEmail.value!.attachments.Should().NotBeNull();
        renderedEmail.value.attachments.Should().HaveCount(1);
        emailAttachment attachment = renderedEmail.value.attachments!.First()!;
        attachment.fileName.Should().Be($"{attachmentName}.pdf");
    }

    [Fact]
    public async Task GIVEN_an_email_template_with_templating_attachments_WHEN_render_template_with_override_attachments_input_THEN_should_render_email_contains_correct_attachments_successfully()
    {
        string emailName = $"email-{CreateNewGuid()}";
        await Template.CreateEmailMjmlTemplate(new createEmailMjmlTemplateInput() { name = emailName, mjml = $"<mjml> <mj-body> <mj-section> <mj-column> <mj-text> {emailName} </mj-text> </mj-column> </mj-section> </mj-body> </mjml>"});

        templateInterface createdEmailTemplate = await Find(new templateWhereInput() { name = emailName });

        string attachmentName = $"attachment-{CreateNewGuid()}";
        await Template.CreateWkhtmltopdfTemplate(
            new createWkhtmltopdfTemplateInput { html = "attachment", name = attachmentName });

        templateInterface createdAttachmentTemplate = await Find(new templateWhereInput() { name = attachmentName });

        await Template.AddEmailAttachmentTemplate(createdEmailTemplate.id!,
            new addEmailAttachmentTemplateInput
            {
                templateId = createdAttachmentTemplate.id, fileName = $"{attachmentName}.pdf"
            });

        string overrideAttachmentName = $"override-attachment-{CreateNewGuid()}";
        await Template.CreateWkhtmltopdfTemplate(
            new createWkhtmltopdfTemplateInput { html = "override-attachment", name = overrideAttachmentName });
        templateInterface createdOverrideAttachmentTemplate = await Find(new templateWhereInput() { name = overrideAttachmentName });


        emailRenderedResult renderedEmail = await Template.RenderEmailTemplate(createdEmailTemplate.id!, new templateRenderParametersInput
        {
            name = "name",
            overrideAttachmentTemplates = new List<overrideEmailAttachmentTemplateInput?>()
            {
                new()
                {
                    fileName = $"{overrideAttachmentName}.pdf",
                    templateId = createdOverrideAttachmentTemplate.id
                }
            }
        }, true);

        renderedEmail.Should().NotBeNull();
        renderedEmail.value.Should().NotBeNull();
        renderedEmail.value!.html.Should().NotBeNull();
        renderedEmail.value!.html.Should().Contain(emailName);
        renderedEmail.value!.attachments.Should().NotBeNull();
        renderedEmail.value.attachments.Should().HaveCount(1);
        emailAttachment attachment = renderedEmail.value.attachments!.First()!;
        attachment.fileName.Should().Be($"{overrideAttachmentName}.pdf");
    }


    [Fact]
    public async Task GIVEN_an_email_template_with_referenced_attachments_WHEN_render_template_THEN_should_render_email_contains_correct_attachments_successfully()
    {
        string emailName = $"email-{CreateNewGuid()}";
        await Template.CreateEmailMjmlTemplate(new createEmailMjmlTemplateInput() { name = emailName, mjml = $"<mjml> <mj-body> <mj-section> <mj-column> <mj-text> {emailName} </mj-text> </mj-column> </mj-section> </mj-body> </mjml>"});

        templateInterface createdEmailTemplate = await Find(new templateWhereInput() { name = emailName });

        string attachmentName = $"attachment-{CreateNewGuid()}";

        await InitializeFileSystem();
        await UploadAttachmentFile();

        await Template.AddEmailAttachmentTemplate(createdEmailTemplate.id!,
            new addEmailAttachmentReferenceInput()
            {
                filePath = "attachments/attachment.txt", fileName = $"{attachmentName}.pdf"
            });

        emailRenderedResult renderedEmail = await Template.RenderEmailTemplate(createdEmailTemplate.id!, new templateRenderParametersInput { name = "name" }, true);

        renderedEmail.Should().NotBeNull();
        renderedEmail.value.Should().NotBeNull();
        renderedEmail.value!.html.Should().NotBeNull();
        renderedEmail.value!.html.Should().Contain(emailName);
        renderedEmail.value!.attachments.Should().NotBeNull();
        renderedEmail.value.attachments.Should().HaveCount(1);
        emailAttachment attachment = renderedEmail.value.attachments!.First()!;
        attachment.fileName.Should().Be($"{attachmentName}.pdf");
    }

    [Fact]
    public async Task GIVEN_an_email_template_with_referenced_attachments_WHEN_render_template_with_override_attachments_input_THEN_should_render_email_contains_correct_attachments_successfully()
    {
        string emailName = $"email-{CreateNewGuid()}";
        await Template.CreateEmailMjmlTemplate(new createEmailMjmlTemplateInput() { name = emailName, mjml = $"<mjml> <mj-body> <mj-section> <mj-column> <mj-text> {emailName} </mj-text> </mj-column> </mj-section> </mj-body> </mjml>"});

        templateInterface createdEmailTemplate = await Find(new templateWhereInput() { name = emailName });

        string attachmentName = $"attachment-{CreateNewGuid()}";

        await InitializeFileSystem();
        await UploadAttachmentFile();

        await Template.AddEmailAttachmentTemplate(createdEmailTemplate.id!,
            new addEmailAttachmentReferenceInput()
            {
                filePath = "attachments/attachment.txt", fileName = $"{attachmentName}.pdf"
            });

        string overrideAttachmentName = $"override-attachment-{CreateNewGuid()}";
        await Template.CreateWkhtmltopdfTemplate(
            new createWkhtmltopdfTemplateInput { html = "override-attachment", name = overrideAttachmentName });
        templateInterface createdOverrideAttachmentTemplate = await Find(new templateWhereInput() { name = overrideAttachmentName });


        emailRenderedResult renderedEmail = await Template.RenderEmailTemplate(createdEmailTemplate.id!, new templateRenderParametersInput
        {
            name = "name",
            overrideAttachmentReferences = new List<overrideEmailAttachmentReferenceInput?>()
            {
                new()
                {
                    fileName = $"{overrideAttachmentName}.pdf",
                    filePath = "attachments/attachment.txt"
                }
            }
        }, true);

        renderedEmail.Should().NotBeNull();
        renderedEmail.value.Should().NotBeNull();
        renderedEmail.value!.html.Should().NotBeNull();
        renderedEmail.value!.html.Should().Contain(emailName);
        renderedEmail.value!.attachments.Should().NotBeNull();
        renderedEmail.value.attachments.Should().HaveCount(1);
        emailAttachment attachment = renderedEmail.value.attachments!.First()!;
        attachment.fileName.Should().Be($"{overrideAttachmentName}.pdf");
    }

    Task UploadAttachmentFile()
    {
        string? directory = Path.GetDirectoryName(new Uri(typeof(TestsBase).Assembly.Location).LocalPath);

        return FileSystem.Upload(
            "attachments/attachment.txt",
            Path.Combine(directory!, "Resources", "Attachment.txt"));
    }

    private async Task InitializeFileSystem()
    {
        await FileSystem.InitializeTenant();
    }

    async Task<templateInterface> Find(templateWhereInput input)
    {
        string query = new QueryBuilder()
            .templates(new QueryBuilder.templatesArgs(where: input), new templatesBuilder()
                .list(new templateInterfaceBuilder()
                    .id()
                    .name()))
            .Build();

        templates  templates = await _client.SendQueryAsync<templates>(query);
        return templates.list!.Single()!;
    }
}