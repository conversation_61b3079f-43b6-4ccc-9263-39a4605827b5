﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Templates
{
    public class TemplateCrudTests : TestsBase
    {
        public TemplateCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_createDynamicTemplateCommand_with_tags_WHEN_creating_dynamic_template_THEN_it_is_created_with_tags()
        {
            List<string> tags = new() { "test" };
            createDynamicTemplateInput input = new() { name = "test", tags = tags! };
            createdStatusResult createResult = await _client.SendMutationAsync<createdStatusResult>(new MutationBuilder()
                .createDynamicTemplate(new MutationBuilder.createDynamicTemplateArgs(input),
                    new createdStatusResultBuilder().WithAllFields()).Build());

            createResult.status.Should().Be("success");
            createResult.errors.Should().BeNullOrEmpty();
            string? createdId = createResult.createdStatus!.id;

            templates templates = await _client.SendQueryAsync<templates>(
                new QueryBuilder().templates(
                    new QueryBuilder.templatesArgs(null, null, null, new templateWhereInput { id = createdId }),
                    new templatesBuilder().list(new templateInterfaceBuilder().id().tags())));

            templateInterface? template = templates.list!.First();
            template!.tags.Should().BeEquivalentTo(tags);
        }

        [Fact]
        public async Task GIVEN_updateDynamicTemplateCommand_with_tags_WHEN_updating_dynamic_template_THEN_it_is_updated()
        {
            List<string> tags = new() { "test" };
            createDynamicTemplateInput input = new() { name = "test", tags = tags! };
            string id = await _client.CreateAndReturnId(new MutationBuilder()
                .createDynamicTemplate(new MutationBuilder.createDynamicTemplateArgs(input),
                    new createdStatusResultBuilder().WithAllFields()).Build());

            List<string> updatedTags = new() { "test" };
            updateDynamicTemplateInput updateInput = new() { tags = updatedTags! };

            result updateResult = await _client.SendMutationAsync<result>(new MutationBuilder()
                .updateDynamicTemplate(new MutationBuilder.updateDynamicTemplateArgs(id, updateInput),
                    new resultBuilder().WithAllFields()).Build());

            updateResult.status.Should().Be("success");
            updateResult.errors.Should().BeNullOrEmpty();

            templates templates = await _client.SendQueryAsync<templates>(
                new QueryBuilder().templates(
                    new QueryBuilder.templatesArgs(null, null, null, new templateWhereInput { id = id }),
                    new templatesBuilder().list(new templateInterfaceBuilder().id().tags())));

            templateInterface template = templates.list!.First()!;
            template!.tags.Should().BeEquivalentTo(updatedTags);
        }

        [Fact]
        public async Task GIVEN_two_templates_WHEN_filter_by_description_THEN_filter_works()
        {
            string search = CreateNewGuid();

            await Template.CreateDynamicTemplate(new createDynamicTemplateInput
            {
                description = "description-1"
            });

            string template2Id = await Template.CreateDynamicTemplate(new createDynamicTemplateInput
            {
                description = $"description {search}"
            });

            templates templates = await _client.SendQueryAsync<templates>(
                new QueryBuilder()
                    .templates(
                        new QueryBuilder.templatesArgs(null, null, null, new templateWhereInput { description_contains = search }),
                    new templatesBuilder()
                        .list(new templateInterfaceBuilder()
                            .id())
                        .totalCount()));


            templates.totalCount.Should().Be(1);
            templates.list!.Count.Should().Be(1);
            templates.list.ElementAt(0)!.id.Should().Be(template2Id);
        }

        [Fact]
        public async Task GIVEN_clauseHtml_templates_WHEN_create_without_status_field_THEN_returns_id()
        {
            createClauseHtmlTemplateInput createClauseHtmlTemplateInput = new()
            {
                name = "name",
                description = "description",
                order = 1,
                html = "<b>html</b>",
            };

            string templateId = await Template.CreateClauseHtmlTemplate(createClauseHtmlTemplateInput);

            templateId.Should().NotBeNullOrWhiteSpace();
        }

        [Fact]
        public async Task GIVEN_clauseHtml_templates_WHEN_create_with_status_field_THEN_returns_id()
        {
            createClauseHtmlTemplateInput createClauseHtmlTemplateInput = new()
            {
                name = "name",
                description = "description",
                order = 1,
                html = "<b>html</b>",
                status = "archived"
            };

            string templateId = await Template.CreateClauseHtmlTemplate(createClauseHtmlTemplateInput);

            templateId.Should().NotBeNullOrWhiteSpace();
        }

        [Fact]
        public async Task GIVEN_clauseHtml_template_created_WHEN_request_this_template_THEN_returns_the_correct_template()
        {
            createClauseHtmlTemplateInput createClauseHtmlTemplateInput = new()
            {
                name = "name",
                description = "description",
                order = 1,
                html = "<b>html</b>",
                status = "archived"
            };

            string templateId = await Template.CreateClauseHtmlTemplate(createClauseHtmlTemplateInput);
            clauseHtml? template = await SearchClauseHtmlTemplateById(templateId);

            template.Should().NotBeNull();
            template!.id.Should().Be(templateId);
            template.name.Should().Be("name");
            template.description.Should().Be("description");
            template.order.Should().Be(1);
            template.status.Should().Be("archived");
            template.html.Should().Be("<b>html</b>");
        }

        [Fact]
        public async Task GIVEN_multiple_clauseHtml_templates_created_WHEN_request_this_template_with_status_THEN_returns_the_correct_templates()
        {
            createClauseHtmlTemplateInput createArchivedClauseHtmlTemplateInput = new()
            {
                name = "name",
                description = "description",
                order = 1,
                html = "<b>html</b>",
                status = "archived"
            };

            string templateId = await Template.CreateClauseHtmlTemplate(createArchivedClauseHtmlTemplateInput);

            createClauseHtmlTemplateInput createClauseHtmlTemplateInput = new()
            {
                name = "name",
                description = "description",
                order = 1,
                html = "<b>html</b>",
            };
            await Template.CreateClauseHtmlTemplate(createClauseHtmlTemplateInput);


            clauseHtml? template = await SearchClauseHtmlTemplateById(templateId, "archived");

            template.Should().NotBeNull();
            template!.id.Should().Be(templateId);
            template.name.Should().Be("name");
            template.description.Should().Be("description");
            template.order.Should().Be(1);
            template.status.Should().Be("archived");
            template.html.Should().Be("<b>html</b>");
        }

        [Fact]
        public async Task GIVEN_clauseHtml_template_created_WHEN_update_this_template_THEN_succeed_and_updated()
        {
            createClauseHtmlTemplateInput createClauseHtmlTemplateInput = new()
            {
                name = "name",
                description = "description",
                html = "<b>html</b>",
            };

            string templateId = await Template.CreateClauseHtmlTemplate(createClauseHtmlTemplateInput);


            updateClauseHtmlTemplateInput updateClauseHtmlTemplateInput = new()
            {
                name = "name-edited",
                description = "description-edited",
                order = 1,
                html = "<b>html-edited</b>",
                status = "archived"
            };

            Func<Task> action = () => Template.UpdateClauseHtmlTemplate(templateId, updateClauseHtmlTemplateInput);

            await action.Should().NotThrowAsync<Exception>();

            clauseHtml? template = await SearchClauseHtmlTemplateById(templateId);

            template.Should().NotBeNull();
            template!.id.Should().Be(templateId);
            template.name.Should().Be("name-edited");
            template.description.Should().Be("description-edited");
            template.order.Should().Be(1);
            template.status.Should().Be("archived");
            template.html.Should().Be("<b>html-edited</b>");
        }

        [Fact]
        public async Task GIVEN_wkhtmltopdf_templates_WHEN_create_THEN_created_and_succeed()
        {
            wkhtmltopdfTemplate? template = await CreateWkhtmltopdfTemplate();

            template.Should().NotBeNull();

            template!.description.Should().Be("description");
            template.html.Should().Be("<b>html</b>");
            template.marginSettings!.left.Should().Be(10);
            template.marginSettings.right.Should().Be(10);
            template.marginSettings.bottom.Should().Be(10);
            template.marginSettings.top.Should().Be(10);
            template.headerSettings!.spacing.Should().Be(10);
            template.footerSettings!.spacing.Should().Be(10);
            template.headerSettings.left.Should().Be("[page] of [toPage]");
            template.footerSettings.left.Should().Be("[page] of [toPage]");
            template.orientation.Should().Be(orientationEnumeration.LANDSCAPE);
        }

        [Fact]
        public async Task GIVEN_created_wkhtmltopdf_templates_WHEN_update_THEN_updated_and_succeed()
        {
            wkhtmltopdfTemplate? template = await CreateWkhtmltopdfTemplate();
            wkhtmltopdfTemplate? updatedTemplate = await UpdateWkhtmltopdfTemplate(template!.id);

            updatedTemplate!.description.Should().Be("updated-description");
            updatedTemplate.html.Should().Be("<b>updated-html</b>");
            updatedTemplate.marginSettings!.left.Should().Be(5);
            updatedTemplate.marginSettings.right.Should().Be(5);
            updatedTemplate.marginSettings.bottom.Should().Be(5);
            updatedTemplate.marginSettings.top.Should().Be(5);
            updatedTemplate.headerSettings!.spacing.Should().Be(5);
            updatedTemplate.footerSettings!.spacing.Should().Be(5);
            updatedTemplate.headerSettings.left.Should().Be("[page]");
            updatedTemplate.footerSettings.left.Should().Be("[page]");
            updatedTemplate.orientation.Should().Be(orientationEnumeration.PORTRAIT);
        }

        [Fact]
        public async Task GIVEN_created_wkhtmltopdf_templates_WHEN_add_pageObject_THEN_added_and_succeed()
        {
            wkhtmltopdfTemplate? template = await CreateWkhtmltopdfTemplate();
            List<pageObject> pageObjects = await AddPageObjectToWkhtmltopdfTemplate(template!.id);

            pageObjects.Should().HaveCount(1);
            pageObject pageObject = pageObjects.First();
            pageObject.html.Should().Be("<b>html</b>");
            pageObject.headerSettings!.spacing.Should().Be(10);
            pageObject.footerSettings!.spacing.Should().Be(10);
            pageObject.headerSettings.left.Should().Be("[page] of [toPage]");
            pageObject.footerSettings.left.Should().Be("[page] of [toPage]");
        }

        [Fact]
        public async Task GIVEN_created_wkhtmltopdf_templates_with_a_pageObject_WHEN_remove_this_pageObject_THEN_removed_and_succeed()
        {
            wkhtmltopdfTemplate? template = await CreateWkhtmltopdfTemplate();
            List<pageObject> pageObjects = await AddPageObjectToWkhtmltopdfTemplate(template!.id);
            pageObject pageObject = pageObjects.First();

            await Template.RemovePageObject(template.id, pageObject.id);

            wkhtmltopdfTemplate? updateTemplate = await SearchWkhtmltopdfTemplate(new templateWhereInput { id = template.id });
            updateTemplate!.pageObjects.Should().NotContain(pageObject);
        }

        [Fact]
        public async Task GIVEN_created_wkhtmltopdf_templates_with_a_pageObject_WHEN_update_this_pageObject_THEN_updated_and_succeed()
        {
            wkhtmltopdfTemplate? template = await CreateWkhtmltopdfTemplate();
            pageObject pageObject = (await AddPageObjectToWkhtmltopdfTemplate(template!.id)).First();
            pageObject updatedPageObject = (await UpdatePageObjectToWkhtmltopdfTemplate(template.id, pageObject.id!)).First();

            updatedPageObject.html.Should().Be("<b>updated-html</b>");
            updatedPageObject.headerSettings!.spacing.Should().Be(5);
            updatedPageObject.footerSettings!.spacing.Should().Be(5);
            updatedPageObject.headerSettings.left.Should().Be("[page]");
            updatedPageObject.footerSettings.left.Should().Be("[page]");
        }

        [Fact]
        public async Task GIVEN_created_wkhtmltopdf_template_without_pageObject_WHEN_render_THEN_succeed_and_returns_rendered_pdf_result()
        {
            wkhtmltopdfTemplate? template = await CreateWkhtmltopdfTemplate();
            templateRenderParametersInput input = new() { name = "name" };

            pdfRenderedResult renderedResult = await Template.RenderWkhtmltopdfTemplate(template!.id,
                input);


            renderedResult.errors.Should().BeNull();
            renderedResult.status.Should().BeEquivalentTo("success");
            renderedResult.value.Should().HaveCountGreaterThan(0);
        }

        [Fact]
        public async Task GIVEN_created_wkhtmltopdf_template_with_pageObject_WHEN_render_THEN_succeed_and_returns_rendered_pdf_result()
        {
            wkhtmltopdfTemplate template = (await CreateWkhtmltopdfTemplate())!;
            await AddPageObjectToWkhtmltopdfTemplate(template!.id!);
            templateRenderParametersInput input = new() { name = "name" };

            pdfRenderedResult renderedResult = await Template.RenderWkhtmltopdfTemplate(template.id!,
                input);

            renderedResult.errors.Should().BeNull();
            renderedResult.status.Should().BeEquivalentTo("success");
            renderedResult.value.Should().HaveCountGreaterThan(0);
        }

        [Fact]
        public async Task GIVEN_html_string_and_wkhtmltopdf_settings_WHEN_renderWkhtmltopdf_THEN_succeed_and_returns_rendered_pdf_result()
        {
            string html = "<b>html</b>";
            marginSettingsInput marginSettings = new() { top = 10, bottom = 10, left = 10, right = 10 };
            headerFooterInput headerSettings = new() { spacing = 10, left = "[page] of [toPage]" };
            headerFooterInput footerSettings = new() { spacing = 10, left = "[page] of [toPage]" };
            pdfRenderedResult renderedResult = await Template.RenderWkhtmltopdf(html, marginSettings, headerSettings, footerSettings);

            renderedResult.errors.Should().BeNull();
            renderedResult.status.Should().BeEquivalentTo("success");
            renderedResult.value.Should().HaveCountGreaterThan(0);
        }

        [Fact]
        public async Task GIVEN_pageObject_settings_WHEN_renderWkhtmltopdf2_THEN_succeed_and_returns_rendered_pdf_result()
        {
            List<pageObjectInput> pageObjectInput = new()
            {
                new pageObjectInput
                {
                    html = "<b>html</b>",
                    headerSettings = new headerFooterInput {spacing = 10, left = "[page] of [toPage]"},
                    footerSettings = new headerFooterInput {spacing = 10, left = "[page] of [toPage]"}
                }
            };
            marginSettingsInput marginSettings = new() { top = 10, bottom = 10, left = 10, right = 10 };

            pdfRenderedResult renderedResult = await Template.RenderWkhtmltopdf2(pageObjectInput, marginSettings);

            renderedResult.errors.Should().BeNull();
            renderedResult.status.Should().BeEquivalentTo("success");
            renderedResult.value.Should().HaveCountGreaterThan(0);
        }

        [Fact]
        public async Task GIVEN_a_created_smsTemplate_WHEN_updating_by_templateId_THEN_should_updated_and_succeed()
        {
            string logicalId = CreateNewGuid();
            await Template.CreateSmsTemplate(new createSmsTemplateInput
            {
                logicalId = logicalId,
                name = "name"
            });

            templateInterface template = await FindById(logicalId);

            template.Should().NotBeNull();
            template.name.Should().Be("name");

            await Template.UpdateSmsTemplate(template.id, new updateSmsTemplateInput { name = "name-updated" });

            template = await FindById(template.id);
            template.name.Should().Be("name-updated");
        }

        [Fact]
        public async Task GIVEN_a_created_smsTemplate_WHEN_updating_by_logicalId_THEN_should_updated_and_succeed()
        {
            string logicalId = CreateNewGuid();
            await Template.CreateSmsTemplate(new createSmsTemplateInput
            {
                logicalId = logicalId,
                name = "name"
            });

            templateInterface template = await FindById(logicalId);

            template.Should().NotBeNull();
            template.name.Should().Be("name");

            await Template.UpdateSmsTemplate(template.logicalId, new updateSmsTemplateInput { name = "name-updated" });

            template = await FindById(template.id);
            template.name.Should().Be("name-updated");
        }

        [Fact]
        public async Task GIVEN_a_created_emailMjmlTemplate_WHEN_updating_by_templateId_THEN_should_updated_and_succeed()
        {
            string logicalId = CreateNewGuid();

            await Template.CreateEmailMjmlTemplate(new createEmailMjmlTemplateInput
            {
                logicalId = logicalId,
                name = "name"
            });

            templateInterface template = await FindById(logicalId);

            template.Should().NotBeNull();
            template.name.Should().Be("name");

            await Template.UpdateEmailMjmlTemplate(template.id,
                new updateEmailMjmlTemplateInput { name = "name-updated" });

            template = await FindById(template.id);
            template.name.Should().Be("name-updated");
        }

        [Fact]
        public async Task GIVEN_a_created_emailMjmlTemplate_WHEN_updating_by_logicalId_THEN_should_updated_and_succeed()
        {
            string logicalId = CreateNewGuid();
            await Template.CreateEmailMjmlTemplate(new createEmailMjmlTemplateInput
            {
                logicalId = logicalId,
                name = "name"
            });

            templateInterface template = await FindById(logicalId);

            template.Should().NotBeNull();
            template.name.Should().Be("name");

            await Template.UpdateEmailMjmlTemplate(template.logicalId, new updateEmailMjmlTemplateInput { name = "name-updated" });

            template = await FindById(template.id);
            template.name.Should().Be("name-updated");
        }

        [Fact]
        public async Task GIVEN_a_created_functionTemplate_WHEN_updating_by_templateId_THEN_should_updated_and_succeed()
        {
            string logicalId = CreateNewGuid();

            await Template.CreateFunctionTemplate(new createFunctionTemplateInput
            {
                logicalId = logicalId,
                name = "name"
            });

            templateInterface template = await FindById(logicalId);

            template.Should().NotBeNull();
            template.name.Should().Be("name");

            await Template.UpdateFunctionTemplate(template.id,
                new updateFunctionTemplateInput { name = "name-updated" });

            template = await FindById(template.id);
            template.name.Should().Be("name-updated");
        }

        [Fact]
        public async Task GIVEN_a_created_functionTemplate_WHEN_updating_by_logicalId_THEN_should_updated_and_succeed()
        {
            string logicalId = CreateNewGuid();
            await Template.CreateFunctionTemplate(new createFunctionTemplateInput
            {
                logicalId = logicalId,
                name = "name"
            });

            templateInterface template = await FindById(logicalId);

            template.Should().NotBeNull();
            template.name.Should().Be("name");

            await Template.UpdateFunctionTemplate(template.logicalId, new updateFunctionTemplateInput { name = "name-updated" });

            template = await FindById(template.id);
            template.name.Should().Be("name-updated");
        }

        [Fact]
        public async Task GIVEN_a_created_notificationTemplate_WHEN_updating_by_templateId_THEN_should_updated_and_succeed()
        {
            string logicalId = CreateNewGuid();

            await Template.CreateNotificationTemplate(new createNotificationTemplateInput
            {
                logicalId = logicalId,
                name = "name"
            });

            templateInterface template = await FindById(logicalId);

            template.Should().NotBeNull();
            template.name.Should().Be("name");

            await Template.UpdateNotificationTemplate(template.id,
                new updateNotificationTemplateInput { name = "name-updated" });

            template = await FindById(template.id);
            template.name.Should().Be("name-updated");
        }

        [Fact]
        public async Task GIVEN_a_created_notificationTemplate_WHEN_updating_by_logicalId_THEN_should_updated_and_succeed()
        {
            string logicalId = CreateNewGuid();
            await Template.CreateNotificationTemplate(new createNotificationTemplateInput
            {
                logicalId = logicalId,
                name = "name"
            });

            templateInterface template = await FindById(logicalId);

            template.Should().NotBeNull();
            template.name.Should().Be("name");

            await Template.UpdateNotificationTemplate(template.logicalId, new updateNotificationTemplateInput { name = "name-updated" });

            template = await FindById(template.id);
            template.name.Should().Be("name-updated");
        }

        async Task<wkhtmltopdfTemplate?> CreateWkhtmltopdfTemplate()
        {
            string templateName = CreateNewGuid();
            createWkhtmltopdfTemplateInput createInput = new()
            {
                name = templateName,
                description = "description",
                html = "<b>html</b>",
                marginSettings = new marginSettingsInput
                {
                    top = 10,
                    bottom = 10,
                    left = 10,
                    right = 10
                },
                headerSettings = new headerFooterInput
                {
                    spacing = 10,
                    left = "[page] of [toPage]"
                },
                footerSettings = new headerFooterInput
                {
                    spacing = 10,
                    left = "[page] of [toPage]"
                },
                orientation = orientationEnumeration.LANDSCAPE
            };

            await Template.CreateWkhtmltopdfTemplate(createInput);

            return await SearchWkhtmltopdfTemplate(new templateWhereInput { name = templateName });
        }

        async Task<wkhtmltopdfTemplate?> UpdateWkhtmltopdfTemplate(string templateId)
        {
            updateWkhtmltopdfTemplateInput input = new()
            {
                description = "updated-description",
                html = "<b>updated-html</b>",
                marginSettings = new marginSettingsInput
                {
                    top = 5,
                    bottom = 5,
                    left = 5,
                    right = 5
                },
                headerSettings = new headerFooterInput
                {
                    spacing = 5,
                    left = "[page]"
                },
                footerSettings = new headerFooterInput
                {
                    spacing = 5,
                    left = "[page]"
                },
                orientation = orientationEnumeration.PORTRAIT
            };

            await Template.UpdateWkhtmltopdfTemplate(templateId, input);

            return await SearchWkhtmltopdfTemplate(new templateWhereInput { id = templateId });
        }

        async Task<List<pageObject>> AddPageObjectToWkhtmltopdfTemplate(string templateId)
        {
            addPageObjectToWkhtmltopdfTemplateInput addPageObjectToWkhtmltopdfTemplateInput = new()
            {
                html = "<b>html</b>",

                headerSettings = new headerFooterInput
                {
                    spacing = 10,
                    left = "[page] of [toPage]"
                },
                footerSettings = new headerFooterInput
                {
                    spacing = 10,
                    left = "[page] of [toPage]"
                }
            };

            await Template.AddPageObject(templateId, addPageObjectToWkhtmltopdfTemplateInput);

            wkhtmltopdfTemplate? updatedTemplate = await SearchWkhtmltopdfTemplate(new templateWhereInput { id = templateId });
            return updatedTemplate!.pageObjects!.ToList()!;
        }

        async Task<List<pageObject>> UpdatePageObjectToWkhtmltopdfTemplate(string templateId, string pageObjectId)
        {
            updatePageObjectOfWkhtmltopdfTemplateInput input = new()
            {
                id = pageObjectId,
                html = "<b>updated-html</b>",
                headerSettings = new headerFooterInput
                {
                    spacing = 5,
                    left = "[page]"
                },
                footerSettings = new headerFooterInput
                {
                    spacing = 5,
                    left = "[page]"
                }
            };

            await Template.UpdatePageObject(templateId, input);

            wkhtmltopdfTemplate? updatedTemplate = await SearchWkhtmltopdfTemplate(new templateWhereInput { id = templateId });
            return updatedTemplate!.pageObjects!.ToList()!;
        }

        async Task<templateInterface> FindById(string id)
        {
            string query = new QueryBuilder()
                .templates(new QueryBuilder.templatesArgs(where: new templateWhereInput { id = id }), new templatesBuilder()
                    .list(new templateInterfaceBuilder()
                        .id()
                        .name()
                        .logicalId()
                        .description()))
                .Build();

            JObject templates = await _client.SendQueryAsync<dynamic>(query);
            return templates["list"]?.ToObject<List<templateInterface>>()?.First();
        }

        async Task<templateInterface> Find(templateWhereInput input)
        {
            string query = new QueryBuilder()
                .templates(new QueryBuilder.templatesArgs(where: input), new templatesBuilder()
                    .list(new templateInterfaceBuilder()
                        .id()
                        .name()
                        .logicalId()
                        .description()))
                .Build();

            JObject templates = await _client.SendQueryAsync<dynamic>(query);
            return templates["list"]?.ToObject<List<templateInterface>>()?.First();
        }

        async Task<clauseHtml?> SearchClauseHtmlTemplateById(string id, string? status = null)
        {
            string query = new QueryBuilder()
                .templates(new QueryBuilder.templatesArgs(where: new templateWhereInput { id = id, status = status }), new templatesBuilder()
                    .list(new templateInterfaceBuilder()
                        .id()
                        .name()
                        .description()
                        .clauseHtmlFragment(new clauseHtmlBuilder()
                            .status()
                            .order()
                            .html())))
                .Build();

            JObject templates = await _client.SendQueryAsync<dynamic>(query);
            return templates["list"]?.ToObject<List<clauseHtml>>()?.First();
        }

        async Task<wkhtmltopdfTemplate?> SearchWkhtmltopdfTemplate(templateWhereInput where)
        {
            string query = new QueryBuilder()
                .templates(new QueryBuilder.templatesArgs(where: where), new templatesBuilder()
                    .list(new templateInterfaceBuilder()
                        .id()
                        .name()
                        .description()
                        .wkhtmltopdfTemplateFragment(new wkhtmltopdfTemplateBuilder()
                            .html()
                            .orientation()
                            .marginSettings(new marginSettingsBuilder()
                                .WithAllFields())
                            .footerSettings(new headerFooterBuilder()
                                .WithAllFields())
                            .headerSettings(new headerFooterBuilder()
                                .WithAllFields())
                            .pageObjects(new pageObjectBuilder()
                                .id()
                                .html()
                                .footerSettings(new headerFooterBuilder()
                                    .WithAllFields())
                                .headerSettings(new headerFooterBuilder()
                                    .WithAllFields())))))
                .Build();

            JObject templates = await _client.SendQueryAsync<dynamic>(query);
            return templates["list"]?.ToObject<List<wkhtmltopdfTemplate>>()?.First();
        }
    }
}
