using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Templates;

public class HtmlToPdfRenderTests : TestsBase
{
    public HtmlToPdfRenderTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task GIVEN_template_and_content_WHEN_render_THEN_file_should_exist()
    {
        var templateId = await CreateTemplate();

        var outputFilePath = $"test-output/{CreateNewGuid()}.pdf";
        await Render(templateId, outputFilePath);

        var file = await FileSystem.GetFile(outputFilePath);
        file?.fileName.Should().NotBeNull();
    }

    private async Task<string> Render(string templateId, string outputFilePath)
    {
        await FileSystem.InitializeTenant();
        var input = new templateRenderParametersInput()
        {
            name = "data",
            contentJsonString = "{}"
        };
        
        return await Template.RenderWkhtmltopdfTemplateV2(templateId, input, outputFilePath);
    }

    private async Task<string> CreateTemplate()
    {
        createWkhtmltopdfTemplateInput createInput = new()
        {
            name = CreateNewGuid(),
            description = "description",
            html = "<b>html</b>",
            orientation = orientationEnumeration.LANDSCAPE
        };

        return await Template.CreateWkhtmltopdfTemplate(createInput);
    }
}