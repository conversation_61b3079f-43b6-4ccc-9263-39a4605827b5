using System.Threading.Tasks;

using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;

using GraphQL.Client.Http;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Notifications;

public static class NotificationsClientExtensions
{
    public static async Task SendNotification(this GraphQLHttpClient client, string fromEntityId, string toEntityId, string toTopic)
    {
        var notificationClient = new Notification(client);
        var input = new sendNotificationInput
        {
            fromEntityId = fromEntityId,
            toEntityId = toEntityId,
            toTopic = toTopic
        };

        await notificationClient.SendNotification(input);
    }
}
