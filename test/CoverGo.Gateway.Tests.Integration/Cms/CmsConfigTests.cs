﻿using System;
using System.Net;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Cms
{
    public class CmsConfigTests : TestsBase
    {
        public CmsConfigTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_tenant_does_not_exist_WHEN_cms_config_fetched_THEN_error_message_should_be_received()
        {
            var result = await FetchCmsConfig($"http://{Guid.NewGuid()}.com");

            result.errors_2.Should().Contain(e => e.code == HttpStatusCode.NotFound.ToString());
        }

        private Task<cmsConfigResult> FetchCmsConfig(string url)
        {
            var query = new QueryBuilder()
                .cmsConfig(new QueryBuilder.cmsConfigArgs(url),
                    new cmsConfigResultBuilder()
                    .status()
                    .errors_2(new errorsBuilder()
                        .WithAllFields()));
            return _client.SendQueryAsync<cmsConfigResult>(query);
        }
    }
}
