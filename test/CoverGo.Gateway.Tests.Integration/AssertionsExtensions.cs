﻿using FluentAssertions;
using FluentAssertions.Primitives;
using Newtonsoft.Json.Linq;
using System;

namespace CoverGo.Gateway.Tests.Integration
{
    static class AssertionsExtensions
    {
        /// <summary>
        /// Do not use this extension if you are not fully understanding how it works!
        /// This kind of comparison takes two records of different type and applies values from source to target
        /// It takes every property from source, and assigns the value to target if it has a property with same name.
        /// Two gotchas here is that it only works with records that have redefined toString() and comparison operators,
        /// Also it has limited ability to compare collections, because if source has less elements in collection than
        /// target, it won't be spotted.
        /// </summary>
        /// <param name="source">Graph type returned from graphql</param>
        /// <param name="target">Input graph type from which object was created from</param>
        public static AndConstraint<ObjectAssertions> ShouldHaveSameValuesAsInput<T1, T2>(this T1 source, T2 target)
        {
            if (source == null) throw new ArgumentNullException(nameof(source));
            if (target == null) throw new ArgumentNullException(nameof(target));

            JObject expectedJObject = JObject.FromObject(target);
            JObject targetJObject = JObject.FromObject(source);
            JObject mergedJObject = expectedJObject;
            mergedJObject.Merge(targetJObject, new JsonMergeSettings
            {
                MergeArrayHandling = MergeArrayHandling.Union,
                MergeNullValueHandling = MergeNullValueHandling.Merge,
                PropertyNameComparison = StringComparison.InvariantCulture,
            });

            T2 actual = mergedJObject.ToObject<T2>()!;

            actual.ToString().Should().Be(target.ToString());

            return actual.Should().NotBeNull();
        }
    }
}