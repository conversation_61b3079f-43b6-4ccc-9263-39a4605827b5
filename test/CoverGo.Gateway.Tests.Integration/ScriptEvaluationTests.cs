using System;
using System.Collections.Generic;
using System.Linq;
using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Xunit;
using static CoverGo.Gateway.Client.MutationBuilder;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class ScriptEvaluationTests : TestsBase
    {
        public ScriptEvaluationTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_script_WHEN_evaluating_THEN_returns_success_with_output()
        {
            productId productId = await CreateProduct("{\"data\": \"test\"}".Escape());

            const string sourceCode = "export function execute({representation, dataInput}) {return `input:${dataInput}, representation:${JSON.parse(representation).data}`;}";

            string scriptId = await Script.Create(sourceCode);
            await Product.AddScript(Product.ProductIdToInput(productId), scriptId);

            evaluateScriptInput evaluateScriptInput = new()
            {
                dataInput = "some input",
                scriptId = scriptId,
                productId = Product.ProductIdToInput(productId)
            };

            evaluateScriptResult result = await EvaluateScript(evaluateScriptInput);

            result.status.Should().Be("success");
            result.value.Should().Be("input:some input, representation:test");
        }

        [Fact]
        public async Task GIVEN_script_WHEN_evaluating_with_direct_representation_input_THEN_returns_success_with_output()
        {
            string representation = "{\"data\": \"test\"}".Escape();

            productId productId = await CreateProduct(representation);

            const string sourceCode = "export function execute({representation, dataInput}) {return `input:${dataInput}, representation:${JSON.parse(representation).data}`;}";

            string scriptId = await Script.Create(sourceCode);
            await Product.AddScript(Product.ProductIdToInput(productId), scriptId);

            evaluateScriptInput evaluateScriptInput = new()
            {
                dataInput = "some input",
                scriptId = scriptId,
                representation = representation
            };

            evaluateScriptResult result = await EvaluateScript(evaluateScriptInput);

            result.status.Should().Be("success");
            result.value.Should().Be("input:some input, representation:test");
        }

        [Fact]
        public async Task GIVEN_pricing_script_and_product_with_discount_codes_WHEN_evaluating_THEN_should_load_discount_codes_and_pass_to_script_evaluation()
        {
            productId productId = await CreateProduct("{\"data\": \"test\"}".Escape());

            DateTime now = DateTime.UtcNow;
            DateTime validFrom = now.AddDays(-1);
            DateTime validTo = now.AddDays(1);
            products_DiscountCode discountCode = await CreateDiscountCode(productId, validFrom, validTo);

            const string sourceCode = "export function execute({representation, dataInput, discountCodes}) { return discountCodes }";

            string scriptId = await Script.Create(sourceCode, includeDiscountCodes: true);
            await Product.AddScript(Product.ProductIdToInput(productId), scriptId);

            evaluateScriptInput evaluateScriptInput = new()
            {
                dataInput = "some input",
                scriptId = scriptId,
                productId = Product.ProductIdToInput(productId)
            };

            evaluateScriptResult result = await EvaluateScript(evaluateScriptInput);
            result.status.Should().Be("success");

            ExpectDiscountCodeBeEquivalent(result.value, discountCode);
        }

        static void ExpectDiscountCodeBeEquivalent(string inputDiscountCode, products_DiscountCode discountCode)
        {
            var discountCodes = JsonConvert.DeserializeObject<PricingScriptDiscountCode[]>(inputDiscountCode);
            discountCodes.Should().BeEquivalentTo(new[] { PricingScriptDiscountCode.From(discountCode) });
        }
        record PricingScriptDiscountCode
        {
            public string? id { get; init; }
            public string? name { get; init; }
            public string? description { get; init; }
            public string? productTypeId { get; init; }
            public object? value { get; init; }
            public DateTime? validFrom { get; init; }
            public DateTime? validTo { get; init; }
            public ICollection<products_ProductId?>? productIds { get; init; }

            public static PricingScriptDiscountCode From(products_DiscountCode discountCode) =>
                new()
                {
                    id = discountCode.id,
                    name = discountCode.name,
                    description = discountCode.description,
                    productTypeId = discountCode.productTypeId,
                    value = discountCode.value,
                    validFrom = discountCode.validFrom,
                    validTo = discountCode.validTo,
                    productIds = discountCode.productIds,
                };
        }

        [Fact]
        public async Task GIVEN_pricing_script_and_product_with_unexpired_discount_codes_WHEN_evaluating_THEN_should_load_discount_codes_and_pass_to_script_evaluation()
        {
            productId productId = await CreateProduct("{\"data\": \"test\"}".Escape());

            DateTime now = DateTime.UtcNow;
            DateTime validFrom = now.AddDays(-1);
            DateTime? validTo = null;
            products_DiscountCode discountCode = await CreateDiscountCode(productId, validFrom, validTo);

            const string sourceCode = "export function execute({representation, dataInput, discountCodes}) { return discountCodes }";

            string scriptId = await Script.Create(sourceCode, includeDiscountCodes: true);
            await Product.AddScript(Product.ProductIdToInput(productId), scriptId);

            evaluateScriptInput evaluateScriptInput = new()
            {
                dataInput = "some input",
                scriptId = scriptId,
                productId = Product.ProductIdToInput(productId)
            };

            evaluateScriptResult result = await EvaluateScript(evaluateScriptInput);
            JsonSerializerSettings jsonSerializerSettings = new() { NullValueHandling = NullValueHandling.Ignore };
            jsonSerializerSettings.Converters.Add(new StringEnumConverter());
            result.status.Should().Be("success");
            ExpectDiscountCodeBeEquivalent(result.value, discountCode);
        }

        [Fact]
        public async Task GIVEN_script_not_pricing_and_product_with_discount_codes_WHEN_evaluating_THEN_should_not_load_discount_codes_and_pass_to_script_evaluation()
        {
            productId productId = await CreateProduct("{\"data\": \"test\"}".Escape());
            DateTime now = DateTime.UtcNow;
            DateTime validFrom = now.AddDays(-1);
            DateTime validTo = now.AddDays(1);
            products_DiscountCode discountCode = await CreateDiscountCode(productId, validFrom, validTo);

            const string sourceCode = "export function execute({representation, dataInput, discountCodes}) {return `input:${dataInput}, representation:${JSON.parse(representation).data}, discountCodes:${discountCodes}`;}";

            string scriptId = await Script.Create(sourceCode, scriptType: scriptTypeEnum.CLAIM);
            await Product.AddScript(Product.ProductIdToInput(productId), scriptId);

            evaluateScriptInput evaluateScriptInput = new()
            {
                dataInput = "some input",
                scriptId = scriptId,
                productId = Product.ProductIdToInput(productId)
            };

            evaluateScriptResult result = await EvaluateScript(evaluateScriptInput);
            JsonSerializerSettings jsonSerializerSettings = new() { NullValueHandling = NullValueHandling.Ignore };
            jsonSerializerSettings.Converters.Add(new StringEnumConverter());
            result.status.Should().Be("success");
            result.value.Should().Be($"input:some input, representation:test, discountCodes:null");
        }

        [Fact]
        public async Task GIVEN_wrong_script_WHEN_evaluating_THEN_returns_failure_and_errors()
        {
            productId productId = await CreateProduct("{data: 'test'}");

            string scriptId = await Script.Create("error");

            evaluateScriptInput evaluateScriptInput = new()
            {
                dataInput = "some input",
                scriptId = scriptId,
                productId = Product.ProductIdToInput(productId)
            };

            evaluateScriptResult result = await EvaluateScript(evaluateScriptInput);

            result.errors.Should().NotBeNullOrEmpty();
        }

        private async Task<products_DiscountCode> CreateDiscountCode(productId productId, DateTime? validFrom, DateTime? validTo)
        {
            string id = CreateNewGuid();
            string code = $"COVERGO-{CreateNewGuid()}";

            products_DiscountCodeUpsertInput createInput = new()
            {
                id = id,
                name = code,
                type = products_DiscountType.AMOUNT,
                value = 100.5,
                description = "Covergo discount code",
                validFrom = validFrom,
                validTo = validTo,
                productTypeId = "gm"
            };

            await CreateDiscountCode(createInput);

            await AddEligibleProductsToDiscountCode(new products_DiscountCodeProductsUpsertInput
            {
                discountCodeId = id,
                productIds = new List<products_ProductIdInput?> { new() { plan = productId.plan, type = productId.type, version = productId.version } }
            });

            return (await DiscountCodesGet(id)).list!.FirstOrDefault()!;
        }

        private async Task<products_GenericDiscountCode8QueryInterface> DiscountCodesGet(string id)
        {
            string query = new QueryBuilder()
                .discountCodesQuery(new QueryBuilder.discountCodesQueryArgs(@where: new products_GenericDiscountCodeQueryInput { @where = new products_GenericDiscountCodeFilterInput { @where = new products_DiscountCodeFilterInput { id = id } } }), new products_GenericDiscountCode8QueryInterfaceBuilder()
                    .totalCount()
                    .list(new products_DiscountCodeBuilder()
                        .id()
                        .name()
                        .type()
                        .description()
                        .value()
                        .validFrom()
                        .validTo()
                        .productTypeId()
                        .productIds(new products_ProductIdBuilder()
                            .plan()
                            .type()
                            .version())))
                .Build();

            return await _client.SendQueryAsync<products_GenericDiscountCode8QueryInterface>(query);
        }

        private async Task AddEligibleProductsToDiscountCode(products_DiscountCodeProductsUpsertInput input)
        {
            string mutation = new MutationBuilder()
                .discountCodesMutationAddEligibleProduct(new discountCodesMutationAddEligibleProductArgs(input: input), new products_ResultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        private async Task CreateDiscountCode(products_DiscountCodeUpsertInput create)
        {
            string mutation = new MutationBuilder()
                .discountCodesMutationCreate(new discountCodesMutationCreateArgs(create: create), new products_ResultOfCreatedStatusBuilder()
                    .status()
                    .errors()
                    .value(new products_CreatedStatusBuilder()
                        .id()))
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        Task<productId> CreateProduct(string representation)
        {
            createProductInput input = new()
            {
                representation = representation,
                productId = new productIdInput
                {
                    plan = CreateNewGuid(),
                    type = CreateNewGuid(),
                    version = CreateNewGuid()
                }
            };

            return Product.Create(input);
        }

        Task<evaluateScriptResult> EvaluateScript(evaluateScriptInput evaluateScriptInput)
        {
            string mutation = new MutationBuilder()
                .evaluateScript(new evaluateScriptArgs(evaluateScriptInput), new evaluateScriptResultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAsync<evaluateScriptResult>(mutation);
        }

        Task<result> DeleteReferenceScript(deleteFileArgs deleteFileArgs)
        {
            string mutation = new MutationBuilder()
                .deleteFile(deleteFileArgs, new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAsync<result>(mutation);
        }

        async Task<createdStatusResult> CreateFileSystemConfig(fileSystemConfigInput input)
        {
            string mutation = new MutationBuilder()
                .createFileSystemConfig(new createFileSystemConfigArgs(input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            return await _client.SendMutationAsync<createdStatusResult>(mutation);
        }
    }
}
