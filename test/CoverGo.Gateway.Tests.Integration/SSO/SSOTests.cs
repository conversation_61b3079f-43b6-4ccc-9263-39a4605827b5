﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using GraphQL.Client.Http;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.SSO
{
    /// <summary>
    /// Test for SSO token validation.
    ///
    /// To run this tests to pass you need to run the oidc test container locally or run with docker compose.
    /// docker run -p9090:9090 -e BIND=0.0.0.0 -e PORT=9090 -e EXPOSED_HOST=http://localhost:9090 --name oidc-token-test-service spectare/oidc-token-test-service:latest
    /// </summary>
    public class SSOTests : TestsBase
    {
        private readonly string _OidcTokenTestServiceUrl;
        public SSOTests(ITestOutputHelper output) : base(output)
        {
            string? oidcTestServiceUrl = Environment.GetEnvironmentVariable("GATEWAY_INTEGRATION_TEST-OidcTokenTestServiceUrl");
            _OidcTokenTestServiceUrl = string.IsNullOrWhiteSpace(oidcTestServiceUrl) ? "http://localhost:9090" : oidcTestServiceUrl;
        }

        [Fact]
        public async Task GIVEN_valid_SSO_jwt_token_WHEN_querying_cases_THEN_return_with_no_errors()
        {
            string permissionGroupId = await SetupPermissionGroup();
            await AddTestSSOConfiguration();
            string jwt = await GetSSOToken(DateTimeOffset.Now.AddDays(-1), new TimeSpan(2, 0, 0, 0), permissionGroupId);

            string query = new QueryBuilder()
                .cases(new(new()), new casesBuilder()
                    .list(new caseBuilder()
                        .id()
                        .name()
                        .fields()))
                .Build();

            using GraphQLHttpClient client = await CreateClient(jwt);
            cases response = await client.SendQueryAsync<cases>(query);
            response.list.Should().NotBeNull();
        }

        [Theory]
        [InlineData("createCases")]
        [InlineData("writeCases")]
        public async Task GIVEN_valid_SSO_jwt_token_WHEN_creating_cases_THEN_return_with_no_errors(string createCasesPermissionName)
        {
            string permissionGroupId = await SetupPermissionGroup(createCasesPermissionName);
            await AddTestSSOConfiguration();
            string jwt = await GetSSOToken(DateTimeOffset.Now.AddDays(-1), new TimeSpan(2, 0, 0, 0), permissionGroupId);

            string query = new MutationBuilder()
                .createCase(new MutationBuilder.createCaseArgs(new createCaseInput() { name = "test" }),
                new createdStatusResultBuilder().WithAllFields())
                .Build();

            using GraphQLHttpClient client = await CreateClient(jwt);
            createdStatusResult response = await client.SendMutationAsync<createdStatusResult>(query);
            response.status.Should().Be("success");
        }

        [Fact]

        public async Task GIVEN_expired_SSO_jwt_token_WHEN_querying_cases_THEN_return_error()
        {
            string permissionGroupId = await SetupPermissionGroup();
            await AddTestSSOConfiguration();
            string jwt = await GetSSOToken(DateTimeOffset.Now.AddHours(-10.0), new TimeSpan(1, 0, 0), permissionGroupId);

            string query = new QueryBuilder()
                .cases(new(new()), new casesBuilder()
                    .list(new caseBuilder()
                        .id()
                        .name()
                        .fields()))
                .Build();

            using GraphQLHttpClient client = await CreateClient(jwt);
            Func<Task<cases>> sendQuery = async () => await client.SendQueryAsync<cases>(query);
            await sendQuery.Should().ThrowAsync<Exception>();
        }

        [Fact]
        public async Task GIVEN_valid_SSO_jwt_token_with_wrong_client_id_WHEN_querying_cases_THEN_return_error()
        {
            string permissionGroupId = await SetupPermissionGroup();
            await AddTestSSOConfiguration();
            string jwt = await GetSSOToken(DateTimeOffset.Now.AddDays(-1), new TimeSpan(2, 0, 0, 0), permissionGroupId, "OtherClient");

            string query = new QueryBuilder()
                .cases(new(new()), new casesBuilder()
                    .list(new caseBuilder()
                        .id()
                        .name()
                        .fields()))
                .Build();

            using GraphQLHttpClient client = await CreateClient(jwt);
            Func<Task<cases>> sendQuery = async () => await client.SendQueryAsync<cases>(query);
            await sendQuery.Should().ThrowAsync<Exception>();
        }

        [Fact]

        public async Task GIVEN_valid_SSO_jwt_token_WHEN_creatingNode_THEN_return_with_no_errors()
        {
            string permissionGroupId = await SetupPermissionGroup();
            await AddTestSSOConfiguration();
            string jwt = await GetSSOToken(DateTimeOffset.Now.AddDays(-1), new TimeSpan(2, 0, 0, 0), permissionGroupId);

            string mutation = @"mutation {
  createNode(node: {alias: ""test""})
}";

            using GraphQLHttpClient client = await CreateClient(jwt);
            string response = await client.SendMutationAsync<string>(mutation);
            response.Should().NotBeNull();
        }

        record nodeType
        {
            public int id { get; set; }
        }

        [Fact]
        public async Task GIVEN_valid_SSO_jwt_token_WHEN_querying_nodeTypes_THEN_return_with_no_errors()
        {
            string permissionGroupId = await SetupPermissionGroup();
            await AddTestSSOConfiguration();
            string jwt = await GetSSOToken(DateTimeOffset.Now.AddDays(-1), new TimeSpan(2, 0, 0, 0), permissionGroupId);

            string query = @"query nodeTypes {
  nodeTypes {
    id
  }
}
";

            using GraphQLHttpClient client = await CreateClient(jwt);
            nodeType[] response = await client.SendQueryAsync<nodeType[]>(query);
            response.Should().NotBeNull();
        }

        private async Task<GraphQLHttpClient> CreateClient(string token)
        {
            GraphQLHttpClient client = await Setup.CreateGraphQLHttpClient();
            client.HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            return client;
        }

        private async Task<string> SetupPermissionGroup(string updateCasesPermissionName = "writeCases")
        {
            string? permissionGroupId = await PermissionGroup.Create(new createPermissionGroupInput
            {
                name = Guid.NewGuid().ToString(),
                description = Guid.NewGuid().ToString()
            });
            await PermissionGroup.AddPermission(permissionGroupId, "readCases", "all");
            await PermissionGroup.AddPermission(permissionGroupId, updateCasesPermissionName, "all");
            await PermissionGroup.AddPermission(permissionGroupId, "readNodes", "all");
            await PermissionGroup.AddPermission(permissionGroupId, "writeNodes", "all");
            await PermissionGroup.AddPermission(permissionGroupId, "readProductSchemas", "all");
            await PermissionGroup.AddPermission(permissionGroupId, "writeProductSchemas", "all");
            return permissionGroupId;
        }

        private async Task<string> GetSSOToken(DateTimeOffset creationDate, TimeSpan expirationInterval, string? permissionGroupId, string clientId = "CoverGoClient")
        {
            using HttpClient httpClient = new HttpClient();
            using HttpResponseMessage httpResponseMessage = await httpClient.PostAsJsonAsync($"{_OidcTokenTestServiceUrl}/token", new
            {
                aud = "71fee7a8-d612-4c76-bfee-9b48fc143d70",
                iss = _OidcTokenTestServiceUrl,
                iat = creationDate.ToUnixTimeSeconds(),
                nbf = creationDate.ToUnixTimeSeconds(),
                exp = creationDate.Add(expirationInterval).ToUnixTimeSeconds(),
                aio = "E2ZgYPjiLLntaXr5/JkeSl+aZ+4JAQA=",
                appid = "71fee7a8-d612-4c76-bfee-9b48fc143d70",
                appidacr = "1",
                idp = $"{_OidcTokenTestServiceUrl}/",
                oid = "1dfee9f4-b488-4338-94fb-fc34a7092706",
                rh = "0.AT8ABSPyROmrHkOZFgDt2kfdSKjn_nES1nZMv-6bSPwUPXA_AAA.",
                sub = Guid.NewGuid().ToString(),
                client_id = clientId,
                tid = "44f22305-abe9-431e-9916-00edda47dd48",
                uti = "F2G19iBogE2NnSK2v7YCAA",
                ver = "1.0",
                email = Guid.NewGuid().ToString(),
                name = "Test User",
                role = permissionGroupId
            });
            httpResponseMessage.EnsureSuccessStatusCode();
            return await httpResponseMessage.Content.ReadAsStringAsync();
        }

        private async Task AddTestSSOConfiguration()
        {
            string deleteMutation = new MutationBuilder().sSOConfigsMutationRemoveSSOConfig(
                new MutationBuilder.sSOConfigsMutationRemoveSSOConfigArgs(_OidcTokenTestServiceUrl),
                new auth_ResultBuilder().WithAllFields()).Build();
            using GraphQLHttpClient client = await Setup.CreateGraphQLHttpClient();
            await client.SendMutationAsync<auth_Result>(deleteMutation);
            string mutation = new MutationBuilder().sSOConfigsMutationAddSSOConfig(new MutationBuilder.sSOConfigsMutationAddSSOConfigArgs(new auth_SSOConfigInput()
            {
                id = _OidcTokenTestServiceUrl,
                idClaim = "iss",
                keyUrlClaim = "idp",
                tenantId = "covergo",
                claimsMap = new List<auth_KeyValuePairOfStringAndStringInput>()
                {
                    new auth_KeyValuePairOfStringAndStringInput()
                    {
                        key = "tid",
                        value = "ssotenant"
                    }
                },
                additionalClaims = new List<auth_KeyValuePairOfStringAndListOfStringInput>()
                {
                    new auth_KeyValuePairOfStringAndListOfStringInput()
                    {
                        key = "role",
                        value = new List<string>() { "Admin" }
                    }
                },
                clientId = "CoverGoClient"
            }), new auth_ResultBuilder().WithAllFields()).Build();
            var result = await client.SendMutationAsync<auth_Result>(mutation);
            if (!(result.isSuccess ?? false))
            {
                throw new Exception("Failed to add test configuration");
            }
        }
    }
}
