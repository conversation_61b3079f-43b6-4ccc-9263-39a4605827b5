﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class ServiceItemCrudTests : TestsBase
    {
        public ServiceItemCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_serviceItem_WHEN_create_THEN_returns_Id()
        {
            (string id, _) = await ServiceItem.Create();

            id.Should().NotBeNull();
        }

        [Fact]
        public async Task GIVEN_serviceItem_added_WHEN_request_this_serviceItem_THEN_returns_serviceItem()
        {
            (string id, createServiceItemInput input) = await ServiceItem.Create();

            serviceItem? serviceItem = await ServiceItem.Search(new serviceItemWhereInput { id = id });

            serviceItem.ShouldHaveSameValuesAsInput(input);
        }

        [Fact]
        public async Task GIVEN_serviceItem_added_WHEN_requested_with_Name_THEN_returns_serviceItem()
        {
            string serviceItemName = Guid.NewGuid().ToString();
            await ServiceItem.Create(serviceItemName);

            serviceItem? serviceItem = await ServiceItem.Search(new serviceItemWhereInput { name = serviceItemName });

            serviceItem!.name.Should().Be(serviceItemName);
        }

        [Fact]
        public async Task GIVEN_serviceItem_added_WHEN_requested_In_serviceItemNames_THEN_returns_serviceItem()
        {
            string serviceItemName1 = Guid.NewGuid().ToString();
            string serviceItemName2 = Guid.NewGuid().ToString();
            List<string> serviceItemNames = new() { serviceItemName1, serviceItemName2 };
            await ServiceItem.Create(serviceItemName1);

            serviceItem? serviceItem = await ServiceItem.Search(new serviceItemWhereInput { name_in = serviceItemNames });

            serviceItem!.name.Should().Be(serviceItemName1);
        }

        [Fact]
        public async Task GIVEN_dataSchema_added_WHEN_update_this_dataSchema_THEN_succeed_and_updated()
        {
            (string id, _) = await ServiceItem.Create();

            updateServiceItemInput updateserviceItemInput = new()
            {
                id = id,
                name = Guid.NewGuid().ToString(),
                description = Guid.NewGuid().ToString(),
                isDescriptionChanged = true,
                isNameChanged = true
            };
            await ServiceItem.Update(updateserviceItemInput);
            serviceItem? serviceItem = await ServiceItem.Search(new serviceItemWhereInput { id = id });

            serviceItem.ShouldHaveSameValuesAsInput(updateserviceItemInput);
        }

        [Fact]
        public async Task GIVEN_serviceItem_added_WHEN_delete_this_serviceItem_THEN_succeed_and_deletes()
        {
            (string id, _) = await ServiceItem.Create();

            deleteServiceItemInput deleteserviceItemInput = new() { serviceItemId = id };
            await ServiceItem.Delete(deleteserviceItemInput);

            serviceItem? noserviceItem = await ServiceItem.Search(new serviceItemWhereInput { id = id });
            noserviceItem.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_noServiceItemExists_WHEN_requested_THEN_NoError()
        {
            string serviceItemId = new Guid().ToString();
            serviceItem? noserviceItem = await ServiceItem.Search(new serviceItemWhereInput { id = serviceItemId });
            noserviceItem.Should().BeNull();
        }
    }
}

