﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Policies
{
    public class PolicyTagTests : TestsBase
    {
        public PolicyTagTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_adding_tag_THEN_it_can_be_queried_by_tag_filter()
        {
            string id = await Policy.Create();
            string tagType = CreateNewGuid();
            string addedTagId = await Policy.AddTag(id, new tagInput { type = tagType });
            addedTagId.Should().NotBeNull();

            List<policy>? policies = await Policy.FindList(new policyWhereInput { tags_some = new tagWhere { type = tagType } });
            policy policy = policies.First(p => p.id == id);
            policy.Should().NotBeNull();
            policy.tags!.Select(t => t!.id).Should().Contain(addedTagId);
        }

        [Fact]
        public async Task GIVEN_policy_with_tag_WHEN_removing_tag_THEN_it_is_not_returned_with_tag_query()
        {
            string id = await Policy.Create();
            string tagType = CreateNewGuid();
            string addedTagId = await Policy.AddTag(id, new tagInput { type = tagType });
            addedTagId.Should().NotBeNull();

            await Policy.RemoveTag(id, addedTagId);

            List<policy>? policies = await Policy.FindList(new policyWhereInput { tags_some = new tagWhere { type = tagType } });
            policies!.Select(i => i.id).Should().NotContain(id);
        }
    }
}
