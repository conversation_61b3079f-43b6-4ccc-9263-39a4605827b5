﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Policies
{
    public class PolicyCrudTests : TestsBase
    {
        public PolicyCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_policies_WHEN_request_count_without_filter_THEN_successfully_return_count()
        {
            initializePolicyInput input = new() { description = "test policy", };
            await Policy.Create(input);

            string? query = new QueryBuilder().policies(new QueryBuilder.policiesArgs(50), new policiesBuilder().totalCount()).Build();
            policies result = await _client.SendQueryAsync<policies>(query);
            result.totalCount.Should().BeGreaterOrEqualTo(1);
        }

        [Fact]
        public async Task GIVEN_policies_WHEN_create_without_fields_and_schemas_THEN_returns_id()
        {
            initializePolicyInput input = new() { description = "test policy", };

            string policyId = await Policy.Create(input);

            policyId.Should().NotBeNullOrWhiteSpace();
        }

        [Fact]
        public async Task GIVEN_policies_WHEN_filter_by_status_not_in_THEN_returns_policies_without_status()
        {
            var status1 = CreateNewGuid();
            var status2 = CreateNewGuid();

            initializePolicyInput input1 = new() { description = "test policy", status = status1 };

            string policyId1 = await Policy.Create(input1);

            initializePolicyInput input2 = new() { description = "test policy 2", status = status2 };

            string policyId2 = await Policy.Create(input2);

            ICollection<policy> filteredPolicies = await PoliciesSearchAsync(new policyWhereInput { and = new List<policyWhereInput?>()
            {
                new policyWhereInput()
                {
                    status_not_in = new List<string?> { status2 }
                },
                new policyWhereInput()
                {
                    status_in = new List<string?> { status1 }
                }
            }});

            IEnumerable<string> filteredPoliciesIds = filteredPolicies.Select(c => c.id).ToArray()!;

            filteredPoliciesIds.Should().Contain(policyId1);
            filteredPoliciesIds.Should().NotContain(policyId2);
        }

        [Fact]
        public async Task GIVEN_policy_created_without_fields_and_schemas_WHEN_request_this_policy_THEN_returns_policy()
        {
            initializePolicyInput input = new() { description = "test policy", };
            string policyId = await Policy.Create(input);

            policy policy = await FindById(policyId);

            policy.generatedFrom.Should().BeNull();
            policy.description.Should().BeEquivalentTo(input.description);
        }

        [Fact]
        public async Task GIVEN_policy_created_without_fields_and_schemas_WHEN_update_this_policy_THEN_succeed_and_updated()
        {
            initializePolicyInput input = new() { description = "test policy", };
            string policyId = await Policy.Create(input);

            updatePolicyInput updatePolicyInput = new()
            {
                description = "updated value",
                lapseReason = "lapseReason"
            };
            await Policy.Update(policyId, updatePolicyInput);

            policy policy = await FindById(policyId);

            policy.description.Should().BeEquivalentTo(updatePolicyInput.description);
            policy.lapseReason.Should().Be("lapseReason");
        }

        [Fact]
        public async Task GIVEN_policies_WHEN_create_with_fields_and_schemas_THEN_returns_id()
        {
            (string schemaId, _) = await DataSchema.Create();
            initializePolicyInput input = new()
            {
                description = "test policy",
                fields = "{}",
                fieldsSchemaId = schemaId,
            };

            string policyId = await Policy.Create(input);

            policyId.Should().NotBeNullOrWhiteSpace();
        }

        [Fact]
        public async Task GIVEN_policy_created_with_fields_and_schemas_WHEN_request_this_policy_THEN_returns_policy()
        {
            (string schemaId, createDataSchemaInput createDataSchemaInput) = await DataSchema.Create();
            initializePolicyInput input = new()
            {
                description = "test policy",
                fields = "{}",
                fieldsSchemaId = schemaId,
            };
            string policyId = await Policy.Create(input);

            policy policy = await FindById(policyId);

            policy.description.Should().Be(input.description);
            policy.fields.Should().Be(input.fields);
            policy.fieldsSchema!.name.Should().Be(createDataSchemaInput.name);
        }

        [Fact]
        public async Task GIVEN_policy_created_with_fields_and_schemas_WHEN_update_this_policy_THEN_succeed_and_updated()
        {
            initializePolicyInput input = new()
            {
                description = "test policy",
                fields = "{}",
                fieldsSchemaId = "test value",
            };
            string policyId = await Policy.Create(input);

            (string schemaId, createDataSchemaInput createDataSchemaInput) = await DataSchema.Create();
            updatePolicyInput updatePolicyInput = new()
            {
                description = "updated value",
                fields = "{ updated: true }",
                extraFields = "{ updated: true }",
                fieldsSchemaId = schemaId
            };
            await Policy.Update(policyId, updatePolicyInput);

            policy policy = await FindById(policyId);

            policy.description.Should().BeEquivalentTo(updatePolicyInput.description);
            policy.fields.Should().Contain("updated");
            policy.extraFields.Should().Contain("updated");
            policy.fieldsSchema!.name.Should().BeEquivalentTo(createDataSchemaInput.name);
        }

        [Fact]
        public async Task GIVEN_policy_created_WHEN_renew_this_policy_THEN_a_new_renewal_policy_is_created()
        {
            initializePolicyInput input1 = new()
            {
                description = "test policy",
                fields = "{}",
            };
            string policyId = await Policy.Create(input1);

            initializePolicyInput input2 = new()
            {
                description = "renewal policy",
                fields = "{}",
                isRenewal = true,
                previousPolicyIds = new List<string?>
                {
                    policyId
                }
            };

            string renewalPolicyId = await Policy.Create(input2);

            policy renewalPolicy = await FindById(renewalPolicyId);
            policy? originalPolicy = renewalPolicy.previousPolicies!.Single();

            renewalPolicy.description.Should().Be(input2.description);
            renewalPolicy.fields.Should().Be(input2.fields);
            renewalPolicy.isRenewal.Should().BeTrue();
            renewalPolicy.previousPolicies.Should().HaveCount(1);
            originalPolicy!.id.Should().Be(policyId);
            originalPolicy.description.Should().Be(input1.description);
            originalPolicy.fields.Should().Be(input1.fields);
            originalPolicy.isRenewal.Should().BeFalse();
            originalPolicy.previousPolicies.Should().HaveCount(0);
        }

        [Fact]
        public async Task GIVEN_policy_created_product_assigned_WHEN_trying_to_update_policy_product_with_endorsements_THEN_success_and_updated()
        {
            string policyId = await Policy.Create();

            policy policy = await FindById(policyId);
            policy.product.Should().BeNull();

            string endorsementId = await Policy.Endorsement.Add(policyId);
            endorsementId.Should().NotBeNullOrEmpty();
            await Policy.Endorsement.Accept(policyId, endorsementId);
            productId productId = await Product.Create();

            await Policy.UpdateProductAsync(policyId, productId, endorsementId);

            policy = await FindById(policyId);
            policy.product.Should().NotBeNull();
            policy.product!.productId.Should().Be(productId);
        }

        [Fact]
        public async Task GIVEN_policy_created_product_assigned_WHEN_trying_to_update_policy_product_without_endorsements_THEN_success_and_updated()
        {
            string policyId = await Policy.Create();

            policy policy = await FindById(policyId);
            policy.product.Should().BeNull();

            productId productId = await Product.Create();

            await Policy.UpdateProductAsync(policyId, productId);

            policy = await FindById(policyId);
            policy.product.Should().NotBeNull();
            policy.product!.productId.Should().Be(productId);
        }

        [Fact]
        public async Task GIVEN_policy_created_WHEN_create_endorsement_with_type_THEN_successfully_created()
        {
            string policyId = await Policy.Create();

            await Policy.Endorsement.Add(policyId, "test-type");

            policy policy = await FindById(policyId);
            policy.Should().NotBeNull();
            policy.endorsements.Should().NotBeEmpty();

            endorsement? endorsement = policy.endorsements!.Single();
            endorsement!.type.Should().Be("test-type");
        }

        [Fact]
        public async Task GIVEN_policy_created_WHEN_create_endorsement_with_reasonOfChange_THEN_successfully_created()
        {
            string reasonOfChange = CreateNewGuid();
            string policyId = await Policy.Create();

            await Policy.Endorsement.Add(policyId, null, reasonOfChange);

            policy policy = await FindById(policyId);
            policy.Should().NotBeNull();
            policy.endorsements.Should().NotBeEmpty();

            endorsement? endorsement = policy.endorsements!.Single();
            endorsement!.reasonOfChange.Should().Be(reasonOfChange);
        }

        [Fact]
        public async Task GIVEN_policy_created_WHEN_create_endorsement_with_cancellationMotive_THEN_successfully_created()
        {
            string cancellationMotive = CreateNewGuid();
            string policyId = await Policy.Create();

            await Policy.Endorsement.Add(policyId, null, cancellationMotive: cancellationMotive);

            policy policy = await FindById(policyId);
            policy.Should().NotBeNull();
            policy.endorsements.Should().NotBeEmpty();

            endorsement? endorsement = policy.endorsements!.Single();
            endorsement!.cancellationMotive.Should().Be(cancellationMotive);
        }

        [Fact]
        public async Task GIVEN_policy_with_p400_filled_WHEN_generating_debit_note_number_THEN_generated_with_increment()
        {
            const string p400 = "012345678";
            string policyId = await Policy.Create(new initializePolicyInput
            {
                fields = @$"{{ ""p400PolicyNumber"": ""{p400}"" }}".Escape()
            });

            string mutation = new MutationBuilder()
                .policyNextDebitNoteNumber(new MutationBuilder.policyNextDebitNoteNumberArgs(policyId),
                    new policyDebitNoteNumberBuilder()
                        .debitNoteNumber())
                .Build();

            policyDebitNoteNumber debitNote = await _client.SendMutationAsync<policyDebitNoteNumber>(mutation);
            debitNote.debitNoteNumber.Should().Be($"GMD-{p400}-00001");

            debitNote = await _client.SendMutationAsync<policyDebitNoteNumber>(mutation);
            debitNote.debitNoteNumber.Should().Be($"GMD-{p400}-00002");
        }

        [Fact]
        public async Task GIVEN_policy_with_p400_filled_WHEN_generating_member_movement_number_THEN_generated_with_increment()
        {
            const string p400 = "012345678";
            string policyId = await Policy.Create(new initializePolicyInput
            {
                fields = @$"{{ ""p400PolicyNumber"": ""{p400}"" }}".Escape(),
                startDate = new DateTime(2020, 01, 01)
            });

            string mutation = new MutationBuilder()
                .policyNextMemberMovementNumber(new MutationBuilder.policyNextMemberMovementNumberArgs(policyId),
                    new policyMemberMovementNumberBuilder()
                        .memberMovementNumber())
                .Build();

            policyMemberMovementNumber memberMovement = await _client.SendMutationAsync<policyMemberMovementNumber>(mutation);
            memberMovement.memberMovementNumber.Should().Be($"012345678-2020/V001");

            memberMovement = await _client.SendMutationAsync<policyMemberMovementNumber>(mutation);
            memberMovement.memberMovementNumber.Should().Be($"012345678-2020/V002");
        }

        async Task<policy> FindById(string policyId)
        {
            string query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: new policyWhereInput { id = policyId }), new policiesBuilder()
                    .list(new policyBuilder()
                          .id()
                          .generatedFrom(new generatedFromBuilder()
                            .caseId()
                            .proposalId()
                            .offerId()
                          )
                          .description()
                          .contractHolder(new entityInterfaceBuilder()
                            .id()
                            .fields())
                          .fields()
                          .extraFields()
                          .isRenewal()
                          .lapseReason()
                          .product(new productBuilder()
                              .productId(new productIdBuilder().WithAllFields()))
                          .endorsements(new policyBuilder.endorsementsArgs(), new endorsementBuilder()
                            .id()
                            .status()
                            .type()
                            .reasonOfChange()
                            .cancellationMotive())
                          .previousPolicies(new policyBuilder()
                              .id()
                              .description()
                              .fields()
                              .isRenewal()
                              .previousPolicies(new policyBuilder()
                                  .id())
                              .fieldsSchema(new dataSchemaBuilder()
                                  .id()
                                  .name()
                                  .description()
                                  .schema()
                                  .standard(new dataSchemaStandardBuilder()
                                      .type()
                                      .version())
                                  .type()))
                          .fieldsSchema(new dataSchemaBuilder()
                              .id()
                              .name()
                              .description()
                              .schema()
                              .standard(new dataSchemaStandardBuilder()
                                  .type()
                                  .version())
                              .type())))
                .Build();

            policies response = await _client.SendQueryAsync<policies>(query);
            return response.list!.First()!;
        }

        private async Task<ICollection<policy>> PoliciesSearchAsync(policyWhereInput where)
        {
            string? query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: where),
                new policiesBuilder()
                    .list(new policyBuilder()
                        .id()
                        .issuerNumber()
                        .originalIssuerNumber()
                        .isRenewal()
                        .renewalVersion()
                        .previousPolicies(new policyBuilder()
                            .id()
                            .isRenewal()
                            .renewalVersion()
                            .originalIssuerNumber())
                        .fields()))
            .Build();

            policies policies = await _client.SendQueryAsync<policies>(query);
            return policies!.list!;
        }
    }
}