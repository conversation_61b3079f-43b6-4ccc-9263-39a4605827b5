﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class PolicyFieldsDiffsTests : TestsBase
    {
        public PolicyFieldsDiffsTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_endorse_fields_THEN_policy_has_fieldsDiff()
        {
            string policyId = await Policy.Create();
            await Policy.Issue(policyId);

            const string fieldsFinalState = @"{
""members"":[
   {
     ""fullName"":""LU121"",
     ""hkid"":""LU121A""
   }]}";

            string endorsementId = await Policy.Endorsement.Add(policyId);
            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsFinalState.Escape() }, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            policy policy = await FindPolicyById(policyId);
            policy.fieldsDiffs!.Single().Should().NotBeNull();
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_add_member_THEN_policy_has_fieldsDiff_with_add_member()
        {
            string policyId = await Policy.Create();

            await Policy.Issue(policyId);

            const string fieldsFinalState = @"{
""members"":[
   {
     ""fullName"":""LU121"",
     ""hkid"":""LU121A""
   }]}";

            string endorsementId = await Policy.Endorsement.Add(policyId);
            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsFinalState.Escape() }, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            policy policy = await FindPolicyById(policyId);
            fieldsDiff diff = policy.fieldsDiffs!.Single()!;

            diff.diffType.Should().Be(FieldsDiffType.ADD);
            diff.before.Should().BeNull();
            diff.after!.RemoveWhitespaces().Should().Be(@"{""fullName"":""LU121"",""hkid"":""LU121A""}");
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_remove_member_THEN_policy_has_fieldsDiff_with_remove_member()
        {
            string policyId = await Policy.Create();
            const string fieldsOriginalState = @"{
""members"":[
   {
     ""fullName"":""LU121"",
     ""hkid"":""LU121A""
   }]}";

            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsOriginalState.Escape() });
            await Policy.Issue(policyId);

            const string fieldsFinalState = "{}";

            string endorsementId = await Policy.Endorsement.Add(policyId);
            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsFinalState.Escape() }, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            policy policy = await FindPolicyById(policyId);
            fieldsDiff diff = policy.fieldsDiffs!.Last()!;

            diff.diffType.Should().Be(FieldsDiffType.REMOVE);
            diff.before!.RemoveWhitespaces().Should().Be(@"{""fullName"":""LU121"",""hkid"":""LU121A""}");
            diff.after.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_change_member_THEN_policy_has_fieldsDiff_with_change_member()
        {
            string policyId = await Policy.Create();
            const string fieldsOriginalState = @"{
""members"":[
   {
     ""fullName"":""LU121"",
     ""hkid"":""LU121A""
   }]}";

            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsOriginalState.Escape() });
            await Policy.Issue(policyId);

            const string fieldsFinalState = @"{
""members"":[
   {
     ""fullName"":""TEST"",
     ""hkid"":""LU121A""
   }]}";

            string endorsementId = await Policy.Endorsement.Add(policyId);
            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsFinalState.Escape() }, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            policy policy = await FindPolicyById(policyId);
            fieldsDiff diff = policy.fieldsDiffs!.Last()!;

            diff.diffType.Should().Be(FieldsDiffType.UPDATE);
            diff.before!.RemoveWhitespaces().Should().Be(@"{""fullName"":""LU121"",""hkid"":""LU121A""}");
            diff.after!.RemoveWhitespaces().Should().Be(@"{""fullName"":""TEST"",""hkid"":""LU121A""}");
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_add_remove_and_change_members_at_once_THEN_policy_has_fieldsDiffs_with_all_types_of_member_movement()
        {
            string policyId = await Policy.Create();

            const string fieldsOriginalState = @"{
""members"":[
   {
     ""fullName"":""Name1"",
     ""hkid"":""hkid1""
   },
   {
     ""fullName"":""Name2"",
     ""hkid"":""hkid2""
   }
]}";

            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsOriginalState.Escape() });
            await Policy.Issue(policyId);

            const string fieldsFinalState = @"{
""members"":[
   {
     ""fullName"":""Name3"",
     ""hkid"":""hkid1""
   },
   {
     ""fullName"":""Name4"",
     ""hkid"":""hkid3""
   }
]}";

            string endorsementId = await Policy.Endorsement.Add(policyId);
            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsFinalState.Escape() }, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            policy policy = await FindPolicyById(policyId);
            fieldsDiff addDiff = policy.fieldsDiffs!.Last(f => f!.diffType == FieldsDiffType.ADD)!;

            addDiff.before.Should().BeNull();
            addDiff.after!.RemoveWhitespaces().Should().Be(@"{""fullName"":""Name4"",""hkid"":""hkid3""}");

            fieldsDiff removeDiff = policy.fieldsDiffs!.Single(f => f!.diffType == FieldsDiffType.REMOVE)!;

            removeDiff.before!.RemoveWhitespaces().Should().Be(@"{""fullName"":""Name2"",""hkid"":""hkid2""}");
            removeDiff.after.Should().BeNull();

            fieldsDiff updateDiff = policy.fieldsDiffs!.Single(f => f!.diffType == FieldsDiffType.UPDATE)!;

            updateDiff.before!.RemoveWhitespaces().Should().Be(@"{""fullName"":""Name1"",""hkid"":""hkid1""}");
            updateDiff.after!.RemoveWhitespaces().Should().Be(@"{""fullName"":""Name3"",""hkid"":""hkid1""}");
        }

        [Fact]
        public async Task GIVEN_policy_with_fieldsDiffs_WHEN_filter_policy_by_fieldsDiffs_THEN_returns_this_policy()
        {
            string policyId = await Policy.Create();
            await Policy.Issue(policyId);

            const string fieldsFinalState = @"{
""members"":[
   {
     ""fullName"":""LU121"",
     ""hkid"":""LU121A""
   }]}";

            string endorsementId = await Policy.Endorsement.Add(policyId);
            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsFinalState.Escape() }, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            policyWhereInput where = new()
            {
                and = new policyWhereInput[] {
                    new() { fieldsDiff_contains = new fieldsDiffWhereInput { to = DateTimeOffset.Now.AddSeconds(2) } },
                    new() { id = policyId }
                }
            };

            ICollection<policy> policies = await FindPolicies(where);
            policies.Should().NotBeEmpty();
        }

        [Fact]
        public async Task GIVEN_policy_with_fieldsDiffs_WHEN_filter_policy_by_fieldsDiffs_from_and_to_THEN_returns_this_policy()
        {
            string policyId = await Policy.Create();

            DateTimeOffset start = DateTimeOffset.Now;

            const string fieldsFinalState = @"{
""members"":[
   {
     ""fullName"":""LU121"",
     ""hkid"":""LU121A""
   }]}";

            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsFinalState.Escape() });

            policyWhereInput where = new()
            {
                and = new policyWhereInput[] {
                    new() { fieldsDiff_contains = new fieldsDiffWhereInput { from = start, to = DateTimeOffset.Now.AddSeconds(2) } },
                    new() { id = policyId }
                }
            };

            ICollection<policy> policies = await FindPolicies(where);
            policies.Should().NotBeEmpty();
        }

        [Fact]
        public async Task GIVEN_policy_with_fieldsDiffs_WHEN_filter_policy_by_wrong_fieldsDiffs_from_and_to_THEN_returns_nothing()
        {
            string policyId = await Policy.Create();

            const string fieldsState1 = @"{
""members"":[
   {
     ""fullName"":""1"",
     ""hkid"":""LU121A""
   }]}";
            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsState1.Escape() });

            DateTimeOffset start = DateTimeOffset.Now;

            const string fieldsState2 = @"{
""members"":[
   {
     ""fullName"":""2"",
     ""hkid"":""LU121A""
   }]}";
            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsState2.Escape() });

            DateTimeOffset end = DateTimeOffset.Now;

            const string fieldsState3 = @"{
""members"":[
   {
     ""fullName"":""3"",
     ""hkid"":""LU121A""
   }]}";
            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsState3.Escape() });

            policyWhereInput where = new()
            {
                and = new policyWhereInput[] {
                    new() { fieldsDiff_contains = new fieldsDiffWhereInput { from = end, to = start } },
                    new() { id = policyId }
                }
            };

            ICollection<policy> policies = await FindPolicies(where);
            policies.Should().BeEmpty();
        }

        [Fact]
        public async Task GIVEN_policy_without_fieldsDiffs_WHEN_filter_policy_by_fieldsDiffs_THEN_returns_nothing()
        {
            string policyId = await Policy.Create();
            await Policy.Issue(policyId);

            policyWhereInput where = new()
            {
                and = new policyWhereInput[] {
                    new() { fieldsDiff_contains = new fieldsDiffWhereInput { to = DateTime.Now.AddSeconds(2) } },
                    new() { id = policyId }
                }
            };

            ICollection<policy> policies = await FindPolicies(where);
            policies.Should().BeEmpty();
        }

        [Fact]
        public async Task GIVEN_policy_with_fieldsDiffs_WHEN_filter_fieldsDiffs_THEN_returns_this_policy_with_relevant_fieldsDiffs()
        {
            string policyId = await Policy.Create();
            const string fieldsOriginalState = "{}";

            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsOriginalState });
            await Policy.Issue(policyId);

            const string fieldsState = @"{
""members"":[
   {
     ""fullName"":""LU121"",
     ""hkid"":""LU121A""
   }]}";

            string endorsementId = await Policy.Endorsement.Add(policyId);
            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsState.Escape() }, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            await Task.Delay(TimeSpan.FromSeconds(2));

            DateTime from = DateTime.Now;

            const string fieldsFinalState = @"{
""members"":[
   {
     ""fullName"":""TEST"",
     ""hkid"":""LU121A""
   }]}";

            endorsementId = await Policy.Endorsement.Add(policyId);
            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsFinalState.Escape() }, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            policyWhereInput where = new() { id = policyId };

            string query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: where), new policiesBuilder()
                    .list(new policyBuilder()
                          .id()
                          .fieldsDiffs(new policyBuilder.fieldsDiffsArgs(new fieldsDiffWhereInput { from = from }), new fieldsDiffBuilder()
                            .diffType()
                            .before()
                            .after()
                            .timeStamp())))
                .Build();

            policies policies = await _client.SendQueryAsync<policies>(query);

            policies.list!.Single()!.fieldsDiffs!.Single()!.after!.RemoveWhitespaces().Should().Be(@"{""fullName"":""TEST"",""hkid"":""LU121A""}");
        }

        [Fact]
        public async Task GIVEN_unissued_policy_with_fieldsDiffs_WHEN_filter_fieldsDiffs_THEN_returns_this_policy_with_relevant_fieldsDiffs()
        {
            string policyId = await Policy.Create();
            const string fieldsOriginalState = "{}";

            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsOriginalState });

            const string fieldsState = @"{
""members"":[
   {
     ""fullName"":""LU121"",
     ""hkid"":""LU121A""
   }]}";

            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsState.Escape() });

            await Task.Delay(TimeSpan.FromSeconds(2));

            DateTime from = DateTime.Now;

            const string fieldsFinalState = @"{
""members"":[
   {
     ""fullName"":""TEST"",
     ""hkid"":""LU121A""
   }]}";

            await Policy.Update(policyId, new updatePolicyInput { fields = fieldsFinalState.Escape() });

            policyWhereInput where = new() { id = policyId };

            string query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: where), new policiesBuilder()
                    .list(new policyBuilder()
                          .id()
                          .fieldsDiffs(new policyBuilder.fieldsDiffsArgs(new fieldsDiffWhereInput { from = from }), new fieldsDiffBuilder()
                            .diffType()
                            .before()
                            .after()
                            .timeStamp())))
                .Build();

            policies policies = await _client.SendQueryAsync<policies>(query);

            policies.list!.Single()!.fieldsDiffs!.Single()!.after!.RemoveWhitespaces().Should().Be(@"{""fullName"":""TEST"",""hkid"":""LU121A""}");
        }

        async Task<ICollection<policy>> FindPolicies(policyWhereInput where)
        {
            string query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: where), new policiesBuilder()
                    .list(new policyBuilder()
                          .id()
                          .fieldsDiffs(new policyBuilder.fieldsDiffsArgs(), new fieldsDiffBuilder()
                            .diffType()
                            .before()
                            .after()
                            .timeStamp())))
                .Build();

            policies response = await _client.SendQueryAsync<policies>(query);
            return response.list!;
        }

        async Task<policy> FindPolicyById(string policyId)
        {
            ICollection<policy> policies = await FindPolicies(new policyWhereInput { id = policyId });
            return policies.Single();
        }
    }
}