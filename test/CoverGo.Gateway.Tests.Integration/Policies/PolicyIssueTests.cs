using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Policies
{
    public class PolicyIssueTests : TestsBase
    {
        public PolicyIssueTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_policy_with_p400_field_and_gm_product_WHEN_issuing_policy_THEN_isserNumber_generated_with_p400_field()
        {
            productId product = await CreateProductAsync();
            string policyId = await CreatePolicyAsync(product, "ASDQWE");

            await Policy.Issue(policyId);

            policy? policy = await PolicyFindById(policyId);

            policy!.isRenewal.Should().Be(false);
            policy!.previousPolicies.Should().BeEmpty();
            policy!.issuerNumber.Should().Be("GMD-ASDQWE");
        }

        [Fact]
        public async Task GIVEN_policy_renewed_from_policy_with_p400_field_and_gm_product_WHEN_issuing_renewed_policy_THEN_isserNumber_generated_with_p400_field()
        {
            productId product = await CreateProductAsync();
            string originalPolicyId = await CreatePolicyAsync(product, "ASDQWE");

            await Policy.Issue(originalPolicyId);

            policy? originalPolicy = await PolicyFindById(originalPolicyId);
            policy? renewedPolicy1 = await PolicyRenewFrom(originalPolicy!, "RENEWAL1");

            await Policy.Issue(renewedPolicy1!.id!);

            renewedPolicy1 = await PolicyFindById(renewedPolicy1.id!);
            renewedPolicy1!.isRenewal.Should().Be(true);
            renewedPolicy1!.previousPolicies!.Select(p => p!.id).ToList().Should().BeEquivalentTo(new List<string> { originalPolicy!.id! });
            renewedPolicy1!.originalIssuerNumber.Should().Be("GMD-ASDQWE");
            renewedPolicy1!.issuerNumber.Should().BeEquivalentTo("GMD-RENEWAL1");
            renewedPolicy1.renewalVersion.Should().Be(1);


            policy? renewedPolicy2 = await PolicyRenewFrom(renewedPolicy1!, "RENEWAL2");

            await Policy.Issue(renewedPolicy2!.id!);

            renewedPolicy2 = await PolicyFindById(renewedPolicy2.id!);
            renewedPolicy2!.isRenewal.Should().Be(true);
            renewedPolicy2!.previousPolicies!.Select(p => p!.id).ToList().Should().BeEquivalentTo(new List<string> { originalPolicy!.id!, renewedPolicy1!.id! });
            renewedPolicy2!.originalIssuerNumber.Should().Be("GMD-ASDQWE");
            renewedPolicy2!.issuerNumber.Should().BeEquivalentTo("GMD-RENEWAL2");
            renewedPolicy2.renewalVersion.Should().Be(2);
        }

        private Task<productId> CreateProductAsync() => Product.Create(new createProductInput
        {
            productId = new productIdInput
            {
                type = "gm",
                plan = CreateNewGuid(),
                version = CreateNewGuid()
            }
        });

        private Task<string> CreatePolicyAsync(productId productId, string? p400 = null)
        {
            initializePolicyInput input = new()
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = p400 != null ? $"{{ \"p400PolicyNumber\": \"{p400}\" }}".Escape() : null,
                productId = new productIdInput
                {
                    plan = productId.plan,
                    version = productId.version,
                    type = productId.type
                }
            };
            return Policy.Create(input);
        }

        private async Task<policy?> PolicyRenewFrom(policy policy, string? p400 = null)
        {
            List<string?> relatedIds = policy?.previousPolicies?.Select(p => p!.id).ToList() ?? new List<string?>();
            relatedIds.Add(policy!.id);

            productId productId = policy!.product!.productId!;

            string renewedPolicyId = await Policy.Create(new initializePolicyInput
            {
                description = policy.description,
                fields = p400 != null ? $"{{ \"p400PolicyNumber\": \"{p400}\" }}".Escape() : policy!.fields!.Escape(),
                previousPolicyIds = relatedIds,
                isRenewal = true,
                productId = new productIdInput
                {
                    plan = productId.plan,
                    version = productId.version,
                    type = productId.type
                }
            });
            return await PolicyFindById(renewedPolicyId);
        }

        private async Task<policy?> PolicyFindById(string policyId)
        {
            ICollection<policy> policies = await PoliciesSearchAsync(new policyWhereInput { id = policyId });
            return policies!.FirstOrDefault();
        }

        private async Task<ICollection<policy>> PoliciesSearchAsync(policyWhereInput where)
        {
            string? query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: where),
                new policiesBuilder()
                    .list(new policyBuilder()
                        .id()
                        .issuerNumber()
                        .originalIssuerNumber()
                        .isRenewal()
                        .renewalNumber()
                        .renewalVersion()
                        .product(new productBuilder()
                            .productId(new productIdBuilder()
                                .plan()
                                .version()
                                .type()))
                        .previousPolicies(new policyBuilder()
                            .id()
                            .isRenewal()
                            .renewalVersion()
                            .originalIssuerNumber())
                        .fields()))
            .Build();

            policies policies = await _client.SendQueryAsync<policies>(query);
            return policies!.list!;
        }
    }
}
