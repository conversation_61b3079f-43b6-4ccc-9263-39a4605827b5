using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Policies
{
    public class PolicyRenewalTests : TestsBase
    {
        public PolicyRenewalTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_renewing_THEN_successfully_stores_renewal_data()
        {
            policy? policy = await PolicyCreateAsync();

            string newIssuerNumber = CreateNewGuid();

            policy? renewedPolicy = await PolicyRenewFrom(policy!, newIssuerNumber);
            renewedPolicy!.renewalVersion.Should().Be(1);
            renewedPolicy.originalIssuerNumber.Should().Be(policy!.issuerNumber);
            renewedPolicy.isRenewal.Should().BeTrue();
            renewedPolicy.renewalNumber.Should().NotBeNullOrWhiteSpace();
            renewedPolicy.previousPolicies!.Select(p => p!.id).ToArray().Should().BeEquivalentTo(policy.id!);
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_renewing_with_renewal_number_THEN_renewed_policy_with_defined_renewal_number()
        {
            policy? policy = await PolicyCreateAsync();

            string newIssuerNumber = CreateNewGuid();
            string renewalNumber = CreateNewGuid();

            policy? renewedPolicy = await PolicyRenewFrom(policy!, newIssuerNumber, renewalNumber);
            renewedPolicy!.renewalVersion.Should().Be(1);
            renewedPolicy.originalIssuerNumber.Should().Be(policy!.issuerNumber);
            renewedPolicy.isRenewal.Should().BeTrue();
            renewedPolicy.renewalNumber.Should().Be(renewalNumber);
            renewedPolicy.previousPolicies!.Select(p => p!.id).ToArray().Should().BeEquivalentTo(policy.id!);
        }

        [Fact]
        public async Task GIVEN_original_policy_and_two_renewed_policies_WHEN_search_by_original_issuer_number_contains_THEN_success()
        {
            policy? originalPolicy = await PolicyCreateAsync();

            policy? renewedPolicy1 = await PolicyRenewFrom(originalPolicy!);
            renewedPolicy1!.renewalVersion.Should().Be(1);

            policy? renewedPolicy2 = await PolicyRenewFrom(renewedPolicy1);
            renewedPolicy2!.renewalVersion.Should().Be(2);

            ICollection<policy> renewedPolicies = await PoliciesSearchAsync(new policyWhereInput { originalIssuerNumber_contains = originalPolicy!.issuerNumber });
            string?[] renewedPoliciesIds = renewedPolicies.Select(p => p.id).ToArray();

            renewedPoliciesIds.Should().BeEquivalentTo(renewedPolicy1.id, renewedPolicy2.id);
        }

        [Fact]
        public async Task GIVEN_original_policy_and_two_renewed_policies_WHEN_search_by_original_issuer_number_in_THEN_success()
        {
            policy? originalPolicy = await PolicyCreateAsync();

            policy? renewedPolicy1 = await PolicyRenewFrom(originalPolicy!);
            renewedPolicy1!.renewalVersion.Should().Be(1);

            policy? renewedPolicy2 = await PolicyRenewFrom(renewedPolicy1);
            renewedPolicy2!.renewalVersion.Should().Be(2);

            ICollection<policy> renewedPolicies = await PoliciesSearchAsync(new policyWhereInput { originalIssuerNumber_in = new[] { originalPolicy!.issuerNumber } });
            string?[] renewedPoliciesIds = renewedPolicies.Select(p => p.id).ToArray();

            renewedPoliciesIds.Should().BeEquivalentTo(renewedPolicy1.id, renewedPolicy2.id);
        }

        [Fact]
        public async Task GIVEN_original_policy_and_renewed_policy_WHEN_search_by_original_issuer_number_and_renewalVersion_THEN_success()
        {
            policy? originalPolicy = await PolicyCreateAsync();

            policy? renewedPolicy1 = await PolicyRenewFrom(originalPolicy!);

            ICollection<policy> renewedPolicies = await PoliciesSearchAsync(new policyWhereInput
            {
                and = new[]
                {
                    new policyWhereInput
                    {
                        originalIssuerNumber_contains = originalPolicy!.issuerNumber
                    },
                    new policyWhereInput
                    {
                        renewalVersion = 1
                    },
                }
            });
            policy policy = renewedPolicies.Single();
            policy.id.Should().Be(renewedPolicy1!.id);
        }

        [Fact]
        public async Task GIVEN_original_policy_and_three_renewed_policies_WHEN_search_by_original_issuer_number_and_renewalVersion_gt_THEN_success()
        {
            policy? originalPolicy = await PolicyCreateAsync();

            policy? renewedPolicy1 = await PolicyRenewFrom(originalPolicy!);
            policy? renewedPolicy2 = await PolicyRenewFrom(renewedPolicy1!);
            policy? renewedPolicy3 = await PolicyRenewFrom(renewedPolicy2!);

            ICollection<policy> renewedPolicies = await PoliciesSearchAsync(new policyWhereInput
            {
                and = new[]
                {
                    new policyWhereInput
                    {
                        originalIssuerNumber_contains = originalPolicy!.issuerNumber
                    },
                    new policyWhereInput
                    {
                        renewalVersion_gt = 2
                    },
                }
            });
            policy policy = renewedPolicies.Single();
            policy.id.Should().Be(renewedPolicy3!.id);
        }

        [Fact]
        public async Task GIVEN_original_policy_and_three_renewed_policies_WHEN_search_by_original_issuer_number_and_renewalVersion_lt_THEN_success()
        {
            policy? originalPolicy = await PolicyCreateAsync();

            policy? renewedPolicy1 = await PolicyRenewFrom(originalPolicy!);
            policy? renewedPolicy2 = await PolicyRenewFrom(renewedPolicy1!);
            await PolicyRenewFrom(renewedPolicy2!);

            ICollection<policy> renewedPolicies = await PoliciesSearchAsync(new policyWhereInput
            {
                and = new[]
                {
                    new policyWhereInput
                    {
                        originalIssuerNumber_contains = originalPolicy!.issuerNumber
                    },
                    new policyWhereInput
                    {
                        renewalVersion_lt = 2
                    },
                }
            });
            policy policy = renewedPolicies.Single();
            policy.id.Should().Be(renewedPolicy1!.id);
        }

        [Theory]
        [InlineData("LAPSED", "LAPSED", "LAPSED", "LAPSED")]
        [InlineData("ISSUED", "ISSUED", "IN_FORCE", "IN_FORCE")]
        [InlineData("REJECTED", "REJECTED", "IN_FORCE", "IN_FORCE")]
        [InlineData("DRAFT", "ACCEPTED", "ISSUED", "ISSUED")]
        [InlineData("ISSUED", "ISSUED", "EXPIRED", "EXPIRED")]
        [InlineData("DRAFT", "DRAFT", "DRAFT", "DRAFT")]
        [InlineData("SENT", "SENT", "SENT", "SENT")]
        [InlineData("DRAFT", "SENT", "DRAFT", "SENT")]
        [InlineData("DRAFT", "SENT", "REJECTED", "SENT")]
        [InlineData("SENT", "SENT", "REJECTED", "SENT")]
        [InlineData("ACCEPTED", "SENT", "DRAFT", "ACCEPTED")]
        public async Task GIVEN_an_original_policy_with_three_renewals_WHEN_search_by_filter_contains_isHighestPriorityRenewalPerPolicy_THEN_return_only_one_correct_renewal_each_policy(string statusOfRenewal1, string statusOfRenewal2, string statusOfRenewal3, string expectedStatus)
        {
            policy? originalPolicy1 = await PolicyCreateAsync();

            policy? renewedPolicy1 = await PolicyRenewFrom(originalPolicy1!, status: statusOfRenewal1);
            policy? renewedPolicy2 = await PolicyRenewFrom(renewedPolicy1!, status: statusOfRenewal2);
            policy? renewedPolicy3 = await PolicyRenewFrom(renewedPolicy2!, status: statusOfRenewal3);


            ICollection<policy> renewedPolicies = await PoliciesSearchAsync(new policyWhereInput
            {
                and = new[]
                {
                    new policyWhereInput
                    {
                        isHighestPriorityRenewalPerPolicy = true
                    },
                    new policyWhereInput
                    {
                        previousPolicyIds_contains_some = new List<string?> {originalPolicy1!.id}
                    }
                }
            });
            policy policy = renewedPolicies.Single();
            policy.status.Should().Be(expectedStatus);
        }

        private async Task<ICollection<policy>> PoliciesSearchAsync(policyWhereInput where)
        {
            string? query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: where),
                new policiesBuilder()
                    .list(new policyBuilder()
                        .id()
                        .issuerNumber()
                        .originalIssuerNumber()
                        .isRenewal()
                        .renewalNumber()
                        .status()
                        .renewalVersion()
                        .previousPolicies(new policyBuilder()
                            .id()
                            .isRenewal()
                            .renewalVersion()
                            .originalIssuerNumber())
                        .fields()))
            .Build();

            policies policies = await _client.SendQueryAsync<policies>(query);
            return policies.list!;
        }

        private async Task<policy?> PolicyFindById(string policyId)
        {
            ICollection<policy> policies = await PoliciesSearchAsync(new policyWhereInput { id = policyId });
            return policies.FirstOrDefault();
        }

        private async Task<policy?> PolicyCreateAsync()
        {
            string policyId = await Policy.Create(new initializePolicyInput
            {
                issuerNumber = CreateNewGuid(),
                fields = "{}",
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
            });
            return await PolicyFindById(policyId);
        }

        private async Task<policy?> PolicyRenewFrom(policy policy, string? issuerNumber = null, string? renewalNumber = null, string? status = null)
        {
            issuerNumber ??= CreateNewGuid();

            List<string?> relatedIds = policy.previousPolicies?.Select(p => p!.id).ToList() ?? new List<string?>();
            relatedIds.Add(policy.id);
            string renewedPolicyId = await Policy.Create(new initializePolicyInput
            {
                fields = "{}",
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                issuerNumber = issuerNumber,
                originalIssuerNumber = policy.isRenewal ?? false ? policy.originalIssuerNumber : policy.issuerNumber,
                isRenewal = true,
                renewalNumber = renewalNumber,
                previousPolicyIds = relatedIds,
                renewalVersion = (policy.renewalVersion ?? 0) + 1,
                status = status,
            });
            return await PolicyFindById(renewedPolicyId);
        }
    }
}
