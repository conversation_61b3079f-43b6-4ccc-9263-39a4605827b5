using AutoFixture;
using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class PolicyAttachmentsTests : TestsBase
    {
        public PolicyAttachmentsTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_add_attachment_to_it_THEN_succeed_and_it_contains_attachment()
        {
            string policyId = await Policy.Create();
            Fixture fixture = new();

            attachmentInput? attachmentInput = fixture.Create<attachmentInput>();

            await AddAttachmentToPolicy(policyId, attachmentInput);

            policy policy = await FindPolicyById(policyId);

            policy.attachments!.Single()!.path.Should().Be(attachmentInput.path);
        }

        Task AddAttachmentToPolicy(string policyId, attachmentInput input)
        {
            string mutation = new MutationBuilder()
                .addPolicyAttachment(
                    new MutationBuilder.addPolicyAttachmentArgs(policyId, input), new resultBuilder()
                        .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        async Task<policy> FindPolicyById(string policyId)
        {
            string query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: new policyWhereInput { id = policyId }), new policiesBuilder()
                    .list(new policyBuilder()
                        .id()
                        .attachments(new attachmentBuilder()
                            .path())))
                .Build();

            policies response = await _client.SendQueryAsync<policies>(query);
            return response.list!.Single()!;
        }
    }
}