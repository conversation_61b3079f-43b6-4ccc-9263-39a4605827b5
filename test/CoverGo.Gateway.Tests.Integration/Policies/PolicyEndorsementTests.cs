using CoverGo.Gateway.Client;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Policies
{
    public class PolicyEndorsementTests : TestsBase
    {
        public PolicyEndorsementTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_endorse_product_twice_THEN_policy_has_product_from_last_endorsement()
        {
            string policyId = await Policy.Create();
            productId productId1 = await Product.Create();
            productId productId2 = await Product.Create();

            string endorsementId1 = await Policy.Endorsement.Add(policyId);
            await Policy.UpdateProductAsync(policyId, productId1, endorsementId1);
            await Policy.Endorsement.Accept(policyId, endorsementId1);

            string endorsementId2 = await Policy.Endorsement.Add(policyId);
            await Policy.UpdateProductAsync(policyId, productId2, endorsementId2);
            await Policy.Endorsement.Accept(policyId, endorsementId2);

            policy policy = await FindById(policyId);
            policy.product!.productId.Should().Be(productId2);
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_endorse_product_future_dated_THEN_policy_has_no_product_now()
        {
            string policyId = await Policy.Create();
            productId productId = await Product.Create();

            string endorsementId = await Policy.Endorsement.Add(policyId, effectiveDate: DateTime.Now.AddDays(1));
            await Policy.UpdateProductAsync(policyId, productId, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            policy policy = await FindById(policyId);
            policy.product.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_endorse_product_future_dated_THEN_policy_has_this_product_in_the_future()
        {
            string policyId = await Policy.Create();
            productId productId = await Product.Create();

            string endorsementId = await Policy.Endorsement.Add(policyId, effectiveDate: DateTime.Now.AddDays(1));
            await Policy.UpdateProductAsync(policyId, productId, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            policy policy = await FindById(policyId, DateTime.Now.AddDays(2));
            policy.product!.productId.Should().Be(productId);
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_creating_two_endorsements_with_same_type_THEN_fails()
        {
            string policyId = await Policy.Create();
            string endorsementType = CreateNewGuid();
            createdStatusResult endorsementCreateResult = await Policy.Endorsement.TryAdd(policyId, endorsementType);
            endorsementCreateResult.status.Should().Be("success");

            endorsementCreateResult = await Policy.Endorsement.TryAdd(policyId, endorsementType);
            endorsementCreateResult.status.Should().Be("failure");
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_creating_two_member_movement_endorsements_THEN_success()
        {
            string policyId = await Policy.Create();
            string endorsementType = "MEMBER_MOVEMENT";
            createdStatusResult endorsementCreateResult = await Policy.Endorsement.TryAdd(policyId, endorsementType);
            endorsementCreateResult.status.Should().Be("success");

            endorsementCreateResult = await Policy.Endorsement.TryAdd(policyId, endorsementType);
            endorsementCreateResult.status.Should().Be("success");
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_creating_two_endorsements_without_type_THEN_success()
        {
            string policyId = await Policy.Create();
            string? endorsementType = null;
            createdStatusResult endorsementCreateResult = await Policy.Endorsement.TryAdd(policyId, endorsementType);
            endorsementCreateResult.status.Should().Be("success");

            endorsementCreateResult = await Policy.Endorsement.TryAdd(policyId, endorsementType);
            endorsementCreateResult.status.Should().Be("success");
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_endorse_product_THEN_policy_had_no_product_before_now()
        {
            string policyId = await Policy.Create();
            productId productId = await Product.Create();

            DateTime createdAt = DateTime.Now;

            await Task.Delay(TimeSpan.FromSeconds(5));

            string endorsementId = await Policy.Endorsement.Add(policyId);
            await Policy.UpdateProductAsync(policyId, productId, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            policy policy = await FindById(policyId, createdAt);
            policy.product.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_endorse_product_future_dated_twice_THEN_policy_has_consecutive_products_at_each_point()
        {
            string policyId = await Policy.Create();
            productId productId1 = await Product.Create();
            productId productId2 = await Product.Create();

            string endorsementId1 = await Policy.Endorsement.Add(policyId, effectiveDate: DateTime.Now.AddDays(2));
            await Policy.UpdateProductAsync(policyId, productId1, endorsementId1);
            await Policy.Endorsement.Accept(policyId, endorsementId1);

            string endorsementId2 = await Policy.Endorsement.Add(policyId, effectiveDate: DateTime.Now.AddDays(4));
            await Policy.UpdateProductAsync(policyId, productId2, endorsementId2);
            await Policy.Endorsement.Accept(policyId, endorsementId2);

            policy policy = await FindById(policyId);
            policy.product.Should().BeNull();

            policy = await FindById(policyId, DateTime.Now.AddDays(3));
            policy.product!.productId.Should().Be(productId1);

            policy = await FindById(policyId, DateTime.Now.AddDays(5));
            policy.product!.productId.Should().Be(productId2);
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_endorse_product_future_dated_twice_in_wrong_order_THEN_policy_has_product_from_last_endorsement()
        {
            string policyId = await Policy.Create();
            productId productId1 = await Product.Create();
            productId productId2 = await Product.Create();

            string endorsementId1 = await Policy.Endorsement.Add(policyId, effectiveDate: DateTime.Now.AddDays(4));
            await Policy.UpdateProductAsync(policyId, productId1, endorsementId1);
            await Policy.Endorsement.Accept(policyId, endorsementId1);

            string endorsementId2 = await Policy.Endorsement.Add(policyId, effectiveDate: DateTime.Now.AddDays(2));
            await Policy.UpdateProductAsync(policyId, productId2, endorsementId2);
            await Policy.Endorsement.Accept(policyId, endorsementId2);

            policy policy = await FindById(policyId);
            policy.product.Should().BeNull();

            policy = await FindById(policyId, DateTime.Now.AddDays(3));
            policy.product!.productId.Should().Be(productId2);

            policy = await FindById(policyId, DateTime.Now.AddDays(5));
            policy.product!.productId.Should().Be(productId2);
        }

        [Fact]
        public async Task GIVEN_policy_with_endorsement_WHEN_update_endorsement_effective_date_to_future_date_THEN_endorsement_is_listed()
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId);

            DateTime effectiveDate = DateTime.Now.AddDays(1);

            updateEndorsementInput input = new()
            {
                isEffectiveDateChanged = true,
                effectiveDate = effectiveDate,
            };

            await Policy.Endorsement.Update(policyId, endorsementId, input);

            policy policy = await FindById(policyId);

            policy.Should().NotBeNull();
            policy.endorsements.Should().NotBeEmpty();
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task GIVEN_policy_with_approved_endorsement_WHEN_update_endorsement_reasonOfChange_THEN_endorsement_is_updated(bool endorsementIsApproved)
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId);
            if (endorsementIsApproved)
                await Policy.Endorsement.Accept(policyId, endorsementId);

            updateEndorsementInput input = new()
            {
                reasonOfChange = CreateNewGuid(),
                isReasonOfChangeChanged = true
            };

            await Policy.Endorsement.Update(policyId, endorsementId, input);

            policy policy = await FindById(policyId);

            policy.endorsements!.Single()!.reasonOfChange.Should().Be(input.reasonOfChange);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task GIVEN_policy_with_approved_endorsement_WHEN_update_endorsement_cancellationMotive_THEN_endorsement_is_updated(bool endorsementIsApproved)
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId);
            if (endorsementIsApproved)
                await Policy.Endorsement.Accept(policyId, endorsementId);

            updateEndorsementInput input = new()
            {
                cancellationMotive = CreateNewGuid(),
                isCancellationMotiveChanged = true
            };

            await Policy.Endorsement.Update(policyId, endorsementId, input);

            policy policy = await FindById(policyId);

            policy.endorsements!.Single()!.cancellationMotive.Should().Be(input.cancellationMotive);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task GIVEN_policy_with_endorsement_WHEN_update_endorsement_status_THEN_endorsement_is_updated(bool endorsementIsApproved)
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId);
            if (endorsementIsApproved)
                await Policy.Endorsement.Accept(policyId, endorsementId);

            updateEndorsementInput input = new()
            {
                status = CreateNewGuid(),
                isStatusChanged = true
            };

            await Policy.Endorsement.Update(policyId, endorsementId, input);

            policy policy = await FindById(policyId);

            policy.endorsements!.Single()!.status.Should().Be(input.status);
        }

        [Fact]
        public async Task GIVEN_policy_with_current_endorsement_WHEN_extend_policy_end_date_endorsement_is_approved_THEN_beforeEndorsement_and_afterEndorsement_of_the_endorsement_should_display_correctly()
        {
            DateTime now = DateTime.UtcNow;
            DateTime beforeEndorsementEndDate = now.AddMonths(1);
            DateTime afterEndorsementEndDate = now.AddMonths(2);
            initializePolicyInput initializePolicyInput = new()
            {
                endDate = beforeEndorsementEndDate
            };
            string policyId = await Policy.Create(initializePolicyInput);
            string endorsementId = await Policy.Endorsement.Add(policyId);

            updatePolicyInput updatePolicyInput = new()
            {
                endDate = afterEndorsementEndDate
            };
            await Policy.Update(policyId, updatePolicyInput, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            policy policy = await FindById(policyId);
            endorsement endorsement = policy.endorsements!.Single()!;
            endorsement.beforeEndorsement!.endDate.Should().BeEquivalentTo(beforeEndorsementEndDate.ToString("yyyy-MM-dd"));
            endorsement.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate.ToString("yyyy-MM-dd"));
        }

        [Fact]
        public async Task GIVEN_policy_with_future_dated_endorsement_WHEN_extend_policy_end_date_endorsement_is_approved_THEN_beforeEndorsement_and_afterEndorsement_of_the_endorsement_should_display_correctly()
        {
            DateTime now = Truncate(DateTime.UtcNow, TimeSpan.TicksPerMillisecond);
            DateTime beforeEndorsementEndDate = now.AddMonths(1);
            DateTime afterEndorsementEndDate = now.AddMonths(2);
            initializePolicyInput initializePolicyInput = new()
            {
                endDate = beforeEndorsementEndDate
            };
            string policyId = await Policy.Create(initializePolicyInput);

            string endorsementId = await Policy.Endorsement.Add(policyId);

            updatePolicyInput updatePolicyInput = new()
            {
                endDate = afterEndorsementEndDate
            };
            await Policy.Update(policyId, updatePolicyInput, endorsementId);

            DateTime futureDated = now.AddDays(1);
            updateEndorsementInput updateEndorsementInput = new()
            {
                effectiveDate = futureDated
            };

            await Policy.Endorsement.Update(policyId, endorsementId, updateEndorsementInput);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            policy policy = await FindById(policyId);
            endorsement endorsement = policy.endorsements!.Single()!;
            endorsement.beforeEndorsement!.endDate.Should().BeEquivalentTo(beforeEndorsementEndDate.ToString("yyyy-MM-dd"));
            endorsement.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate.ToString("yyyy-MM-dd"));
        }

        [Fact]
        public async Task GIVEN_policy_with_future_dated_endorsement_WHEN_extend_policy_end_date_endorsement_is_created_THEN_beforeEndorsement_and_afterEndorsement_of_the_endorsement_should_display_correctly()
        {
            DateTime now = Truncate(DateTime.UtcNow, TimeSpan.TicksPerMillisecond);
            DateTime beforeEndorsementEndDate = now.AddMonths(1);
            DateTime afterEndorsementEndDate = now.AddMonths(2);
            initializePolicyInput initializePolicyInput = new()
            {
                endDate = beforeEndorsementEndDate
            };
            string policyId = await Policy.Create(initializePolicyInput);

            string endorsementId = await Policy.Endorsement.Add(policyId);

            updatePolicyInput updatePolicyInput = new()
            {
                endDate = afterEndorsementEndDate
            };
            await Policy.Update(policyId, updatePolicyInput, endorsementId);

            DateTime futureDated = now.AddDays(1);
            updateEndorsementInput updateEndorsementInput = new()
            {
                effectiveDate = futureDated
            };

            await Policy.Endorsement.Update(policyId, endorsementId, updateEndorsementInput);

            policy policy = await FindById(policyId);
            endorsement endorsement = policy.endorsements!.Single()!;
            endorsement.beforeEndorsement!.endDate.Should().BeEquivalentTo(beforeEndorsementEndDate.ToString("yyyy-MM-dd"));
            endorsement.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate.ToString("yyyy-MM-dd"));
        }

        [Fact]
        public async Task GIVEN_policy_with_two_future_dated_endorsements_WHEN_both_extend_policy_end_date_endorsements_are_approved_THEN_beforeEndorsement_and_afterEndorsement_of_the_endorsement_should_display_correctly()
        {
            DateTime now = Truncate(DateTime.UtcNow, TimeSpan.TicksPerMillisecond);
            DateTime beforeEndorsementEndDate = now.AddMonths(1);

            initializePolicyInput initializePolicyInput = new()
            {
                endDate = beforeEndorsementEndDate
            };
            string policyId = await Policy.Create(initializePolicyInput);

            string endorsementId1 = await Policy.Endorsement.Add(policyId);

            DateTime afterEndorsementEndDate1 = now.AddMonths(2);
            updatePolicyInput updatePolicyInput1 = new()
            {
                endDate = afterEndorsementEndDate1
            };
            await Policy.Update(policyId, updatePolicyInput1, endorsementId1);

            DateTime futureDated1 = now.AddDays(1);
            updateEndorsementInput updateEndorsementInput1 = new()
            {
                effectiveDate = futureDated1
            };

            await Policy.Endorsement.Update(policyId, endorsementId1, updateEndorsementInput1);
            await Policy.Endorsement.Accept(policyId, endorsementId1);


            string endorsementId2 = await Policy.Endorsement.Add(policyId);

            DateTime afterEndorsementEndDate2 = now.AddMonths(3);
            updatePolicyInput updatePolicyInput2 = new()
            {
                endDate = afterEndorsementEndDate2
            };
            await Policy.Update(policyId, updatePolicyInput2, endorsementId2);

            DateTime futureDated2 = now.AddDays(2);
            updateEndorsementInput updateEndorsementInput = new()
            {
                effectiveDate = futureDated2
            };

            await Policy.Endorsement.Update(policyId, endorsementId2, updateEndorsementInput);
            await Policy.Endorsement.Accept(policyId, endorsementId2);

            policy policy = await FindById(policyId);

            endorsement endorsement1 = policy.endorsements!.First(x => x!.id == endorsementId1)!;
            endorsement endorsement2 = policy.endorsements!.First(x => x!.id == endorsementId2)!;
            endorsement1.beforeEndorsement!.endDate.Should().BeEquivalentTo(beforeEndorsementEndDate.ToString("yyyy-MM-dd"));
            endorsement1.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate1.ToString("yyyy-MM-dd"));
            endorsement2.beforeEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate1.ToString("yyyy-MM-dd"));
            endorsement2.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate2.ToString("yyyy-MM-dd"));
        }

        [Fact]
        public async Task GIVEN_policy_with_two_future_dated_endorsements_WHEN_the_first_endorsement_is_approved_and_second_endorsement_is_created_THEN_beforeEndorsement_and_afterEndorsement_of_the_endorsement_should_display_correctly()
        {
            DateTime now = Truncate(DateTime.UtcNow, TimeSpan.TicksPerMillisecond);
            DateTime beforeEndorsementEndDate = now.AddMonths(1);

            initializePolicyInput initializePolicyInput = new()
            {
                endDate = beforeEndorsementEndDate
            };
            string policyId = await Policy.Create(initializePolicyInput);

            string endorsementId1 = await Policy.Endorsement.Add(policyId);

            DateTime afterEndorsementEndDate1 = now.AddMonths(2);
            updatePolicyInput updatePolicyInput1 = new()
            {
                endDate = afterEndorsementEndDate1
            };
            await Policy.Update(policyId, updatePolicyInput1, endorsementId1);

            DateTime futureDated1 = now.AddDays(1);
            updateEndorsementInput updateEndorsementInput1 = new()
            {
                effectiveDate = futureDated1
            };

            await Policy.Endorsement.Update(policyId, endorsementId1, updateEndorsementInput1);
            await Policy.Endorsement.Accept(policyId, endorsementId1);


            string endorsementId2 = await Policy.Endorsement.Add(policyId);

            DateTime afterEndorsementEndDate2 = now.AddMonths(3);
            updatePolicyInput updatePolicyInput2 = new()
            {
                endDate = afterEndorsementEndDate2
            };
            await Policy.Update(policyId, updatePolicyInput2, endorsementId2);

            DateTime futureDated2 = now.AddDays(2);
            updateEndorsementInput updateEndorsementInput = new()
            {
                effectiveDate = futureDated2
            };

            await Policy.Endorsement.Update(policyId, endorsementId2, updateEndorsementInput);

            policy policy = await FindById(policyId);

            endorsement endorsement1 = policy.endorsements!.First(x => x!.id == endorsementId1)!;
            endorsement endorsement2 = policy.endorsements!.First(x => x!.id == endorsementId2)!;
            endorsement1.beforeEndorsement!.endDate.Should().BeEquivalentTo(beforeEndorsementEndDate.ToString("yyyy-MM-dd"));
            endorsement1.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate1.ToString("yyyy-MM-dd"));
            endorsement2.beforeEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate1.ToString("yyyy-MM-dd"));
            endorsement2.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate2.ToString("yyyy-MM-dd"));
        }


        [Fact]
        public async Task GIVEN_policy_with_two_future_dated_endorsements_WHEN_the_first_endorsement_is_created_and_second_endorsement_is_approved_THEN_beforeEndorsement_and_afterEndorsement_of_the_endorsement_should_display_correctly()
        {
            DateTime now = Truncate(DateTime.UtcNow, TimeSpan.TicksPerMillisecond);
            DateTime beforeEndorsementEndDate = now.AddMonths(1);

            initializePolicyInput initializePolicyInput = new()
            {
                endDate = beforeEndorsementEndDate
            };
            string policyId = await Policy.Create(initializePolicyInput);

            string endorsementId1 = await Policy.Endorsement.Add(policyId);

            DateTime afterEndorsementEndDate1 = now.AddMonths(2);
            updatePolicyInput updatePolicyInput1 = new()
            {
                endDate = afterEndorsementEndDate1
            };
            await Policy.Update(policyId, updatePolicyInput1, endorsementId1);

            DateTime futureDated1 = now.AddDays(1);
            updateEndorsementInput updateEndorsementInput1 = new()
            {
                effectiveDate = futureDated1
            };

            await Policy.Endorsement.Update(policyId, endorsementId1, updateEndorsementInput1);


            string endorsementId2 = await Policy.Endorsement.Add(policyId);

            DateTime afterEndorsementEndDate2 = now.AddMonths(3);
            updatePolicyInput updatePolicyInput2 = new()
            {
                endDate = afterEndorsementEndDate2
            };
            await Policy.Update(policyId, updatePolicyInput2, endorsementId2);

            DateTime futureDated2 = now.AddDays(2);
            updateEndorsementInput updateEndorsementInput = new()
            {
                effectiveDate = futureDated2
            };

            await Policy.Endorsement.Update(policyId, endorsementId2, updateEndorsementInput);
            await Policy.Endorsement.Accept(policyId, endorsementId2);

            policy policy = await FindById(policyId);

            endorsement endorsement1 = policy.endorsements!.First(x => x!.id == endorsementId1)!;
            endorsement endorsement2 = policy.endorsements!.First(x => x!.id == endorsementId2)!;
            endorsement1.beforeEndorsement!.endDate.Should().BeEquivalentTo(beforeEndorsementEndDate.ToString("yyyy-MM-dd"));
            endorsement1.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate1.ToString("yyyy-MM-dd"));
            endorsement2.beforeEndorsement!.endDate.Should().BeEquivalentTo(beforeEndorsementEndDate.ToString("yyyy-MM-dd"));
            endorsement2.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate2.ToString("yyyy-MM-dd"));
        }

        [Fact]
        public async Task GIVEN_policy_with_back_dated_endorsement_WHEN_extend_policy_end_date_endorsement_is_approved_THEN_beforeEndorsement_and_afterEndorsement_of_the_endorsement_should_display_correctly()
        {
            DateTime now = Truncate(DateTime.UtcNow, TimeSpan.TicksPerMillisecond);
            DateTime beforeEndorsementEndDate = now.AddMonths(1);
            DateTime afterEndorsementEndDate = now.AddMonths(2);
            initializePolicyInput initializePolicyInput = new()
            {
                endDate = beforeEndorsementEndDate
            };
            string policyId = await Policy.Create(initializePolicyInput);

            await Task.Delay(TimeSpan.FromSeconds(5));

            string endorsementId = await Policy.Endorsement.Add(policyId);

            updatePolicyInput updatePolicyInput = new()
            {
                endDate = afterEndorsementEndDate
            };
            await Policy.Update(policyId, updatePolicyInput, endorsementId);

            DateTime backDated = now.AddSeconds(2);
            updateEndorsementInput updateEndorsementInput = new()
            {
                effectiveDate = backDated
            };

            await Policy.Endorsement.Update(policyId, endorsementId, updateEndorsementInput);

            await Policy.Endorsement.Accept(policyId, endorsementId);

            policy policy = await FindById(policyId);
            endorsement endorsement = policy.endorsements!.Single()!;
            endorsement.beforeEndorsement!.endDate.Should().BeEquivalentTo(beforeEndorsementEndDate.ToString("yyyy-MM-dd"));
            endorsement.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate.ToString("yyyy-MM-dd"));
        }

        [Fact(Skip = "Obsolete")]
        public async Task GIVEN_policy_with_two_back_dated_endorsements_WHEN_both_extend_policy_end_date_endorsement_are_approved_THEN_beforeEndorsement_and_afterEndorsement_of_the_endorsement_should_display_correctly()
        {
            DateTime now = Truncate(DateTime.UtcNow, TimeSpan.TicksPerMillisecond);
            DateTime beforeEndorsementEndDate = now.AddMonths(1);
            initializePolicyInput initializePolicyInput = new()
            {
                endDate = beforeEndorsementEndDate
            };
            string policyId = await Policy.Create(initializePolicyInput);

            await Task.Delay(TimeSpan.FromSeconds(6));

            string endorsementId1 = await Policy.Endorsement.Add(policyId);
            DateTime afterEndorsementEndDate1 = now.AddMonths(2);
            updatePolicyInput updatePolicyInput1 = new()
            {
                endDate = afterEndorsementEndDate1
            };
            await Policy.Update(policyId, updatePolicyInput1, endorsementId1);

            DateTime backDated1 = now.AddSeconds(2);
            updateEndorsementInput updateEndorsementInput1 = new()
            {
                effectiveDate = backDated1
            };

            await Policy.Endorsement.Update(policyId, endorsementId1, updateEndorsementInput1);
            await Policy.Endorsement.Accept(policyId, endorsementId1);


            string endorsementId2 = await Policy.Endorsement.Add(policyId);
            DateTime afterEndorsementEndDate2 = now.AddMonths(3);
            updatePolicyInput updatePolicyInput2 = new()
            {
                endDate = afterEndorsementEndDate2
            };
            await Policy.Update(policyId, updatePolicyInput2, endorsementId2);

            DateTime backDated2 = now.AddSeconds(4);
            updateEndorsementInput updateEndorsementInput2 = new()
            {
                effectiveDate = backDated2
            };

            await Policy.Endorsement.Update(policyId, endorsementId2, updateEndorsementInput2);
            await Policy.Endorsement.Accept(policyId, endorsementId2);

            policy policy = await FindById(policyId);
            endorsement endorsement1 = policy.endorsements!.First(x => x!.id == endorsementId1)!;
            endorsement endorsement2 = policy.endorsements!.First(x => x!.id == endorsementId2)!;
            endorsement1.beforeEndorsement!.endDate.Should().BeEquivalentTo(beforeEndorsementEndDate.ToString("yyyy-MM-dd"));
            endorsement1.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate1.ToString("yyyy-MM-dd"));
            endorsement2.beforeEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate1.ToString("yyyy-MM-dd"));
            endorsement2.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate2.ToString("yyyy-MM-dd"));
        }

        [Fact]
        public async Task GIVEN_policy_with_two_back_dated_endorsements_WHEN_the_first_endorsement_is_created_and_second_endorsement_is_approved_THEN_beforeEndorsement_and_afterEndorsement_of_the_endorsement_should_display_correctly()
        {
            DateTime now = Truncate(DateTime.UtcNow, TimeSpan.TicksPerMillisecond);
            DateTime beforeEndorsementEndDate = now.AddMonths(1);
            initializePolicyInput initializePolicyInput = new()
            {
                endDate = beforeEndorsementEndDate
            };
            string policyId = await Policy.Create(initializePolicyInput);

            await Task.Delay(TimeSpan.FromSeconds(6));

            string endorsementId1 = await Policy.Endorsement.Add(policyId);
            DateTime afterEndorsementEndDate1 = now.AddMonths(2);
            updatePolicyInput updatePolicyInput1 = new()
            {
                endDate = afterEndorsementEndDate1
            };
            await Policy.Update(policyId, updatePolicyInput1, endorsementId1);

            DateTime backDated1 = now.AddSeconds(2);
            updateEndorsementInput updateEndorsementInput1 = new()
            {
                effectiveDate = backDated1
            };

            await Policy.Endorsement.Update(policyId, endorsementId1, updateEndorsementInput1);

            string endorsementId2 = await Policy.Endorsement.Add(policyId);
            DateTime afterEndorsementEndDate2 = now.AddMonths(3);
            updatePolicyInput updatePolicyInput2 = new()
            {
                endDate = afterEndorsementEndDate2
            };
            await Policy.Update(policyId, updatePolicyInput2, endorsementId2);

            DateTime backDated2 = now.AddSeconds(4);
            updateEndorsementInput updateEndorsementInput2 = new()
            {
                effectiveDate = backDated2
            };

            await Policy.Endorsement.Update(policyId, endorsementId2, updateEndorsementInput2);
            await Policy.Endorsement.Accept(policyId, endorsementId2);

            policy policy = await FindById(policyId);
            endorsement endorsement1 = policy.endorsements!.First(x => x!.id == endorsementId1)!;
            endorsement endorsement2 = policy.endorsements!.First(x => x!.id == endorsementId2)!;
            endorsement1.beforeEndorsement!.endDate.Should().BeEquivalentTo(beforeEndorsementEndDate.ToString("yyyy-MM-dd"));
            endorsement1.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate1.ToString("yyyy-MM-dd"));
            endorsement2.beforeEndorsement!.endDate.Should().BeEquivalentTo(beforeEndorsementEndDate.ToString("yyyy-MM-dd"));
            endorsement2.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate2.ToString("yyyy-MM-dd"));
        }

        [Fact]
        public async Task GIVEN_policy_with_two_back_dated_endorsements_WHEN_the_first_endorsement_is_approved_and_second_endorsement_is_created_THEN_beforeEndorsement_and_afterEndorsement_of_the_endorsement_should_display_correctly()
        {
            DateTime now = Truncate(DateTime.UtcNow, TimeSpan.TicksPerMillisecond);
            DateTime beforeEndorsementEndDate = now.AddMonths(1);
            initializePolicyInput initializePolicyInput = new()
            {
                endDate = beforeEndorsementEndDate
            };
            string policyId = await Policy.Create(initializePolicyInput);

            await Task.Delay(TimeSpan.FromSeconds(6));

            string endorsementId1 = await Policy.Endorsement.Add(policyId);
            DateTime afterEndorsementEndDate1 = now.AddMonths(2);
            updatePolicyInput updatePolicyInput1 = new()
            {
                endDate = afterEndorsementEndDate1
            };
            await Policy.Update(policyId, updatePolicyInput1, endorsementId1);

            DateTime backDated1 = now.AddSeconds(2);
            updateEndorsementInput updateEndorsementInput1 = new()
            {
                effectiveDate = backDated1
            };

            await Policy.Endorsement.Update(policyId, endorsementId1, updateEndorsementInput1);
            await Policy.Endorsement.Accept(policyId, endorsementId1);


            string endorsementId2 = await Policy.Endorsement.Add(policyId);
            DateTime afterEndorsementEndDate2 = now.AddMonths(3);
            updatePolicyInput updatePolicyInput2 = new()
            {
                endDate = afterEndorsementEndDate2
            };
            await Policy.Update(policyId, updatePolicyInput2, endorsementId2);

            DateTime backDated2 = now.AddSeconds(4);
            updateEndorsementInput updateEndorsementInput2 = new()
            {
                effectiveDate = backDated2
            };

            await Policy.Endorsement.Update(policyId, endorsementId2, updateEndorsementInput2);

            policy policy = await FindById(policyId);
            endorsement endorsement1 = policy.endorsements!.First(x => x!.id == endorsementId1)!;
            endorsement endorsement2 = policy.endorsements!.First(x => x!.id == endorsementId2)!;
            endorsement1.beforeEndorsement!.endDate.Should().BeEquivalentTo(beforeEndorsementEndDate.ToString("yyyy-MM-dd"));
            endorsement1.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate1.ToString("yyyy-MM-dd"));
            endorsement2.beforeEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate1.ToString("yyyy-MM-dd"));
            endorsement2.afterEndorsement!.endDate.Should().BeEquivalentTo(afterEndorsementEndDate2.ToString("yyyy-MM-dd"));
        }

        [Fact(Skip = "Obsolete, to be removed")]
        public async Task GIVEN_policy_WHEN_endorse_product_back_dated_twice_THEN_policy_has_consecutive_products_at_each_point()
        {
            string policyId = await Policy.Create();
            productId productId1 = await Product.Create();
            productId productId2 = await Product.Create();

            DateTime createdAt = DateTime.Now;

            await Task.Delay(TimeSpan.FromSeconds(5));

            Console.WriteLine(DateTime.UtcNow);
            string endorsementId1 = await Policy.Endorsement.Add(policyId, effectiveDate: createdAt.AddSeconds(2));
            await Policy.UpdateProductAsync(policyId, productId1, endorsementId1);
            await Policy.Endorsement.Accept(policyId, endorsementId1);
            Console.WriteLine(DateTime.UtcNow);

            string endorsementId2 = await Policy.Endorsement.Add(policyId, effectiveDate: createdAt.AddSeconds(4));
            await Policy.UpdateProductAsync(policyId, productId2, endorsementId2);
            await Policy.Endorsement.Accept(policyId, endorsementId2);

            policy policy = await FindById(policyId, createdAt);
            policy.product.Should().BeNull();

            policy = await FindById(policyId, createdAt.AddSeconds(3));
            policy.product!.productId.Should().Be(productId1);

            policy = await FindById(policyId);
            policy.product!.productId.Should().Be(productId2);
        }

        [Fact(Skip = "Obsolete, to be removed")]
        public async Task GIVEN_policy_WHEN_endorse_product_back_dated_twice_in_wrong_order_THEN_policy_has_product_from_last_endorsement()
        {
            string policyId = await Policy.Create();
            productId productId1 = await Product.Create();
            productId productId2 = await Product.Create();

            DateTime createdAt = DateTime.Now;

            await Task.Delay(TimeSpan.FromSeconds(5));

            string endorsementId1 = await Policy.Endorsement.Add(policyId, effectiveDate: createdAt.AddSeconds(4));
            await Policy.UpdateProductAsync(policyId, productId1, endorsementId1);
            await Policy.Endorsement.Accept(policyId, endorsementId1);

            string endorsementId2 = await Policy.Endorsement.Add(policyId, effectiveDate: createdAt.AddSeconds(2));
            await Policy.UpdateProductAsync(policyId, productId2, endorsementId2);
            await Policy.Endorsement.Accept(policyId, endorsementId2);

            policy policy = await FindById(policyId, createdAt);
            policy.product.Should().BeNull();

            policy = await FindById(policyId, createdAt.AddSeconds(3));
            policy.product!.productId.Should().Be(productId2);

            policy = await FindById(policyId);
            policy.product!.productId.Should().Be(productId2);
        }

        [Fact]
        public async Task GIVEN_policy_with_endorsement_WHEN_update_endorsement_effective_date_to_past_date_THEN_successfully_updated()
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId);

            DateTime effectiveDate = DateTime.Now.AddDays(-1);

            updateEndorsementInput input = new()
            {
                isEffectiveDateChanged = true,
                effectiveDate = effectiveDate,
            };

            await Policy.Endorsement.Update(policyId, endorsementId, input);

            policy policy = await FindById(policyId);

            policy.Should().NotBeNull();

            endorsement? endorsement = policy.endorsements?.Single();

            endorsement!.effectiveDate.Should().BeCloseTo(effectiveDate.ToUniversalTime(), TimeSpan.FromMilliseconds(1));
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_endorse_product_back_dated_THEN_policy_has_this_product_now()
        {
            string policyId = await Policy.Create();
            productId productId = await Product.Create();

            string endorsementId = await Policy.Endorsement.Add(policyId, effectiveDate: DateTime.Now.AddDays(-1));
            await Policy.UpdateProductAsync(policyId, productId, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            policy policy = await FindById(policyId);
            policy.product!.productId.Should().Be(productId);
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_endorse_product_back_dated_THEN_policy_had_no_product_before_effective_date()
        {
            string policyId = await Policy.Create();
            productId productId = await Product.Create();

            DateTime createdAt = DateTime.Now;

            await Task.Delay(TimeSpan.FromSeconds(5));

            string endorsementId = await Policy.Endorsement.Add(policyId, effectiveDate: DateTime.Now.AddSeconds(-1));
            await Policy.UpdateProductAsync(policyId, productId, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            policy policy = await FindById(policyId, createdAt);
            policy.product.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_policy_with_rejected_endorsement_WHEN_trying_to_accept_the_endorsement_THEN_fails()
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId);

            await Policy.Endorsement.Reject(policyId, endorsementId);

            result result = await Policy.Endorsement.TryAccept(policyId, endorsementId);

            result!.status.Should().Be("failure");
            result!.errors!.Single().Should().Contain("Unable to accept rejected endorsement");
        }

        [Fact]
        public async Task GIVEN_policy_with_rejected_endorsement_WHEN_trying_to_set_the_endorsement_status_to_approved_THEN_fails()
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId);

            await Policy.Endorsement.Reject(policyId, endorsementId);

            result result = await Policy.Endorsement.TryUpdate(policyId, endorsementId, new updateEndorsementInput
            {
                status = "APPROVED"
            });

            result!.status.Should().Be("failure");
            result!.errors!.Single().Should().Contain("Unable to accept rejected endorsement");
        }

        [Fact]
        public async Task GIVEN_policy_with_endorsement_WHEN_filter_endorsements_by_type_THEN_endorsements_is_listed_correctly()
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId, "type-a");
            await Policy.Endorsement.Add(policyId, "type-b");


            policy policy = await FindById(policyId, null, new endorsementWhereInput { type = "type-a" });

            policy.Should().NotBeNull();
            policy.endorsements.Should().HaveCount(1);
            policy.endorsements.Should().Contain(x => x!.id == endorsementId);
        }

        [Fact]
        public async Task GIVEN_policy_with_endorsement_WHEN_filter_endorsements_by_type_in_THEN_endorsements_is_listed_correctly()
        {
            string policyId = await Policy.Create();
            await Policy.Endorsement.Add(policyId, "type-a");
            string endorsementId = await Policy.Endorsement.Add(policyId, "type-b");


            policy policy = await FindById(policyId, null, new endorsementWhereInput { type_in = new[] { "type-b" } });

            policy.Should().NotBeNull();
            policy.endorsements.Should().HaveCount(1);
            policy.endorsements.Should().Contain(x => x!.id == endorsementId);
        }

        [Fact]
        public async Task GIVEN_policy_with_endorsement_WHEN_filter_endorsements_by_source_exists_THEN_endorsements_is_listed_correctly()
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId, source: null);

            var policy = await Policy.FindById(
                policyId,
                new endorsementWhereInput
                {
                    source_exists = true,
                });

            policy.endorsements.Should().HaveCount(0);
        }

        [Fact]
        public async Task GIVEN_policy_add_new_endorsement_WHEN_set_new_UW_property_value_THEN_return_endorsements_with_correct_data()
        {
            string policyId = await Policy.Create();
            string endorsementType = "MEMBER_MOVEMENT";
            string endorsementId = await Policy.Endorsement.Add(policyId, type: endorsementType, memberMovementVersions: memberMovementVersions.MEMBER_MOVEMENT_V1, underwritingStatus: underwritingStatus.WAITING);
            policy policy = await FindById(policyId);
            policy.endorsements.Should().HaveCount(1);
        }

        [Fact]
        public async Task GIVEN_policy_add_new_endorsement_WHEN_update_new_UW_property_value_THEN_return_endorsements_with_updated_UW_data()
        {
            string policyId = await Policy.Create();
            string endorsementType = "MEMBER_MOVEMENT";
            string endorsementId = await Policy.Endorsement.Add(policyId, type: endorsementType, memberMovementVersions: memberMovementVersions.MEMBER_MOVEMENT_V1, underwritingStatus: underwritingStatus.WAITING);
            await Policy.Endorsement.Update(policyId, endorsementId, new updateEndorsementInput
            {
                isUnderwritingStatusChanged = true,
                underwritingStatus = underwritingStatus.PASSED
            });
            policy policy = await FindById(policyId);
            policy.endorsements.Should().HaveCount(1);
        }

        [Fact]
        public async Task GIVEN_policy_with_endorsement_WHEN_filter_endorsements_by_member_movement_version_in_THEN_endorsements_is_listed_correctly()
        {
            string policyId = await Policy.Create();
            string endorsementType = "MEMBER_MOVEMENT";
            await Policy.Endorsement.Add(policyId, type: endorsementType, memberMovementVersions: memberMovementVersions.MEMBER_MOVEMENT_V1 );
            string endorsementId = await Policy.Endorsement.Add(policyId,
                type: endorsementType,
                memberMovementVersions: memberMovementVersions.MEMBER_MOVEMENT_V2);


            policy policy = await FindById(policyId, null, new endorsementWhereInput
            {
                memberMovementVersion_in = new List<memberMovementVersions?>
                {
                    memberMovementVersions.MEMBER_MOVEMENT_V2
                }
            });

            policy.Should().NotBeNull();
            policy.endorsements.Should().HaveCount(1);
            policy.endorsements.Should().Contain(x => x!.id == endorsementId);
        }

        [Fact]
        public async Task GIVEN_policy_with_endorsement_WHEN_draft_THEN_status_is_draft()
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId, isDraft: true, source: endorsementSource.HR_PORTAL);

            var policy = await Policy.FindById(policyId);

            policy.endorsements.Single(it => it.id == endorsementId).status.Should().Be("DRAFT");
        }

        async Task<policy> FindById(string policyId, DateTime? asOf = null, endorsementWhereInput? endorsementWhereInput = null)
        {
            string query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: new policyWhereInput { id = policyId }, asOf: asOf), new policiesBuilder()
                    .list(new policyBuilder()
                          .id()
                          .product(new productBuilder()
                              .productId(new productIdBuilder()
                                  .WithAllFields()))
                          .endorsements(new policyBuilder.endorsementsArgs { where = (endorsementWhereInput ?? new endorsementWhereInput()) }, new endorsementBuilder()
                            .id()
                            .status()
                            .effectiveDate()
                            .reasonOfChange()
                            .cancellationMotive()
                            .beforeEndorsement(new policyBuilder()
                                .endDate())
                            .afterEndorsement(new policyBuilder()
                                .endDate()))))
                .Build();

            policies response = await _client.SendQueryAsync<policies>(query);
            return response.list!.Single()!;
        }

        DateTime Truncate(DateTime dateTime, long resolution)
        {
            if (dateTime == DateTime.MinValue || dateTime == DateTime.MaxValue) return dateTime;
            return dateTime.AddTicks(-(dateTime.Ticks % resolution));
        }
    }
}