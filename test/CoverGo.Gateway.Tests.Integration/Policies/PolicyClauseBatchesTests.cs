﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Policies
{
    public class PolicyClauseBatchesTests : TestsBase
    {
        public PolicyClauseBatchesTests(ITestOutputHelper output) : base(output)
        {
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task GIVEN_policy_WHEN_add_clausesBatch_THEN_added_and_succeed(bool withEndorsement)
        {
            string policyId = await CreatePolicy(withEndorsement);

            policyClauseBatchInput batch = new()
            {
                addClauseInputs = new List<addClauseToPolicyInput?>
                {
                    new()
                    {
                        order = 1,
                        htmlOverride = "addPolicyClauseBatchInput-1",
                        templateId = CreateNewGuid(),
                        type = "manual"
                    },
                    new()
                    {
                        order = 2,
                        htmlOverride = "addPolicyClauseBatchInput-2",
                        templateId = CreateNewGuid(),
                        type = "selected-from-library"
                    }
                }
            };

            await PolicyClauseBatch(policyId, batch, withEndorsement);

            policy policy = await FindById(policyId);

            policy.clauses.Should().HaveCount(2);
            policy.clauses.Should().Equal(batch.addClauseInputs, (c1, c2) => c1!.order == c2!.order);
            policy.clauses.Should().Equal(batch.addClauseInputs, (c1, c2) => c1!.htmlOverride == c2!.htmlOverride);
            policy.clauses.Should().Equal(batch.addClauseInputs, (c1, c2) => c1!.type == c2!.type);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task GIVEN_policy_with_clause_WHEN_update_clausesBatch_THEN_updated_and_succeed(bool withEndorsement)
        {
            (string policyId, string clauseId) = await CreatePolicyWithClause(withEndorsement);

            policyClauseBatchInput updateBatch = new()
            {
                updateClauseInputs = new List<updateClauseOnPolicyInput?>
                {
                    new()
                    {
                        clauseId = clauseId,
                        order = 2,
                        htmlOverride = "htmlOverride-updated",
                        isHtmlOverrideChanged = true,
                        isOrderChanged = true,
                    }
                }
            };
            await PolicyClauseBatch(policyId, updateBatch, withEndorsement);

            policy policy = await FindById(policyId);

            policy.clauses!.Single()!.order.Should().Be(updateBatch.updateClauseInputs.Single()!.order);
            policy.clauses!.Single()!.htmlOverride.Should().Be(updateBatch.updateClauseInputs.Single()!.htmlOverride);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task GIVEN_policy_with_clause_WHEN_remove_clausesBatch_THEN_removed_and_succeed(bool withEndorsement)
        {
            (string policyId, string clauseId) = await CreatePolicyWithClause(withEndorsement);

            policyClauseBatchInput removeBatch = new()
            {
                removeClauseInputs = new List<removeClauseFromPolicyInput?> { new() { clauseId = clauseId } }
            };
            await PolicyClauseBatch(policyId, removeBatch, withEndorsement);

            policy policy = await FindById(policyId);

            policy.clauses.Should().BeEmpty();
        }

        async Task<string> CreatePolicy(bool issue)
        {
            string policyId = await Policy.Create();
            if (issue)
                await Policy.Issue(policyId);

            return policyId;
        }

        async Task<(string policyId, string clauseId)> CreatePolicyWithClause(bool issue)
        {
            string policyId = await Policy.Create();

            policyClauseBatchInput addBatch = new()
            {
                addClauseInputs = new List<addClauseToPolicyInput?>
                {
                    new()
                    {
                        order = 1,
                        htmlOverride ="htmlOverride-added",
                    }
                }
            };
            await PolicyClauseBatch(policyId, addBatch, false);

            policy policy = await FindById(policyId);

            if (issue)
                await Policy.Issue(policyId);

            return (policyId, policy.clauses!.Single()!.id!);
        }

        async Task PolicyClauseBatch(string policyId, policyClauseBatchInput batch, bool withEndorsement)
        {
            string? endorsementId = withEndorsement ? await Policy.Endorsement.Add(policyId) : null;

            string mutation = new MutationBuilder()
                .policyClauseBatch(new MutationBuilder.policyClauseBatchArgs(policyId, batch, endorsementId), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);

            if (withEndorsement)
                await Policy.Endorsement.Accept(policyId, endorsementId!);
        }

        async Task<policy> FindById(string policyId)
        {
            string query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: new policyWhereInput { id = policyId }), new policiesBuilder()
                    .list(new policyBuilder()
                          .id()
                          .clauses(new policyBuilder.clausesArgs(), new clauseBuilder()
                              .id()
                              .order()
                              .type()
                              .htmlOverride()
                              .template(new templateInterfaceBuilder()
                                .id()))
                          ))
                .Build();

            policies response = await _client.SendQueryAsync<policies>(query);
            return response.list!.First()!;
        }
    }
}