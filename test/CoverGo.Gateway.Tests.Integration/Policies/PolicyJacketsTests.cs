using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

#pragma warning disable 8619

namespace CoverGo.Gateway.Tests.Integration.Policies
{
    public class PolicyJacketsTests : TestsBase
    {
        public PolicyJacketsTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_policy_and_two_jackets_created_WHEN_adding_jackets_instance_THEN_successfully_added()
        {
            string policyId = await Policy.Create();

            await SetUpJacketsAsync(policyId);

            policy policy = await QueryPolicyAsync(policyId);

            policy.jackets.Should().NotBeNull();
            policy.jackets!.Count.Should().Be(2);

        }

        [Fact]
        public async Task GIVEN_policy_and_two_jackets_created_WHEN_adding_jackets_instance_through_endorsement_THEN_successfully_added()
        {
            string policyId = await Policy.Create();

            string endorsementId = await Policy.Endorsement.Add(policyId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            await SetUpJacketsAsync(policyId, endorsementId);

            policy policy = await QueryPolicyAsync(policyId);

            policy.jackets.Should().NotBeNull();
            policy.jackets!.Count.Should().Be(2);

        }

        [Fact]
        public async Task GIVEN_policy_with_two_jackets_WHEN_updating_jacket_order_THEN_successfully_updated()
        {
            string policyId = await Policy.Create();

            await SetUpJacketsAsync(policyId);

            policy policy = await QueryPolicyAsync(policyId);
            jacketInstance? jacket = policy.jackets!.First();

            await UpdateJacketOrderAsync(policyId, jacket!.id, 10);

            policy = await QueryPolicyAsync(policyId);

            jacket = policy.jackets!.First(x => x!.id == jacket.id);
            jacket!.order.Should().Be(10);
        }

        [Fact]
        public async Task GIVEN_policy_with_two_jackets_WHEN_updating_jacket_order_through_endorsement_THEN_successfully_updated()
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            await SetUpJacketsAsync(policyId);

            policy policy = await QueryPolicyAsync(policyId);
            jacketInstance? jacket = policy.jackets!.First();

            await UpdateJacketOrderAsync(policyId, jacket!.id!, 10, endorsementId);

            policy = await QueryPolicyAsync(policyId);

            jacket = policy.jackets!.First(x => x!.id == jacket.id);
            jacket!.order.Should().Be(10);
        }

        [Fact]
        public async Task GIVEN_policy_with_two_jackets_WHEN_removing_jacket_THEN_successfully_removed()
        {
            string policyId = await Policy.Create();

            await SetUpJacketsAsync(policyId);

            policy policy = await QueryPolicyAsync(policyId);
            jacketInstance? jacket = policy.jackets!.First();

            await RemoveJacketAsync(policyId, jacket!.id!);

            policy = await QueryPolicyAsync(policyId);

            policy.jackets!.Count.Should().Be(1);
            policy.jackets.Any(x => x!.id == jacket.id).Should().Be(false);
        }

        [Fact]
        public async Task GIVEN_policy_with_two_jackets_WHEN_removing_jacket_through_endorsement_THEN_successfully_removed()
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId);
            await Policy.Endorsement.Accept(policyId, endorsementId);

            await SetUpJacketsAsync(policyId);

            policy policy = await QueryPolicyAsync(policyId);
            jacketInstance? jacket = policy.jackets.First();

            await RemoveJacketAsync(policyId, jacket!.id, endorsementId);

            policy = await QueryPolicyAsync(policyId);

            policy.jackets!.Count.Should().Be(1);
            policy.jackets.Any(x => x!.id == jacket.id).Should().Be(false);
        }

        private Task RemoveJacketAsync(string policyId, string jacketId, string? endorsementId = null)
        {
            string? mutation = new MutationBuilder()
                .removeJacketFromPolicy(new MutationBuilder.removeJacketFromPolicyArgs(policyId, jacketId),
                    new resultBuilder().WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        private Task UpdateJacketOrderAsync(string policyId, string jacketId, int order,
            string? endorsementId = null)
        {
            string? mutation = new MutationBuilder()
                .policyJacketBatch(
                    new MutationBuilder.policyJacketBatchArgs(policyId,
                        new policyJacketInstanceBatchInput
                        {
                            updateJacketInstanceInputs = new List<updatePolicyJacketInstanceInput?>
                            {
                                new() {instanceId = jacketId, order = order}
                            }
                        }, endorsementId),
                    new resultBuilder()
                        .status())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        private async Task SetUpJacketsAsync(string policyId, string? endorsementId = null)
        {
            string jacket1Id = await Jacket.Create(new createJacketInput { title = CreateNewGuid() });
            string jacket2Id = await Jacket.Create(new createJacketInput { title = CreateNewGuid() });

            string? mutation = new MutationBuilder().policyJacketBatch(new MutationBuilder.policyJacketBatchArgs(
                policyId,
                new policyJacketInstanceBatchInput
                {
                    addJacketInstanceInputs = new List<addPolicyJacketInstanceInput>
                    {
                        new() {jacketId = jacket1Id, order = 1}, new() {jacketId = jacket2Id, order = 2}
                    }
                },
                endorsementId
            ), new resultBuilder().WithAllFields()).Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        private async Task<policy> QueryPolicyAsync(string policyId)
        {
            string? query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: new policyWhereInput { id = policyId }), new policiesBuilder()
                    .list(new policyBuilder()
                        .jackets(new jacketInstanceBuilder()
                            .id()
                            .jacket(new jacketBuilder()
                                .id()
                                .status()
                                .title())
                            .order()
                            .lastModifiedAt()
                            .lastModifiedBy(new loginBuilder()
                                .username()))))
                .Build();


            policies policy = await _client.SendQueryAsync<policies>(query);

            return policy.list.Single();

        }
    }
}
