using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Policies
{
    public class PolicyImportTests : TestsBase
    {
        public PolicyImportTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_importPoliciesCommand_WHEN_import_THEN_policies_are_imported()
        {

            var importPoliciesCommand = new policies_ImportPoliciesCommandInput()
            {
                importPolicyCommands = new List<policies_ImportPolicyCommandInput?>()
                {
                    new()
                    {
                        policyId = Guid.NewGuid().ToString(),
                        createPolicyEventCommands = new List<policies_CreatePolicyEventCommandInput?>()
                        {
                            new()
                            {
                                eventType = policies_PolicyEventType.CREATION,
                                status = "CREATED",
                                fields = "{}",
                                timestamp = new DateTime(2021,01,01,01,00,00, DateTimeKind.Utc)
                            },
                            new()
                            {
                                eventType = policies_PolicyEventType.UPDATE_POLICY,
                                status = "APPROVED",
                                timestamp = new DateTime(2021,01,01,02,00,00, DateTimeKind.Utc)
                            }
                        }
                    },
                    new()
                    {
                        policyId = Guid.NewGuid().ToString(),
                        createPolicyEventCommands = new List<policies_CreatePolicyEventCommandInput?>()
                        {
                            new()
                            {
                                eventType = policies_PolicyEventType.CREATION,
                                status = "CREATED",
                                isRenewal = false,
                                fields = "{}",
                                timestamp = new DateTime(2021,01,01,01,00,00, DateTimeKind.Utc)
                            },
                            new()
                            {
                                eventType = policies_PolicyEventType.UPDATE_POLICY,
                                status = "APPROVED",
                                timestamp = new DateTime(2021,01,01,02,00,00, DateTimeKind.Utc)
                            },
                            new()
                            {
                                eventType = policies_PolicyEventType.UPDATE_POLICY,
                                status = "SETTLED",
                                timestamp = new DateTime(2021,01,01,03,00,00, DateTimeKind.Utc)
                            }
                        }
                    }
                }
            };

            await PolicyImport(importPoliciesCommand);

            policy policy1 = (await Policy.FindById(importPoliciesCommand.importPolicyCommands.First()?.policyId!))!;
            policy policy2 = (await Policy.FindById(importPoliciesCommand.importPolicyCommands.Last()?.policyId!))!;

            policy1.Should().NotBeNull();
            policy1.status.Should().Be("APPROVED");
            policy1.fields.Should().Be("{}");

            policy2.Should().NotBeNull();
            policy2.status.Should().Be("SETTLED");
            policy2.fields.Should().Be("{}");
        }

        private async Task PolicyImport(policies_ImportPoliciesCommandInput input)
        {
            string? mutation = new MutationBuilder()
                .policyMutationImport(new MutationBuilder.policyMutationImportArgs(input),
                    new policies_ResultBuilder().status())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }
    }
}