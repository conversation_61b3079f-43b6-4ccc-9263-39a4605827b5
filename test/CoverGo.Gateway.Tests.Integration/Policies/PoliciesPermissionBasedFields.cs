using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using FluentAssertions;
using GraphQL.Client.Http;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Policies
{
    public class PoliciesPermissionBasedFields : TestsBase
    {
        public PoliciesPermissionBasedFields(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_permissionSchema_set_WHEN_fetch_policy_THEN_returns_policy_with_allowed_field()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape()
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            const string expectedFields = @"{""field1"":""Value1""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);

            policy? policy = await new Policy(client).FindById(policyId);
            policy.Should().NotBeNull();

            policy!.fields!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_all_fields_allowed_set_WHEN_fetch_policy_THEN_returns_policy_with_allowed_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema").Escape()
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),

                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            const string expectedFields = @"{""field1"":""Value1"",""field2"":""Value2""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);

            policy? policy = await new Policy(client).FindById(policyId);
            policy.Should().NotBeNull();

            policy!.fields!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_set_WHEN_patch_policy_fields_THEN_updates_policy_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.WRITE,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape()
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),

                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            string fieldsPatch = @"
[
 {
  ""op"": ""replace"",
  ""path"": ""/field1"",
  ""value"": ""Value3""
 }
]".Escape();

            const string expectedFields = "{\"field1\":\"Value3\",\"field2\":\"Value2\"}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "writePolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            await new Policy(client).Update(policyId, new updatePolicyInput { fieldsPatch = fieldsPatch });

            policy? policy = await Policy.FindById(policyId);
            policy.Should().NotBeNull();

            policy!.fields!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_set_WHEN_patch_policy_fields_without_permission_THEN_fails()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.WRITE,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape()
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),

                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            string fieldsPatch = @"
[
 {
  ""op"": ""replace"",
  ""path"": ""/field2"",
  ""value"": ""Value3""
 }
]".Escape();

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "writePolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            Func<Task> method = () => new Policy(client).Update(policyId, new updatePolicyInput { fieldsPatch = fieldsPatch });

            await method.Should().ThrowAsync<Exception>();
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_set_WHEN_fetch_policy_with_condition_met_THEN_returns_policy_with_allowed_field()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.EQUALS,
                    path = "field2",
                    value = new scalarValueInput { stringValue = "Value2" }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            const string expectedFields = @"{""field1"":""Value1""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy? policy = await new Policy(client).FindById(policyId);

            policy!.fields!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_set_WHEN_fetch_policy_without_condition_met_THEN_returns_policy_without_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.EQUALS,
                    path = "field2",
                    value = new scalarValueInput { stringValue = "Value3" }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {

                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            const string? expectedFields = null;

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy policy = (await new Policy(client).FindById(policyId))!;

            policy.fields.Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_stringContains_set_WHEN_fetch_policy_with_condition_met_THEN_returns_policy_with_allowed_field()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.STRING_CONTAINS,
                    path = "field2",
                    value = new scalarValueInput { stringValue = "Value2" }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {

                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value3Value2Value4""
}".Escape()
            });

            const string expectedFields = @"{""field1"":""Value1""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy policy = (await new Policy(client).FindById(policyId))!;

            policy.fields!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_stringContains_set_WHEN_fetch_policy_without_condition_met_THEN_returns_policy_without_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.STRING_CONTAINS,
                    path = "field2",
                    value = new scalarValueInput { stringValue = "Value3" }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            const string? expectedFields = null;

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy policy = (await new Policy(client).FindById(policyId))!;

            policy.fields.Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_arrayContains_set_WHEN_fetch_policy_with_condition_met_THEN_returns_policy_with_allowed_field()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.ARRAY_CONTAINS,
                    path = "field2",
                    value = new scalarValueInput { stringValue = "Value2" }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": [ ""Value3"", ""Value2"", ""Value4"" ]
}".Escape()
            });

            const string expectedFields = @"{""field1"":""Value1""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy policy = (await new Policy(client).FindById(policyId))!;

            policy.fields!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_arrayContains_set_WHEN_fetch_policy_without_condition_met_THEN_returns_policy_without_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.ARRAY_CONTAINS,
                    path = "field2",
                    value = new scalarValueInput { stringValue = "Value3" }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": [ ""Value2"" ]
}".Escape()
            });

            const string? expectedFields = null;

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy policy = (await new Policy(client).FindById(policyId))!;

            policy.fields.Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_in_set_WHEN_fetch_policy_with_condition_met_THEN_returns_policy_with_allowed_field()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.IN,
                    path = "field2",
                    value = new scalarValueInput
                    {
                        arrayValue = new List<scalarValueInput?>
                        {
                            new () { stringValue = "Value2" },
                            new () { stringValue = "Value3" }
                        }
                    }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            const string expectedFields = @"{""field1"":""Value1""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy policy = (await new Policy(client).FindById(policyId))!;

            policy.fields!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_in_set_WHEN_fetch_policy_without_condition_met_THEN_returns_policy_without_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {

                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.IN,
                    path = "field2",
                    value = new scalarValueInput
                    {
                        arrayValue = new List<scalarValueInput?>
                        {
                            new () { stringValue = "Value2" },
                            new () { stringValue = "Value3" }
                        }
                    }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value4""
}".Escape()
            });

            const string? expectedFields = null;

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy policy = (await new Policy(client).FindById(policyId))!;

            policy.fields.Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_lessThan_set_WHEN_fetch_policy_with_condition_met_THEN_returns_policy_with_allowed_field()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.LESS_THAN,
                    path = "field2",
                    value = new scalarValueInput { numberValue = 5.0 }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": 4
}".Escape()
            });

            const string expectedFields = @"{""field1"":""Value1""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy policy = (await new Policy(client).FindById(policyId))!;

            policy.fields!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_lessThan_set_WHEN_fetch_policy_without_condition_met_THEN_returns_policy_without_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.LESS_THAN,
                    path = "field2",
                    value = new scalarValueInput { numberValue = 5 }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": 6
}".Escape()
            });

            const string? expectedFields = null;

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy policy = (await new Policy(client).FindById(policyId))!;

            policy.fields.Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_greaterThan_set_WHEN_fetch_policy_with_condition_met_THEN_returns_policy_with_allowed_field()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.GREATER_THAN,
                    path = "field2",
                    value = new scalarValueInput { numberValue = 5.0 }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": 6
}".Escape()
            });

            const string expectedFields = @"{""field1"":""Value1""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy policy = (await new Policy(client).FindById(policyId))!;

            policy.fields!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_greaterThan_set_WHEN_fetch_policy_without_condition_met_THEN_returns_policy_without_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.GREATER_THAN,
                    path = "field2",
                    value = new scalarValueInput { numberValue = 5 }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": 4
}".Escape()
            });

            const string? expectedFields = null;

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy policy = (await new Policy(client).FindById(policyId))!;

            policy.fields.Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_lessThan_dateTime_set_WHEN_fetch_policy_with_condition_met_THEN_returns_policy_with_allowed_field()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.LESS_THAN,
                    path = "field2",
                    value = new scalarValueInput { dateValue = new DateTime(1988, 4, 1) }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""1987-04-01T00:00:00.000Z""
}".Escape()
            });

            const string expectedFields = @"{""field1"":""Value1""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy policy = (await new Policy(client).FindById(policyId))!;

            policy.fields!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_lessThan_dateTime_set_WHEN_fetch_policy_without_condition_met_THEN_returns_policy_without_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.LESS_THAN,
                    path = "field2",
                    value = new scalarValueInput { dateValue = new DateTime(1988, 4, 1) }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""1989-04-01T00:00:00.000Z""
}".Escape()
            });

            const string? expectedFields = null;

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy policy = (await new Policy(client).FindById(policyId))!;

            policy.fields.Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_greaterThan_dateTime_set_WHEN_fetch_policy_with_condition_met_THEN_returns_policy_with_allowed_field()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.GREATER_THAN,
                    path = "field2",
                    value = new scalarValueInput { dateValue = new DateTime(1988, 4, 1) }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""1989-04-01T00:00:00.000Z""
}".Escape()
            });

            const string expectedFields = @"{""field1"":""Value1""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy policy = (await new Policy(client).FindById(policyId))!;

            policy.fields!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_greaterThan_dateTime_set_WHEN_fetch_policy_without_condition_met_THEN_returns_policy_without_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.GREATER_THAN,
                    path = "field2",
                    value = new scalarValueInput { dateValue = new DateTime(1988, 4, 1) }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""1987-04-01T00:00:00.000Z""
}".Escape()
            });

            const string? expectedFields = null;

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            policy policy = (await new Policy(client).FindById(policyId))!;

            policy.fields.Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_set_WHEN_patch_policy_fields_with_condition_met_THEN_updates_policy_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.WRITE,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.EQUALS,
                    path = "field2",
                    value = new scalarValueInput { stringValue = "Value2" }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            string fieldsPatch = @"
[
 {
  ""op"": ""replace"",
  ""path"": ""/field1"",
  ""value"": ""Value3""
 }
]".Escape();

            const string expectedFields = @"{""field1"":""Value3"",""field2"":""Value2""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "writePolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            await new Policy(client).Update(policyId, new updatePolicyInput { fieldsPatch = fieldsPatch });

            policy? policy = await Policy.FindById(policyId);

            policy!.fields!.RemoveLineBreaks().RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_set_WHEN_patch_policy_fields_without_condition_met_THEN_fails()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.WRITE,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.EQUALS,
                    path = "field2",
                    value = new scalarValueInput { stringValue = "Value4" }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            string fieldsPatch = @"
[
 {
  ""op"": ""replace"",
  ""path"": ""/field1"",
  ""value"": ""Value3""
 }
]".Escape();

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "writePolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            Func<Task> method = () => new Policy(client).Update(policyId, new updatePolicyInput { fieldsPatch = fieldsPatch });

            await method.Should().ThrowAsync<Exception>();
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_updateCondition_set_WHEN_patch_policy_fields_with_condition_met_THEN_updates_policy_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.WRITE,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                updateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.EQUALS,
                    path = "field1",
                    value = new scalarValueInput { stringValue = "Value3" }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            string fieldsPatch = @"
[
 {
  ""op"": ""replace"",
  ""path"": ""/field1"",
  ""value"": ""Value3""
 }
]".Escape();

            const string expectedFields = @"{""field1"":""Value3"",""field2"":""Value2""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "writePolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            await new Policy(client).Update(policyId, new updatePolicyInput { fieldsPatch = fieldsPatch });

            policy? policy = await Policy.FindById(policyId);

            policy!.fields!.RemoveLineBreaks().RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_updateCondition_containing_and_clause_set_WHEN_patch_policy_fields_with_condition_met_THEN_updates_policy_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.WRITE,
                schema = GetResource("PermissionsJsonSchema").Escape(),
                updateCondition = new fieldsWhereInput
                {
                    and = new List<fieldsWhereInput?>
                    {
                        new()
                        {
                            condition = fieldsWhereCondition.EQUALS,
                            path = "field1",
                            value = new scalarValueInput { stringValue = "Value3" }
                        },
                        new()
                        {
                            condition = fieldsWhereCondition.EQUALS,
                            path = "field2",
                            value = new scalarValueInput { stringValue = "Value4" }
                        },
                    }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            string fieldsPatch = @"
[
 {
  ""op"": ""replace"",
  ""path"": ""/field1"",
  ""value"": ""Value3""
 },
 {
  ""op"": ""replace"",
  ""path"": ""/field2"",
  ""value"": ""Value4""
 },

]".Escape();

            const string expectedFields = @"{""field1"":""Value3"",""field2"":""Value4""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "writePolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            await new Policy(client).Update(policyId, new updatePolicyInput { fieldsPatch = fieldsPatch });

            policy? policy = await Policy.FindById(policyId);

            policy!.fields!.RemoveLineBreaks().RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_containing_or_clause_set_WHEN_patch_policy_fields_with_condition_met_THEN_updates_policy_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.WRITE,
                schema = GetResource("PermissionsJsonSchema").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    or = new List<fieldsWhereInput?>
                    {
                        new()
                        {
                            condition = fieldsWhereCondition.EQUALS,
                            path = "field1",
                            value = new scalarValueInput { stringValue = "Value1" }
                        },
                        new()
                        {
                            condition = fieldsWhereCondition.EQUALS,
                            path = "field2",
                            value = new scalarValueInput { stringValue = "Value5" }
                        },
                    }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            string fieldsPatch = @"
[
 {
  ""op"": ""replace"",
  ""path"": ""/field1"",
  ""value"": ""Value3""
 },
 {
  ""op"": ""replace"",
  ""path"": ""/field2"",
  ""value"": ""Value4""
 },

]".Escape();

            const string expectedFields = @"{""field1"":""Value3"",""field2"":""Value4""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "writePolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            await new Policy(client).Update(policyId, new updatePolicyInput { fieldsPatch = fieldsPatch });

            policy? policy = await Policy.FindById(policyId);

            policy!.fields!.RemoveLineBreaks().RemoveWhitespaces().Should().Be(expectedFields);
        }


        [Fact]
        public async Task GIVEN_permissionSchema_with_stateCondition_containing_and_clause_set_WHEN_patch_policy_fields_with_condition_met_THEN_updates_policy_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.WRITE,
                schema = GetResource("PermissionsJsonSchema").Escape(),
                stateCondition = new fieldsWhereInput
                {
                    and = new List<fieldsWhereInput?>
                    {
                        new()
                        {
                            condition = fieldsWhereCondition.EQUALS,
                            path = "field1",
                            value = new scalarValueInput { stringValue = "Value1" }
                        },
                        new()
                        {
                            condition = fieldsWhereCondition.EQUALS,
                            path = "field2",
                            value = new scalarValueInput { stringValue = "Value2" }
                        },
                    }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            string fieldsPatch = @"
[
 {
  ""op"": ""replace"",
  ""path"": ""/field1"",
  ""value"": ""Value3""
 },
 {
  ""op"": ""replace"",
  ""path"": ""/field2"",
  ""value"": ""Value4""
 },

]".Escape();

            const string expectedFields = @"{""field1"":""Value3"",""field2"":""Value4""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "writePolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            await new Policy(client).Update(policyId, new updatePolicyInput { fieldsPatch = fieldsPatch });

            policy? policy = await Policy.FindById(policyId);

            policy!.fields!.RemoveLineBreaks().RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_updateCondition_set_WHEN_patch_policy_fields_without_condition_met_THEN_fails()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "policy",
                actionType = permissionSchemaActionType.WRITE,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape(),
                updateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.EQUALS,
                    path = "field1",
                    value = new scalarValueInput { stringValue = "Value1" }
                }
            });

            string policyId = await Policy.Create(new initializePolicyInput
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            string fieldsPatch = @"
[
 {
  ""op"": ""replace"",
  ""path"": ""/field1"",
  ""value"": ""Value3""
 }
]".Escape();

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "writePolicies", policyId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { policyId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            Func<Task> method = () => new Policy(client).Update(policyId, new updatePolicyInput { fieldsPatch = fieldsPatch });

            await method.Should().ThrowAsync<Exception>();
        }
    }
}
