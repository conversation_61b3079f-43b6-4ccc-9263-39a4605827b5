using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Policies
{
    public class PoliciesMembersTests : TestsBase
    {
        [Fact]
        public async Task GIVEN_policy_WHEN_create_member_THEN_member_appears()
        {
            string policyId = await Policy.Create();
            var member = new policyMemberInput { memberId = CreateNewGuid() };

            await MembersBatch(policyId,
                new policyMembersBatchInput { create = new List<policyMemberInput?> { member } });

            policy policy = await FindById(policyId);
            policy!.members!.totalCount.Should().Be(1);
            policy!.members!.list!.Single()!.memberId.Should().Be(member.memberId);
            policy.membersMovements!.totalCount.Should().Be(1);
            policy!.membersMovements!.list!.Single()!.memberId.Should().Be(member.memberId);
        }

        [Fact]
        public async Task GIVEN_policy_members_with_effective_dates_WHEN_querying_activity_THEN_activity_is_correct()
        {
            string policyId = await Policy.Create();
            string memberId = CreateNewGuid();
            DateTime now = DateTime.UtcNow;
            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new List<policyMemberInput?>
                {
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-01-01"),
                        planId = "plan-1"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-01-01"),
                        endDate = DateTime.Parse("2021-03-01"),
                        planId = "plan-1"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-03-02"),
                        planId = "plan-2"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-03-02"),
                        endDate = DateTime.Parse("2021-05-01"),
                        planId = "plan-2"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-05-02"),
                        planId = "plan-3"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-05-02"),
                        endDate = DateTime.Parse("2021-03-31"),
                        planId = "plan-3"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-04-01"),
                        planId = "plan-4"
                    },
                }
            });

            var policy = await FindById(policyId);

            var activities = policy.membersActivity!.list!.OrderByDescending(a => a!.timestamp).ToArray();
            activities.Length.Should().Be(3);

            activities[0]!.startDate.Should().Be("2021-04-01");
            activities[0]!.endDate.Should().Be(null);
            activities[0]!.planId.Should().Be("plan-4");

            activities[1]!.startDate.Should().Be("2021-03-02");
            activities[1]!.endDate.Should().Be("2021-03-31");
            activities[1]!.planId.Should().Be("plan-2");

            activities[2]!.startDate.Should().Be("2021-01-01");
            activities[2]!.endDate.Should().Be("2021-03-01");
            activities[2]!.planId.Should().Be("plan-1");
        }

        [Fact]
        public async Task GIVEN_policy_members_with_effective_dates_WHEN_removing_member_THEN_activity_is_empty()
        {
            string policyId = await Policy.Create();
            string memberId = CreateNewGuid();
            DateTime now = DateTime.UtcNow;
            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new List<policyMemberInput?>
                {
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-01-01"),
                        planId = "plan-1"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-01-01"),
                        endDate = DateTime.Parse("2021-03-01"),
                        planId = "plan-1"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-03-02"),
                        planId = "plan-2"
                    },
                    new()
                    {
                         memberId = memberId,
                        startDate = DateTime.Parse("2021-03-02"),
                        endDate = DateTime.Parse("2021-05-01"),
                        planId = "plan-2"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-05-02"),
                        planId = "plan-3"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-05-02"),
                        endDate = DateTime.Parse("2021-03-31"),
                        planId = "plan-3"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-04-01"),
                        planId = "plan-4"
                    },
                }
            });

            await MembersBatch(policyId, new policyMembersBatchInput
            {
                delete = new List<policyMemberInput?>
                {
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-04-01"),
                        planId = "plan-4"
                    }
                }
            });

            var policy = await FindById(policyId);

            var activities = policy.membersActivity!.list!.ToArray();
            activities.Length.Should().Be(0);
        }

        [Fact]
        public async Task GIVEN_policy_members_with_effective_dates_WHEN_removing_member_and_reinstanting_him_THEN_activity_is_correct()
        {
            string policyId = await Policy.Create();
            string memberId = CreateNewGuid();
            DateTime now = DateTime.UtcNow;
            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new List<policyMemberInput?>
                {
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-01-01"),
                        planId = "plan-1"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-01-01"),
                        endDate = DateTime.Parse("2021-03-01"),
                        planId = "plan-1"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-03-02"),
                        planId = "plan-2"
                    },
                    new()
                    {
                         memberId = memberId,
                        startDate = DateTime.Parse("2021-03-02"),
                        endDate = DateTime.Parse("2021-05-01"),
                        planId = "plan-2"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-05-02"),
                        planId = "plan-3"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-05-02"),
                        endDate = DateTime.Parse("2021-03-31"),
                        planId = "plan-3"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-04-01"),
                        planId = "plan-4"
                    },
                }
            });

            await MembersBatch(policyId, new policyMembersBatchInput
            {
                delete = new List<policyMemberInput?>
                {
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-04-01"),
                        planId = "plan-4"
                    }
                }
            });

            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new List<policyMemberInput?>
                {
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-04-01"),
                        planId = "plan-4"
                    },
                }
            });

            var policy = await FindById(policyId);

            var activities = policy.membersActivity!.list!.OrderByDescending(i => i!.timestamp).ToArray();
            activities.Length.Should().Be(3);

            activities[0]!.startDate.Should().Be("2021-04-01");
            activities[0]!.endDate.Should().Be(null);
            activities[0]!.planId.Should().Be("plan-4");

            activities[1]!.startDate.Should().Be("2021-03-02");
            activities[1]!.endDate.Should().Be("2021-03-31");
            activities[1]!.planId.Should().Be("plan-2");

            activities[2]!.startDate.Should().Be("2021-01-01");
            activities[2]!.endDate.Should().Be("2021-03-01");
            activities[2]!.planId.Should().Be("plan-1");
        }

        [Fact]
        public async Task GIVEN_policy_members_with_effective_dates_WHEN_removing_member_and_querying_its_state_before_the_removal_date_THEN_activity_is_correct()
        {
            string policyId = await Policy.Create();
            string memberId = CreateNewGuid();

            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new List<policyMemberInput?>
                {
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-01-01"),
                        planId = "plan-1"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-01-01"),
                        endDate = DateTime.Parse("2021-03-01"),
                        planId = "plan-1"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-03-02"),
                        planId = "plan-2"
                    },
                    new()
                    {
                         memberId = memberId,
                        startDate = DateTime.Parse("2021-03-02"),
                        endDate = DateTime.Parse("2021-05-01"),
                        planId = "plan-2"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-05-02"),
                        planId = "plan-3"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-05-02"),
                        endDate = DateTime.Parse("2021-03-31"),
                        planId = "plan-3"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-04-01"),
                        planId = "plan-4"
                    },
                }
            });

            DateTime now = DateTime.UtcNow;

            await Task.Delay(TimeSpan.FromSeconds(3));

            await MembersBatch(policyId, new policyMembersBatchInput
            {
                delete = new List<policyMemberInput?>
                {
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-04-01"),
                        planId = "plan-4"
                    }
                }
            });

            var policy = await FindById(policyId, now);

            var activities = policy.membersActivity!.list!.OrderByDescending(i => i!.timestamp).ToArray();
            activities.Length.Should().Be(3);

            activities[0]!.startDate.Should().Be("2021-04-01");
            activities[0]!.endDate.Should().Be(null);
            activities[0]!.planId.Should().Be("plan-4");

            activities[1]!.startDate.Should().Be("2021-03-02");
            activities[1]!.endDate.Should().Be("2021-03-31");
            activities[1]!.planId.Should().Be("plan-2");

            activities[2]!.startDate.Should().Be("2021-01-01");
            activities[2]!.endDate.Should().Be("2021-03-01");
            activities[2]!.planId.Should().Be("plan-1");
        }

        [Fact]
        public async Task GIVEN_policy_members_with_effective_dates_WHEN_terminating_member_THEN_activity_is_correct()
        {
            string policyId = await Policy.Create();
            string memberId = CreateNewGuid();

            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new List<policyMemberInput?>
                {
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-01-01"),
                        planId = "plan-1"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-01-01"),
                        endDate = DateTime.Parse("2021-03-01"),
                        planId = "plan-1"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-03-02"),
                        planId = "plan-2"
                    },
                    new()
                    {
                         memberId = memberId,
                        startDate = DateTime.Parse("2021-03-02"),
                        endDate = DateTime.Parse("2021-05-01"),
                        planId = "plan-2"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-05-02"),
                        planId = "plan-3"
                    },
                    new()
                    {
                        memberId = memberId,
                        startDate = DateTime.Parse("2021-05-02"),
                        endDate = DateTime.Parse("2021-02-15"),
                        planId = "plan-3"
                    }
                }
            });

            var policy = await FindById(policyId);

            var activities = policy.membersActivity!.list!.ToArray();

            activities.Length.Should().Be(1);
            activities[0]!.startDate.Should().Be("2021-01-01");
            activities[0]!.endDate.Should().Be("2021-02-15");
            activities[0]!.planId.Should().Be("plan-1");

        }

        [Fact]
        public async Task GIVEN_policy_with_two_members_added_WHEN_filtering_by_member_fields_THEN_success()
        {
            string policyId = await Policy.Create();
            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new List<policyMemberInput?>
                {
                    new()
                    {
                        memberId = "member-1",
                        fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
                    },
                    new()
                    {
                        memberId = "member-2",
                        fields = @"
{
    ""field1"": ""Value3"",
    ""field2"": ""Value4""
}".Escape()
                    },
                }
            });

            policy policy = await FindById(policyId,
                where: new policyMembersFilterAggregateInput
                {
                    and = new List<policyMembersFilterAggregateInput?>
                    {
                        new()
                        {
                            where = new policyMembersFilterInput
                            {
                                fields = new fieldsWhereInput
                                {
                                    path = "fields.field1",
                                    condition = fieldsWhereCondition.EQUALS,
                                    value = new scalarValueInput { stringValue = "Value3" }
                                }
                            }
                        },
                        new()
                        {
                            where = new policyMembersFilterInput
                            {
                                fields = new fieldsWhereInput
                                {
                                    path = "fields.field2",
                                    condition = fieldsWhereCondition.EQUALS,
                                    value = new scalarValueInput { stringValue = "Value4" }
                                }
                            }
                        }
                    }
                });

            policy.members!.totalCount.Should().Be(1);

            var members = policy.members.list!.ToList();

            members.Count.Should().Be(1);
            members[0]!.memberId.Should().Be("member-2");
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_update_member_THEN_member_updated()
        {
            string policyId = await Policy.Create();
            var member = new policyMemberInput { memberId = CreateNewGuid() };

            await MembersBatch(policyId,
                new policyMembersBatchInput { create = new List<policyMemberInput?> { member } });

            var updateMember = new policyMemberInput
            {
                memberId = member.memberId,
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            };

            await MembersBatch(policyId,
                new policyMembersBatchInput { update = new List<policyMemberInput?> { updateMember } });

            policy policy = await FindById(policyId);
            policy!.members!.totalCount.Should().Be(1);
            policyMember policyMember = policy!.members!.list!.Single()!;

            policyMember.memberId.Should().Be(member.memberId);
            policyMember.fields.Should().Contain("field1");

            policy.membersMovements!.totalCount.Should().Be(1);

            policy.membersMovements!.list!.ToList()[0]!.fields.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_delete_member_THEN_member_is_marked_as_removed()
        {
            string policyId = await Policy.Create();
            var member = new policyMemberInput { memberId = CreateNewGuid() };

            await MembersBatch(policyId,
                new policyMembersBatchInput { create = new List<policyMemberInput?> { member } });

            var removeMember = new policyMemberInput { memberId = member.memberId };

            await MembersBatch(policyId,
                new policyMembersBatchInput { delete = new List<policyMemberInput?> { removeMember } });

            policy policy = await FindById(policyId);
            policy!.members!.totalCount.Should().Be(1);
            policyMember policyMember = policy!.members!.list!.Single()!;

            policyMember.memberId.Should().Be(member.memberId);
            policyMember.isRemoved.Should().BeTrue();

            policy.membersMovements!.totalCount.Should().Be(1);
            var memberMovementLog = policy!.membersMovements!.list!.OrderByDescending(i => i!.timestamp).ToList();

            memberMovementLog![0]!.isRemoved.Should().BeTrue();
        }

        [Fact]
        public async Task GIVEN_policy_with_non_approved_endorsement_WHEN_add_member_THEN_members_not_appears()
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId);

            var member = new policyMemberInput { memberId = CreateNewGuid() };

            await MembersBatch(policyId,
                new policyMembersBatchInput { create = new List<policyMemberInput?> { member } }, endorsementId);

            policy policy = await FindById(policyId);
            policy!.members!.totalCount.Should().Be(0);
            policy!.members!.list!.Should().BeEmpty();
            policy.membersMovements!.totalCount.Should().Be(0);
            policy!.membersMovements!.list!.Should().BeEmpty();
        }

        [Fact]
        public async Task GIVEN_policy_with_approved_endorsement_WHEN_batch_members_THEN_members_appears()
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId);

            var member = new policyMemberInput { memberId = CreateNewGuid() };

            await MembersBatch(policyId,
                new policyMembersBatchInput { create = new List<policyMemberInput?> { member } }, endorsementId);

            policy policy = await FindById(policyId);
            policy!.members!.totalCount.Should().Be(0);
            policy!.members!.list!.Should().BeEmpty();
            policy.membersMovements!.totalCount.Should().Be(0);
            policy!.membersMovements!.list!.Should().BeEmpty();

            await Policy.Endorsement.Accept(policyId, endorsementId);

            policy = await FindById(policyId);
            policy!.members!.totalCount.Should().Be(1);
            policy!.members!.list!.Single()!.memberId.Should().Be(member.memberId);
            policy.membersMovements!.totalCount.Should().Be(1);
            policy!.membersMovements!.list!.Single()!.memberId.Should().Be(member.memberId);
        }

        [Fact]
        public async Task
            GIVEN_policy_with_one_member_WHEN_update_member_through_endorsement_THEN_update_applied_only_when_endorsement_accepted()
        {
            string policyId = await Policy.Create();
            var member = new policyMemberInput { memberId = CreateNewGuid() };

            await MembersBatch(policyId,
                new policyMembersBatchInput { create = new List<policyMemberInput?> { member } });

            string endorsementId = await Policy.Endorsement.Add(policyId);

            var updateMember = new policyMemberInput
            {
                memberId = member.memberId,
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            };

            await MembersBatch(policyId,
                new policyMembersBatchInput { update = new List<policyMemberInput?> { updateMember } }, endorsementId);

            policy policy = await FindById(policyId);
            policy!.members!.totalCount.Should().Be(1);

            policyMember? policyMember = policy!.members!.list!.Single();

            policyMember!.memberId.Should().Be(member.memberId);
            policyMember.fields.Should().Be("{}");

            policy.membersMovements!.totalCount.Should().Be(1);
            policy!.membersMovements!.list!.Single()!.memberId.Should().Be(member.memberId);

            await Policy.Endorsement.Accept(policyId, endorsementId);

            policy = await FindById(policyId);
            policy!.members!.totalCount.Should().Be(1);
            policyMember = policy!.members!.list!.Single()!;

            policyMember.memberId.Should().Be(member.memberId);
            policyMember.fields.Should().Contain("field1");

            policy.membersMovements!.totalCount.Should().Be(2);
            var memberMovementLog = policy!.membersMovements!.list!.OrderByDescending(i => i!.timestamp).ToList();

            memberMovementLog![0]!.fields.Should().NotBeNullOrEmpty();
            memberMovementLog![1]!.fields.Should().Be("{}");
        }

        [Fact]
        public async Task
            GIVEN_policy_with_approved_future_dated_endorsement_WHEN_batch_members_THEN_members_appears_in_the_future()
        {
            DateTime endorsementDate = DateTime.UtcNow.AddDays(1);
            DateTime futureDate = endorsementDate.AddDays(1);

            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId, effectiveDate: endorsementDate);

            await Policy.Endorsement.Accept(policyId, endorsementId);

            var member = new policyMemberInput { memberId = CreateNewGuid() };

            await MembersBatch(policyId,
                new policyMembersBatchInput { create = new List<policyMemberInput?> { member } }, endorsementId);

            await Task.Delay(1);

            policy policy = await FindById(policyId);
            policy!.members!.totalCount.Should().Be(0);
            policy!.members!.list!.Should().BeEmpty();
            policy.membersMovements!.totalCount.Should().Be(0);
            policy!.membersMovements!.list!.Should().BeEmpty();

            policy = await FindById(policyId, futureDate);
            policy!.members!.totalCount.Should().Be(1);
            policy!.members!.list!.Single()!.memberId.Should().Be(member.memberId);
            policy.membersMovements!.totalCount.Should().Be(1);
            policy!.membersMovements!.list!.Single()!.memberId.Should().Be(member.memberId);
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_adding_updating_removing_1_000_members_THEN_success()
        {
            string policyId = await Policy.Create();

            const int membersCount = 1_000;
            const int fieldsCount = 50;

            List<policyMemberInput> members = new();

            for (int i = 0; i < membersCount; i++)
            {
                var fields = new Dictionary<string, string>();
                for (int j = 0; j < fieldsCount; j++)
                {
                    fields.Add(CreateNewGuid(), CreateNewGuid());
                }
                members.Add(new policyMemberInput
                {
                    memberId = CreateNewGuid(),
                    fields = fields.ToEscapedJsonString(),
                    startDate = DateTime.UtcNow,
                    endDate = DateTime.UtcNow,
                    planId = CreateNewGuid(),
                });
            }

            await MembersBatch(policyId, new policyMembersBatchInput { create = members! });
            await MembersBatch(policyId, new policyMembersBatchInput { update = members! });
            await MembersBatch(policyId, new policyMembersBatchInput { delete = members! });
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_adding_member_with_no_member_id_THEN_member_id_generated_automatically()
        {
            string policyId = await Policy.Create();

            await MembersBatch(policyId,
                new policyMembersBatchInput { create = new List<policyMemberInput?> { new() } });

            policy policy = await FindById(policyId);

            policy.members!.totalCount.Should().Be(1);

            var membersList = policy.members.list!.ToList();

            membersList.Count.Should().Be(1);
            membersList[0]!.memberId.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task
            GIVEN_policy_with_member_added_through_not_accepted_endorsement_WHEN_previewing_endorsement_THEN_member_appears()
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId);

            await MembersBatch(policyId,
                new policyMembersBatchInput { create = new List<policyMemberInput?> { new() { startDate = DateTime.UtcNow } } },
                endorsementId);

            policy policy = await FindById(policyId);

            policy.members!.totalCount.Should().Be(0);
            policy.membersActivity!.totalCount.Should().Be(0);
            policy.membersMovements!.totalCount.Should().Be(0);

            string query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs { where = new policyWhereInput { id = policyId } },
                    new policiesBuilder()
                        .list(new policyBuilder()
                            .id()
                            .endorsements(new policyBuilder.endorsementsArgs { where = new endorsementWhereInput { id = endorsementId } },
                                new endorsementBuilder()
                                    .beforeEndorsement(PolicyWithMembersQueryBuild())
                                    .afterEndorsement(PolicyWithMembersQueryBuild())))).Build();

            policies policies = await _client.SendQueryAsync<policies>(query);

            endorsement endorsement = policies.list!.First()!.endorsements!.First()!;
            policy before = endorsement!.beforeEndorsement!;
            policy after = endorsement.afterEndorsement!;

            before.members!.totalCount.Should().Be(0);
            before.members.list!.Count.Should().Be(0);
            before.membersMovements!.totalCount.Should().Be(0);
            before.membersMovements.list!.Count.Should().Be(0);

            after.members!.totalCount.Should().Be(1);
            after.members.list!.Count.Should().Be(1);
            after.membersMovements!.totalCount.Should().Be(1);
            after.membersMovements.list!.Count.Should().Be(1);
        }

        [Fact]
        public async Task
            GIVEN_policy_with_two_members_one_with_startDate_set_to_null_WHEN_filtering_by_ActiveOnly_THEN_only_the_member_with_non_null_startDate_appears()
        {
            string policyId = await Policy.Create();
            DateTime now = DateTime.UtcNow;

            await MembersBatch(policyId,
                new policyMembersBatchInput { create = new List<policyMemberInput?> { new(), new() { startDate = now } } });

            policy policy = await FindById(policyId,
                where: new policyMembersFilterAggregateInput
                {
                    where = new policyMembersFilterInput { havingStartDate = true }
                });

            policy.members!.totalCount.Should().Be(1);
            policy.members.list!.Count.Should().Be(1);
            policy.members.list.ElementAt(0)!.startDate.Should().NotBeNull();

            policy.membersMovements!.totalCount.Should().Be(1);
            policy.membersMovements.list!.Count.Should().Be(1);
            policy.membersMovements.list.ElementAt(0)!.startDate.Should().NotBeNull();
        }

        [Theory]
        [InlineData(false)]
        public async Task
            GIVEN_policy_with_few_members_with_different_dates_WHEN_filtering_by_is_isTerminated_THEN_only_the_filtered_member_appears(
                bool isTerminated)
        {
            string policyId = await Policy.Create();
            DateTime now = DateTime.UtcNow;

            string member1Id = CreateNewGuid();
            string member2Id = CreateNewGuid();
            string member3Id = CreateNewGuid();
            string member4Id = CreateNewGuid();
            string member5Id = CreateNewGuid();
            string member6Id = CreateNewGuid();

            await MembersBatch(policyId,
                new policyMembersBatchInput
                {
                    create = new List<policyMemberInput?>
                    {
                        new() { memberId = member1Id, startDate = now.AddDays(-1) },
                        new() { memberId = member2Id, startDate = now.AddDays(-2), endDate = now.AddDays(-1) },
                        new() { memberId = member3Id, startDate = now.AddDays(-2), endDate = now.AddDays(1) },
                        new() { memberId = member4Id, startDate = now.AddDays(1) },
                        new() { memberId = member5Id, startDate = now.AddDays(1), endDate = now.AddDays(2) },
                        new() { memberId = member6Id }
                    }
                });

            policy policy = await FindById(policyId, isTerminated: isTerminated);

            string[] expectedMemberIds = isTerminated ? new[] { member2Id, member6Id } : new[] { member1Id, member3Id, member4Id, member5Id };

            string[] membersIds = policy.members!.list!.Select(x => x!.memberId)!.ToArray()!;

            membersIds.Should().BeEquivalentTo(expectedMemberIds);
        }

        [Fact]
        public async Task
            GIVEN_policy_with_few_members_WHEN_filtering_by_createdAt_lt_THEN_return_correct_members()
        {
            string policyId = await Policy.Create();


            string member1Id = CreateNewGuid();
            string member2Id = CreateNewGuid();

            await MembersBatch(policyId,
                new policyMembersBatchInput
                {
                    create = new List<policyMemberInput?>
                    {
                        new() { memberId = member1Id},
                    }
                });

            await Task.Delay(TimeSpan.FromSeconds(2));
            DateTime now = DateTime.UtcNow;

            await MembersBatch(policyId,
                new policyMembersBatchInput
                {
                    create = new List<policyMemberInput?>
                    {
                        new() { memberId = member2Id},
                    }
                });

            policy policy = await FindById(policyId, where: new policyMembersFilterAggregateInput{where = new policyMembersFilterInput{createdAt_lt = now}});

            string[] expectedMemberIds = {member1Id};

            string[] membersIds = policy.members!.list!.Select(x => x!.memberId)!.ToArray()!;

            membersIds.Should().BeEquivalentTo(expectedMemberIds);
        }

        [Fact]
        public async Task
            GIVEN_policy_with_few_members_WHEN_filtering_by_createdAt_gt_THEN_return_correct_members()
        {
            string policyId = await Policy.Create();


            string member1Id = CreateNewGuid();
            string member2Id = CreateNewGuid();

            await MembersBatch(policyId,
                new policyMembersBatchInput
                {
                    create = new List<policyMemberInput?>
                    {
                        new() { memberId = member1Id},
                    }
                });

            await Task.Delay(TimeSpan.FromSeconds(2));
            DateTime now = DateTime.UtcNow;

            await MembersBatch(policyId,
                new policyMembersBatchInput
                {
                    create = new List<policyMemberInput?>
                    {
                        new() { memberId = member2Id},
                    }
                });

            policy policy = await FindById(policyId, where: new policyMembersFilterAggregateInput{where = new policyMembersFilterInput{createdAt_gt = now}});

            string[] expectedMemberIds = {member2Id};

            string[] membersIds = policy.members!.list!.Select(x => x!.memberId)!.ToArray()!;

            membersIds.Should().BeEquivalentTo(expectedMemberIds);
        }

        [Fact]
        public async Task
            GIVEN_policy_with_few_members_WHEN_filtering_by_lastModifiedAt_lt_THEN_return_correct_members()
        {
            string policyId = await Policy.Create();


            string member1Id = CreateNewGuid();
            string member2Id = CreateNewGuid();

            await MembersBatch(policyId,
                new policyMembersBatchInput
                {
                    create = new List<policyMemberInput?>
                    {
                        new() { memberId = member1Id},
                    }
                });


            await Task.Delay(TimeSpan.FromSeconds(2));
            DateTime now = DateTime.UtcNow;

            await MembersBatch(policyId,
                new policyMembersBatchInput
                {
                    create = new List<policyMemberInput?>
                    {
                        new() { memberId = member2Id},
                    }
                });

            policy policy = await FindById(policyId, where: new policyMembersFilterAggregateInput{where = new policyMembersFilterInput{lastModifiedAt_lt = now}});

            string[] expectedMemberIds = {member1Id};

            string[] membersIds = policy.members!.list!.Select(x => x!.memberId)!.ToArray()!;

            membersIds.Should().BeEquivalentTo(expectedMemberIds);
        }

        [Fact]
        public async Task
            GIVEN_policy_with_few_members_WHEN_filtering_by_lastModifiedAt_gt_THEN_return_correct_members()
        {
            string policyId = await Policy.Create();


            string member1Id = CreateNewGuid();
            string member2Id = CreateNewGuid();

            await MembersBatch(policyId,
                new policyMembersBatchInput
                {
                    create = new List<policyMemberInput?>
                    {
                        new() { memberId = member1Id},
                    }
                });

            await Task.Delay(TimeSpan.FromSeconds(2));
            DateTime now = DateTime.UtcNow;

            await MembersBatch(policyId,
                new policyMembersBatchInput
                {
                    create = new List<policyMemberInput?>
                    {
                        new() { memberId = member2Id},
                    }
                });

            policy policy = await FindById(policyId, where: new policyMembersFilterAggregateInput{where = new policyMembersFilterInput{lastModifiedAt_gt = now}});

            string[] expectedMemberIds = {member2Id};

            string[] membersIds = policy.members!.list!.Select(x => x!.memberId)!.ToArray()!;

            membersIds.Should().BeEquivalentTo(expectedMemberIds);
        }

        [Fact]
        public async Task GIVEN_policy_with_member_having_endDate_in_future_WHEN_querying_after_the_endDate_THEN_the_member_is_terminated()
        {
            string policyId = await Policy.Create();
            DateTime now = DateTime.UtcNow;

            string member1Id = CreateNewGuid();

            await MembersBatch(policyId,
                new policyMembersBatchInput
                {
                    create = new List<policyMemberInput?>
                    {
                        new() { memberId = member1Id, startDate = now.AddDays(-1), endDate = now.AddDays(3) }
                    }
                });

            policy policy = await FindById(policyId, isTerminated: true, asOf: now.AddDays(4));

            policy.members!.totalCount.Should().Be(1);
            var member = policy.members!.list!.FirstOrDefault()!;

            member.memberId.Should().Be(member1Id);
        }

        [Fact]
        public async Task
            GIVEN_policy_with_members_added_through_different_endorsements_WHEN_querying_report_THEN_successfully_calculated()
        {
            string policyId = await Policy.Create();
            string endorsement1Id = await Policy.Endorsement.Add(policyId);
            string endorsement2Id = await Policy.Endorsement.Add(policyId);

            await Policy.Endorsement.Accept(policyId, endorsement1Id);
            await Policy.Endorsement.Accept(policyId, endorsement2Id);

            #region data init

            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new List<policyMemberInput?>
                {
                    new()
                    {
                        fields = new
                        {
                            movementData = new
                            {
                                movementType = "changedPlan",
                                changedAmount = 150.22,
                                isChangePlanBefore = false
                            }
                        }.ToEscapedJsonString()
                    },
                    new() { fields = new { movementData = new { changedAmount = 150.22 } }.ToEscapedJsonString() },
                    new()
                    {
                        fields = new
                        {
                            movementData = new
                            {
                                movementType = "changedPlan",
                                changedAmount = 150.22,
                                isChangePlanBefore = true
                            }
                        }.ToEscapedJsonString()
                    },
                    new()
                    {
                        fields = new { movementData = new { movementType = "changedPlan", changedAmount = 150.22 } }
                            .ToEscapedJsonString()
                    },
                    new()
                    {
                        fields = new { movementData = new { movementType = "changedPlan", changedAmount = 150.22 } }
                            .ToEscapedJsonString()
                    },
                }
            }, endorsement1Id);

            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new List<policyMemberInput?>
                {
                    new()
                    {
                        fields = new
                        {
                            movementData = new
                            {
                                movementType = "changedPlan",
                                changedAmount = 120.22,
                                isChangePlanBefore = false
                            }
                        }.ToEscapedJsonString()
                    },
                    new() { fields = new { movementData = new { changedAmount = 120.22 } }.ToEscapedJsonString() },
                    new()
                    {
                        fields = new
                        {
                            movementData = new
                            {
                                movementType = "changedPlan",
                                changedAmount = 120.22,
                                isChangePlanBefore = true
                            }
                        }.ToEscapedJsonString()
                    },
                    new()
                    {
                        fields = new { movementData = new { movementType = "changedPlan", changedAmount = 120.22 } }
                            .ToEscapedJsonString()
                    },
                    new()
                    {
                        fields = new { movementData = new { movementType = "changedPlan", changedAmount = 120.22 } }
                            .ToEscapedJsonString()
                    },
                }
            }, endorsement2Id);

            #endregion

            string? reportQuery = new QueryBuilder().policyMembersMovementsReport(
                new QueryBuilder.policyMembersMovementsReportArgs(
                    new policyMembersWhereInput { policyId_In = new List<string?> { policyId } }),
                new policyMembersMovementsReportBuilder()
                    .totalCount()
                    .list(new policyMemberMovementReportItemBuilder()
                        .endorsement(new endorsementBuilder()
                            .id())
                        .policy(new policyBuilder()
                            .id())
                        .movementType()
                        .amount())).Build();

            policyMembersMovementsReport report =
                await _client.SendMutationAsync<policyMembersMovementsReport>(reportQuery);
            report.totalCount.Should().Be(4);

            policyMemberMovementReportItem?[] items = report!.list!
                .OrderBy(i => i!.policy!.id)
                .ThenBy(i => i!.endorsement!.id)
                .ThenBy(i => i!.movementType)
                .ToArray();

            policyMemberMovementReportItem? endorsement1ChangedPlanItem = items.First(x => x!.endorsement!.id == endorsement1Id && x.movementType == "changedPlan");
            policyMemberMovementReportItem? endorsement2ChangedPlanItem = items.First(x => x!.endorsement!.id == endorsement2Id && x.movementType == "changedPlan");
            policyMemberMovementReportItem? endorsement1NoTypeItem = items.First(x => x!.endorsement!.id == endorsement1Id && x.movementType == null);
            policyMemberMovementReportItem? endorsement2NoTypeItem = items.First(x => x!.endorsement!.id == endorsement2Id && x.movementType == null);

            endorsement1ChangedPlanItem!.amount.Should().Be(450.66m);
            endorsement2ChangedPlanItem!.amount.Should().Be(360.66m);
            endorsement1NoTypeItem!.amount.Should().Be(150.22m);
            endorsement2NoTypeItem!.amount.Should().Be(120.22);
        }

        [Fact]
        public async Task
            GIVEN_policy_and_one_member_added_WHEN_mark_member_movement_as_printed_THEN_marked_as_printed_and_no_new_moveemnt_log_added()
        {
            string policyId = await Policy.Create();
            await MembersBatch(policyId, new policyMembersBatchInput { create = new List<policyMemberInput?> { new() } });
            policy policy = await FindById(policyId);

            policy!.membersMovements!.totalCount.Should().Be(1);
            policyMember movement = policy!.membersMovements!.list!.First()!;
            movement.isPrinted.Should().BeFalse();

            string mutation = new MutationBuilder()
                .policyMembersMovementLogItemMarkPrinted(new MutationBuilder.policyMembersMovementLogItemMarkPrintedArgs(movement!.id!), new resultBuilder().status()).Build();
            await _client.SendMutationAndEnsureSuccessAsync(mutation);

            policy = await FindById(policyId);

            policy!.membersMovements!.totalCount.Should().Be(1);
            movement = policy!.membersMovements!.list!.First()!;
            movement.isPrinted.Should().BeTrue();
        }

        [Fact]
        public async Task GIVEN_policy_and_nine_members_created_WHEN_order_by_createdAt_THEN_returns_in_correct_order()
        {
            string policyId = await Policy.Create();
            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new List<policyMemberInput?>
                {
                    new() { memberId = "member-1" },
                    new() { memberId = "member-2" },
                    new() { memberId = "member-3" },
                    new() { memberId = "member-4" },
                    new() { memberId = "member-5" },
                    new() { memberId = "member-6" },
                    new() { memberId = "member-7" },
                    new() { memberId = "member-8" },
                    new() { memberId = "member-9" },
                }
            });

            policy policy = await FindById(policyId, sort: new sortInput
            {
                fieldName = "createdAt",
                type = "asc"
            });

            string policyMembersIds = string.Join("|", policy.members!.list!.Select(x => x!.memberId).ToArray()!);

            policyMembersIds.Should().Be(string.Join("|", new[] { "member-1", "member-2", "member-3", "member-4", "member-5", "member-6", "member-7", "member-8", "member-9" }));
        }

        [Fact]
        public async Task GIVEN_policy_and_nine_members_created_WHEN_order_by_name_and_created_date_THEN_returns_in_correct_order()
        {
            string policyId = await Policy.Create();
            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new List<policyMemberInput?>
                {
                    new() { memberId = "member-1", validationResult = PolicyMemberValidationResult.INVALID },
                    new() { memberId = "member-2", validationResult = PolicyMemberValidationResult.INVALID },
                    new() { memberId = "member-3", validationResult = PolicyMemberValidationResult.VALID },
                    new() { memberId = "member-4", validationResult = PolicyMemberValidationResult.INVALID },
                    new() { memberId = "member-5" },
                    new() { memberId = "member-6" },
                    new() { memberId = "member-7" },
                    new() { memberId = "member-8" },
                    new() { memberId = "member-9" },
                }
            });

            policy policy = await FindById(
                policyId,
                sort2: new List<sortInput>
                {
                    new()
                    {
                        fieldName = "validationResult",
                        type = "asc",
                    },
                    new()
                    {
                        fieldName = "createdAt",
                        type = "asc",
                    },
                });

            string policyMembersIds = string.Join("|", policy.members!.list!.Select(x => x!.memberId).ToArray()!);

            policyMembersIds.Should().Be(string.Join("|",
                new[] { "member-5|member-6|member-7|member-8|member-9|member-1|member-2|member-4|member-3" }));
        }

        [Fact]
        public async Task GIVEN_policy_and_one_member_added_WHEN_updating_member_THEN_creation_date_is_not_lost()
        {
            string policyId = await Policy.Create();
            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new List<policyMemberInput?>
                {
                    new() { memberId = "member-1" }
                }
            });

            policy policy = await FindById(policyId);
            policyMember member = policy.members!.list!.FirstOrDefault()!;

            DateTime? createdAt = member.createdAt!;
            DateTime? updatedAt = member.lastModifiedAt!;

            await MembersBatch(policyId, new policyMembersBatchInput
            {
                update = new List<policyMemberInput?>
                {
                    new() { memberId = "member-1", createdAt = createdAt, planId = "plan-1" }
                }
            });

            policy = await FindById(policyId);
            member = policy.members!.list!.FirstOrDefault()!;

            member.createdAt.Should().Be(createdAt);
            member.lastModifiedAt!.Should().NotBe(updatedAt.Value);
        }

        public static readonly object[][] IndividualsMembersTestData =
        {
            new object[] { DateTime.UtcNow.AddMonths(-1), null!, true },
            new object[] { DateTime.UtcNow.AddMonths(-2), DateTime.UtcNow.AddMonths(-1), false },
        };

        [Theory, MemberData(nameof(IndividualsMembersTestData))]
        public async Task
            GIVEN_individual_and_policy_WHEN_adding_individual_to_policy_as_member_with_start_date_and_end_date_THEN_individual_status_is_correct(
                DateTime? startDate, DateTime? endDate, bool hasActivePolicy)
        {
            string policyId = await Policy.Create(new initializePolicyInput
            {
                startDate = DateTime.UtcNow.AddMonths(-2),
                endDate = DateTime.UtcNow.AddMonths(2),
            });
            string individualId = await Entity.CreateIndividual();

            await MembersBatch(policyId,
                new policyMembersBatchInput
                {
                    create = new List<policyMemberInput?>
                    {
                        new()
                        {
                            fields = new { individualId }.ToEscapedJsonString(),
                            startDate = startDate,
                            endDate = endDate
                        }
                    }
                });

            individuals individuals =
                await Entity.FindIndividual(new individualWhereInput { id_in = new[] { individualId } });
            individuals.list!.First()!.hasActivePolicy.Should().Be(hasActivePolicy);
        }


        public static readonly object[][] IndividualsMembersEndorsementsTestData =
        {
            // startDate, endDate, acceptEndorsement, hasActivePolicy
            new object[] { DateTime.UtcNow.AddMonths(-2), null!, true, true },
            new object[] { DateTime.UtcNow.AddMonths(-2), null!, false, false },
            new object[] { DateTime.UtcNow.AddMonths(-2), DateTime.UtcNow.AddMonths(-1), true, false },
            new object[] { DateTime.UtcNow.AddMonths(-2), DateTime.UtcNow.AddMonths(-1), false, false },
        };

        public PoliciesMembersTests(ITestOutputHelper output) : base(output)
        {
        }

        [Theory, MemberData(nameof(IndividualsMembersEndorsementsTestData))]
        public async Task
            GIVEN_individual_and_policy_WHEN_adding_individual_to_policy_as_member_with_start_date_and_end_date_trhough_endorsement_THEN_individual_status_is_correct(
                DateTime? startDate, DateTime? endDate, bool acceptEndorsement, bool hasActivePolicy)
        {
            string policyId = await Policy.Create(new initializePolicyInput
            {
                fields = "{}",
                startDate = startDate,
                endDate = DateTime.UtcNow.AddMonths(2),
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid()
            });
            string individualId = await Entity.CreateIndividual();
            string endorsementId = await Policy.Endorsement.Add(policyId);

            await MembersBatch(policyId,
                new policyMembersBatchInput
                {
                    create = new List<policyMemberInput?>
                    {
                        new()
                        {
                            fields = new { individualId }.ToEscapedJsonString(),
                            startDate = startDate,
                            endDate = endDate
                        }
                    }
                }, endorsementId);

            if (acceptEndorsement) await Policy.Endorsement.Accept(policyId, endorsementId);

            individuals? individuals =
                await Entity.FindIndividual(new individualWhereInput { id_in = new[] { individualId } });
            individuals!.list!.First()!.hasActivePolicy.Should().Be(hasActivePolicy);
        }

        [Fact]
        public async Task GIVEN_policy_with_member_added_WHEN_assigning_underwriting_and_validation_results_THEN_changes_applied()
        {
            string policyId = await Policy.Create();
            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new List<policyMemberInput?>
                {
                    new()
                    {
                        underwritingResult = PolicyMemberUnderwritingResult.APPROVED,
                        validationResult = PolicyMemberValidationResult.VALID,
                    },
                }
            });

            policy policy = await FindById(policyId);
            policyMember member = policy.members!.list!.FirstOrDefault()!;

            var memberId = member.id;
            member.underwritingResult.Should().Be(PolicyMemberUnderwritingResult.APPROVED);
            member.validationResult.Should().Be(PolicyMemberValidationResult.VALID);

            await MembersBatch(policyId, new policyMembersBatchInput
            {
                update = new List<policyMemberInput?>
                {
                    new()
                    {
                        memberId = memberId,
                        underwritingResult = PolicyMemberUnderwritingResult.MANUAL_PENDING,
                        validationResult = PolicyMemberValidationResult.INVALID,
                    },
                }
            });

            policy = await FindById(
                policyId,
                where: new policyMembersFilterAggregateInput()
                {
                    where = new policyMembersFilterInput
                    {
                        underwritingResult = PolicyMemberUnderwritingResult.MANUAL_PENDING,
                        validationResult = PolicyMemberValidationResult.INVALID,
                    }
                });
            member = policy.members!.list!.FirstOrDefault()!;

            member.underwritingResult.Should().Be(PolicyMemberUnderwritingResult.MANUAL_PENDING);
            member.validationResult.Should().Be(PolicyMemberValidationResult.INVALID);

            policy = await FindById(
                policyId,
                where: new policyMembersFilterAggregateInput()
                {
                    where = new policyMembersFilterInput
                    {
                        underwritingResult = PolicyMemberUnderwritingResult.MANUAL_REJECTED,
                        validationResult = PolicyMemberValidationResult.INVALID,
                    }
                });
            policy.members!.list!.FirstOrDefault().Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_policy_with_member_added_WHEN_filtering_by_underwriting_and_validation_in_THEN_members_are_filtered()
        {
            string policyId = await Policy.Create();
            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new List<policyMemberInput?>
                {
                    new()
                    {
                        underwritingResult = PolicyMemberUnderwritingResult.APPROVED,
                        validationResult = PolicyMemberValidationResult.VALID,
                    },
                    new()
                    {
                        underwritingResult = PolicyMemberUnderwritingResult.APPROVED,
                        validationResult = PolicyMemberValidationResult.INVALID,
                    },
                    new()
                    {
                        underwritingResult = PolicyMemberUnderwritingResult.MANUAL_APPROVED,
                        validationResult = PolicyMemberValidationResult.INVALID,
                    },
                }
            });

            var policy = await FindById(policyId);
            policy.members!.list!.Count.Should().Be(3);

            policy = await FindById(
                policyId,
                where: new policyMembersFilterAggregateInput()
                {
                    where = new policyMembersFilterInput
                    {
                        underwritingResult_in = new List<PolicyMemberUnderwritingResult?>
                        {
                            PolicyMemberUnderwritingResult.APPROVED,
                        }
                    }
                });
            policy.members!.list!.Count.Should().Be(2);

            policy = await FindById(
                policyId,
                where: new policyMembersFilterAggregateInput()
                {
                    and = new policyMembersFilterAggregateInput[]
                    {
                        new()
                        {
                            where = new policyMembersFilterInput
                            {
                                validationResult_in = new List<PolicyMemberValidationResult?>
                                {
                                    PolicyMemberValidationResult.INVALID,
                                },
                            }
                        },
                        new()
                        {
                            where = new policyMembersFilterInput
                            {
                                underwritingResult_in = new List<PolicyMemberUnderwritingResult?>
                                {
                                    PolicyMemberUnderwritingResult.APPROVED,
                                },
                            }
                        },
                    }
                });
            policy.members!.list!.Count.Should().Be(1);
        }

        [Fact]
        public async Task GIVEN_policy_with_terminated_member_added_WHEN_query_with_no_terminated_filter_THEN_members_are_returned()
        {
            var utcNow = DateTime.Now;
            string policyId = await Policy.Create(new initializePolicyInput
            {
                startDate = utcNow.AddDays(-1),
                endDate = utcNow.AddDays(-2),
            });

            await MembersBatch(
                policyId,
                new policyMembersBatchInput
                {
                    create = new List<policyMemberInput?>
                    {
                        new()
                        {
                            memberId = "abra kadabra",
                            startDate = utcNow.AddDays(-1),
                            endDate = utcNow.AddDays(-2),
                            fields = "{\\\"movementType\\\": \\\"terminated\\\"}"
                        },
                    }
                });

            var filter = new policyMembersFilterAggregateInput()
            {
                and = new[]
                {
                    new policyMembersFilterAggregateInput()
                    {
                        where = new policyMembersFilterInput()
                        {
                            fields = new fieldsWhereInput
                            {
                                condition = fieldsWhereCondition.EQUALS,
                                path = "fields.movementType",
                                value = new scalarValueInput()
                                {
                                    stringValue = "terminated"
                                }
                            },
                        }
                    },
                }
            };

            var policy = await FindById(
                policyId,
                where: filter,
                isTerminated: true,
                filterOutTerminatedMemberMovements: false);
            policy.members!.list!.Count.Should().Be(1);
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_updating_member_with_the_same_input_twice_THEN_memberMovements_return_latest_update()
        {
            var policyId = await Policy.Create();
            var endorsementId = await Policy.Endorsement.Add(policyId);
            var memberId = CreateNewGuid();

            var startDate = DateTime.UtcNow;

            var createInput = new policyMemberInput
            {
                memberId = memberId,
                planId = "employee",
                startDate = startDate,
                fields = @"{ ""a"": ""b"" }".Escape()
            };

            var updateInput = new policyMemberInput
            {
                memberId = memberId,
                planId = "employee",
                startDate = startDate,
                internalCode = "U00045076-00",
                fields = @"{ ""a"": ""b"", ""b"": ""c"" }".Escape()
            };

            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new policyMemberInput[] { createInput },
                update = new policyMemberInput[] { updateInput, updateInput }
            }, endorsementId);

            await Policy.Endorsement.Accept(policyId, endorsementId);

            var policy = await FindById(policyId);

            var membersMovements = policy.membersMovements!.list!.ToArray();

            membersMovements.Length.Should().Be(1);
            membersMovements[0].internalCode.Should().Be("U00045076-00");
        }

        [Fact]
        public async Task GIVEN_policy_with_no_members_WHEN_adding_one_member_through_the_endorsement_AND_approving_endorsement_AND_adding_one_more_with_different_endorsement_AND_previewing_the_endorsement_THEN_second_member_appears()
        {
            string policyId = await Policy.Create();
            string endorsementAId = await Policy.Endorsement.Add(policyId);

            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new policyMemberInput[] { new policyMemberInput { memberId = CreateNewGuid() } }
            }, endorsementAId);

            await Policy.Endorsement.Accept(policyId, endorsementAId);

            await Task.Delay(TimeSpan.FromSeconds(1));

            string endorsementBId = await Policy.Endorsement.Add(policyId);

            await MembersBatch(policyId, new policyMembersBatchInput
            {
                create = new policyMemberInput[] { new policyMemberInput { memberId = CreateNewGuid() } }
            }, endorsementBId);

            string query = new QueryBuilder().policies(
                new(where: new policyWhereInput { id = policyId }),
                new policiesBuilder()
                    .list(new policyBuilder()
                        .endorsements(
                            new policyBuilder.endorsementsArgs(where: new endorsementWhereInput { id = endorsementBId }),
                            new endorsementBuilder()
                                .afterEndorsement(new policyBuilder()
                                    .members(
                                        new policyBuilder.membersArgs(),
                                        new membersBuilder()
                                            .list(new policyMemberBuilder()
                                                .memberId()
                                                .endorsementId()))))))
            .Build();

            policies policies = await _client.SendQueryAsync<policies>(query);

            policyMember[] members = policies!.list!.FirstOrDefault()!.endorsements!.FirstOrDefault()!.afterEndorsement!.members!.list!.ToArray()!;

            members.Length.Should().Be(2);
            members.Any(it => it.endorsementId == endorsementAId).Should().BeTrue();
            members.Any(it => it.endorsementId == endorsementBId).Should().BeTrue();
        }

        private Task MembersBatch(string policyId, policyMembersBatchInput batchInput,
            string endorsementId = null!)
        {
            string? mutation = new MutationBuilder()
                .policyMembersBatch(
                    new MutationBuilder.policyMembersBatchArgs(policyId, batchInput, endorsementId)
                    , new resultBuilder()
                        .status())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        async Task<policy> FindById(
            string policyId,
            DateTime? asOf = null,
            policyMembersFilterAggregateInput? where = null,
            sortInput? sort = null,
            List<sortInput>? sort2 = null,
            bool? isTerminated = null,
            bool? filterOutTerminatedMemberMovements = null,
            bool? filterOutUnderwritingNotApproved = null)
        {
            string query = new QueryBuilder()
                .policies(
                    new QueryBuilder.policiesArgs(where: new policyWhereInput { id = policyId }, asOf: asOf),
                    new policiesBuilder()
                        .list(PolicyWithMembersQueryBuild(where, sort, sort2, isTerminated, filterOutTerminatedMemberMovements, filterOutUnderwritingNotApproved)))
                .Build();

            policies response = await _client.SendQueryAsync<policies>(query);
            return response.list!.Single()!;
        }

        private static policyBuilder PolicyWithMembersQueryBuild(
            policyMembersFilterAggregateInput where = null!,
            sortInput? sort = null,
            List<sortInput>? sort2 = null,
            bool? isTerminated = null,
            bool? filterOutTerminatedMemberMovements = null,
            bool? filterOutUnderwritingNotApproved = null) =>
               new policyBuilder()
                .id()
                .members(new policyBuilder.membersArgs(filter: where, sort: sort, sort2: sort2, isTerminated: isTerminated, filterOutTerminatedMemberMovements: filterOutTerminatedMemberMovements,filterOutUnderwritingNotApproved: filterOutUnderwritingNotApproved), new membersBuilder()
                    .list(PolicyMemberBuilder())
                    .totalCount())
                .membersMovements(new policyBuilder.membersMovementsArgs(filter: where, sort: sort, sort2: sort2),
                    new membersMovementsBuilder()
                        .list(PolicyMemberBuilder())
                        .totalCount())
                .membersActivity(new policyBuilder.membersActivityArgs(filter: where, sort: sort, sort2: sort2),
                    new membersActivityBuilder()
                        .list(PolicyMemberBuilder())
                        .totalCount());

        private static policyMemberBuilder PolicyMemberBuilder() =>
            new policyMemberBuilder()
                .id()
                .memberId()
                .policyId()
                .createdAt()
                .lastModifiedAt()
                .internalCode()
                .dependentOf()
                .timestamp()
                .endorsementId()
                .planId()
                .startDate()
                .endDate()
                .fields()
                .createdById()
                .isRemoved()
                .isPrinted()
                .underwritingResult()
                .validationResult();

        private static string CreateMemberId() => CreateNewGuid().Substring(8);
    }
}