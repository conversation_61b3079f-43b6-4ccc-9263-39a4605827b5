using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Policies
{
    public class PolicyUnderwritingsTests : TestsBase
    {
        public PolicyUnderwritingsTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_policy_with_policy_member_added_WHEN_adding_underwriting_THEN_success()
        {
            string policyId = await Policy.Create();
            string memberId = await PolicyMemberAdd(policyId);

            string testField = CreateNewGuid();
            string fields = new { field1 = testField }.ToEscapedJsonString();

            await PolicyUnderwritingAdd(policyId, new policies_CreatePolicyUnderwritingCommandInput
            {
                policyMemberId = memberId,
                fields = fields,
                status = "test",
            });

            policyUnderwriting[] underwritings = await FindByPolicyId(policyId);

            underwritings.Length.Should().Be(1);

            policyUnderwriting underwriting = underwritings[0];

            underwriting.policyMember.Should().NotBeNull();
            underwriting.fields.Should().Contain(testField);
            underwriting.status.Should().Be("test");
        }

        [Fact]
        public async Task
            GIVEN_policy_with_policy_member_and_underwriting_added_WHEN_adding_updating_and_removing_underwriting_remark_THEN_success()
        {
            string policyId = await Policy.Create();
            string memberId = await PolicyMemberAdd(policyId);

            string testField = CreateNewGuid();
            string fields = new { field1 = testField }.ToEscapedJsonString();

            await PolicyUnderwritingAdd(policyId, new policies_CreatePolicyUnderwritingCommandInput
            {
                policyMemberId = memberId,
                fields = fields,
                status = "test",
            });

            policyUnderwriting[] underwritings = await FindByPolicyId(policyId);
            policyUnderwriting underwriting = underwritings[0];

            string remark = CreateNewGuid();
            string remarkType = CreateNewGuid();

            string? mutation = new MutationBuilder().policyUnderwritingMutationAddRemark(
                new MutationBuilder.policyUnderwritingMutationAddRemarkArgs(
                    command: new policies_AddPolicyUnderwritingRemarkCommandInput
                    {
                        underwritingId = underwriting.id,
                        policyId = policyId,
                        remark = remark,
                        remarkType = remarkType
                    }), new policies_ResultBuilder().status()).Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);

            underwritings = await FindByPolicyId(policyId);
            underwriting = underwritings[0];

            policyUnderwritingRemark?[] remarks = underwriting.remarks!.ToArray();

            remarks.Length.Should().Be(1);

            remarks[0]!.remark.Should().Be(remark);
            remarks[0]!.remarkType.Should().Be(remarkType);

            string remarkUpdated = CreateNewGuid();
            string remarkTypeUpdated = CreateNewGuid();

            mutation = new MutationBuilder().policyUnderwritingMutationUpdateRemark(
                new MutationBuilder.policyUnderwritingMutationUpdateRemarkArgs(
                    command: new policies_UpdatePolicyUnderwritingRemarkCommandInput
                    {
                        remarkId = remarks[0]!.id,
                        underwritingId = underwriting.id,
                        policyId = policyId,
                        remark = remarkUpdated,
                        remarkType = remarkTypeUpdated
                    }), new policies_ResultBuilder().status()).Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);

            underwritings = await FindByPolicyId(policyId);
            underwriting = underwritings[0];

            remarks = underwriting.remarks!.ToArray();

            remarks.Length.Should().Be(1);

            remarks[0]!.remark.Should().Be(remarkUpdated);
            remarks[0]!.remarkType.Should().Be(remarkTypeUpdated);

            mutation = new MutationBuilder().policyUnderwritingMutationDeleteRemark(
                new MutationBuilder.policyUnderwritingMutationDeleteRemarkArgs(
                    command: new policies_DeletePolicyUnderwritingRemarkCommandInput
                    {
                        remarkId = remarks[0]!.id,
                        underwritingId = underwriting.id,
                        policyId = policyId,
                    }), new policies_ResultBuilder().status()).Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);

            underwritings = await FindByPolicyId(policyId);
            underwriting = underwritings[0];

            remarks = underwriting.remarks!.ToArray();

            remarks.Should().BeEmpty();
        }

        [Fact]
        public async Task
            GIVEN_policy_with_policy_member_with_underwriting_added_WHEN_updating_underwriting_THEN_success()
        {
            string policyId = await Policy.Create();
            string memberId = await PolicyMemberAdd(policyId);

            string testField = CreateNewGuid();
            string fields = new { field1 = testField }.ToEscapedJsonString();

            await PolicyUnderwritingAdd(policyId, new policies_CreatePolicyUnderwritingCommandInput
            {
                policyMemberId = memberId,
                fields = fields,
                status = "test",
            });

            policyUnderwriting[] underwritings = await FindByPolicyId(policyId);
            policyUnderwriting underwriting = underwritings[0];

            await PolicyUnderwritingUpdate(policyId,
                new policies_UpdatePolicyUnderwritingCommandInput { id = underwriting.id, status = "updated", });

            underwritings = await FindByPolicyId(policyId);
            underwriting = underwritings[0];

            underwriting.status.Should().Be("updated");
        }

        [Fact]
        public async Task
            GIVEN_policy_with_policy_member_with_underwriting_added_WHEN_delete_underwriting_THEN_success()
        {
            string policyId = await Policy.Create();
            string memberId = await PolicyMemberAdd(policyId);

            string testField = CreateNewGuid();
            string fields = new { field1 = testField }.ToEscapedJsonString();

            await PolicyUnderwritingAdd(policyId, new policies_CreatePolicyUnderwritingCommandInput
            {
                policyMemberId = memberId,
                fields = fields,
                status = "test",
            });

            policyUnderwriting[] underwritings = await FindByPolicyId(policyId);
            policyUnderwriting underwriting = underwritings[0];

            await PolicyUnderwritingDelete(policyId, new policies_DeletePolicyUnderwritingCommandInput { id = underwriting.id });

            underwritings = await FindByPolicyId(policyId);
            underwritings.Should().BeEmpty();
        }

        [Fact]
        public async Task
            GIVEN_policy_with_policy_member_added_WHEN_adding_underwriting_with_endorsement_THEN_underwriting_added_only_when_endorsement_accepted()
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId);
            string memberId = await PolicyMemberAdd(policyId);

            string testField = CreateNewGuid();
            string fields = new { field1 = testField }.ToEscapedJsonString();

            await PolicyUnderwritingAdd(policyId, new policies_CreatePolicyUnderwritingCommandInput
            {
                policyMemberId = memberId,
                fields = fields,
                status = "test",
            }, endorsementId);

            policyUnderwriting[] underwritings = await FindByPolicyId(policyId);

            underwritings.Should().BeEmpty();

            await Policy.Endorsement.Accept(policyId, endorsementId);

            underwritings = await FindByPolicyId(policyId);

            underwritings.Length.Should().Be(1);
            policyUnderwriting underwriting = underwritings[0];

            underwriting.policyMember.Should().NotBeNull();
            underwriting.fields.Should().Contain(testField);
            underwriting.status.Should().Be("test");
        }

        [Fact]
        public async Task
            GIVEN_policy_with_policy_member_added_WHEN_adding_underwriting_with_endorsement_and_preview_endorsement_THEN_underwriting_appears()
        {
            string policyId = await Policy.Create();
            string endorsementId = await Policy.Endorsement.Add(policyId);
            string memberId = await PolicyMemberAdd(policyId);

            string testField = CreateNewGuid();
            string fields = new { field1 = testField }.ToEscapedJsonString();

            await PolicyUnderwritingAdd(policyId, new policies_CreatePolicyUnderwritingCommandInput
            {
                policyMemberId = memberId,
                fields = fields,
                status = "test",
            }, endorsementId);

            policyUnderwriting[] underwritings = await PreviewInEndorsement(policyId, endorsementId);

            underwritings.Length.Should().Be(1);
            policyUnderwriting underwriting = underwritings[0];

            underwriting.policyMember.Should().NotBeNull();
            underwriting.fields.Should().Contain(testField);
            underwriting.status.Should().Be("test");
        }

        [Fact]
        public async Task
            GIVEN_policy_with_policy_member_added_WHEN_adding_underwriting_with_future_dated_endorsement_and_see_policy_at_endorsement_effective_date_THEN_underwriting_appears()
        {
            string policyId = await Policy.Create();
            DateTime now = DateTime.UtcNow;

            string endorsementId = await Policy.Endorsement.Add(policyId, effectiveDate: now.AddDays(1));
            await Policy.Endorsement.Accept(policyId, endorsementId);

            string memberId = await PolicyMemberAdd(policyId);

            string testField = CreateNewGuid();
            string fields = new { field1 = testField }.ToEscapedJsonString();

            await PolicyUnderwritingAdd(policyId, new policies_CreatePolicyUnderwritingCommandInput
            {
                policyMemberId = memberId,
                fields = fields,
                status = "test",
            }, endorsementId);

            policyUnderwriting[] underwritings = await FindByPolicyId(policyId);
            underwritings.Should().BeEmpty();

            underwritings = await FindByPolicyId(policyId, asOf: now.AddDays(2));

            underwritings.Length.Should().Be(1);
            policyUnderwriting underwriting = underwritings[0];

            underwriting.policyMember.Should().NotBeNull();
            underwriting.fields.Should().Contain(testField);
            underwriting.status.Should().Be("test");
        }

        private async Task PolicyUnderwritingAdd(string policyId, policies_CreatePolicyUnderwritingCommandInput input,
            string endorsementId = null!)
        {
            var batch = new policies_BatchPolicyUnderwritingCommandInput
            {
                policyId = policyId,
                endorsementId = endorsementId,
                create = new List<policies_CreatePolicyUnderwritingCommandInput?> { input }
            };
            await PolicyUnderwritingBatch(batch);
        }

        private async Task PolicyUnderwritingUpdate(string policyId,
            policies_UpdatePolicyUnderwritingCommandInput input, string endorsementId = null!)
        {
            var batch = new policies_BatchPolicyUnderwritingCommandInput
            {
                policyId = policyId,
                endorsementId = endorsementId,
                update = new List<policies_UpdatePolicyUnderwritingCommandInput?> { input }
            };
            await PolicyUnderwritingBatch(batch);
        }

        private async Task PolicyUnderwritingDelete(string policyId,
            policies_DeletePolicyUnderwritingCommandInput input, string endorsementId = null!)
        {
            var batch = new policies_BatchPolicyUnderwritingCommandInput
            {
                policyId = policyId,
                endorsementId = endorsementId,
                delete = new List<policies_DeletePolicyUnderwritingCommandInput?> { input }
            };
            await PolicyUnderwritingBatch(batch);
        }

        private async Task PolicyUnderwritingBatch(policies_BatchPolicyUnderwritingCommandInput batch)
        {
            string? mutation = new MutationBuilder()
                .policyUnderwritingMutationBatch(
                    new MutationBuilder.policyUnderwritingMutationBatchArgs(command: batch),
                    new policies_ResultBuilder().status())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        private async Task<string> PolicyMemberAdd(string policyId, string endorsementId = null!)
        {
            string memberId = Guid.NewGuid().ToString();
            await MembersBatch(policyId,
                new policyMembersBatchInput { create = new List<policyMemberInput?> { new() { memberId = memberId } } },
                endorsementId);

            return memberId;
        }

        private Task MembersBatch(string policyId, policyMembersBatchInput batchInput,
            string endorsementId = null!)
        {
            string? mutation = new MutationBuilder()
                .policyMembersBatch(
                    new MutationBuilder.policyMembersBatchArgs(policyId, batchInput, endorsementId)
                    , new resultBuilder()
                        .status())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        private async Task<policyUnderwriting[]> FindByPolicyId(string policyId, DateTime? asOf = null,
            policyUnderwritingFilterInput where = null!)
        {
            string query = new QueryBuilder()
                .policies(
                    new QueryBuilder.policiesArgs(where: new policyWhereInput { id = policyId }, asOf: asOf),
                    new policiesBuilder()
                        .list(new policyBuilder().id().underwritings(new policyBuilder.underwritingsArgs(filter: where),
                            new policiesUnderwritingsBuilder()
                                .totalCount()
                                .list(new policyUnderwritingBuilder()
                                    .id()
                                    .policyId()
                                    .policyMember(PolicyMemberBuilder())
                                    .fields()
                                    .remarks(new policyUnderwritingRemarkBuilder()
                                        .id()
                                        .remark()
                                        .remarkType()
                                        .fields())
                                    .status()))))
                .Build();

            policies response = await _client.SendQueryAsync<policies>(query);
            policy policy = response.list!.Single()!;

            policyUnderwriting?[] underwritings = policy.underwritings!.list!.ToArray();
            policy.underwritings.totalCount.Should().Be(underwritings.Length);

            return underwritings!;
        }

        private async Task<policyUnderwriting[]> PreviewInEndorsement(string policyId, string endorsementId,
            DateTime? asOf = null,
            policyUnderwritingFilterInput where = null!)
        {
            string query = new QueryBuilder()
                .policies(
                    new QueryBuilder.policiesArgs(where: new policyWhereInput { id = policyId }, asOf: asOf),
                    new policiesBuilder()
                        .list(new policyBuilder()
                            .id()
                            .endorsements(
                                new policyBuilder.endorsementsArgs(
                                    where: new endorsementWhereInput { id = endorsementId }),
                                new endorsementBuilder().afterEndorsement(new policyBuilder()
                                    .id()
                                    .underwritings(new policyBuilder.underwritingsArgs(filter: where),
                                        new policiesUnderwritingsBuilder()
                                            .totalCount()
                                            .list(new policyUnderwritingBuilder()
                                                .id()
                                                .policyId()
                                                .policyMember(PolicyMemberBuilder())
                                                .fields()
                                                .remarks(new policyUnderwritingRemarkBuilder()
                                                    .id()
                                                    .remark()
                                                    .remarkType()
                                                    .fields())
                                                .status())))))).Build();

            policies response = await _client.SendQueryAsync<policies>(query);
            policy policy = response.list!.Single()!;

            endorsement? endorsement = policy.endorsements!.Single();

            policy = endorsement!.afterEndorsement!;

            policyUnderwriting?[] underwritings = policy.underwritings!.list!.ToArray();
            policy.underwritings.totalCount.Should().Be(underwritings.Length);

            return underwritings!;
        }

        private static policyMemberBuilder PolicyMemberBuilder() =>
            new policyMemberBuilder()
                .id()
                .memberId()
                .policyId()
                .createdAt()
                .lastModifiedAt()
                .internalCode()
                .dependentOf()
                .timestamp()
                .endorsementId()
                .planId()
                .startDate()
                .endDate()
                .fields()
                .createdById()
                .isRemoved()
                .isPrinted();
    }
}