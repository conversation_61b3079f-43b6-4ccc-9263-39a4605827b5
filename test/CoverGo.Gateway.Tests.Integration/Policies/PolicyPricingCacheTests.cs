using System.Linq;
using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Policies;

public class PolicyPricingCacheTests : TestsBase
{
    private const string PricingScriptSourceCode = "export function execute({dataInput}) {return `${dataInput}`;}";
    private const string OriginalPricingDataInput = "pricing1";
    private const string NewPricingDataInput = "pricing2";

    public PolicyPricingCacheTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task GIVEN_issued_policy_WHEN_query_pricing_after_updating_policy_THEN_display_cached_pricing()
    {
        productId productId = await SetUpProductAndScript();

        string policyId = await InitializePolicy(productId);
        policy policy = await FindPolicyById(policyId);
        GetPolicyPricingInput(policy).Should().Be(OriginalPricingDataInput);

        // Note: Using updatePolicy won't re-cache pricing,
        // so policy.pricing should use cached pricing (ie.return old pricing data),
        // instead of executing script with updated policy.fields to get latest pricing data,
        // This is a hack of our system to show that pricing are cached after issuing policy
        policy issuedThenUpdatedPolicy = await IssueThenUpdatePolicyViaUpdate(policyId, NewPricingDataInput);
        GetPolicyPricingInput(issuedThenUpdatedPolicy).Should().Be(OriginalPricingDataInput);
    }

    [Fact]
    public async Task GIVEN_issued_policy_WHEN_query_pricing_before_endorsement_accepted_THEN_display_old_cached_pricing()
    {
        productId productId = await SetUpProductAndScript();

        string policyId = await InitializePolicy(productId);
        policy issuedPolicy = await IssueAndGetPolicy(policyId);
        GetPolicyPricingInput(issuedPolicy).Should().Be(OriginalPricingDataInput);

        await AddEndorsementAndUpdatePolicyViaUpdate2(policyId, NewPricingDataInput);
        policy policyWithUnacceptedEndorsement = await FindPolicyById(policyId);
        GetPolicyPricingInput(policyWithUnacceptedEndorsement).Should().Be(OriginalPricingDataInput);
    }

    [Fact]
    public async Task GIVEN_issued_policy_WHEN_query_pricing_after_endorsement_accepted_THEN_display_new_cached_pricing()
    {
        productId productId = await SetUpProductAndScript();

        string policyId = await InitializePolicy(productId);
        policy issuedPolicy = await IssueAndGetPolicy(policyId);
        GetPolicyPricingInput(issuedPolicy).Should().Be(OriginalPricingDataInput);

        string endorsementId = await AddEndorsementAndUpdatePolicyViaUpdate2(policyId, NewPricingDataInput);
        await Policy.Endorsement.Accept(policyId, endorsementId);
        policy policyWithAcceptedEndorsement = await FindPolicyById(policyId);
        GetPolicyPricingInput(policyWithAcceptedEndorsement).Should().Be(NewPricingDataInput);
    }

    [Fact]
    public async Task GIVEN_issued_policy_WHEN_query_pricing_before_endorsement_accepted_THEN_display_old_pricing_in_beforeEndorsement_and_new_pricing_in_afterEndorsement()
    {
        productId productId = await SetUpProductAndScript();

        string policyId = await InitializePolicy(productId);
        policy issuedPolicy = await IssueAndGetPolicy(policyId);
        GetPolicyPricingInput(issuedPolicy).Should().Be(OriginalPricingDataInput);

        string endorsementId = await AddEndorsementAndUpdatePolicyViaUpdate2(policyId, NewPricingDataInput);
        policy policyWithUnacceptedEndorsement = await FindPolicyById(policyId);

        endorsement endorsement = policyWithUnacceptedEndorsement.endorsements!.Where(e => e!.id == endorsementId)!.FirstOrDefault()!;
        string beforeEndorsementPricing = JToken.Parse(endorsement.beforeEndorsement!.pricing!).Value<string>("input")!;
        beforeEndorsementPricing.Should().Be(OriginalPricingDataInput);

        string afterEndorsementPricing = JToken.Parse(endorsement.afterEndorsement!.pricing!).Value<string>("input")!;
        afterEndorsementPricing.Should().Be(NewPricingDataInput);
    }

    async Task<string> InitializePolicy(productId productId)
    {
        initializePolicyInput initializePolicyInput = new()
        {
            productId = new productIdInput{ plan = productId.plan, type = productId.type, version=productId.version },
            fields = $@"{{\""input\"":\""{OriginalPricingDataInput}\""}}"
        };
        return await Policy.Create(initializePolicyInput);
    }

    async Task<policy> IssueAndGetPolicy(string policyId)
    {
        await Policy.Issue(policyId);
        return await FindPolicyById(policyId);
    }

    async Task<policy> IssueThenUpdatePolicyViaUpdate(string policyId, string dataInput)
    {
        await Policy.Issue(policyId);
        updatePolicyInput updatePolicyInput = new() { fields = $@"{{\""input\"":\""{dataInput}\""}}" };

        await UpdatePolicy(policyId, updatePolicyInput);

        return await FindPolicyById(policyId);
    }

    async Task<string> AddEndorsementAndUpdatePolicyViaUpdate2(string policyId, string dataInput)
    {
        string endorsementId = await Policy.Endorsement.Add(policyId);
        updatePolicyInput updatePolicyInput = new() { fields = $@"{{\""input\"":\""{dataInput}\""}}" };
        await Policy.Update(policyId, updatePolicyInput, endorsementId);

        return endorsementId;
    }

    string GetPolicyPricingInput(policy policy) => JToken.Parse(policy.pricing!).Value<string>("input");

    async Task<productId> SetUpProductAndScript()
    {
        productId productId = await Product.Create();
        (string scriptId, _) = await CreatePricingScript();
        await AddScriptToProduct(Product.ProductIdToInput(productId), scriptId);

        return productId;
    }

    async Task<(string, createScriptInput)> CreatePricingScript()
    {
        createScriptInput input = new()
        {
            name = CreateNewGuid(),
            inputSchema = "{}",
            outputSchema = "{}",
            sourceCode = PricingScriptSourceCode,
            type = scriptTypeEnum.PRICING
        };

        return (await Script.Create(input), input);
    }

    Task AddScriptToProduct(productIdInput productId, string scriptId)
    {
        string mutation = new MutationBuilder()
            .addScriptToProduct(new MutationBuilder.addScriptToProductArgs(new addScriptToProductInput { productId = productId, scriptId = scriptId }), new resultBuilder()
                .WithAllFields())
            .Build();

        return _client.SendMutationAndEnsureSuccessAsync(mutation);
    }

    async Task<policy> FindPolicyById(string policyId)
        {
            string query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: new policyWhereInput { id = policyId }), new policiesBuilder()
                    .list(new policyBuilder()
                          .id()
                          .fields()
                          .product(new productBuilder()
                              .productId(new productIdBuilder().WithAllFields()))
                          .pricing()
                          .endorsements(new policyBuilder.endorsementsArgs(), new endorsementBuilder()
                            .id()
                            .status()
                            .type()
                            .reasonOfChange()
                            .beforeEndorsement(new policyBuilder().pricing())
                            .afterEndorsement(new policyBuilder().pricing())
                          )))
                .Build();

            policies response = await _client.SendQueryAsync<policies>(query);
            return response.list!.First()!;
        }

    private async Task UpdatePolicy(string policyId, updatePolicyInput input)
    {
        string mutation = new MutationBuilder()
            .updatePolicy(new MutationBuilder.updatePolicyArgs(input, policyId), new resultBuilder()
                .WithAllFields())
            .Build();

        await _client.SendMutationAsync<createdStatusResult>(mutation);
    }
}
