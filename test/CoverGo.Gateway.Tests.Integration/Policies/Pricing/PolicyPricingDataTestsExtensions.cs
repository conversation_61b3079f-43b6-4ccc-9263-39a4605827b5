using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using CoverGo.GraphQL.Client;
using FluentAssertions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Policies.Pricing;

public static class PolicyPricingDataTestsExtensions
{
    public static async Task<string> CreatePolicy(
        this CoverGoGraphQlClient client,
        initializePolicyInput? policyInput = null)
    {
        if (policyInput == null)
        {
            policyInput = new();
        }

        var createPolicyQuery = new MutationBuilder()
            .initializePolicy(
                new()
                {
                    policy = policyInput with
                    {
                        description = policyInput?.description ?? Guid.NewGuid().ToString()
                    },
                },
                new policyResultBuilder().policyStatus(new policyStatusBuilder().id()))
            .Build();
        var result = await client.SendMutationAsync<policyResult>(createPolicyQuery);
        return result!.policyStatus!.id!;
    }

    public static async Task<string> SetUpPolicy(this CoverGoGraphQlClient client, string policyIssuerNumber = null!)
    {
        string scriptMutation = new MutationBuilder().createScript(new(new createScriptInput
        {
            inputSchema = PolicyPricingTestConsts.Pricing.InputSchema.Escape(),
            outputSchema = "",
            name = "pricing",
            sourceCode = PolicyPricingTestConsts.Pricing.Script.Replace("\n", "\\n").Replace("\r", "\\r").Replace(@"""", @"\"""),
            type = scriptTypeEnum.PRICING
        }), new createdStatusResultBuilder().createdStatus(new createdStatusBuilder().id().ids())).Build();

        var scriptResult = await client.SendMutationAsync<createdStatusResult>(scriptMutation);

        var dataSchemaMutation = new MutationBuilder()
            .createDataSchema(new(new createDataSchemaInput
            {
                schema = PolicyPricingTestConsts.ProductType.BillingSchema.Escape(),
                name = "billing",
                type = "client",
                description = "test"
            }), new createdStatusResultBuilder()
                .status()
                .createdStatus(new createdStatusBuilder().id()))
            .Build();

        var dataSchemaResult = await client.SendMutationAsync<createdStatusResult>(dataSchemaMutation);
        dataSchemaResult.status.Should().Be("success");

        var productTypeId = Guid.NewGuid().ToString();

        var createProducTypeMutation = new MutationBuilder().createProductType(new(new()
        {
            typeId = productTypeId
        }), new resultBuilder().status()).Build();

        var createProductTypeResult = await client.SendMutationAsync<result>(createProducTypeMutation);
        createProductTypeResult.status.Should().Be("success");

        var addDataSchemaMutation = new MutationBuilder().addDataSchemaToProductType(new(new addDataSchemaToProductTypeInput
        {
            dataSchemaId = dataSchemaResult.createdStatus!.id!,
            productTypeId = productTypeId
        }), new resultBuilder().status()).Build();

        var addDataSchemaResult = await client.SendMutationAsync<result>(addDataSchemaMutation);
        addDataSchemaResult.status.Should().Be("success");

        var productId = new productIdInput
        {
            plan = "plan01",
            type = productTypeId,
            version = Guid.NewGuid().ToString(),
        };

        string productMutation = new MutationBuilder().createProduct(new(new createProductInput
        {
            productId = productId,
            representation = PolicyPricingTestConsts.Product.Representation.Escape(),
        }), new productBuilder().productId(new productIdBuilder().plan().type().version())).Build();

        await client.SendMutationAsync<product>(productMutation);

        string addScriptMutation = new MutationBuilder().addScriptToProduct(new(new addScriptToProductInput
        {
            productId = productId,
            scriptId = scriptResult.createdStatus!.id
        }), new resultBuilder().status()).Build();

        await client.SendMutationAsync<result>(addScriptMutation);

        var startDate = DateTime.Now.AddMonths(-1);
        var endDate = DateTime.Now.AddMonths(11);

        string policyMutation = new MutationBuilder().initializePolicy(new(new initializePolicyInput
        {
            productId = productId,
            fields = PolicyPricingTestConsts.Policy.Fields(startDate, endDate).Escape(),
            endDate = endDate,
            startDate = startDate,
            issuerNumber = policyIssuerNumber,
        }), new policyResultBuilder().errors().status().policyStatus(new policyStatusBuilder().id())).Build();

        policyResult policyResult = await client.SendMutationAsync<policyResult>(policyMutation);

        string policyId = policyResult.policyStatus!.id!;
        return policyId;
    }

    public static async Task AddPolicyMember(this CoverGoGraphQlClient client, string policyId, string? endorsementId = null)
    {
        string membersMutation = new MutationBuilder().policyMembersBatch(new(policyId, new policyMembersBatchInput
        {
            create = new List<policyMemberInput?>{
                new policyMemberInput
                {
                    memberId = Guid.NewGuid().ToString(),
                    planId = "plan01",
                    underwritingResult = PolicyMemberUnderwritingResult.APPROVED,
                    startDate = DateTime.Now.AddMonths(-1),
                    fields = "{\n  \"inherited\": false,\n  \"sequenceNo\": \"3121\",\n  \"isUpdated\": true,\n  \"fullName\": \"123123\",\n  \"staffNo\": \"123123\",\n  \"passportNo\": \"123123\",\n  \"hkid\": \"123123\",\n  \"dateOfBirth\": \"2022-07-05\",\n  \"gender\": \"female\",\n  \"memberType\": \"employee\",\n  \"relationshipToEmployee\": null,\n  \"dependentOf\": [],\n  \"claimsSettlementMode\": \"ce\",\n  \"isNeedManualApproval\": false,\n  \"isApproved\": false,\n  \"effectiveDate\": null\n}".Escape()
                }
            }
        }, endorsementId), new resultBuilder().status()).Build();

        await client.SendMutationAsync<result>(membersMutation);
    }

    public static async Task<policy> GetPolicyWithPricing(this CoverGoGraphQlClient client, string policyId)
    {
        string? query = new QueryBuilder()
            .policies(new(where: new()
            {
                id = policyId
            }), new policiesBuilder()
                .list(new policyBuilder().id()
                                         .startDate()
                                         .endDate()
                                         .pricingData(new PricingRootGraphTypeBuilder().pricePerInsureds(new PricingPricePerInsuredGraphTypeBuilder().memberId().amount()))
                                         .endorsements(new policyBuilder.endorsementsArgs(), new endorsementBuilder()
                                            .beforeEndorsement(new policyBuilder().pricingData(new PricingRootGraphTypeBuilder().pricePerInsureds(new PricingPricePerInsuredGraphTypeBuilder().memberId().amount().underwritingResult())))
                                            .afterEndorsement(new policyBuilder().pricingData(new PricingRootGraphTypeBuilder().pricePerInsureds(new PricingPricePerInsuredGraphTypeBuilder().memberId().amount().underwritingResult()))))))
            .Build();

        policies? policies = await client.SendQueryAsync<policies>(query);
        policy? policy = policies.list!.FirstOrDefault();
        return policy!;
    }

    public static async Task<string> AddEndorsement(this CoverGoGraphQlClient client, string policyId)
    {
        string? mutation = new MutationBuilder().addEndorsement(new MutationBuilder.addEndorsementArgs(policyId: policyId, type: "MEMBER_MOVEMENT"), new createdStatusResultBuilder().createdStatus(new createdStatusBuilder().id())).Build();
        createdStatusResult? result = await client.SendMutationAsync<createdStatusResult>(mutation);
        return result.createdStatus!.id!;
    }

    public static async Task AcceptEndorsement(this CoverGoGraphQlClient client, string policyId, string endorsementId)
    {
        string? mutation = new MutationBuilder().acceptEndorsement(new MutationBuilder.acceptEndorsementArgs(policyId, endorsementId), new resultBuilder().status()).Build();
        await client.SendMutationAsync<result>(mutation);
    }

    public static async Task<transaction[]> GetPolicyTransactions(this CoverGoGraphQlClient client, string policyId)
    {
        var query = new QueryBuilder().policies(
            new QueryBuilder.policiesArgs(where: new policyWhereInput() { id = policyId }),
            new policiesBuilder()
                .list(new policyBuilder()
                    .transactions(new(), new transactionBuilder()
                        .id()
                        .amount()
                        .type()
                        .attachments(new attachmentBuilder()
                            .fileName()
                            .path())
                        .createdAt()
                        .currencyCode()
                        .description())))
            .Build();

        var policies = await client.SendQueryAsync<policies>(query);
        var result = policies!.list!.Single()!.transactions!.OrderBy(x => x!.createdAt)!.ToArray();
        return result!;
    }

    public static string Escape(this string input) =>
            input.Replace(@"""", @"\""").Replace("\n", "").Replace("\r", "");

    public static string EscapeJs(this string js) => js.Replace(@"\", @"\\");
}