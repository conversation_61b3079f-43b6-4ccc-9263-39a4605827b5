using System.Linq;
using System.Threading.Tasks;
using CoverGo.GraphQL.Client;
using FluentAssertions;
using Xunit;

namespace CoverGo.Gateway.Tests.Integration.Policies.Pricing
{
    public class PolicyPricingDataTest
    {

        [Fact]
        public async Task GIVEN_policy_with_one_member_added_WHEN_adding_two_more_members_with_endorsement_AND_calculating_pricing_AND_check_pricing_before_and_after_endorsement_THEN_pricing_is_correct()
        {
            using var client = new CoverGoGraphQlClient();
            await client.Authorize();
            var policyId = await client.SetUpPolicy();

            await client.AddPolicyMember(policyId);

            var endorsementId = await client.AddEndorsement(policyId);

            await client.AddPolicyMember(policyId, endorsementId);
            await client.AddPolicyMember(policyId, endorsementId);

            var now = await client.GetPolicyWithPricing(policyId);

            now.pricingData!.pricePerInsureds!.Count.Should().Be(1);

            var endorsement = now.endorsements!.FirstOrDefault();

            var before = endorsement!.beforeEndorsement!;
            var after = endorsement!.afterEndorsement!;

            before.pricingData!.pricePerInsureds!.Count.Should().Be(1);
            after.pricingData!.pricePerInsureds!.Count.Should().Be(3);

            await client.AcceptEndorsement(policyId, endorsementId);

            now = await client.GetPolicyWithPricing(policyId);

            now.pricingData!.pricePerInsureds!.Count.Should().Be(3);

        }
    }
}