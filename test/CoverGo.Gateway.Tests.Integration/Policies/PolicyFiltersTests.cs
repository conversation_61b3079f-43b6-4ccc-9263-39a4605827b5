using CoverGo.Gateway.Client;
using FluentAssertions;
using Fluid.Values;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class PolicyFiltersTests : TestsBase
    {
        public PolicyFiltersTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_filter_by_issuerNumber_in_THEN_returns_this_policy()
        {
            string issuerNumber = CreateNewGuid();
            string policyId = await CreatePolicy(issuerNumber);

            policy? policy = await Filter(new policyWhereInput { issuerNumber_in = new[] { issuerNumber, CreateNewGuid() } });
            policy!.id.Should().Be(policyId);
        }

        [Fact]
        public async Task GIVEN_policy_and_transaction_WHEN_query_policy_transactions_with_filter_THEN_returns_policy_with_correct_transactions()
        {
            string issuerNumber = CreateNewGuid();
            string policyId = await <PERSON>reatePolicy(issuerNumber);

            string endorsementId = CreateNewGuid();

            string transactionId1 = await CreateTransaction(policyId, endorsementId);
            string transactionId2 = await CreateTransaction(policyId, CreateNewGuid());

            policy? policy = await Filter(new policyWhereInput { id = policyId }, new transactionWhereInput { endorsementId = endorsementId });
            policy!.transactions.Should().HaveCount(1);
            transaction? transaction = policy.transactions!.First();
            transaction!.id.Should().Be(transactionId1);
        }

        [Fact]
        public async Task GIVEN_policy_and_renewal_policy_WHEN_filter_by_isRenewal_in_THEN_returns_correct_policy()
        {
            initializePolicyInput input1 = new()
            {
                description = "test policy",
                fields = "{}",
            };
            string policyId = await Policy.Create(input1);

            initializePolicyInput input2 = new()
            {
                description = "renewal policy",
                fields = "{}",
                isRenewal = true,
                previousPolicyIds = new List<string?>
                {
                    policyId
                }
            };

            string renewalPolicyId = await Policy.Create(input2);

            policy? policy = await Filter(new policyWhereInput
            {
                and = new policyWhereInput[]
                {
                    new (){ isRenewal = false  },
                    new (){ id_in = new[]{ policyId, renewalPolicyId } }
                }
            });
            policy? renewalPolicy = await Filter(new policyWhereInput
            {
                and = new policyWhereInput[]
                {
                    new (){ isRenewal = true  },
                    new (){ id_in = new[]{ policyId, renewalPolicyId } }
                }
            });

            policy!.id.Should().Be(policyId);
            renewalPolicy!.id.Should().Be(renewalPolicyId);
        }

        [Fact]
        public async Task GIVEN_an_unissued_policy_and_an_issued_policy_WHEN_filter_by_isIssued_in_THEN_returns_correct_policy()
        {
            productId product = await CreateProductAsync();
            string policyId1 = await CreatePolicyAsync(product);
            string policyId2 = await CreatePolicyAsync(product);
            await Policy.Issue(policyId1);


            policy? unIssuedPolicy = await Filter(new policyWhereInput
            {
                and = new policyWhereInput[]
                {
                    new (){ isIssued = false },
                    new (){ id_in = new[]{ policyId1, policyId2 } }
                }
            });
            policy? issuedPolicy = await Filter(new policyWhereInput
            {
                and = new policyWhereInput[]
                {
                    new (){ isIssued = true  },
                    new (){ id_in = new[]{ policyId1, policyId2 } }
                }
            });

            unIssuedPolicy!.id.Should().Be(policyId2);
            issuedPolicy!.id.Should().Be(policyId1);
        }

        [Fact]
        public async Task GIVEN_a_product_WHEN_filter_policies_by_product_THEN_returns_correct_policies()
        {
            string someValue = CreateNewGuid();

            productId product = await Product.Create(new createProductInput
            {
                productId = new productIdInput
                {
                    type = "gm",
                    plan = CreateNewGuid(),
                    version = CreateNewGuid(),
                },
                fields = @$"{{ productProp: {{ nestedProp: \""{someValue}\"" }} }}"
            });

            string policyId1 = await CreatePolicyAsync(product);
            string policyId2 = await CreatePolicyAsync(product);
            await Policy.Issue(policyId1);

            ICollection<policy?>? policies = await Find(new policyWhereInput
            {
                product = new()
                {
                    fields = new()
                    {
                        path = "fields.productProp.nestedProp",
                        value = new()
                        {
                            stringValue = someValue,
                        },
                        condition = fieldsWhereCondition.EQUALS,
                    }
                }
            });

            policies?.Should().HaveCount(2);
            policies?.Should().Contain((policy) => policy!.id == policyId1);
            policies?.Should().Contain((policy) => policy!.id == policyId2);
        }

        [Fact]
        public async Task GIVEN_renewal_policy_WHEN_filter_by_previousPolicyIds_contains_THEN_returns_correct_policy()
        {
            initializePolicyInput input1 = new()
            {
                description = "test policy",
                fields = "{}",
            };
            string policyId = await Policy.Create(input1);

            initializePolicyInput input2 = new()
            {
                description = "renewal policy",
                fields = "{}",
                isRenewal = true,
                previousPolicyIds = new List<string?>
                {
                    policyId
                }
            };

            string renewalPolicyId = await Policy.Create(input2);

            policy? renewalPolicy = await Filter(new policyWhereInput
            {
                and = new policyWhereInput[]
                {
                    new (){ isRenewal = true  },
                    new (){ previousPolicyIds_contains =  policyId }
                }
            });

            renewalPolicy!.id.Should().Be(renewalPolicyId);
        }

        [Fact]
        public async Task GIVEN_renewal_policy_WHEN_filter_by_previousPolicyIds_contains_every_THEN_returns_correct_policy()
        {
            initializePolicyInput input1 = new()
            {
                description = "test policy",
                fields = "{}",
            };
            string policyId = await Policy.Create(input1);

            initializePolicyInput input2 = new()
            {
                description = "renewal policy 1",
                fields = "{}",
                isRenewal = true,
                previousPolicyIds = new List<string?>
                {
                    policyId
                }
            };

            string renewalPolicyId1 = await Policy.Create(input2);

            initializePolicyInput input3 = new()
            {
                description = "renewal policy 2",
                fields = "{}",
                isRenewal = true,
                previousPolicyIds = new List<string?>
                {
                    policyId, renewalPolicyId1
                }
            };

            string renewalPolicyId2 = await Policy.Create(input3);

            policy? renewalPolicy = await Filter(new policyWhereInput
            {
                and = new policyWhereInput[]
                {
                    new (){ isRenewal = true  },
                    new (){ previousPolicyIds_contains_every =  new List<string?>{ policyId, renewalPolicyId1 } }
                }
            });

            renewalPolicy!.id.Should().Be(renewalPolicyId2);
        }

        [Fact]
        public async Task GIVEN_renewal_policy_WHEN_filter_by_previousPolicyIds_contains_some_THEN_returns_correct_policy()
        {
            initializePolicyInput input1 = new()
            {
                description = "test policy",
                fields = "{}",
            };
            string policyId = await Policy.Create(input1);

            initializePolicyInput input2 = new()
            {
                description = "renewal policy 1",
                fields = "{}",
                isRenewal = true,
                previousPolicyIds = new List<string?>
                {
                    policyId
                }
            };

            string renewalPolicyId1 = await Policy.Create(input2);

            initializePolicyInput input3 = new()
            {
                description = "renewal policy 2",
                fields = "{}",
                isRenewal = true,
                previousPolicyIds = new List<string?>
                {
                    policyId, renewalPolicyId1
                }
            };

            string renewalPolicyId2 = await Policy.Create(input3);

            policy? renewalPolicy = await Filter(new policyWhereInput
            {
                and = new policyWhereInput[]
                {
                    new (){ isRenewal = true  },
                    new (){ previousPolicyIds_contains_some =  new List<string?>{  renewalPolicyId1, CreateNewGuid() } }
                }
            });

            renewalPolicy!.id.Should().Be(renewalPolicyId2);
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_filter_by_holder_name_contains_with_random_value_THEN_returns_nothing()
        {
            string name = CreateNewGuid();
            string holderId = await Entity.CreateIndividual(new createIndividualInput { englishFirstName = name + CreateNewGuid() });
            initializePolicyInput input = new()
            {
                description = "test policy",
                holderId = holderId
            };
            await Policy.Create(input);

            policy? policy = await Filter(new policyWhereInput { holder = new entityWhereInput { name_contains = CreateNewGuid() } });

            policy.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_filter_by_holder_name_contains_THEN_returns_this_policy()
        {
            string name = CreateNewGuid();
            string holderId = await Entity.CreateIndividual(new createIndividualInput { englishFirstName = name + CreateNewGuid() });
            initializePolicyInput input = new()
            {
                description = "test policy",
                holderId = holderId
            };
            string policyId = await Policy.Create(input);

            policy? policy = await Filter(new policyWhereInput { holder = new entityWhereInput { name_contains = name } });
            policy!.id.Should().Be(policyId);
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_filter_by_fields_equals_with_random_value_THEN_returns_nothing()
        {
            initializePolicyInput input = new()
            {
                description = "test policy",
                fields = new { foobar = CreateNewGuid() }.ToEscapedJsonString()
            };
            await Policy.Create(input);

            policyWhereInput where = new()
            {
                fields = new fieldsWhereInput
                {
                    path = "fields.foobar",
                    condition = fieldsWhereCondition.EQUALS,
                    value = new scalarValueInput { stringValue = CreateNewGuid() }
                },
            };

            policy? policy = await Filter(where);

            policy.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_filter_by_fields_equals_THEN_returns_this_policy()
        {
            string searchValue = CreateNewGuid();
            initializePolicyInput input = new()
            {
                description = "test policy",
                fields = new { foobar = searchValue }.ToEscapedJsonString()
            };
            string policyId = await Policy.Create(input);

            policyWhereInput where = new()
            {
                fields = new fieldsWhereInput
                {
                    path = "fields.foobar",
                    condition = fieldsWhereCondition.EQUALS,
                    value = new scalarValueInput { stringValue = searchValue }
                },
            };

            policy? policy = await Filter(where);

            policy!.id.Should().Be(policyId);
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_filter_by_fields_string_contains_THEN_returns_this_policy()
        {
            string searchValue = CreateNewGuid();
            initializePolicyInput input = new()
            {
                description = "test policy",
                fields = new { foobar = searchValue + CreateNewGuid() }.ToEscapedJsonString()
            };
            string policyId = await Policy.Create(input);

            policyWhereInput where = new()
            {
                fields = new fieldsWhereInput
                {
                    path = "fields.foobar",
                    condition = fieldsWhereCondition.STRING_CONTAINS,
                    value = new scalarValueInput { stringValue = searchValue }
                },
            };

            policy? policy = await Filter(where);

            policy!.id.Should().Be(policyId);
        }

        [Fact]
        public async Task GIVEN_policy_with_fields_WHEN_apply_and_filter_to_two_fields_properties_THEN_returns_this_policy()
        {
            string searchValue1 = CreateNewGuid();
            string searchValue2 = CreateNewGuid();
            initializePolicyInput input = new()
            {
                description = "test policy",
                fields = new
                {
                    foobar = searchValue1 + CreateNewGuid(),
                    barfoo = searchValue2 + CreateNewGuid()
                }.ToEscapedJsonString()
            };
            string policyId = await Policy.Create(input);

            policyWhereInput where = new()
            {
                fields = new fieldsWhereInput
                {
                    and = new List<fieldsWhereInput?>{
                        new()
                        {
                            path = "fields.foobar",
                            condition = fieldsWhereCondition.STRING_CONTAINS,
                            value = new scalarValueInput { stringValue = searchValue1 }
                        },
                        new()
                        {
                            path = "fields.barfoo",
                            condition = fieldsWhereCondition.STRING_CONTAINS,
                            value = new scalarValueInput { stringValue = searchValue2 }
                        },
                    }
                },
            };

            policy? policy = await Filter(where);

            policy!.id.Should().Be(policyId);
        }

        [Fact]
        public async Task GIVEN_two_policies_with_fields_WHEN_apply_or_filter_to_two_fields_properties_THEN_returns_two_policies()
        {
            string searchValue1 = CreateNewGuid();
            string searchValue2 = CreateNewGuid();
            initializePolicyInput input1 = new()
            {
                description = "test policy 1",
                fields = new
                {
                    foobar = searchValue1 + CreateNewGuid()
                }.ToEscapedJsonString()
            };

            initializePolicyInput input2 = new()
            {
                description = "test policy 2",
                fields = new
                {
                    barfoo = searchValue2 + CreateNewGuid()
                }.ToEscapedJsonString()
            };

            string policy1Id = await Policy.Create(input1);
            string policy2Id = await Policy.Create(input2);

            policyWhereInput where = new()
            {
                fields = new fieldsWhereInput
                {
                    or = new List<fieldsWhereInput?>{
                        new()
                        {
                            path = "fields.foobar",
                            condition = fieldsWhereCondition.STRING_CONTAINS,
                            value = new scalarValueInput { stringValue = searchValue1 }
                        },
                        new()
                        {
                            path = "fields.barfoo",
                            condition = fieldsWhereCondition.STRING_CONTAINS,
                            value = new scalarValueInput { stringValue = searchValue2 }
                        },
                    }
                },
            };

            ICollection<policy?>? policies = await Find(where);
            policies!.Count.Should().Be(2);

            policies.FirstOrDefault(p => p!.id == policy1Id).Should().NotBeNull();
            policies.FirstOrDefault(p => p!.id == policy2Id).Should().NotBeNull();
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_filter_by_referralCode_THEN_returns_this_policy()
        {
            string internalCode = CreateNewGuid();
            await Entity.CreateInternal(new createInternalInput { internalCode = internalCode });

            string policyId = await Policy.Create(new initializePolicyInput { referralCode = internalCode });

            policy? policy = await Filter(new policyWhereInput { referralCode = internalCode });
            policy!.id.Should().Be(policyId);
        }

        [Fact]
        public async Task GIVEN_policy_WHEN_filter_endorsements_by_member_movement_version_in_THEN_returns_this_policy()
        {
            // Arrange
            string policyId = await Policy.Create();
            string endorsementType = "MEMBER_MOVEMENT";
            await Policy.Endorsement.Add(policyId, type: endorsementType, memberMovementVersions: memberMovementVersions.MEMBER_MOVEMENT_V1);
            await Policy.Endorsement.Add(policyId, type: endorsementType, memberMovementVersions: memberMovementVersions.MEMBER_MOVEMENT_V2);

            policyWhereInput where = new policyWhereInput
            {
                endorsement = new policyEndorsementWhereInput
                {
                    memberMovementVersion_in = new List<memberMovementVersions?> { memberMovementVersions.MEMBER_MOVEMENT_V2 }
                }
            };
            string query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: where), new policiesBuilder()
                    .list(new policyBuilder()
                       .id()
                       .endorsements(new policyBuilder.endorsementsArgs(), new endorsementBuilder().memberMovementVersion())))
               .Build();

            // Act
            policies response = await _client.SendQueryAsync<policies>(query);

            // Assert
            response.Should().NotBeNull();
            response.list.Should().NotBeNullOrEmpty();
            ICollection<policy?> policies = response.list!;
            policies.Should().AllSatisfy(p =>
            {
                p.Should().NotBeNull();
                p!.endorsements.Should().NotBeNullOrEmpty();
                p.endorsements.Should().Contain(e => e!.memberMovementVersion == memberMovementVersions.MEMBER_MOVEMENT_V2);
            });
        }

        Task<string> CreatePolicy(string issuerNumber)
        {
            initializePolicyInput input = new()
            {
                description = "test policy",
                issuerNumber = issuerNumber
            };
            return Policy.Create(input);
        }

        Task<string> CreateTransaction(string policyId, string endorsementId)
        {
            createTransactionInput input = new()
            {
                policyId = policyId,
                endorsementId = endorsementId
            };

            return Transaction.Create(input);
        }

        async Task<policy?> Filter(policyWhereInput where, transactionWhereInput transactionWhereInput = null)
        {
            ICollection<policy?>? policies = await Find(where, transactionWhereInput);
            return policies!.SingleOrDefault();
        }

        async Task<ICollection<policy?>?> Find(policyWhereInput where, transactionWhereInput transactionWhereInput = null)
        {
            string query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: where), new policiesBuilder()
                    .list(new policyBuilder()
                        .id()
                        .transactions(new policyBuilder.transactionsArgs { where = transactionWhereInput }, new transactionBuilder().id().endorsementId())))
                .Build();

            policies response = await _client.SendQueryAsync<policies>(query);
            return response.list;
        }

        private Task<productId> CreateProductAsync() => Product.Create(new createProductInput
        {
            productId = new productIdInput
            {
                type = "gm",
                plan = CreateNewGuid(),
                version = CreateNewGuid()
            }
        });

        private Task<string> CreatePolicyAsync(productId productId, string? p400 = null)
        {
            initializePolicyInput input = new()
            {
                description = CreateNewGuid(),
                fieldsSchemaId = CreateNewGuid(),
                fields = p400 != null ? $"{{ \"p400PolicyNumber\": \"{p400}\" }}".Escape() : null,
                productId = new productIdInput
                {
                    plan = productId.plan,
                    version = productId.version,
                    type = productId.type
                }
            };
            return Policy.Create(input);
        }
    }
}