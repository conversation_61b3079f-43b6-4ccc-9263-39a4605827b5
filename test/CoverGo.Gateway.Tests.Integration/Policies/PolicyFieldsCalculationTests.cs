﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class PolicyFieldsCalculationTests : TestsBase
    {
        public PolicyFieldsCalculationTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact(Skip = "need to be fixed")]
        public async Task GIVEN_policy_WHEN_query_pricing_THEN_returns_pricing()
        {
            (productId productId, string expectedOutput) = await Product.CreateWithScript(scriptTypeEnum.PRICING);

            const string fields = "{ value = 5 }";

            policy policy = await CreatePolicy(fields, productId);

            policy.pricing.Should().Be(string.Format(expectedOutput, fields));
        }

        async Task<policy> CreatePolicy(string fields, productId productId)
        {
            initializePolicyInput input = new()
            {
                description = "test policy",
                productId = Product.ProductIdToInput(productId),
                fields = fields,
            };
            string policyId = await Policy.Create(input);

            return await FindById(policyId);
        }

        async Task<policy> FindById(string policyId)
        {
            string query = new QueryBuilder()
                .policies(new QueryBuilder.policiesArgs(where: new policyWhereInput { id = policyId }), new policiesBuilder()
                    .list(new policyBuilder()
                        .pricing()))
                .Build();

            policies response = await _client.SendQueryAsync<policies>(query);
            return response.list!.First()!;
        }
    }
}