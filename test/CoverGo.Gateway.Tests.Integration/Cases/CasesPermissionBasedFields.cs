using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using FluentAssertions;
using GraphQL.Client.Http;
using System;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Cases
{
    public class CasesPermissionBasedFields : TestsBase
    {
        public CasesPermissionBasedFields(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_permissionSchema_set_WHEN_fetch_case_THEN_returns_case_with_allowed_field()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "case",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape()
            });

            string caseId = await Case.Create(new createCaseInput
            {
                name = CreateNewGuid(),
                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            const string expectedFields = @"{""field1"":""Value1""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readCases", caseId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { caseId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);

            @case? @case = await new Case(client).FindById(caseId);
            @case.Should().NotBeNull();

            @case!.fields!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_all_fields_allowed_set_WHEN_fetch_case_THEN_returns_case_with_allowed_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "case",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema").Escape()
            });

            string caseId = await Case.Create(new createCaseInput
            {
                name = CreateNewGuid(),

                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            const string expectedFields = @"{""field1"":""Value1"",""field2"":""Value2""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readCases", caseId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { caseId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);

            @case? @case = await new Case(client).FindById(caseId);
            @case.Should().NotBeNull();

            @case!.fields!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Theory]
        [InlineData("updateCases")]
        [InlineData("writeCases")]
        public async Task GIVEN_permissionSchema_set_WHEN_patch_case_fields_THEN_updates_case_fields(string casesPermissionName)
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "case",
                actionType = permissionSchemaActionType.WRITE,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape()
            });

            string caseId = await Case.Create(new createCaseInput
            {
                name = CreateNewGuid(),

                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            string fieldsPatch = @"
[
 {
  ""op"": ""replace"",
  ""path"": ""/field1"",
  ""value"": ""Value3""
 }
]".Escape();

            const string expectedFields = "{\"field1\":\"Value3\",\"field2\":\"Value2\"}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, casesPermissionName, caseId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { caseId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            await new Case(client).Update(caseId, new updateCaseInput { fieldsPatch = fieldsPatch });

            @case? @case = await Case.FindById(caseId);
            @case.Should().NotBeNull();

            @case!.fields!.Should().Be(expectedFields);
        }

        [Theory]
        [InlineData("updateCases")]
        [InlineData("writeCases")]
        public async Task GIVEN_permissionSchema_set_WHEN_patch_case_fields_without_permission_THEN_fails(string casesPermissionName)
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "case",
                actionType = permissionSchemaActionType.WRITE,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape()
            });

            string caseId = await Case.Create(new createCaseInput
            {
                name = CreateNewGuid(),

                fields = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            string fieldsPatch = @"
[
 {
  ""op"": ""replace"",
  ""path"": ""/field2"",
  ""value"": ""Value3""
 }
]".Escape();

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, casesPermissionName, caseId);

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { caseId });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            Func<Task> method = () => new Case(client).Update(caseId, new updateCaseInput { fieldsPatch = fieldsPatch });

            await method.Should().ThrowAsync<Exception>();
        }
    }
}
