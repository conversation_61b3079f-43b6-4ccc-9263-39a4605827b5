﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class ProposalAndOfferFiltersTests : TestsBase
    {
        public ProposalAndOfferFiltersTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_proposal_and_offer_WHEN_filter_offer_status_on_both_THEN_returns_this_offer()
        {
            string status = CreateNewGuid();

            (string caseId, string proposalId, string offerId) = await SetupOffers(status);

            @case @case = await SearchCaseProposalOffer(status);

            @case.id.Should().Be(caseId);
            @case.proposals!.Single(p => p!.basket != null)!.id.Should().Be(proposalId);
            @case.proposals!.Single(p => p!.basket != null)!.basket!.Single()!.id.Should().Be(offerId);
        }

        async Task<@case> SearchCaseProposalOffer(string status)
        {
            offerWhere offerWhere = new() { status = status };
            proposalWhere proposalWhere = new() { offers_contains = new offerWhere { status = status } };
            caseWhere caseWhere = new() { proposals_contains = new proposalWhere { offers_contains = new offerWhere { status = status } } };

            string query = new QueryBuilder()
                .cases(new QueryBuilder.casesArgs(caseWhere), new casesBuilder()
                    .list(new caseBuilder()
                        .id()
                        .proposals(new caseBuilder.proposalsArgs(proposalWhere), new proposalBuilder()
                            .id()
                            .status()
                            .basket(new proposalBuilder.basketArgs(offerWhere), new offerBuilder()
                                .id()
                                .status()
                                .fields()
                                .product(new productBuilder()
                                    .productId(new productIdBuilder()
                                        .plan()
                                        .type()
                                        .version())
                                .representation())))))
                .Build();

            cases response = await _client.SendQueryAsync<cases>(query);
            return response.list!.First()!;
        }

        async Task<(string caseId, string proposalId, string offerId)> SetupOffers(string status)
        {
            string caseId = await Case.Create(new createCaseInput { name = "test case", });

            string proposal1Id = await CreateProposal(caseId);
            string proposal2Id = await CreateProposal(caseId);

            await CreateOffer(caseId, proposal1Id, CreateNewGuid());
            await CreateOffer(caseId, proposal1Id, CreateNewGuid());
            string offerId = await CreateOffer(caseId, proposal1Id, status);
            await CreateOffer(caseId, proposal2Id, CreateNewGuid());
            await CreateOffer(caseId, proposal2Id, CreateNewGuid());
            return (caseId, proposal1Id, offerId);
        }

        Task<string> CreateOffer(string caseId, string proposalId, string status) =>
            Offer.Create(caseId, proposalId, new addOfferInput { status = status });

        Task<string> CreateProposal(string caseId) =>
            Proposal.Create(caseId, new addProposalInput { name = "new name" });
    }
}