﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class OfferCrudTests : TestsBase
    {
        private static readonly MongoClient _mongoClient = new(Environment.GetEnvironmentVariable("DATABASE_CONNECT_STRING") ?? "mongodb://localhost:27017/?readPreference=primary&appname=MongoDB%20Compass&ssl=false");

        public OfferCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_offers_WHEN_create_without_contract_and_schema_THEN_returns_id()
        {
            addOfferInput addOfferInput = new() { policyId = "new policy Id" };

            (string _, string _, string offerId) = await CreateOffer(addOfferInput);

            offerId.Should().NotBeNullOrWhiteSpace();
        }

        [Fact]
        public async Task GIVEN_offer_created_without_fields_and_schemas_WHEN_request_this_offer_THEN_returns_offer()
        {
            addOfferInput addOfferInput = new() { policyNumber = "new policy number" };

            (string caseId, _, _) = await CreateOffer(addOfferInput);

            offer offer = await SearchOfferByCaseId(caseId);

            offer.policyNumber.Should().BeEquivalentTo(addOfferInput.policyNumber);
        }

        [Fact]
        public async Task GIVEN_offer_created_without_fields_and_schemas_WHEN_update_this_offer_THEN_succeed_and_updated()
        {
            addOfferInput addOfferInput = new();

            (string caseId, string proposalId, string offerId) = await CreateOffer(addOfferInput);

            updateOfferInput updateOfferInput = new() { policyNumber = "updated policy number" };

            await UpdateOffer(caseId, proposalId, offerId, updateOfferInput);

            offer offer = await SearchOfferByCaseId(caseId);

            offer.policyNumber.Should().BeEquivalentTo(updateOfferInput.policyNumber);
        }

        [Fact]
        public async Task GIVEN_offers_WHEN_create_with_fields_and_schemas_THEN_returns_id()
        {
            addOfferInput addOfferInput = new()
            {
                fields = "{}",
                fieldsSchemaId = "new schema id",
                contractId = "new contract id",
                underwriting = "new underwriting",
                pricing = "new pricing"
            };

            (string _, string _, string offerId) = await CreateOffer(addOfferInput);

            offerId.Should().NotBeNullOrWhiteSpace();
        }

        [Fact(Skip = "need to be fixed")]
        public async Task GIVEN_offer_created_with_fields_and_schemas_WHEN_request_this_offer_THEN_returns_offer()
        {
            (string schemaId, createDataSchemaInput createDataSchemaInput) = await DataSchema.Create();

            addOfferInput addOfferInput = new()
            {
                fields = "{}",
                fieldsSchemaId = schemaId,
                underwriting = "new underwriting",
                pricing = "new pricing"
            };

            (string caseId, _, _) = await CreateOffer(addOfferInput);

            offer offer = await SearchOfferByCaseId(caseId);

            offer.ShouldHaveSameValuesAsInput(addOfferInput);
            offer.fieldsSchema!.id.Should().BeEquivalentTo(schemaId);
            offer.ShouldHaveSameValuesAsInput(createDataSchemaInput);
        }

        [Fact(Skip = "need to be fixed")]
        public async Task GIVEN_offer_created_with_fields_and_schemas_WHEN_update_this_offer_THEN_succeed_and_updated()
        {
            addOfferInput addOfferInput = new()
            {
                fields = "{}",
                underwriting = "new underwriting",
                pricing = "new pricing"
            };

            (string caseId, string proposalId, string offerId) = await CreateOffer(addOfferInput);

            (string schemaId, createDataSchemaInput createDataSchemaInput) = await DataSchema.Create();

            updateOfferInput updateOfferInput = new()
            {
                fields = "{ value = 5 }",
                fieldsSchemaId = schemaId,
                underwriting = "updated underwriting",
                pricing = "updated pricing"
            };

            await UpdateOffer(caseId, proposalId, offerId, updateOfferInput);

            offer offer = await SearchOfferByCaseId(caseId);

            offer.ShouldHaveSameValuesAsInput(updateOfferInput);

            offer.fieldsSchema!.id.Should().BeEquivalentTo(schemaId);
            offer.fieldsSchema.ShouldHaveSameValuesAsInput(createDataSchemaInput);
        }

        [Fact]
        public async Task GIVEN_case_without_fields2_in_offer_WHEN_queried_THEN_offer_fields_content_should_be_fields()
        {
            BsonDocument caseDocument = BsonDocument.Parse(
                @"{""_id"":""798af5ee-4943-42d6-ab23-6ed7e605a09c"",
                ""caseNumber"":""142d6dde-ac4b-4289-8e87-c4089ca46f41"",
                ""name"":""99e3f4a4-4f14-40bb-8640-080113ec3770"",
                ""description"":""adab1183-a60e-4a05-a43b-1fd3f661382d"",
                ""facts"":[],
                ""notes"":[],
                ""proposals"":[{""_id"":""54b99482-8863-493f-b7ca-7d965d49f6de"",
                ""caseId"":""798af5ee-4943-42d6-ab23-6ed7e605a09c"",
                ""name"":""a67398dd-4b54-43d3-b414-b7803900cfb6"",
                ""proposalNumber"":""VZOF1B"",
                ""isIssued"":false,
                ""notes"":[],
                ""basket"":[{""_id"":""fe2b0db8-b7aa-40ad-ad4c-f1283798e0db"",
                ""offerNumber"":""N6WI3Y"",
                ""isPremiumOverridden"":false,
                ""clauses"":[],
                ""jackets"":[],
                ""facts"":[],
                ""events"":[{""value"":{""offerId"":""fe2b0db8-b7aa-40ad-ad4c-f1283798e0db"",
                ""proposalId"":""54b99482-8863-493f-b7ca-7d965d49f6de"",
                ""offerNumber"":""N6WI3Y"",
                ""isPremiumOverridden"":false,
                ""fields"":""{\""msg\"":\""i am from fields1\""}""},
                ""relatedId"":""798af5ee-4943-42d6-ab23-6ed7e605a09c"",
                ""type"":""addOffer""}],
                ""commissions"":[],
                ""fields"":""{\""msg\"":\""i am from fields1\""}""}],
                ""totalPrice"":{""amount"":0,
                ""paymentFrequency"":""OneTime""},
                ""policyIds"":[],
                ""renewalHistory"":{""renewalCount"":0},
                ""isRejected"":false}],
                ""stakeholders"":[],
                ""beneficiaryEligibilities"":[],
                ""paymentInfos"":[]}");
            await InsertDocumentToCaseCollection(caseDocument);

            string idOfSeededCaseWithoutFields2 = "798af5ee-4943-42d6-ab23-6ed7e605a09c";
            offer offer = await SearchOfferByCaseId(idOfSeededCaseWithoutFields2);

            offer.fields.Should().Be("{\"msg\":\"i am from fields1\"}");
        }

        [Fact]
        public async Task GIVEN_case_with_fields2_only_in_offer_WHEN_queried_THEN_offer_fields_content_should_be_fields2()
        {
            BsonDocument caseDocument = BsonDocument.Parse(
                @"{""_id"":""cf104661-9f17-4ada-8651-ad7d91f83e11"",
                ""caseNumber"":""004c6262-3fcc-481a-bdcc-a44ff571ce98"",
                ""name"":""3720b7e3-d1ac-4886-b72a-da8d986a8c0f"",
                ""description"":""227b7476-7ce4-4670-9492-576087d039c9"",
                ""facts"":[],
                ""notes"":[],
                ""proposals"":[{""_id"":""0c69c879-956e-4a04-9af5-c830d67282f0"",
                ""caseId"":""cf104661-9f17-4ada-8651-ad7d91f83e11"",
                ""name"":""46fbbc33-45a6-46e4-adf3-59d0a02af751"",
                ""proposalNumber"":""HX7J6I"",
                ""isIssued"":false,
                ""notes"":[],
                ""basket"":[{""_id"":""f53fd86d-64a4-413f-a643-bc94bc4bad23"",
                ""offerNumber"":""OUXCS1"",
                ""isPremiumOverridden"":false,
                ""clauses"":[],
                ""jackets"":[],
                ""facts"":[],
                ""events"":[{""value"":{""offerId"":""f53fd86d-64a4-413f-a643-bc94bc4bad23"",
                ""proposalId"":""0c69c879-956e-4a04-9af5-c830d67282f0"",
                ""offerNumber"":""OUXCS1"",
                ""isPremiumOverridden"":false,
                ""fields"":""{\""msg\"":\""i am from fields2\""}""},
                ""relatedId"":""cf104661-9f17-4ada-8651-ad7d91f83e11"",
                ""type"":""addOffer""}],
                ""commissions"":[],
                ""fields2"":{""msg"":""i am from fields2""}}],
                ""totalPrice"":{""amount"":0,
                ""paymentFrequency"":""OneTime""},
                ""policyIds"":[],
                ""renewalHistory"":{""renewalCount"":0},
                ""isRejected"":false}],
                ""stakeholders"":[],
                ""beneficiaryEligibilities"":[],
                ""paymentInfos"":[]}");

            await InsertDocumentToCaseCollection(caseDocument);

            string idOfSeededCaseWithFields2Only = "cf104661-9f17-4ada-8651-ad7d91f83e11";
            offer offer = await SearchOfferByCaseId(idOfSeededCaseWithFields2Only);

            offer.fields.Should().Be("{\"msg\":\"i am from fields2\"}");
        }

        [Fact]
        public async Task GIVEN_case_with_fields_and_fields2_in_offer_WHEN_queried_THEN_offer_fields_content_should_be_fields2()
        {
            BsonDocument caseDocument = BsonDocument.Parse(
                @"{""_id"":""813d85f1-acf4-4910-930b-258e3fd0b901"",
                ""caseNumber"":""142d6dde-ac4b-4289-8e87-c4089ca46f41"",
                ""name"":""99e3f4a4-4f14-40bb-8640-080113ec3770"",
                ""description"":""adab1183-a60e-4a05-a43b-1fd3f661382d"",
                ""facts"":[],
                ""notes"":[],
                ""proposals"":[{""_id"":""54b99482-8863-493f-b7ca-7d965d49f6de"",
                ""caseId"":""813d85f1-acf4-4910-930b-258e3fd0b901"",
                ""name"":""a67398dd-4b54-43d3-b414-b7803900cfb6"",
                ""proposalNumber"":""VZOF1B"",
                ""isIssued"":false,
                ""notes"":[],
                ""basket"":[{""_id"":""fe2b0db8-b7aa-40ad-ad4c-f1283798e0db"",
                ""offerNumber"":""N6WI3Y"",
                ""isPremiumOverridden"":false,
                ""clauses"":[],
                ""jackets"":[],
                ""facts"":[],
                ""events"":[{""value"":{""offerId"":""fe2b0db8-b7aa-40ad-ad4c-f1283798e0db"",
                ""proposalId"":""54b99482-8863-493f-b7ca-7d965d49f6de"",
                ""offerNumber"":""N6WI3Y"",
                ""isPremiumOverridden"":false,
                ""fields"":""{\""msg\"":\""i am from fields1\""}""},
                ""relatedId"":""813d85f1-acf4-4910-930b-258e3fd0b901"",
                ""type"":""addOffer""},
                {""value"":{""proposalId"":""54b99482-8863-493f-b7ca-7d965d49f6de"",
                ""offerId"":""fe2b0db8-b7aa-40ad-ad4c-f1283798e0db"",
                ""isStatusChanged"":false,
                ""isOfferNumberChanged"":false,
                ""isPolicyNumberChanged"":false,
                ""isValuesChanged"":false,
                ""isProductIdChanged"":false,
                ""isPremiumChanged"":false,
                ""isStartDateChanged"":false,
                ""isEndDateChanged"":false,
                ""isPricingChanged"":false,
                ""isUnderwritingChanged"":false,
                ""fields"":""{\""msg\"":\""i am from fields2\""}"",
                ""isFieldsChanged"":true,
                ""isFieldsSchemaIdChanged"":false},
                ""relatedId"":""813d85f1-acf4-4910-930b-258e3fd0b901"",
                ""type"":""updateOffer""}],
                ""commissions"":[],
                ""fields"":""{\""msg\"":\""i am from fields1\""}"",
                ""fields2"":{""msg"":""i am from fields2""}}],
                ""totalPrice"":{""amount"":0,
                ""paymentFrequency"":""OneTime""},
                ""policyIds"":[],
                ""renewalHistory"":{""renewalCount"":0},
                ""isRejected"":false}],
                ""stakeholders"":[],
                ""beneficiaryEligibilities"":[],
                ""paymentInfos"":[]}");
            await InsertDocumentToCaseCollection(caseDocument);

            string idOfSeededCaseWithFieldsAndFields2 = "813d85f1-acf4-4910-930b-258e3fd0b901";
            offer offer = await SearchOfferByCaseId(idOfSeededCaseWithFieldsAndFields2);

            offer.fields.Should().Be("{\"msg\":\"i am from fields2\"}");
        }

        [Fact]
        public async Task GIVEN_case_with_proposal_WHEN_adding_offer_AND_adding_product_tree_id_THEN_it_is_persisted()
        {
            string? productTreeId = CreateNewGuid();
            addOfferInput addOfferInput = new()
            {
                productTreeId = productTreeId
            };
            (string caseId, string _, string _) = await CreateOffer(addOfferInput);
            offer offer = await SearchOfferByCaseId(caseId);
            offer.productTreeId.Should().Be(productTreeId);
        }

        [Fact]
        public async Task GIVEN_case_with_proposal_WHEN_adding_offer_AND_updating_product_tree_id_THEN_it_is_updated()
        {
            string? productTreeId = CreateNewGuid();
            addOfferInput addOfferInput = new()
            {
                productTreeId = productTreeId
            };
            (string caseId, string proposalId, string offerId) = await CreateOffer(addOfferInput);

            string? updatedProductTreeId = CreateNewGuid();

            updateOfferInput updateOfferInput = new() { productTreeId = updatedProductTreeId };

            await UpdateOffer(caseId, proposalId, offerId, updateOfferInput);

            offer offer = await SearchOfferByCaseId(caseId);
            offer.productTreeId.Should().Be(updatedProductTreeId);
        }

        [Fact]
        public async Task
            GIVEN_case_with_proposal_WHEN_adding_offer_AND_adding_product_tree_record_id_THEN_it_is_persisted()
        {
            string? productTreeRecordId = CreateNewGuid();
            productTreeRecordInput productTreeRecord = new() { type = "test", recordId = CreateNewGuid() };
            addOfferInput addOfferInput = new() { productTreeRecords = new List<productTreeRecordInput> { productTreeRecord }! };
            (string caseId, string _, string _) = await CreateOffer(addOfferInput);
            offer offer = await SearchOfferByCaseId(caseId);

            offer.productTreeRecords!.First()!.type.Should().Be(productTreeRecord.type);
            offer.productTreeRecords!.First()!.type.Should().Be(productTreeRecord.type);

        }

        async Task InsertDocumentToCaseCollection(BsonDocument doc)
        {
            var collection = _mongoClient.GetDatabase("cases").GetCollection<BsonDocument>("covergo-cases");
            await collection.InsertOneAsync(doc);
        }

        async Task<(string caseId, string proposalId, string offerId)> CreateOffer(addOfferInput input)
        {
            string caseId = await Case.Create(new createCaseInput { name = "test case", });
            string proposalId = await CreateProposal(caseId);
            string offerId = await Offer.Create(caseId, proposalId, input);
            return (caseId, proposalId, offerId);
        }

        async Task UpdateOffer(string caseId, string proposalId, string offerId, updateOfferInput input)
        {
            string mutation = new MutationBuilder()
                .updateOfferOfProposal(new MutationBuilder.updateOfferOfProposalArgs(caseId, proposalId, offerId, input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        Task<string> CreateProposal(string caseId) =>
            Proposal.Create(caseId, new addProposalInput { name = "new name" });

        async Task<offer> SearchOfferByCaseId(string id)
        {
            string query = new QueryBuilder()
                .cases(new QueryBuilder.casesArgs(new caseWhere { id = id }), new casesBuilder()
                    .list(new caseBuilder()
                        .id()
                        .proposals(new caseBuilder.proposalsArgs(), new proposalBuilder()
                            .id()
                            .basket(new proposalBuilder.basketArgs(), new offerBuilder()
                                .id()
                                .policyNumber()
                                .fields()
                                .underwriting()
                                .pricing()
                                .fieldsSchema(new dataSchemaBuilder()
                                    .id()
                                    .name()
                                    .description()
                                    .schema()
                                    .type()
                                    .standard(new dataSchemaStandardBuilder()
                                        .type()
                                        .version()
                                    )
                                )
                                .productTreeId()
                                .productTreeRecords(
                                    new productTreeRecordBuilder().WithAllFields()
                                    )
                            ))))
                    .Build();

            cases response = await _client.SendQueryAsync<cases>(query);
            return response.list!.First()!.proposals!.First()!.basket!.First()!;
        }
    }
}