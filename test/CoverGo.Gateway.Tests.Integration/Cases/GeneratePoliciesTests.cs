using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Cases
{
    public class GeneratePoliciesTests : TestsBase
    {
        public GeneratePoliciesTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_proposal_WHEN_generate_policy_from_THEN_policy_has_info_from_original_case()
        {
            createCaseInput createCaseInput = new()
            {
                name = "test case",
                fields = "{}"
            };

            string caseId = await Case.Create(createCaseInput);
            string proposalId = await Proposal.Create(caseId);
            string offerId = await Offer.Create(caseId, proposalId, new addOfferInput());

            string? policyId = await GeneratePolicyFromProposalAsync(caseId, proposalId, true);
            policyId.Should().NotBeNullOrEmpty();

            policy? policy = await PolicyGetAsync(policyId);
            policy?.generatedFrom!.caseId.Should().Be(caseId);
            policy?.generatedFrom!.proposalId.Should().Be(proposalId);
            policy?.generatedFrom!.offerId.Should().Be(offerId);
            policy?.extraFields!.Should().Be(createCaseInput.fields);

            // policy.extraFields should be null if copyCaseFieldsToExtraFields is false
            string? policyId2 = await GeneratePolicyFromProposalAsync(caseId, proposalId);
            policy? policy2 = await PolicyGetAsync(policyId2);
            policy2?.extraFields!.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_proposal_with_offer_with_two_jackets_WHEN_generating_policy_from_THEN_jackets_copied_by_values()
        {
            string caseId = await Case.Create();
            string proposalId = await Proposal.Create(caseId);
            string offerId = await Offer.Create(caseId, proposalId, new addOfferInput());

            string jacket1Id = await JacketCreateAsync();
            string jacket2Id = await JacketCreateAsync();

            await ProposalOfferAddJacketAsync(caseId, proposalId, offerId, jacket1Id, 3);
            await ProposalOfferAddJacketAsync(caseId, proposalId, offerId, jacket2Id, 5);

            @case? @case = await CaseGetAsync(caseId);
            @case.Should().NotBeNull();

            jacketInstance?[] jacketInstances = @case!.proposals!.ElementAt(0)!.basket!.ElementAt(0)!.jackets!.ToArray();
            jacketInstances.Should().HaveCount(2);

            string? policyId = await GeneratePolicyFromProposalAsync(caseId, proposalId);
            policyId.Should().NotBeNullOrEmpty();

            policy? policy = await PolicyGetAsync(policyId);

            PolicyJacketAssert(policy, jacketInstances[0]);
            PolicyJacketAssert(policy, jacketInstances[1]);
        }

        [Fact]
        public async Task GIVEN_proposal_with_offer_with_a_jacket_WHEN_generating_policy_from_THEN_jacket_should_not_have_duplication_from_jacket_library()
        {
            string caseId = await Case.Create();
            string proposalId = await Proposal.Create(caseId);
            string offerId = await Offer.Create(caseId, proposalId, new addOfferInput());

            string jacketTitle = CreateNewGuid();
            string jacketId = await JacketCreateAsync(jacketTitle);
            await ProposalOfferAddJacketAsync(caseId, proposalId, offerId, jacketId, 3);
            @case? @case = await CaseGetAsync(caseId);
            @case.Should().NotBeNull();

            jacketInstance?[] jacketInstances = @case!.proposals!.ElementAt(0)!.basket!.ElementAt(0)!.jackets!.ToArray();
            jacketInstances.Should().HaveCount(1);

            string? policyId = await GeneratePolicyFromProposalAsync(caseId, proposalId);
            policyId.Should().NotBeNullOrEmpty();

            policy? policy = await PolicyGetAsync(policyId);

            PolicyJacketAssert(policy, jacketInstances[0]);

            List<jacket> jackets = await SearchJacketsByTitle(jacketTitle);
            jackets.Should().HaveCount(1);
        }

        [Fact]
        public async Task GIVEN_case_with_holder_WHEN_generating_policy_from_proposal_THEN_holder_copied_by_values()
        {
            string expectedFields = "{\"address\":{\"street\":\"Connaught road West\"}}";
            createIndividualInput createIndividualInput = new()
            {
                fields = @"{ address: { street: \""Connaught road West\"" } }"
            };
            string individualId = await Entity.CreateIndividual(createIndividualInput);

            productId productId = await Product.Create();

            createCaseInput createCaseInput = new() { name = "test case", holderId = individualId };
            string caseId = await Case.Create(createCaseInput);

            string proposalId = await Proposal.Create(caseId);

            addOfferInput addOfferInput = new() { productId = Product.ProductIdToInput(productId) };
            await Offer.Create(caseId, proposalId, addOfferInput);

            string policyId = await Policy.GenerateFromProposal(caseId, proposalId);
            policy? policy = await PolicyGetAsync(policyId);

            policy?.contractHolder?.id.Should().Be(individualId);
            policy?.contractHolder?.fields.Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_individual_WHEN_searching_by_product_THEN_holder_is_found()
        {
            string someValue = CreateNewGuid();
            string individualId = await Entity.CreateIndividual();

            productId productId = await Product.Create(new()
            {
                productId = new productIdInput
                {
                    plan = CreateNewGuid(),
                    type = CreateNewGuid(),
                    version = CreateNewGuid()
                },
                fields = @$"{{ productProp: {{ nestedProp: \""{someValue}\"" }} }}"
            });

            createCaseInput createCaseInput = new() { name = "test case", holderId = individualId };
            string caseId = await Case.Create(createCaseInput);
            string proposalId = await Proposal.Create(caseId);
            addOfferInput addOfferInput = new() { productId = Product.ProductIdToInput(productId) };
            await Offer.Create(caseId, proposalId, addOfferInput);

            string policyId = await Policy.GenerateFromProposal(caseId, proposalId);
            await Policy.AddContractInsured(policyId, individualId);

            individuals individuals = await Entity.FindIndividual(
                new()
                {
                    product = new()
                    {
                        fields = new()
                        {
                            path = "fields.productProp.nestedProp",
                            value = new()
                            {
                                stringValue = someValue,
                            },
                            condition = fieldsWhereCondition.EQUALS,
                        }
                    }
                }
            );

            individuals?.list?.SingleOrDefault()?.id.Should().Be(individualId);
        }

        [Fact]
        public async Task GIVEN_offer_fields_in_case_WHEN_generating_policy_from_proposal_THEN_case_offer_fields_copied_to_policy_fields()
        {
            string fields = @"{reminder: \""It is an important reminder\""}";
            string expectedFields = "{\"reminder\":\"It is an important reminder\"}";

            productId productId = await Product.Create();

            createCaseInput createCaseInput = new() { name = "test case" };
            string caseId = await Case.Create(createCaseInput);

            string proposalId = await Proposal.Create(caseId);

            addOfferInput addOfferInput = new() { productId = Product.ProductIdToInput(productId), fields = fields };
            await Offer.Create(caseId, proposalId, addOfferInput);

            string policyId = await Policy.GenerateFromProposal(caseId, proposalId);
            policy? policy = await PolicyGetAsync(policyId);

            policy?.fields!.RemoveWhitespaces().Should().Be(expectedFields.RemoveWhitespaces());
        }

        [Fact]
        public async Task GIVEN_offer_fields_in_case_WHEN_generating_policy_from_proposal_THEN_case_offer_fields_copied_to_policy_fields_without_insuredGroups()
        {
            string fields = @"{billing:{billingPricingDateMode:\""POLICY_START_DATE\"",billingMode:\""calendarYear\"",billingFrequency:\""monthly\""},insuredGroups:[{numberOfInsureds:1,planSelected:\""1\"",key:1690888843061}],policy:{startDate:\""2023-01-01\"",endDate:\""2023-12-31\""}}";
            string expectedFields = "{\"billing\":{\"billingPricingDateMode\":\"POLICY_START_DATE\",\"billingMode\":\"calendarYear\",\"billingFrequency\":\"monthly\"},\"policy\":{\"startDate\":\"2023-01-01\",\"endDate\":\"2023-12-31\"}}";

            productId productId = await Product.Create();

            createCaseInput createCaseInput = new() { name = "test case" };
            string caseId = await Case.Create(createCaseInput);

            string proposalId = await Proposal.Create(caseId);

            addOfferInput addOfferInput = new() { productId = Product.ProductIdToInput(productId), fields = fields };
            await Offer.Create(caseId, proposalId, addOfferInput);

            string policyId = await Policy.GenerateFromProposal(caseId, proposalId);

            policy? policy = await PolicyGetAsync(policyId);
            policy?.fields!.RemoveWhitespaces().Should().Be(expectedFields.RemoveWhitespaces());
        }

        [Fact]
        public async Task GIVEN_policy_created_and_issued_from_proposal_WHEN_search_by_havingIssuedPolicies_THEN_success()
        {
            var testCaseName = CreateNewGuid();
            var caseId = await Case.Create(new createCaseInput { name = testCaseName });
            var proposalId = await Proposal.Create(caseId);

            productId productId = await Product.Create();
            var offerId = await Offer.Create(caseId, proposalId, new addOfferInput
            {
                productId = Product.ProductIdToInput(productId),
                fields = "{}"
            });

            var policyId = await Policy.GenerateFromProposal(caseId, proposalId);

            var cases = await CasesGetAsync(new caseWhere
            {
                id = caseId,
                havingIssuedPolicies = true
            });

            cases.Should().BeEmpty();

            await Policy.Issue(policyId);

            cases = await CasesGetAsync(new caseWhere
            {
                id = caseId,
                havingIssuedPolicies = true
            });

            cases.Should().NotBeEmpty();
            var @case = cases.FirstOrDefault();
            @case!.name.Should().Be(testCaseName);
        }

        [Fact]
        public async Task GIVEN_offer_with_productTreeId_WHEN_generating_policy_from_it_THEN_policy_has_productTreeId()
        {
            string caseId = await Case.Create(new createCaseInput { });
            string proposalId = await Proposal.Create(caseId);

            string productTreeId = CreateNewGuid();
            await Offer.Create(caseId, proposalId, new addOfferInput
            {
                productTreeId = productTreeId
            });

            string policyId = await Policy.GenerateFromProposal(caseId, proposalId);
            policy? policy = await PolicyGetAsync(policyId);
            policy!.productTreeId.Should().Be(productTreeId);
        }

        [Fact]
        public async Task GIVEN_offer_with_productTreeRecords_WHEN_generating_policy_from_it_THEN_policy_has_productTreeId()
        {
            string caseId = await Case.Create(new createCaseInput { });
            string proposalId = await Proposal.Create(caseId);

            productTreeRecordInput productTreeRecord = new() { type = "test", recordId = CreateNewGuid() };
            await Offer.Create(caseId, proposalId, new addOfferInput
            {
                productTreeRecords = new List<productTreeRecordInput> { productTreeRecord }!
            });

            string policyId = await Policy.GenerateFromProposal(caseId, proposalId);
            policy? policy = await PolicyGetAsync(policyId);
            policy!.productTreeRecords!.First()!.type.Should().Be(productTreeRecord.type);
            policy!.productTreeRecords!.First()!.recordId.Should().Be(productTreeRecord.recordId);
        }

        private static void PolicyJacketAssert(policy? policy, jacketInstance? jacketInstance)
        {
            jacketInstance? policyJacket = policy!.jackets!.FirstOrDefault(x => x!.jacket!.title == jacketInstance!.jacket!.title);
            policyJacket.Should().NotBeNull();

            policyJacket!.jacket!.id.Should().NotBeNullOrEmpty();
            policyJacket!.jacket!.id.Should().NotBe(jacketInstance!.id);
            policyJacket!.order.Should().Be(jacketInstance?.order);

            policyJacket!.jacket!.clauses!.Count.Should().Be(jacketInstance!.jacket!.clauses!.Count);
        }

        private async Task<string?> GeneratePolicyFromProposalAsync(string caseId, string proposalId, bool copyCaseFieldsToExtraFields = false)
        {
            string mutation = new MutationBuilder()
                .generatePoliciesFromProposal(
                    new MutationBuilder.generatePoliciesFromProposalArgs(caseId, proposalId, copyCaseFieldsToExtraFields),
                    new createdStatusResultBuilder().WithAllFields()
                ).Build();
            createdStatusResult result = await _client.SendMutationAsync<createdStatusResult>(mutation);
            return result.createdStatus!.ids!.Single();
        }

        private Task<string> JacketCreateAsync(string? title = null)
        {
            createJacketInput input = new()
            {
                title = string.IsNullOrEmpty(title) ? CreateNewGuid() : title,
                status = CreateNewGuid(),
                clauses = new List<addClauseToJacketInput?>
                {
                    new() { id = CreateNewGuid(), order = 0, templateId = CreateNewGuid() },
                    new() { id = CreateNewGuid(), order = 1, templateId = CreateNewGuid() }
                }
            };

            return Jacket.Create(input);
        }

        private Task ProposalOfferAddJacketAsync(string caseId, string proposalId, string offerId, string jacketId, int order)
        {
            jacketInstanceBatchInput input = new()
            {
                addJacketInstanceInputs = new List<addJacketInstanceInput?>
                    {
                        new()
                        {
                            jacketId = jacketId,
                            order = order
                        }
                    }
            };

            string mutation = new MutationBuilder().
                proposalOfferJacketBatch(new MutationBuilder.proposalOfferJacketBatchArgs(caseId, proposalId, offerId, input),
                    new resultBuilder().WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        private async Task<List<@case?>> CasesGetAsync(caseWhere where)
        {
            string query = new QueryBuilder()
                .cases(new QueryBuilder.casesArgs(where), new casesBuilder()
                .list(new caseBuilder()
                    .name()))
            .Build();

            cases cases = await _client.SendQueryAsync<cases>(query);

            return cases.list!.ToList();
        }

        private async Task<@case?> CaseGetAsync(string caseId)
        {
            string query = new QueryBuilder()
                .cases(new QueryBuilder.casesArgs(new caseWhere { id = caseId }), new casesBuilder()
                .list(new caseBuilder()
                    .proposals(new caseBuilder.proposalsArgs(), new proposalBuilder()
                        .basket(new proposalBuilder.basketArgs(), new offerBuilder()
                            .jackets(new offerBuilder.jacketsArgs(), new jacketInstanceBuilder()
                                .id()
                                .order()
                                .jacket(new jacketBuilder()
                                    .id()
                                    .title()
                                    .clauses(new jacketBuilder.clausesArgs(), new clauseBuilder()
                                        .id()
                                        .order())))))))
            .Build();

            cases cases = await _client.SendQueryAsync<cases>(query);

            return cases.list!.Single();
        }

        private async Task<policy?> PolicyGetAsync(string? policyId)
        {
            string query = new QueryBuilder().policies(new QueryBuilder.policiesArgs(where: new policyWhereInput { id = policyId }),
                new policiesBuilder()
                .list(new policyBuilder()
                    .generatedFrom(new generatedFromBuilder()
                        .caseId()
                        .proposalId()
                        .offerId()
                    )
                    .contractHolder(new entityInterfaceBuilder()
                        .id()
                        .fields())
                    .fields()
                    .extraFields()
                    .jackets(new jacketInstanceBuilder()
                        .id()
                        .order()
                        .jacket(new jacketBuilder()
                            .id()
                            .title()
                            .status()
                            .clauses(new jacketBuilder.clausesArgs(), new clauseBuilder()
                                .id())))
                    .productTreeId()
                    .productTreeRecords(
                        new productTreeRecordBuilder().WithAllFields()
                        )
                ))
            .Build();

            policies result = await _client.SendQueryAsync<policies>(query);

            return result.list?.FirstOrDefault();
        }

        async Task<List<jacket>> SearchJacketsByTitle(string title)
        {
            string query = new QueryBuilder()
                .jackets(new QueryBuilder.jacketsArgs(where: new jacketWhereInput { title = title }), new jacketsBuilder()
                    .list(new jacketBuilder()
                        .id()
                        .title()
                        .status()
                        .clauses(new jacketBuilder.clausesArgs(), new clauseBuilder()
                            .id()
                            .order()
                        )))
                .Build();

            jackets jackets = await _client.SendQueryAsync<jackets>(query);
            return jackets.list!.ToList()!;
        }
    }
}
