﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Cases
{
    public class CopyProposalTests : TestsBase
    {
        public CopyProposalTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_proposal_WHEN_copy_THEN_proposal_offers_copied_by_values()
        {
            (string caseId, string proposalId, string offerId) = await SetupOffers();
            string copiedProposalId = await CopyProposal(caseId, proposalId);

            offer originalOffer = (await FindCaseProposal(caseId, proposalId)).basket!.First()!;
            offer copiedOffer = (await FindCaseProposal(caseId, copiedProposalId)).basket!.First()!;

            originalOffer.fields!.Should().Be(copiedOffer.fields);
            originalOffer.startDate.Should().Be(copiedOffer.startDate);
            originalOffer.endDate.Should().Be(copiedOffer.endDate);
        }


        async Task<(string caseId, string proposalId, string offerId)> SetupOffers()
        {
            string caseId = await Case.Create(new createCaseInput { name = "test case", });
            string proposalId = await CreateProposal(caseId);
            string offerId = await CreateOffer(caseId, proposalId);

            return (caseId, proposalId, offerId);
        }

        async Task<string> CopyProposal(string caseId, string proposalId)
        {
            copyProposalInput input = new() { copiedFromId = proposalId };

            string mutation = new MutationBuilder()
                .copyProposal(new MutationBuilder.copyProposalArgs(caseId, input), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            createdStatusResult response = await _client.SendMutationAsync<createdStatusResult>(mutation);
            return response!.createdStatus!.id!;
        }

        async Task<proposal> FindCaseProposal(string caseId, string proposalId)
        {
            caseWhere caseWhere = new() { id = caseId };
            offerWhere offerWhere = new();
            proposalWhere proposalWhere = new() { id = proposalId };

            string query = new QueryBuilder()
                .cases(new QueryBuilder.casesArgs(caseWhere), new casesBuilder()
                    .list(new caseBuilder()
                        .id()
                        .proposals(new caseBuilder.proposalsArgs(proposalWhere), new proposalBuilder()
                            .id()
                            .status()
                            .basket(new proposalBuilder.basketArgs(offerWhere), new offerBuilder()
                                .id()
                                .status()
                                .fields()))))
                .Build();

            cases response = await _client.SendQueryAsync<cases>(query);
            return response.list!.First()!.proposals!.FirstOrDefault()!;
        }

        Task<string> CreateOffer(string caseId, string proposalId)
        {
            DateTime startDate = DateTime.UtcNow;
            DateTime endDate = DateTime.UtcNow.AddYears(1);
            string fields = @"{reminder: \""It is an important reminder\""}";

            return Offer.Create(
                    caseId,
                    proposalId,
                    new addOfferInput
                    {
                        status = CreateNewGuid(),
                        fields = fields,
                        startDate = startDate,
                        endDate = endDate
                    });
        }

        Task<string> CreateProposal(string caseId) =>
            Proposal.Create(caseId, new addProposalInput { name = "new name" });
    }
}
