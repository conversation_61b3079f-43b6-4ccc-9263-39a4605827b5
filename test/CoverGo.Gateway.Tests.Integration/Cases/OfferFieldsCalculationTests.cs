﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class OfferFieldsCalculationTests : TestsBase
    {
        public OfferFieldsCalculationTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact(Skip = "need to be fixed")]
        public async Task GIVEN_offer_WHEN_query_pricing_THEN_returns_pricing()
        {
            (productId productId, string expectedOutput) = await Product.CreateWithScript(scriptTypeEnum.PRICING);

            const string fields = "{ value = 5 }";

            offer offer = await CreateOffer(fields, productId);

            offer.pricing.Should().Be(string.Format(expectedOutput, fields));
        }

        [Fact(Skip = "need to be fixed")]
        public async Task GIVEN_offer_WHEN_query_underwriting_THEN_returns_underwriting()
        {
            (productId productId, string expectedOutput) = await Product.CreateWithScript(scriptTypeEnum.UNDERWRITING);

            const string fields = "{ value = 5 }";

            offer offer = await CreateOffer(fields, productId);

            offer.underwriting.Should().Be(string.Format(expectedOutput, fields));
        }

        async Task<offer> CreateOffer(string fields, productId productId)
        {
            addOfferInput addOfferInput = new()
            {
                policyId = "new policy Id",
                fields = fields,
                productId = Product.ProductIdToInput(productId)
            };

            string caseId = await Case.Create();
            string proposalId = await Proposal.Create(caseId);
            string offerId = await Offer.Create(caseId, proposalId, addOfferInput);

            return await FindOffer(caseId, proposalId, offerId);
        }

        async Task<offer> FindOffer(string caseId, string proposalId, string offerId)
        {
            string query = new QueryBuilder()
                .cases(new QueryBuilder.casesArgs(new caseWhere { id = caseId }),
                new casesBuilder()
                    .list(new caseBuilder()
                        .proposals(new caseBuilder.proposalsArgs(new proposalWhere { id = proposalId }), new proposalBuilder()
                            .basket(new proposalBuilder.basketArgs(new offerWhere { id = offerId }), new offerBuilder()
                                .pricing()
                                .underwriting()
                                .id()
                                .status()))))
                .Build();

            cases cases = await _client.SendQueryAsync<cases>(query);

            return cases.list!.First()!.proposals!.First()!.basket!.First()!;
        }
    }
}