using CoverGo.Gateway.Client;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class OfferClausesAndJacketsTests : TestsBase
    {
        public OfferClausesAndJacketsTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_a_created_offer_WHEN_add_multiple_clauses_into_the_offer_THEN_added_and_succeed()
        {
            addOfferInput addOfferInput = new() { policyId = "new policy Id" };

            (string caseId, string proposalId, string offerId) = await <PERSON>reateOffer(addOfferInput);

            clauseBatchInput clauseBatchInput = new()
            {
                addClauseInputs = new List<clauseBatchAddInput?> {
                    new()
                    {
                        templateId = Guid.NewGuid().ToString(),
                        order = 2
                    },
                    new()
                    {
                        templateId = Guid.NewGuid().ToString(),
                        order = 1
                    }
                }
            };

            Func<Task> action = () => ProposalOfferClauseBatch(caseId, proposalId, offerId, clauseBatchInput);
            await action.Should().NotThrowAsync<Exception>();
            offer offer = await SearchOfferByCaseId(caseId);
            offer.clauses.Should().HaveCount(2);
        }

        [Fact]
        public async Task GIVEN_a_created_offer_WHEN_add_multiple_jackets_into_the_offer_THEN_added_and_succeed()
        {
            addOfferInput addOfferInput = new() { policyId = "new policy Id" };

            (string caseId, string proposalId, string offerId) = await CreateOffer(addOfferInput);

            jacketInstanceBatchInput jacketInstanceBatchInput = new()
            {
                addJacketInstanceInputs = new List<addJacketInstanceInput?> {
                    new()
                    {
                        jacketId = Guid.NewGuid().ToString(),
                        order = 2
                    },
                    new()
                    {
                        jacketId = Guid.NewGuid().ToString(),
                        order = 1
                    }
                }
            };

            Func<Task> action = () => ProposalOfferJacketBatch(caseId, proposalId, offerId, jacketInstanceBatchInput);
            await action.Should().NotThrowAsync<Exception>();
            offer offer = await SearchOfferByCaseId(caseId);
            offer.jackets.Should().HaveCount(2);
        }

        [Fact]
        public async Task GIVEN_a_created_offer_with_list_of_jackets_WHEN_remove_jacket_instance_from_the_offer_THEN_removed_and_succeed()
        {
            addOfferInput addOfferInput = new() { policyId = "new policy Id" };

            (string caseId, string proposalId, string offerId) = await CreateOffer(addOfferInput);

            jacketInstanceBatchInput jacketInstanceBatchInput = new()
            {
                addJacketInstanceInputs = new List<addJacketInstanceInput?> {
                    new()
                    {
                        jacketId = Guid.NewGuid().ToString(),
                        order = 2
                    },
                    new()
                    {
                        jacketId = Guid.NewGuid().ToString(),
                        order = 1
                    }
                }
            };

            await ProposalOfferJacketBatch(caseId, proposalId, offerId, jacketInstanceBatchInput);
            offer offerBeforeRemovedJacket = await SearchOfferByCaseId(caseId);
            offerBeforeRemovedJacket.jackets.Should().HaveCount(2);

            Func<Task> action = () => RemoveJacketFromOffer(caseId, proposalId, offerId, offerBeforeRemovedJacket.jackets!.First()!.id!);
            await action.Should().NotThrowAsync<Exception>();
            offer offerAfterRemovedJacket = await SearchOfferByCaseId(caseId);
            offerAfterRemovedJacket.jackets.Should().HaveCount(1);
            offerAfterRemovedJacket.jackets!.First().Should().BeEquivalentTo(offerBeforeRemovedJacket.jackets!.Last());

        }

        private async Task<(string caseId, string proposalId, string offerId)> CreateOffer(addOfferInput input)
        {
            string caseId = await Case.Create(new createCaseInput { name = "test case", });
            string proposalId = await CreateProposal(caseId);
            string offerId = await Offer.Create(caseId, proposalId, input);
            return (caseId, proposalId, offerId);
        }

        private Task<string> CreateProposal(string caseId) =>
            Proposal.Create(caseId, new addProposalInput { name = "new name" });

        private Task ProposalOfferClauseBatch(string caseId, string proposalId, string offerId, clauseBatchInput clauseBatchInput)
        {
            string mutation = new MutationBuilder()
                .proposalOfferClauseBatch(new MutationBuilder.proposalOfferClauseBatchArgs(caseId, proposalId, offerId, clauseBatchInput), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        private async Task ProposalOfferJacketBatch(string caseId, string proposalId, string offerId, jacketInstanceBatchInput jacketInstanceBatchInput)
        {
            string mutation = new MutationBuilder()
                .proposalOfferJacketBatch(new MutationBuilder.proposalOfferJacketBatchArgs(caseId, proposalId, offerId, jacketInstanceBatchInput), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        private Task RemoveJacketFromOffer(string caseId, string proposalId, string offerId, string jacketInstanceId)
        {
            string mutation = new MutationBuilder()
                .removeJacketFromProposalOffer(new MutationBuilder.removeJacketFromProposalOfferArgs(caseId, proposalId, offerId, jacketInstanceId), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        private async Task<offer> SearchOfferByCaseId(string id)
        {
            string query = new QueryBuilder()
                .cases(new QueryBuilder.casesArgs(new caseWhere { id = id }), new casesBuilder()
                    .list(new caseBuilder()
                        .id()
                        .proposals(new caseBuilder.proposalsArgs(), new proposalBuilder()
                            .id()
                            .basket(new proposalBuilder.basketArgs(), new offerBuilder()
                                .id()
                                .clauses(new offerBuilder.clausesArgs(), new clauseBuilder()
                                    .id())
                                .jackets(new offerBuilder.jacketsArgs(), new jacketInstanceBuilder()
                                    .id())
                                ))))
                .Build();

            cases response = await _client.SendQueryAsync<cases>(query);
            return response.list!.First()!.proposals!.First()!.basket!.First()!;
        }
    }
}