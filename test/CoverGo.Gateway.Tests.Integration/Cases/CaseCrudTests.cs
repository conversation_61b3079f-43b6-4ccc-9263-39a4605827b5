﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class CaseCrudTests : TestsBase
    {
        public CaseCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_login_with_no_readCases_permission_WHEN_reading_cases_THEN_returns_no_cases()
        {
            createCaseInput createCaseInput = new() { name = "test case" };

            await Case.Create(createCaseInput);
            await Case.Create(createCaseInput);
            List<@case> allCases = await SearchAllCases();

            _client.HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", await GetLoginTokenWithNoPermissions());
            List<@case> authroisedCases = await SearchAllCases();

            allCases.Count().Should().BeGreaterThan(0);
            authroisedCases.Count().Should().Be(0);
        }

        [Fact]
        public async Task GIVEN_cases_WHEN_create_without_fields_and_schemas_THEN_returns_id()
        {
            createCaseInput createCaseInput = new() { name = "test case", };

            string caseId = await Case.Create(createCaseInput);

            caseId.Should().NotBeNullOrWhiteSpace();
        }

        [Fact]
        public async Task GIVEN_case_created_without_fields_and_schemas_WHEN_request_this_case_THEN_returns_case()
        {
            createCaseInput createCaseInput = new() { name = "test case", };

            string caseId = await Case.Create(createCaseInput);

            @case? @case = await SearchCaseById(caseId);

            @case?.name.Should().Be(createCaseInput.name);
        }

        [Fact]
        public async Task GIVEN_case_created_without_fields_and_schemas_WHEN_update_this_case_THEN_succeed_and_updated()
        {
            createCaseInput createCaseInput = new() { name = "test case", };

            string caseId = await Case.Create(createCaseInput);

            updateCaseInput updateCaseInput = new() { name = "updated value", };

            await UpdateCase(caseId, updateCaseInput);

            @case? @case = await SearchCaseById(caseId);

            @case?.name.Should().Be(updateCaseInput.name);
        }

        [Fact]
        public async Task GIVEN_cases_WHEN_create_with_fields_and_schemas_THEN_returns_id()
        {
            (string? schemaId1, _) = await CreateDataSchema();
            (string? schemaId2, _) = await CreateDataSchema();

            createCaseInput createCaseInput = new()
            {
                name = "test case",
                fields = "{}",
                fieldsSchemaId = schemaId1,
                workflowSchemaId = schemaId2,
            };

            string caseId = await Case.Create(createCaseInput);

            caseId.Should().NotBeNullOrWhiteSpace();
        }

        [Fact]
        public async Task GIVEN_case_created_with_fields_and_schemas_WHEN_request_this_case_THEN_returns_case()
        {
            (string? schemaId1, createDataSchemaInput createDataSchemaInput1) = await CreateDataSchema("name1");
            (string? schemaId2, createDataSchemaInput createDataSchemaInput2) = await CreateDataSchema("name2");

            createCaseInput createCaseInput = new()
            {
                name = "test case",
                fields = "{}",
                fieldsSchemaId = schemaId1,
                workflowSchemaId = schemaId2,
            };

            string caseId = await Case.Create(createCaseInput);

            @case? @case = await SearchCaseById(caseId);

            @case?.name.Should().Be(createCaseInput.name);
            @case?.fields.Should().Be(createCaseInput.fields);
            @case?.fieldsSchema.ShouldHaveSameValuesAsInput(createDataSchemaInput1);
            @case?.workflowSchema.ShouldHaveSameValuesAsInput(createDataSchemaInput2);
        }

        [Fact]
        public async Task GIVEN_case_created_with_fields_and_schemas_WHEN_update_this_case_THEN_succeed_and_updated()
        {
            createCaseInput createCaseInput = new()
            {
                name = "test case",
                fields = "{}",
                fieldsSchemaId = "test value",
                workflowSchemaId = "test value",
            };

            string caseId = await Case.Create(createCaseInput);

            (string? schemaId1, createDataSchemaInput createDataSchemaInput1) = await CreateDataSchema("name1");
            (string? schemaId2, createDataSchemaInput createDataSchemaInput2) = await CreateDataSchema("name2");

            updateCaseInput updateCaseInput = new()
            {
                name = "updated value",
                fields = "{}",
                fieldsSchemaId = schemaId1,
                workflowSchemaId = schemaId2,
            };

            await UpdateCase(caseId, updateCaseInput);

            @case? @case = await SearchCaseById(caseId);

            @case?.name.Should().Be(updateCaseInput.name);
            @case?.fields.Should().Be(updateCaseInput.fields);
            @case?.fieldsSchema.ShouldHaveSameValuesAsInput(createDataSchemaInput1);
            @case?.workflowSchema.ShouldHaveSameValuesAsInput(createDataSchemaInput2);
        }

        [Fact]
        public async Task GIVEN_case_created_WHEN_searched_with_caseId_THEN_creation_event_should_exist()
        {
            createCaseInput createCaseInput = new() { name = "test case" };
            string caseId = await Case.Create(createCaseInput);

            caseWhere where = new() { id = caseId };
            @case? @case = await SearchCase(where);

            List<detailedEventLog?> events = @case!.events!.ToList();
            events.Count.Should().BeGreaterOrEqualTo(1);
            events.Should().Contain(y => y!.type == "creation");
            events.Should().Contain(e => e!.valueAsString!.Contains(@"""name"":""test case"""));
        }

        [Fact]
        public async Task GIVEN_case_created_WHEN_Added_proposal_and_request_with_proposalNumber_contains_THEN_returns_case_with_proposal()
        {
            createCaseInput createCaseInput = new()
            {
                name = "test case",
                fields = "{}",
                caseNumber = CreateNewGuid()
            };

            string caseId = await Case.Create(createCaseInput);
            addProposalInput proposal = new() { proposalNumber = CreateNewGuid() };
            await Proposal.Create(caseId, proposal);

            caseWhere where = new()
            {
                proposals_contains = new proposalWhere
                {
                    proposalNumber_contains = proposal.proposalNumber
                }
            };
            @case? @case = await SearchCase(where);

            @case?.proposals!.Select(x => x!.proposalNumber).Count().Should().Be(1);
            @case?.proposals!.Select(x => x!.proposalNumber).Single().Should().Be(proposal.proposalNumber);
        }

        [Fact]
        public async Task GIVEN_case_created_WHEN_Added_proposals_and_request_with_caseNumber_contains_THEN_returns_cases_with_proposals()
        {
            createCaseInput createCaseInput = new()
            {
                name = "test case",
                fields = "{}",
                caseNumber = CreateNewGuid()
            };

            string caseId = await Case.Create(createCaseInput);
            addProposalInput proposal1 = new() { proposalNumber = CreateNewGuid() };
            await Proposal.Create(caseId, proposal1);
            addProposalInput proposal2 = new() { proposalNumber = CreateNewGuid() };
            await Proposal.Create(caseId, proposal2);


            caseWhere where = new()
            {
                proposals_contains = new proposalWhere
                {
                    proposalNumber_contains = proposal1.proposalNumber
                }
            };
            @case? @case = await SearchCase(where);

            @case?.proposals!.Select(x => x!.proposalNumber).Count().Should().Be(2);

            @case?.proposals!.Select(c => c!.proposalNumber)
                .Should().BeEquivalentTo(proposal1.proposalNumber, proposal2.proposalNumber);
        }

        [Fact]
        public async Task GIVEN_case_created_WHEN_request_with_caseNumber_contains_THEN_returns_case()
        {
            createCaseInput createCaseInput = new()
            {
                name = CreateNewGuid(),
                fields = "{}",
                caseNumber = CreateNewGuid()
            };

            string caseId = await Case.Create(createCaseInput);
            caseWhere where = new() { caseNumber_contains = createCaseInput.caseNumber };
            @case? @case = await SearchCase(where);

            @case?.caseNumber.Should().Be(createCaseInput.caseNumber);
        }

        [Fact]
        public async Task GIVEN_case_created_WHEN_request_with_multiple_unmatched_criteria_THEN_returns_no_case()
        {
            string suffix = "M23D";
            DateTime now = DateTime.UtcNow;
            string companyId = await Entity.CreateCompany(new createCompanyInput { nameFormat = $"{suffix}-{CreateNewGuid()}" });
            createCaseInput createCaseInput = new()
            {
                name = "test case",
                fields = "{}",
                caseNumber = $"{suffix}-{CreateNewGuid()}",
            };
            string caseId = await Case.Create(createCaseInput);

            addProposalInput proposal = new() { proposalNumber = $"{suffix}-{CreateNewGuid()}" };
            await Proposal.Create(caseId, proposal);

            updateCaseInput updateInput = new() { holderId = companyId };
            await Case.Update(caseId, updateInput);

            caseWhere where = new()
            {
                and = new List<caseWhere>
                {
                    new () { holderCompany = new individualWhereInput { name_contains = "AnyValue"} },
                    new () { createdAt_gt = new DateTimeOffset(now) }
                }!
            };
            @case? @case = await SearchCase(where);
            @case.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_case_created_WHEN_request_with_multiple_criteria_THEN_returns_case()
        {
            string suffix = "M23D";
            DateTime now = DateTime.Now;
            string companyId = await Entity.CreateCompany(new createCompanyInput { nameFormat = $"{suffix}-{CreateNewGuid()}" });
            createCaseInput createCaseInput = new()
            {
                name = "test case",
                fields = "{}",
                caseNumber = $"{suffix}-{CreateNewGuid()}",
            };
            string caseId = await Case.Create(createCaseInput);

            addProposalInput proposal = new() { proposalNumber = $"{suffix}-{CreateNewGuid()}" };
            await Proposal.Create(caseId, proposal);

            //await createCompanyTask;
            updateCaseInput updateInput = new() { holderId = companyId };
            await Case.Update(caseId, updateInput);

            caseWhere where = new()
            {
                and = new List<caseWhere>
                {
                    new()
                    {
                        or = new List<caseWhere>
                        {
                            new() { caseNumber = suffix },
                            new() { proposal =new proposalWhere { proposalNumber_contains = suffix  }, },
                            new() { holderCompany = new individualWhereInput { name_contains = suffix} },
                        }!
                    },
                    new()
                    {
                        createdAt_gt = new DateTimeOffset(now)
                    }
                }!
            };
            @case? @case = await SearchCase(where);

            @case!.id.Should().Be(caseId);
            @case?.proposals!.Select(x => x!.proposalNumber).Count().Should().Be(1);
            @case?.proposals!.Select(x => x!.proposalNumber).Single().Should().Be(proposal.proposalNumber);
        }

        [Fact]
        public async Task GIVEN_case_created_WHEN_request_with_multiple_criteria_and_one_false_value_in_or_THEN_returns_case()
        {
            string suffix = "M23D";
            DateTime now = DateTime.Now;
            string companyId = await Entity.CreateCompany(new createCompanyInput { nameFormat = $"{suffix}-{CreateNewGuid()}" });
            createCaseInput createCaseInput = new()
            {
                name = "test case",
                fields = "{}",
                caseNumber = $"{suffix}-{CreateNewGuid()}",
            };
            string caseId = await Case.Create(createCaseInput);

            addProposalInput proposal = new() { proposalNumber = $"{suffix}-{CreateNewGuid()}" };
            await Proposal.Create(caseId, proposal);

            //await createCompanyTask;
            updateCaseInput updateInput = new() { holderId = companyId };
            await Case.Update(caseId, updateInput);

            caseWhere where = new()
            {
                and = new List<caseWhere>
                {
                    new()
                    {
                        or = new List<caseWhere>
                        {
                            new() { caseNumber = suffix },
                            new() { proposal =new proposalWhere { proposalNumber_contains = suffix  }, },
                            new() { holderCompany = new individualWhereInput { name_contains = "AnyValue"} },
                        }!
                    },
                    new()
                    {
                        createdAt_gt = new DateTimeOffset(now)
                    }
                }!
            };
            @case? @case = await SearchCase(where);

            @case!.id.Should().Be(caseId);
            @case.proposals!.Select(x => x!.proposalNumber).Count().Should().Be(1);
            @case?.proposals!.Select(x => x!.proposalNumber).Single().Should().Be(proposal.proposalNumber);
        }

        [Fact]
        public async Task GIVEN_case_with_offer_created_WHEN_adding_clauses_by_value_THEN_actually_added_by_values()
        {
            string caseId = await Case.Create();
            string proposalId = await Proposal.Create(caseId);
            string offerId = await Offer.Create(caseId, proposalId, new addOfferInput());

            const string initialHtml = "<div>Boo</div>";
            string templateId = await Template.CreateClauseHtmlTemplate(new createClauseHtmlTemplateInput { html = initialHtml.Escape() });

            string mutation = new MutationBuilder().proposalOfferClauseBatch(
                new MutationBuilder.proposalOfferClauseBatchArgs(caseId, proposalId, offerId,
                    new clauseBatchInput
                    {
                        addClauseInputs = new List<clauseBatchAddInput?>
                        {
                            new()
                            {
                                templateId = templateId,
                                storeTemplateByValue = true,
                                renderParameters = new templateRenderParametersInput
                                {
                                    name = "test", contentJsonString = "{}"
                                },
                                type = "manually"
                            }
                        }
                    }), new resultBuilder().status()).Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);

            await Template.UpdateClauseHtmlTemplate(templateId,
                new updateClauseHtmlTemplateInput { html = "<div>Updated</div>".Escape() });

            string query = new QueryBuilder().cases(new QueryBuilder.casesArgs(new caseWhere { id = caseId }),
                new casesBuilder()
                    .list(new caseBuilder()
                        .proposals(new caseBuilder.proposalsArgs(new proposalWhere()),
                            new proposalBuilder()
                                .basket(new proposalBuilder.basketArgs(),
                                    new offerBuilder()
                                        .clauses(new offerBuilder.clausesArgs(),
                                            new clauseBuilder()
                                                .storeTemplateByValue()
                                                .type()
                                                .renderedHtmlResult(new stringResultBuilder().value())
                                                .template(new templateInterfaceBuilder()
                                                    .clauseHtmlFragment(new clauseHtmlBuilder()
                                                        .html()))))))).Build();

            cases cases = await _client.SendQueryAsync<cases>(query);

            clause clause = cases.list!.ToArray()[0]!.proposals!.ToArray()[0]!.basket!.ToArray()[0]!.clauses!.First()!;
            clause.storeTemplateByValue.Should().BeTrue();
            clause.type.Should().Be("manually");

            string template = clause.renderedHtmlResult!.value!;
            template.Should().Be(initialHtml);
        }

        [Fact]
        public async Task GIVEN_case_with_offer_created_WHEN_adding_jackets_by_value_THEN_actually_added_by_value()
        {
            string caseId = await Case.Create();
            string proposalId = await Proposal.Create(caseId);
            string offerId = await Offer.Create(caseId, proposalId, new addOfferInput());

            const string initialHtml = "<div>Boo</div>";
            string templateId = await Template.CreateClauseHtmlTemplate(new createClauseHtmlTemplateInput { html = initialHtml.Escape() });

            string jacketClauseId = CreateNewGuid();

            string jacketId = await Jacket.Create(new createJacketInput
            {
                title = "initial",
                clauses = new List<addClauseToJacketInput?>
                {
                    new()
                    {
                        id = jacketClauseId,
                        templateId = templateId,
                        renderParameters = new templateRenderParametersInput
                        {
                            name = "test", contentJsonString = "{}"
                        }
                    }
                }
            });

            string mutation = new MutationBuilder().proposalOfferJacketBatch(
                new MutationBuilder.proposalOfferJacketBatchArgs(caseId, proposalId, offerId,
                    new jacketInstanceBatchInput
                    {
                        addJacketInstanceInputs = new List<addJacketInstanceInput?>
                        {
                            new()
                            {
                                jacketId = jacketId, order = 0, storeJacketByValue = true
                            }
                        }
                    }), new resultBuilder().status()).Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);

            await Jacket.Update(jacketId,
                new updateJacketInput { title = "updated" });

            await Template.UpdateClauseHtmlTemplate(templateId,
                new updateClauseHtmlTemplateInput { html = "<div>Updated</div>".Escape() });

            string query = new QueryBuilder().cases(new QueryBuilder.casesArgs(new caseWhere { id = caseId }),
                new casesBuilder()
                    .list(new caseBuilder()
                        .proposals(new caseBuilder.proposalsArgs(new proposalWhere()),
                            new proposalBuilder()
                                .basket(new proposalBuilder.basketArgs(),
                                    new offerBuilder()
                                        .jackets(new offerBuilder.jacketsArgs(), new jacketInstanceBuilder()
                                            .storedByValue()
                                            .jacket(new jacketBuilder()
                                            .title()
                                            .clauses(new jacketBuilder.clausesArgs(),
                                                new clauseBuilder()
                                                    .storeTemplateByValue()
                                                    .renderedHtmlResult(new stringResultBuilder().value()))))
                                )))).Build();

            cases cases = await _client.SendQueryAsync<cases>(query);

            jacketInstance jacketInstance = cases.list!.First()!.proposals!.First()!.basket!.First()!.jackets!.First()!;
            jacketInstance.storedByValue.Should().BeTrue();

            jacket jacket = jacketInstance.jacket!;

            jacket.title.Should().Be("initial");
            jacket.clauses!.Count.Should().Be(1);

            clause clause = jacket.clauses.First()!;
            clause.storeTemplateByValue.Should().BeTrue();
            clause.renderedHtmlResult!.value.Should().Be(initialHtml);
        }

        [Fact]
        public async Task
            GIVEN_proposal_with_clauses_added_by_values_WHEN_generating_policy_from_proposal_THEN_clauses_copied_to_policy_by_value()
        {
            string caseId = await Case.Create();
            string proposalId = await Proposal.Create(caseId);
            string offerId = await Offer.Create(caseId, proposalId, new addOfferInput());

            const string initialHtml = "<div>Initial</div>";
            string templateId = await Template.CreateClauseHtmlTemplate(new createClauseHtmlTemplateInput { html = initialHtml.Escape() });

            string addClauseMutation = new MutationBuilder().proposalOfferClauseBatch(
                new MutationBuilder.proposalOfferClauseBatchArgs(caseId, proposalId, offerId,
                    new clauseBatchInput
                    {
                        addClauseInputs = new List<clauseBatchAddInput?>
                        {
                            new()
                            {
                                templateId = templateId,
                                storeTemplateByValue = true,
                                renderParameters = new templateRenderParametersInput
                                {
                                    name = "test", contentJsonString = "{}"
                                }
                            }
                        }
                    }), new resultBuilder().status()).Build();

            await _client.SendMutationAndEnsureSuccessAsync(addClauseMutation);

            await Offer.Update(caseId, proposalId, offerId, new updateOfferInput
            {
                status = "Accepted"
            });

            await Template.UpdateClauseHtmlTemplate(templateId,
                new updateClauseHtmlTemplateInput { html = "<div>Updated</div>".Escape() });

            string generatePoliciesMutation = new MutationBuilder()
                .generatePoliciesFromProposal(new MutationBuilder.generatePoliciesFromProposalArgs(caseId, proposalId, false, true, true),
                    new createdStatusResultBuilder()
                        .createdStatus(new createdStatusBuilder()
                            .id()
                            .ids())
                        .status())
                .Build();

            createdStatusResult generatePolicyResult = await _client.SendMutationAsync<createdStatusResult>(generatePoliciesMutation);

            string policyId = generatePolicyResult!.createdStatus!.ids!.First()!;

            policy policy = await SearchPolicyById(policyId);

            policy.clauses!.Count.Should().Be(1);
            clause clause = policy.clauses.First()!;
            clause.renderedHtmlResult!.value.Should().Be(initialHtml);
            clause.storeTemplateByValue.Should().BeTrue();
        }

        [Fact]
        public async Task
            GIVEN_proposal_with_source_added_WHEN_generating_policy_from_proposal_THEN_source_copied_to_policy()
        {
            string source = "S-123456";
            string caseId = await Case.CreateWithSource(source);
            string proposalId = await Proposal.Create(caseId);
            string offerId = await Offer.Create(caseId, proposalId, new addOfferInput());


            await Offer.Update(caseId, proposalId, offerId, new updateOfferInput
            {
                status = "Accepted"
            });


            string generatePoliciesMutation = new MutationBuilder()
                .generatePoliciesFromProposal(new MutationBuilder.generatePoliciesFromProposalArgs(caseId, proposalId, false, true, true),
                    new createdStatusResultBuilder()
                        .createdStatus(new createdStatusBuilder()
                            .id()
                            .ids())
                        .status())
                .Build();

            createdStatusResult generatePolicyResult = await _client.SendMutationAsync<createdStatusResult>(generatePoliciesMutation);

            string policyId = generatePolicyResult!.createdStatus!.ids!.First()!;

            policy policy = await SearchPolicyById(policyId);

            policy.source.Should().BeEquivalentTo(source);
        }

        [Fact]
        public async Task
            GIVEN_proposal_with_jackets_added_by_values_WHEN_generating_policy_from_proposal_THEN_jackets_copied_from_proposal_by_values()
        {
            string caseId = await Case.Create();
            string proposalId = await Proposal.Create(caseId);
            string offerId = await Offer.Create(caseId, proposalId, new addOfferInput());

            const string initialHtml = "<div>Boo</div>";
            string templateId =
                await Template.CreateClauseHtmlTemplate(
                    new createClauseHtmlTemplateInput { html = initialHtml.Escape() });

            string jacketClauseId = CreateNewGuid();

            string jacketId = await Jacket.Create(new createJacketInput
            {
                title = "initial",
                clauses = new List<addClauseToJacketInput?>
                {
                    new()
                    {
                        id = jacketClauseId,
                        templateId = templateId,
                        renderParameters = new templateRenderParametersInput
                        {
                            name = "test", contentJsonString = "{}"
                        }
                    }
                }
            });

            await Offer.Update(caseId, proposalId, offerId, new updateOfferInput
            {
                status = "Accepted"
            });

            string proposalJacketAddMutation = new MutationBuilder().proposalOfferJacketBatch(
                new MutationBuilder.proposalOfferJacketBatchArgs(caseId, proposalId, offerId,
                    new jacketInstanceBatchInput
                    {
                        addJacketInstanceInputs = new List<addJacketInstanceInput?>
                        {
                            new() { jacketId = jacketId, order = 0, storeJacketByValue = true }
                        }
                    }), new resultBuilder().status()).Build();

            await _client.SendMutationAndEnsureSuccessAsync(proposalJacketAddMutation);

            await Jacket.Update(jacketId,
                new updateJacketInput { title = "updated" });

            await Template.UpdateClauseHtmlTemplate(templateId,
                new updateClauseHtmlTemplateInput { html = "<div>Updated</div>".Escape() });


            string generatePoliciesMutation = new MutationBuilder()
                .generatePoliciesFromProposal(new MutationBuilder.generatePoliciesFromProposalArgs(caseId, proposalId, false, true, true),
                    new createdStatusResultBuilder()
                        .createdStatus(new createdStatusBuilder()
                            .id()
                            .ids())
                        .status())
                .Build();

            createdStatusResult generatePolicyResult = await _client.SendMutationAsync<createdStatusResult>(generatePoliciesMutation);

            string policyId = generatePolicyResult!.createdStatus!.ids!.First()!;

            policy policy = await SearchPolicyById(policyId);

            policy.jackets!.Count.Should().Be(1);

            jacketInstance jacketInstance = policy.jackets.First()!;
            jacketInstance.jacket!.title.Should().Be("initial");

            jacketInstance.jacket!.clauses!.Count.Should().Be(1);

            clause clause = jacketInstance.jacket.clauses.First()!;
            clause.renderedHtmlResult!.value.Should().Be(initialHtml);
            clause.storeTemplateByValue.Should().BeTrue();
        }

        [Fact]
        public async Task GIVEN_proposal_with_offer_with_clauses_stored_by_values_WHEN_clone_clauses_to_another_offer_THEN_cloned_by_values()
        {
            string caseId = await Case.Create();
            string proposalId = await Proposal.Create(caseId);
            string offerId = await Offer.Create(caseId, proposalId, new addOfferInput());

            const string initialHtml = "<div>Initial</div>";
            string templateId = await Template.CreateClauseHtmlTemplate(new createClauseHtmlTemplateInput { html = initialHtml.Escape() });

            string addClauseMutation = new MutationBuilder().proposalOfferClauseBatch(
                new MutationBuilder.proposalOfferClauseBatchArgs(caseId, proposalId, offerId,
                    new clauseBatchInput
                    {
                        addClauseInputs = new List<clauseBatchAddInput?>
                        {
                            new()
                            {
                                templateId = templateId,
                                storeTemplateByValue = true,
                                renderParameters = new templateRenderParametersInput
                                {
                                    name = "test", contentJsonString = "{}"
                                }
                            }
                        }
                    }), new resultBuilder().status()).Build();

            await _client.SendMutationAndEnsureSuccessAsync(addClauseMutation);

            await Template.UpdateClauseHtmlTemplate(templateId,
                new updateClauseHtmlTemplateInput { html = "<div>Updated</div>".Escape() });

            string newOfferId = await Offer.Create(caseId, proposalId, new addOfferInput());

            string mutation = new MutationBuilder()
                .proposalOfferClausesClone(
                    new MutationBuilder.proposalOfferClausesCloneArgs(
                        new offerIdInput { caseId = caseId, offerId = offerId, proposalId = proposalId },
                        new offerIdInput { caseId = caseId, offerId = newOfferId, proposalId = proposalId }),
                    new resultBuilder()
                        .status()
                        .errors()).Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);

            @case? @case = await Case.FindById(caseId);

            offer offer = @case!.proposals!.FirstOrDefault(x => x!.id == proposalId)!.basket!.FirstOrDefault(x => x!.id == newOfferId)!;

            offer.Should().NotBeNull();

            clause? clause = offer.clauses!.Single();
            clause!.renderedHtmlResult!.value.Should().Be(initialHtml);
        }

        [Fact]
        public async Task GIVEN_proposal_with_offer_with_jackets_stored_by_values_WHEN_clone_jackets_to_another_offer_THEN_cloned_by_values()
        {
            string caseId = await Case.Create();
            string proposalId = await Proposal.Create(caseId);
            string offerId = await Offer.Create(caseId, proposalId, new addOfferInput());

            const string initialHtml = "<div>Boo</div>";
            string templateId =
                await Template.CreateClauseHtmlTemplate(
                    new createClauseHtmlTemplateInput { html = initialHtml.Escape() });

            string jacketClauseId = CreateNewGuid();

            string jacketId = await Jacket.Create(new createJacketInput
            {
                title = "initial",
                clauses = new List<addClauseToJacketInput?>
                {
                    new()
                    {
                        id = jacketClauseId,
                        templateId = templateId,
                        renderParameters = new templateRenderParametersInput
                        {
                            name = "test", contentJsonString = "{}"
                        }
                    }
                }
            });

            string proposalJacketAddMutation = new MutationBuilder().proposalOfferJacketBatch(
                new MutationBuilder.proposalOfferJacketBatchArgs(caseId, proposalId, offerId,
                    new jacketInstanceBatchInput
                    {
                        addJacketInstanceInputs = new List<addJacketInstanceInput?>
                        {
                            new() { jacketId = jacketId, order = 0, storeJacketByValue = true }
                        }
                    }), new resultBuilder().status()).Build();

            await _client.SendMutationAndEnsureSuccessAsync(proposalJacketAddMutation);

            await Jacket.Update(jacketId,
                new updateJacketInput { title = "updated" });

            await Template.UpdateClauseHtmlTemplate(templateId,
                new updateClauseHtmlTemplateInput { html = "<div>Updated</div>".Escape() });

            string newOfferId = await Offer.Create(caseId, proposalId, new addOfferInput());

            string mutation = new MutationBuilder()
                .proposalOfferJacketsClone(
                    new MutationBuilder.proposalOfferJacketsCloneArgs(
                        new offerIdInput { caseId = caseId, offerId = offerId, proposalId = proposalId },
                        new offerIdInput { caseId = caseId, offerId = newOfferId, proposalId = proposalId }),
                    new resultBuilder()
                        .status()
                        .errors()).Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);

            @case? @case = await Case.FindById(caseId);

            offer offer = @case!.proposals!.FirstOrDefault(x => x!.id == proposalId)!.basket!.FirstOrDefault(x => x!.id == newOfferId)!;

            offer.Should().NotBeNull();

            jacketInstance jacketInstance = offer.jackets!.Single()!;
            var jacket = jacketInstance.jacket;

            jacket.Should().NotBeNull();
            jacket!.title.Should().Be("initial");

            clause? clause = jacket!.clauses!.Single();
            clause!.renderedHtmlResult!.value.Should().Be(initialHtml);
        }

        async Task<string> GetLoginTokenWithNoPermissions()
        {
            string groupId = await PermissionGroup.Create();
            string clientId = await CreateClientId();
            await AddClientIdToGroup(clientId, groupId);

            (string loginId, string username, string password) = await CreateLogin();

            await Login.AddToGroup(loginId, groupId);

            string token = await Token.Fetch(new QueryBuilder.token_2Args(GraphQLClientConfig.Local.TenantId, clientId, username, password));

            return token;
        }

        private async Task<policy> SearchPolicyById(string policyId)
        {
            string? query = new QueryBuilder().policies(new QueryBuilder.policiesArgs(where: new policyWhereInput { id = policyId }),
                new policiesBuilder()
                    .list(new policyBuilder()
                        .id()
                        .source()
                        .jackets(new jacketInstanceBuilder()
                            .id()
                            .jacket(new jacketBuilder()
                                .id()
                                .title()
                                .clauses(new jacketBuilder.clausesArgs(), new clauseBuilder()
                                    .id()
                                    .storeTemplateByValue()
                                    .template(new templateInterfaceBuilder()
                                        .clauseHtmlFragment(new clauseHtmlBuilder()
                                            .id()
                                            .html()))
                                    .renderedHtmlResult(new stringResultBuilder()
                                    .value()))))
                        .clauses(new policyBuilder.clausesArgs(), new clauseBuilder()
                            .id()
                            .storeTemplateByValue()
                            .template(new templateInterfaceBuilder()
                                .clauseHtmlFragment(new clauseHtmlBuilder()
                                    .id()
                                    .html()))
                            .renderedHtmlResult(new stringResultBuilder()
                            .value())))).Build();

            policies policies = await _client.SendQueryAsync<policies>(query);
            return policies.list!.First()!;
        }

        async Task<(string?, createDataSchemaInput)> CreateDataSchema(string? name = null)
        {
            createDataSchemaInput input = new()
            {
                name = name ?? "test dataSchema",
                description = "new descrption",
                schema = "{}",
                standard = new dataSchemaStandardInput
                {
                    type = dataSchemaStandardTypeEnum.STATE_CHART,
                    version = "new version"
                },
                type = "some type"
            };

            return (await DataSchema.Create(input), input);
        }

        async Task UpdateCase(string caseId, updateCaseInput input)
        {
            string mutation = new MutationBuilder()
                .updateCase(new MutationBuilder.updateCaseArgs(caseId, input), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAsync<result>(mutation);
        }

        async Task<List<@case>> SearchAllCases()
        {
            string query = new QueryBuilder()
                .cases(new QueryBuilder.casesArgs(new caseWhere()), new casesBuilder()
                    .list(new caseBuilder()
                        .id()
                        .name()
                        .fields()
                        .fieldsSchema(new dataSchemaBuilder()
                            .id()
                            .name()
                            .description()
                            .schema()
                            .standard(new dataSchemaStandardBuilder()
                                .type()
                                .version()
                            )
                            .type()
                        )
                        .workflowSchema(new dataSchemaBuilder()
                            .id()
                            .name()
                            .description()
                            .schema()
                            .standard(new dataSchemaStandardBuilder()
                                .type()
                                .version()
                            )
                        .type())))
                .Build();

            cases cases = await _client.SendQueryAsync<cases>(query);
            return cases.list!.ToList()!;
        }

        async Task<@case?> SearchCaseById(string id)
        {
            string query = new QueryBuilder()
                .cases(new QueryBuilder.casesArgs(new caseWhere { id = id }), new casesBuilder()
                    .list(new caseBuilder()
                        .id()
                        .name()
                        .fields()
                        .fieldsSchema(new dataSchemaBuilder()
                            .id()
                            .name()
                            .description()
                            .schema()
                            .standard(new dataSchemaStandardBuilder()
                                .type()
                                .version()
                            )
                            .type()
                        )
                        .workflowSchema(new dataSchemaBuilder()
                            .id()
                            .name()
                            .description()
                            .schema()
                            .standard(new dataSchemaStandardBuilder()
                                .type()
                                .version()
                            )
                        .type())))
                .Build();

            cases cases = await _client.SendQueryAsync<cases>(query);
            return cases.list?.First();
        }

        async Task<(string loginId, string username, string password)> CreateLogin()
        {
            string username = CreateNewGuid();
            createLoginInput input = new()
            {
                username = username,
                clientId = await CreateClientId(),
                email = $"{username}@covergo.com",
                isEmailConfirmed = true,
                password = CreateNewGuid()
            };

            return (await Login.Create(input), username, input.password);
        }

        async Task<string> CreateClientId()
        {
            string appId = CreateNewGuid();
            string mutation = new MutationBuilder()
                .createApp(new MutationBuilder.createAppArgs(new createAppInput { appId = appId, appName = CreateNewGuid() }), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);

            return appId;
        }

        Task AddClientIdToGroup(string clientId, string groupId) =>
            AddPermissionToGroup(groupId, "clientId", clientId);

        Task AddPermissionToGroup(string groupId, string permission, string value)
        {
            string mutation = new MutationBuilder()
                .addPermissionToPermissionGroup(new MutationBuilder.addPermissionToPermissionGroupArgs(groupId, permission, value), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        async Task<@case?> SearchCase(caseWhere where)
        {
            string query = new QueryBuilder()
                .cases(new QueryBuilder.casesArgs(where), new casesBuilder()
                     .list(new caseBuilder()
                        .id()
                        .name()
                        .caseNumber()
                        .events(new detailedEventLogBuilder()
                            .type()
                            .valueAsString())
                        .fields()
                        .fieldsSchema(new dataSchemaBuilder()
                            .id()
                            .name()
                            .description()
                            .schema()
                            .standard(new dataSchemaStandardBuilder()
                                .type()
                                .version()
                            )
                            .type()
                        )
                        .proposals(new caseBuilder.proposalsArgs(), new proposalBuilder().proposalNumber())
                        .workflowSchema(new dataSchemaBuilder()
                            .id()
                            .name()
                            .description()
                            .schema()
                            .standard(new dataSchemaStandardBuilder()
                                .type()
                                .version()
                            )
                        .type())))
                .Build();

            cases cases = await _client.SendQueryAsync<cases>(query);
            return cases.list?.FirstOrDefault();
        }
    }
}