﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class ProposalReferralCodeTests : TestsBase
    {
        public ProposalReferralCodeTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_case_and_active_internal_WHEN_add_proposal_with_referral_code_THEN_it_is_added_with_referral_code()
        {
            string caseName = CreateNewGuid();
            string caseId = await CreateCase(caseName);
            string internalCode = CreateNewGuid();
            await Entity.CreateInternal(new createInternalInput { isActive = true, internalCode = internalCode });

            string proposalId = await Proposal.Create(caseId, new addProposalInput
            {
                referralCode = internalCode
            });

            IReadOnlyCollection<proposal> proposals = await SearchProposals(new caseWhere
            {
                id = caseId
            });

            proposals.FirstOrDefault(p => p.id == proposalId)!.referralCode.Should().Be(internalCode);
        }

        [Fact]
        public async Task GIVEN_case_and_inactive_internal_WHEN_add_proposal_with_referral_code_THEN_it_is_added_without_referral_code()
        {
            string caseName = CreateNewGuid();
            string caseId = await CreateCase(caseName);
            string internalCode = CreateNewGuid();
            await Entity.CreateInternal(new createInternalInput { isActive = false, internalCode = internalCode });

            string proposalId = await Proposal.Create(caseId, new addProposalInput
            {
                referralCode = internalCode
            });

            IReadOnlyCollection<proposal> proposals = await SearchProposals(new caseWhere
            {
                id = caseId
            });

            proposals.FirstOrDefault(p => p.id == proposalId)!.referralCode.Should().BeNull();
        }

        async Task<IReadOnlyCollection<proposal>> SearchProposals(caseWhere where)
        {
            string query = new QueryBuilder()
                .cases(new QueryBuilder.casesArgs(where), new casesBuilder()
                    .list(new caseBuilder()
                        .id()
                        .proposals(new caseBuilder.proposalsArgs(), new proposalBuilder()
                            .id()
                            .referralCode())))
                .Build();

            cases cases = await _client.SendQueryAsync<cases>(query);
            return cases.list!.SelectMany(c => c!.proposals!).ToArray()!;
        }

        Task<string> CreateCase(string? name = null) =>
            Case.Create(new createCaseInput { name = name ?? "test case", });
    }
}