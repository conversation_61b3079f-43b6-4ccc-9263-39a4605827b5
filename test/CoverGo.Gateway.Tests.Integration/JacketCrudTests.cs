using CoverGo.Gateway.Client;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class JacketCrudTests : TestsBase
    {
        public JacketCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_jackets_WHEN_create_without_clauses_THEN_succeed_and_created()
        {
            createJacketInput input = new() { title = "title" };
            string jacketId = await Jacket.Create(input);
            jacket? jacket = await SearchJacketById(jacketId);
            jacket.Should().NotBeNull();
            jacket!.title.Should().Be("title");
        }

        [Fact]
        public async Task GIVEN_jackets_WHEN_create_with_clauses_THEN_succeed_and_created()
        {
            createJacketInput input = new()
            {
                title = "title",
                clauses = new List<addClauseToJacketInput?>
                {
                    new()
                    {
                        id =  Guid.NewGuid().ToString(),
                        templateId = Guid.NewGuid().ToString(),
                    }
                }
            };
            string jacketId = await Jacket.Create(input);
            jacket? jacket = await SearchJacketById(jacketId);
            jacket.Should().NotBeNull();
            jacket!.title.Should().Be("title");
            jacket.clauses.Should().HaveCount(1);
            jacket.clauses!.First()!.id.Should().BeEquivalentTo(input.clauses.First()!.id);
        }

        [Fact]
        public async Task GIVEN_a_created_jacket_WHEN_update_this_jacket_THEN_succeed_and_updated()
        {
            createJacketInput createJacketInput = new()
            {
                title = "title",
                clauses = new List<addClauseToJacketInput?>
                {
                    new()
                    {
                        id =  Guid.NewGuid().ToString(),
                        templateId = Guid.NewGuid().ToString(),
                    }
                }
            };

            string jacketId = await Jacket.Create(createJacketInput);
            updateJacketInput updateJacketInput = new()
            {
                title = "updated-title",
                status = "archived",
                clauses = new List<addClauseToJacketInput?>
                {
                    new()
                    {
                        id =  Guid.NewGuid().ToString(),
                        templateId = Guid.NewGuid().ToString(),
                    },
                    new()
                    {
                        id =  Guid.NewGuid().ToString(),
                        templateId = Guid.NewGuid().ToString(),
                    }
                }
            };

            Func<Task> action = () => Jacket.Update(jacketId, updateJacketInput);
            await action.Should().NotThrowAsync<Exception>();

            jacket? jacket = await SearchJacketById(jacketId);
            jacket.Should().NotBeNull();
            jacket!.title.Should().Be("updated-title");
            jacket.status.Should().Be("archived");
            jacket.clauses.Should().HaveCount(2);
        }

        [Fact]
        public async Task GIVEN_a_created_jacket_WHEN_delete_this_jacket_THEN_succeed_and_deleted()
        {
            createJacketInput createJacketInput = new()
            {
                title = "title",
                clauses = new List<addClauseToJacketInput?>
                {
                    new()
                    {
                        id =  Guid.NewGuid().ToString(),
                        templateId = Guid.NewGuid().ToString(),
                    }
                }
            };

            string jacketId = await Jacket.Create(createJacketInput);


            await Jacket.Delete(jacketId);

            jacket? jacket = await SearchJacketById(jacketId);
            jacket.Should().BeNull();
        }

        async Task<jacket?> SearchJacketById(string id)
        {
            string query = new QueryBuilder()
                .jackets(new QueryBuilder.jacketsArgs(where: new jacketWhereInput { id = id }), new jacketsBuilder()
                    .list(new jacketBuilder()
                        .id()
                        .title()
                        .status()
                        .clauses(new jacketBuilder.clausesArgs(), new clauseBuilder()
                            .id()
                            .order()
                            )))
                .Build();

            jackets jackets = await _client.SendQueryAsync<jackets>(query);
            return jackets.list!.FirstOrDefault();
        }
    }
}