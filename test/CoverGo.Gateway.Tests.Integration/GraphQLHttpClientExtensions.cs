using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using CoverGo.Gateway.Client;

using GraphQL;
using GraphQL.Client.Http;

using MongoDB.Bson;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    static class GraphQLHttpClientExtensions
    {
        public static async Task<TResponse> CatchInternalServerError<TResponse>(Func<Task<TResponse>> func)
        {
            try
            {
                return await func();
            }
            catch (GraphQLHttpRequestException exception)
            {
                var output = TestContext.AsyncLocalTestContext.Value;
                output.WriteLine(string.Join(Environment.NewLine,
                    "GraphQL Error Exception details",
                    exception.StatusCode.ToString(),
                    exception.Message,
                    exception.Content,
                    exception.Data.ToJson()));
                throw new Exception(
                    JsonConvert.SerializeObject(new
                    {
                        exception.StatusCode,
                        exception.Content,
                        exception.Message,
                        exception.Data,
                    }),
                    exception);
            }
        }

        public static async Task<TResponse> SendMutationAsync<TResponse>(this GraphQLHttpClient client, string mutation)
            where TResponse : class
        {
            return await CatchInternalServerError(async () =>
            {
                GraphQLResponse<JToken> response = await client.SendMutationAsync<JToken>(new GraphQLHttpRequest(mutation));
                Validate(response);

                return response.Data.First?.First?.ToObject<TResponse>()
                    ?? throw new Exception("Cannot deserialize result");
            });
        }

        public static async Task<TResponse> SendQueryAsync<TResponse>(this GraphQLHttpClient client, string query)
            where TResponse : class
        {
            GraphQLResponse<JToken> response = await client.SendQueryAsync<JToken>(new GraphQLHttpRequest(query));
            Validate(response);

            return response.Data.First?.First?.ToObject<TResponse>()
                ?? throw new Exception("Cannot deserialize result");
        }

        public static Task<TResponse> SendMutationAsync<TResponse>(this GraphQLHttpClient client, MutationBuilder mutation)
            where TResponse : class =>
            client.SendMutationAsync<TResponse>(mutation.Build());

        public static Task<TResponse> SendQueryAsync<TResponse>(this GraphQLHttpClient client, QueryBuilder query)
            where TResponse : class =>
            client.SendQueryAsync<TResponse>(query.Build());

        public static Task<TResponse> SendAsync<TResponse>(this MutationBuilder mutation, GraphQLHttpClient client)
            where TResponse : class =>
            client.SendMutationAsync<TResponse>(mutation.Build());

        public static Task<TResponse> SendAsync<TResponse>(this QueryBuilder query, GraphQLHttpClient client)
            where TResponse : class =>
            client.SendMutationAsync<TResponse>(query.Build());

        static void Validate<TResponse>(GraphQLResponse<TResponse> response)
        {
            if (response.Errors?.Any() == true)
            {
                var error = response.Errors.First();
                var exceptionMessage = error.Message;
                if (error?.Extensions?.TryGetValue("message", out var extraMessage) == true)
                {
                    exceptionMessage += Environment.NewLine + extraMessage;
                }
                throw new Exception(exceptionMessage);
            }
        }

        public static async Task<string> CreateAndReturnId(this GraphQLHttpClient client, string creationMutation)
        {
            createdStatusResult result = await client.SendMutationAsync<createdStatusResult>(creationMutation);
            return result.TryGetId();
        }

        public static async Task<ICollection<string?>?> CreateAndReturnIds(this GraphQLHttpClient client, string creationMutation)
        {
            createdStatusResult result = await client.SendMutationAsync<createdStatusResult>(creationMutation);
            return result.TryGetIds();
        }

        public static string TryGetId(this createdStatusResult result)
        {
            if (result.errors?.Any() == true)
                throw new Exception(result.errors.First());

            if (result.createdStatus?.id == null)
                throw new Exception("created id equals null");

            return result.createdStatus.id;
        }

        public static ICollection<string?>? TryGetIds(this createdStatusResult result)
        {
            if (result.errors?.Any() == true)
                throw new Exception(result.errors.First());

            if (result.createdStatus?.ids == null)
                throw new Exception("created id equals null");

            return result.createdStatus.ids;
        }

        public static async Task SendMutationAndEnsureSuccessAsync(this GraphQLHttpClient client, string mutation)
        {
            result result = await client.SendMutationAsync<result>(mutation);
            if (result.errors?.Any() == true)
                throw new Exception(result.errors.First());
            if (result.errors_2?.Any() == true)
                throw new Exception(result.errors_2.First().message);

            if (result.status != "success")
                throw new Exception("operation was unsuccessful");
        }
    }
}