﻿using System;
using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class InviteEntityToLoginTests : TestsBase
    {
        public InviteEntityToLoginTests(ITestOutputHelper output) : base(output)
        {
        }

        [Theory]
        [InlineData(null, true)]
        [InlineData(true, true)]
        [InlineData(false, false)]
        public async Task GIVEN_WHEN_inviteEntityToLogin_with_useDefaultPermissions_param_THEN_expected_set_of_permissions_set(bool? useDefaultPermissions, bool isClientIdPermissionExists)
        {
            string entityId = await Entity.CreateIndividual();

            string clientId = await CreateClientId();
            string username = Guid.NewGuid().ToString("N");

            await Login.InviteEntityToLogin(clientId, new inviteEntityInput()
            {
                entityId = entityId,
                useDefaultPermissions = useDefaultPermissions,
                username = username,
                email = $"{Guid.NewGuid():N}@mail.com"
            });

            login login = await Login.GetLogin(username);

            login.targettedPermissions!
                .Any(p => p!.permission!.id == "clientId")
                .Should()
                .Be(isClientIdPermissionExists);
        }

        [Fact]
        public async Task GIVEN_WHEN_inviteEntityToLogin_with_permission_groups_THEN_expected_set_of_permissions_set()
        {
            string entityId = await Entity.CreateIndividual();
            string clientId = await CreateClientId();
            string username = Guid.NewGuid().ToString("N");
            string permissionGroupId = await PermissionGroup.Create();

            await Login.InviteEntityToLogin(clientId, new inviteEntityInput()
            {
                entityId = entityId,
                useDefaultPermissions = false,
                username = username,
                email = $"{Guid.NewGuid():N}@mail.com",
                permissionGroupIds = new List<string> { permissionGroupId }
            });

            login login = await Login.GetLogin(username);

            login.permissionGroups!.Single()!.id.Should().Be(permissionGroupId);
        }

        async Task<string> CreateClientId()
        {
            string appId = CreateNewGuid();
            string mutation = new MutationBuilder()
                .createApp(new MutationBuilder.createAppArgs(new createAppInput
                {
                    appId = appId,
                    appName = CreateNewGuid(),
                    redirectUris = new[]{$"http://test.test/{appId}"}
                }), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);

            return appId;
        }
    }
}