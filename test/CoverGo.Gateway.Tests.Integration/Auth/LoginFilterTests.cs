﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class LoginFilterTests : TestsBase
    {
        public LoginFilterTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_login_WHEN_query_with_email_filter_THEN_receive_login()
        {
            string email = $"{CreateNewGuid()}@covergo.com";
            await Login.Create(new createLoginInput
            {
                username = CreateNewGuid(),
                password = "testingPassword123",
                isEmailConfirmed = true,
                clientId = CreateNewGuid(),
                email = email
            });

            loginWhere filter = new() { email_in = new List<string?> { email } };
            login? login = await SearchLogin(filter);

            login!.email.Should().Be(email);
        }

        [Fact]
        public async Task GIVEN_login_WHEN_query_with_id_and_createdAt_gt_filter_THEN_receive_login()
        {
            string email = $"{CreateNewGuid()}@covergo.com";
            string createdId = await Login.Create(new createLoginInput
            {
                username = CreateNewGuid(),
                password = "testingPassword123",
                isEmailConfirmed = true,
                clientId = CreateNewGuid(),
                email = email
            });

            loginWhere filter = new()
            {
                ids = new List<string?> { createdId },
                createdAt_gt = DateTime.UtcNow.AddMinutes(-1)
            };
            login? login = await SearchLogin(filter);

            login!.id.Should().Be(createdId);
            login.createdAt.Should().BeAfter(DateTime.UtcNow.AddMinutes(-1));
        }

        [Fact]
        public async Task GIVEN_login_WHEN_query_with_id_and_wrong_createdAt_gt_filter_THEN_do_not_receive_login()
        {
            string email = $"{CreateNewGuid()}@covergo.com";
            string createdId = await Login.Create(new createLoginInput
            {
                username = CreateNewGuid(),
                password = "testingPassword123",
                isEmailConfirmed = true,
                clientId = CreateNewGuid(),
                email = email
            });

            loginWhere filter = new()
            {
                ids = new List<string?> { createdId },
                createdAt_gt = DateTime.UtcNow.AddMinutes(1)
            };
            login? login = await SearchLogin(filter);

            login.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_login_WHEN_query_with_id_and_createdAt_lt_filter_THEN_receive_login()
        {
            string email = $"{CreateNewGuid()}@covergo.com";
            string createdId = await Login.Create(new createLoginInput
            {
                username = CreateNewGuid(),
                password = "testingPassword123",
                isEmailConfirmed = true,
                clientId = CreateNewGuid(),
                email = email
            });

            loginWhere filter = new()
            {
                ids = new List<string?> { createdId },
                createdAt_lt = DateTime.UtcNow
            };
            login? login = await SearchLogin(filter);

            login!.id.Should().Be(createdId);
            login.createdAt.Should().BeBefore(DateTime.UtcNow);
        }

        [Fact]
        public async Task GIVEN_login_WHEN_query_with_id_and_wrong_createdAt_lt_filter_THEN_do_not_receive_login()
        {
            string email = $"{CreateNewGuid()}@covergo.com";
            string createdId = await Login.Create(new createLoginInput
            {
                username = CreateNewGuid(),
                password = "testingPassword123",
                isEmailConfirmed = true,
                clientId = CreateNewGuid(),
                email = email
            });

            loginWhere filter = new()
            {
                ids = new List<string?> { createdId },
                createdAt_lt = DateTime.UtcNow.AddMinutes(-1)
            };
            login? login = await SearchLogin(filter);

            login.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_login_WHEN_query_with_id_and_createdAt_gt_and_createdAt_lt_filter_THEN_receive_login()
        {
            string email = $"{CreateNewGuid()}@covergo.com";
            string createdId = await Login.Create(new createLoginInput
            {
                username = CreateNewGuid(),
                password = "testingPassword123",
                isEmailConfirmed = true,
                clientId = CreateNewGuid(),
                email = email
            });

            loginWhere filter = new()
            {
                ids = new List<string?> { createdId },
                createdAt_lt = DateTime.UtcNow.AddMinutes(2),
                createdAt_gt = DateTime.UtcNow.AddMinutes(-1)
            };
            login? login = await SearchLogin(filter);

            login!.id.Should().Be(createdId);
        }

        [Fact]
        public async Task GIVEN_login_WHEN_query_with_id_and_wrong_createdAt_gt_and_createdAt_lt_filter_THEN_do_not_receive_login()
        {
            string email = $"{CreateNewGuid()}@covergo.com";
            string createdId = await Login.Create(new createLoginInput
            {
                username = CreateNewGuid(),
                password = "testingPassword123",
                isEmailConfirmed = true,
                clientId = CreateNewGuid(),
                email = email
            });

            loginWhere filter = new()
            {
                ids = new List<string?> { createdId },
                createdAt_lt = DateTime.UtcNow.AddMinutes(1),
                createdAt_gt = DateTime.UtcNow.AddMinutes(1)
            };
            login? login = await SearchLogin(filter);

            login!.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_login_WHEN_query_with_id_and_createdAt_gt_and_wrong_createdAt_lt_filter_THEN_do_not_receive_login()
        {
            string email = $"{CreateNewGuid()}@covergo.com";
            string createdId = await Login.Create(new createLoginInput
            {
                username = CreateNewGuid(),
                password = "testingPassword123",
                isEmailConfirmed = true,
                clientId = CreateNewGuid(),
                email = email
            });

            loginWhere filter = new()
            {
                ids = new List<string?> { createdId },
                createdAt_lt = DateTime.UtcNow.AddMinutes(-1),
                createdAt_gt = DateTime.UtcNow.AddMinutes(-1)
            };
            login? login = await SearchLogin(filter);

            login.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_two_logins_WHEN_query_by_names_with_or_statement_THEN_returns_two_logins()
        {
            string name1 = CreateNewGuid();
            string name2 = CreateNewGuid();

            await Login.Create(name1);
            await Login.Create(name2);

            ICollection<login?> logins = await SearchLogins(new loginWhere
            {
                or = new List<loginWhere?>
                {
                    new() { usernames = new[] { name1 }},
                    new() { usernames = new[] { name2 }},
                }
            });

            logins!.Count.Should().Be(2);
        }

        [Fact]
        public async Task Given_login_with_no_password_WHEN_query_by_email_THEN_return_login()
        {
            string? name = CreateNewGuid();

            createLoginInput input = new()
            {
                username = name,
                isEmailConfirmed = true,
                clientId = CreateNewGuid(),
                email = $"{name}@covergo.com"
            };

            await Login.Create(input);

            login? login = await SearchLogin(new loginWhere { email_in = new[] { $"{name}@covergo.com" } });
            login.Should().NotBeNull();
        }

        [Fact]
        public async Task GIVEN_login_WHEN_query_by_name_and_id_with_and_statement_THEN_successfully_return_login()
        {
            string name = CreateNewGuid();

            string loginId = await Login.Create(name);

            login? login = await SearchLogin(new loginWhere
            {
                and = new List<loginWhere?>
                {
                    new() { usernames = new[] { name }},
                    new() { ids = new[]{ loginId } },
                }
            });

            login!.username.Should().Be(name);
            login.id.Should().Be(loginId);
        }

        async Task<login?> SearchLogin(loginWhere where)
        {
            ICollection<login?> logins = await SearchLogins(where);
            return logins.FirstOrDefault();
        }

        async Task<ICollection<login?>> SearchLogins(loginWhere where)
        {
            string query = new QueryBuilder()
                .logins(new QueryBuilder.loginsArgs(where: where), new loginsBuilder()
                    .list(new loginBuilder()
                        .id()
                        .username()
                        .email()
                        .createdAt()))
                .Build();

            logins logins = await _client.SendQueryAsync<logins>(query);
            return logins.list!;
        }
    }
}