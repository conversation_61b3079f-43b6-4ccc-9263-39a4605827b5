﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class PermissionSchemaCRUDTests : TestsBase
    {
        public PermissionSchemaCRUDTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_permissionSchema_WHEN_create_THEN_returns_id()
        {
            createPermissionSchemaInput createPermissionSchemaInput = new()
            {
                name = "test permissionSchema",
                description = "new description",
                objectType = CreateNewGuid(),
                actionType = permissionSchemaActionType.READ,
                schema = "{}",
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.GREATER_THAN,
                    path = "foo.bar",
                    value = new scalarValueInput { stringValue = "test value" }
                },
                updateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.LESS_THAN,
                    path = "foo.bar.test",
                    value = new scalarValueInput { stringValue = "test value 2" }
                }
            };

            string id = await PermissionSchema.Create(createPermissionSchemaInput);

            id.Should().NotBeNullOrWhiteSpace();
        }

        [Fact]
        public async Task GIVEN_permissionSchema_added_WHEN_request_this_permissionSchema_THEN_returns_permissionSchema()
        {
            (string id, createPermissionSchemaInput createPermissionSchemaInput) = await PermissionSchema.Create();

            permissionSchema? permissionSchema = await SearchPermissionSchema(id);

            permissionSchema.ShouldHaveSameValuesAsInput(createPermissionSchemaInput);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_added_WHEN_update_this_permissionSchema_THEN_succeed_and_updated()
        {
            (string id, _) = await PermissionSchema.Create();

            updatePermissionSchemaInput updatePermissionSchemaInput = new()
            {
                permissionSchemaId = id,
                name = "updated value",
                description = "updated description",
                objectType = CreateNewGuid(),
                actionType = permissionSchemaActionType.WRITE,
                schema = "{}",
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.IN,
                    path = "foo.bar.bar",
                    value = new scalarValueInput { stringValue = CreateNewGuid() }
                },
                updateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.ARRAY_CONTAINS,
                    path = "foo.bar.test.test",
                    value = new scalarValueInput { stringValue = CreateNewGuid() }
                }
            };

            await UpdatePermissionSchema(updatePermissionSchemaInput);

            permissionSchema? permissionSchema = await SearchPermissionSchema(id);

            permissionSchema.ShouldHaveSameValuesAsInput(updatePermissionSchemaInput);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_added_WHEN_delete_this_permissionSchema_THEN_succeed_and_deletes()
        {
            (string id, _) = await PermissionSchema.Create();

            await DeletePermissionSchema(id);

            permissionSchema? noPermissionSchema = await SearchPermissionSchema(id);
            noPermissionSchema.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_permissionSchema_created_WHEN_add_it_to_login_THEN_succeed()
        {
            (string permissionSchemaId, _) = await PermissionSchema.Create();
            string loginId = await Login.Create();

            string[] expectedIds = { CreateNewGuid(), CreateNewGuid() };
            await AddPermissionSchemaToLogin(loginId, permissionSchemaId, expectedIds);

            login login = await FindLogin(loginId);

            login.targetedPermissionSchemas!.Single()!.permissionSchema!.id.Should().Be(permissionSchemaId);
            login.targetedPermissionSchemas!.Single()!.targetIds.Should().BeEquivalentTo(expectedIds);
        }

        [Fact]
        public async Task GIVEN_login_with_targetedPermissionSchemas_WHEN_add_more_ids_THEN_succeed()
        {
            (string permissionSchemaId, _) = await PermissionSchema.Create();
            string loginId = await Login.Create();
            string[] expectedIds1 = { CreateNewGuid(), CreateNewGuid() };
            await AddPermissionSchemaToLogin(loginId, permissionSchemaId, expectedIds1);

            string[] expectedIds2 = { CreateNewGuid(), CreateNewGuid() };
            await AddPermissionSchemaToLogin(loginId, permissionSchemaId, expectedIds2);

            login login = await FindLogin(loginId);

            login.targetedPermissionSchemas!.Single()!.targetIds.Should().BeEquivalentTo(expectedIds1.Concat(expectedIds2));
        }

        [Fact]
        public async Task GIVEN_login_with_targetedPermissionSchemas_WHEN_remove_some_ids_THEN_still_has_the_rest_of_target_ids()
        {
            (string permissionSchemaId, _) = await PermissionSchema.Create();
            string loginId = await Login.Create();
            string[] expectedIds = { CreateNewGuid(), CreateNewGuid() };
            await AddPermissionSchemaToLogin(loginId, permissionSchemaId, expectedIds);

            string[] ids = { CreateNewGuid(), CreateNewGuid() };
            await AddPermissionSchemaToLogin(loginId, permissionSchemaId, ids);
            await RemovePermissionSchemaFromLogin(loginId, permissionSchemaId, ids);

            login login = await FindLogin(loginId);

            login.targetedPermissionSchemas!.Single()!.targetIds.Should().BeEquivalentTo(expectedIds);
        }

        [Fact]
        public async Task GIVEN_login_with_targetedPermissionSchemas_WHEN_remove_all_targets_THEN_succeed()
        {
            (string permissionSchemaId, _) = await PermissionSchema.Create();
            string loginId = await Login.Create();
            string[] ids = { CreateNewGuid(), CreateNewGuid() };
            await AddPermissionSchemaToLogin(loginId, permissionSchemaId, ids);

            await RemovePermissionSchemaFromLogin(loginId, permissionSchemaId, ids);

            login login = await FindLogin(loginId);

            login.targetedPermissionSchemas!.Single()!.targetIds.Should().BeNullOrEmpty();
        }

        async Task UpdatePermissionSchema(updatePermissionSchemaInput input)
        {
            string mutation = new MutationBuilder()
                .updatePermissionSchema(new MutationBuilder.updatePermissionSchemaArgs(input), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAsync<result>(mutation);
        }

        async Task DeletePermissionSchema(string id)
        {
            string mutation = new MutationBuilder()
                .deletePermissionSchema(new MutationBuilder.deletePermissionSchemaArgs(id), new resultBuilder()
                    .status()
                    .errors())
                .Build();

            await _client.SendMutationAsync<result>(mutation);
        }

        async Task<permissionSchema?> SearchPermissionSchema(string id)
        {
            string query = new QueryBuilder()
                .permissionSchemas(new QueryBuilder.permissionSchemasArgs(where: new permissionSchemaWhereInput { id = id }), new permissionSchemasBuilder()
                    .list(new permissionSchemaBuilder()
                        .id()
                        .name()
                        .objectType()
                        .actionType()
                        .description()
                        .schema()
                        .stateCondition(new fieldsWhereBuilder()
                            .WithAllFields())
                        .updateCondition(new fieldsWhereBuilder()
                            .WithAllFields())))
                .Build();

            permissionSchemas permissionSchemas = await _client.SendQueryAsync<permissionSchemas>(query);
            return permissionSchemas.list?.SingleOrDefault();
        }

        Task AddPermissionSchemaToLogin(string loginId, string permissionSchemaId, ICollection<string> targetIds)
        {
            string mutation = new MutationBuilder()
                .addTargetedPermissionSchemaToLogin(new MutationBuilder.addTargetedPermissionSchemaToLoginArgs(new addTargetedPermissionSchemaToLoginInput { loginId = loginId, permissionSchemaId = permissionSchemaId, targetIds = targetIds }), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        Task RemovePermissionSchemaFromLogin(string loginId, string permissionSchemaId, ICollection<string>? targetIds = null)
        {
            string mutation = new MutationBuilder()
                .removeTargetedPermissionSchemaFromLogin(new MutationBuilder.removeTargetedPermissionSchemaFromLoginArgs(new removeTargetedPermissionSchemaFromLoginInput { loginId = loginId, permissionSchemaId = permissionSchemaId, targetIds = targetIds }), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        async Task<login> FindLogin(string loginId)
        {
            string query = new QueryBuilder()
                .logins(new QueryBuilder.loginsArgs(ids: new[] { loginId }), new loginsBuilder()
                    .list(new loginBuilder()
                        .id()
                        .targetedPermissionSchemas(new targetedPermissionSchemaBuilder()
                            .permissionSchema(new permissionSchemaBuilder()
                                .WithAllFields())
                            .targetIds())))
                .Build();

            logins response = await _client.SendQueryAsync<logins>(query);

            return response.list!.Single()!;
        }
    }
}