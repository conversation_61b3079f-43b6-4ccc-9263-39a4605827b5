using CoverGo.Gateway.Client;
using FluentAssertions;
using GraphQL.Client.Http;
using GraphQL.Client.Serializer.Newtonsoft;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Auth
{
    public class LoginExpiredPasswordsTests : TestsBase
    {
        private static readonly MongoClient _mongoClient;
        static LoginExpiredPasswordsTests()
        {
            GraphQLClientConfig config = new();
            config.Load();
            Client = new GraphQLHttpClient(config.GatewayGraphQLUrl ?? "http://localhost:60060/graphql", new NewtonsoftJsonSerializer());
            _mongoClient = new MongoClient(Environment.GetEnvironmentVariable("DATABASE_CONNECT_STRING") ?? "mongodb://localhost:27017/?readPreference=primary&appname=MongoDB%20Compass&ssl=false");
        }

        public LoginExpiredPasswordsTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_login_with_expired_password_WHEN_trying_to_login_THEN_failure()
        {
            string clientId = await CreateAppAndPasswordValidator();

            string email = $"{CreateNewGuid()}@covergo.com";
            string username = CreateNewGuid();
            string password = CreateNewGuid();
            await Login.Create(new createLoginInput
            {
                username = username,
                password = password,
                isEmailConfirmed = true,
                clientId = clientId,
                email = email
            });

            await Task.Delay(TimeSpan.FromSeconds(12));

            token token = await GetAuthToken(clientId, username, password);
            token.errorDescription.Should().Be("Password expired.");
        }

        [Fact]
        public async Task GIVEN_login_with_expired_password_WHEN_trying_to_update_password_THEN_success()
        {
            string clientId = await CreateAppAndPasswordValidator();

            string email = $"{CreateNewGuid()}@covergo.com";
            string username = CreateNewGuid();
            string password = CreateNewGuid();
            await Login.Create(new createLoginInput
            {
                username = username,
                password = password,
                isEmailConfirmed = true,
                clientId = clientId,
                email = email
            });

            await Task.Delay(TimeSpan.FromSeconds(12));

            string newPassword = CreateNewGuid();
            result updateResult = await UpdateExpiredPasswordAsync(username, password, newPassword);
            updateResult.status.Should().Be("success");

            token token = await GetAuthToken(clientId, username, newPassword);
            token.error.Should().BeNull();
            token.accessToken.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task
            GIVEN_login_with_expired_password_WHEN_trying_to_update_password_and_provide_wrong_old_password_THEN_failure()
        {
            string clientId = await CreateAppAndPasswordValidator();

            string email = $"{CreateNewGuid()}@covergo.com";
            string username = CreateNewGuid();
            string password = CreateNewGuid();
            await Login.Create(new createLoginInput
            {
                username = username,
                password = password,
                isEmailConfirmed = true,
                clientId = clientId,
                email = email
            });

            await Task.Delay(TimeSpan.FromSeconds(12));

            string newPassword = CreateNewGuid();
            result updateResult = await UpdateExpiredPasswordAsync(username, "wrong-password", newPassword);
            updateResult.status.Should().Be("failure");
        }

        [Fact]
        public async Task GIVEN_login_with_not_expired_password_WHEN_trying_to_update_password_THEN_failure()
        {
            string clientId = await CreateAppAndPasswordValidator();

            string email = $"{CreateNewGuid()}@covergo.com";
            string username = CreateNewGuid();
            string password = CreateNewGuid();
            await Login.Create(new createLoginInput
            {
                username = username,
                password = password,
                isEmailConfirmed = true,
                clientId = clientId,
                email = email
            });

            string newPassword = CreateNewGuid();
            result updateResult = await UpdateExpiredPasswordAsync(username, password, newPassword);
            updateResult.status.Should().Be("failure");
        }

        private static async Task<result> UpdateExpiredPasswordAsync(string username, string oldPassword, string newPassword)
        {
            string mutation = new MutationBuilder()
                .updateExpiredPassword(
                    new MutationBuilder.updateExpiredPasswordArgs("covergo",
                        new changeExpiredPasswordInput
                        {
                            userName = username,
                            currentPassword = oldPassword,
                            newPassword = newPassword
                        }), new resultBuilder().status()).Build();

            return await Client.SendMutationAsync<result>(mutation);
        }

        private static async Task<token> GetAuthToken(string clientId, string username, string password)
        {
            string query = new QueryBuilder().token_2(
                    new QueryBuilder.token_2Args("covergo", clientId, username, password),
                    new tokenBuilder()
                        .accessToken()
                        .error()
                        .errorDescription())
                .Build();

            token token = await Client.SendQueryAsync<token>(query);
            return token!;
        }

        async Task CreateApp(createAppInput input)
        {
            string mutation = new MutationBuilder()
                .createApp(new MutationBuilder.createAppArgs(input), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);
        }

        async Task<string> CreateAppAndPasswordValidator()
        {
            string appIdGuid = CreateNewGuid();
            createAppInput input = new() { appId = appIdGuid, appName = appIdGuid, emailConfirmationTokenLifespan = TimeSpan.FromDays(2).TotalSeconds };
            await CreateApp(input);


            //TODO: replace with API call when it is developed
            IMongoCollection<BsonDocument>? collection = _mongoClient.GetDatabase("auth").GetCollection<BsonDocument>("covergo-passwordValidators");
            await collection.InsertOneAsync(BsonDocument.Parse($"{{\"clientId\":\"{appIdGuid}\",\"passwordLifespanInSeconds\":10,\"allowExpiredPasswordEasyReset\":true}}"));

            return appIdGuid;
        }

        private static GraphQLHttpClient Client { get; }
    }
}