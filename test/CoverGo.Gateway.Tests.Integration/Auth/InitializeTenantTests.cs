﻿using AutoFixture.Xunit2;
using CoverGo.Gateway.Client;
using FluentAssertions;
using GraphQL.Client.Http;
using Newtonsoft.Json.Linq;
using System;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Auth
{
    public class InitializeTenantTests : TestsBase
    {
        private const string _appConfig = @"{
	emails: {
		emailTemplateId: ""d1716ec0-1b97-4b74-8aab-cf3a8cd2eb41"",
		emailFrom: ""<EMAIL>"",
		emailSubject: ""Set your password"",
		forgotPasswordEmailTemplateId: ""a0b598ed-de3a-41a5-b211-2a59285a5121"",
		forgotPasswordEmailFrom: ""<EMAIL>"",
		forgotPasswordEmailFromName: ""CoverGo"",
		forgotPasswordEmailSubject: ""Forgot password"",
	},

	dateFormat: ""DD/MM/YYYY"",
	dateTimeFormat: ""DD/MM/YYYY HH:mm:ss"",
	name: ""Company ABC"",
	logoUrl: ""/covergo-logo-blue.svg"", // from public folder
	fileSystem: {
		bucketName: ""coverlibrary--uat"",
	},

	scripts: {
		pricingScriptSourceUrl: ""scripts/pricing_usage_based_group_0-0-1_bundled.txt"",
		underwritingScriptSourceUrl: ""scripts/underwriting_usage_based_group_0-0-1_bundled.txt"",
		cancellationScriptSourceUrl: ""scripts/cancellation_usage_based_group_0-0-1_bundled.txt"",
		policyYearBasedIndividualPricing: ""scripts/pricing_years_based_individual_bundled.txt"",
	},

	statuses: {
		initialCaseStatus: ""In Progress"",
		case: [
			{ name: ""In Progress"", value: ""In Progress"" },
			{ name: ""Accepted"", value: ""Accepted"" },
			{ name: ""Closed"", value: ""Closed"" },
		],

		initialProposalStatus: ""In Progress"",
		proposal: [
			{ name: ""In Progress"", value: ""In Progress"" },
			{ name: ""Accepted"", value: ""Accepted"" },
			{ name: ""Closed"", value: ""Closed"" },
		],
	},

	schemas: {
		arrayFields: {
			type: ""object"",
			properties: {
				beneficiaries: {
					type: ""array"",
					items: {
						type: ""object"",
						properties: {
							englishFirstName: {
								type: ""string"",
							},
							englishLastName: {
								type: ""string"",
							},
							relationship: {
								type: ""string"",
							},
							share: {
								type: ""number"",
							},
						},
					},
				},
			},
		},
		otherFields: {
			type: ""object"",
			properties: {
				hkid: {
					type: ""string"",
				},
			},
		},
	},

	menu: [
		{
			id: ""clients"",
			label: ""Clients"",
			description: """",
            icon: """",

            type: ""headline"",
		},
		{
			id: ""individuals"",
			label: ""Individuals"",
			description: ""Individuals"",
			icon: ""smile"",
			to: { name: ""ViewIndividualList"" },
		},
		{
            id: ""companies"",
			label: ""Companies"",
			description: ""Companies"",
			icon: ""shop"",
			to: { name: ""ViewCompanyList"" },
		},
		{
            id: ""lifecycle"",
			label: ""Contracts"",
			description: """",

            icon: """",

            type: ""headline"",
		},
		{
            id: ""products"",
			label: ""Products"",
			description: ""Products"",
			icon: ""tag"",
			to: { name: ""ViewProductList"" },
		},
		{
            id: ""quotes"",
			label: ""Quotes"",
			description: ""Quotes"",
			icon: ""fileText"",
			to: { name: ""ViewCaseList"" },
		},
		{
            id: ""policies"",
			label: ""Policies"",
			description: ""Policies"",
			icon: ""fileDone"",
			to: { name: ""ViewPolicyList"" },
		},
		{
            id: ""renewals"",
			label: ""Renewals"",
			description: ""Renewals"",
			icon: ""fileSync"",
			to: { name: ""ViewRenewalList"" },
		},
		{
            id: ""claims"",
			label: ""Claims"",
			description: ""Claims"",
			icon: ""safety"",
			to: { name: ""ViewClaimsList"" },
		},
		{
            id: ""lifecycle"",
			label: ""Transactions"",
			description: """",

            icon: """",

            type: ""headline"",
		},
		{
            id: ""transactions"",
			label: ""Transactions"",
			description: ""Transactions"",
			icon: ""creditCard"",
			to: { name: ""ViewTransactionList"" },
		},
		{
            id: ""stakeholders"",
			label: ""Stakeholders"",
			description: """",

            icon: """",

            type: ""headline"",
		},
		{
            id: ""users"",
			label: ""Internal Users"",
			description: ""Internal Users"",
			icon: ""user"",
			to: { name: ""ViewInternalUser"" },
		},
		{
            id: ""agents"",
			label: ""Agents"",
			description: ""Agents"",
			icon: ""team"",
			to: { name: ""View403"" },
		},
		{
            id: ""hierarchy"",
			label: ""Hierarchy"",
			description: ""Hierarchy"",
			icon: ""Apartment"",
			to: { name: ""View403"" },
		},
		{
            id: ""others"",
			label: ""Others"",
			description: """",

            icon: """",

            type: ""headline"",
		},
		{
            id: ""settings"",
			label: ""Settings"",
			description: ""Settings"",
			icon: ""setting"",
			to: { name: ""ViewSettings"" },
		},
	],
}";

        public InitializeTenantTests(ITestOutputHelper output) : base(output)
        {
        }

        [Theory]
        [AutoData]
        public async Task GIVEN_an_uninitialized_tenant_WHEN_initializing_tenant_with_hosts_and_application_with_configuration_for_host_THEN_app_config_is_can_be_retrieved_by_host(
            string appId, Uri hostUri)
        {
            string tenantId = "covergo_ni";

            string initializeTenantMutation = new MutationBuilder().initializeTenant(
                new MutationBuilder.initializeTenantArgs(tenantId,
                    new createAdminInput()
                    {
                        email = "<EMAIL>",
                        password = Guid.NewGuid().ToString(),
                        username = Guid.NewGuid().ToString(),
                    },
                    new string[] { hostUri.Host },
                    null,
                    new createAppInput[]
                    {
                        new createAppInput()
                        {
                            appId = appId,
                            appName = "Admin",
                            urlRouting = new urlRoutingInput()
                            {
                                url = hostUri.ToString()
                            },
                            appConfig = JToken.Parse(_appConfig).ToString(Newtonsoft.Json.Formatting.None).Escape()
                        }
                    }),
                new resultBuilder().WithAllFields()
            ).Build();

            using GraphQLHttpClient graphQlClient = await Setup.CreateGraphQLHttpClient();
            await graphQlClient.SendMutationAndEnsureSuccessAsync(initializeTenantMutation);

            string applicationConfigurationQuery = new QueryBuilder().appConfigurationQueryAppConfigurationFromUrl(
                new QueryBuilder.appConfigurationQueryAppConfigurationFromUrlArgs(hostUri.ToString()),
                new auth_AppConfigurationBuilder().WithAllFields()
            ).Build();

            auth_AppConfiguration applicationConfiguration = await graphQlClient.SendQueryAsync<auth_AppConfiguration>(applicationConfigurationQuery);

            applicationConfiguration.tenantId.Should().BeEquivalentTo(tenantId);
            applicationConfiguration.appId.Should().BeEquivalentTo(appId);
            JToken.Parse(applicationConfiguration.appConfig ?? "{}").Should().BeEquivalentTo(JToken.Parse(_appConfig));
        }
        [Theory]
        [InlineData("tenant_id")]
        [InlineData("tenant_id_123")]
        public async Task GIVEN_a_valid_tenant_id_WHEN_initializing_tenant_in_auth_THEN_error_is_returned(string tenantId)
        {
            string initializeTenantMutation = new MutationBuilder().initializeTenantAuth(
                new MutationBuilder.initializeTenantAuthArgs(tenantId),
                new resultBuilder().WithAllFields()).Build();

            using GraphQLHttpClient graphQlClient = await Setup.CreateGraphQLHttpClient();
            Func<Task> mutationCall = () => graphQlClient.SendMutationAndEnsureSuccessAsync(initializeTenantMutation);
            await mutationCall.Should().NotThrowAsync<Exception>();
        }


        [Theory]
        [InlineData("tenant-id")]
        [InlineData("tenant/id")]
        public async Task GIVEN_an_invalid_tenant_id_WHEN_initializing_tenant_in_auth_THEN_error_is_returned(string tenantId)
        {
            string initializeTenantMutation = new MutationBuilder().initializeTenantAuth(
                new MutationBuilder.initializeTenantAuthArgs(tenantId),
                new resultBuilder().WithAllFields()).Build();

            using GraphQLHttpClient graphQlClient = await Setup.CreateGraphQLHttpClient();
            Func<Task> mutationCall = () => graphQlClient.SendMutationAndEnsureSuccessAsync(initializeTenantMutation);
            await mutationCall.Should().ThrowAsync<Exception>();
        }
    }
}
