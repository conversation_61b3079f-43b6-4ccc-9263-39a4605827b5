using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Auth
{
    public class AuthTransactionIdReplacementTests : AuthReplacementTests
    {
        public AuthTransactionIdReplacementTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_login_has_permission_contains_transactionIdForClaimIdIfClaimant_and_a_claim_transaction_of_this_login_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_this_transactionId()
        {
            string username = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);

            string claimId = await Claim.Create(new createClaimInput { claimantId = entityId });
            string transactionId = await Transaction.Create(new createTransactionInput { claimId = claimId });

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readClaims", @"{claimIdIfClaimant}");
            await Login.AddPermission(login.id!, "readTransactions", @"{transactionIdForClaimIdIfClaimant}");

            login = await FindLogin(username);

            login.targettedPermissions!.Single(tp => tp!.permission!.id == "readTransactions" && tp.targetIds!.Contains(transactionId)).Should().NotBeNull();
        }

        [Fact]
        public async Task GIVEN_login_has_permission_contains_transactionIdForClaimIdIf_a_dependent_IsClaimant_and_a_claim_transaction_of_this_dependent_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_this_transactionId()
        {
            string username = CreateNewGuid();
            string dependentUsername = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string dependentEntityId = await Entity.CreateWithAssociatedLogin(dependentUsername);
            await Entity.AddLink(entityId, dependentEntityId, "child");

            string claimId = await Claim.Create(new createClaimInput { claimantId = dependentEntityId });
            string transactionId = await Transaction.Create(new createTransactionInput { claimId = claimId });

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readClaims", @"{claimIdIfClaimant}");
            await Login.AddPermission(login.id!, "readTransactions", @"{transactionIdForClaimIdIf{fromLinkSource:child:{entityId}}IsClaimant}");

            login = await FindLogin(username);

            login.targettedPermissions!.Single(tp => tp!.permission!.id == "readTransactions" && tp.targetIds!.Contains(transactionId)).Should().NotBeNull();
        }
    }
}