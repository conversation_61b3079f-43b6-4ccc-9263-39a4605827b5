using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Auth
{
    public class AuthPolicyIdReplacementTests : AuthReplacementTests
    {
        public AuthPolicyIdReplacementTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task
            GIVEN_login_has_permission_contains_policyIdIfUninsured_and_the_login_used_to_be_insured_by_a_policy_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_this_policyId()
        {
            string username = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);

            string policyId = await Policy.Create(new initializePolicyInput { insuredIds = new List<string?> { entityId } });

            await Policy.Issue(policyId);

            string endorsementId = await Policy.Endorsement.Add(policyId);
            await Policy.RemoveContractInsured(policyId, entityId, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);


            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readPolicies", @"{policyIdIfUninsured}");

            login = await FindLogin(username);

            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readPolicies" && tp.targetIds!.Contains(policyId)).Should()
                .NotBeNull();
        }

        [Fact]
        public async Task
            GIVEN_login_has_permission_contains_policyIdIf_a_dependent__IsUninsured_and_the_dependent_used_to_be_insured_by_a_policy_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_this_policyId()
        {
            string username = CreateNewGuid();
            string dependentUsername = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string dependentEntityId = await Entity.CreateWithAssociatedLogin(dependentUsername);

            await Entity.AddLink(entityId, dependentEntityId, "child");

            string policyId = await Policy.Create(new initializePolicyInput { insuredIds = new List<string?> { dependentEntityId } });
            await Policy.Issue(policyId);

            string endorsementId = await Policy.Endorsement.Add(policyId);
            await Policy.RemoveContractInsured(policyId, dependentEntityId, endorsementId);
            await Policy.Endorsement.Accept(policyId, endorsementId);


            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readPolicies",
                @"{policyIdIf{fromLinkSource:child:{entityId}}IsUninsured}");

            login = await FindLogin(username);

            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readPolicies" && tp.targetIds!.Contains(policyId)).Should()
                .NotBeNull();
        }

        [Fact]
        public async Task
            GIVEN_login_has_permission_contains_policyIdIfRenewal_and_a_policy_is_renewal_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_this_policyId()
        {
            string username = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string policyId = await Policy.Create(new initializePolicyInput { isRenewal = true });

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readPolicies", @"{policyIdIfRenewal}");

            login = await FindLogin(username);

            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readPolicies" && tp.targetIds!.Contains(policyId)).Should()
                .NotBeNull();
        }

        [Fact]
        public async Task
            GIVEN_login_has_permission_contains_allowedReadPolicies_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_policyIds_in_readPolicies()
        {
            string username = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string policyId1 = CreateNewGuid();
            string policyId2 = CreateNewGuid();

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readPolicies", policyId1);
            await Login.AddPermission(login.id!, "readPolicies", policyId2);
            await Login.AddPermission(login.id!, "readFiles", "policies/{allowedReadPolicies}");

            login = await FindLogin(username);

            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readFiles" && tp.targetIds!.Contains($"policies/{policyId1}"))
                .Should().NotBeNull();
            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readFiles" && tp.targetIds!.Contains($"policies/{policyId2}"))
                .Should().NotBeNull();

        }

        [Fact]
        public async Task
            GIVEN_login_has_permission_contains_policyIdIfInsured_and_the_login_is_insured_by_a_policy_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_this_policyId()
        {
            string username = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string policyId = await Policy.Create(new initializePolicyInput { insuredIds = new List<string?> { entityId } });

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readPolicies", @"{policyIdIfInsured}");

            login = await FindLogin(username);

            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readPolicies" && tp.targetIds!.Contains(policyId)).Should()
                .NotBeNull();
        }

        [Fact]
        public async Task
            GIVEN_login_has_permission_contains_policyIdIf_a_dependent_IsInsured_and_the_dependent_is_insured_by_a_policy_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_this_policyId()
        {
            string username = CreateNewGuid();
            string dependentUsername = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string dependentEntityId = await Entity.CreateWithAssociatedLogin(dependentUsername);

            await Entity.AddLink(entityId, dependentEntityId, "child");

            string policyId = await Policy.Create(new initializePolicyInput { insuredIds = new List<string?> { dependentEntityId } });

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readPolicies",
                "{policyIdIf{fromLinkSource:child:{entityId}}IsInsured}");

            login = await FindLogin(username);

            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readPolicies" && tp.targetIds!.Contains(policyId)).Should()
                .NotBeNull();
        }
    }
}