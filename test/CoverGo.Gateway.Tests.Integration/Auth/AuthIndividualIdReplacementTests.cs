using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Auth
{
    public class AuthIndividualIdReplacementTests : AuthReplacementTests
    {
        public AuthIndividualIdReplacementTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_two_logins_have_workingFor_relationship_with_another_login_and_one_of_them_has_claim_with_extend_readIndividuals_link_permission_WHEN_query_this_login_THEN_returns_login_with_claim_on_individualId_of_the_other_login()
        {
            string usernameOfMemberA = CreateNewGuid();
            string entityIdOfMemberA = await Entity.CreateWithAssociatedLogin(usernameOfMemberA);
            string usernameOfMemberB = CreateNewGuid();
            string entityIdOfMemberB = await Entity.CreateWithAssociatedLogin(usernameOfMemberB);
            string usernameOfCompanyA = CreateNewGuid();
            string entityIdOfCompanyA = await Entity.CreateWithAssociatedLogin(usernameOfCompanyA);

            await Entity.AddLink(entityIdOfMemberA, entityIdOfCompanyA, "workingFor");
            await Entity.AddLink(entityIdOfMemberB, entityIdOfCompanyA, "workingFor");

            login login = await FindLogin(usernameOfMemberA);

            await Login.AddPermission(login.id!, "readIndividuals", @"{fromLinkSource:workingFor:{entityId}}");
            await Login.AddPermission(login.id!, "readIndividuals", @"{fromLinkTarget:workingFor:{allowedReadIndividuals}}");
            login = await FindLogin(usernameOfMemberA);

            login.targettedPermissions!.Single(tp => tp!.permission!.id == "readIndividuals" && tp.targetIds!.Contains(entityIdOfMemberB)).Should().NotBeNull();
        }
    }
}