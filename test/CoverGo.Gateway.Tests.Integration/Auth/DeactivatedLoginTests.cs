﻿using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Permissions;
using FluentAssertions;
using GraphQL;
using GraphQL.Client.Http;
using Newtonsoft.Json.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Auth;

public class DeactivatedLoginTests : PermissionTestsBase
{
    public DeactivatedLoginTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task GIVEN_login_WHEN_query_me_THEN_receive_success()
    {
        PermissionTestContext? context = await new PermissionTestContextBuilder()
               .Build();

        GraphQLHttpClient? loginClient = context.Client;
        string? meQuery = new QueryBuilder().me(
                new loginBuilder().id()
            ).Build();
        login? res = await loginClient.SendQueryAsync<login>(meQuery);
        res.Should().NotBeNull();
    }

    [Fact]
    public async Task GIVEN_deactivated_login_WHEN_query_me_THEN_failure()
    {
        PermissionTestContext? context = await new PermissionTestContextBuilder()
               .Build();

        GraphQLHttpClient? adminClient = _client;
        string? loginId = context.LoginId;

        string? mutation = new MutationBuilder()
            .loginMutationDeactivate(
                new(loginId),
                new auth_ResultBuilder().WithAllFields()
            ).Build();

        await adminClient.SendMutationAndEnsureSuccessAsync(mutation);

        GraphQLHttpClient? loginClient = context.Client;
        string? meQuery = new QueryBuilder().me(
                new loginBuilder().id()
            ).Build();
        GraphQLResponse<JToken>? res = await loginClient.SendQueryAsync<JToken>(new GraphQLRequest(meQuery));
        res.Errors.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_deactivated_login_WHEN_reactivate_AND_query_me_THEN_failure()
    {
        PermissionTestContext? context = await new PermissionTestContextBuilder()
               .Build();

        GraphQLHttpClient? adminClient = _client;
        string? loginId = context.LoginId;

        string? deactivateMutation = new MutationBuilder()
            .loginMutationDeactivate(
                new(loginId),
                new auth_ResultBuilder().WithAllFields()
            ).Build();
        await adminClient.SendMutationAndEnsureSuccessAsync(deactivateMutation);

        string? reactivateMutation = new MutationBuilder()
            .loginMutationReactivate(
                new(loginId),
                new auth_ResultBuilder().WithAllFields()
            ).Build();
        await adminClient.SendMutationAndEnsureSuccessAsync(reactivateMutation);

        GraphQLHttpClient? loginClient = context.Client;
        string? meQuery = new QueryBuilder().me(
                new loginBuilder().id()
            ).Build();
        login? res = await loginClient.SendQueryAsync<login>(meQuery);
        res.Should().NotBeNull();
    }
}
