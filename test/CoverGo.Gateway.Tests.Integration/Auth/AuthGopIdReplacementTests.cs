﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Auth
{
    public class AuthGopIdReplacementTests : AuthReplacementTests
    {
        public AuthGopIdReplacementTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_login_and_its_gop_and_login_has_claim_gop_if_id_member_WHEN_query_this_login_THEN_returns_login_with_claim_on_this_gop_id()
        {
            string username = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            Guid gopId = await GuaranteeOfPayment.Create(new createGOPInput { memberId = entityId });

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readGOPs", @"{gopIdIfMember}");

            login = await FindLogin(username);

            login.targettedPermissions!.Single(tp => tp!.permission!.id == "readGOPs" && tp.targetIds!.Contains(gopId.ToString())).Should().NotBeNull();
        }

        [Fact]
        public async Task
            GIVEN_login_has_permission_contains_allowedReadGOPs_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_GopIds_in_readGOPs()
        {
            string username = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string gopId1 = CreateNewGuid();
            string gopId2 = CreateNewGuid();

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readGOPs", gopId1);
            await Login.AddPermission(login.id!, "readGOPs", gopId2);
            await Login.AddPermission(login.id!, "readFiles", "gops/{allowedReadGOPs}");

            login = await FindLogin(username);

            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readFiles" && tp.targetIds!.Contains($"gops/{gopId1}"))
                .Should().NotBeNull();
            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readFiles" && tp.targetIds!.Contains($"gops/{gopId2}"))
                .Should().NotBeNull();

        }

        [Theory]
        [InlineData("updateGOPs", "{allowedUpdateGOPs}")]
        [InlineData("writeGOPs", "{allowedWriteGOPs}")]
        public async Task
            GIVEN_login_has_permission_contains_allowedWriteGOPs_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_GopIds_in_readGOPs(
                string writePermissionName, string targetedPermissionPlaceholder)
        {
            string username = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string gopId1 = (await GuaranteeOfPayment.Create(new createGOPInput())).ToString();
            string gopId2 = (await GuaranteeOfPayment.Create(new createGOPInput())).ToString();

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, writePermissionName, gopId1);
            await Login.AddPermission(login.id!, writePermissionName, gopId2);
            await Login.AddPermission(login.id!, "readFiles", $"gops/{targetedPermissionPlaceholder}");

            login = await FindLogin(username);

            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readFiles" && tp.targetIds!.Contains($"gops/{gopId1}"))
                .Should().NotBeNull();
            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readFiles" && tp.targetIds!.Contains($"gops/{gopId2}"))
                .Should().NotBeNull();

        }
    }
}