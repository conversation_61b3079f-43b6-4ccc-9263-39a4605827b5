using CoverGo.Gateway.Client;
using FluentAssertions;
using Newtonsoft.Json.Linq;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests
{
    public static class TestContext
    {
        public static AsyncLocal<ITestOutputHelper> AsyncLocalTestContext = new AsyncLocal<ITestOutputHelper>();
    }
}

namespace CoverGo.Gateway.Tests.Integration.Auth
{

    public class UserStorageCrudTests : TestsBase
    {
        public UserStorageCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_user_WHEN_add_data_to_storage_THEN_successfully_added()
        {
            string key = CreateNewGuid();
            JToken fields = JToken.Parse("{ 'field1': 'value1' }");
            result createResult = await UserStorageItemCreateInternal(key, fields);

            createResult.status.Should().Be("success");
        }

        [Fact]
        public async Task GIVEN_user_with_one_storage_item_added_WHEN_update_item_in_the_storage_THEN_successfully_updated()
        {
            string key = CreateNewGuid();
            JToken fields = JToken.Parse("{ 'field1': 'value1' }");
            await UserStorageItemCreateInternal(key, fields);

            JToken fieldsUpdated = JToken.Parse("{ 'field1': 'value2' }");

            result updatedResult = await UserStorageItemUpdateInternal(key, fieldsUpdated);
            updatedResult.status.Should().Be("success");

            userStorageItems storedItems = await UserItemsSearch(new userStorageItemsWhereInput
            {
                key_contains = key
            });

            storedItems.count.Should().Be(1);

            userStorageItem? item = storedItems.list!.Single();

            item!.key.Should().Be(key);
            item.fields.Should().Be(fieldsUpdated.ToString());
        }

        [Fact]
        public async Task GIVEN_user_with_one_storage_item_added_WHEN_delete_item_in_the_storage_THEN_successfully_deleted()
        {
            string key = CreateNewGuid();
            JToken fields = JToken.Parse("{ 'field1': 'value1' }");
            await UserStorageItemCreateInternal(key, fields);

            result deletedResult = await UserStorageItemDeleteInternal(key);
            deletedResult.status.Should().Be("success");

            userStorageItems storedItems = await UserItemsSearch(new userStorageItemsWhereInput
            {
                key_contains = key
            });

            storedItems.count.Should().Be(0);
            storedItems.list.Should().BeEmpty();
        }

        private Task<userStorageItems> UserItemsSearch(userStorageItemsWhereInput where)
        {
            string query = new QueryBuilder()
                .userStorage(new userStorageItemsBuilder()
                    .count(where)
                    .list(new userStorageItemsBuilder.listArgs(where), new userStorageItemBuilder()
                        .key()
                        .fields()))
            .Build();

            return _client.SendQueryAsync<userStorageItems>(query);
        }

        private Task<result> UserStorageItemCreateInternal(string key, JToken fields)
        {
            string mutation = new MutationBuilder()
                .createUserStorageItem(new MutationBuilder.createUserStorageItemArgs(new userStorageItemCreateInput
                    {
                    key = key,
                    fields = fields.ToString().Escape()
                }),
                new resultBuilder()
                    .status())
                .Build();

            return _client.SendMutationAsync<result>(mutation);
        }

        private Task<result> UserStorageItemUpdateInternal(string key, JToken fields)
        {
            string mutation = new MutationBuilder()
                .updateUserStorageItem(new MutationBuilder.updateUserStorageItemArgs(new userStorageItemUpdateInput
                    {
                    key = key,
                    fields = fields.ToString().Escape()
                }),
                new resultBuilder()
                    .status())
                .Build();

            return _client.SendMutationAsync<result>(mutation);
        }

        private Task<result> UserStorageItemDeleteInternal(string key)
        {
            string mutation = new MutationBuilder()
                .deleteUserStorageItem(new MutationBuilder.deleteUserStorageItemArgs(key),
                new resultBuilder()
                    .status())
                .Build();

            return _client.SendMutationAsync<result>(mutation);
        }
    }
}
