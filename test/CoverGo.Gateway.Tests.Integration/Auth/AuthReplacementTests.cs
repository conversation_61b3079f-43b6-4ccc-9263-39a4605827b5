using CoverGo.Gateway.Client;
using System.Threading.Tasks;

using Xunit.Abstractions;

namespace CoverGo.Gateway.Tests.Integration.Auth
{
    public class AuthReplacementTests : TestsBase
    {
        public AuthReplacementTests(ITestOutputHelper output) : base(output)
        {
        }

        protected async Task<login> FindLogin(string username)
        {
            string query = new QueryBuilder()
                .login(new QueryBuilder.loginArgs(username), new loginBuilder()
                    .id()
                    .targettedPermissions(new targettedPermissionBuilder()
                        .permission(new permissionBuilder()
                            .id())
                        .targetIds()))
                .Build();

            return await _client.SendQueryAsync<login>(query);
        }
    }
}