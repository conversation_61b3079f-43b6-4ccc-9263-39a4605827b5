﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Auth
{
    public class AuthClaimIdReplacementTests : AuthReplacementTests
    {
        public AuthClaimIdReplacementTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_login_and_its_claim_and_login_has_claim_claimIfIdClaimant_WHEN_query_this_login_THEN_returns_login_with_claim_on_this_claim_id()
        {
            string username = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string claimId = await Claim.Create(new createClaimInput { claimantId = entityId });

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readClaims", @"{claimIdIfClaimant}");

            login = await FindLogin(username);

            login.targettedPermissions!.Single(tp => tp!.permission!.id == "readClaims" && tp.targetIds!.Contains(claimId)).Should().NotBeNull();
        }

        [Fact]
        public async Task GIVEN_login_has_permission_contains_claimIdIf_a_dependent_IsClaimant_and_a_claim_of_this_dependent_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_this_claimId()
        {
            string username = CreateNewGuid();
            string dependentUsername = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string dependentEntityId = await Entity.CreateWithAssociatedLogin(dependentUsername);
            await Entity.AddLink(entityId, dependentEntityId, "child");
            string claimId = await Claim.Create(new createClaimInput { claimantId = dependentEntityId });

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readClaims", @"{claimIdIf{fromLinkSource:child:{entityId}IsClaimant}");

            login = await FindLogin(username);

            login.targettedPermissions!.Single(tp => tp!.permission!.id == "readClaims" && tp.targetIds!.Contains(claimId)).Should().NotBeNull();
        }
    }
}