using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Auth
{
    public class AuthCaseIdReplacementTests : AuthReplacementTests
    {
        public AuthCaseIdReplacementTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_login_has_permission_contains_caseIdIf_a_dependent_IsHolder_and_is_case_holder__WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_this_caseId()
        {
            string username = CreateNewGuid();
            string dependentUsername = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string dependentEntityId = await Entity.CreateWithAssociatedLogin(dependentUsername);
            await Entity.AddLink(entityId, dependentEntityId, "child");

            string caseId = await Case.Create(new createCaseInput
            {
                holderId = dependentEntityId
            });

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readCases", @"{caseIdIf{fromLinkSource:child:{entityId}}IsHolder}");

            login = await FindLogin(username);

            login.targettedPermissions!.Single(tp => tp!.permission!.id == "readCases" && tp.targetIds!.Contains(caseId)).Should().NotBeNull();
        }

        [Fact]
        public async Task GIVEN_login_has_permission_contains_proposalIdIf_a_dependent_IsHolder_and_is_case_holder__WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_this_proposalId()
        {
            string username = CreateNewGuid();
            string dependentUsername = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string dependentEntityId = await Entity.CreateWithAssociatedLogin(dependentUsername);
            await Entity.AddLink(entityId, dependentEntityId, "child");

            string caseId = await Case.Create(new createCaseInput
            {
                holderId = dependentEntityId,
            });
            string proposalId = await Proposal.Create(caseId);

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readCases", @"{proposalIdIf{fromLinkSource:child:{entityId}}IsHolder}");

            login = await FindLogin(username);

            login.targettedPermissions!.Single(tp => tp!.permission!.id == "readCases" && tp.targetIds!.Contains(proposalId)).Should().NotBeNull();
        }

        [Fact]
        public async Task
     GIVEN_login_has_permission_contains_allowedReadCases_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_GopIds_in_readGOPs()
        {
            string username = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string caseId1 = CreateNewGuid();
            string caseId2 = CreateNewGuid();

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readCases", caseId1);
            await Login.AddPermission(login.id!, "readCases", caseId2);
            await Login.AddPermission(login.id!, "readFiles", "cases/{allowedReadCases}");

            login = await FindLogin(username);

            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readFiles" && tp.targetIds!.Contains($"cases/{caseId1}"))
                .Should().NotBeNull();
            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readFiles" && tp.targetIds!.Contains($"cases/{caseId2}"))
                .Should().NotBeNull();

        }

        [Theory]
        [InlineData("updateCases", "allowedUpdateCases")]
        [InlineData("writeCases", "allowedWriteCases")]
        public async Task
            GIVEN_login_has_permission_contains_allowedWriteCases_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_GopIds_in_readGOPs(
                string updatePermissionName, string targetedPermissionName)
        {
            string username = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string caseId1 = CreateNewGuid();
            string caseId2 = CreateNewGuid();

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, updatePermissionName, caseId1);
            await Login.AddPermission(login.id!, updatePermissionName, caseId2);
            await Login.AddPermission(login.id!, "readFiles", $"cases/{{{targetedPermissionName}}}");

            login = await FindLogin(username);

            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readFiles" && tp.targetIds!.Contains($"cases/{caseId1}"))
                .Should().NotBeNull();
            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readFiles" && tp.targetIds!.Contains($"cases/{caseId2}"))
                .Should().NotBeNull();

        }

        [Fact]
        public async Task
            GIVEN_login_has_permission_contains_allowedReadProposals_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_GopIds_in_readGOPs()
        {
            string username = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string proposalId1 = CreateNewGuid();
            string proposalId2 = CreateNewGuid();

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readProposals", proposalId1);
            await Login.AddPermission(login.id!, "readProposals", proposalId2);
            await Login.AddPermission(login.id!, "readFiles", "proposals/{allowedReadProposals}");

            login = await FindLogin(username);

            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readFiles" && tp.targetIds!.Contains($"proposals/{proposalId1}"))
                .Should().NotBeNull();
            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readFiles" && tp.targetIds!.Contains($"proposals/{proposalId2}"))
                .Should().NotBeNull();
        }

        [Fact]
        public async Task
            GIVEN_login_has_permission_contains_allowedWriteProposals_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_GopIds_in_readGOPs()
        {
            string username = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string proposalId1 = CreateNewGuid();
            string proposalId2 = CreateNewGuid();

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "writeProposals", proposalId1);
            await Login.AddPermission(login.id!, "writeProposals", proposalId2);
            await Login.AddPermission(login.id!, "writeProposals", "{proposalIdIfStakeholder}");
            await Login.AddPermission(login.id!, "readFiles", "proposals/{allowedWriteProposals}");

            login = await FindLogin(username);

            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readFiles" && tp.targetIds!.Contains($"proposals/{proposalId1}"))
                .Should().NotBeNull();
            login.targettedPermissions!
                .Single(tp => tp!.permission!.id == "readFiles" && tp.targetIds!.Contains($"proposals/{proposalId2}"))
                .Should().NotBeNull();
        }
    }
}