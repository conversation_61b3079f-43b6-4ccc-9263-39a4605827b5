﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Tests.Integration.Crud;
using GraphQL.Client.Http;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Auth
{
    public class DependentPermissionsTests : TestsBase
    {
        public DependentPermissionsTests(ITestOutputHelper output) : base(output)
        {
        }

        [Theory]
        [InlineData("updatePolicies")]
        [InlineData("writePolicies")]
        public async Task GIVEN_policy_AND_login_WHEN_add_read_permission_AND_dependent_write_permission_THEN_write_permission_is_granted(
            string writePermissionName)
        {
            initializePolicyInput input = new();
            string policyId = await Policy.Create(input);

            string loginId = await Login.Create();
            await Login.AddPermission(loginId!, writePermissionName, "{allowedReadPolicies}");
            await Login.AddPermission(loginId!, "readPolicies", policyId);

            loginWhere filter = new() { ids = new List<string> { loginId }! };
            login? login = await GetLoginWithPermissions(filter);
            targettedPermission? writePoliciesPermissions = login!.targettedPermissions!.First(p => p!.permission!.id == writePermissionName);
            writePoliciesPermissions!.targetIds.Should().Contain(policyId);
        }

        [Fact]
        public async Task GIVEN_dependent_permission_WHEN_get_permissions_THEN_permissions_list_is_correct()
        {
            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId, "readPolicies", "{creatorRights}");
            await Login.AddPermission(loginId, "createPolicies", "all");
            await Login.AddPermission(loginId, "readFiles", "{allowedReadClaims}");
            await Login.AddPermission(loginId, "readClaims", "{claimIdIf{allowedReadPolicies}}");
            await Login.AddPermission(loginId, "createClaims", "all");

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            string policyId = await new Policy(client).Create(new initializePolicyInput());
            string claimId = await new Claim(client).Create(new createClaimInput { policyId = policyId });

            login? permissions = await GetLoginWithPermissions(new loginWhere { ids = new List<string?> { loginId }});

            permissions!
                .targettedPermissions!
                .Where(p => p!.permission!.id == "readFiles")
                .SelectMany(p => p!.targetIds!)
                .Should()
                .BeEquivalentTo(new[]
                {
                    "{allowedReadClaims}",
                    "{claimIdIf{allowedReadPolicies}}",
                    "{claimIdIf{creatorRights}}",
                    $"{{claimIdIf{policyId}}}",
                    claimId
                });
        }


        async Task<login?> GetLoginWithPermissions(loginWhere where)
        {
            string query = new QueryBuilder()
                .logins(new QueryBuilder.loginsArgs(where: where), new loginsBuilder()
                    .list(new loginBuilder()
                        .id()
                        .targettedPermissions(new targettedPermissionBuilder()
                            .permission(new permissionBuilder()
                                .id())
                            .targetIds())
                        )
                    )
                .Build();

            logins logins = await _client.SendQueryAsync<logins>(query);
            return logins.list!.First();
        }
    }
}
