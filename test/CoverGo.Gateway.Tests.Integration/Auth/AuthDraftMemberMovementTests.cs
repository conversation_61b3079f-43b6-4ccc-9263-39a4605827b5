using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Auth
{
    public class AuthDraftMemberMovementTests : TestsBase
    {
        protected static readonly GraphQLClientConfig ClientConfig = GraphQLClientConfig.Local.Load();

        public AuthDraftMemberMovementTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_login_does_not_have_draftMemberMovement_permission__WHEN_draftMemberMovement_THEN_should_return_error()
        {
            var (loginId, policyId, loginInput) = await CreateSampleLoginAndPolicy();
            await Login.RemovePermission(loginId, "writeMemberMovements:draft", "{creatorRights}");
            await Login.RemovePermission(loginId, "writePolicies", "all");
            var policyClient = await CreatePolicyClient(loginInput);

            createdStatusResult actualResult = null;
            var action = async () =>
            {
                actualResult = await policyClient.Endorsement.TryAdd(policyId, type: "MEMBER_MOVEMENT", isDraft: true, source: endorsementSource.HR_PORTAL);
            };

            await action.Should().ThrowAsync<Exception>().WithMessage("*not authorized*");
            actualResult.Should().BeNull();
        }

        [Theory]
        [InlineData("admin_portal", null, true)]
        [InlineData("admin_portal", endorsementSource.ADMIN_PORTAL, true)]
        [InlineData("admin_portal", endorsementSource.HR_PORTAL, false)]
        [InlineData("hr_portal", null, false)]
        [InlineData("hr_portal", endorsementSource.ADMIN_PORTAL, false)]
        [InlineData("hr_portal", endorsementSource.HR_PORTAL, true)]
        public async Task GIVEN_login_has_draftMemberMovement_permission__WHEN_draftMemberMovement_THEN_should_authorize_user(string appId, endorsementSource? endorsementSource, bool expectedSuccess)
        {
            var (loginId, policyId, loginInput) = await CreateSampleLoginAndPolicy(appId);
            var policyClient = await CreatePolicyClient(loginInput);

            createdStatusResult actualResult = null;
            var action = async () =>
            {
                actualResult = await policyClient.Endorsement.TryAdd(policyId, type: "MEMBER_MOVEMENT", isDraft: true, source: endorsementSource);
            };

            if (expectedSuccess)
            {
                await action.Should().NotThrowAsync();
                actualResult.Should().NotBeNull();
                actualResult.errors.Should().BeNullOrEmpty();
                actualResult.status.Should().NotBeNullOrEmpty();
            }
            else
            {
                await action.Should().ThrowAsync<Exception>().WithMessage("*not authorized*");
            }
        }

        [Theory]
        [InlineData("MEMBER_MOVEMENT", false)]
        [InlineData("NOT_MEMBER_MOVEMENT", true)]
        [InlineData("NOT_MEMBER_MOVEMENT", false)]
        public async Task GIVEN_login_has_draftMemberMovement_permission_but_not_writePolicies__WHEN_addEndorsement_THEN_should_authorize_user(string endorsementType, bool isDraft)
        {
            var (loginId, policyId, loginInput) = await CreateSampleLoginAndPolicy();
            await Login.RemovePermission(loginId, "writePolicies", "all");
            var policyClient = await CreatePolicyClient(loginInput);

            createdStatusResult actualResult = null;
            var action = async () =>
            {
                actualResult = await policyClient.Endorsement.TryAdd(policyId, type: endorsementType, isDraft: isDraft, source: endorsementSource.HR_PORTAL);
            };

            await action.Should().ThrowAsync<Exception>().WithMessage("*not authorized*");
            actualResult.Should().BeNull();
        }

        private async Task<(string loginId, string policyId, createLoginInput loginInput)> CreateSampleLoginAndPolicy(string appId = "admin")
        {
            var entityId = await Entity.CreateInternal();
            var loginData = await Login.CreateValidLoginAndReturnIdAndInput(entityId: entityId);

            await Login.AddPermission(loginData.loginId, "appId", appId);
            await Login.AddPermission(loginData.loginId, "writePolicies", "all");
            await Login.AddPermission(loginData.loginId, "issuePolicies", "all");
            await Login.AddPermission(loginData.loginId, "writeMemberMovements:draft", "{creatorRights}");

            var policyClient = await CreatePolicyClient(loginData.input);
            string policyId = await policyClient.Create(new initializePolicyInput { insuredIds = new List<string?> { entityId } });
            await policyClient.Issue(policyId);

            return (loginData.loginId, policyId, loginData.input);
        }

        private async Task<Policy> CreatePolicyClient(createLoginInput loginInput)
        {
            var client = await CreateGraphQLHttpClient(loginInput.clientId!, loginInput.username!, loginInput.password!);
            return new Policy(client);
        }
    }
}