﻿using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.Gateway.Domain.Auth;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Auth
{
    public class PermissionTests : TestsBase
    {
        public PermissionTests(ITestOutputHelper output) : base(output)
        {
        }

        [Theory]
        [InlineData("writeClaims")]
        [InlineData("updateClaims")]
        [InlineData("deleteClaims")]
        public async Task GIVEN_login_with_claimIdIfProvider_permission_WHEN_query_claims_THEN_returns_only_provider_claims(string writeClaimsPermissionName)
        {
            var entityId = await Entity.CreateInternal();
            var loginData = await Login.CreateValidLoginAndReturnIdAndInput(entityId: entityId);
            var credentials = loginData.input;

            await Login.AddPermission(loginData.loginId, "readClaims", @"{claimIdIfProvider}");
            await Login.AddPermission(loginData.loginId, writeClaimsPermissionName, @"{claimIdIfProvider}");

            var providerId1 = await Entity.CreateCompany();
            var providerId2 = await Entity.CreateCompany();
            var providerId3 = await Entity.CreateCompany();

            await Entity.AddLink(entityId, providerId1, "workingFor");
            await Entity.AddLink(entityId, providerId2, "workingFor");

            string claimId1 = await Claim.Create(new createClaimInput { providerId = providerId1 });
            string claimId2 = await Claim.Create(new createClaimInput { providerId = providerId2 });
            string claimId3 = await Claim.Create(new createClaimInput { providerId = providerId3 });

            // check permissions list
            var login = await Login.GetLogin(credentials.username!);

            login.targettedPermissions!.Where(p => p!.permission!.id == "readClaims").SelectMany(p => p!.targetIds!).OrderBy(t => t)
                .Should().BeEquivalentTo(new[] { "{claimIdIfProvider}", claimId1, claimId2 }.OrderBy(t => t));

            login.targettedPermissions!.Where(p => p!.permission!.id == writeClaimsPermissionName).SelectMany(p => p!.targetIds!).OrderBy(t => t)
                .Should().BeEquivalentTo(new[] { "{claimIdIfProvider}", claimId1, claimId2 }.OrderBy(t => t));

            // check claims query
            var client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            var claims = await new Claim(client).Find(new claimWhereInput
            {
                id_in = new[] { claimId1, claimId2, claimId3 }
            });

            claims.Should().HaveCount(2);

            claims.Select(c => c!.id!.Value).OrderBy(c => c)
                .Should().BeEquivalentTo(new[] { Guid.Parse(claimId1), Guid.Parse(claimId2) }.OrderBy(c => c));
        }

        #region test cases for the add/remove a targetted perrmission into a permission group mutation

        [Fact]
        public async Task
            GIVEN_login_without_addAccessRestrictedContentPermission_permission_WHEN_add_accessRestrictedContent_permission_to_a_permission_group_THEN_return_unauthorized_result()
        {
            (string _, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all")
                });

            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.AddPermissionToGroup("dumb-group-id", UserClaim.AccessRestrictedContent.ToString(), "all");
            await action.Should().ThrowAsync<Exception>()
                .WithMessage(
                "GraphQL.Validation.ValidationError: covergo: You are not authorized to run this. You are missing 'addAccessRestrictedContentPermission' permission.*");
        }

        [Fact]
        public async Task
            GIVEN_login_without_removeAccessRestrictedContentPermission_permission_WHEN_remove_accessRestrictedContent_permission_from_a_permission_group_THEN_return_unauthorized_result()
        {
            (string _, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.AccessRestrictedContent.ToString(), value: UserClaimValues.AccessRestrictedContent.Full)
                });
            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.RemovePermissionToGroup("dumb-group-id", UserClaim.AccessRestrictedContent.ToString(), "all");
            await action.Should().ThrowAsync<Exception>()
                .WithMessage(
                    "GraphQL.Validation.ValidationError: covergo: You are not authorized to run this. You are missing 'removeAccessRestrictedContentPermission' permission.*");
        }

        [Fact]
        public async Task
            GIVEN_login_with_addAccessRestrictedContentPermission_permission_WHEN_add_accessRestrictedContent_permission_to_a_permission_group_THEN_the_permission_added_successfully()
        {
            (string _, string groupId, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.AddAccessRestrictedContentPermission.ToString(), value:"all")
                });

            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.AddPermissionToGroup(groupId, UserClaim.AccessRestrictedContent.ToString(), "all");
            await action.Should().NotThrowAsync<Exception>();
        }

        [Fact]
        public async Task
            GIVEN_login_with_removeAccessRestrictedContentPermission_permission_WHEN_remove_accessRestrictedContent_permission_from_a_permission_group_THEN_the_permission_removed_successfully()
        {
            (string _, string groupId, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.AddAccessRestrictedContentPermission.ToString(), value:"all"),
                    (permissionId: UserClaim.AccessRestrictedContent.ToString(), value: UserClaimValues.AccessRestrictedContent.Full)
                });
            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.AddPermissionToGroup(groupId, UserClaim.AccessRestrictedContent.ToString(), "all");
            await action.Should().NotThrowAsync<Exception>();
        }

        [Fact]
        public async Task
            GIVEN_login_without_addAccessRestrictedContentPermission_permission_WHEN_add_any_permission_to_a_permission_group_THEN_the_permission_added_successfully()
        {
            (string _, string groupId, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all")
                });
            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.AddPermissionToGroup(groupId, UserClaim.ReadClaims.ToString(), "all");
            await action.Should().NotThrowAsync<Exception>();
        }

        [Fact]
        public async Task
            GIVEN_login_without_removeAccessRestrictedContentPermission_permission_WHEN_remove_any_permission_to_a_permission_group_THEN_the_permission_removed_successfully()
        {
            (string _, string groupId, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.ReadClaims.ToString(), value:"all")
                });
            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.RemovePermissionToGroup(groupId, UserClaim.ReadClaims.ToString(), "all");
            await action.Should().NotThrowAsync<Exception>();
        }

        [Fact]
        public async Task
            GIVEN_login_with_addAccessRestrictedContentPermission_permission_WHEN_add_any_permission_to_a_permission_group_THEN_the_permission_added_successfully()
        {
            (string _, string groupId, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.AddAccessRestrictedContentPermission.ToString(), value:"all"),
                });
            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.AddPermissionToGroup(groupId, UserClaim.ReadClaims.ToString(), "all");
            await action.Should().NotThrowAsync<Exception>();
        }

        [Fact]
        public async Task
            GIVEN_login_with_removeAccessRestrictedContentPermission_permission_WHEN_remove_any_permission_from_a_permission_group_THEN_the_permission_removed_successfully()
        {
            (string _, string groupId, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.ReadClaims.ToString(), value:"all"),
                    (permissionId: UserClaim.RemoveAccessRestrictedContentPermission.ToString(), value:"all"),
                });
            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.RemovePermissionToGroup(groupId, UserClaim.ReadClaims.ToString(), "all");
            await action.Should().NotThrowAsync<Exception>();
        }

#endregion

        #region test cases for the add/remove a targetted perrmission into a login mutation

        [Fact]
        public async Task
            GIVEN_login_without_addAccessRestrictedContentPermission_permission_WHEN_add_accessRestrictedContent_permission_to_a_login_THEN_return_unauthorized_result()
        {
            (string _, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all")
                });
            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.AddPermission("dumb-login-id", UserClaim.AccessRestrictedContent.ToString(), UserClaimValues.AccessRestrictedContent.Full);
            await action.Should().ThrowAsync<Exception>()
                .WithMessage(
                "GraphQL.Validation.ValidationError: covergo: You are not authorized to run this. You are missing 'addAccessRestrictedContentPermission' permission.*");
        }

        [Fact]
        public async Task
            GIVEN_login_without_removeAccessRestrictedContentPermission_permission_WHEN_remove_accessRestrictedContent_permission_to_a_login_THEN_return_unauthorized_result()
        {
            (string _, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.AccessRestrictedContent.ToString(), value: UserClaimValues.AccessRestrictedContent.Full)
                });
            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.RemovePermission("dumb-login-id", UserClaim.AccessRestrictedContent.ToString(), UserClaimValues.AccessRestrictedContent.Full);
            await action.Should().ThrowAsync<Exception>()
                .WithMessage(
                    "GraphQL.Validation.ValidationError: covergo: You are not authorized to run this. You are missing 'removeAccessRestrictedContentPermission' permission.*");
        }

        [Fact]
        public async Task
            GIVEN_login_with_addAccessRestrictedContentPermission_permission_WHEN_add_accessRestrictedContent_permission_to_a_login_THEN_the_permission_added_successfully()
        {
            (string loginId, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.AddAccessRestrictedContentPermission.ToString(), value:"all")
                });

            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.AddPermission(loginId, UserClaim.AccessRestrictedContent.ToString(), UserClaimValues.AccessRestrictedContent.Full);
            await action.Should().NotThrowAsync<Exception>();
        }

        [Fact]
        public async Task
            GIVEN_login_with_removeAccessRestrictedContentPermission_permission_WHEN_remove_accessRestrictedContent_permission_from_a_login_THEN_the_permission_removed_successfully()
        {
            (string loginId, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.RemoveAccessRestrictedContentPermission.ToString(), value:"all"),
                    (permissionId: UserClaim.AccessRestrictedContent.ToString(), value: UserClaimValues.AccessRestrictedContent.Full)
                });
            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.RemovePermission(loginId, UserClaim.AccessRestrictedContent.ToString(), UserClaimValues.AccessRestrictedContent.Full);
            await action.Should().NotThrowAsync<Exception>();
        }

        [Fact]
        public async Task
            GIVEN_login_without_addAccessRestrictedContentPermission_permission_WHEN_add_any_permission_to_a_login_THEN_the_permission_added_successfully()
        {
            (string loginId, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all")
                });
            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.AddPermission(loginId, UserClaim.ReadClaims.ToString(), "all");
            await action.Should().NotThrowAsync<Exception>();
        }

        [Fact]
        public async Task
            GIVEN_login_without_removeAccessRestrictedContentPermission_permission_WHEN_remove_any_permission_to_a_login_THEN_the_permission_removed_successfully()
        {
            (string loginId, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.ReadClaims.ToString(), value:"all")
                });
            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.RemovePermission(loginId, UserClaim.ReadClaims.ToString(), "all");
            await action.Should().NotThrowAsync<Exception>();
        }

        [Fact]
        public async Task
            GIVEN_login_with_addAccessRestrictedContentPermission_permission_WHEN_add_any_permission_to_a_login_THEN_the_permission_added_successfully()
        {
            (string loginId, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.AddAccessRestrictedContentPermission.ToString(), value:"all"),
                });
            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.AddPermission(loginId, UserClaim.ReadClaims.ToString(), "all");
            await action.Should().NotThrowAsync<Exception>();
        }

        [Fact]
        public async Task
            GIVEN_login_with_removeAccessRestrictedContentPermission_permission_WHEN_remove_any_permission_to_a_login_THEN_the_permission_removed_successfully()
        {
            (string loginId, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.ReadClaims.ToString(), value:"all"),
                    (permissionId: UserClaim.RemoveAccessRestrictedContentPermission.ToString(), value:"all"),
                });
            OverrideSuperAdminToken(token);

            Func<Task> action = () =>  Login.RemovePermission(loginId, UserClaim.ReadClaims.ToString(), "all");
            await action.Should().NotThrowAsync<Exception>();
        }

        #endregion

        #region test cases for the add/remove multiple targetted perrmissions into a login mutation

        [Fact]
        public async Task
            GIVEN_login_without_addAccessRestrictedContentPermission_permission_AND_a_list_of_permissions_contains_accessRestrictedContent_WHEN_add_multi_permissions_to_a_login_THEN_return_unauthorized_result()
        {
            (string _, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all")
                });
            OverrideSuperAdminToken(token);

            List<(string permission, string value)> permissions = new()
            {
                (UserClaim.AccessRestrictedContent.ToString(), "all")
            };

            Func<Task> action = () =>  Login.AddPermissions("dumb-login-id", permissions);
            await action.Should().ThrowAsync<Exception>()
                .WithMessage(
                "GraphQL.Validation.ValidationError: covergo: You are not authorized to run this. You are missing 'addAccessRestrictedContentPermission' permission.*");
        }

        [Fact]
        public async Task
            GIVEN_login_without_removeAccessRestrictedContentPermission_permission_AND_a_list_of_permissions_contains_accessRestrictedContent_WHEN_remove_multiple_permissions_from_a_login_THEN_return_unauthorized_result()
        {
            (string _, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.AccessRestrictedContent.ToString(), value: UserClaimValues.AccessRestrictedContent.Full)
                });
            OverrideSuperAdminToken(token);

            List<(string permission, string value)> permissions = new()
            {
                (UserClaim.AccessRestrictedContent.ToString(), "all")
            };
            Func<Task> action = () =>  Login.RemovePermissions("dumb-login-id", permissions);
            await action.Should().ThrowAsync<Exception>()
                .WithMessage(
                    "GraphQL.Validation.ValidationError: covergo: You are not authorized to run this. You are missing 'removeAccessRestrictedContentPermission' permission.*");
        }

        [Fact]
        public async Task
            GIVEN_login_with_addAccessRestrictedContentPermission_permission_AND_a_list_of_permissions_contains_accessRestrictedContent_WHEN_add_multiple_permissions_to_a_login_THEN_the_permission_added_successfully()
        {
            (string loginId, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.AddAccessRestrictedContentPermission.ToString(), value:"all")
                });

            OverrideSuperAdminToken(token);

            List<(string permission, string value)> permissions = new()
            {
                (UserClaim.AccessRestrictedContent.ToString(), "all")
            };
            Func<Task> action = () =>  Login.AddPermissions(loginId, permissions);
            await action.Should().NotThrowAsync<Exception>();
        }

        [Fact]
        public async Task
            GIVEN_login_with_removeAccessRestrictedContentPermission_permission_AND_a_list_of_permissions_contains_accessRestrictedContent_WHEN_remove_multiple_permissions_from_a_login_THEN_the_permission_removed_successfully()
        {
            (string loginId, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.RemoveAccessRestrictedContentPermission.ToString(), value:"all"),
                    (permissionId: UserClaim.AccessRestrictedContent.ToString(), value: UserClaimValues.AccessRestrictedContent.Full)
                });
            OverrideSuperAdminToken(token);

            List<(string permission, string value)> permissions = new()
            {
                (UserClaim.AccessRestrictedContent.ToString(), "all"),
            };
            Func<Task> action = () =>  Login.RemovePermissions(loginId, permissions);
            await action.Should().NotThrowAsync<Exception>();
        }

        [Fact]
        public async Task
            GIVEN_login_without_addAccessRestrictedContentPermission_permission_AND_a_list_of_permissions_without_accessRestrictedContent_WHEN_add_multiple_permissions_to_a_login_THEN_the_permission_added_successfully()
        {
            (string loginId, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all")
                });
            OverrideSuperAdminToken(token);

            List<(string permission, string value)> permissions = new()
            {
                (UserClaim.ReadClaims.ToString(), "all"),
            };
            Func<Task> action = () =>  Login.AddPermissions(loginId, permissions);
            await action.Should().NotThrowAsync<Exception>();
        }

        [Fact]
        public async Task
            GIVEN_login_without_removeAccessRestrictedContentPermission_permission_AND_a_list_of_permissions_without_accessRestrictedContent_WHEN_remove_multiple_permissions_from_a_login_THEN_the_permission_removed_successfully()
        {
            (string loginId, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.ReadClaims.ToString(), value:"all")
                });
            OverrideSuperAdminToken(token);

            List<(string permission, string value)> permissions = new()
            {
                (UserClaim.ReadClaims.ToString(), "all"),
            };
            Func<Task> action = () =>  Login.RemovePermissions(loginId, permissions);
            await action.Should().NotThrowAsync<Exception>();
        }

        [Fact]
        public async Task
            GIVEN_login_with_addAccessRestrictedContentPermission_permission_AND_a_list_of_permissions_without_accessRestrictedContent_WHEN_add_multiple_permissions_to_a_login_THEN_the_permission_added_successfully()
        {
            (string loginId, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.AddAccessRestrictedContentPermission.ToString(), value:"all"),
                });
            OverrideSuperAdminToken(token);

            List<(string permission, string value)> permissions = new()
            {
                (UserClaim.ReadClaims.ToString(), "all"),
            };
            Func<Task> action = () =>  Login.AddPermissions(loginId, permissions);
            await action.Should().NotThrowAsync<Exception>();
        }

        [Fact]
        public async Task
            GIVEN_login_with_removeAccessRestrictedContentPermission_permission_AND_a_list_of_permissions_without_accessRestrictedContent_WHEN_remove_multiple_permissions_from_a_login_THEN_the_permission_removed_successfully()
        {
            (string loginId, string _, string token) = await PrepareNewLoginWithParticularPermissions(
                new List<(string permissionId, string value)>
                {
                    (permissionId: UserClaim.WriteTargettedPermissions.ToString(), value: "all"),
                    (permissionId: UserClaim.ReadClaims.ToString(), value:"all"),
                    (permissionId: UserClaim.RemoveAccessRestrictedContentPermission.ToString(), value:"all"),
                });
            OverrideSuperAdminToken(token);

            List<(string permission, string value)> permissions = new()
            {
                (UserClaim.ReadClaims.ToString(), "all"),
            };
            Func<Task> action = () =>  Login.RemovePermissions(loginId, permissions);
            await action.Should().NotThrowAsync<Exception>();
        }

        #endregion

        #region private methods
        private async Task<(string loginId, string groupId, string token)> PrepareNewLoginWithParticularPermissions(List<(string permissionId, string value)> permissions)
        {
            string groupId = await PermissionGroup.Create();
            string clientId = await CreateClientId();
            await AddClientIdToGroup(clientId, groupId);

            (string loginId, string username, string password) = await CreateLogin();

            await Login.AddPermissions(loginId, permissions);
            await Login.AddToGroup(loginId, groupId);

            string token = await Token.Fetch(new QueryBuilder.token_2Args(GraphQLClientConfig.Local.TenantId, clientId, username, password));

            return (loginId, groupId, token);
        }

        private async Task<(string loginId, string username, string password)> CreateLogin()
        {
            string username = CreateNewGuid();
            createLoginInput input = new()
            {
                username = username,
                clientId = await CreateClientId(),
                email = $"{username}@covergo.com",
                isEmailConfirmed = true,
                password = CreateNewGuid()
            };

            return (await Login.Create(input), username, input.password);
        }

        private async Task<string> CreateClientId()
        {
            string appId = CreateNewGuid();
            string mutation = new MutationBuilder()
                .createApp(new MutationBuilder.createAppArgs(new createAppInput { appId = appId, appName = CreateNewGuid() }), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _client.SendMutationAndEnsureSuccessAsync(mutation);

            return appId;
        }

        private Task AddClientIdToGroup(string clientId, string groupId) =>
            Login.AddPermissionToGroup(groupId, "clientId", clientId);

        private void OverrideSuperAdminToken(string token) => _client.HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        #endregion
    }
}