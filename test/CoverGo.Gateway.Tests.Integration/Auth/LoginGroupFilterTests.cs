using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class LoginGroupFilterTests : TestsBase
    {
        public LoginGroupFilterTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_group_WHEN_filtering_by_name_THEN_returns_this_group()
        {
            string groupName = CreateNewGuid();
            await PermissionGroup.Create(groupName);

            permissionGroupWhereInput where = new() { name = groupName };
            permissionGroup group = await PermissionGroup.Find(where);

            group.name.Should().Be(groupName);
        }

        [Fact]
        public async Task GIVEN_group_WHEN_updating_THEN_returns_updated_group()
        {
            string[] productTypes = new[] { CreateNewGuid(), CreateNewGuid() };

            string id = await PermissionGroup.Create(
                new createPermissionGroupInput
                {
                    name = CreateNewGuid(),
                    description = CreateNewGuid(),
                    productTypes = productTypes,
                });

            string groupName = CreateNewGuid();
            string groupDescription = CreateNewGuid();

            await PermissionGroup.Update(
                id,
                new updatePermissionGroupInput
                {
                    name = groupName,
                    description = groupDescription,
                    productTypes = null,
                });

            permissionGroup group = await PermissionGroup.Find(
                new permissionGroupWhereInput
                {
                    id = id,
                });

            group.name.Should().Be(groupName);
            group.description.Should().Be(groupDescription);
            group.productTypes.Should().BeEmpty();
        }

        [Fact]
        public async Task GIVEN_group_WHEN_filtering_by_name_in_THEN_returns_this_group()
        {
            string groupName = CreateNewGuid();
            await PermissionGroup.Create(groupName);

            permissionGroupWhereInput where = new() { name_in = new List<string?> { groupName } };
            permissionGroup group = await PermissionGroup.Find(where);

            group.name.Should().Be(groupName);
        }

        [Fact]
        public async Task GIVEN_group_WHEN_fetching_THEN_can_fetch_logins()
        {
            string groupId = await PermissionGroup.Create();
            string loginId = await CreateLogin();
            await Login.AddToGroup(loginId, groupId);

            permissionGroup group = await PermissionGroup.Find(new permissionGroupWhereInput { id = groupId });

            group.logins.Should().NotBeNullOrEmpty();
            group.logins!.First()!.id.Should().Be(loginId);
        }

        [Fact]
        public async Task GIVEN_logins_WHEN_filtering_by_group_THEN_returns_relevant_logins()
        {
            string groupId = await PermissionGroup.Create();
            string loginId = await CreateLogin();
            await CreateLogin();
            await Login.AddToGroup(loginId, groupId);

            loginWhere where = new() { permissionGroupIds = new[] { groupId } };
            IReadOnlyCollection<string?>? loginIds = await GetLoginIds(where);

            loginIds!.Single().Should().Be(loginId);
        }

        [Fact]
        public async Task GIVEN_logins_with_associated_users_WHEN_filtering_by_entity_THEN_returns_relevant_logins()
        {
            string name = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(name);
            await Entity.CreateWithAssociatedLogin(CreateNewGuid());
            string loginId = (await Entity.FindAssociatedLoginIds(new individualWhereInput { id = entityId }))!.First()!;

            loginWhere where = new() { entity = new entityWhereInput { name_contains = name } };
            IReadOnlyCollection<string?>? loginIds = await GetLoginIds(where);

            loginIds!.Single().Should().Be(loginId);
        }

        [Fact]
        public async Task GIVEN_logins_with_associated_users_WHEN_filtering_by_and_condition_with_entity_THEN_returns_relevant_logins()
        {
            string name = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(name);
            await Entity.CreateWithAssociatedLogin(CreateNewGuid());
            string loginId = (await Entity.FindAssociatedLoginIds(new individualWhereInput { id = entityId }))!.First()!;

            loginWhere where = new()
            {
                and = new List<loginWhere?>
                {
                    new()
                    {
                        entity = new entityWhereInput
                        {
                            name_contains = name
                        }
                    }
                }
            };
            IReadOnlyCollection<string?>? loginIds = await GetLoginIds(where);

            loginIds!.Single().Should().Be(loginId);
        }

        [Fact]
        public async Task GIVEN_logins_with_associated_users_WHEN_filtering_by_or_condition_with_entity_THEN_returns_relevant_logins()
        {
            string name = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(name);
            await Entity.CreateWithAssociatedLogin(CreateNewGuid());
            string loginId = (await Entity.FindAssociatedLoginIds(new individualWhereInput { id = entityId }))!.First()!;

            loginWhere where = new()
            {
                or = new List<loginWhere?>
                {
                    new()
                    {
                        entity = new entityWhereInput
                        {
                            name_contains = name
                        }
                    }
                }
            };
            IReadOnlyCollection<string?>? loginIds = await GetLoginIds(where);

            loginIds!.Single().Should().Be(loginId);
        }

        Task<string> CreateLogin() =>
            Login.Create(CreateNewGuid(), CreateNewGuid());

        async Task<IReadOnlyCollection<string?>?> GetLoginIds(loginWhere where)
        {
            string query = new QueryBuilder()
                .logins(new QueryBuilder.loginsArgs(where: where), new loginsBuilder()
                    .list(new loginBuilder()
                        .id()
                        .email()
                        .username()
                        .permissionGroups(new permissionGroupBuilder()
                            .id()
                            .name())))
                .Build();

            logins response = await _client.SendQueryAsync<logins>(query);

            return response.list?.Select(l => l?.id).ToArray();
        }
    }
}