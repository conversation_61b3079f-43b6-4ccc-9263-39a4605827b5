using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Auth
{
    public class AuthCompanyIdReplacementTests : AuthReplacementTests
    {
        public AuthCompanyIdReplacementTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_login_has_permission_contains_companyIdIfTagsContainProvider_and_a_company_has_tag_contains_provider_WHEN_query_this_login_THEN_the_permission_value_should_be_replaced_by_this_companyId()
        {
            string username = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string companyId = await Entity.CreateCompany(new createCompanyInput
            {
                tags = new List<string> { "provider" }!
            });

            login login = await FindLogin(username);

            await Login.AddPermission(login.id!, "readCompanies", @"{companyIdIfTagsContainProvider}");

            login = await FindLogin(username);

            login.targettedPermissions!.Single(tp => tp!.permission!.id == "readCompanies" && tp.targetIds!.Contains(companyId)).Should().NotBeNull();
        }
    }
}