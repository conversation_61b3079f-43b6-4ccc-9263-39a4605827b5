﻿using AutoFixture.Xunit2;
using CoverGo.Gateway.Client;
using FluentAssertions;
using GraphQL.Client.Http;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Auth
{
    public class PasswordValidatorsTests : TestsBase
    {
        public PasswordValidatorsTests(ITestOutputHelper output) : base(output)
        {
        }

        [Theory]
        [AutoData]
        public async Task GIVEN_Non_exisitng_tenant_id_WHEN_calling_passwordValidators_query_THEN_return_random_values_that_are_same_for_each_call(string tenantId)
        {
            GraphQLHttpClient graphQlClient = await Setup.CreateGraphQLHttpClient();
            passwordValidators passwordValidators = await graphQlClient.SendQueryAsync<passwordValidators>(GetPasswordValidatorsQuery(tenantId, "admin"));
            passwordValidators.Should().NotBeNull();
            passwordValidators secondPasswordValidators = await graphQlClient.SendQueryAsync<passwordValidators>(GetPasswordValidatorsQuery(tenantId, "admin"));
            secondPasswordValidators.Should().BeEquivalentTo(passwordValidators);
        }

        [Theory]
        [AutoData]
        public async Task GIVEN_two_non_exisitng_tenant_ids_WHEN_calling_passwordValidators_query_THEN_return_random_values_that_are_different_for_each_tenant_id(string tenantId1, string tenantId2)
        {
            GraphQLHttpClient graphQlClient = await Setup.CreateGraphQLHttpClient();
            passwordValidators passwordValidators = await graphQlClient.SendQueryAsync<passwordValidators>(GetPasswordValidatorsQuery(tenantId1, "admin"));
            passwordValidators.Should().NotBeNull();
            passwordValidators secondPasswordValidators = await graphQlClient.SendQueryAsync<passwordValidators>(GetPasswordValidatorsQuery(tenantId2, "admin"));
            secondPasswordValidators.Should().NotBeEquivalentTo(passwordValidators);
        }

        [Theory]
        [AutoData]
        public async Task GIVEN_Non_exisitng_client_id_WHEN_calling_passwordValidators_query_THEN_eturn_random_values_that_are_same_for_each_call(string clientId)
        {
            GraphQLHttpClient graphQlClient = await Setup.CreateGraphQLHttpClient();
            passwordValidators passwordValidators = await graphQlClient.SendQueryAsync<passwordValidators>(GetPasswordValidatorsQuery("covergo", clientId));
            passwordValidators.Should().NotBeNull();
            passwordValidators secondPasswordValidators = await graphQlClient.SendQueryAsync<passwordValidators>(GetPasswordValidatorsQuery("covergo", clientId));
            secondPasswordValidators.Should().BeEquivalentTo(passwordValidators);
        }

        private string GetPasswordValidatorsQuery(string tenantId, string clientId)
        {
            return new QueryBuilder().passwordValidators(new QueryBuilder.passwordValidatorsArgs(tenantId, clientId), new passwordValidatorsBuilder().WithAllFields()).Build();
        }
    }
}
