﻿using CoverGo.Gateway.Client;
using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration;

public class FileSystemPermissionsTests : TestsBase
{
    private const string TestFolderPath = "test-files-permissions";
    public FileSystemPermissionsTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task GIVEN_writeFiles_permission_with_path_with_slash_WHEN_uploadFile_THEN_it_is_sucessful()
    {
        (string loginId, createLoginInput input) loginData = await Login.CreateValidLoginAndReturnIdAndInput();
        createLoginInput? credentials = loginData.input;
        await Login.AddPermission(loginData.loginId, "writeFiles", @"test-files-permissions/");

        HttpClient? client = await CreateRestHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
        await UploadRandomFile(client);
    }

    [Fact]
    public async Task GIVEN_writeFiles_permission_with_path_without_slash_WHEN_uploadFile_THEN_it_is_sucessful()
    {
        (string loginId, createLoginInput input) loginData = await Login.CreateValidLoginAndReturnIdAndInput();
        createLoginInput? credentials = loginData.input;
        await Login.AddPermission(loginData.loginId, "writeFiles", @"test-files-permissions");

        HttpClient? client = await CreateRestHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
        await UploadRandomFile(client);
    }

    private async Task<string> UploadRandomFile(HttpClient client, int? ttlSeconds = default)
    {
        await FileSystem.InitializeTenant();

        string fileName = $"{Guid.NewGuid():N}.txt";
        string? fileProviderPath = $"{TestFolderPath}/{fileName}";

        await File.WriteAllTextAsync(fileName, Guid.NewGuid().ToString());
        await FileSystem.Upload(fileProviderPath, fileName, ttlSeconds, client);

        return fileProviderPath;
    }
}
