using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class PanelProviderTierAttachmentTests : TestsBase
    {
        public PanelProviderTierAttachmentTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_panelProviderTier_added_WHEN_add_attachment_to_it_THEN_succeed_and_panelProviderTier_contains_attachment()
        {
            createPanelProviderTierInput createPanelProviderTierInput = new() { name = "name" };
            string panelProviderTierId = await PanelProviderTier.Create(createPanelProviderTierInput);

            attachmentInput attachmentInput = new() { path = "title" };
            await PanelProviderTier.AddAttachment(panelProviderTierId, attachmentInput);

            panelProviderTier panelProviderTier = (await SearchPanelProviderTiersByWhereInput(new panelProviderTierWhereInput { id = panelProviderTierId })).First();

            panelProviderTier.attachments.Should().NotBeNullOrEmpty();
            panelProviderTier.attachments!.Single()!.path.Should().Be(attachmentInput.path);
        }

        [Fact]
        public async Task GIVEN_panelProviderTier_with_attachments_added_WHEN_remove_a_attachment_of_the_panelProviderTier_THEN_removed_and_succeed()
        {
            createPanelProviderTierInput createPanelProviderTierInput = new() { name = "name" };
            string panelProviderTierId = await PanelProviderTier.Create(createPanelProviderTierInput);

            attachmentInput attachmentInput = new() { path = "title" };
            await PanelProviderTier.AddAttachment(panelProviderTierId, attachmentInput);

            panelProviderTier panelProviderTier = (await SearchPanelProviderTiersByWhereInput(new panelProviderTierWhereInput { id = panelProviderTierId })).First();

            panelProviderTier.attachments.Should().HaveCount(1);

            await PanelProviderTier.RemoveAttachment(panelProviderTierId, "title");

            panelProviderTier updatedPanelProviderTier = (await SearchPanelProviderTiersByWhereInput(new panelProviderTierWhereInput { id = panelProviderTierId })).First();

            updatedPanelProviderTier.attachments.Should().HaveCount(0);
        }

        async Task<List<panelProviderTier>> SearchPanelProviderTiersByWhereInput(panelProviderTierWhereInput? input = null)
        {
            string query = new QueryBuilder()
                .panelProviderTiers(new QueryBuilder.panelProviderTiersArgs(where: input), new panelProviderTiersBuilder()
                    .list(new panelProviderTierBuilder()
                        .id()
                        .attachments(new attachmentBuilder()
                            .id()
                            .path())
                    ))
                .Build();

            panelProviderTiers panelProviderTiers = await _client.SendQueryAsync<panelProviderTiers>(query);
            return panelProviderTiers.list!.ToList()!;
        }
    }
}