using System;
using System.Collections.Generic;

namespace CoverGo.Gateway.Tests.Integration.AttachedRules
{
    public class MongoLoginDao : Microsoft.AspNetCore.Identity.MongoDB.IdentityUser // TODO: to clean. Belongs in infra
    {
        public string EntityId { get; set; }
        public string EntityType { get; set; }
        public string CreatedById { get; set; }
        public string LastModifiedById { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }
        public DateTime? PasswordLastUpdated { get; set; }
        public bool IsTemporaryPassword { get; set; } = false;
        public bool IgnorePasswordLifespan { get; set; }
        public List<string> PreviouslyUsedPasswords { get; set; }
        public List<string> TargetedPermissionSchemaIds { get; set; }
    }
}