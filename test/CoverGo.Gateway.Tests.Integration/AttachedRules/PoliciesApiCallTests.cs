using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using FluentAssertions;
using GraphQL.Client.Http;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.AttachedRules
{
    public class PoliciesApiCallTests : AttachedRulesTestBase
    {
        public PoliciesApiCallTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact(Skip = "Temporary disabled due to CI issues")]
        public async Task GIVEN_attached_api_call_on_policy_created_event_WHEN_policy_created_THEN_api_called()
        {
            (string username, GraphQLHttpClient client) = await InitilizeTestUserAndClient();

            var description = CreateNewGuid();

            var createRuleMutation = @$"mutation {{
  policies_AttachedOnEventApiCallMutationCreate(command: {{
      create: {{
          url: ""{ClientConfig.GatewayGraphQLUrl}""
          method: ""POST""
          stringContent: ""{{\""operationName\"":null,\""variables\"":{{}},\""query\"":\""{{\\n  policies(where: {{id: \\\""{{$obj.id}}\\\""}}) {{\\n    list {{\\n      id\\n    }}\\n  }}\\n}}\\n\""}}""
          contentType: ""application/json""
          headers: [
            {{
              key: ""Authorization""
              value: ""{{$usr.authorization}}""
            }}]
          eventExpression: ""type = 'creation'""
          objectExpression: ""description = '{description}'""
          exprectedContentExpression: ""data.policies.list[any] = (id = '{{$obj.id}}')""
          description: ""call external api when policy has been created""
          userExpression: ""username = '{username}'""
        }}
    }}) {{
    status
  }}
}}";
            await client.SendMutationAndEnsureSuccessAsync(createRuleMutation);

            var createPolicyMutation = new MutationBuilder().initializePolicy(new MutationBuilder.initializePolicyArgs(new initializePolicyInput
            {
                description = description
            }), new policyResultBuilder().errors().status()).Build();

            var policyCreationResult = await client.SendMutationAsync<policyResult>(createPolicyMutation);

            policyCreationResult.status.Should().Be("success");
            policyCreationResult.errors.Should().BeNullOrEmpty();
        }

        [Fact(Skip = "Temporary disabled due to CI issues")]
        public async Task GIVEN_attached_api_call_on_policy_created_event_WHEN_policy_created_and_endpoint_does_not_exists_THEN_command_succeded_but_contains_errors()
        {
            (string username, GraphQLHttpClient client) = await InitilizeTestUserAndClient();

            var description = CreateNewGuid();

            var createRuleMutation = @$"mutation {{
  policies_AttachedOnEventApiCallMutationCreate(command: {{
      create: {{
          url: ""{ClientConfig.GatewayGraphQLUrl}""
          method: ""POST""
          stringContent: ""{{\""operationName\"":null,\""variables\"":{{}},\""query\"":\""{{\\n  doesNotExists(where: {{id: \\\""{{$obj.id}}\\\""}}) {{\\n    list {{\\n      id\\n    }}\\n  }}\\n}}\\n\""}}""
          contentType: ""application/json""
          headers: [
            {{
              key: ""Authorization""
              value: ""{{$usr.authorization}}""
            }}]
          eventExpression: ""type = 'creation'""
          objectExpression: ""description = '{description}'""
          exprectedContentExpression: ""data.policies.list[any] = (id = '{{$obj.id}}')""
          description: ""call external api when policy has been created""
          userExpression: ""username = '{username}'""
        }}
    }}) {{
    status
  }}
}}";
            await client.SendMutationAndEnsureSuccessAsync(createRuleMutation);

            var createPolicyMutation = new MutationBuilder().initializePolicy(new MutationBuilder.initializePolicyArgs(new initializePolicyInput
            {
                description = description
            }), new policyResultBuilder().errors().status()).Build();

            var policyCreationResult = await client.SendMutationAsync<policyResult>(createPolicyMutation);

            policyCreationResult.status.Should().Be("success");
            policyCreationResult.errors.Should().HaveCount(1);
            var error = policyCreationResult.errors!.Single();

            error.Should().Contain("Response evaluation failed");
        }
    }
}