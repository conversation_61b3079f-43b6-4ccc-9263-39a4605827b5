using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using FluentAssertions;
using GraphQL.Client.Http;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.AttachedRules
{
    public class PoliciesCommandPermissionsTests : AttachedRulesTestBase
    {
        public PoliciesCommandPermissionsTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact(Skip = "Temporary disabled due to CI issues")]
        public async Task GIVEN_login_with_command_permission_assigned_WHEN_creating_policy_THEN_permission_works_correctly()
        {
            (string username, GraphQLHttpClient client) = await InitilizeTestUserAndClient();
            var policyCrud = new Policy(client);

            var description = CreateNewGuid();

            var permissionMutation = new MutationBuilder().policies_AttachedCommandPermissionMutationCreate(new MutationBuilder.policies_AttachedCommandPermissionMutationCreateArgs(new policies_CreateAttachedRuleCommandOfpoliciesAttachedCommandPermissionInput
            {
                create = new policies_policiesAttachedCommandPermissionInput
                {
                    command = "CreatePolicyCommand",
                    userExpression = $"username = '{username}'",
                    action = policies_AttachedRuleAllowAction.DENY,
                    commandExpression = $"description = '{description}'",
                    description = $"Disallow to create policy with descriptions set to '{description}'"
                }
            }), new policies_ResultOfCreatedStatusBuilder().status()).Build();

            await client.SendMutationAndEnsureSuccessAsync(permissionMutation);

            var createPolicyMutation = new MutationBuilder().initializePolicy(new MutationBuilder.initializePolicyArgs(new initializePolicyInput
            {
                description = description
            }), new policyResultBuilder().status().errors()).Build();

            var result = await client.SendMutationAsync<policyResult>(createPolicyMutation);

            result.status.Should().Be("failure");

            createPolicyMutation = new MutationBuilder().initializePolicy(new MutationBuilder.initializePolicyArgs(new initializePolicyInput
            {
                description = description + "dummy"
            }), new policyResultBuilder().status().errors()).Build();

            result = await client.SendMutationAsync<policyResult>(createPolicyMutation);

            result.status.Should().Be("success");
        }

        [Theory(Skip = "Temporary disabled due to CI issues")]
        [InlineData(null)]
        [InlineData("")]
        [InlineData(" ")]
        [InlineData("     ")]
        public async Task GIVEN_login_with_command_permission_that_disallows_to_issue_policy_when_issuer_number_is_empty_WHEN_issuing_policy_THEN_permission_works_correctly(string issuerNumber)
        {
            (string username, GraphQLHttpClient client) = await InitilizeTestUserAndClient();
            var policyCrud = new Policy(client);

            var description = CreateNewGuid();

            var policyId = await policyCrud.Create(new initializePolicyInput { description = description });

            var permissionMutation = new MutationBuilder().policies_AttachedCommandPermissionMutationCreate(new MutationBuilder.policies_AttachedCommandPermissionMutationCreateArgs(new policies_CreateAttachedRuleCommandOfpoliciesAttachedCommandPermissionInput
            {
                create = new policies_policiesAttachedCommandPermissionInput
                {
                    command = "IssuePolicyCommand",
                    userExpression = $"username = '{username}'",
                    action = policies_AttachedRuleAllowAction.DENY,
                    commandExpression = @"issuerNumber = null | issuerNumber ^= '\\s?'",
                    objectExpression = $"description = '{description}'",
                    description = $"Disallow to issue policy with null or empty issuerNumber"
                }
            }), new policies_ResultOfCreatedStatusBuilder().status().errors()).Build();


            await client.SendMutationAndEnsureSuccessAsync(permissionMutation);

            var issuePolicyMutation = new MutationBuilder().issuePolicy(new MutationBuilder.issuePolicyArgs(policyId, issuerNumber), new policyResultBuilder().status().errors());

            var result = await client.SendMutationAsync<policyResult>(issuePolicyMutation);

            result.status.Should().Be("failure");
            result.errors!.Single().Should().Contain("Description: 'Disallow to issue policy with null or empty issuerNumber'");

        }

        [Fact(Skip = "temporary disable due to CI issues")]
        public async Task GIVEN_login_with_command_permission_that_disallows_to_issue_policy_when_issuer_number_is_equal_to_username_WHEN_issuing_policy_THEN_permission_works_correctly()
        {
            (string username, GraphQLHttpClient client) = await InitilizeTestUserAndClient();
            var policyCrud = new Policy(client);

            var description = CreateNewGuid();

            var policyId = await policyCrud.Create(new initializePolicyInput { description = description });

            var permissionMutation = new MutationBuilder().policies_AttachedCommandPermissionMutationCreate(new MutationBuilder.policies_AttachedCommandPermissionMutationCreateArgs(new policies_CreateAttachedRuleCommandOfpoliciesAttachedCommandPermissionInput
            {
                create = new policies_policiesAttachedCommandPermissionInput
                {
                    command = "IssuePolicyCommand",
                    userExpression = $"username = '{username}'",
                    action = policies_AttachedRuleAllowAction.DENY,
                    commandExpression = @"issuerNumber = '{$usr.username}'",
                    objectExpression = $"description = '{description}'",
                    description = $"Disallow to issue policy with issuer number set to username"
                }
            }), new policies_ResultOfCreatedStatusBuilder().status().errors()).Build();


            await client.SendMutationAndEnsureSuccessAsync(permissionMutation);

            var issuePolicyMutation = new MutationBuilder().issuePolicy(new MutationBuilder.issuePolicyArgs(policyId, username), new policyResultBuilder().status().errors());

            var result = await client.SendMutationAsync<policyResult>(issuePolicyMutation);

            result.status.Should().Be("failure");
            result.errors!.Single().Should().Contain("Description: 'Disallow to issue policy with issuer number set to username'");

        }
    }
}