using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using FluentAssertions;
using GraphQL.Client.Http;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.AttachedRules
{
    public class PoliciesAttachedFiltersTests : AttachedRulesTestBase
    {
        public PoliciesAttachedFiltersTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact(Skip = "Temporary disabled due to CI issues")]
        public async Task GIVEN_simple_attached_filter_WHEN_querying_policies_THEN_filter_is_correct()
        {
            (string username, GraphQLHttpClient client) = await InitilizeTestUserAndClient();
            var policiesCrud = new Policy(client);

            var policyDescription1 = CreateNewGuid();
            var policyDescription2 = CreateNewGuid();

            var policy1Id = await policiesCrud.Create(new initializePolicyInput { description = policyDescription1 });
            var policy2Id = await policiesCrud.Create(new initializePolicyInput { description = policyDescription2 });

            var policies = await policiesCrud.FindList(new policyWhereInput { id_in = new[] { policy1Id, policy2Id } });

            policies.Should().HaveCount(2);

            var mutation = new MutationBuilder().policies_AttachedFilterMutationCreate(new MutationBuilder.policies_AttachedFilterMutationCreateArgs(command: new policies_CreateAttachedRuleCommandOfpoliciesAttachedFilterInput
            {
                create = new policies_policiesAttachedFilterInput
                {
                    userExpression = $"username = '{username}'",
                    objectExpression = $"description = '{policyDescription1}'"
                }
            }), new policies_ResultOfCreatedStatusBuilder().status()).Build();

            await client.SendMutationAndEnsureSuccessAsync(mutation);

            policies = await policiesCrud.FindList(new policyWhereInput()); // no any filters here

            policies.Should().HaveCount(1);

            policies![0].id.Should().Be(policy1Id);
        }

        [Fact(Skip = "Temporary disabled due to CI issues")]
        public async Task GIVEN_login_AND_complexive_attached_filter_WHEN_querying_policies_THEN_filter_is_correct()
        {
            (string username, GraphQLHttpClient client) = await InitilizeTestUserAndClient();
            var policiesCrud = new Policy(client);

            var nestedValue = CreateNewGuid();

            var policy1Id = await policiesCrud.Create(new initializePolicyInput
            {
                fields = new
                {
                    items = new object[]{
                        new {
                            key = "someValue",
                            nested = new object[]{
                                new{
                                    nestedKey = nestedValue
                                },
                                new{
                                    nestedKey = "nestedValue2"
                                }
                            }
                        },
                        new {
                            key = "someValue1",
                            nested = new object[]{
                                new{
                                    nestedKey = "nestedValue3"
                                },
                                new{
                                    nestedKey = "nestedValue4"
                                }
                            }
                        }
                    }
                }.ToEscapedJsonString()
            });

            var policy2Id = await policiesCrud.Create(new initializePolicyInput
            {
                fields = new
                {
                    items = new object[]{
                        new {
                            key = "someValue",
                            nested = new object[]{
                                new{
                                    nestedKey = "nestedValue5"
                                },
                                new{
                                    nestedKey = "nestedValue6"
                                }
                            }
                        },
                        new {
                            key = "someValue1",
                            nested = new object[]{
                                new{
                                    nestedKey = "nestedValue7"
                                },
                                new{
                                    nestedKey = "nestedValue8"
                                }
                            }
                        }
                    }
                }.ToEscapedJsonString()
            });

            var policies = await policiesCrud.FindList(new policyWhereInput { id_in = new[] { policy1Id, policy2Id } });

            policies.Should().HaveCount(2);

            var mutation = new MutationBuilder().policies_AttachedFilterMutationCreate(new MutationBuilder.policies_AttachedFilterMutationCreateArgs(command: new policies_CreateAttachedRuleCommandOfpoliciesAttachedFilterInput
            {
                create = new policies_policiesAttachedFilterInput
                {
                    userExpression = $"username = '{username}'",
                    objectExpression = $"fields.items[any] = ( nested[any] = ( nestedKey = '{nestedValue}' & nestedKey != 'nestedValue2' ))"
                }
            }), new policies_ResultOfCreatedStatusBuilder().status()).Build();

            await client.SendMutationAndEnsureSuccessAsync(mutation);

            policies = await policiesCrud.FindList(new policyWhereInput()); // no any filters here

            policies.Should().HaveCount(1);

            policies![0].id.Should().Be(policy1Id);
        }
    }
}