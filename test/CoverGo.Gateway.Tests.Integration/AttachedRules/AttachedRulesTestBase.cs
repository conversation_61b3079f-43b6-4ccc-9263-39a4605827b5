using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using CoverGo.Configuration;
using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using CoverGo.MongoUtils;
using GraphQL.Client.Http;
using GraphQL.Client.Serializer.Newtonsoft;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.MongoDB;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Conventions;
using MongoDB.Driver;

using Xunit.Abstractions;

namespace CoverGo.Gateway.Tests.Integration.AttachedRules
{
    public abstract class AttachedRulesTestBase : TestsBase
    {
        protected static readonly GraphQLClientConfig ClientConfig = GraphQLClientConfig.Local.Load();

        protected AttachedRulesTestBase(ITestOutputHelper output) : base(output)
        {
        }

        protected async Task<(string username, GraphQLHttpClient client)> InitilizeTestUserAndClient()
        {
            var username = $"{CreateNewGuid()}@gmail.com";
            var password = CreateNewGuid();

            var config = new DbConfig()
            {
                ProviderId = "mongoDb",
                ConnectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECT_STRING") ?? "****************************************"
            };

            var conventionPack = new ConventionPack
                {
                    new CamelCaseElementNameConvention(),
                    new EnumRepresentationConvention(BsonType.String),
                    new IgnoreExtraElementsConvention(true),
                    new IgnoreIfNullConvention(true)
                };
            ConventionRegistry.Register("all", conventionPack, t => true);

            MongoClient mongoClient = MongoTools.GetOrAddMongoClient(config);
            IMongoDatabase db = mongoClient.GetDatabase("auth");

            var envConfig = GraphQLClientConfig.Local.Load();

            var passwordHasher = new PasswordHasher<MongoLoginDao> { };
            IUserStore<MongoLoginDao> userStore = new UserStore<MongoLoginDao>(db.GetCollection<MongoLoginDao>($"{envConfig.TenantId}-users"));

            var userManager = new UserManager<MongoLoginDao>(userStore, null, passwordHasher, Enumerable.Empty<IUserValidator<MongoLoginDao>>(), Enumerable.Empty<IPasswordValidator<MongoLoginDao>>(), new UpperInvariantLookupNormalizer { }, null, null, null);

            var loginDao = new MongoLoginDao
            {
                UserName = username,
                Email = username,
                EmailConfirmed = true,

                Claims = new List<IdentityUserClaim> {
                        new(new System.Security.Claims.Claim("clientId", "admin")),
                        new(new System.Security.Claims.Claim("role", "admin"))
                    },

                CreatedAt = DateTime.UtcNow,
                LastModifiedAt = DateTime.UtcNow
            };

            await userManager.CreateAsync(loginDao, password);

            QueryBuilder.token_2Args tokenArgs = new(ClientConfig.TenantId, ClientConfig.ClientId, username, password);
            GraphQLHttpClient client = new(ClientConfig.GatewayGraphQLUrl, new NewtonsoftJsonSerializer());
            client.HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", await new Token(client).Fetch(tokenArgs));
            return (username, client);
        }
    }
}