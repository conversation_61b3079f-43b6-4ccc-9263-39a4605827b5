using System;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using FluentAssertions;
using GraphQL.Client.Http;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Permissions;

public class CasePermissionTests : PermissionTestsBase
{
    public CasePermissionTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task GIVEN_user_has_no_writeCases_permission_WHEN_create_case_THEN_cannot_create()
    {
        var client = await BuildClientWithoutPermission();

        await ShouldNotHavePermission(() => CreateCase(client), "createCases|writeCases");
    }

    [Fact]
    public async Task GIVEN_user_has_no_readCases_permission_WHEN_read_THEN_cannot_read_case()
    {
        var client = await BuildClientWithoutPermission();
        var caseId = await CreateCase();

        var @case = await GetCase(client, caseId);

        @case.Should().BeNull();
    }

    [Fact]
    public async Task GIVEN_user_has_readCases_caseIdIfProposalReferrer_permission_WHEN_read_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("readCases", "{caseIdIfProposalReferrer}")
            .InternalUser()
            .Build();
        var caseId = await CreateCase();
        var internalCode = await GetEntityInternalCode(context.EntityId);
        await AddProposalToCase(caseId, internalCode);

        await ShouldReadCase(context.Client, caseId);
    }

    [Fact]
    public async Task GIVEN_user_has_readCases_caseIdIfHolder_permission_WHEN_read_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("readCases", "{caseIdIfHolder}")
            .Build();
        var caseId = await CreateCase(context.EntityId);

        await ShouldReadCase(context.Client, caseId);
    }

    [Fact]
    public async Task GIVEN_user_has_readCases_caseIdIfStakeholder_permission_WHEN_read_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("readCases", "{caseIdIfStakeholder}")
            .Build();
        var caseId = await CreateCase();
        await AddStakeHolder(caseId, context.EntityId);

        await ShouldReadCase(context.Client, caseId);
    }

    [Theory]
    [InlineData("updateCases")]
    [InlineData("writeCases")]
    public async Task
        GIVEN_user_has_read_update_or_write_cases_creatorRights_permission_WHEN_write_and_read_THEN_should_write_and_read(string updatePermissionName)
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("readCases", "{creatorRights}")
            .WithPermission("createCases", "{creatorRights}")
            .WithPermission(updatePermissionName, "{creatorRights}")
            .Build();
        var caseId = await CreateCase(context.Client);

        await ShouldUpdateCase(context.Client, caseId);
        await ShouldReadCase(context.Client, caseId);
    }

    [Theory]
    [InlineData("updateCases")]
    [InlineData("writeCases")]
    public async Task
        GIVEN_user_has_read_write_cases_caseIdIfSourceContains_permission_WHEN_write_and_read_THEN_should_write_and_read(string updatePermissionName)
    {
        var source = CreateNewGuid();
        var context = await new PermissionTestContextBuilder()
            .WithPermission("readCases", $"{{caseIdIfSourceContains= {source}, {CreateNewGuid()} }}")
            .WithPermission(updatePermissionName, $"{{caseIdIfSourceContains= {source}, {CreateNewGuid()} }}")
            .Build();
        var caseId = await CreateCase(source: source);

        await ShouldUpdateCase(context.Client, caseId);
        await ShouldReadCase(context.Client, caseId);
    }

    [Theory]
    [InlineData("updateCases")]
    [InlineData("writeCases")]
    public async Task
        GIVEN_user_has_read_write_cases_caseIdIf_fromLinkSource_IsHolder_permission_WHEN_write_and_read_THEN_should_write_and_read(string updatePermissionName)
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("readCases", "{caseIdIf{fromLinkSource:hrOf:{entityId}}IsHolder}}")
            .WithPermission(updatePermissionName, "{caseIdIf{fromLinkSource:hrOf:{entityId}}IsHolder}}")
            .Build();
        var contractHolderId = await CreateIndividual();
        var caseId = await CreateCase(holderId: contractHolderId);
        await AddLink(context.EntityId, contractHolderId);

        await ShouldUpdateCase(context.Client, caseId);
        await ShouldReadCase(context.Client, caseId);
    }

    [Fact]
    public async Task
        GIVEN_user_has_read_write_proposals_proposalIdIf_fromLinkSource_IsHolder_permission_WHEN_write_and_read_THEN_should_write_and_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("readProposals", "{proposalIdIf{fromLinkSource:hrOf:{entityId}}IsHolder}")
            .WithPermission("readCases", "{caseIdIf{fromLinkSource:hrOf:{entityId}}IsHolder}}")
            .WithPermission("writeProposals", "{proposalIdIf{fromLinkSource:hrOf:{entityId}}IsHolder}")
            .WithPermission("writeProposals", "{proposalIdIfStakeholder}")
            .InternalUser()
            .Build();

        var holderId = await CreateIndividual();
        var caseId = await CreateCase(holderId: holderId);
        var proposalIdOld = await AddProposalToCase(caseId);
        var proposalIdNew = await AddProposalToCase(caseId, renewedToId: proposalIdOld);
        await AddLink(context.EntityId, holderId);

        await ShouldWriteProposal(context.Client, caseId, proposalIdNew);
        await ShouldReadProposal(context.Client, caseId, proposalIdNew);
    }

    [Fact]
    public async Task
        GIVEN_user_has_write_proposals_with_proposal_if_stakeholder_permission_WHEN_add_offer_to_proposal_THEN_should_not_throw_error()
    {
        var contextWithPermission = await new PermissionTestContextBuilder()
           .WithPermission("writeProposals", "{proposalIdIfStakeholder}")
           .InternalUser()
           .Build();

        var holderId = await CreateIndividual();
        var caseId = await CreateCase(holderId: holderId);
        await AddStakeHolder(caseId, contextWithPermission.EntityId);
        var proposalId = await AddProposalToCase(caseId);

        await ShouldAddOfferToProposal(contextWithPermission.Client, caseId, proposalId);
    }

    [Fact]
    public async Task
        GIVEN_user_has_write_proposals_with_proposal_if_stakeholder_permission_WHEN_add_offer_to_proposal_THEN_should_throw_error()
    {
        var holderId = await CreateIndividual();
        var caseId = await CreateCase(holderId: holderId);
        var proposalId = await AddProposalToCase(caseId);

        var contextWithoutPermission = await new PermissionTestContextBuilder()
           .InternalUser()
           .Build();

        await ShouldNotAddOfferToProposal(contextWithoutPermission.Client, caseId, proposalId);
    }

    private async Task ShouldWriteProposal(GraphQLHttpClient client, string caseId, string proposalId)
    {
        var input = new updateProposalInput { name = CreateNewGuid() };
        var proposalClient = new Proposal(client);
        Func<Task> write = () => proposalClient.Update(caseId, proposalId, input);

        await write.Should().NotThrowAsync();
    }

    private async Task ShouldAddOfferToProposal(GraphQLHttpClient client, string caseId, string proposalId)
    {
        var input = JsonSerializer.Deserialize<addOfferInput>(@$"
                                    {{
                                        ""status"": ""Added"",
                                        ""productId"": {{
                                          ""plan"": ""00047609"",
                                          ""type"": ""gm"",
                                          ""version"": ""1.0_custom_669568674""
                                        }}
                                    }}
                                    ")!;
        var proposalClient = new Proposal(client);
        Func<Task> write = () => proposalClient.AddOfferToProposal(caseId, proposalId, input);

        await write.Should().NotThrowAsync();
    }

    private async Task ShouldNotAddOfferToProposal(GraphQLHttpClient client, string caseId, string proposalId)
    {
        var input = JsonSerializer.Deserialize<addOfferInput>(@$"
                                    {{
                                        ""status"": ""Added"",
                                        ""productId"": {{
                                          ""plan"": ""00047609"",
                                          ""type"": ""gm"",
                                          ""version"": ""1.0_custom_669568674""
                                        }}
                                    }}
                                    ")!;
        var proposalClient = new Proposal(client);
        Func<Task> write = () => proposalClient.AddOfferToProposal(caseId, proposalId, input);

        await write.Should().ThrowAsync<Exception>();
    }

    private async Task ShouldReadProposal(GraphQLHttpClient client, string caseId , string proposalId)
    {
        var @case = await GetCase(client, caseId);

        @case!.proposals!.First(p => p!.id == proposalId)!.renewalHistory!.renewedTo!.id.Should().NotBeNull();
    }


    private async Task ShouldReadCase(GraphQLHttpClient client, string caseId)
    {
        var @case = await GetCase(client, caseId);

        @case.Should().NotBeNull();
    }

    private async Task ShouldUpdateCase(GraphQLHttpClient client, string caseId)
    {
        var update = new updateCaseInput { description = CreateNewGuid() };
        var caseClient = new Case(client);
        Func<Task> write = () => caseClient.Update(caseId, update);

        await write.Should().NotThrowAsync();
    }

    private Task AddStakeHolder(string caseId, string entityId)
    {
        var input = new addStakeholderInput
        {
            entityId = entityId,
            type = CreateNewGuid(),
        };
        return Case.AddStakeHolder(caseId, input);
    }

    private Task<string> AddProposalToCase(string caseId, string? referralCode = default, string? renewedToId = default)
    {
        var proposal = new addProposalInput
        {
            proposalNumber = CreateNewGuid(),
            renewalHistory = new renewalHistoryInput
            {
                renewedToId = renewedToId
            },
            referralCode = referralCode
        };
        return Proposal.Create(caseId, proposal);
    }

    private Task<@case?> GetCase(GraphQLHttpClient client, string caseId)
    {
        var caseClient = new Case(client);

        return caseClient.FindById(caseId);
    }

    private Task<string> CreateCase(GraphQLHttpClient client)
    {
        var caseClient = new Case(client);
        var input = new createCaseInput
        {
            name = CreateNewGuid(),
        };
        return caseClient.Create(input);
    }

    private Task<string> CreateCase(string? holderId = default, string? source = default)
    {
        var input = new createCaseInput
        {
            name = CreateNewGuid(),
            holderId = holderId,
            source = source,
        };

        return Case.Create(input);
    }
}