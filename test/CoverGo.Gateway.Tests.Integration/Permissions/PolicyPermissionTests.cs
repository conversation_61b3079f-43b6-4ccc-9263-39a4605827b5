using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using FluentAssertions;
using GraphQL.Client.Http;
using Xunit;
using Xunit.Abstractions;
using Entity = CoverGo.Gateway.Tests.Integration.Crud.Entity;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Permissions;

public class PolicyPermissionTests : PermissionTestsBase
{
    public PolicyPermissionTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task
        GIVEN_user_has_no_writePolicies_permission_WHEN_createPolicy_THEN_cannot_create_policy()
    {
        var client = await BuildClientWithoutPermission();

        await ShouldNotHavePermission(() => CreatePolicy(client), "createPolicies|writePolicies");
    }

    [Fact]
    public async Task GIVEN_user_has_no_readPolicies_permission_WHEN_query_THEN_cannot_read_policy()
    {
        var client = await BuildClientWithoutPermission();
        var policyId = await CreatePolicy();

        var policy = await GetPolicy(client, policyId);

        policy.Should().BeNull();
    }

    [Fact]
    public async Task GIVEN_user_has_readPolicy_policyIdIfHolder_permission_WHEN_read_policy_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .WithPermission("readPolicies", "{policyIdIfHolder}")
            .Build();
        var policyId = await CreatePolicy(context.Client, context.EntityId);

        await ShouldReadPolicy(context.Client, policyId);
    }

    [Fact]
    public async Task GIVEN_user_has_readPolicy_policyIdIfInsured_permission_WHEN_read_insured_policy_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .WithPermission("readPolicies", "{policyIdIfInsured}")
            .Build();
        var policyId = await CreatePolicy(context.Client, insuredIds: new List<string?> { context.EntityId });

        await ShouldReadPolicy(context.Client, policyId);
    }

    [Fact]
    public async Task GIVEN_user_has_readPolicy_policyIdIfUninsured_permission_AND_contract_terminated_WHEN_read_policy_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .WithPermission("readPolicies", "{policyIdIfUninsured}")
            .Build();
        var policyId = await CreatePolicy(context.Client, insuredIds: new List<string?> { context.EntityId });
        await TerminateContract(policyId, context.EntityId);

        await ShouldReadPolicy(context.Client, policyId);
    }

    [Fact]
    public async Task GIVEN_user_has_readPolicy_policyIdIfReferrer_permission_WHEN_read_referred_policy_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .WithPermission("readPolicies", "{policyIdIfReferrer}")
            .InternalUser()
            .Build();
        var internalCode = await GetEntityInternalCode(context.EntityId);
        var policyId = await CreatePolicy(context.Client, referralCode: internalCode);

        await ShouldReadPolicy(context.Client, policyId);
    }

    [Fact]
    public async Task GIVEN_user_has_readPolicy_policyIdIfRenewal_permission_WHEN_read_renewed_policy_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .WithPermission("readPolicies", "{policyIdIfRenewal}")
            .Build();
        var policyId = await CreatePolicy(context.Client);
        policyId = await RenewPolicy(policyId);

        await ShouldReadPolicy(context.Client, policyId);
    }

    [Fact]
    public async Task GIVEN_user_has_cancelPolicy_creatorRights_permission_WHEN_cancel_policy_THEN_should_cancel()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .WithPermission("readPolicies", "{creatorRights}")
            .WithPermission("cancelPolicies", "{creatorRights}")
            .Build();
        var policyId = await CreatePolicy(context.Client);
        await CancelPolicy(context.Client, policyId);

        var policy = await GetPolicy(context.Client, policyId);

        policy!.cancelledBy!.id.Should().Be(context.LoginId);
    }

    [Fact]
    public async Task GIVEN_user_has_no_cancelPolicy_permission_WHEN_cancel_policy_THEN_should_not_cancel()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .Build();
        var policyId = await CreatePolicy(context.Client);
        Func<Task> cancel = () => CancelPolicy(context.Client, policyId);

        (await cancel.Should().ThrowAsync<Exception>())
            .Where(e => e.Message.Contains($"missing 'cancelPolicies:{policyId}' permission."));
    }

    [Theory]
    [InlineData("updatePolicies")]
    [InlineData("writePolicies")]
    public async Task GIVEN_user_has_writePolicy_creatorRights_permission_WHEN_update_policy_THEN_should_update(
        string writePoliciesPermission)
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .WithPermission(writePoliciesPermission, "{creatorRights}")
            .WithPermission("readPolicies", "{creatorRights}")
            .Build();
        var policyId = await CreatePolicy(context.Client);

        await UpdatePolicy(context.Client, policyId);

        await ShouldReadPolicy(context.Client, policyId);
    }

    [Fact]
    public async Task GIVEN_user_has_readPolicy_creatorRights_permission_WHEN_read_policy_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .WithPermission("readPolicies", "{creatorRights}")
            .Build();
        var policyId = await CreatePolicy(context.Client);

        await ShouldReadPolicy(context.Client, policyId);
    }

    [Theory]
    [InlineData("updatePolicies")]
    [InlineData("writePolicies")]
    public async Task
        GIVEN_user_has_permission_on_individual_policy_holder_WHEN_write_and_read_policy_THEN_should_write_and_read(
            string writePermissionName)
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("writeIndividuals", "{creatorRights}")
            .WithPermission("readIndividuals", "{creatorRights}")
            .WithPermission("createIndividuals", "all")
            .WithPermission("createPolicies", "all")
            .WithPermission(writePermissionName, "{policyIdIf{allowedWriteIndividuals}IsHolder}")
            .WithPermission("readPolicies", "{policyIdIf{allowedReadIndividuals}IsHolder}")
            .Build();
        var contractHolderId = await CreateIndividual(context.Client);
        var policyId = await CreatePolicy(context.Client, contractHolderId);

        await UpdatePolicy(context.Client, policyId);

        await ShouldReadPolicy(context.Client, policyId);
    }

    [Theory]
    [InlineData("updatePolicies")]
    [InlineData("writePolicies")]
    public async Task GIVEN_user_has_readPolicies_and_writePolicies_fromLinkSource_IsHolder_permission_WHEN_read_policy_THEN_should_read(
        string writePermissionName)
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .WithPermission(writePermissionName, "{policyIdIf{fromLinkSource:hrOf:{entityId}}IsHolder}")
            .WithPermission("readPolicies", "{policyIdIf{fromLinkSource:hrOf:{entityId}}IsHolder}")
            .Build();
        var contractHolderId = await CreateIndividual();
        var policyId = await CreatePolicy(context.Client, contractHolderId);
        await AddLink(context.EntityId, contractHolderId);

        await UpdatePolicy(context.Client, policyId);

        await ShouldReadPolicy(context.Client, policyId);
    }

    [Theory]
    [InlineData("updatePolicies")]
    [InlineData("writePolicies")]
    public async Task GIVEN_user_has_readPolicies_and_writePolicies_fromLinkSource_IsInsured_permission_WHEN_read_policy_THEN_should_read(
        string writePermissionName)
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .WithPermission(writePermissionName, "{policyIdIf{fromLinkSource:hrOf:{entityId}}IsInsured}")
            .WithPermission("readPolicies", "{policyIdIf{fromLinkSource:hrOf:{entityId}}IsInsured}")
            .Build();

        var insuredId = await CreateIndividual();
        var policyId = await CreatePolicy(context.Client, insuredIds: new List<string?> { insuredId });
        await AddLink(context.EntityId, insuredId);

        await UpdatePolicy(context.Client, policyId);

        await ShouldReadPolicy(context.Client, policyId);
    }

    [Theory]
    [InlineData("updatePolicies")]
    [InlineData("writePolicies")]
    public async Task GIVEN_user_has_readPolicies_and_writePolicies_fromLinkSource_IsUninsured_permission_WHEN_read_policy_THEN_should_read(
        string writePermissionName)
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .WithPermission(writePermissionName, "{policyIdIf{fromLinkSource:hrOf:{entityId}}IsUninsured}")
            .WithPermission("readPolicies", "{policyIdIf{fromLinkSource:hrOf:{entityId}}IsUninsured}")
            .Build();

        var insuredId = await CreateIndividual();
        var policyId = await CreatePolicy(context.Client, insuredIds: new List<string?> { insuredId });
        await AddLink(context.EntityId, insuredId);
        await TerminateContract(policyId, insuredId);

        await UpdatePolicy(context.Client, policyId);

        await ShouldReadPolicy(context.Client, policyId);
    }

    [Fact]
    public async Task
        GIVEN_user_has_not_readFiles_allowedReadPolicies_with_policyNumberIfHolder_permission_WHEN_read_file_THEN_cannot_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .WithPermission("readPolicies", "{policyNumberIfHolder}")
            .Build();
        var policyId = await CreatePolicy(context.Client, context.EntityId);
        var fileKey = await UploadPolicyFile(policyId);

        var fileSummary = await GetFile(context.Client, fileKey);

        fileSummary.Should().BeNull();
    }

    [Fact]
    public async Task
        GIVEN_user_has_readFiles_allowedReadPolicies_with_policyNumberIfHolder_permission_WHEN_read_file_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .WithPermission("readPolicies", "{policyNumberIfHolder}")
            .WithPermission("readFiles", "policies/{allowedReadPolicies}")
            .Build();
        var policyId = await CreatePolicy(context.Client, context.EntityId);
        var fileKey = await UploadPolicyFile(policyId);

        var fileSummary = await GetFile(context.Client, fileKey);

        fileSummary.Should().NotBeNull();
    }

    [Fact]
    public async Task
        GIVEN_user_has_readPolicy_and_readTransactions_permissions_WHEN_read_endorsements_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .WithPermission("readPolicies", "{policyIdIf{fromLinkSource:hrOf:{entityId}}IsHolder}")
            .WithPermission("readTransactions", "{transactionIdIf{allowedReadPolicies}}")
            .Build();
        var insuredId = await CreateIndividual();
        var policyId = await CreatePolicy(context.Client, holderId: insuredId);
        await CreateTransaction(policyId);
        await AddLink(context.EntityId, insuredId);

        var policy = await GetPolicy(context.Client, policyId);
        policy!.endorsements!.First()!.transactions.Should().HaveCountGreaterOrEqualTo(1);
    }

    private async Task<string> CreateTransaction(string policyId)
    {
        var endorsementId = await Policy.Endorsement.Add(policyId);
        await Policy.Endorsement.Accept(policyId, endorsementId);
        return await Transaction.Create(new createTransactionInput
        {
            endorsementId = endorsementId,
            policyId = policyId,
            amount = 100
        });
    }

    private async Task<fileSummary?> GetFile(GraphQLHttpClient client, string fileKey)
    {
        var fileSystemClient = new FileSystem(client);

        try
        {
            return await fileSystemClient.GetFile(fileKey);
        }
        catch (Exception exception) when (exception.Message == "Cannot deserialize result")
        {
            // this is due to the response difference between file listing query and others
            return null;
        }
    }

    private async Task<string> UploadPolicyFile(string policyId)
    {
        await FileSystem.InitializeTenant();
        var policy = await Policy.FindById(policyId);
        var fileName = $"{policy!.issuerNumber}.txt";
        await File.WriteAllTextAsync(fileName, Guid.NewGuid().ToString());
        var filePath = $"policies/{fileName}";
        await FileSystem.Upload(filePath, fileName);

        return filePath;
    }

    private async Task TerminateContract(string policyId, string entityId)
    {
        string endorsementId = await Policy.Endorsement.Add(policyId);
        await Policy.RemoveContractInsured(policyId, entityId, endorsementId);
        await Policy.Endorsement.Accept(policyId, endorsementId);
    }

    private Task UpdatePolicy(GraphQLHttpClient client, string policyId)
    {
        var policiesClient = new Policy(client);

        return policiesClient.AddTag(policyId, new tagInput { type = CreateNewGuid() });
    }

    private async Task ShouldReadPolicy(GraphQLHttpClient client, string policyId)
    {
        var policy = await GetPolicy(client, policyId);
        policy.Should().NotBeNull();
    }

    private async Task ShouldReadMembers(GraphQLHttpClient client, string policyId)
    {
        var policy = await GetPolicyWithMembers(client, policyId);
        policy.members!.totalCount.Should().BeGreaterOrEqualTo(1);
    }

    private Task CancelPolicy(GraphQLHttpClient client, string policyId)
    {
        var policiesClient = new Policy(client);

        return policiesClient.CancelPolicy(policyId);
    }

    private async Task<string> RenewPolicy(string policyId)
    {
        var policy = await Policy.FindById(policyId);
        return await Policy.Create(new initializePolicyInput
        {
            fields = "{}",
            description = CreateNewGuid(),
            fieldsSchemaId = CreateNewGuid(),
            issuerNumber = CreateNewGuid(),
            originalIssuerNumber = policy!.isRenewal ?? false ? policy.originalIssuerNumber : policy.issuerNumber,
            isRenewal = true,
            renewalVersion = (policy.renewalVersion ?? 0) + 1,

        });
    }

    private Task<string> CreateIndividual(GraphQLHttpClient client)
    {
        var entitiesClient = new Entity(client);

        return entitiesClient.CreateIndividual();
    }

    private Task<policy?> GetPolicy(GraphQLHttpClient client, string id)
    {
        var policiesClient = new Policy(client);
        return policiesClient.FindById(id);
    }

    private Task<policy> GetPolicyWithMembers(GraphQLHttpClient client, string id)
    {
        var policiesClient = new Policy(client);
        return policiesClient.GetWithMembers(id);
    }

    private async Task CreateMembers(string policyId)
    {
        var endorsementId = await Policy.Endorsement.Add(policyId);
        var individualId = await Entity.CreateIndividual();
        var memberId = Guid.NewGuid().ToString().Substring(0, 6);

        await Policy.PolicyMember.Batch(policyId, endorsementId,
            new policyMembersBatchInput
            {
                create = new List<policyMemberInput?> { new()
                {
                    memberId = memberId,
                    fields = new { individualId }.ToEscapedJsonString()
                }}
            });

        await Policy.Endorsement.Accept(policyId, endorsementId);
        await Policy.Issue(policyId);
    }
}