using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using FluentAssertions;
using GraphQL.Client.Http;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Permissions;

public class TransactionPermissionTests : PermissionTestsBase
{
    public TransactionPermissionTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task
        GIVEN_user_has_no_writeOrCreateTransactions_permission_WHEN_createTransaction_THEN_cannot_create_transaction()
    {
        var client = await BuildClientWithoutPermission();

        await ShouldNotHavePermission(() => CreateTransaction(client), "createTransactions|writeTransactions");
    }

    [Fact]
    public async Task GIVEN_user_has_no_readTransaction_permission_WHEN_query_THEN_cannot_read_transaction()
    {
        var client = await BuildClientWithoutPermission();
        var transactionId = await CreateTransaction();

        var transaction = await GetTransaction(client, transactionId);

        transaction.Should().BeNull();
    }

    [Fact]
    public async Task
        GIVEN_user_has_readTransaction_transactionIdForPolicyIdIfHolder_permission_WHEN_query_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createPolicies", "all")
            .WithPermission("createTransactions", "all")
            .WithPermission("readTransactions", "{transactionIdForPolicyIdIfHolder}")
            .Build();
        var policyId = await CreatePolicy(context.Client, context.EntityId);
        var transactionId = await CreateTransaction(context.Client, policyId);

        await ShouldReadTransaction(context.Client, transactionId);
    }

    [Fact]
    public async Task
        GIVEN_user_has_readTransaction_transactionIdForClaimIdIfClaimant_permission_WHEN_query_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createTransactions", "all")
            .WithPermission("readClaims", "{claimIdIfClaimant}")
            .WithPermission("readTransactions", "{transactionIdForClaimIdIfClaimant}")
            .Build();
        var claimId = await CreateClaim(context.EntityId);
        var transactionId = await CreateTransaction(context.Client, claimId: claimId);

        await ShouldReadTransaction(context.Client, transactionId);
    }

    [Fact]
    public async Task GIVEN_user_has_read_create_transaction_creatorRights_WHEN_create_and_query_THEN_should_create_and_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("createTransactions", "all")
            .WithPermission("readTransactions", "{creatorRights}")
            .Build();
        var transactionId = await CreateTransaction(context.Client);

        await ShouldReadTransaction(context.Client, transactionId);
    }

    [Fact]
    public async Task
        GIVEN_user_has_write_paymentMethods_creatorRights_WHEN_create_payment_THEN_should_create()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("writePaymentMethods", "{creatorRights}")
            .Build();
        Func<Task> create = () => CreatePaymentMethod(context.Client, context.EntityId);

        await create.Should().NotThrowAsync();
    }

    [Fact]
    public async Task
        GIVEN_user_has_readTransactions_transactionIdIf_allowedReadPolicies_permission_WHEN_read_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("readPolicies", "{policyIdIfHolder}")
            .WithPermission("createTransactions", "all")
            .WithPermission("readTransactions", "{transactionIdIf{allowedReadPolicies}}")
            .Build();
        var policyId = await CreatePolicy(context.EntityId);

        var transactionId = await CreateTransaction(context.Client, policyId: policyId);

        await ShouldReadTransaction(context.Client, transactionId);
    }

    [Fact]
    public async Task
        GIVEN_user_has_readTransaction_transactionIdForClaimIdIf_fromLinkSource_IsClaimant_permission_WHEN_read_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
              .WithPermission("readTransactions", "{transactionIdForClaimIdIf{fromLinkSource:child:{entityId}}IsClaimant}")
              .Build();
        var claimantId = await CreateIndividual();
        var claimId = await CreateClaim(claimantId);
        await AddLink(context.EntityId, claimantId, "child");
        var transactionId = await CreateTransaction(claimId: claimId);

        await ShouldReadTransaction(context.Client, transactionId);
    }

    [Fact]
    public async Task GIVEN_user_has_writeTransaction_permission_WHEN_mutation_batch_THEN_success()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("writeTransactions", "{creatorRights}")
            .Build();

        Func<Task> batch = () => MutationBatch(context.Client);

        await batch.Should().NotThrowAsync();
    }

    [Fact]
    public async Task GIVEN_user_has_not_writeTransaction_permission_WHEN_mutation_batch_THEN_fail()
    {
        var client = await BuildClientWithoutPermission();

        Func<Task> batch = () => MutationBatch(client);

        await batch.Should().ThrowAsync<Exception>();
    }

    private Task MutationBatch(GraphQLHttpClient client)
    {
        string type = CreateNewGuid();

        string mutation = new MutationBuilder()
            .transactionsMutationBatch(new MutationBuilder.transactionsMutationBatchArgs(command:
                    new transactions_BatchTransactionsCommandInput
                    {
                        create = new List<transactions_CreateTransactionCommandInput?> { new() { type = type }, new() { type = type } }
                    }),
                new transactions_ResultOfCreatedStatusBuilder().status())
            .Build();

        return client.SendMutationAndEnsureSuccessAsync(mutation);
    }

    private Task CreatePaymentMethod(GraphQLHttpClient client, string entityId)
    {
        var transactionsClient = new Transaction(client);
        var bankInput = new bankPaymentMethodInput
        {
            bankName = CreateNewGuid(),
            bankNumber = CreateNewGuid(),
            accountHolderName = CreateNewGuid()
        };
        return transactionsClient.CreatePaymentMethod(entityId, bankInput);
    }

    private Task<string> CreateClaim(string claimantId)
    {
        return Claim.Create(new createClaimInput { claimantId = claimantId });
    }

    private async Task ShouldReadTransaction(GraphQLHttpClient client, string transactionId)
    {
        var transaction = await GetTransaction(client, transactionId);
        transaction.Should().NotBeNull();
    }

    private Task<string> CreateTransaction(
        GraphQLHttpClient client,
        string? policyId = default,
        string? claimId = default)
    {
        var transactionsClient = new Transaction(client);
        var input = new createTransactionInput
        {
            amount = 100,
            policyId = policyId,
            claimId = claimId,
        };

        return transactionsClient.Create(input);
    }

    private Task<string> CreateTransaction(string? policyId = default, string? claimId = default)
    {
        var input = new createTransactionInput
        {
            amount = 100,
            policyId = policyId,
            claimId = claimId,
        };

        return Transaction.Create(input);
    }

    private Task<transaction?> GetTransaction(GraphQLHttpClient client, string transactionId)
    {
        var transactionsClient = new Transaction(client);
        var where = new transactionWhereInput { id = transactionId };
        var responseBuilder = new transactionBuilder()
            .id();
        return transactionsClient.Find(where, responseBuilder);
    }
}