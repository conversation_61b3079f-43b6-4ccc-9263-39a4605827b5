using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using FluentAssertions;
using GraphQL.Client.Http;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Permissions;

public class AdvisorPermissionsTests : PermissionTestsBase
{
    public AdvisorPermissionsTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task GIVEN_user_has_no_write_review_permission_WHEN_create_review_THEN_cannot_review()
    {
        var client = await BuildClientWithoutPermission();

        await ShouldNotHavePermission(() => CreateReview(client), "writeReviews");
    }

    [Fact]
    public async Task GIVEN_user_has_write_review_permission_WHEN_review_THEN_should_review()
    {
        var client = await BuildClientWithDefaultPermission();

        var id = await <PERSON><PERSON><PERSON><PERSON>ie<PERSON>(client);

        id.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_user_has_creatorRights_write_review_permission_WHEN_delete_review_THEN_deletes()
    {
        var client = await BuildClientWithDefaultPermission();
        var id = await CreateReview(client);

        Func<Task> deleteFunc = () => DeleteReviewAsync(client, id);

        await deleteFunc.Should().NotThrowAsync<Exception>();
    }

    [Fact]
    public async Task GIVEN_user_has_no_creatorRights_write_review_permission_WHEN_delete_review_THEN_should_not_delete()
    {
        var id = await Advisor.CreateReview(GenerateInput());
        var client = await BuildClientWithDefaultPermission();

        Func<Task> deleteFunc = () => DeleteReviewAsync(client, id);

        (await deleteFunc.Should().ThrowAsync<Exception>())
            .Where(e => e.Message.Contains($"missing 'writeReviews:{id}' permission."));
    }

    private Task DeleteReviewAsync(GraphQLHttpClient client, string reviewId)
    {
        var advisory = new Advisor(client);

        return advisory.DeleteReview(reviewId);
    }

    private Task<string> CreateReview(GraphQLHttpClient client)
    {
        var advisory = new Advisor(client);

        return advisory.CreateReview(GenerateInput());
    }

    private async Task<GraphQLHttpClient> BuildClientWithDefaultPermission()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("writeReviews", "{creatorRights}")
            .Build();

        return context.Client;
    }

    private createReviewInput GenerateInput()
    {
        return new createReviewInput
        {
            scores = new List<scoreInput?>
            {
                new()
                {
                    type = CreateNewGuid(),
                    comment = CreateNewGuid(),
                    value = 1
                }
            },
            comment = CreateNewGuid(),
            relatedId = CreateNewGuid(),
            type = CreateNewGuid(),
        };
    }
}