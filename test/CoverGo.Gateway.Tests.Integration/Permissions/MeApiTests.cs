using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using FluentAssertions;
using GraphQL.Client.Http;
using Xunit;
using Xunit.Abstractions;
using Login = CoverGo.Gateway.Tests.Integration.Crud.Login;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Permissions;

public class MeApiTests : TestsBase
{
    public MeApiTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task GIVEN_user_has_permissions_WHEN_me_called_THEN_unresolved_permissions_should_be_returned()
    {
        var permissionGroupId = await PermissionGroup.Create();
        await PermissionGroup.AddPermission(permissionGroupId, "readCases", "{caseIdIfProposalReferrer}");
        await PermissionGroup.AddPermission(permissionGroupId, "readIndividuals", "{creatorRights}");
        await PermissionGroup.AddPermission(permissionGroupId, "readPolicies", "{creatorRights}");

        var context = await new PermissionTestContextBuilder()
            .WithPermission("groups", permissionGroupId)
            .Build();

        var meLogin = await Me(context.Client);

        meLogin.targettedPermissions.Should()
            .Contain(tp => tp.permission.id == "readCases" &&
                           tp.targetIds.Contains("{caseIdIfProposalReferrer}"));
        meLogin.targettedPermissions.Should()
            .Contain(tp => tp.permission.id == "readIndividuals" &&
                           tp.targetIds.Contains("{creatorRights}"));
        meLogin.targettedPermissions.Should()
            .Contain(tp => tp.permission.id == "readPolicies" &&
                           tp.targetIds.Contains("{creatorRights}"));
    }

    private Task<login> Me(GraphQLHttpClient client)
    {
        var loginClient = new Login(client);

        return loginClient.Me();
    }
}