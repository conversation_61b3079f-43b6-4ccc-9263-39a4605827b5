using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using FluentAssertions;
using GraphQL.Client.Http;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Permissions;

public class FilePermissionTests : PermissionTestsBase
{
    private const string TestFolderPath = "test-files";

    public FilePermissionTests(ITestOutputHelper output)
        : base(output)
    {
    }

    [Fact]
    public async Task GIVEN_user_doesnt_have_admin_role_WHEN_initialize_file_system_THEN_cannot_initialize()
    {
        var client = await BuildClientWithoutPermission();

        await ShouldNotHavePermission(() => InitializeFileSystem(client), "role:admin");
    }

    [Fact]
    public async Task GIVEN_user_has_admin_role_WHEN_initialize_file_system_THEN_should_initialize()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("role", "admin")
            .Build();

        await ShouldInitialize(context.Client);
    }

    [Fact]
    public async Task
        GIVEN_user_doesnt_have_createFileSystemConfig_permission_WHEN_create_fileSystemConfig_THEN_cannot_create()
    {
        var client = await BuildClientWithoutPermission();

        await ShouldNotHavePermission(() => CreateFileSystemConfig(client), "createFileSystemConfigs|writeFileSystemConfigs");
    }

    [Theory]
    [InlineData("createFileSystemConfigs")]
    [InlineData("writeFileSystemConfigs")]
    public async Task GIVEN_user_has_createFileSystemConfigs_permission_WHEN_create_THEN_should_create(string createPermissionName)
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission(createPermissionName, "all")
            .Build();

        await ShouldCreate(context.Client);
    }

    [Fact]
    public async Task GIVEN_user_doesnt_have_readFileSystemConfigs_permission_WHEN_read_config_THEN_cannot_read()
    {
        var client = await BuildClientWithoutPermission();

        await ShouldNotRead(client);
    }

    [Fact]
    public async Task GIVEN_user_has_readFileSystemConfigs_permission_WHEN_read_config_THEN_should_read()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("writeFileSystemConfigs", "all")
            .WithPermission("readFileSystemConfigs", "all")
            .Build();
        await CreateFileSystemConfig(context.Client);

        await ShouldRead(context.Client);
    }

    [Theory]
    [InlineData("updatePolicies", "{allowedUpdatePolicies}")]
    [InlineData("writePolicies", "{allowedWritePolicies}")]
    public async Task GIVEN_user_has_writeFile_policy_permission_WHEN_upload_file_THEN_should_upload(
        string writePermissionName, string targetedPermissionPlaceholder)
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("writeFiles", $"policies/{targetedPermissionPlaceholder}")
            .WithPermission("createPolicies", "all")
            .WithPermission(writePermissionName, "{creatorRights}")
            .Build();
        var policyId = await CreatePolicy(context.Client, context.EntityId);

        await ShouldUpload(context.Client, @$"policies/{policyId}/attachments/file.txt");
    }

    [Fact]
    public async Task GIVEN_user_has_no_lockFile_permission_WHEN_lock_file_THEN_cannot_lock()
    {
        var client = await BuildClientWithoutPermission();
        var filePath = await UploadRandomFile();

        await ShouldNotLock(client, filePath);
    }

    [Fact]
    public async Task GIVEN_user_has_no_lockFile_permission_WHEN_unlock_file_THEN_cannot_unlock()
    {
        var client = await BuildClientWithoutPermission();
        var filePath = await UploadRandomFile();

        await ShouldNotUnlock(client, filePath);
    }

    [Fact]
    public async Task GIVEN_user_has_lockFile_permission_WHEN_lock_and_unlock_THEN_can_lock_and_unlock()
    {
        var context = await new PermissionTestContextBuilder()
            .WithPermission("lockFiles", "all")
            .Build();
        var filePath = await UploadRandomFile();

        await ShouldLock(context.Client, filePath);
        await ShouldUnlock(context.Client, filePath);
    }

    [Fact]
    public async Task GIVEN_user_has_write_permission_for_all_directory_WHEN_copying_file_THEN_file_is_copied()
    {
        PermissionTestContext context = await new PermissionTestContextBuilder()
            .WithPermission("writeFiles", "all")
            .Build();

        await ShouldCopyFile(context);
    }

    [Fact]
    public async Task GIVEN_user_has_write_permission_for_all_subdirectory_WHEN_copying_file_THEN_file_is_copied()
    {
        PermissionTestContext context = await new PermissionTestContextBuilder()
            .WithPermission("writeFiles", $"{TestFolderPath}/all")
            .Build();

        await ShouldCopyFile(context, TestFolderPath);
    }

    [Fact]
    public async Task GIVEN_user_has_write_permission_for_single_subdirectory_WHEN_copying_file_THEN_file_is_copied()
    {
        string sourceSubdirectory = $"{TestFolderPath}/{Guid.NewGuid():N}";
        PermissionTestContext context = await new PermissionTestContextBuilder()
            .WithPermission("writeFiles", sourceSubdirectory)
            .Build();

        await ShouldCopyFile(context, sourceSubdirectory);
    }

    [Theory]
    [InlineData("updateGOPs", "{allowedUpdateGOPs}")]
    [InlineData("writeGOPs", "{allowedWriteGOPs}")]
    public async Task GIVEN_user_has_resolved_write_permission_for_all_directory_WHEN_copying_file_THEN_file_is_copied(
        string writePermissionName, string targetedPermissionPlaceholder)
    {
        PermissionTestContext context = await new PermissionTestContextBuilder()
            .WithPermission(writePermissionName, "all")
            .WithPermission("writeFiles", targetedPermissionPlaceholder)
            .Build();

        await ShouldCopyFile(context);
    }

    [Theory]
    [InlineData("updateGOPs", "{allowedUpdateGOPs}")]
    [InlineData("writeGOPs", "{allowedWriteGOPs}")]
    public async Task GIVEN_user_has_resolved_write_permission_for_all_subdirectory_WHEN_copying_file_THEN_file_is_copied(
        string writePermissionName, string targetedPermissionPlaceholder)
    {
        string sourceSubdirectory = Guid.NewGuid().ToString("N");
        PermissionTestContext context = await new PermissionTestContextBuilder()
            .WithPermission(writePermissionName, "all")
            .WithPermission("writeFiles", $"{sourceSubdirectory}/{targetedPermissionPlaceholder}")
            .Build();

        await ShouldCopyFile(context, sourceSubdirectory);
    }

    [Theory]
    [InlineData("updateGOPs", "{allowedUpdateGOPs}")]
    [InlineData("writeGOPs", "{allowedWriteGOPs}")]
    public async Task GIVEN_user_has_resolved_write_permission_for_single_subdirectory_WHEN_copying_file_THEN_file_is_copied(
        string writePermissionName, string targetedPermissionPlaceholder)
    {
        string gopId = Guid.NewGuid().ToString("N");
        PermissionTestContext context = await new PermissionTestContextBuilder()
            .WithPermission(writePermissionName, $"{gopId}")
            .WithPermission("writeFiles", $"{TestFolderPath}/{targetedPermissionPlaceholder}")
            .Build();

        await ShouldCopyFile(context, $"{TestFolderPath}/{gopId}");
    }

    [Theory]
    [InlineData("updateGOPs", "{allowedUpdateGOPs}")]
    [InlineData("writeGOPs", "{allowedWriteGOPs}")]
    public async Task GIVEN_user_does_not_have_resolved_write_permission_for_all_subdirectory_WHEN_copying_file_THEN_file_is_not_copied(
        string writePermissionName, string targetedPermissionPlaceholder)
    {
        string sourceSubdirectory = Guid.NewGuid().ToString("N");
        PermissionTestContext context = await new PermissionTestContextBuilder()
            .WithPermission(writePermissionName, $"{Guid.NewGuid():N}")
            .WithPermission("writeFiles", $"{sourceSubdirectory}/{targetedPermissionPlaceholder}")
            .Build();

        var copyOp = () => CopyFile(context, sourceSubdirectory);

        await copyOp.Should().ThrowAsync<Exception>().WithMessage("*not authorized*");
    }

    private async Task<string> UploadRandomFile(string directoryPath = TestFolderPath)
    {
        string fileName = $"{Guid.NewGuid():N}.txt";
        string? fileProviderPath = $"{directoryPath}/{fileName}";

        await File.WriteAllTextAsync(fileName, Guid.NewGuid().ToString());
        await FileSystem.Upload(fileProviderPath, fileName);

        return fileProviderPath;
    }

    private async Task ShouldLock(GraphQLHttpClient client, string path)
    {
        await FileSystem.InitializeTenant();
        var fileSystemClient = new FileSystem(client);
        Func<Task> lockFile = () => fileSystemClient.LockFile(path);

        await lockFile.Should().NotThrowAsync<Exception>();
    }

    private async Task ShouldUnlock(GraphQLHttpClient client, string path)
    {
        await FileSystem.InitializeTenant();
        var fileSystemClient = new FileSystem(client);
        Func<Task> unlockFile = () => fileSystemClient.UnlockFile(path);

        await unlockFile.Should().NotThrowAsync<Exception>();
    }

    private async Task ShouldNotLock(GraphQLHttpClient client, string path)
    {
        await FileSystem.InitializeTenant();
        var fileSystemClient = new FileSystem(client);
        Func<Task> lockFile = () => fileSystemClient.LockFile(path);

        await ShouldNotHavePermission(() => lockFile(), $"lockFiles:{path}");
    }

    private async Task ShouldNotUnlock(GraphQLHttpClient client, string path)
    {
        await FileSystem.InitializeTenant();
        var fileSystemClient = new FileSystem(client);
        Func<Task> lockFile = () => fileSystemClient.UnlockFile(path);

        await ShouldNotHavePermission(() => lockFile(), $"lockFiles:{path}");
    }

    private async Task ShouldUpload(GraphQLHttpClient client, string path)
    {
        await FileSystem.InitializeTenant();
        var fileSystemClient = new FileSystem(client);
        string fileName = $"{Guid.NewGuid():N}.txt";
        await File.WriteAllTextAsync(fileName, Guid.NewGuid().ToString());
        Func<Task> upload = () => fileSystemClient.Upload(path, fileName);

        await upload.Should().NotThrowAsync<Exception>();
    }

    private Task InitializeFileSystem(GraphQLHttpClient client)
    {
        var fileSystemClient = new FileSystem(client);

        return fileSystemClient.InitializeTenant();
    }

    private async Task ShouldCreate(GraphQLHttpClient client)
    {
        Func<Task> initialize = () => CreateFileSystemConfig(client);
        await initialize.Should().NotThrowAsync();
    }

    private async Task ShouldInitialize(GraphQLHttpClient client)
    {
        Func<Task> initialize = () => InitializeFileSystem(client);
        await initialize.Should().NotThrowAsync();
    }

    private async Task ShouldRead(GraphQLHttpClient client)
    {
        var configs = await ReadFileSystemConfig(client);

        configs.Should().HaveCountGreaterOrEqualTo(1);
    }

    private async Task ShouldNotRead(GraphQLHttpClient client)
    {
        var configs = await ReadFileSystemConfig(client);

        configs.Should().BeNullOrEmpty();
    }

    private Task CreateFileSystemConfig(GraphQLHttpClient client)
    {
        var fileSystemClient = new FileSystem(client);
        var input = new fileSystemConfigInput
        {
            accessKeyId = CreateNewGuid(),
            accessKeySecret = CreateNewGuid(),
            bucketName = CreateNewGuid(),
            endpoint = CreateNewGuid(),
            providerId = "local"
        };

        return fileSystemClient.CreateFileSystemConfig(input);
    }

    private Task<ICollection<fileSystemConfig?>> ReadFileSystemConfig(GraphQLHttpClient client)
    {
        var fileSystemClient = new FileSystem(client);

        return fileSystemClient.GetFileSystemConfigs();
    }

    private async Task<(result, string, string)> CopyFile(PermissionTestContext context, string? sourceSubdirectory)
    {
        sourceSubdirectory ??= Guid.NewGuid().ToString("N");
        string destinationSubdirectory = Guid.NewGuid().ToString("N");

        await FileSystem.InitializeTenant();

        string sourceFilePath = await UploadRandomFile(sourceSubdirectory);
        string destinationFilePath = $"{destinationSubdirectory}/{sourceFilePath}";

        var userFileSystem = new FileSystem(context.Client);
        result copyOp = await userFileSystem.CopyFiles(new[] { sourceFilePath }, new[] { destinationFilePath });

        return (copyOp, sourceFilePath, destinationFilePath);
    }

    private async Task ShouldCopyFile(PermissionTestContext context, string? sourceSubdirectory = default)
    {
        var (copyOp, sourceFilePath, destinationFilePath) = await CopyFile(context, sourceSubdirectory);

        copyOp.status.Should().Be("success");
        (await FileSystem.GetFile(sourceFilePath)).Should().NotBeNull();
        (await FileSystem.GetFile(destinationFilePath)).Should().NotBeNull();
    }
}