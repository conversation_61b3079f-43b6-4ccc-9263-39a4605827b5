using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;

using GraphQL.Client.Http;

using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Permissions;

public abstract class PermissionTestsBase : TestsBase
{
    protected PermissionTestsBase(ITestOutputHelper output) : base(output)
    {
    }

    protected Task<string> CreatePolicy(string? holderId = default)
    {
        var input = CreateinitializePolicyInput(holderId);

        return Policy.Create(input);
    }

    protected Task<string> CreatePolicy(GraphQLHttpClient client,
        string? holderId = default,
        string? referralCode = default,
        List<string?>? insuredIds = default)
    {
        var input = CreateinitializePolicyInput(holderId, referralCode, insuredIds);
        var policiesClient = new Policy(client);

        return policiesClient.Create(input);
    }


    protected initializePolicyInput CreateinitializePolicyInput(
        string? holderId = default,
        string? referralCode = default,
        List<string?>? insuredIds = default)
    {
        return new initializePolicyInput
        {
            description = "test policy",
            issuerNumber = CreateNewGuid(),
            insuredIds = insuredIds,
            holderId = holderId,
            referralCode = referralCode,
        };
    }

    protected Task AddLink(string sourceId, string targetId, string linkType = "hrOf")
    {
        return Entity.AddLink(sourceId, targetId, linkType);
    }

    protected Task<string> CreateIndividual(string? source = default)
    {
        var input = new createIndividualInput
        {
            email = CreateNewGuid() + "@covergo.com",
            source = source
        };
        return Entity.CreateIndividual(input);
    }

    protected async Task<string> GetEntityInternalCode(string entityId)
    {
        var where = new internalWhereInput
        {
            id = entityId
        };
        var individual = await Entity.FindInternal(where);

        return individual.list!.First()!.internalCode!;
    }
}