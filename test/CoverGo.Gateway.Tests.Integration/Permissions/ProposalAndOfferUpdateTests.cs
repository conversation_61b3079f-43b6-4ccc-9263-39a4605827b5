﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Tests.Integration.Crud;
using GraphQL.Client.Http;
using Xunit;
using Xunit.Abstractions;
using GraphQL.Client.Serializer.Newtonsoft;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Permissions
{
    public class ProposalAndOfferUpdateTests : TestsBase
    {
        public ProposalAndOfferUpdateTests(ITestOutputHelper output) : base(output)
        {
        }


        [Fact]
        public async Task Given_login_WHEN_create_Case_with_Proposal_Then_Proposal_Permissions_are_queryable()
        {
            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();

            string username = CreateNewGuid();
            string dependentUsername = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string dependentEntityId = await Entity.CreateWithAssociatedLogin(dependentUsername);
            await Entity.AddLink(entityId, dependentEntityId, "child");


            GraphQLClientConfig config = GraphQLClientConfig.Local.Load();

            QueryBuilder.token_2Args tokenArgs = new(config.TenantId, config.ClientId, credentials.username, credentials.password);
            GraphQLHttpClient client = new(config.GatewayGraphQLUrl, new NewtonsoftJsonSerializer());
            client.HttpClient.DefaultRequestHeaders.Authorization = new("Bearer", await new Token(client).Fetch(tokenArgs));

            await Login.AddPermission(loginId, "readCases", "{creatorRights}");
            await Login.AddPermission(loginId, "writeCases", "{creatorRights}");

            await Login.AddPermission(loginId, "readProposals", "{creatorRights}");
            await Login.AddPermission(loginId, "writeProposals", "{creatorRights}");
            await Login.AddPermission(loginId, "writeProposals", "{proposalIdIfStakeholder}");

            string caseId = await new Case(client).Create(new() { holderId = dependentEntityId });
            string proposalId = await new Proposal(client).Create(caseId);

            loginWhere filter = new() { ids = new List<string> { loginId } };
            login? login = await GetLoginWithPermissions(filter);

            login.targettedPermissions.First(p => p.permission.id == "writeProposals").targetIds.Should().Contain(proposalId);
        }

        [Fact]
        public async Task Given_login_WHEN_create_Case_with_Proposal_and_Offer_Then_Offer_Permissions_are_queryable()
        {
            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            string username = CreateNewGuid();
            string dependentUsername = CreateNewGuid();
            string entityId = await Entity.CreateWithAssociatedLogin(username);
            string dependentEntityId = await Entity.CreateWithAssociatedLogin(dependentUsername);
            await Entity.AddLink(entityId, dependentEntityId, "child");

            GraphQLClientConfig config = GraphQLClientConfig.Local.Load();

            QueryBuilder.token_2Args tokenArgs = new(config.TenantId, config.ClientId, credentials.username, credentials.password);
            GraphQLHttpClient client = new(config.GatewayGraphQLUrl, new NewtonsoftJsonSerializer());
            client.HttpClient.DefaultRequestHeaders.Authorization = new("Bearer", await new Token(client).Fetch(tokenArgs));

            await Login.AddPermission(loginId, "writeCases", "{creatorRights}");

            await Login.AddPermission(loginId, "readProposals", "{creatorRights}");
            await Login.AddPermission(loginId, "writeProposals", "{creatorRights}");
            await Login.AddPermission(loginId, "writeProposals", "{proposalIdIfStakeholder}");

            string caseId = (await new Case(client).Create(new()
            {
                holderId = dependentEntityId,
            }));

            string proposalId = (await new Proposal(client).Create(caseId));

            await Login.AddPermission(loginId, "readOffers", "{creatorRights}");
            await Login.AddPermission(loginId, "writeOffers", "{creatorRights}");
            await Login.AddPermission(loginId, "overrideOffers", "{creatorRights}");

            var offerId = await new Offer(client).Create(caseId, proposalId, new addOfferInput());


            loginWhere filter = new() { ids = new List<string> { loginId } };
            login? login = await GetLoginWithPermissions(filter);

            login.targettedPermissions.First(p => p.permission.id == "overrideOffers").targetIds.Should().Contain(offerId);
            login.targettedPermissions.First(p => p.permission.id == "overrideOffers").targetIds.Should().Contain(caseId);
        }

        async Task<login?> GetLoginWithPermissions(loginWhere where)
        {
            string query = new QueryBuilder()
                .logins(new QueryBuilder.loginsArgs(where: where), new loginsBuilder()
                    .list(new loginBuilder()
                        .id()
                        .targettedPermissions(new targettedPermissionBuilder()
                            .permission(new permissionBuilder()
                                .id())
                            .targetIds())
                        )
                    )
                .Build();

            logins logins = await _client.SendQueryAsync<logins>(query);
            return logins.list!.First();
        }

    }
}
