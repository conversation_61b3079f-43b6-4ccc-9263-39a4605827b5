using System.Linq;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using FluentAssertions;
using GraphQL.Client.Http;
using GraphQL.Client.Serializer.Newtonsoft;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration.Permissions;

public class ProductTypePermissionsTests : TestsBase
{
    public ProductTypePermissionsTests(ITestOutputHelper output) : base(output)
    {
    }

    [Fact]
    public async Task GIVEN_products_with_different_types_WHEN_user_has_permission_to_access_specific_product_type_THEN_only_right_products_returned_by_query()
    {
        string type1 = CreateNewGuid();
        string type2 = CreateNewGuid();

        productId product1 = await CreateProduct(type1);
        productId product2 = await CreateProduct(type2);

        string email = $"{CreateNewGuid()}@gmail.com";
        string password = CreateNewGuid();

        GraphQLHttpClient client = await SetUpPermittedClient(email, password, type1);

        string query = new QueryBuilder().products_2(
            new QueryBuilder.products_2Args(),
            new productsBuilder()
                .totalCount()
                .list(new productBuilder().productId(new productIdBuilder()
                    .plan()
                    .type()
                    .version()))).Build();

        products products = await client.SendQueryAsync<products>(query);

        products.totalCount.Should().Be(1);
        products!.list!.ElementAt(0)!.productId!.type.Should().Be(type1);
    }

    [Fact]
    public async Task GIVEN_offers_created_from_different_products_WHEN_user_has_permission_to_access_specific_product_type_THEN_only_right_offers_returned_by_query()
    {
        string type1 = CreateNewGuid();
        string type2 = CreateNewGuid();

        productId product1Id = await CreateProduct(type1);
        productId product2Id = await CreateProduct(type2);

        string email = $"{CreateNewGuid()}@gmail.com";
        string password = CreateNewGuid();

        GraphQLHttpClient client = await SetUpPermittedClient(email, password, type1);

        createCaseInput createCaseInput = new()
        {
            name = "test case 1",
            fields = "{}"
        };

        string caseId = await Case.Create(createCaseInput);
        string proposalId = await Proposal.Create(caseId);
        string offer1Id = await Offer.Create(caseId, proposalId, new addOfferInput { productId = new productIdInput { plan = product1Id.plan, type = product1Id.type, version = product1Id.version } });
        string offer2Id = await Offer.Create(caseId, proposalId, new addOfferInput { productId = new productIdInput { plan = product2Id.plan, type = product2Id.type, version = product2Id.version } });

        string query = new QueryBuilder().cases(
            new QueryBuilder.casesArgs(where: new caseWhere { id = caseId }),
            new casesBuilder()
                .totalCount()
                .list(new caseBuilder().proposals(
                    new caseBuilder.proposalsArgs(),
                    new proposalBuilder().basket(
                        new proposalBuilder.basketArgs(),
                        new offerBuilder().product(new productBuilder()
                            .productId(new productIdBuilder()
                                .type()))))))
            .Build();

        cases cases = await client.SendQueryAsync<cases>(query);

        cases.totalCount.Should().Be(1);
        offer?[] basket = cases.list!.ElementAt(0)!.proposals!.ElementAt(0)!.basket!.ToArray();

        basket.Length.Should().Be(1);

        basket[0]!.product!.productId!.type.Should().Be(type1);
    }

    [Fact]
    public async Task GIVEN_policies_created_from_different_products_WHEN_user_has_permission_to_access_specific_product_type_THEN_only_right_policies_returned_by_query()
    {
        string type1 = CreateNewGuid();
        string type2 = CreateNewGuid();

        productId product1Id = await CreateProduct(type1);
        productId product2Id = await CreateProduct(type2);

        string email = $"{CreateNewGuid()}@gmail.com";
        string password = CreateNewGuid();

        GraphQLHttpClient client = await SetUpPermittedClient(email, password, type1);

        createCaseInput createCase1Input = new()
        {
            name = "test case 1",
            fields = "{}"
        };

        createCaseInput createCase2Input = new()
        {
            name = "test case 2",
            fields = "{}"
        };

        string case1Id = await Case.Create(createCase1Input);
        string proposal1Id = await Proposal.Create(case1Id);
        string offer1Id = await Offer.Create(case1Id, proposal1Id, new addOfferInput { productId = new productIdInput { plan = product1Id.plan, type = product1Id.type, version = product1Id.version } });

        string? policy1Id = await GeneratePolicyFromProposalAsync(case1Id, proposal1Id, true);

        string case2Id = await Case.Create(createCase2Input);
        string proposal2Id = await Proposal.Create(case2Id);
        string offer2Id = await Offer.Create(case2Id, proposal2Id, new addOfferInput { productId = new productIdInput { plan = product2Id.plan, type = product2Id.type, version = product2Id.version } });

        string? policy2Id = await GeneratePolicyFromProposalAsync(case2Id, proposal2Id, true);

        string query = new QueryBuilder().policies(
            new QueryBuilder.policiesArgs(),
            new policiesBuilder()
                .totalCount()
                .list(new policyBuilder()
                    .id()
                    .product(new productBuilder().productId(new productIdBuilder()
                        .type()
                        .plan()
                        .version())))).Build();

        policies policies = await client.SendQueryAsync<policies>(query);

        policies.totalCount.Should().Be(1);
        policies.list!.ElementAt(0)!.id.Should().Be(policy1Id);
        policies.list!.ElementAt(0)!.product!.productId!.type.Should().Be(type1);
    }

    private async Task<string?> GeneratePolicyFromProposalAsync(string caseId, string proposalId, bool copyCaseFieldsToExtraFields = false)
    {
        string mutation = new MutationBuilder()
            .generatePoliciesFromProposal(
                new MutationBuilder.generatePoliciesFromProposalArgs(caseId, proposalId, copyCaseFieldsToExtraFields),
                new createdStatusResultBuilder().WithAllFields()
            ).Build();
        createdStatusResult result = await _client.SendMutationAsync<createdStatusResult>(mutation);
        return result.createdStatus!.ids!.Single();
    }

    private async Task<GraphQLHttpClient> SetUpPermittedClient(string email, string password, string productType)
    {
        GraphQLClientConfig config = GraphQLClientConfig.Local.Load();

        string loginId = await Login.Create(new createLoginInput
        {
            email = email,
            username = email,
            password = password,
            isEmailConfirmed = true,
            clientId = config.ClientId
        });

        string permissionGroupId = await PermissionGroup.Create(new createPermissionGroupInput
        {
            name = CreateNewGuid(),
            description = "Product type permissions tests",
            productTypes = new[] { productType }
        });

        await Task.WhenAll(
            PermissionGroup.AddPermission(permissionGroupId, "readProducts", "all"),
            PermissionGroup.AddPermission(permissionGroupId, "readPolicies", "all"),
            PermissionGroup.AddPermission(permissionGroupId, "readCases", "all"));

        await Login.AddToGroup(loginId, permissionGroupId);

        QueryBuilder.token_2Args tokenArgs = new(config.TenantId, config.ClientId, email, password);
        GraphQLHttpClient client = new(config.GatewayGraphQLUrl, new NewtonsoftJsonSerializer());
        client.HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", await new Token(client).Fetch(tokenArgs));

        return client;
    }

    private async Task<productId> CreateProduct(string type)
    {
        string mutation = new MutationBuilder().createProduct(
            new MutationBuilder.createProductArgs(product: new createProductInput
            {
                productId = new productIdInput
                {
                    type = type,
                    plan = CreateNewGuid(),
                    version = CreateNewGuid()
                }
            }),
            new productBuilder().productId(new productIdBuilder()
                .type()
                .plan()
                .version())).Build();


        product product = await _client.SendMutationAsync<product>(mutation);

        return product!.productId!;
    }
}
