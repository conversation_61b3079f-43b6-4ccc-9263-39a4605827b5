﻿using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using CoverGo.Gateway.Tests.Integration.Crud;
using GraphQL.Client.Http;
using GraphQL.Client.Serializer.Newtonsoft;
using IdentityModel.Client;

namespace CoverGo.Gateway.Tests.Integration
{

    static class Setup
    {
        public static async Task<GraphQLHttpClient> CreateGraphQLHttpClient()
        {
            GraphQLClientConfig config = GraphQLClientConfig.Local.Load();
            QueryBuilder.token_2Args tokenArgs = new(config.TenantId, config.ClientId, config.Username,
                config.Password);
            HttpClient httpClient = new()
            {
                Timeout = TimeSpan.FromMinutes(5),
            };
            GraphQLHttpClient client =
                new(new GraphQLHttpClientOptions { EndPoint = new Uri(config.GatewayGraphQLUrl) },
                    new NewtonsoftJsonSerializer(), httpClient);

            client.HttpClient.BaseAddress = new Uri(config.BaseUrl);
            client.HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", await new Token(client).Fetch(tokenArgs));
            return client;
        }

        public static async Task<string> GetAccessToken(string authUrl)
        {
            var client = new HttpClient();

            var response = await client.RequestPasswordTokenAsync(new()
            {
                Address = $"{authUrl}covergo/connect/token",
                ClientId = UserCredentials.Admin.ClientId,
                UserName = UserCredentials.Admin.UserName,
                Password = UserCredentials.Admin.Password,
                Scope = "custom_profile offline_access",
            });

            return response.AccessToken;
        }
    }

    public class UserCredentials
    {
        public string TenantId { get; private init; }
        public string ClientId { get; private init; }
        public string UserName { get; private init; }
        public string Password { get; private init; }

        public static UserCredentials Admin => new()
        {
            ClientId = "admin",
            Password = "V9K&KobcZO3",
            UserName = "<EMAIL>",
            TenantId = "covergo"
        };
    }
}