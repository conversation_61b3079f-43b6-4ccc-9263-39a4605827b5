using System;
using System.Net.Http;
using System.Net.Http.Headers;

using CoverGo.Gateway.Tests.GatewayClient;

using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Gateway.Tests.Integration.Support;

public abstract class ApiTestBase
{
    private WebApplicationFactory<Program> _applicationFactory;
    private readonly Lazy<IConfiguration> _configuration;
    private readonly Lazy<ITestGatewayClient> _gatewayClient;
    private readonly Lazy<HttpClient> _gatewayHttpClient;

    private IConfiguration Configuration => _configuration.Value;
    protected ITestGatewayClient GatewayClient => _gatewayClient.Value;

    protected ApiTestBase(GatewayWebApplicationFactory applicationFactory)
    {
        _applicationFactory = applicationFactory;
        _configuration = new(CreateConfiguration);
        _gatewayClient = new(CreateTestGatewayClient);
        _gatewayHttpClient = new(CreateRestClient);
        ConfigureApiServices(services =>
        {
            // Use Gateway WebApplicationFactory for stitching
            services.AddSingleton(new TestHttpClientFactoryDecoratorOptions()
            {
                Clients =
                {
                    ["gateway"] = () => _gatewayHttpClient.Value,
                }
            });
            services.Decorate<IHttpClientFactory, TestHttpClientFactoryDecorator>();
        });
    }

    protected ApiTestBase() : this(new GatewayWebApplicationFactory())
    {
    }

    private ITestGatewayClient CreateTestGatewayClient()
    {
        var serviceCollection = new ServiceCollection();
        var httpClientFactory = new TestHttpClientFactory
        {
            Clients =
            {
                ["TestGatewayClient"] = () => _gatewayHttpClient.Value,
            }
        };

        serviceCollection.AddSingleton<IHttpClientFactory>(httpClientFactory);
        serviceCollection.AddTestGatewayClient();
        var serviceProvider = serviceCollection.BuildServiceProvider();
        return serviceProvider.GetRequiredService<ITestGatewayClient>();
    }

    private HttpClient CreateRestClient()
    {
        HttpClient client = _applicationFactory.CreateClient(new WebApplicationFactoryClientOptions { BaseAddress = GetGatewayUri(Configuration) });
        var authToken = Setup.GetAccessToken(Configuration.GetConnectionString("auth")!).GetAwaiter().GetResult();
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
        return client;
    }

    private IConfiguration CreateConfiguration()
    {
        IConfiguration configuration = TestConfigFactory.Create();
        return configuration;
    }

    private static Uri GetGatewayUri(IConfiguration configuration) => GetServiceUri(configuration, "gateway");

    private static Uri GetServiceUri(IConfiguration configuration, string serviceName) => new UriBuilder(configuration.GetConnectionString(serviceName)) { Path = "graphql" }.Uri;

    protected void ConfigureApiServices(Action<IServiceCollection> servicesConfiguration) =>
        _applicationFactory = _applicationFactory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureTestServices(servicesConfiguration);
        });
}
