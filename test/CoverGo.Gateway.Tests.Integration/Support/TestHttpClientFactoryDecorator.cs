using System;
using System.Collections.Generic;
using System.Net.Http;

namespace CoverGo.Gateway.Tests.Integration.Support;

public class TestHttpClientFactoryDecoratorOptions
{
    public Dictionary<string, Func<HttpClient>> Clients { get; } = new();
}

public class TestHttpClientFactoryDecorator(
    IHttpClientFactory baseClient,
    TestHttpClientFactoryDecoratorOptions options) : IHttpClientFactory
{
    public HttpClient CreateClient(string name)
    {
        if (options.Clients.TryGetValue(name, out var client))
        {
            return client();
        }
        return baseClient.CreateClient(name);
    }
}
