using System;

using Microsoft.Extensions.Configuration;

namespace CoverGo.Gateway.Tests.Integration.Support;

public class TestConfigFactory
{
    private const string SettingsFileName = "testsettings";

    public static IConfiguration Create()
    {
        var environmentName = TestEnvironment.GetEnvironment();
        Console.WriteLine($"Loading settings for {environmentName} environment");

        var config = new ConfigurationBuilder()
            .AddJsonFile($"{SettingsFileName}.json")
            .AddJsonFile($"{SettingsFileName}.{environmentName}.json", true)
            .AddEnvironmentVariables()
            .AddUserSecrets<TestConfigFactory>(optional: true);

        return config.Build();
    }
}
