﻿using CoverGo.Gateway.Client;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;
using Task = System.Threading.Tasks.Task;

namespace CoverGo.Gateway.Tests.Integration
{
    public class ServiceItemAgreedFeeCrudTests : TestsBase
    {
        public ServiceItemAgreedFeeCrudTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task GIVEN_serviceItemAgreedFee_WHEN_create_THEN_returns_Id()
        {
            (string id, _) = await ServiceItemAgreedFee.Create();

            id.Should().NotBeNull();
        }

        [Fact]
        public async Task GIVEN_serviceItemAgreedFee_added_WHEN_request_THEN_returns_serviceItemAgreedFee()
        {
            (string serviceItemId, _) = await ServiceItem.Create("test");

            (string id, createServiceItemAgreedFeeInput input) = await ServiceItemAgreedFee.Create(serviceItemId);

            serviceItemAgreedFee? serviceItemAgreedFee = await ServiceItemAgreedFee.Search(new serviceItemAgreedFeeWhereInput { id = id });

            serviceItemAgreedFee.ShouldHaveSameValuesAsInput(input);
            serviceItemAgreedFee!.serviceItem!.name.Should().Be("test");
        }

        [Fact]
        public async Task GIVEN_serviceItemAgreedFee_added_WHEN_requested_with_Currency_THEN_returns_serviceItemAgreedFee()
        {
            (string serviceItemId, _) = await ServiceItem.Create();
            string currency = Guid.NewGuid().ToString();
            await ServiceItemAgreedFee.Create(serviceItemId, currency);

            serviceItemAgreedFee? serviceItemAgreedFee = await ServiceItemAgreedFee.Search(new serviceItemAgreedFeeWhereInput { currency = currency });

            serviceItemAgreedFee!.currency.Should().Be(currency);
        }

        [Fact]
        public async Task GIVEN_serviceItemAgreedFee_added_WHEN_requested_In_serviceItemAgreedFeeCurrencies_THEN_returns_serviceItemAgreedFee()
        {
            (string serviceItemId, _) = await ServiceItem.Create();
            string currency1 = Guid.NewGuid().ToString();
            string currency2 = Guid.NewGuid().ToString();
            List<string> currencies = new() { currency1, currency2 };
            await ServiceItemAgreedFee.Create(serviceItemId, currency1);

            serviceItemAgreedFee? serviceItemAgreedFee = await ServiceItemAgreedFee.Search(new serviceItemAgreedFeeWhereInput { currency_in = currencies });

            serviceItemAgreedFee!.currency.Should().Be(currency1);
        }

        [Fact]
        public async Task GIVEN_serviceItemAgreedFee_added_WHEN_update_THEN_succeed_and_updated()
        {
            (string serviceItemId, _) = await ServiceItem.Create();
            (string id, _) = await ServiceItemAgreedFee.Create(serviceItemId);

            updateServiceItemAgreedFeeInput updateInput = new()
            {
                id = id,
                currency = Guid.NewGuid().ToString(),
                rate = new Random().Next(),
                serviceItemId = Guid.NewGuid().ToString(),
                isCurrencyChanged = true,
                isRateChanged = true,
                isServiceItemIdChanged = true
            };
            await ServiceItemAgreedFee.Update(updateInput);
            serviceItemAgreedFee? serviceItemAgreedFee = await ServiceItemAgreedFee.Search(new serviceItemAgreedFeeWhereInput { id = id });

            serviceItemAgreedFee.ShouldHaveSameValuesAsInput(updateInput);
        }

        [Fact]
        public async Task GIVEN_serviceItemAgreedFee_added_WHEN_delete_this_serviceItemAgreedFee_THEN_succeed_and_deletes()
        {
            (string serviceItemId, _) = await ServiceItem.Create("test");
            (string id, _) = await ServiceItemAgreedFee.Create(serviceItemId);

            deleteServiceItemAgreedFeeInput deleteserviceItemAgreedFeeInput = new() { serviceItemAgreedFeeId = id };
            await ServiceItemAgreedFee.Delete(deleteserviceItemAgreedFeeInput);

            serviceItemAgreedFee? noserviceItemAgreedFee = await ServiceItemAgreedFee.Search(new serviceItemAgreedFeeWhereInput { id = id });
            noserviceItemAgreedFee.Should().BeNull();
        }
    }
}
