
FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build-service
COPY --from=mcr.microsoft.com/dotnet/sdk:6.0-alpine /usr/share/dotnet /usr/share/dotnet
ARG BUILDCONFIG=Release
ARG APP_VERSION=1.0.0
ARG GH_ACCOUNT
ARG GH_TOKEN

RUN dotnet tool install --global dotnet-trace
RUN dotnet tool install --global dotnet-dump
RUN dotnet tool install --global dotnet-counters

WORKDIR /app

#copy csproj and restore as distinct layers
# optimisation from https://docs.microsoft.com/en-us/aspnet/core/host-and-deploy/docker/building-net-docker-images?view=aspnetcore-6.0
COPY ./*.sln .
COPY ./src/Common/*.csproj ./src/Common/
COPY ./src/CoverGo.Gateway.Application/*.csproj ./src/CoverGo.Gateway.Application/
COPY ./src/CoverGo.Gateway.Domain/*.csproj ./src/CoverGo.Gateway.Domain/
COPY ./src/CoverGo.Gateway.Infrastructure/*.csproj ./src/CoverGo.Gateway.Infrastructure/
COPY ./src/CoverGo.Gateway.Interfaces/*.csproj ./src/CoverGo.Gateway.Interfaces/

COPY ./nuget.config .
COPY ./Directory.Build.props .
COPY ./Directory.Packages.props .
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text

RUN dotnet restore ./src/Common/Common.csproj
RUN dotnet restore ./src/CoverGo.Gateway.Application/CoverGo.Gateway.Application.csproj
RUN dotnet restore ./src/CoverGo.Gateway.Domain/Domain.csproj
RUN dotnet restore ./src/CoverGo.Gateway.Infrastructure/Infrastructure.csproj
RUN dotnet restore ./src/CoverGo.Gateway.Interfaces/Interfaces.csproj

COPY ./src/Common ./src/Common
COPY ./src/CoverGo.Gateway.Application ./src/CoverGo.Gateway.Application
COPY ./src/CoverGo.Gateway.Domain ./src/CoverGo.Gateway.Domain
COPY ./src/CoverGo.Gateway.Infrastructure ./src/CoverGo.Gateway.Infrastructure
COPY ./src/CoverGo.Gateway.Interfaces ./src/CoverGo.Gateway.Interfaces

RUN dotnet publish ./src/CoverGo.Gateway.Application/CoverGo.Gateway.Application.csproj -c $BUILDCONFIG -o ./out /p:Version="$APP_VERSION" --no-restore

FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS service-runtime
COPY --from=mcr.microsoft.com/dotnet/aspnet:6.0-alpine /usr/share/dotnet /usr/share/dotnet

ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
RUN apk add --no-cache icu-libs

WORKDIR /app

COPY --from=build-service /app/out ./
COPY --from=build-service /root/.dotnet/tools/ /opt/bin

RUN adduser -D buildadmin
RUN chown buildadmin:buildadmin /app /app/*
USER buildadmin

ENV ASPNETCORE_URLS http://*:8080
ARG COMMIT_SHA
ENV SENTRY_RELEASE=${COMMIT_SHA} REVISION=${COMMIT_SHA}
EXPOSE 8080
ENTRYPOINT ["dotnet", "CoverGo.Gateway.Application.dll"]

FROM build-service as build-tests-unit

COPY ./test/CoverGo.Gateway.Tests.Unit/*.csproj ./test/CoverGo.Gateway.Tests.Unit/
COPY ./test/CoverGo.Gateway.Tests.Unit ./test/CoverGo.Gateway.Tests.Unit/

COPY ./nuget.config .
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text

RUN dotnet restore ./test/CoverGo.Gateway.Tests.Unit/Tests.Unit.csproj
RUN dotnet publish ./test/CoverGo.Gateway.Tests.Unit/Tests.Unit.csproj -c $BUILDCONFIG -o ./out /p:Version=$APP_VERSION --no-restore
COPY coverlet.runsettings ./

FROM build-tests-unit AS run-tests-unit
WORKDIR /app
ENTRYPOINT ["dotnet", "test","./test/CoverGo.Gateway.Tests.Unit/Tests.Unit.csproj"]
CMD ["--nologo","--no-restore","--logger","junit;LogFileName=TestResults.xml","--settings", "coverlet.runsettings"]

FROM build-service as build-client
ARG BUILDCONFIG="Release"
ARG FILE_VERSION="1.0.0.0"
ARG INFORMATIONAL_VERSION="1.0"
ARG APP_VERSION="1.0.0"

COPY ./src/CoverGo.Gateway.Client/*.csproj ./src/CoverGo.Gateway.Client/
COPY ./src/CoverGo.Gateway.Client ./src/CoverGo.Gateway.Client/
RUN dotnet restore ./src/CoverGo.Gateway.Client/Client.csproj
RUN dotnet build ./src/CoverGo.Gateway.Client/Client.csproj -c $BUILDCONFIG --no-restore -v Minimal -p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"

FROM build-client as pack-client
ARG BUILDCONFIG="Release"
ARG FILE_VERSION="1.0.0.0"
ARG INFORMATIONAL_VERSION="1.0"
ARG APP_VERSION="1.0.0"
RUN dotnet pack  ./src/CoverGo.Gateway.Client/Client.csproj -c $BUILDCONFIG -o nuget --no-build -p:IncludeSymbols=true -p:SymbolPackageFormat=snupkg /p:PackageVersion="$APP_VERSION"  /p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"

FROM build-client as build-tests-integration
ARG BUILDCONFIG="Release"
ARG APP_VERSION="1.0.0"
COPY ./test/CoverGo.Gateway.Tests.Integration/*.csproj ./test/CoverGo.Gateway.Tests.Integration/
COPY ./test/CoverGo.Gateway.Tests.GatewayClient/*.csproj ./test/CoverGo.Gateway.Tests.GatewayClient/

RUN dotnet restore ./test/CoverGo.Gateway.Tests.Integration/Tests.Integration.csproj
RUN dotnet restore ./test/CoverGo.Gateway.Tests.GatewayClient/CoverGo.Gateway.Tests.GatewayClient.csproj

COPY ./test/CoverGo.Gateway.Tests.Integration ./test/CoverGo.Gateway.Tests.Integration/
COPY ./test/CoverGo.Gateway.Tests.GatewayClient ./test/CoverGo.Gateway.Tests.GatewayClient/
RUN dotnet publish ./test/CoverGo.Gateway.Tests.Integration/Tests.Integration.csproj -c $BUILDCONFIG -o ./out /p:Version=$APP_VERSION --no-restore

FROM mcr.microsoft.com/dotnet/sdk:8.0-bookworm-slim AS run-tests-integration
COPY --from=mcr.microsoft.com/dotnet/sdk:6.0-bullseye-slim /usr/share/dotnet /usr/share/dotnet
WORKDIR /app
COPY --from=build-tests-integration /app/out ./
ENV GATEWAY_INTEGRATION_TEST-GatewayUrl="http://covergo-gateway:60060"
ENTRYPOINT ["dotnet", "test", "CoverGo.Gateway.Tests.Integration.dll"]
# This is overriden in docker-compose.yml
CMD ["-v","d", "--no-build","--no-restore","--logger","trx;LogFilePrefix=TestResults;verbosity=detailed","--logger","junit;LogFileName=TestResults.xml;verbosity=detailed","--logger","console;verbosity=detailed"]
