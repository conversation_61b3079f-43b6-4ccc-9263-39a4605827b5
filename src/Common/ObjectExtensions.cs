using System;
using System.Collections;
using System.Linq;
using System.Reflection;
using Newtonsoft.Json;

namespace CoverGo.Gateway.Common;

public static class ObjectExtensions
{
    public static T DeepClone<T>(this T self)
    {
        string serialized = JsonConvert.SerializeObject(self);
        return JsonConvert.DeserializeObject<T>(serialized);
    }

    public static bool AreAllPropertiesDefault<T>(this T self) where T : class
    {
        if (self == null)
            return true;

        PropertyInfo[] properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (PropertyInfo property in properties)
        {
            if (!property.CanRead)
                continue;

            object propertyValue = property.GetValue(self);
            Type propertyType = property.PropertyType;

            if (propertyType == typeof(string))
            {
                if (!string.IsNullOrEmpty((string)propertyValue))
                    return false;
            }
            else if (typeof(IEnumerable).IsAssignableFrom(propertyType) && propertyType != typeof(string))
            {
                if (propertyValue != null && ((IEnumerable)propertyValue).Cast<object>().Any())
                    return false;
            }
            else
            {
                object defaultValue = propertyType.IsValueType ? Activator.CreateInstance(propertyType) : null;
                if (!Equals(propertyValue, defaultValue))
                    return false;
            }
        }

        return true;
    }
}