using System;
using System.Collections.Generic;
using System.Net;
using Microsoft.Extensions.Options;

namespace CoverGo.Gateway.Common;

public class PermittedIpList
{
    public readonly PermittedIpListOptions Options;

    public PermittedIpList(IOptions<PermittedIpListOptions> options)
    {
        Options = options?.Value ?? new();
    }

    private static bool IsInvalidArgs(string appId, string tenantId, IPAddress ipAddress) =>
        string.IsNullOrWhiteSpace(appId) || string.IsNullOrWhiteSpace(tenantId) || ipAddress is null;

    private static IPAddress MapToIPv4(IPAddress ipAddress) =>
        (ipAddress?.IsIPv4MappedToIPv6 ?? false) ? ipAddress.MapToIPv4() : ipAddress;

    private static IPAddress GetParsedIpAddress(string ipAddress) =>
        IPAddress.TryParse(ipAddress, out IPAddress result) ? result : null;

    private bool IsIncluded(string appId, string tenantId, string ipAddress)
    {
        IPAddress parsedIpAddress = GetParsedIpAddress(ipAddress);

        if (IsInvalidArgs(appId, tenantId, parsedIpAddress)) return true;
        parsedIpAddress = MapToIPv4(parsedIpAddress);

        foreach (PermittedIpConfig config in Options.PermittedIpConfigs)
        {
            if (config.Tenants.Contains(tenantId) && config.AppIds.Contains(appId))
                return config.GetPermittedIpSet().Contains(parsedIpAddress);
        }
        return true;
    }

    public bool IsIncluded(string appId, string tenantId, List<string> ipAddresses)
    {
        bool isIncluded = false;
        foreach (string ipAddress in ipAddresses)
        {
            isIncluded = IsIncluded(appId, tenantId, ipAddress);
            if(isIncluded)
                break;
        }
        return isIncluded;
    }

    public static bool IsIncluded(string permittedIpAddresses, string ipAddress)
    {
        IPAddress parsedIpAddress = MapToIPv4(GetParsedIpAddress(ipAddress));
        IpSet permittedIpSet = IpSet.ParseOrDefault(permittedIpAddresses);
        return permittedIpSet?.Contains(parsedIpAddress) ?? true;
    }
}

public class PermittedIpConfig
{
    public HashSet<string> Tenants { get; set; }
    public HashSet<string> AppIds { get; set; }
    public string PermittedIpAddresses { get; set; }
    private IpSet _ipSet;

    public IpSet GetPermittedIpSet()
    {
        if (_ipSet is null) _ipSet = IpSet.ParseOrDefault(PermittedIpAddresses);

        return _ipSet;
    }
}

public class PermittedIpListOptions
{
    public const string PermittedIpListKey = "PermittedIpList";
    public PermittedIpConfig[] PermittedIpConfigs { get; set; } = Array.Empty<PermittedIpConfig>();
}