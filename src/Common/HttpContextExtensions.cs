﻿using System;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Gateway.Common
{
    public static class HttpContextExtensions
    {
        public static string GetClaim(this HttpContext httpContext, string claimType) =>
            httpContext?.User.Claims.FirstOrDefault(c => c.Type == claimType)?.Value;

        public static string GetTenantId(this HttpContext httpContext) =>
            GetClaim(httpContext, "tenantId");

        /// <summary>
        /// Time when the request started, UTC
        /// </summary>
        public static DateTime GetTimestamp(this HttpContext httpContext) =>
            (DateTime)httpContext.Items["RequestStartedOn"];

        // Remove this method after migration to .net 6.0 and use native property instead
        public static string UserAgent(this HttpContext httpContext) =>
            httpContext.Request.Headers["User-Agent"].ToString();

        public static string GetClientIpAddress(this HttpContext httpContext)
        {
            string ipAddress = httpContext?.Request?.Headers["X-forwarded-for"].FirstOrDefault();

            if (string.IsNullOrWhiteSpace(ipAddress))
            {
                ipAddress = httpContext?.Connection?.RemoteIpAddress?.ToString();
            }
            else
            {
                string[] ipAddresses = ipAddress.Split(',');
                return ipAddresses.Length > 0 ? ipAddresses[0].Trim() : string.Empty;
            }
            return ipAddress;
        }

        public static List<string> GetClientIpAddresses(this HttpContext httpContext)
        {
            List<string> ipAddresses = [];
            string? remoteIpAddress = httpContext.Connection.RemoteIpAddress?.ToString();
            if (!string.IsNullOrWhiteSpace(remoteIpAddress))
                ipAddresses.Add(remoteIpAddress);

            string? header = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(header))
            {
                string[] forwardedIpAddresses = header.Split(',');
                string forwardedIpAddress = forwardedIpAddresses.Length > 0 ? forwardedIpAddresses[0].Trim() : string.Empty;
                if (!string.IsNullOrWhiteSpace(forwardedIpAddress))
                    ipAddresses.Add(forwardedIpAddress);
            }

            return ipAddresses;
        }
    }
}