using System;
using System.Collections.Generic;

namespace CoverGo.Gateway.Common;

public static class UrlValidator
{
    public static bool IsReturnUrlValid(string returnUrl) =>
        Uri.TryCreate(returnUrl, UriKind.Absolute, out Uri uriResult) &&
        (uriResult.Scheme == Uri.UriSchemeHttp || uriResult.Scheme == Uri.UriSchemeHttps);

    /// <summary>
    /// This list should probably come from auth service and be set by TenantId
    /// List of allowed origins Cross-Origin Resource Sharing (CORS)
    /// </summary>
    public static readonly IReadOnlyList<string> AllowedOrigins = new List<string>
    {
        // localhosts
        "http://localhost",
        "https://localhost",
        "http://localhost:5060",
        "http://localhost:8080",
        "http://localhost:8081",
        "http://localhost:8082",
        "http://localhost:8083",
        "http://localhost:8084",
        "http://localhost:8085",
        "http://localhost:3000",
        "http://localhost:3300",
        "http://localhost:5000",
        "http://localhost:60060",
        "http://nuxt:3000", // used for cypress
        "https://localhost:5001",

        // capacitor: mobile wrapper for Member Portal
        "capacitor://localhost",

        // tahoe
        "https://shop.tahoelife.com.hk",
        "https://api.tahoelife.com.hk",

        // Apex Compare
        "https://apexcompare.com",
        "https://www.apexcompare.com",

        // Covergo
        "https://api.covergo.com",

        "https://*.eu.covergo.com",
        "https://*.ca.covergo.com",
        "https://api.me.covergo.com",
        "https://api.dev.covergo.cloud",
        "https://api.preprod.asia.covergo.cloud",
        "https://cover-app.dev.covergo.cloud",
        "https://cover-app-next.dev.covergo.cloud",
        "https://*.dev.covergo.cloud", // allow any SubDomain for dev for automatic tenant creation
        "https://*.preprod.covergo.cloud", // allow any SubDomain for preprod for automatic tenant creation
        "https://*.asia.covergo.cloud", // allow any SubDomain for asia aws saas
        "https://*.asia.covergo.com",  // allow any SubDomain for ASIA PROD CLUSTER for automatic tenant creation
        "https://*.eu.covergo.cloud", // allow any SubDomain for eu aws saas
        "https://*.ca.covergo.cloud", // allow any SubDomain for ca aws saas
        "https://*.me.covergo.cloud", // allow any SubDomain for me aws saas
        "https://*.me.covergo.com", // allow any SubDomain for me aws saas
        "https://crm.covergo.com",
        "https://stripe-connect.covergo.com",
        "https://payment-gateway.covergo.com",
        "https://payments.covergo.com",
        "https://client.covergo.com",

        // CoverQuote
        "https://lapetus.covergo.com",
        "https://*.quote.hk",

        //Asia
        "https://imedical.asiainsurance.hk",
        "https://test.asiainsurance.hk",

        // CoverStudio
        "https://studio.covergo.com",

        // Jetco redirections
        "https://ujpg.jtetbwkl.com.hk",
        "https://www.jetcopg.com",

        // Myvhis
        "https://www.myvhis.com.hk",
        "https://myvhis.hk",
        "https://www.myvhis.hk",

        // Polipocket (Prive)
        "https://ins-dev.hive-up.com",
        "https://api.polipocketapp.com",
        "https://aag-uat.polipocketapp.com",
        "https://aag-test.polipocketapp.com",
        "https://aag.polipocketapp.com",
        "https://protect.privetechnologies.com",

        // UOB (Prive)
        "https://uobuat.privemanagers.com",
        "https://uat-staging.privemanagers.com",
        "https://hkoffice.privemanagers.com:8080",
        "https://www.privetechnologies.com",
        "https://www.privemanagers.com",

        // for chubbId_uat
        "https://covergo-coverquote-uat.chubbdigital.com",
        "https://covergo-gateway-uat.chubbdigital.com",
        "https://chubbid-uat.quote.hk",

        // for chubbId
        "https://covergo.chubb.com",
        "https://covergo-gateway.chubbdigital.com",

        // for companycover
        "https://companycover.hk",
        "https://*.companycover.hk",

        //for emd
        "https://www.e-md.io",
        "https://e-md.io",
        "https://gp.e-md.io",
        "https://sp.e-md.io",
        "https://admin.e-md.io",
        "https://purpose.e-md.io",

        //for raghav
        "https://karak-uae.firebaseapp.com",
        "https://snapinsure.ae",
        "https://snap-insure.com",
        "https://snapinsure.co",
        "https://snapinsure.me",

        // For TCB
        "https://app.tcb.uat.ap-southeast-1.aws.covergo.cloud", // uat
        "https://app.tcb.uat15.ap-southeast-1.aws.covergo.cloud", // uat 1.5 env
        "https://itcblife.techcombank.com.vn", // prod
        "https://itcblife-v2.techcombank.com.vn", // prod v2
	    "https://itcblife-uat.techcombank.com.vn", // on-prem UAT
        "https://app.tcb.sandbox.ap-southeast-1.aws.covergo.cloud", //sandbox

        // For DBS
        "https://admin-env1.rmhk-ebfna.uat.dbs.com",
        "https://admin-env1.rmhk-ebfna.uat.dbs.com:443",
        "https://adminweb-uat1.rmhk.ibg.dev.cloudnow.dbs.com",
        "https://adminweb-uat1.rmhk.ibg.dev.cloudnow.dbs.com:80",

        "https://admin-env2.rmhk-ebfna.uat.dbs.com",
        "https://admin-env2.rmhk-ebfna.uat.dbs.com:443",
        "https://adminweb-uat2.rmhk.ibg.dev.cloudnow.dbs.com",
        "https://adminweb-uat2.rmhk.ibg.dev.cloudnow.dbs.com:80",

        "https://admin.rmhk-ebfna.sgp.dbs.com",
        "https://admin.rmhk-ebfna.sgp.dbs.com:443",
        "https://adminweb-1a.rmhk.cbg.prd.cloudnow.dbs.com",
        "https://adminweb-1a.rmhk.cbg.prd.cloudnow.dbs.com:80",
        "https://adminweb-2a.rmhk.cbg.prd.cloudnow.dbs.com",
        "https://adminweb-2a.rmhk.cbg.prd.cloudnow.dbs.com:80",
        "https://admin-rmhk-ebfna.sgp.dbs.com",

        // For Fubon
        "http://fubon-demo.web.app",
        "https://fubon-demo.web.app",
        "http://uatechannel.fubonlife.com.hk",
        "https://uatechannel.fubonlife.com.hk",
        "http://uatechannel.fubon.com.hk",
        "https://uatechannel.fubon.com.hk",
        "http://fubon-cancer.web.app",
        "https://fubon-cancer.web.app",
        "http://devechannel-fubonlife.web.app",
        "https://devechannel-fubonlife.web.app",
        "http://10.43.51.10:32102",
        "http://10.43.51.10:32101",
        "http://10.43.29.13:32101",
        "http://10.43.29.13:32102",

        // For BOCL
        "http://liveyoung.boclife.com.hk",
        "https://liveyoung.boclife.com.hk",

        // For BoC Alicloud (UAT)
        "https://wwwuat.bocgins.com",
        "http://192.168.22.21:32100",
        "http://192.168.22.21:32105",
        "http://192.168.22.21:32106",
        "http://192.168.22.21:32107",
        "http://192.168.22.21:32108",

        // For BoC Alicloud (PROD)
        "https://www.bocgins.com",
        "http://192.168.202.22:32100",
        "http://192.168.202.22:32105",
        "http://192.168.202.22:32106",
        "http://192.168.202.22:32107",
        "http://192.168.202.22:32108",

        "http://175.45.33.236:32107",
        "https://wwwuat.bocgins.com/motor",
        "https://www.bocgins.com/motor",
        "https://wwwuat.bocgins.com/motor/",
        "https://www.bocgins.com/motor/",

        // For XN
        "https://admin.covergo-dev.xn.com",
        "https://studio.covergo-dev.xn.com",
        "https://admin.covergo-uat.xn.com",
        "https://studio.covergo-uat.xn.com",
        "https://admin.covergo.xn.com",
        "https://studio.covergo.xn.com",
        "https://*.xn-global.com",

        // For FiLife
        "https://admin.covergo-dev.fi.life",
        "https://studio.covergo-dev.fi.life",
        "https://api.covergo-dev.fi.life",
        "https://api.covergo-prod.fi.life",
        "https://*.pages.dev",
        "https://admin.covergo-prod.fi.life",
        "https://studio.covergo-prod.fi.life",


        //For msig
        "https://studio.msig.dev.ap-east-1.covergo.cloud",
        "https://admin.msig.dev.ap-east-1.covergo.cloud",
        "https://hr.msig.dev.ap-east-1.covergo.cloud",
        "https://member.msig.dev.ap-east-1.covergo.cloud",
        "http://admin.msig.dev.covergoaws.msighkcloud.net",

        "https://api.msig.prod.ap-east-1.covergo.cloud",
        "https://member.msig.prod.ap-east-1.covergo.cloud",
        "https://hr.msig.prod.ap-east-1.covergo.cloud",
        "https://medigo.msig.com.hk",
        "https://hr.medigo.msig.com.hk",
        "https://api.medigo.msig.com.hk",
        "https://admin.prod.covergoaws.msighkcloud.net",
        "https://studio.prod.covergoaws.msighkcloud.net",
        "https://api.prod.covergoaws.msighkcloud.net",
        "https://api.qa.covergoaws.msighkcloud.net",

        "https://studio.msig.qa.ap-east-1.covergo.cloud",
        "https://admin.qa.covergoaws.msighkcloud.net",
        "https://hr.medigo.qa.covergoaws.msighkcloud.net",
        "https://medigo.qa.covergoaws.msighkcloud.net",
        "https://admin.msig.qa.ap-east-1.covergo.cloud",
        "https://hr.msig.qa.ap-east-1.covergo.cloud",
        "https://member.msig.qa.ap-east-1.covergo.cloud",
        "https://api.msig.qa.ap-east-1.covergo.cloud",

        "https://18.167.44.36", // DEV AWS Lambda
        "https://18.166.159.129", // SIT AWS Lambda
        "https://18.163.248.193", //QA AWS Lambda
        "https://18.163.253.125", // PROD AWS Lambda
        "https://18.162.131.52", // PROD AWS Lambda

        //For AsiaEB/CTF
        "https://member.eb.ctflife.com.hk",
        "https://agency.eb.ctflife.com.hk",
        "https://hr.eb.ctflife.com.hk",

        //For Insured Nomads
        "https://join-uat.insurednomads.com",
        "http://join-uat.insurednomads.com",
        "https://join.insurednomads.com",
        "http://join.insurednomads.com",
        "https://portal.insurednomads.com",
        "http://portal.insurednomads.com",
        "https://nomads-health-admin.quote.hk",

        //For DLVN
        "https://daiichi-life-uat.quote.hk",
        "https://e.dai-ichi-life.com.vn",
        "https://www.e.dai-ichi-life.com.vn",

        // For AXA
        "https://axahk-admin.dev.covergo.cloud",
        "https://axahkibm-uat-admin.quote.hk",
        "https://axahkibm-uat-studio.quote.hk",
        "https://axahkibm-admin.dev.covergo.cloud",
        "https://axa.ea6wn4xpig5e86gc308ni.dedicated.covergo.cloud",
        "https://axa.vofdb5qlvi6pibm6xyp6h.dedicated.covergo.cloud",
        "https://axa.2upe0nqcu873ly64ry7rk.dedicated.covergo.cloud",
        "https://axa.gssk5scnmaa8too1fj3b3.dedicated.covergo.cloud",

        //For AXA Thailand
        "https://adminhealthclaim.axa.co.th",
        "https://healthclaim.axa.co.th",
        "https://myclaims.axa.co.th",

        // For WFP
        "https://wfp-dev-studio.quote.hk",
        "https://wfp-admin-dev.quote.hk",
        "https://wfp-member-dev.quote.hk",
        "https://wfp-broker-portal-dev.quote.hk",
        "https://www.mywealthspan.com",

        "https://cg-demo-ep.covergo.cloud",

        // For Aevitae
        "https://aevitae-studio-uat.covergo.cloud",
        "https://aevitae-employer-uat.covergo.cloud",
        "https://aevitae-admin-uat.covergo.cloud",

        // For Aevitae prod
        "https://aevitae-admin.eu.covergo.com",
        "https://aevitae-employer.eu.covergo.com",
        "https://aevitae-member.eu.covergo.com",
        "https://aevitae-provider.eu.covergo.com",

        // For Bupa Dev
        "https://admin.dev.bupa.hk.covergo.net",
        "https://studio.dev.bupa.hk.covergo.net",
        "https://member.dev.bupa.hk.covergo.net",
        "https://hr.dev.bupa.hk.covergo.net",
        "https://provider.dev.bupa.hk.covergo.net",
        "https://admin-core.dev.bupa.hk.covergo.net",
        "https://studio-core.dev.bupa.hk.covergo.net",
        "https://member-core.dev.bupa.hk.covergo.net",
        "https://hr-core.dev.bupa.hk.covergo.net",
        "https://provider-core.dev.bupa.hk.covergo.net",

        // For Bupa UAT
        "https://admin.uat.bupa.hk.covergo.net",
        "https://studio.uat.bupa.hk.covergo.net",
        "https://member.uat.bupa.hk.covergo.net",
        "https://hr.uat.bupa.hk.covergo.net",
        "https://provider.uat.bupa.hk.covergo.net",
        "https://admin-qa.uat.bupa.hk.covergo.net",
        "https://studio-qa.uat.bupa.hk.covergo.net",
        "https://member-qa.uat.bupa.hk.covergo.net",
        "https://hr-qa.uat.bupa.hk.covergo.net",
        "https://provider-qa.uat.bupa.hk.covergo.net",
        "https://admin-dm.uat.bupa.hk.covergo.net",
        "https://studio-dm.uat.bupa.hk.covergo.net",
        "https://member-dm.uat.bupa.hk.covergo.net",
        "https://hr-dm.uat.bupa.hk.covergo.net",
        "https://provider-dm.uat.bupa.hk.covergo.net",

        // For Bupa PROD
        "https://admin.prod.bupa.hk.covergo.net",
        "https://studio.prod.bupa.hk.covergo.net",
        "https://member.prod.bupa.hk.covergo.net",
        "https://hr.prod.bupa.hk.covergo.net",
        "https://provider.prod.bupa.hk.covergo.net",

        // For Bupa Broker Portal
        "https://bp-admin.dev.bupa.hk.covergo.net",
        "https://bp-admin.uat.bupa.hk.covergo.net",
        "https://bp-admin.prod.bupa.hk.covergo.net",

        "https://studio-bp.dev.bupa.hk.covergo.net",
        "https://studio-bp.uat.bupa.hk.covergo.net",
        "https://studio-bp.prod.bupa.hk.covergo.net",

        "https://bp-broker.dev.bupa.hk.covergo.net",
        "https://bp-broker.uat.bupa.hk.covergo.net",
        "https://brokerportal-uat.bupa.com.hk",
        "https://brokerportal.bupa.com.hk",

        // For CoverHealth PROD - ME
        "https://studio.me.covergo.com",
        "https://coverhealth-admin.me.covergo.com",
        "https://coverhealth-portal-member.me.covergo.com",
        "https://coverhealth-portal-hr.me.covergo.com",
        "https://coverhealth-provider.me.covergo.com",
        "https://coverhealth-broker.me.covergo.com",

        // For Walaa PROD
        "https://walaa-studio.me.covergo.com",
        "https://walaa-admin.me.covergo.com",
        "https://walaa-member.me.covergo.com",
        "https://walaa-hr.me.covergo.com",
        "https://walaa-provider.me.covergo.com",
        "https://walaa-broker.me.covergo.com",

        // For Medgulf PROD
        "https://medgulf-studio.me.covergo.com",
        "https://medgulf-admin.me.covergo.com",
        "https://medgulf-member.me.covergo.com",
        "https://medgulf-hr.me.covergo.com",
        "https://medgulf-provider.me.covergo.com",
        "https://medgulf-broker.me.covergo.com",

        // For GMS CA UAT
        "https://gentle-sky-0ce82511e-staging.westus2.5.azurestaticapps.net",

        "https://*.uat.axath.asia.covergo.net",
    };
}
