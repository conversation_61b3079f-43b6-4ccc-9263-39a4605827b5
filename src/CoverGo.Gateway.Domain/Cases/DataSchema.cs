﻿using System.Collections.Generic;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Domain.Cases
{
    public class DataSchema : SystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public JToken Schema { get; set; }
        public DataSchemaStandard Standard { get; set; }
        public string Type { get; set; }
        public string[] Tags { get; set; }
        public string[] UiSchemaIds { get; set; }
    }

    public class DataSchemaStandard
    {
        public DataSchemaStandardTypeEnum Type { get; set; }
        public string Version { get; set; }
    }

    public enum DataSchemaStandardTypeEnum
    {
        JSON_SCHEMA = 0,
        STATE_CHART = 1
    }

    public class CreateDataSchemaCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Schema { get; set; }
        public DataSchemaStandard Standard { get; set; }
        public string Type { get; set; }
        public string CreatedById { get; set; }
        public string[] Tags { get; set; }
    }

    public class UpdateDataSchemaCommand
    {
        public string DataSchemaId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Schema { get; set; }
        public DataSchemaStandard Standard { get; set; }
        public string Type { get; set; }
        public string ModifiedById { get; set; }
        public string[] Tags { get; set; }
    }

    public class DataSchemaWhere
    {
        public IReadOnlyCollection<DataSchemaWhere> Or { get; set; }
        public IReadOnlyCollection<DataSchemaWhere> And { get; set; }
        public string Id { get; set; }
        public IReadOnlyCollection<string> Id_in { get; set; }
        public string Type { get; set; }
        public IReadOnlyCollection<string> Tags_contains { get; set; }


    }

    public class AddUiSchemaToDataSchemaCommand
    {
        public string DataSchemaId { get; set; }
        public string UiSchemaId { get; set; }

        public string AddedById { get; set; }
    }

    public class RemoveUiSchemaFromDataSchemaCommand
    {
        public string DataSchemaId { get; set; }
        public string UiSchemaId { get; set; }

        public string RemovedById { get; set; }
    }

}