using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Binder;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Users;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Domain.Cases
{
    public interface ICaseService
    {
        Task<long> GetTotalCountAsync(string tenantId, CaseWhere where);
        Task<IEnumerable<Case>> GetAsync(string tenantId, QueryArguments queryArguments);
        Task<IEnumerable<CasesReport>> GetReportAsync(string tenantId, QueryArguments queryArguments);
        Task<IDictionary<string, Proposal>> GetProposalDictionaryAsync(string tenantId, CaseWhere where);
        Task<IDictionary<string, Proposal>> GetProposalDictionaryByPolicyIdAsync(string tenantId, CaseWhere where);
        Task<IEnumerable<string>> GetIdsAsync(string tenantId, QueryArguments queryArguments);
        Task<List<DetailedEventLog>> GetEventsAsync(string tenantId, EventQuery query);

        Task<Result<string>> CreateCaseAsync(string tenantId, CreateCaseCommand command);
        Task<Result> UpdateCaseAsync(string tenantId, string caseId, UpdateCaseCommand command);
        Task<Result> DeleteCaseAsync(string tenantId, string caseId, DeleteCaseCommand command);

        Task<Result<string>> AddProposalAsync(string tenantId, string caseId, AddProposalCommand command, string accessToken = null);
        Task<Result<string>> CopyProposalAsync(string tenantId, string caseId, CopyProposalCommand command);
        Task<Result<string>> RenewProposalAsync(string tenantId, string caseId, RenewProposalCommand command, string accessToken);
        Task<Result> IssueProposalAsync(string tenantId, string caseId, IssueProposalCommand command);
        Task<Result> RejectProposalAsync(string tenantId, string caseId, RejectProposalCommand command);
        Task<Result> UpdateProposalAsync(string tenantId, string caseId, UpdateProposalCommand command);
        Task<Result> RemoveProposalAsync(string tenantId, string caseId, RemoveCommand command);

        Task<Result<string>> AddOfferAsync(string tenantId, string caseId, AddOfferCommand command, string accessToken = null);
        Task<Result> UpdateOfferAsync(string tenantId, string caseId, UpdateOfferCommand command);
        Task<Result> RemoveOfferAsync(string tenantId, string caseId, RemoveOfferFromProposalCommand command);

        Task<Result<CreatedStatus>> AddFactToCaseAsync(string tenantId, string caseId, AddFactCommand command);
        Task<Result> UpdateFactOfCaseAsync(string tenantId, string caseId, UpdateFactCommand command);
        Task<Result> RemoveFactFromCaseAsync(string tenantId, string caseId, RemoveFactCommand command);
        Task<Result> CaseFactBatchAsync(string tenantId, string caseId, FactCommandBatch batch);

        Task<Result> AddNoteToCaseAsync(string tenatId, string caseId, AddNoteCommand command);
        Task<Result> UpdateNoteOfCaseAsync(string tenantId, string caseId, UpdateNoteCommand command);
        Task<Result> RemoveNoteFromCaseAsync(string tenantId, string caseId, RemoveNoteCommand command);

        Task<Result<string>> AddStakeholderToCaseAsync(string tenantId, string caseId, AddStakeholderCommand command);
        Task<Result> UpdateStakeholderOfCaseAsync(string tenantId, string caseId, UpdateStakeholderCommand command);
        Task<Result> RemoveStakeholderFromCaseAsync(string tenantId, string caseId, RemoveStakeholderCommand command);

        Task<Result<string>> AddStakeholderToProposalAsync(string tenantId, string caseId, AddStakeholderCommand command);
        Task<Result> UpdateStakeholderOfProposalAsync(string tenantId, string caseId, UpdateStakeholderCommand command);
        Task<Result> RemoveStakeholderFromProposalAsync(string tenantId, string caseId, RemoveStakeholderCommand command);

        Task<Result<CreatedStatus>> GeneratePoliciesFromProposalAsync(string tenantId, string caseId, GeneratePoliciesFromProposalCommand command, string accessToken = null);

        Task<Result<string>> AddDiscountToProposalOfferAsync(string tenantId, string caseId, AddDiscountCommand command);
        Task<Result> UpdateDiscountOfProposalOfferAsync(string tenantId, string caseId, UpdateDiscountCommand command);
        Task<Result> RemoveDiscountFromProposalOfferAsync(string tenantId, string caseId, RemoveDiscountFromOfferCommand command);

        Task<Result<string>> AddLoadingToProposalOfferAsync(string tenantId, string caseId, AddLoadingCommand command);
        Task<Result> UpdateLoadingOfProposalOfferAsync(string tenantId, string caseId, UpdateLoadingCommand command);
        Task<Result> RemoveLoadingFromProposalOfferAsync(string tenantId, string caseId, RemoveLoadingCommand command);

        Task<Result<CreatedStatus>> AddExclusionToProposalOfferAsync(string tenantId, string caseId, AddExclusionCommand command);
        Task<Result> RemoveExclusionFromProposalOfferAsync(string tenantId, string caseId, RemoveExclusionCommand command);

        Task<Result<string>> AddClauseToOfferAsync(string tenantId, string caseId, AddClauseCommand command);
        Task<Result> UpdateClauseOfOfferAsync(string tenantId, string caseId, UpdateClauseCommand command);
        Task<Result> RemoveClauseFromOfferAsync(string tenantId, string caseId, RemoveClauseCommand command);
        Task<Result> ProposalOfferClauseBatchAsync(string tenantId, string caseId, ClauseBatchCommand command);
        Task<Result> ProposalOfferJacketBatchAsync(string tenantId, string caseId, JacketInstanceBatchCommand command);
        Task<Result> RemoveJacketFromOfferAsync(string tenantId, string caseId, RemoveJacketInstanceCommand instanceCommand);

        Task<Result> ProposalOfferFactBatchAsync(string tenantId, string caseId, FactCommandBatch batch);
        Task<Result<string>> AddFactToProposalOfferAsync(string tenantId, string caseId, AddFactCommand command);
        Task<Result> UpdateFactOfProposalOfferAsync(string tenantId, string caseId, UpdateFactCommand command);
        Task<Result> RemoveFactFromProposalOfferAsync(string tenantId, string caseId, RemoveFactCommand command);

        Task<Result> UpsertBenefitOptionOfProposalOfferAsync(string tenantId, string caseId, UpsertBenefitOptionCommand command);
        Task<Result> RemoveBenefitOptionFromProposalOfferAsync(string tenantId, string caseId, RemoveBenefitOptionCommand command);

        Task<Result<string>> AddStakeholderToProposalOfferAsync(string tenantId, string caseId, AddStakeholderCommand command);
        Task<Result> UpdateStakeholderOfProposalOfferAsync(string tenantId, string caseId, UpdateStakeholderCommand command);
        Task<Result> RemoveStakeholderFromProposalOfferAsync(string tenantId, string caseId, RemoveStakeholderCommand command);

        Task<Result> AddAssociatedContractToProposalOfferAsync(string tenantId, string caseId, AddAssociatedContractCommand command);
        Task<Result> RemoveAssociatedContractFromProposalOfferAsync(string tenantId, string caseId, RemoveAssociatedContractCommand command);
        Task<Result> AddCommissionToOfferAsync(string tenantId, string caseId, AddCommissionCommand command);
        Task<Result> UpdateCommissionOfOfferAsync(string tenantId, string caseId, UpdateCommissionCommand command);
        Task<Result> RemoveCommissionFromOfferAsync(string tenantId, string caseId, RemoveCommissionCommand command);

        Task<Result<CreatedStatus>> AddBeneficiaryEligibilityAsync(string tenantId, string caseId, AddBeneficiaryEligibilityCommand command);
        Task<Result> UpdateBeneficiaryEligibilityAsync(string tenantId, string caseId, UpdateBeneficiaryEligibilityCommand command);
        Task<Result> RemoveBeneficiaryEligibilityAsync(string tenantId, string caseId, RemoveCommand command);

        Task<Result<CreatedStatus>> AddPaymentInfoAsync(string tenantId, string caseId, AddPaymentInfoCommand command);
        Task<Result> UpdatePaymentInfoAsync(string tenantId, string caseId, UpdatePaymentInfoCommand command);
        Task<Result> RemovePaymentInfoAsync(string tenantId, string caseId, RemoveCommand command);
        Task<IReadOnlyCollection<DataSchema>> GetDataSchemasAsync(string tenantId, DataSchemaWhere where);
        Task<Result<CreatedStatus>> CreateDataSchemaAsync(string tenantId, CreateDataSchemaCommand command);
        Task<Result> UpdateDataSchemaAsync(string tenantId, UpdateDataSchemaCommand command);
        Task<Result> DeleteDataSchemaAsync(string tenantId, string dataSchemaId, DeleteCommand command);

        Task<Result> AddUiSchemaToDataSchemaAsync(string tenantId, AddUiSchemaToDataSchemaCommand command);
        Task<Result> RemoveUiSchemaFromDataSchemaAsync(string tenantId, RemoveUiSchemaFromDataSchemaCommand command);
    }
    public class CasesReport
    {
        public JToken ReportFields { get; set; }
    }
    public class Case : SystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string CaseNumber { get; set; }
        public string Source { get; set; }
        public IEnumerable<Fact> Facts { get; set; }
        public IEnumerable<Note> Notes { get; set; }
        public string HolderId { get; set; }
        public IEnumerable<string> OtherHolderIds { get; set; }
        public IEnumerable<string> InsuredIds { get; set; }
        public string Status { get; set; }
        public IEnumerable<Proposal> Proposals { get; set; }
        public IEnumerable<Stakeholder> Stakeholders { get; set; }
        public List<CaseAgent> Agents { get; set; } = new List<CaseAgent>();
        public List<CaseHandler> Handlers { get; set; } = new List<CaseHandler>();
        public IEnumerable<BeneficiaryEligibility> BeneficiaryEligibilities { get; set; }
        public IEnumerable<PaymentInfo> PaymentInfos { get; set; }
        public string ComponentId { get; set; }
        public JToken Fields { get; set; }
        public string FieldsSchemaId { get; set; }
        public string WorkflowSchemaId { get; set; }
        public AccessPolicy? AccessPolicy { get; set; }
        public string ChannelId { get; set; }
    }

    public class Proposal : SystemObject
    {
        public string Id { get; set; }
        public string CaseId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string ProposalNumber { get; set; }
        public string ReferralCode { get; set; }
        public bool IsIssued { get; set; }
        public string IssuedById { get; set; }
        public DateTime? IssuedAt { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public IEnumerable<Offer> Basket { get; set; }
        public AugmentedPrice TotalPrice { get; set; }
        public IEnumerable<string> PolicyIds { get; set; }
        public RenewalHistory RenewalHistory { get; set; }
        public IEnumerable<Stakeholder> Stakeholders { get; set; }
        public bool IsRejected { get; set; }
        public IEnumerable<string> RejectionCodes { get; set; }
        public string RejectionRemarks { get; set; }
        public string RejectedById { get; set; }
        public bool IsAccepted { get; set; }
        public string AcceptedById { get; set; }
        public bool IsApprovalNeeded { get; set; }
    }

    public class RenewalHistory
    {
        public string RenewedFromId { get; set; }
        public string RenewedToId { get; set; }
        public int RenewalCount { get; set; }
    }

    public class CaseWhere : Where
    {
        public List<CaseWhere> Or { get; set; }
        public List<CaseWhere> And { get; set; }

        public string Id { get; set; }
        public string Name_contains { get; set; }
        public IEnumerable<string> Id_in { get; set; }
        public string CaseNumber { get; set; }
        public string Status { get; set; }
        public string Source { get; set; }
        public string Source_contains { get; set; }
        public ProposalWhere Proposal { get; set; }
        public ProposalWhere Proposals_contains { get; set; }
        public string HolderId { get; set; }
        public IEnumerable<string> HolderId_in { get; set; }
        public IndividualWhere HolderIndividual { get; set; }
        public CompanyWhere HolderCompany { get; set; }
        public StakeholderWhere Stakeholders_contains { get; set; }
        public FactWhere Facts_contains { get; set; }
        public ProposalWhere Proposals_every { get; set; }
        public bool? Proposals_exist { get; set; }
        public string CaseNumber_contains { get; set; }
        public FieldsWhere FieldsWhere { get; set; }
        public bool? HavingIssuedPolicies { get; set; }
        public AccessPolicy? AccessPolicy { get; set; }
        public string ChannelId { get; set; }
    }

    public class StakeholderWhere : Where
    {
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string Type { get; set; }
        public List<string> Type_in { get; set; }
        public string EntityId { get; set; }
        public List<string> EntityId_in { get; set; }
    }

    public class ProposalWhere : Where
    {
        public string Id { get; set; }
        public IEnumerable<string> Id_in { get; set; }

        public string ProposalNumber { get; set; }
        public string ProposalNumber_contains { get; set; }

        public string Name { get; set; }
        public string Name_contains { get; set; }

        public string PolicyId_contains { get; set; }
        public List<string> PolicyId_contains_every { get; set; }
        public List<string> PolicyId_contains_some { get; set; }

        public OfferWhere Offers_contains { get; set; }
        public List<OfferWhere> Offer_contains_every { get; set; }
        public List<OfferWhere> Offer_contains_some { get; set; }
        public RenewalHistoryWhere RenewalHistory { get; set; }

        public string Status { get; set; }
        public bool? IsIssued { get; set; }
        public DateTime? IssuedAt_lt { get; set; }
        public DateTime? IssuedAt_gt { get; set; }
        public OfferWhere Offers_every { get; set; }
        public bool? Offers_exist { get; set; }
        public string ReferralCode { get; set; }
        public bool? IsApprovalNeeded { get; set; }
        public string ChannelId { get; set; }
    }

    public class FactWhere : Where
    {
        private JToken _jValue;
        public string Id { get; set; }
        public string Type { get; set; }
        public string ValueJsonString { get; set; }
        public JToken Value
        {
            get
            {
                if (ValueJsonString == null) return _jValue;

                Result<JToken> parseResult = TryParseJsonStringToJToken(ValueJsonString);
                _jValue = parseResult.Status == "success" ? parseResult.Value : JToken.Parse("{}");

                return _jValue;
            }
            set => _jValue = value;
        }

        internal static Result<JToken> TryParseJsonStringToJToken(string json)
        {
            try
            {
                var token = JToken.Parse(json);
                return new Result<JToken> { Status = "success", Value = token };
            }
            catch (Exception e)
            {
                return new Result<JToken>
                {
                    Status = "failure",
                    Errors = new List<string> { e.Message }
                };
            }
        }
    }
    public class RenewalHistoryWhere
    {
        public bool? IsRenewed { get; set; }
        public string RenewedFromId { get; set; }
        public string RenewedById { get; set; }
    }

    public class OfferWhere : Where
    {
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string OfferNumber { get; set; }
        public List<string> OfferNumber_in { get; set; }
        public string Status { get; set; }
        public List<Products.ProductId> ProductId_in { get; set; }
    }

    public class CreateCaseCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string CaseNumber { get; set; }
        public string Status { get; set; }
        public string Source { get; set; }
        public string HolderId { get; set; }
        public IEnumerable<string> OtherHolderIds { get; set; }
        public IEnumerable<string> InsuredIds { get; set; }
        public string ComponentId { get; set; }
        public string ChannelId { get; set; }

        public string Fields { get; set; }
        public string FieldsSchemaId { get; set; }
        public string WorkflowSchemaId { get; set; }

        public string CreatedById { get; set; }
    }

    public class UpdateCaseCommand
    {
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string Source { get; set; }
        public bool IsSourceChanged { get; set; }
        public string CaseNumber { get; set; }
        public bool IsCaseNumberChanged { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public string HolderId { get; set; }
        public bool IsHolderIdChanged { get; set; }
        public List<string> OtherHolderIds { get; set; }
        public bool IsOtherHolderIdsChanged { get; set; }

        public List<string> InsuredIds { get; set; }
        public bool IsInsuredIdsChanged { get; set; }

        public string ComponentId { get; set; }
        public bool IsComponentIdChanged { get; set; }

        public string Fields { get; set; }
        public string FieldsPatch { get; set; }
        public bool IsFieldsChanged { get; set; }
        public string FieldsSchemaId { get; set; }
        public bool IsFieldsSchemaIdChanged { get; set; }
        public string WorkflowSchemaId { get; set; }
        public bool IsWorkflowSchemaIdChanged { get; set; }
        public string ChannelId { get; set; }
        public bool IsChannelIdChanged { get; set; }


        public string ModifiedById { get; set; }
    }

    public class DeleteCaseCommand
    {
        public string DeletedById { get; set; }
    }

    public class AddProposalCommand
    {
        public string Name { get; set; }
        public string Status { get; set; }
        public string Description { get; set; }
        public string ProposalNumber { get; set; }
        public string ProposalNumberType { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string ReferralCode { get; set; }
        public PremiumInput TotalPrice { get; set; }
        public RenewalHistory RenewalHistory { get; set; }
        public string AddedById { get; set; }
    }

    public class CopyProposalCommand
    {
        public string CopiedFromId { get; set; }
        public string CopiedById { get; set; }
    }

    public class RenewProposalCommand
    {
        public string ProposalNumber { get; set; }
        public bool OverrideStartDateAndEndDateAutomatically { get; set; }
        public string RenewedFromId { get; set; }
        public string RenewedById { get; set; }
    }

    public class IssueProposalCommand
    {
        public string ProposalId { get; set; }
        public string IssuedById { get; set; }
    }

    public class RejectProposalCommand
    {
        public string ProposalId { get; set; }
        public IEnumerable<string> Codes { get; set; }
        public string Remarks { get; set; }
        public string RejectedById { get; set; }
    }

    public class UpdateProposalCommand
    {
        public string ProposalId { get; set; }
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public string ProposalNumber { get; set; }
        public bool IsProposalNumberChanged { get; set; }
        public string ReferralCode { get; set; }
        public bool IsReferralCodeChanged { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public bool IsExpiryDateChanged { get; set; }
        public PremiumToUpdate TotalPrice { get; set; }
        public bool IsTotalPriceChanged { get; set; }
        public RenewalHistory RenewalHistory { get; set; }
        public bool IsRenewalHistoryChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveOfferFromProposalCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string RemovedById { get; set; }
    }

    public class GeneratePoliciesFromProposalCommand
    {
        public string ProposalId { get; set; }
        public string ClientId { get; set; }
        public string GeneratedById { get; set; }
        public bool CopyCaseFieldsToExtraFields { get; set; }
        public bool StoreClausesByValue { get; set; }
        public bool StoreJacketsByValue { get; set; }
        public string PolicyStatus { get; set; }
    }

    public class CaseAgent
    {
        public string Id { get; set; }
        public string StakeholderId { get; set; }
        public string PortalUserId { get; set; }
        public string EntityId { get; set; }
        public CaseAgentRoleType RoleType { get; set; }
    }

    public class CaseHandler
    {
        public string StakeholderId { get; set; }
        public string PortalUserId { get; set; }
        public string EntityId { get; set; }
    }
    
    public enum CaseAgentRoleType
    {
        Primary,
        Secondary,
        Servicing
    }
}
