﻿#nullable enable

namespace CoverGo.Gateway.RemoteSchemas.Domain;

public interface IRemoteServicesFilter
{
    /// <summary>
    /// Returns true if <paramref name="serviceName"/> is available for the specified <paramref name="tenantId"/>.
    /// </summary>
    /// <param name="serviceName"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    public bool CanUseService(
        string serviceName,
        string? tenantId);
}
