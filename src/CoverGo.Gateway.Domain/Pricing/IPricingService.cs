﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Products;

using Newtonsoft.Json.Linq;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Domain.Pricing
{
    public interface IPricingService
    {
        Task<Result> MigratePricingsAsync(string tenantId, MigratePricingsCommand command);
        Task<Result> CleanPricingTest(string tenantId, string typeId);
        Task<IEnumerable<PriceDto>> GetPricingsAsync(string tenantId, PriceFilter factors);
        Task<IEnumerable<PriceDto>> GetConvertedPricingsAsync(string tenantId, PriceFilter factors);
        Task<IEnumerable<PriceDto>> GetConvertedPricingsAsync(string tenantId, ExchangeRateConversionInput input);
        Task<Result> CreatePriceLogic(string tenantId, ProductId productId, CreatePriceLogicCommand command);
        Task<Result> UpdatePriceLogic(string tenantId, ProductId productId, UpdatePriceLogicCommand command);
        Task<Result> DeletePriceLogic(string tenantId, ProductId productId, DeletePriceLogicCommand command);
        Task<Result<CreatedStatus>> AddPricingOptionAsync(string tenantId, ProductId productId, AddPricingOptionCommand command);
        Task<Result> UpdatePricingOptionAsync(string tenantId, ProductId productId, UpdatePricingOptionCommand command);
        Task<Result> RemovePricingOptionAsync(string tenantId, ProductId productId, RemoveCommand command);

    }

    public class PriceFilter
    {
        public IEnumerable<ProductId> ProductIds { get; set; }
        public IEnumerable<string> DiscountCodes { get; set; }
        public JToken Factors { get; set; }
        public DateTime PricingDate { get; set; }
        public IEnumerable<CurrencyCode> ConvertTo { get; set; }
    }

    public abstract class PriceFactors
    {
        public IEnumerable<ProductId> ProductIds { get; set; }
        public IEnumerable<BenefitOption> BenefitOptions { get; set; }
        public IEnumerable<string> DiscountCodes { get; set; }
    }

    public class BenefitOption : SystemObject
    {
        public string TypeId { get; set; }
        public string Key { get; set; }
        public JToken Value { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public string InsuredId { get; set; }
    }

    public class UpsertBenefitOptionCommand
    {
        public string CommandId { get; set; }
        public DateTime Timestamp { get; set; }
        public string EndorsementId { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string TypeId { get; set; } //ToDo: Should be benefitTypeId
        public string Key { get; set; }
        public JToken Value { get; set; }
        public string InsuredId { get; set; }

        public string UpsertedById { get; set; }
    }

    public class UpsertBenefitOptionCommandBatch
    {
        public List<UpsertBenefitOptionCommand> UpsertBenefitOptionCommands { get; set; }
    }

    public class AddUpsertBenefitOptionCommandToRequestCommand
    {
        public string RequestId { get; set; }
        public UpsertBenefitOptionCommand Command { get; set; }
    }

    public class RemoveBenefitOptionCommand
    {
        public string CommandId { get; set; }
        public DateTime Timestamp { get; set; }
        public string EndorsementId { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string TypeId { get; set; } //ToDo: Should be benefitTypeId
        public string RemovedById { get; set; }
    }

    public class PriceDto : SystemObject
    {
        public string DataLoaderCacheId { get; set; }
        public ProductId ProductId { get; set; }
        public decimal? Amount { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public PaymentFrequency PaymentFrequency { get; set; }
        public IEnumerable<Discount> AppliedDiscounts { get; set; }
        public IEnumerable<Tax> AppliedTaxes { get; set; }
        public IEnumerable<Discount> Discounts { get; set; }
        public IEnumerable<Loading> Loadings { get; set; }
        public decimal? OriginalPrice { get; set; }
        public List<IndicativePrice> IndicativePrices { get; set; }
        public List<PricingOption> Options { get; set; }
        public string AmountLogic { get; set; }
        public string Metadata { get; set; }
    }

    public class IndicativePrice
    {
        public string Type { get; set; }
        public decimal Amount { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public IEnumerable<Discount> AppliedDiscounts { get; set; }
        public IEnumerable<Discount> Discounts { get; set; }
        public IEnumerable<Tax> AppliedTaxes { get; set; }
        public decimal OriginalPrice { get; set; }
    }

    public class PricingOption
    {
        public string Id { get; set; }
        public string Key { get; set; }
        public JToken Value { get; set; } 
    }

    public class Discount : SystemObject
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public decimal? Ratio { get; set; }
        public decimal? Flat { get; set; }
        public decimal? OriginalPrice { get; set; }
        public decimal? NewPrice { get; set; }
        public bool AlwaysApplies { get; set; }
        public int Order { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public JToken CalculationJsonLogic { get; set; }
        public bool IsManual { get; set; }
    }

    public class Tax : SystemObject
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public decimal? OriginalPrice { get; set; }
        public decimal? NewPrice { get; set; }
        public decimal? Ratio { get; set; }
        public decimal? Flat { get; set; }
        public int Order { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public JToken CalculationJsonLogic { get; set; }
    }

    public class AugmentedPrice : SystemObject
    {
        public decimal? Amount { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public PaymentFrequency PaymentFrequency { get; set; }
        public List<Discount> AppliedDiscounts { get; set; }
        public List<string> DiscountCodes { get; set; }
        public List<Discount> Discounts { get; set; }
        public List<Tax> AppliedTaxes { get; set; }
        public List<Loading> Loadings { get; set; }
        public decimal? OriginalPrice { get; set; }
        public string Metadata { get; set; }
        public bool IsPricedAtStartDate { get; set; }
    }

    public class Price
    {
        public decimal Amount { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
    }

    public class PriceGraph
    {
        public decimal Amount { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public string FormattedPrice { get; set; }
    }

    public class PremiumInput
    {
        public decimal? Amount { get; set; }
        public decimal? GrossAmount { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public PaymentFrequency PaymentFrequency { get; set; }
        public IEnumerable<string> DiscountCodes { get; set; }
        public List<Discount> AppliedDiscounts { get; set; }
        public List<Tax> AppliedTaxes { get; set; }
        public List<Discount> Discounts { get; set; }
        public List<Loading> Loadings { get; set; }
        public decimal? OriginalPrice { get; set; }
        public string Metadata { get; set; }
        public bool IsPricedAtStartDate { get; set; }
    }

    public class Loading : SystemObject
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public decimal? Ratio { get; set; }
        public decimal? Flat { get; set; }
        public int Order { get; set; }
        public JToken CalculationJsonLogic { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public decimal? OriginalPrice { get; set; }
        public decimal? NewPrice { get; set; }
    }

    public class Exclusion : SystemObject
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string BenefitParentTypeId { get; set; }
        public string BenefitTypeId { get; set; }
        public string BenefitOptionKey { get; set; }
        public string Remark { get; set; }
    }

    public class CreatePriceLogicCommand
    {
        public ProductId ProductId { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public IEnumerable<PricingOption> Options { get; set; }
        public string AmountLogic { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdatePriceLogicCommand
    {
        public CurrencyCode CurrencyCode { get; set; }
        public bool IsCurrencyCodeChanged { get; set; }
        public string AmountLogic { get; set; }
        public bool IsAmountLogicChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class DeletePriceLogicCommand
    {
        public string DeletedById { get; set; }
    }

    public class AddPricingOptionCommand
    {
        public string Key { get; set; }
        public JToken Value { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdatePricingOptionCommand
    {
        public string Id { get; set; }
        public string Key { get; set; }
        public bool IsKeyChanged { get; set; }
        public JToken Value { get; set; }
        public bool IsValueChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class MigratePricingsCommand
    {
        public IEnumerable<CreatePriceLogicCommand> PriceLogicInputs { get; set; }
    }

    public class ExchangeRateConversionInput
    {
        public IEnumerable<PriceDto> Prices { get; set; }
        public ExchangeRateConversionOptions Options { get; set; }
    }

    public class ExchangeRateConversionOptions
    {
        public IEnumerable<CurrencyCode> CurrencyCodes { get; set; }
    }
}
