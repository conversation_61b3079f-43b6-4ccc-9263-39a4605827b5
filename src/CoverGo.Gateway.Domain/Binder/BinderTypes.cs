﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Commissions;
using CoverGo.Gateway.Domain.Users;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Domain.Binder
{
    public interface IBinderService
    {
        Task<long> GetTotalCountAsync(string tenantId, BinderWhere where);
        Task<IEnumerable<Binder>> GetAsync(string tenantId, BinderQueryArguments queryArguments);
        Task<IDictionary<string, Binder>> GetDictionaryAsync(string tenantId, BinderWhere where);
        Task<Result<CreatedStatus>> CreateAsync(string tenantId, CreateBinderCommand command);
        Task<Result> UpdateAsync(string tenantId, string binderId, UpdateBinderCommand command);
        Task<Result> DeleteAsync(string tenantId, string binderId, DeleteBinderCommand command);
        Task<Result<CreatedStatus>> AddFactAsync(string tenantId, string binderId, AddFactCommand command);
        Task<Result> UpdateFactAsync(string tenantId, string binderId, UpdateFactCommand command);
        Task<Result> RemoveFactAsync(string tenantId, string binderId, RemoveFactCommand command);
    }

    public class Binder : SystemObject
    {
        public string Id { get; set; }
        public List<OtherId> OtherIds { get; set; }
        public string Name { get; set; }
        public string Status { get; set; }
        public DateTime? BindingDate { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string FirstPartyEntityId { get; set; }
        public string SecondPartyEntityId { get; set; }
        public CommissionRule CommissionRule { get; set; }
        public List<Fact> Facts { get; set; }
    }

    public class OtherId
    {
        public string Key { get; set; }
        public string Id { get; set; }
    }

    public class BinderWhere : Where
    {
        public List<BinderWhere> Or { get; set; }
        public List<BinderWhere> And { get; set; }

        public string Id { get; set; }
        public IEnumerable<string> Id_in { get; set; }
        public string Status { get; set; }
        public CommissionRuleWhere CommissionRuleWhere { get; set; }
    }

    public class CreateBinderCommand
    {
        public string Name { get; set; }
        public IEnumerable<OtherId> OtherIds { get; set; }
        public DateTime? BindingDate { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string FirstPartyEntityId { get; set; }
        public string SecondPartyEntityId { get; set; }
        public UpdateCommissionRuleCommand CommissionRule { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateBinderCommand
    {
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public IEnumerable<OtherId> OtherIds { get; set; }
        public bool IsOtherIdsChanged { get; set; }
        public DateTime? BindingDate { get; set; }
        public bool IsBindingDateChanged { get; set; }
        public DateTime? StartDate { get; set; }
        public bool IsStartDateChanged { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsEndDateChanged { get; set; }
        public string FirstPartyEntityId { get; set; }
        public bool IsFirstPartyEntityIdChanged { get; set; }
        public string SecondPartyEntityId { get; set; }
        public bool IsSecondPartyEntityIdChanged { get; set; }
        public UpdateCommissionRuleCommand CommissionRule { get; set; }
        public bool IsCommissionRuleChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class DeleteBinderCommand
    {
        public string DeletedById { get; set; }
    }

    public class BinderQueryArguments
    {
        public BinderWhere Where { get; set; }
        public OrderBy OrderBy { get; set; }
        public int? First { get; set; }
        public int? Skip { get; set; }
    }

    public class AssociatedContract
    {
        public string Id { get; set; }
        public string ContractId { get; set; }
    }

    public class AddAssociatedContractCommand
    {
        public string CommandId { get; set; }
        public string EndorsementId { get; set; }
        public DateTime Timestamp { get; set; }

        public string ContractId { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string AddedById { get; set; }
    }

    public class RemoveAssociatedContractCommand
    {
        public string CommandId { get; set; }
        public string EndorsementId { get; set; }
        public DateTime Timestamp { get; set; }

        public string Id { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string RemovedById { get; set; }
    }
}
