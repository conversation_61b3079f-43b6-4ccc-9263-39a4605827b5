﻿namespace CoverGo.Gateway.Domain.Pdf
{
    public class MarginSettings
    {
        public double? Top { get; set; }
        public double? Bottom { get; set; }
        public double? Left { get; set; }
        public double? Right { get; set; }
    }

    public class HeaderFooterSettings
    {
        public int? FontSize { get; set; }
        public string FontName { get; set; }
        public string Left { get; set; }
        public string Center { get; set; }
        public string Right { get; set; }
        public bool? Line { get; set; }
        public double? Spacing { get; set; }
        public string HtmUrl { get; set; }
        public string Html { get; set; }
    }
}
