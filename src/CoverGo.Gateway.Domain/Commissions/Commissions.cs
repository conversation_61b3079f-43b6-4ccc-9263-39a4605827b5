﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Products;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;

namespace CoverGo.Gateway.Domain.Commissions
{
    public class CommissionRule : SystemObject
    {
        public string Id { get; set; }
        public string JsonRule { get; set; }
        public ProductId ProductId { get; set; }
        public IEnumerable<string> EntityIds { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class CommissionRuleQuery
    {
        public CommissionRuleWhere Where { get; set; }
        public OrderBy OrderBy { get; set; }
        public int? Skip { get; set; }
        public int? Limit { get; set; }
    }


    public class CommissionRuleWhere : Where
    {
        public List<CommissionRuleWhere> And { get; set; }
        public List<CommissionRuleWhere> Or { get; set; }

        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public ProductIdWhere ProductId { get; set; }
        public string EntityId { get; set; }
    }

    public class CreateCommissionRuleCommand
    {
        public ProductId ProductId { get; set; }
        public string JsonRule { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateCommissionRuleCommand
    {
        public string Id { get; set; }
        public ProductId ProductId { get; set; }
        public bool IsProductIdChanged { get; set; }
        public string JsonRule { get; set; }
        public bool IsJsonRuleChanged { get; set; }
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class Function : SystemObject
    {
        public string Id { get; set; }
        public Engine Engine { get; set; }
        public IEnumerable<Input> Inputs { get; set; }
        public IEnumerable<Output> Outputs { get; set; }
    }

    public class Engine
    {
        public string Id { get; set; } //maybe not needed now
        public string Name { get; set; } //should maybe be localized?
        public string RunTime { get; set; } //is only 'excelTemplate` for now
        public string Version { get; set; } //maybe not needed as these can be on the template
        public string TemplateId { get; set; } //only for excelTemplate
    }

    public class Input
    {
        public string Id { get; set; }
        public string Type { get; set; } //might be needed to cast before evaluation
        public string Name { get; set; }
        public string Liquid { get; set; } //check if hardcoded value works here, it should
        public string Var { get; set; } //for excel, this is cell address mapping
    }

    public class Output
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Var { get; set; } //for excel, this is cell address mapping
        public JToken Value { get; set; }
    }

    public class CreateFunctionCommand
    {
        public string Id { get; set; }
        public Engine Engine { get; set; }
        public IEnumerable<Input> Inputs { get; set; }
        public IEnumerable<Output> Outputs { get; set; }
        public string CreatedById { get; set; }
    }
}
