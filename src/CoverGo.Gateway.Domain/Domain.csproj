<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>CoverGo.Gateway.Domain</AssemblyName>
    <RootNamespace>CoverGo.Gateway.Domain</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ComponentPro.Saml" />
    <PackageReference Include="CoverGo.BuildingBlocks.Auth" />
    <PackageReference Include="CoverGo.DateUtils" />
    <PackageReference Include="CoverGo.Proxies.Product" />
    <PackageReference Include="CoverGo.Users.Client" />
    <PackageReference Include="CoverGo.Policies.Client" />
    <PackageReference Include="GraphQL.Client" />
    <PackageReference Include="IdentityModel" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Newtonsoft.Json" />
  </ItemGroup>

</Project>
