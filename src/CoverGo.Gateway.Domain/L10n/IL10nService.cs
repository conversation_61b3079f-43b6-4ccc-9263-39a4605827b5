﻿using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Domain.L10n
{
    public interface IL10nService
    {
        Task<Result> MigrateL10ns(string tenantId);
        Task<IDictionary<string, string>> GetL10nsAsync(string tenantId, string locale, IEnumerable<string> ids);
        Task<IEnumerable<string>> GetKeysFromValueContainsAsync(string tenantId, string locale, string valueContains, string keyStartsWith, string keyEndsWith);
        Task UpsertAsync(string tenantId, UpsertL10nCommand command);
        Task<Result> DeleteAsync(string tenantId, DeleteL10nCommand command);
        Task<Result> DeleteAllAsync(string tenantId, DeleteAllL10nCommand deleteAllL10nCommand);
    }

    public class UpsertL10nCommand
    {
        public string Locale { get; set; }
        public string Key { get; set; }
        public string Value { get; set; }
    }

    public class DeleteL10nCommand
    {
        public string Locale { get; set; }
        public string Key { get; set; }
    }

    public class DeleteAllL10nCommand
    {
        public string Key { get; set; }
        public string DeletedById { get; set; }
    }

    public class Configs
    {
        public string EmailSender { get; set; }
        public string EmailName { get; set; }
    }
}
