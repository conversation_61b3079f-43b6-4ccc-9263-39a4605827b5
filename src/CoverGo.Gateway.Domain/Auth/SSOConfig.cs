﻿using System.Collections.Generic;

namespace CoverGo.Gateway.Domain.Auth;

public class SSOConfig
{
    public string Id { get; set; }
    public string IdClaim { get; set; } //Which claim to check to get the `Id` of this configuration
    public string KeyUrlClaim { get; set; } //Which claim to check for KeyUrl
    public string KeyUrl { get; set; } // Default claim if KeyUrlClaim does not exist
    public Dictionary<string, string> ClaimsMap { get; set; }
    public Dictionary<string, List<string>> AdditionalClaims { get; set; }
    public string ClientId { get; set; } // Client ID for OIDC
    public string ClientSecret { get; set; } // Client secret to get access token from code when using authorization code flow
    public bool? ValidateExistingLoginByEmail { get; set; } = false;
    public string TenantId { get; set; } // Populated in service, would be redundant to save
    public bool? UseIdentityToken { get; set; } = false; //Use IdentityToken as AccessToken for AzureAD
    public string ClientIdClaim { get; set; } = "client_id"; // Client ID for OIDC
    public bool? AutoProvisionUser { get; set; } = false;
    public string[] UserNameClaims { get; set; } //which claims to check to build user name
    public bool? AutoAssignRoles { get; set; } = false;
    public string RolesClaim { get; set; } = "groups"; //which claim to check for external roles
    public Dictionary<string, string> ExternalRolesMatching { get; set; } //how to match external roles on CoverGo roles
    public string[] DefaultRoles { get; set; }
    public string ProviderId { get; set; }
    public string IdentityProviderHint { get; set; }
    public string ErrorPath { get; set; } = "/login";
}