using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ComponentPro.Saml2;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Notifications;
using CoverGo.Gateway.Domain.Users;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Domain.Auth
{
    public interface IAuthService
    {
        Task<Token> GetInternalAccessTokenAsync(string tenantId, string asLoginId, string appId, IDictionary<string, string> additionalClaims, CancellationToken cancellationToken = default);
        Task<Token> GetWholeAccessTokenAsync(string tenantId, string clientId, string clientSecret, string username, string password) => GetWholeAccessTokenAsync(tenantId, clientId, clientSecret, username, password, new Dictionary<string, string>());
        Task<Token> GetWholeAccessTokenAsync(string tenantId, string clientId, string clientSecret, string username, string password, Dictionary<string, string> parameters);
        Task<Token> RefreshTokenAsync(string tenantId, string clientId, string clientSecret, string refreshToken);

        Task<Result<PreOtpLogin>> CreatePreOtpLoginAsync(string tenantId, CreatePreOtpLoginCommand command);
        Task<Result<OtpLogin>> CreateOtpLoginAsync(string tenantId, CreateOtpLoginCommand command);
        Task<Result<Token>> CreateAccessTokenFromOtpLoginAsync(string tenantId, CreateAccessTokenFromOtpLoginCommand command);

        Task<Login> GetLoginById(string tenantId, string loginId, string appId = null);
        Task<Login> GetLoginByNameAsync(string tenantId, string username, string appId = null);
        Task<IEnumerable<Login>> GetLoginsAsync(string tenantId, LoginWhere filter);
        Task<long> GetLoginTotalCountAsync(string tenantId, LoginWhere loginFilter);
        Task<Result<CreatedStatus>> CreateLoginAsync(string tenantId, CreateLoginCommand command);
        Task<Result> UpdateLoginAsync(string tenantId, string loginId, UpdateLoginCommand command);
        Task<Result> DeleteLoginAsync(string tenantId, string loginId, DeleteCommand command);

        Task<Result> ForgotPasswordAsync(string tenantId, ForgotPasswordCommand command);
        Task<Result> ConfirmEmailAsync(string tenantId, string loginId, ConfirmEmailCommand command);
        Task<Result> ResetPasswordAsync(string tenantId, string loginId, ResetPasswordCommand command);
        Task<Result> ChangePasswordAsync(string tenantId, string loginId, ChangePasswordCommand command);
        Task<Result> ChangeExpiredPasswordAsync(string tenantId, ChangeExpiredPasswordCommand command);
        Task<Result> ValidatePasswordAsync(string tenantId, string password);
        Task<Result> ResendConfirmationEmailAsync(string tenantId, string loginId, ResendEmailCommand command);
        Task<Result> SendCodeAsync(string tenantId, string loginId, SendCodeCommand command);
        Task<Result> VerifyCodeAsync(string tenantId, string loginId, VerifyCodeCommand command);
        Task<Result> VerifyResetPasswordCodeAsync(string tenantId, string loginId, VerifyCodeCommand command);

        Task<Result> AddTargettedPermissionsAsync(string tenantId, string loginId, IEnumerable<AddTargettedPermissionCommand> command);
        Task<Result> RemoveTargettedPermissionAsync(string tenantId, string loginId, string type, string value, string removedById);
        Task<Result> RemoveTargettedPermissionsAsync(string tenantId, string loginId, IEnumerable<RemoveTargettedPermissionCommand> commands);

        Task<IEnumerable<TargetGroup>> GetAllTargetGroups(string tenantId);
        Task<TargetGroup> GetTargetGroupAsync(string tenantId, string id);
        Task<Result> CreateTargetGroupAsync(string tenantId, CreateTargetGroupCommand command);
        Task<Result> UpdateTargetGroupAsync(string tenantId, string id, UpdateTargetGroupCommand command);
        Task<Result> DeleteTargetGroupAsync(string tenantId, string id, DeleteCommand command);
        Task<Result> AddUserToTargetGroupAsync(string tenantId, string id, string targetId);
        Task<Result> RemoveUserFromTargetGroupAsync(string tenantId, string id, string targetId);
        Task<Result> AddTargetGroupToTargetGroupAsync(string tenantId, string id, string targetGroupId);
        Task<Result> RemoveTargetGroupFromTargetGroupAsync(string tenantId, string id, string targetGroupId);

        Task<IEnumerable<string>> GetAllPermissionsAsync(string tenantId);
        Task<PermissionGroup> GetPermissionGroupAsync(string tenantId, string id);
        Task<IEnumerable<PermissionGroup>> GetPermissionGroupsAsync(string tenantId, PermissionGroupWhere where);
        Task<IEnumerable<EventLog>> GetPermissionGroupEventsAsync(string tenantId, EventQuery queryArguments);
        Task<Result> CreatePermissionGroupAsync(string tenantId, CreatePermissionGroupCommand command);
        Task<Result> UpdatePermissionGroupAsync(string tenantId, string id, UpdatePermissionGroupCommand command);
        Task<Result> DeletePermissionGroupAsync(string tenantId, string id, DeleteCommand command);
        Task<Result> AddPermissionToPermissionGroupAsync(string tenantId, string id, AddPermissionToPermissionGroupCommand command);
        Task<Result> RemovePermissionFromPermissionGroupAsync(string tenantId, string id, RemovePermissionFromPermissionGroupCommand command);
        Task<Result> AddPermissionGroupToPermissionGroupAsync(string tenantId, string id, AddPermissionGroupToPermissionGroupCommand command);
        Task<Result> RemovePermissionGroupFromPermissionGroupAsync(string tenantId, string id, RemovePermissionGroupFromPermissionGroupCommand command);
        Task<Result> AddLoginIdToPermissionGroupAsync(string tenantId, string id, AddLoginPermissionsToPermissionGroupCommand command);
        Task<Result> RemoveLoginIdFromPermissionGroupAsync(string tenantId, string id, RemoveLoginPermissionsFromPermissionGroupCommand command);
        Task<IDictionary<string, Login>> GetLoginsDictionaryAsync(string tenantId, IEnumerable<string> ids);
        Task<ILookup<string, Login>> GetLoginsByEntityIdsLookupAsync(string tenantId, IEnumerable<string> entityIds);
        Task<ILookup<string, Login>> GetLoginsByPermissionGroupIdsLookupAsync(string tenantId, IEnumerable<string> permissionGroupIds);

        Task<PasswordValidators> GetPasswordValidators(string tenantId, string clientId);
        Task<IEnumerable<Login>> GetLoginsAsync(string tenantId, QueryArguments queryArguments);
        Task<IEnumerable<EventLog>> GetLoginEventsAsync(string tenantId, EventQuery queryArguments);
        Task<IEnumerable<DetailedEventLog>> GetLoginEventsV2Async(string tenantId, QueryArguments<LoginEventWhere> queryArguments);

        Task<Result<TenantIdAndAppId>> GetTenantIdAndAppIdByUrlAsync(string url);

        Task<TenantSettings> GetTenantSettingsAsync(string tenantId);
        Task<Result> AddHostToTenantSettingsAsync(string tenantId, string host);
        Task<Result> RemoveHostFromTenantSettingsAsync(string tenantId, string host);

        Task<IEnumerable<App>> GetAppsAsync(string tenantId, QueryArguments queryArguments);
        Task<IDictionary<string, App>> GetAppsDictionaryAsync(string tenantId, AppWhere appWhere);
        Task<IEnumerable<EventLog>> GetAppEventsAsync(string tenantId, EventQuery queryArguments);

        Task<long> GetTotalCountAsync(string tenantId, AppWhere where);

        Task<Result> CreateAppAsync(string tenantId, CreateAppCommand command);
        Task<Result> UpdateAppAsync(string tenantId, string appId, UpdateAppCommand command);
        Task<Result> DeleteAppAsync(string tenantId, string appId, DeleteCommand command);

        Task<Result> CreateTenantAsync(string tenantId, CreateTenantCommand command);
        Task<Result> InitializeTenantAsync(InitializeTenantCommand command);

        Task<Result> UpdateLockoutEndDate(string tenantId, string loginId, ChangeUserLockoutDateCommand command);

        Task<long> GetTotalCount(string tenantId, PermissionSchemaWhere where);
        Task<IEnumerable<PermissionSchema>> GetPermissionSchemas(string tenantId, QueryArguments queryArguments);
        Task<Result<CreatedStatus>> CreatePermissionSchema(string tenantId, CreatePermissionSchemaCommand command);
        Task<Result> UpdatePermissionSchema(string tenantId, UpdatePermissionSchemaCommand command);
        Task<Result> DeletePermissionSchema(string tenantId, string permissionSchemaId, DeleteCommand command);
        Task<Result> AddTargetedPermissionSchemaToLogin(string tenantId, AddTargetedPermissionSchemaToLoginCommand command);
        Task<Result> RemoveTargetedPermissionSchemaFromLogin(string tenantId, RemoveTargetedPermissionSchemaFromLoginCommand command);
        Task<IReadOnlyCollection<TargetedPermissionSchema>> GetTargetedPermissionSchemas(string tenantId, IReadOnlyCollection<string> targetedPermissionSchemaIds);
        Task<Result<IReadOnlyCollection<UserStorageItem>>> UserStorageQueryAsync(string tenantId, string loginId, QueryArguments<UserStorageItemWhere> query);
        Task<Result> UserStorageCreateAsync(string tenantId, string loginId, CreateUserStorageItemCommand command);
        Task<Result> UserStorageUpdateAsync(string tenantId, string loginId, UpdateUserStorageItemCommand command);
        Task<Result> UserStorageDeleteAsync(string tenantId, string loginId, string storageItemKey);
        Task<Result<long>> UserStorageCountAsync(string tenantId, string loginId, QueryArguments<UserStorageItemWhere> query);
        Task<Dictionary<string, List<string>>> GetPermissionTargetIdsAsync(PermissionTargetIdQuery query);
        Dictionary<string, List<string>> GetPermissionTargetIds(
            PermissionTargetIdQuery query);
    }

    public class InitializeTenantCommand
    {
        public string TenantId { get; set; }
    }

    public class Token
    {
        public string AccessToken { get; set; }
        public string IdentityToken { get; set; }
        public string TokenType { get; set; }
        public string RefreshToken { get; set; }
        public string ErrorDescription { get; set; }
        public int ExpiresIn { get; set; }
        public string Error { get; set; }
        public bool RequiresTwoFactor { get; set; }
    }

    public class AppWhere : Where
    {
        public IEnumerable<AppWhere> Or { get; set; }
        public IEnumerable<AppWhere> And { get; set; }
        public string AppId { get; set; }
        public IEnumerable<string> AppId_in { get; set; }
    }

    public class LoginWhere : Where
    {
        public IEnumerable<LoginWhere> Or { get; set; }
        public IEnumerable<LoginWhere> And { get; set; }

        public IEnumerable<string> Ids { get; set; }
        public IEnumerable<string> EntityIds { get; set; }
        public IEnumerable<string> EntityTypes { get; set; }
        public IEnumerable<string> Usernames { get; set; }

        public bool? IsEmailConfirmed { get; set; }
        public bool? IsTelephoneNumberConfirmed { get; set; }
        public IEnumerable<string> Email_in { get; set; }
        public IEnumerable<string> PermissionGroupIds { get; set; }
        public EntityWhere Entity { get; set; }
        public bool? IsActive { get; set; }
        public bool? ExcludePermissions { get; set; } = false;
    }

    public class LoginEventWhere : Where
    {
        public IEnumerable<string> LoginIds { get; set; }
        public IEnumerable<string> Types { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }

    public class TenantSettings
    {
        public string TenantId { get; set; }
        public IEnumerable<string> Hosts { get; set; }
        public IEnumerable<SSOConfig> SSOConfigs { get; set; }
    }

    public class CreateLoginCommand
    {
        public string ClientId { get; set; }
        public string Username { get; set; }
        public string Email { get; set; }
        public string Password { get; set; }
        public string TelephoneNumber { get; set; }
        public string CreatedById { get; set; }
        public string EntityId { get; set; }
        public string EntityType { get; set; }
        public bool IsEmailConfirmed { get; set; }
        public bool IgnorePasswordValidation { get; set; }
        public bool IsPasswordValidationDobRequire { get; set; }
        public IEnumerable<string> AppIdsToBeGrantedAccessTo { get; set; }
        public SendNotificationCommand SendNotificationCommand { get; set; }
        public string RedirectQueryString { get; set; }
        public bool? UseDefaultPermissions { get; set; }
        public DateTime? ActiveFrom { get; set; }
    }

    public class UpdateLoginCommand
    {
        public string UserName { get; set; }
        public bool IsUserNameChanged { get; set; }
        public string Email { get; set; }
        public bool IsEmailChanged { get; set; }
        public string TelephoneNumber { get; set; }
        public bool IsTelephoneNumberChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class CreatePreOtpLoginCommand
    {
        public string ClientId { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
    }

    public class PreOtpLogin
    {
        public string Token { get; set; }
    }

    public class CreateOtpLoginCommand
    {
        public string Token { get; set; }
        public string SmsTemplateId { get; set; }
    }

    public class OtpLogin
    {
        public string Token { get; set; }
    }

    public class CreateAccessTokenFromOtpLoginCommand
    {
        public string Token { get; set; }
        public string OneTimePassword { get; set; }
    }

    public class PasswordValidators
    {
        public bool RequireConfirmedEmail { get; set; } = false;
        public bool RequireConfirmPhoneNumber { get; set; } = false;
        public bool RequireDigit { get; set; } = false;
        public int RequireLength { get; set; } = 0;
        public int RequireUniqueChars { get; set; } = 1;
        public bool RequireLowercase { get; set; } = false;
        public bool RequireUppercase { get; set; } = false;
        public bool RequireNonAlphanumeric { get; set; } = false;
        public bool RequireLetter { get; set; } = false;
    }

    public class VerifyCodeCommand
    {
        public string Purpose { get; set; }
        public string Code { get; set; }
    }

    public class SendCodeCommand
    {
        public string Purpose { get; set; }
        public string ClientId { get; set; }
        public string TemplateId { get; set; }
    }

    public class ResendEmailCommand
    {
        public string ClientId { get; set; }
        public SendNotificationCommand SendNotificationCommand { get; set; }
        public string CallbackUrl { get; set; }
    }

    public class ForgotPasswordCommand
    {
        public string ClientId { get; set; }
        public string Email { get; set; }
        public string Username { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string Language { get; set; }
        public SendNotificationCommand SendNotificationCommand { get; set; }
    }

    public class ForgotPassword2Command
    {
        public string ClientId { get; set; }
        public string Email { get; set; }
        public string Username { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string Language { get; set; }
    }

    public class ChangePasswordCommand
    {
        //public string ClientId { get; set; }
        public string CurrentPassword { get; set; }
        public string NewPassword { get; set; }
        public bool IgnorePasswordValidation { get; set; }
        public SendNotificationCommand SendNotificationCommand { get; set; }
    }

    public class ChangeExpiredPasswordCommand
    {
        public string UserName { get; set; }
        public string CurrentPassword { get; set; }
        public string NewPassword { get; set; }
    }

    public class AddTargettedPermissionCommand
    {
        public string Type { get; set; }
        public string Value { get; set; }
        public string AddedById { get; set; }
    }

    public class RemoveTargettedPermissionCommand
    {
        public string Type { get; set; }
        public string Value { get; set; }
        public string RemovedById { get; set; }
    }

    public class Login : SystemObject
    {
        public string Id { get; set; }
        public string Username { get; set; }
        public string Email { get; set; }
        public bool IsEmailConfirmed { get; set; }
        public string TelephoneNumber { get; set; }
        public bool IsTelephoneNumberConfirmed { get; set; }
        public DateTime? LockoutEndDateUtc { get; set; }
        public int AccessFailedCount { get; set; }
        public Dictionary<string, IEnumerable<string>> TargettedPermissions { get; set; }
        public IEnumerable<PermissionGroup> PermissionGroups { get; set; }
        public IEnumerable<string> InheritedLoginIds { get; set; }
        public string EntityId { get; set; }
        public IReadOnlyCollection<string> TargetedPermissionSchemaIds { get; set; }
        public bool PermissionLazyLoadingRequired { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime? PasswordLastUpdated { get; set; }
    }

    public class TargettedPermission
    {
        public string Type { get; set; }
        public string Value { get; set; }
    }

    public class ResetPasswordCommand
    {
        public string Code { get; set; }
        public string Password { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public SendNotificationCommand SendNotificationCommand { get; set; }
    }

    public class TargetGroup
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public IEnumerable<string> TargetIds { get; set; }
        public IEnumerable<string> TargetGroupIds { get; set; }
    }

    public class CreateTargetGroupCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class UpdateTargetGroupCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class PermissionGroup : SystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public Dictionary<string, IEnumerable<string>> TargettedPermissions { get; set; }
        public IEnumerable<string> PermissionGroupIds { get; set; }
        public IEnumerable<string> ProductTypes { get; set; }
    }

    public class PermissionGroupWhere : Where
    {
        public IEnumerable<PermissionGroupWhere> And { get; set; }
        public IEnumerable<PermissionGroupWhere> Or { get; set; }

        public string Id { get; set; }
        public IEnumerable<string> Id_in { get; set; }
        public string Name { get; set; }
        public IEnumerable<string> Name_in { get; set; }
    }

    public class CreatePermissionGroupCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string CreatedById { get; set; }
        public IEnumerable<string> ProductTypes { get; set; }
    }

    public class UpdatePermissionGroupCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string ModifiedById { get; set; }
        public IEnumerable<string> ProductTypes { get; set; }
    }

    public class AddPermissionToPermissionGroupCommand
    {
        public string PermissionId { get; set; }
        public string TargetId { get; set; } = "";
        public string AddedById { get; set; }
    }

    public class RemovePermissionFromPermissionGroupCommand
    {
        public string PermissionId { get; set; }
        public string TargetId { get; set; } = "";
        public string RemovedById { get; set; }
    }

    public class AddPermissionGroupToPermissionGroupCommand
    {
        public string PermissionGroupId { get; set; }
        public string AddedById { get; set; }
    }

    public class RemovePermissionGroupFromPermissionGroupCommand
    {
        public string PermissionGroupId { get; set; }
        public string RemovedById { get; set; }
    }

    public class AddLoginPermissionsToPermissionGroupCommand
    {
        public string LoginId { get; set; }
        public string AddedById { get; set; }
    }

    public class RemoveLoginPermissionsFromPermissionGroupCommand
    {
        public string LoginId { get; set; }
        public string RemovedById { get; set; }
    }

    public class CreateTenantCommand
    {
        public CreateAdminCommand AdminSettings { get; set; }
    }

    public class CreateAdminCommand
    {
        public string Username { get; set; }
        public string Email { get; set; }
        public string Password { get; set; }
    }

    public class TenantIdAndAppId
    {
        public string TenantId { get; set; }
        public string AppId { get; set; }
    }

    public class App : SystemObject
    {
        public string AppId { get; set; }
        public string AppName { get; set; }
        public List<string> RedirectUris { get; set; }
        public string Email { get; set; }
        public string EmailSenderName { get; set; }
        public bool UseNotificationConfig { get; set; }
        public int? AccessTokenLifetime { get; set; }
        public int? AbsoluteRefreshTokenLifetime { get; set; }
        public int? SlidingRefreshTokenLifetime { get; set; }
        public TimeSpan? EmailConfirmationTokenLifespan { get; set; }
        public UrlRouting UrlRouting { get; set; }
        public bool Requires2FA { get; set; }
        public string DefaultTimeZone { get; set; }
        public bool ActivationTokenExpiryDisabled { get; set; }
        public bool RequiresEmail2FA { get; set; }

        public string AppConfig { get; set; }
    }

    public class UrlRouting
    {
        public string Url { get; set; }
        public string RegexPattern { get; set; }
        public int Order { get; set; }
    }

    public class UrlRoutingToUpdate
    {
        public string Url { get; set; }
        public string IsUrlChanged { get; set; }
        public string RegexPattern { get; set; }
        public bool IsRegexPatternChanged { get; set; }
        public int Order { get; set; }
        public bool IsOrderChanged { get; set; }
    }

    public class CreateAppCommand
    {
        public string AppId { get; set; }
        public string AppName { get; set; }
        public List<string> RedirectUris { get; set; }
        public string Email { get; set; }
        public string EmailSenderName { get; set; }
        public bool UseNotificationConfig { get; set; }
        public int? AccessTokenLifetime { get; set; }
        public int? AbsoluteRefreshTokenLifetime { get; set; }
        public int? SlidingRefreshTokenLifetime { get; set; }
        public TimeSpan? EmailConfirmationTokenLifespan { get; set; }
        public bool Requires2FA { get; set; }
        public UrlRouting UrlRouting { get; set; }
        public string CreatedById { get; set; }
        public string DefaultTimeZone { get; set; }
        public bool ActivationTokenExpiryDisabled { get; set; }
        public bool RequiresEmail2FA { get; set; }
        public string AppConfig { get; set; }
        public ForgotPasswordEmailSettings? ForgotPasswordEmailSettings { get; set; }
    }

    public class ForgotPasswordEmailSettings
    {
        public string From { get; set; }
        public string FromName { get; set; }
        public string Subject { get; set; }
        public string TemplateId { get; set; }
        public string Link { get; set; }
    }

    public class UpdateAppCommand
    {
        public string AppName { get; set; }
        public bool IsAppNameChanged { get; set; }
        public List<string> RedirectUris { get; set; }
        public bool IsRedirectUrisChanged { get; set; }
        public string Email { get; set; }
        public bool IsEmailChanged { get; set; }
        public string EmailSenderName { get; set; }
        public bool IsEmailSenderNameChanged { get; set; }
        public int? AccessTokenLifetime { get; set; }
        public bool IsAccessTokenLifetimeChanged { get; set; }
        public TimeSpan? EmailConfirmationTokenLifespan { get; set; }
        public bool IsEmailConfirmationTokenLifespanChanged { get; set; }
        public bool? Requires2FA { get; set; }
        public bool IsRequires2FAChanged { get; set; }
        public UrlRoutingToUpdate UrlRouting { get; set; }
        public bool IsUrlRoutingChanged { get; set; }
        public bool? UseNotificationConfig { get; set; }
        public bool IsUseNotificationConfigChanged { get; set; }
        public int AbsoluteRefreshTokenLifetime { get; set; }
        public bool IsAbsoluteRefreshTokenLifetimeChanged { get; set; }
        public int SlidingRefreshTokenLifetime { get; set; }
        public bool IsSlidingRefreshTokenLifetimeChanged { get; set; }
        public string DefaultTimeZone { get; set; }
        public bool IsDefaultTimeZoneChanged { get; set; }
        public bool ActivationTokenExpiryDisabled { get; set; }
        public bool IsActivationTokenExpiryDisabledChanged { get; set; }
        public bool RequiresEmail2FA { get; set; }
        public bool IsRequiresEmail2FAChanged { get; set; }
        public string AppConfig { get; set; }
        public string IsAppConfigChanged { get; set; }
        public ForgotPasswordEmailSettingsToUpdate? ForgotPasswordEmailSettings { get; set; }
        public bool IsForgotPasswordEmailSettingsChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class ForgotPasswordEmailSettingsToUpdate
    {
        public string From { get; set; }
        public bool IsFromChanged { get; set; }
        public string FromName { get; set; }
        public bool IsFromNameChanged { get; set; }
        public string Subject { get; set; }
        public bool IsSubjectChanged { get; set; }
        public string TemplateId { get; set; }
        public bool IsTemplateIdChanged { get; set; }
        public string Link { get; set; }
        public bool IsLinkChanged { get; set; }
    }

    public class OTPRemarks
    {
        public string Username { get; set; }

        public string Token { get; set; }
    }
    public class RemarkInputParam
    {
        public string EncryptedKey { get; set; }
        public string ProductId { get; set; }
        public string LangPref { get; set; }
        public string VoucherCode { get; set; }
        public string ClientId { get; set; }
        public string UserId { get; set; }

    }

    public class RemarkOutputParam
    {
        public bool Success { get; set; }
        public Token Token { get; set; }
        public string ErrorMessage { get; set; }
    }
    public class PermissioningObject
    {
        public string DelegatinLoginId { get; set; }
        public List<AddTargettedPermissionCommand> ToDelegate { get; set; }
        public List<AddTargettedPermissionCommand> ToRequest { get; set; }
    }

    public class ValidatePasswordCommand
    {
        public string Password { get; set; }
    }

    public class ConfirmEmailCommand
    {
        public string Code { get; set; }
        public string ConfirmedById { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public SendNotificationCommand SendNotificationCommand { get; set; }
    }

    public class ChangeUserLockoutDateCommand
    {
        public DateTime? EndDateTime { get; set; }
        public string ModifiedById { get; set; }
    }

    public class PermissionSchema : SystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string ObjectType { get; set; }
        public PermissionSchemaActionType ActionType { get; set; }
        public JToken Schema { get; set; }
        public FieldsWhere StateCondition { get; set; }
        public FieldsWhere UpdateCondition { get; set; }
    }

    public enum PermissionSchemaActionType
    {
        Read = 1,
        Write = 2
    }

    public class CreatePermissionSchemaCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string ObjectType { get; set; }
        public PermissionSchemaActionType ActionType { get; set; }
        public string Schema { get; set; }
        public FieldsWhere StateCondition { get; set; }
        public FieldsWhere UpdateCondition { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdatePermissionSchemaCommand
    {
        public string PermissionSchemaId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string ObjectType { get; set; }
        public PermissionSchemaActionType? ActionType { get; set; }
        public string Schema { get; set; }
        public FieldsWhere StateCondition { get; set; }
        public FieldsWhere UpdateCondition { get; set; }
        public string ModifiedById { get; set; }
    }

    public class PermissionSchemaWhere : Where
    {
        public IEnumerable<PermissionSchemaWhere> And { get; set; }
        public IEnumerable<PermissionSchemaWhere> Or { get; set; }
        public string Id { get; set; }
        public IEnumerable<string> Id_in { get; set; }
        public string ObjectType { get; set; }
        public PermissionSchemaActionType? ActionType { get; set; }
        public string Name { get; set; }
        public FieldsWhere Schema { get; set; }
    }

    public class TargetedPermissionSchemaGraph
    {
        public string Id { get; set; }

        public PermissionSchema PermissionSchema { get; set; }

        public IReadOnlyCollection<string> TargetIds { get; set; }
    }

    public class TargetedPermissionSchema
    {
        public string Id { get; set; }

        public string PermissionSchemaId { get; set; }

        public IReadOnlyCollection<string> TargetIds { get; set; }
    }

    public class AddTargetedPermissionSchemaToLoginCommand
    {
        public string LoginId { get; set; }

        public string PermissionSchemaId { get; set; }

        public IReadOnlyCollection<string> TargetIds { get; set; }

        public string AddedById { get; set; }
    }

    public class RemoveTargetedPermissionSchemaFromLoginCommand
    {
        public string LoginId { get; set; }

        public string PermissionSchemaId { get; set; }

        public IReadOnlyCollection<string> TargetIds { get; set; }

        public string RemovedById { get; set; }
    }

    public class CreateUserStorageItemCommand
    {
        public string Key { get; set; }
        public string Fields { get; set; }
    }

    public class UserStorageItem : SystemObject
    {
        public string Key { get; set; }
        public JToken Fields { get; set; }

    }

    public class UpdateUserStorageItemCommand
    {
        public string Key { get; set; }
        public string Fields { get; set; }
    }

    public class UserStorageItems
    {
        public List<UserStorageItem> List { get; set; }
        public long Count { get; set; }
    }

    public class UserStorageItemWhere : Where
    {
        public UserStorageItemWhere[] And { get; set; }

        public UserStorageItemWhere[] Or { get; set; }

        public string Key_contains { get; set; }

        public string[] Key_in { get; set; }

        public FieldsWhere FieldsWhere { get; set; }
    }

    public class SamlSSOResponse
    {
        public Response Data { get; set; }
        public bool Success { get; set; }

        public string Message { get; set; }
    }

    public class SamlSsoInputParam
    {
        public string TenantId { get; set; }
        public string ClientId { get; set; }
        public string UserId { get; set; }
        public IList<SamlAttributes> Attributes { get; set; }
    }
    public class SamlAttributes
    {
        public string Name { get; set; }    
        public string Value { get; set; } 
    }

    public record PermissionTargetIdQuery(string TenantId, string LoginId, string[] PermissionNames);
}