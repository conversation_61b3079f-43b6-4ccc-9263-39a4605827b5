﻿using CoverGo.DomainUtils;
using Microsoft.Extensions.Configuration;
using System;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Domain.Auth
{
    public interface ISamlAuthService
    {
        Task<Result<SamlSsoOutputParam>> Verify(SamlSsoInputParam param);
        Task<string> CreateAuthnRequest(string host);
        Task<string> Logout(string host);

        Task<string> Metadata(string host);

        Task<Result<string>> GetPortalUrl(string tenantId, string appId);
        AppSetting AppSetting { get; }
        AccountSetting AccountSetting { get; }
    }

    public class SamlSsoOutputParam
    {
        public Token Token{ get; set; }
        public bool Success { get; set; }

        public string Message { get; set; }
    }

    public class AccountSetting
    {
        private readonly string _cert;
        private readonly string _idpIssuerUrl;
        private readonly string _targetUrl;

        public AccountSetting(IConfiguration _configuration)
        {
            _targetUrl = _configuration.GetValue<string>("BOCL_IDP_SERVICE_URL");
            _cert = _configuration.GetValue<string>("BOCL_SAML_SSO_CERTIFICATE");
            _idpIssuerUrl = _configuration.GetValue<string>("BOCL_IDP_ISSUER_URL");
        }

        public string Certificate => _cert;
        public string IDPIssuerUrl => _idpIssuerUrl; 
        public string IDPServiceUrl => _targetUrl;

    }

    public class AppSetting
    {
        private readonly string _issuer;

        public AppSetting(IConfiguration _configuration)
        {
            _issuer = _configuration.GetValue<string>("BOCL_IDP_SERVICE_ENTITY_ID");
        }
        
        public string Issuer => _issuer;
        
    }

    public class Certificate
    {
        public X509Certificate2 cert;

        public void LoadCertificate(string certificate)
        {
            try
            {
                cert = new X509Certificate2(StringToByteArray(certificate));
            }
            catch (Exception)
            {
                cert = new X509Certificate2(Convert.FromBase64String(certificate));
            }
        }

        public void LoadCertificate(byte[] certificate)
        {
            cert = new X509Certificate2(certificate);
        }

        private byte[] StringToByteArray(string st)
        {
            byte[] bytes = new byte[st.Length];
            for (int i = 0; i < st.Length; i++)
            {
                bytes[i] = (byte)st[i];
            }
            return bytes;
        }
    }
}
