﻿using IdentityModel;

namespace CoverGo.Gateway.Domain.Auth;

public static class AuthTokenNames
{
    public const string AccessToken = OidcConstants.TokenResponse.AccessToken;
    public const string IdentityToken = OidcConstants.TokenResponse.IdentityToken;
    public const string RefreshToken = OidcConstants.TokenResponse.RefreshToken;
    public const string ExpiresIn = OidcConstants.TokenResponse.ExpiresIn;
    public const string ExpiresAt = "expires_at";
}