using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;

namespace CoverGo.Gateway.Domain.Users.Kyc
{
    public class KycProviderInfoGraph
    {
        public string Id { get; set; }
        public KycProviderEnum ProviderEnum { get; set; }
        public string ProviderReference { get; set; }
        public string ProviderStatus { get; set; }
        public IEnumerable<string> ProviderEvents { get; set; }
        
        public static KycProviderInfoGraph ToGraph(KycProviderInfo dto)
        {
            if (dto == null)
                return null;

            return new KycProviderInfoGraph
            {
                Id = dto.Id,
                ProviderEnum = dto.ProviderEnum,
                ProviderReference = dto.ProviderReference,
                ProviderStatus = dto.ProviderStatus,
                ProviderEvents = dto.ProviderEvents.Select(e => e.ToString(Formatting.None))
            };
        }
    }
}