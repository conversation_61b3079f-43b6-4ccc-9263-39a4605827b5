using System.Collections.Generic;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Domain.Users
{
    public class PanelProviderTier : SystemObject
    {
        public string Id { get; set; }
        public string PanelId { get; set; }
        public List<string> ServiceItemAgreedFeeIds { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public JToken Fields { get; set; }
        public IEnumerable<Attachment> Attachments { get; set; }
    }

    public class PanelProviderTierCreateCommand
    {
        public string PanelId { get; set; }

        public List<string> ServiceItemAgreedFeeIds { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public JToken Fields { get; set; }

        public string CreatedById { get; set; }
    }

    public class PanelProviderTierUpdateCommand
    {
        public string PanelId { get; set; }

        public bool IsPanelIdChanged { get; set; }

        public string Name { get; set; }

        public bool IsNameChanged { get; set; }

        public string Description { get; set; }

        public bool IsDescriptionChanged { get; set; }

        public JToken Fields { get; set; }

        public bool IsFieldsChanged { get; set; }

        public string ModifiedById { get; set; }
    }

    public class ServiceItemAgreedFeeAddCommand
    {
        public string Id { get; set; }
        public string AddedById { get; set; }
    }

    public class ServiceItemAgreedFeeRemoveCommand
    {
        public string Id { get; set; }
        public string RemovedById { get; set; }
    }

    public class ServiceItemAgreedFeeBatchCommand
    {
        public string[] AddIds { get; set; }

        public string[] RemoveIds { get; set; }

        public string ById { get; set; }
    }


    public class PanelProviderTierWhere : Where
    {
        public IReadOnlyCollection<PanelProviderTierWhere> Or { get; set; }

        public IReadOnlyCollection<PanelProviderTierWhere> And { get; set; }

        public string Id { get; set; }

        public IReadOnlyCollection<string> Id_in { get; set; }

        public string PanelId { get; set; }

        public IReadOnlyCollection<string> PanelId_in { get; set; }

        public string ServiceItemAgreedFeeId { get; set; }

        public IReadOnlyCollection<string> ServiceItemAgreedFeeId_in { get; set; }
    }
}