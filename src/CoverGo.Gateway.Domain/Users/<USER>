using System.Collections.Generic;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Domain.Users
{
    public class Diagnosis
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string ClassKind { get; set; }
        public int? DepthInKind { get; set; }
        public string Code { get; set; }
        public string Chapter { get; set; }
        public JToken Fields { get; set; }
    }
    
    public class CreateDiagnosisCommand
    {
        public string Title { get; set; }
        public string ClassKind { get; set; }
        public int? DepthInKind { get; set; }
        public string Code { get; set; }
        public string Chapter { get; set; }
        public string Fields { get; set; }
        public string CreatedById { get; set; }
    }
    
    public class UpdateDiagnosisCommand
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string ClassKind { get; set; }
        public int? DepthInKind { get; set; }
        public string Code { get; set; }
        public string Chapter { get; set; }
        public string Fields { get; set; }
        public string ModifiedById { get; set; }
    }
    
    public class DiagnosisBatchCommand 
        : IEntityBatch<CreateDiagnosisCommand, UpdateDiagnosisCommand, RemoveCommand>
    {
        public List<CreateDiagnosisCommand> Create { get; set; }
        public List<UpdateDiagnosisCommand> Update { get; set; }
        public List<RemoveCommand> Delete { get; set; }
    }
    
    public class DiagnosisWhere : QueryArguments<Filter<DiagnosisFilter>>
    {
    }
    
    public class DiagnosisFilter
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string ClassKind { get; set; }
        public int? DepthInKind { get; set; }
        public string Code { get; set; }
        public string Chapter { get; set; }
        
        public string Title_contains { get; set; }
        public string ClassKind_contains { get; set; }
        public string Code_contains { get; set; }
        public string Chapter_contains { get; set; }
        
        public List<string> Id_in { get; set; }
        public List<string> Title_in { get; set; }
        public List<string> ClassKind_in { get; set; }
        public List<int> DepthInKind_in { get; set; }
        public List<string> Code_in { get; set; }
        public List<string> Chapter_in { get; set; }
        public FieldsWhere FieldsWhere { get; set; }
    }
}