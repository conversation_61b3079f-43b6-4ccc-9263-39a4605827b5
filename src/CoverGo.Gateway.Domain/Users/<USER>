﻿using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Users;

namespace CoverGo.Users.Domain.Companies
{
    public class Company : Entity
    {
        public string RegistrationNumber { get; set; }
        public string NatureOfBusiness { get; set; }
        public CompanyTypes Type { get; set; }
        public bool? AcceptsMarketing { get; set; }
        public string MigrationBatchId { get; set; }
        public bool? ExternallySearched { get; set; }
    }

    public class CreateCompanyCommand : CreateEntityCommand
    {
        public string RegistrationNumber { get; set; }
        public string NatureOfBusiness { get; set; }
        public CompanyTypes Type { get; set; }
        public bool? AcceptsMarketing { get; set; }
        public AccessPolicy? AccessPolicy { get; set; }
    }

    public enum CompanyTypes
    {
        Undefined,
        Lead,
        Customer
    }

    public class UpdateCompanyCommand : UpdateEntityCommand
    {
        public string RegistrationNumber { get; set; }
        public bool IsRegistrationNumberChanged { get; set; }

        public string NatureOfBusiness { get; set; }
        public bool IsNatureOfBusinessChanged { get; set; }

        public bool? AcceptsMarketing { get; set; }
        public bool IsAcceptsMarketingChanged { get; set; }
    }
}
