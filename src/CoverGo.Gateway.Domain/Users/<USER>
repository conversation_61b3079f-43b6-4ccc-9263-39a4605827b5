
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Encryptions;
using CoverGo.Users.Domain.NegotiatedRate;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.Gateway.Domain.Auth;

namespace CoverGo.Gateway.Domain.Users
{
    public interface IEntityService
    {
        Task<IEnumerable<Entity>> GenericQueryAsync(string tenantId, EntityWhere where);
        Task<IEnumerable<string>> GenericQueryIds(string tenantId, EntityWhere where);
        Task<IEnumerable<EntityId>> GenericQueryIdsAndTypesAsync(string tenantId, EntityWhere where);

        Task<Result<CreatedStatus>> AddLinkAsync(string tenantId, AddLinkCommand command);
        Task<Result<CreatedStatus>> AddLinksAsync(string tenantId, List<AddLinkCommand> commands);
        Task<Result> RemoveLinkAsync(string tenantId, string Id, DeleteCommand command);
        Task<Result> RemoveLinkAsync(string tenantId, RemoveLinkCommand command);
        Task<IEnumerable<Relationships>> GetRelationshipsAsync(string tenantId, QueryArguments queryArguments);
        Task<long> GetRelationshipsTotalCountAsync(string tenantId, RelationshipWhere where);
        Task<Result<CreatedStatus>> AddAddressAsync(string tenantId, string entityId, EntityTypes type, AddAddressCommand command);
        Task<Result> UpdateAddressAsync(string tenantId, string entityId, EntityTypes type, UpdateAddressCommand command);
        Task<Result> RemoveAddressAsync(string tenantId, string entityId, EntityTypes type, string id, string removedById);
        Task<Result<CreatedStatus>> AddIdentityAsync(string tenantId, string entityId, EntityTypes type, AddIdentityCommand command);
        Task<Result> UpdateIdentityAsync(string tenantId, string entityId, EntityTypes type, UpdateIdentityCommand command);
        Task<Result> RemoveIdentityAsync(string tenantId, string entityId, EntityTypes type, string id, string removedById);
        Task<Result<CreatedStatus>> AddContactAsync(string tenantId, string entityId, EntityTypes type, AddContactCommand command);
        Task<Result> UpdateContactAsync(string tenantId, string entityId, EntityTypes type, UpdateContactCommand command);
        Task<Result> RemoveContactAsync(string tenantId, string entityId, EntityTypes type, string id, string removedById);
        Task<Result<CreatedStatus>> AddFactAsync(string tenantId, string entityId, EntityTypes type, AddFactCommand command);
        Task<Result> UpdateFactAsync(string tenantId, string entityId, EntityTypes type, UpdateFactCommand command);
        Task<Result> RemoveFactAsync(string tenantId, string entityId, EntityTypes type, string id, string removedById);
        Task<Result> FactBatchAsync(string tenantId, EntityTypes type, string entityId, FactCommandBatch batch);

        Task<Result> AddAttachmentAsync(string tenantId, string entityId, EntityTypes type, AddAttachmentCommand command);

        Task<Result> UpdateAttachmentAsync(string tenantId, string entityId, EntityTypes type, UpdateAttachmentCommand command);

        Task<Result> RemoveAttachmentAsync(string tenantId, string entityId, EntityTypes type, RemoveAttachmentCommand command);
        Task<Result> AddNoteAsync(string tenantId, string entityId, EntityTypes type, AddNoteCommand command);
        Task<Result> UpdateNoteAsync(string tenantId, string entityId, EntityTypes type, UpdateNoteCommand command);
        Task<Result> RemoveNoteAsync(string tenantId, string entityId, EntityTypes type, string id, string removedById);

        Task<Result> InitializeTenantEncryptionsAsync(string tenantId, InitializeTenantEncryptionsCommand command);
        Task<Result<CreatedStatus>> CreateServiceItemAsync(string tenantId, CreateServiceItemCommand command);
        Task<IEnumerable<ServiceItem>> GetServiceItemsAsync(string tenantId, ServiceItemWhere where);
        Task<Result> UpdateServiceItemAsync(string tenantId, UpdateServiceItemCommand updateServiceItemCommand);
        Task<Result> DeleteServiceItemAsync(string tenantId, string ServiceItemId, DeleteCommand deleteCommand);

        Task<Result<CreatedStatus>> CreateServiceItemAgreedFeeAsync(string tenantId, CreateServiceItemAgreedFeeCommand command);
        Task<IEnumerable<ServiceItemAgreedFee>> GetServiceItemAgreedFeesAsync(string tenantId, ServiceItemAgreedFeeWhere where);
        Task<Result> UpdateServiceItemAgreedFeeAsync(string tenantId, UpdateServiceItemAgreedFeeCommand updateServiceItemAgreedFeeCommand);
        Task<Result> DeleteServiceItemAgreedFeeAsync(string tenantId, string serviceItemAgreedFeeId, DeleteCommand deleteCommand);

        Task<long> GetTotalCountAsync(string tenantId, PanelProviderTierWhere where);

        Task<IEnumerable<PanelProviderTier>> GetAsync(string tenantId, QueryArguments queryArguments);

        Task<List<DomainUtils.EventLog>> GetEventsAsync(string tenantId, EventQuery query);

        Task<Result<CreatedStatus>> CreatePanelProviderTierAsync(string tenantId, PanelProviderTierCreateCommand command);

        Task<Result> UpdatePanelProviderTierAsync(string tenantId, string panelProviderTierId, PanelProviderTierUpdateCommand command);

        Task<Result> DeletePanelProviderTierAsync(string tenantId, string panelProviderTierId, DeleteCommand command);

        Task<Result> AddServiceItemAgreedFeeAsync(string tenantId, string panelProviderTierId, ServiceItemAgreedFeeAddCommand command);

        Task<Result> RemoveServiceItemAgreedFeeAsync(string tenantId, string panelProviderTierId, ServiceItemAgreedFeeRemoveCommand command);

        Task<Result> ServiceItemAgreedFeeBatchAsync(string tenantId, string panelProviderTierId, ServiceItemAgreedFeeBatchCommand command);

        Task<ILookup<string,IEnumerable<ServiceItemAgreedFee>>> GetServiceItemAgreedFeesByTierIds(string tenantId, PanelProviderTierWhere panelProviderTierWhere);

        Task<Result> AddAttachmentAsync(string tenantId, string guaranteeOfPaymentId, AddAttachmentCommand command);

        Task<Result> RemoveAttachmentAsync(string tenantId, string guaranteeOfPaymentId, RemoveAttachmentCommand command);
    }

    public enum EntityTypes
    {
        Undefined,
        Individual,
        Internal,
        Company,
        Object,
        Organization,
    }

    public class Entity : SystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string InternalCode { get; set; }
        public string PhotoPath { get; set; }
        public string EntityType { get; set; }
        public IEnumerable<Contact> Contacts { get; set; }
        public IEnumerable<Identity> Identities { get; set; }
        public IEnumerable<Address> Addresses { get; set; }
        public IEnumerable<Fact> Facts { get; set; }

        public IEnumerable<Attachment> Attachments { get; set; }
        public IEnumerable<Note> Notes { get; set; }

        public IEnumerable<Link> Links { get; set; } //populated in gateway

        public string CreatedBy { get; set; }
        public string LastModifiedBy { get; set; }

        public string Source { get; set; }

        public JToken Fields { get; set; }

        public IEnumerable<string> Tags { get; set; }
        public string Status { get; set; }
        public virtual AccessPolicy? AccessPolicy { get; set; }
    }

    public class EntityId
    {
        public string Id { get; set; }
        public string Type { get; set; }
    }

    public class Stakeholder
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public string Type { get; set; }
    }

    public class AddStakeholderCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string EntityId { get; set; }
        public string Type { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateStakeholderCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Id { get; set; }
        public string EntityId { get; set; }
        public bool IsEntityIdChanged { get; set; }
        public string Type { get; set; }
        public bool IsTypeChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveStakeholderCommand
    {
        public string Id { get; set; }
        public string RemovedById { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
    }

    public class CreateEntityBatchCommand<T> where T : CreateEntityCommand
    {
        public T CreateEntityCommand { get; set; }
        public List<AddContactCommand> AddContactCommands { get; set; } = new();
        public List<AddFactCommand> AddFactCommands { get; set; } = new();
        public List<AddIdentityCommand> AddIdentityCommands { get; set; } = new();
        public List<AddAddressCommand> AddAddressCommands { get; set; } = new();
    }

    public abstract class CreateEntityCommand
    {
        public string NameFormat { get; set; }
        public string CreatedById { get; set; }

        public string InternalCode { get; set; }
        public int? InternalCodeLength { get; set; }

        public InternalCodeGenerationStrategy InternalCodeGenerationStrategy { get; set; }

        public string PhotoPath { get; set; }

        public string Source { get; set; }

        public string Fields { get; set; }

        public IEnumerable<string> Tags { get; set; }

        public string Status { get; set; }

        public string LinkedTo { get; set; }
        public string InternalCodeFormat { get; set; }
    }

    public enum InternalCodeGenerationStrategy
    {
        Random = 0,
        Sequential = 1,
    }

    public abstract class UpdateEntityCommand
    {
        public string EndorsementId { get; set; } // for endorsements
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public string EntityId { get; set; }

        public string Id { get; set; }
        public bool IsIdChanged { get; set; }

        public string NameFormat { get; set; }
        public bool IsNameFormatChanged { get; set; }

        public string InternalCode { get; set; }
        public bool IsInternalCodeChanged { get; set; }

        public string PhotoPath { get; set; }
        public bool IsPhotoPathChanged { get; set; }

        public string Source { get; set; }
        public bool IsSourceChanged { get; set; }

        public string Fields { get; set; }
        public bool IsFieldsChanged { get; set; }
        public string FieldsPatch { get; set; }

        public IEnumerable<string> Tags { get; set; }
        public bool IsTagsChanged { get; set; }

        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class Contact
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public string Value { get; set; }
    }

    public class Identity
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public string Value { get; set; }
    }

    public class Address
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public Dictionary<string, string> Fields { get; set; }
    }

    public class Fact
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public JToken Value { get; set; }
    }

    public class AddLinkCommand
    {
        public string SourceId { get; set; }
        public string SourceEntityType { get; set; }
        public string Link { get; set; }
        public string TargetId { get; set; }
        public string TargetEntityType { get; set; }
        public JToken Value { get; set; }
        public string AddedById { get; set; }
    }

    public class RemoveLinkCommand
    {
        public string SourceId { get; set; }
        public string Link { get; set; }
        public string TargetId { get; set; }
        public string RemovedById { get; set; }
    }

    public class AddAddressCommand
    {
        public string EndorsementId { get; set; } // for endorsements
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public string EntityId { get; set; }

        public string Type { get; set; }
        public Dictionary<string, string> Fields { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateAddressCommand
    {
        public string EndorsementId { get; set; } // for endorsements
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public string EntityId { get; set; }
        public string Id { get; set; }
        public string Type { get; set; }
        public Dictionary<string, string> Fields { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AddContactCommand
    {
        public string EndorsementId { get; set; } // for endorsements
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public string EntityId { get; set; }
        public string Type { get; set; }
        public string Value { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateContactCommand
    {
        public string EndorsementId { get; set; } // for endorsements
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public string EntityId { get; set; }
        public string Id { get; set; }
        public string Type { get; set; }
        public string Value { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AddIdentityCommand
    {
        public string EndorsementId { get; set; } // for endorsements
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public string EntityId { get; set; }
        public string Type { get; set; }
        public string Value { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateIdentityCommand
    {
        public string EndorsementId { get; set; } // for endorsements
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public string EntityId { get; set; }
        public string Id { get; set; }
        public string Type { get; set; }
        public string Value { get; set; }
        public string ModifiedById { get; set; }
    }

    public class Relationships
    {
        public string EntityId { get; set; }
        public IEnumerable<Link> Links { get; set; }
    }

    public class Link : SystemObject
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public string TargetId { get; set; }
        public string TargetEntityType { get; set; }
        public JToken Value { get; set; }
    }

    public class RelationshipWhere : Where
    {
        public IEnumerable<RelationshipWhere> Or { get; set; }
        public IEnumerable<RelationshipWhere> And { get; set; }
        public IEnumerable<string> EntityId_in { get; set; } // checks both source and target
        public string Type { get; set; }
        public IEnumerable<string> Type_in { get; set; }
        public JToken Value { get; set; }
        public JToken Value_not { get; set; }
        public LinkFilter Link { get; set; }
    }

    public class LinkFilter
    {
        public string SourceId { get; set; }
        public IEnumerable<string> SourceId_in { get; set; }
        public IEnumerable<string> SourceEntityTypes_in { get; set; }
        public string Type { get; set; }
        public IEnumerable<string> TypeIn { get; set; }
        public string TargetId { get; set; }
        public IEnumerable<string> TargetId_in { get; set; }
        public IEnumerable<string> TargetEntityTypes_in { get; set; }
    }

    public class RelationshipListWhere : Where
    {
        public string TargetId { get; set; }
        public IEnumerable<string> TargetId_in { get; set; }
        public string Type { get; set; }
        public IEnumerable<string> Type_in { get; set; }
    }
}
