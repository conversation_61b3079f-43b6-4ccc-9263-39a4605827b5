﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using System;
using System.Collections.Generic;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.Gateway.Domain.Auth;

namespace CoverGo.Gateway.Domain.Users
{
    public class CustomerWhere : EntityWhere
    {
        public bool? AcceptsMarketing { get; set; }
    }

    public class CompanyWhere : CustomerWhere
    {
        public List<CompanyWhere> Or { get; set; }
        public List<CompanyWhere> And { get; set; }
        public string Fields_path { get; set; }
        public string Fields_regex { get; set; }
        public string Name_regex { get; set; }
        public string RegistrationNumber { get; set; }
        public List<string> RegistrationNumber_in { get; set; }
        public string RegistrationNumber_contains { get; set; }

        public string NatureOfBusiness { get; set; }
        public List<string> NatureOfBusiness_in { get; set; }
        public string NatureOfBusiness_contains { get; set; }

        public CompanyTypes? Type { get; set; }
        public List<CompanyTypes> Type_in { get; set; }
        public string MigrationBatchId { get; set; }
        public List<string> MigrationBatchId_in { get; set; }
        public bool? HasMigrationBatchId { get; set; }
        public bool? ExternalClientProvider { get; set; }
    }

    public class IndividualWhere : CustomerWhere
    {
        public List<IndividualWhere> Or { get; set; }
        public List<IndividualWhere> And { get; set; }

        public bool? HasEnglishFirstNameNotNull { get; set; }
        public bool? HasEnglishLastNameNotNull { get; set; }

        public string Occupation { get; set; }
        public List<string> Occupation_in { get; set; }
        public string Occupation_contains { get; set; }

        public DateTime? DateOfBirth { get; set; }
        public List<DateTime> DateOfBirth_in { get; set; }
        public DateTime? DateOfBirth_lt { get; set; }
        public DateTime? DateOfBirth_gt { get; set; }

        public IndividualTypes? Type { get; set; }
        public List<IndividualTypes> Type_in { get; set; }
        public bool? HasActivePolicy { get; set; }
        public string IntegrationSystemId { get; set; }
        public List<string> IntegrationExternalEntityId_in { get; set; }
        public ProductWhere Product { get; set; }
        public AccessPolicy? AccessPolicy { get; set; }

        public string Username_contains { get; set; }
    }

    public class ObjectWhere : EntityWhere
    {
        public List<ObjectWhere> Or { get; set; }
        public List<ObjectWhere> And { get; set; }
    }

    public class InternalWhere : EntityWhere
    {
        public List<InternalWhere> Or { get; set; }
        public List<InternalWhere> And { get; set; }

        public string Fields_path { get; set; }
        public string Fields_regex { get; set; }
        public bool? IsActive { get; set; }
        public string Title { get; set; }
        public List<string> Title_in { get; set; }
        public string Title_contains { get; set; }
        public string Name_regex { get; set; }
    }

    public class OrganizationWhere : EntityWhere
    {
        public List<OrganizationWhere> Or { get; set; }
        public List<OrganizationWhere> And { get; set; }

        public string Type { get; set; }
        public bool? IsActive { get; set; }
    }

    public class EntityWhere : Where
    {
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string Id_contains { get; set; }

        public string Name { get; set; }
        public List<string> Name_in { get; set; }
        public string Name_contains { get; set; }

        public string InternalCode { get; set; }
        public List<string> InternalCode_in { get; set; }
        public string InternalCode_contains { get; set; }

        public FactWhere Facts_contains { get; set; }

        public string Email { get; set; }
        public List<string> Email_in { get; set; }
        public string Email_contains { get; set; }

        public string TelephoneNumber { get; set; }
        public List<string> TelephoneNumber_in { get; set; }
        public string TelephoneNumber_contains { get; set; }

        public string Source { get; set; }
        public List<string> Source_in { get; set; }
        public string Source_contains { get; set; }
        public string Source_not_contains { get; set; }
        public IdentityWhere Identities_contains { get; set; }

        public List<string> Tags_in { get; set; }
        public string Tags_contains { get; set; }
        public bool? Tags_exists { get; set; }
        public FieldsWhere Fields { get; set; }
        [Obsolete("Moved into RelationshipList_contains")]
        public string RelationshipListType { get; set; }
        [Obsolete("Moved into RelationshipList_contains")]
        public List<string> RelationshipListType_in { get; set; }
        public RelationshipListWhere RelationshipList_contains { get; set; }

        public string Status { get; set; }
        public List<string> Status_in { get; set; }
    }

    public class IdentityWhere : Where
    {
        public string Id { get; set; }
        public List<string> Id_in { get; set; }

        public string Type { get; set; }
        public List<string> Type_in { get; set; }
        public string Type_contains { get; set; }

        public string Value { get; set; }
        public List<string> Value_in { get; set; }
        public string Value_contains { get; set; }
    }
}
