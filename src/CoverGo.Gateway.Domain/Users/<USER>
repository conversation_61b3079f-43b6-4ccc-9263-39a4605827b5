﻿using CoverGo.Gateway.Domain.Users;
using System.Collections.Generic;

namespace CoverGo.Users.Domain.Objects
{
    public class Object : Entity
    {
        public string TypeId { get; set; }
    }

    public class CreateObjectCommand : CreateEntityCommand
    {
        public string TypeId { get; set; }
        public IEnumerable<Fact> Facts { get; set; }
    }

    public class UpdateObjectCommand : UpdateEntityCommand
    {
        public string TypeId { get; set; }
        public bool IsTypeIdChanged { get; set; }
    }
}
