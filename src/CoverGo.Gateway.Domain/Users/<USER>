﻿namespace CoverGo.Gateway.Domain.Users
{
    public class Organization : Entity
    {
        public string Type { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public class CreateOrganizationCommand : CreateEntityCommand
    {
        public string Type { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public class UpdateOrganizationCommand : UpdateEntityCommand
    {
        public string Type { get; set; }
        public bool IsTypeChanged { get; set; }
        public bool IsActive { get; set; }
        public bool IsIsActiveChanged { get; set; }
    }
}
