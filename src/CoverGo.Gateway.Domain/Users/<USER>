using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Domain.Users
{
    public class Disability : SystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public List<string> DiagnosisIds { get; set; }
    }
    
    public class CreateDisabilityCommand
    {
        public string Name { get; set; }
        public string CreatedById { get; set; }
    }
    
    public class UpdateDisabilityCommand
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string ModifiedById { get; set; }
    }

    public class DisabilityBatchCommand 
        : IEntityBatch<CreateDisabilityCommand, UpdateDisabilityCommand, RemoveCommand>
    {
        public List<CreateDisabilityCommand> Create { get; set; }
        public List<UpdateDisabilityCommand> Update { get; set; }
        public List<RemoveCommand> Delete { get; set; }
    }
    
    public class DisabilityWhere : QueryArguments<Filter<DisabilityFilter>>
    {
    }
    
    public class DisabilityFilter
    {
        public string Id { get; set; }

        public List<string> Id_in { get; set; }
        
        public string Name { get; set; }
        
        public string Name_contains { get; set; }
        
        public List<string> Name_in { get; set; }
    }
}