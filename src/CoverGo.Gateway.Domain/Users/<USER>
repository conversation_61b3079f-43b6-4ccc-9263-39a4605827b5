﻿using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading.Tasks;
using CoverGo.Users.Domain.Individuals;

namespace CoverGo.Gateway.Domain.Users
{
    public interface IEntityService<T, Y, U>
        where T : Entity
        where Y : CreateEntityCommand
        where U : UpdateEntityCommand
    {
        Task<T> GetSingleAsync(string tenantId, string id);
        Task<IEnumerable<T>> GetAsync(string tenantId, QueryArguments searchFilter);
        Task<IEnumerable<T>> GetAsync(string tenantId, EntityWhere where);
        Task<long> GetTotalCount(string tenantId, EntityWhere where);
        Task<IEnumerable<EventLog>> GetEventsAsync(string tenantId, EventQuery query);
        Task<IDictionary<string, T>> GetDictionaryAsync(string tenantId, QueryArguments searchFilter);
        Task<IDictionary<string, T>> GetDictionaryAsync(string tenantId, EntityWhere where);

        Task<Result<CreatedStatus>> CreateAsync(string tenantId, Y command);
        Task<Result> UpdateAsync(string tenantId, string entityId, U command);
        Task<Result> DeleteAsync(string tenantId, string entityId, string deletedById);

        Task<Result<CreatedStatus>> CreateFromBatchAsync(string tenantId, CreateEntityBatchCommand<Y> command);
        Task<Result<CreatedStatus>> CreateManyFromBatchAsync(string tenantId, List<CreateEntityBatchCommand<Y>> commands);
        Task<Result> AddDisabilityAsync(string tenantId, string entityId, AddCommand command);
        Task<Result> RemoveDisabilityAsync(string tenantId, string entityId, RemoveCommand command);
        Task<Result> DisabilityBatchAsync(string tenantId, string individualId, BatchCommand command);
        Task<Result<string>> CreateKycApplicantAsync(string tenantId, string entityId, CreateKycApplicantCommand command);
        Task<Result<string>> GenerateKycAuthTokenAsync(string tenantId, string entityId, GenerateKycAuthTokenCommand command);
        Task<Result> OnKycWebhookEventReceivedAsync(string tenantId, OnKycWebhookEventReceivedCommand command);
    }
}
