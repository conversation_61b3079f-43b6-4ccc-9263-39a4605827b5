using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Gateway.Domain.Users.Kyc
{
    public class KycGraph
    {
        public string Status { get; set; }
        public IEnumerable<KycProviderInfoGraph> ProviderInfos { get; set; }

        public static KycGraph ToGraph(Kyc dto)
        {
            if (dto == null)
                return null;

            return new KycGraph
            {
                Status = dto.Status,
                ProviderInfos = dto.ProviderInfos?.Select(p => KycProviderInfoGraph.ToGraph(p))
            };
        }
    }
}