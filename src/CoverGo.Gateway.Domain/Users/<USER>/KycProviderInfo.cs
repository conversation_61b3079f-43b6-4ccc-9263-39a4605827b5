using System.Collections.Generic;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Domain.Users.Kyc
{
    public class KycProviderInfo
    {
        public string Id { get; set; }
        public KycProviderEnum ProviderEnum { get; set; }
        public string ProviderReference { get; set; }
        public string ProviderStatus { get; set; }
        public IEnumerable<JToken> ProviderEvents { get; set; }
    }
}