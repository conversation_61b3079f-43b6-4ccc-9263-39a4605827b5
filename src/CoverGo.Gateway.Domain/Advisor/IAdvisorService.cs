﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Products;

using Newtonsoft.Json.Linq;

using System.Collections.Generic;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Domain.Advisor
{
    public interface IAdvisorService
    {
        Task<IEnumerable<Advice>> GetAdvicesAsync(string tenantId, AdviceQuery query);
        Task<long> GetReviewTotalCountAsync(string tenantId, ReviewWhere where);
        Task<IEnumerable<Review>> GetReviewsAsync(string tenantId, QueryArguments query);
        Task<IEnumerable<Review>> GetAggregatedReviewsAsync(string tenantId, QueryArguments query);
        Task<Result<CreatedStatus>> CreateReviewAsync(string tenatId, CreateReviewCommand command);
        Task<Result> UpdateReviewAsync(string tenatId, string id, UpdateReviewCommand command);
        Task<Result> DeleteReviewAsync(string tenantId, string id, DeleteCommand command);
        Task<Result<CreatedStatus>> AddScoreAsync(string tenantId, string id, AddScoreCommand command);
        Task<Result> UpdateScoreAsync(string tenantId, string id, UpdateScoreCommand command);
        Task<Result> RemoveScoreAsync(string tenantId, string id, RemoveCommand command);
    }

    public class Score
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public decimal Value { get; set; }
        public string Comment { get; set; }
    }

    public class Advice
    {
        public IEnumerable<Score> Scores { get; set; }
        public Offer Offer { get; set; }
    }

    public class AdviceQuery
    {
        public string AdvisorId { get; set; }
        public IEnumerable<ProductId> ProductIds { get; set; }
        public string ClientId { get; set; }
        public JToken Values { get; set; }
    }

    public class Review : SystemObject
    {
        public string Id { get; set; }
        public string Type { get; set; } //e.g. entities/products/etc.
        public string RelatedId { get; set; }
        public IEnumerable<Score> Scores { get; set; } //for multiple aspects of the related object that can be scored
        public string Comment { get; set; }
    }

    public class CreateReviewCommand
    {
        public string Type { get; set; }
        public string RelatedId { get; set; }
        public IEnumerable<Score> Scores { get; set; }
        public string Comment { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateReviewCommand
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public bool IsTypeChanged { get; set; }
        public string RelatedId { get; set; }
        public bool IsRelatedIdChanged { get; set; }
        public string Comment { get; set; }
        public bool IsCommentChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AddScoreCommand
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public decimal Value { get; set; }
        public string Comment { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateScoreCommand
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public bool IsTypeChanged { get; set; }
        public decimal Value { get; set; }
        public bool IsValueChanged { get; set; }
        public string Comment { get; set; }
        public bool IsCommentChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class ReviewWhere : Where
    {
        public List<ReviewWhere> Or { get; set; }
        public List<ReviewWhere> And { get; set; }

        public string Id { get; set; }
        public IEnumerable<string> Id_in { get; set; }
        public string Type { get; set; }
        public IEnumerable<string> Type_in { get; set; }
        public string RelatedId { get; set; }
        public IEnumerable<string> RelatedId_in { get; set; }
    }
}
