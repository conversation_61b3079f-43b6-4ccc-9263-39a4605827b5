﻿using System;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Commissions;
using CoverGo.Gateway.Domain.Pdf;

using Newtonsoft.Json.Linq;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Domain.Templates
{
    public interface ITemplateService
    {
        Task<long> GetTotalCountAsync(string tenantId, TemplateWhere where);
        Task<IEnumerable<Template>> GetAsync(string tenantId, QueryArguments queryArguments);

        Task<Result<byte[]>> RenderPdfDrawingAsync(string tenantId, string templateId, RenderParameters body);
        Task<Result<string>> RenderPdfDrawingV2Async(string tenantId, string templateId, RenderParameters body);
        Task<Result> CreatePdfDrawingTemplateAsync(string tenantId, CreatePdfDrawingTemplateCommand command);
        Task<Result> UpdatePdfDrawingTemplateAsync(string tenantId, string templateId, UpdatePdfDrawingTemplateCommand command);
        Task<Result<string>> AddPdfDrawingAsync(string tenantId, string templateId, AddPdfDrawingCommand command);
        Task<Result> UpdatePdfDrawingAsync(string tenantId, string templateId, UpdatePdfDrawingCommand command);
        Task<Result> RemovePdfDrawingAsync(string tenantId, string templateId, RemovePdfDrawingCommand command);
        Task<Result<string>> RenderWkhtmltopdfAsync(string tenantId, string templateId, string outputFilePath, RenderParameters body);
        Task<Result<byte[]>> RenderWkhtmltopdfAsync(string tenantId, string templateId, RenderParameters body);
        Task<Result<string>> RenderWkhtmltopdfV2Async(string tenantId, string templateId, RenderParameters body);
        Task<Result<byte[]>> RenderWkhtmltopdfAsync(string tenantId, RenderWkhtmltopdfCommand command);
        Task<Result<string>> RenderWkhtmltopdfV2Async(string tenantId, RenderWkhtmltopdfCommand command);
        Task<Result<string>> CreateWkhtmltopdfTemplateAsync(string tenantId, CreateWkhtmltopdfTemplateCommand command);
        Task<Result> UpdateWkhtmltopdfTemplateAsync(string tenantId, string templateId, UpdateWkhtmltopdfTemplateCommand command);
        Task<Result<string>> AddPageObjectAsync(string tenantId, string templateId, AddPageObjectCommand command);
        Task<Result> UpdatePageObjectAsync(string tenantId, string templateId, UpdatePageObjectCommand command);
        Task<Result> RemovePageObjectAsync(string tenantId, string templateId, RemovePageObjectCommand command);

        Task<Result<string>> RenderEmailMjmlAsync(string tenantId, RenderEmailMjmlCommand renderEmailMjmlCommand);
        Task<Result<EmailRendered>> RenderEmailAsync(string tenantId, string templateId, bool includeAttachments, RenderParameters body);
        Task<Result<SmsRendered>> RenderSmsAsync(string tenantId, RenderSmsCommand renderEmailMjmlCommand);
        Task<Result<SmsRendered>> RenderSmsAsync(string tenantId, string templateId, RenderParameters body);
        Task<Result> CreateEmailMjmlTemplateAsync(string tenantId, CreateEmailMjmlTemplateCommand command);
        Task<Result> UpdateEmailMjmlTemplateAsync(string tenantId, string templateId, UpdateEmailMjmlTemplateCommand command);
        Task<Result> AddEmailAttachmentTemplateAsync(string tenantId, string templateId, AddEmailAttachmentTemplateCommand command);
        Task<Result> UpdateEmailAttachmentTemplateAsync(string tenantId, string templateId, UpdateEmailAttachmentTemplateCommand command);
        Task<Result> RemoveEmailAttachmentTemplateAsync(string tenantId, string templateId, RemoveEmailAttachmentTemplateCommand command);
        Task<Result> AddEmailAttachmentReferenceAsync(string tenantId, string templateId, AddEmailAttachmentReferenceCommand command);
        Task<Result> UpdateEmailAttachmentReferenceAsync(string tenantId, string templateId, UpdateEmailAttachmentReferenceCommand command);
        Task<Result> RemoveEmailAttachmentReferenceAsync(string tenantId, string templateId, RemoveCommand command);

        Task<Result<string>> RenderHtmlTemplateAsync(string tenantId, string TemplateId, RenderParameters parameters);
        Task<Result<string>> RenderHtmlTemplateFromInstanceAsync(string tenantId, HtmlTemplateRenderFromInstanceCommand command);
        Task<Result<string>> RenderHtmlAsync(string tenantId, RenderHtmlCommand command);
        Task<Result<CreatedStatus>> CreateDynamicTemplateAsync(string tenantId, CreateDynamicTemplateCommand command);
        Task<Result> UpdateDynamicTemplateAsync(string tenantId, string templateId, UpdateDynamicTemplateCommand command);
        Task<Result> AddDynamicValueAsync(string tenantId, string templateId, AddDynamicValueCommand command);
        Task<Result> UpdateDynamicValueAsync(string tenantId, string templateId, UpdateDynamicValueCommand command);
        Task<Result> RemoveDynamicValueAsync(string tenantId, string templateId, RemoveDynamicValueCommand command);

        Task<Result<CreatedStatus>> CreateFunctionTemplateAsync(string tenantId, CreateFunctionTemplateCommand command);
        Task<Result> UpdateFunctionTemplateAsync(string tenantId, string templateId, UpdateFunctionTemplateCommand command);
        Task<Result<CreatedStatus>> AddInputToFunctionTemplateAsync(string tenantId, string templateId, AddFunctionInputCommand command);
        Task<Result> UpdateInputOfFunctionTemplateAsync(string tenantId, string templateId, UpdateFunctionInputCommand command);
        Task<Result> RemoveInputFromFunctionTemplateAsync(string tenantId, string templateId, RemoveCommand command);
        Task<Result<CreatedStatus>> AddOutputToFunctionTemplateAsync(string tenantId, string templateId, AddFunctionOutputCommand command);
        Task<Result> UpdateOutputOfFunctionTemplateAsync(string tenantId, string templateId, UpdateFunctionOutputCommand command);

        Task<Result> RemoveOutputFromFunctionTemplateAsync(string tenantId, string templateId, RemoveCommand command);
        Task<Result<CreatedStatus>> CreateNotificationTemplateAsync(string tenantId, CreateNotificationTemplateCommand command);
        Task<Result> UpdateNotificationTemplateAsync(string tenantId, string templateId, UpdateNotificationTemplateCommand command);
        Task<Result> DeleteTemplateAsync(string tenantId, string templateId, DeleteTemplateCommand command);

        Task<Result<IEnumerable<Output>>> RenderFunctionTemplateAsync(string tenantId, string templateId, RenderParameters renderParameters);

        Task<Result> CreateSmsTemplateAsync(string tenantId, CreateSmsTemplateCommand command);
        Task<Result> UpdateSmsTemplateAsync(string tenantId, string templateId, UpdateSmsTemplateCommand command);

        Task<IEnumerable<TemplateRelationship>> GetTemplateRelationshipsAsync(string tenantId, TemplateRelationshipWhere where);
        Task<Result<CreatedStatus>> CreateTemplateRelationshipAsync(string tenantId, CreateTemplateRelationshipCommand command);
        Task<Result> DeleteTemplateRelationshipAsync(string tenantId, string templateRelationshipId, DeleteCommand command);

        Task<Result<CreatedStatus>> CreateClauseHtmlTemplateAsync(string tenantId, CreateClauseHtmlTemplateCommand command);
        Task<Result> UpdateClauseHtmlTemplateAsync(string tenantId, string templateId, UpdateClauseHtmlTemplateCommand command);

        Task<Result<string>> GetJobStatusAsync(string jobId);
    }

    public class RenderSmsCommand
    {
        public string BodyLiquid { get; set; }
        public RenderParameters RenderParameters { get; set; }
    }

    public class EmailRendered
    {
        public string Html { get; set; }
        public IEnumerable<EmailAttachment> Attachments { get; set; } = Enumerable.Empty<EmailAttachment>();
    }

    public class SmsRendered
    {
        public string Body { get; set; }
    }

    public class RenderParameters
    {
        public string Name { get; set; }
        public string Html { get; set; }
        public string ContentJsonString { get; set; }
        public JObject Content { get; set; }
        public string VariablesJsonString { get; set; }

        public string AccessToken { get; set; }
        public JObject Variables { get; set; }
        public StatisticsMetadata StatisticsMetadata { get; set; }

        public List<AddEmailAttachmentTemplateCommand> OverrideAttachmentTemplates { get; set; }
        public List<AddEmailAttachmentReferenceCommand> OverrideAttachmentReferences { get; set; }
    }

    public class StatisticsMetadata
    {
        public string Vendor { get; set; }
        public string ProductCode { get; set; }
    }

    public class HtmlTemplateRenderFromInstanceCommand
    {
        public RenderParameters RenderParameters { get; set; }
        public Template Template { get; set; }
    }

    public class EmailAttachment
    {
        public string FileName { get; set; }
        public byte[] Bytes { get; set; }
    }

    public class Template : SystemObject
    {
        public string Id { get; set; }
        public string LogicalId { get; set; }
        public string Type { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string GraphQlQuery { get; set; }

        public string Mjml { get; set; } // only for email
        public string Subject { get; set; } // only for email
        public IEnumerable<AttachmentReference> AttachmentReferences { get; set; } // only for email 
        public IEnumerable<AttachmentTemplate> AttachmentTemplates { get; set; } // only for email

        public string BodyLiquid { get; set; } // only for sms

        public string FileKey { get; set; } // only for pdf drawing
        public Drawing[] Drawings { get; set; } = Array.Empty<Drawing>(); // only for pdf drawing
        public string Html { get; set; } // only for wkhtmltopdf, clauseHtml
        public int? Order { get; set; } // only for clauseHtml
        public string Status { get; set; } // only for clauseHtml
        public MarginSettings MarginSettings { get; set; } // only for wkhtmltopdf
        public Orientation? Orientation { get; set; } // only for wkhtmltopdf
        public HeaderFooterSettings HeaderSettings { get; set; } // only for wkhtmltopdf
        public HeaderFooterSettings FooterSettings { get; set; } // only for wkhtmltopdf
        public List<PageObject> PageObjects { get; set; } // only for wkhtmltopdf

        public IEnumerable<DynamicValue> DynamicValues { get; set; } // only for dynamic

        public Engine Engine { get; set; }
        public IEnumerable<Input> Inputs { get; set; } //only for excelTemplate
        public IEnumerable<Output> Outputs { get; set; } //only for excelTemplate

        public string Password { get; set; }
        public string PasswordLiquid { get; set; }

        public IEnumerable<string> Tags { get; set; }

        public IEnumerable<EventLog> Events { get; set; }
    }

    public class DynamicValue
    {
        public string ValueId { get; set; }
        public JToken Value { get; set; }
    }
    public class CreateWkhtmltopdfTemplateCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string GraphQlQuery { get; set; }
        public string Html { get; set; }
        public Orientation? Orientation { get; set; }
        public MarginSettings MarginSettings { get; set; }
        public HeaderFooterSettings HeaderSettings { get; set; }
        public HeaderFooterSettings FooterSettings { get; set; }
        public string Password { get; set; }
        public string PasswordLiquid { get; set; }
        public string LogicalId { get; set; }
        public string CreatedById { get; set; }
    }

    public enum Orientation
    {
        Landscape,
        Portrait,
    }

    public class PageObject
    {
        public string Id { get; set; }
        public string Html { get; set; }
        public HeaderFooterSettings HeaderSettings { get; set; }
        public HeaderFooterSettings FooterSettings { get; set; }
    }

    public class UpdateWkhtmltopdfTemplateCommand
    {
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string GraphQlQuery { get; set; }
        public bool IsGraphQlQueryChanged { get; set; }
        public string Html { get; set; }
        public bool IsHtmlChanged { get; set; }
        
        public Orientation? Orientation { get; set; }
        public bool IsOrientationChanged { get; set; }
        public MarginSettings MarginSettings { get; set; }
        public bool IsMarginSettingsChanged { get; set; }
        public HeaderFooterSettings HeaderSettings { get; set; }
        public bool IsHeaderSettingsChanged { get; set; }
        public HeaderFooterSettings FooterSettings { get; set; }
        public bool IsFooterSettingsChanged { get; set; }
        public string Password { get; set; }
        public bool IsPasswordChanged { get; set; }
        public string PasswordLiquid { get; set; }
        public bool IsPasswordLiquidChanged { get; set; }
        public string LogicalId { get; set; }
        public bool IsLogicalIdChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AddPageObjectCommand
    {
        public string Html { get; set; }
        public bool IsHtmlChanged { get; set; }
        public HeaderFooterSettings HeaderSettings { get; set; }
        public HeaderFooterSettings FooterSettings { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdatePageObjectCommand
    {
        public string Id { get; set; }
        public string Html { get; set; }
        public bool IsHtmlChanged { get; set; }
        public HeaderFooterSettings HeaderSettings { get; set; }
        public bool IsHeaderSettingsChanged { get; set; }
        public HeaderFooterSettings FooterSettings { get; set; }
        public bool IsFooterSettingsChanged { get; set; }


        public string ModifiedById { get; set; }
    }

    public class RemovePageObjectCommand
    {
        public string Id { get; set; }
        public string RemovedById { get; set; }
    }

    public class CreateClauseHtmlTemplateCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string GraphQlQuery { get; set; }
        public string Html { get; set; }
        public string Status { get; set; }
        public int? Order { get; set; }
        public string LogicalId { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateClauseHtmlTemplateCommand
    {
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string GraphQlQuery { get; set; }
        public bool IsGraphQlQueryChanged { get; set; }
        public string Html { get; set; }
        public bool IsHtmlChanged { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public int? Order { get; set; }
        public bool IsOrderChanged { get; set; }
        public string LogicalId { get; set; }
        public bool IsLogicalIdChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AttachmentTemplate
    {
        public string AttachmentTemplateId { get; set; }
        public string FileName { get; set; }
        public string TemplateId { get; set; }
    }

    public class AttachmentReference
    {
        public string Id { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
    }


    public class Drawing
    {
        public string DrawingId { get; set; }
        public int Page { get; set; }
        public Font Font { get; set; }
        public TextAlignment? TextAlignment { get; set; }
        public VerticalAlignment? VerticalAlignment { get; set; }
        public Point Point1 { get; set; }
        public Point Point2 { get; set; }
        public string LiquidSource { get; set; }
    }

    public class Font
    {
        public string FamilyName { get; set; }
        public int EmSize { get; set; }
        public XFontStyle FontStyle { get; set; }
    }

    public enum XFontStyle
    {
        Regular = 0,
        Bold = 1,
        Italic = 2,
        BoldItalic = 3,
        Underline = 4,
        Strikeout = 8
    }

    public enum VerticalAlignment
    {
        Top,
        Center,
        Bottom
    }

    public enum TextAlignment
    {
        Left,
        Center,
        Right
    }

    public class Point
    {
        public double X { get; set; }
        public double Y { get; set; }
    }

    public class CreatePdfDrawingTemplateCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string FileKey { get; set; }
        public string GraphQlQuery { get; set; }
        public string Password { get; set; }
        public string PasswordLiquid { get; set; }
        public string LogicalId { get; set; }
        public string CreatedById { get; set; }
    }

    public class CreateEmailMjmlTemplateCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Subject { get; set; }
        public string Mjml { get; set; }
        public string GraphQlQuery { get; set; }
        public string LogicalId { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdatePdfDrawingTemplateCommand
    {
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string FileKey { get; set; }
        public bool IsFileKeyChanged { get; set; }
        public string GraphQlQuery { get; set; }
        public bool IsGraphQlQueryChanged { get; set; }
        public string Password { get; set; }
        public bool IsPassordChanged { get; set; }
        public string PasswordLiquid { get; set; }
        public bool IsPasswordLiquidChanged { get; set; }
        public string LogicalId { get; set; }
        public bool IsLogicalIdChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class UpdateEmailMjmlTemplateCommand
    {
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string Subject { get; set; }
        public bool IsSubjectChanged { get; set; }
        public string Mjml { get; set; }
        public bool IsMjMlChanged { get; set; }
        public string GraphQlQuery { get; set; }
        public bool IsGraphQlQueryChanged { get; set; }
        public string LogicalId { get; set; }
        public bool IsLogicalIdChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class CreateSmsTemplateCommand
    {
        public string Name { get; set; }
        public string BodyLiquid { get; set; }
        public string Description { get; set; }
        public string GraphQlQuery { get; set; }
        public string LogicalId { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateSmsTemplateCommand
    {
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string BodyLiquid { get; set; }
        public bool IsBodyLiquidChanged { get; set; }
        public string GraphQlQuery { get; set; }
        public bool IsGraphQlQueryChanged { get; set; }
        public string LogicalId { get; set; }
        public bool IsLogicalIdChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AddEmailAttachmentTemplateCommand
    {
        public string FileName { get; set; }
        public string TemplateId { get; set; }

        public string AddedById { get; set; }
    }

    public class UpdateEmailAttachmentTemplateCommand
    {
        public string AttachmentTemplateId { get; set; }
        public string FileName { get; set; }
        public bool IsFileNameChanged { get; set; }

        public string ModifiedById { get; set; }
    }

    public class RemoveEmailAttachmentTemplateCommand
    {
        public string AttachmentTemplateId { get; set; }

        public string RemovedById { get; set; }
    }

    public class AddEmailAttachmentReferenceCommand
    {
        public string Id { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }

        public string AddedById { get; set; }
    }

    public class UpdateEmailAttachmentReferenceCommand
    {
        public string Id { get; set; }
        public string FileName { get; set; }
        public bool IsFileNameChanged { get; set; }
        public string FilePath { get; set; }
        public bool IsFilePathChanged { get; set; }

        public string ModifiedById { get; set; }
    }

    public class CreateFunctionTemplateCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string FileKey { get; set; }
        public string GraphQlQuery { get; set; }
        public Engine Engine { get; set; }
        public IEnumerable<Input> Inputs { get; set; }
        public IEnumerable<Output> Outputs { get; set; }
        public string LogicalId { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateFunctionTemplateCommand
    {
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string FileKey { get; set; }
        public bool IsFileKeyChanged { get; set; }
        public string GraphQlQuery { get; set; }
        public bool IsGraphQlQueryChanged { get; set; }
        public EngineToUpdate Engine { get; set; }
        public bool IsEngineChanged { get; set; }
        public string LogicalId { get; set; }
        public bool IsLogicalIdChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AddFunctionInputCommand
    {
        public string Id { get; set; }
        public string Type { get; set; } //might be needed to cast before evaluation
        public string Name { get; set; }
        public string Liquid { get; set; } //check if hardcoded value works here, it should
        public string Var { get; set; } //for excel, this is cell address mapping
        public string AddedById { get; set; }
    }

    public class UpdateFunctionInputCommand
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Liquid { get; set; } //check if hardcoded value works here, it should
        public bool IsLiquidChanged { get; set; }
        public string Var { get; set; } //for excel, this is cell address mapping/named range
        public bool IsVarChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AddFunctionOutputCommand
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Var { get; set; } //for excel, this is cell address mapping
        public string AddedById { get; set; }
    }

    public class UpdateFunctionOutputCommand
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Var { get; set; } //for excel, this is cell address mapping
        public bool IsVarChanged { get; set; }
        public string ModifiedById { get; set; }
    }
    public class EngineToUpdate
    {
        public string Id { get; set; } //maybe not needed
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string RunTime { get; set; } //is only 'excelTemplate' for now
        public bool IsRunTimeChanged { get; set; }
        public string Version { get; set; } //maybe not needed as these can be on the template
        public bool IsVersionChanged { get; set; }
        public string TemplateId { get; set; } //only for excelTemplate
        public bool IsTemplateIdChanged { get; set; }
    }

    public class DeleteTemplateCommand
    {
        public string DeletedById { get; set; }
    }

    public class AddPdfDrawingCommand
    {
        public int Page { get; set; }
        public Font Font { get; set; }
        public TextAlignment? TextAlignment { get; set; }
        public VerticalAlignment? VerticalAlignment { get; set; }
        public Point Point1 { get; set; }
        public Point Point2 { get; set; }
        public string LiquidSource { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdatePdfDrawingCommand
    {
        public string DrawingId { get; set; }
        public int Page { get; set; }
        public bool IsPageChanged { get; set; }
        public Font Font { get; set; }
        public bool IsFontChanged { get; set; }
        public TextAlignment? TextAlignment { get; set; }
        public bool IsTextAlignmentChanged { get; set; }
        public VerticalAlignment? VerticalAlignment { get; set; }
        public bool IsVerticalAlignmentChanged { get; set; }
        public Point Point1 { get; set; }
        public bool IsPoint1Changed { get; set; }
        public Point Point2 { get; set; }
        public bool IsPoint2Changed { get; set; }
        public string LiquidSource { get; set; }
        public bool IsLiquidSourceChanged { get; set; }

        public string ModifiedById { get; set; }
    }

    public class RemovePdfDrawingCommand
    {
        public string DrawingId { get; set; }

        public string RemovedById { get; set; }
    }

    public class UpdateDynamicTemplateCommand
    {
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public List<string> Tags { get; set; }
        public bool IsTagsChanged { get; set; }
        public string LogicalId { get; set; }
        public bool IsLogicalIdChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AddDynamicValueCommand
    {
        public JToken Value { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateDynamicValueCommand
    {
        public string ValueId { get; set; }
        public JToken Value { get; set; }
        public bool IsValueChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveDynamicValueCommand
    {
        public string ValueId { get; set; }

        public string RemovedById { get; set; }
    }

    public class CreateDynamicTemplateCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public List<string> Tags { get; set; }
        public string LogicalId { get; set; }
        public string CreatedById { get; set; }
    }

    public class CreateNotificationTemplateCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string LogicalId { get; set; }
        public string CreatedById { get; set; }
    }
    public class UpdateNotificationTemplateCommand
    {
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string LogicalId { get; set; }
        public bool IsLogicalIdChanged { get; set; }
        public string ModifiedById { get; set; }
    }


    public class TemplateWhere : Where
    {
        public List<TemplateWhere> Or { get; set; }
        public List<TemplateWhere> And { get; set; }

        public string Id { get; set; }
        public IEnumerable<string> Id_in { get; set; }
        public string LogicalId { get; set; }
        public IEnumerable<string> LogicalId_in { get; set; }
        public string Name { get; set; }
        public string Name_contains { get; set; }
        public string Status { get; set; }
        public string Status_in { get; set; }
        public string Status_contains { get; set; }
        public string Type { get; set; }
        public string Type_in { get; set; }
        public string Tags_contains { get; set; }
        public string Description_contains { get; set; }
        public bool? Has_LogicalId { get; set; }
        public string LogicalId_contains { get; set; }
    }
    public class RenderEmailMjmlCommand
    {
        public string EmailMjml { get; set; }
        public RenderParameters RenderParameters { get; set; }
    }

    public class RenderHtmlCommand
    {
        public string Html { get; set; }
        public RenderParameters RenderParameters { get; set; }
    }

    public class RenderWkhtmltopdfCommand
    {
        public string Html { get; set; }
        public Orientation? Orientation { get; set; }
        public MarginSettings MarginSettings { get; set; }
        public HeaderFooterSettings HeaderSettings { get; set; }
        public HeaderFooterSettings FooterSettings { get; set; }
        public List<PageObject> PageObjects { get; set; }
        public RenderParameters RenderParameters { get; set; }
        public string Password { get; set; }

    }
}
