﻿using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Domain.Templates
{
    public class TemplateRelationship : SystemObject
    {
        public string Id { get; set; }
        public Template Template { get; set; }
        public string Action { get; set; }
    }

    public class CreateTemplateRelationshipCommand
    {
        public string TemplateId { get; set; }
        public string Action { get; set; }

        public string CreatedById { get; set; }
    }

    public class TemplateRelationshipWhere : Where
    {
        public List<TemplateRelationshipWhere> Or { get; set; }
        public List<TemplateRelationshipWhere> And { get; set; }

        public string Id { get; set; }
        public IEnumerable<string> Id_in { get; set; }
        public string Action { get; set; }
    }
}