﻿using System.Collections.Generic;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Domain.Products
{
    public class UiSchema : SystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public JToken Schema { get; set; }
        public UiSchemaStandard Standard { get; set; }
    }

    public class CreateUiSchemaCommand
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Schema { get; set; }
        public UiSchemaStandard Standard { get; set; }
        public string CreatedById { get; set; }
    }

    public class UiSchemaStandard
    {
        public UiSchemaStandardTypeEnum Type { get; set; }
        public string Version { get; set; }
    }

    public enum UiSchemaStandardTypeEnum
    {
        JSON_SCHEMA = 0,
        GRAPHQL = 1
    }

    public class UiSchemaWhere : Where
    {
        public IReadOnlyCollection<UiSchemaWhere> Or { get; set; }
        public IReadOnlyCollection<UiSchemaWhere> And { get; set; }
        public string Id { get; set; }
        public string Name { get; set; }
        public FieldsWhere Schema { get; set; }
        public IReadOnlyCollection<string> Name_in { get; set; }
        public IReadOnlyCollection<string> Id_in { get; set; }
    }

    public class UpdateUiSchemaCommand
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Schema { get; set; }
        public UiSchemaStandard Standard { get; set; }
        public string ModifiedById { get; set; }
    }
}