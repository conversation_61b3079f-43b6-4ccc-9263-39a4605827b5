﻿using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Domain.Products
{
    public interface IInsurerService
    {
        Task<IEnumerable<Insurer>> GetInsurersAsync(string tenantId, string clientId, InsurerFilter filter);
        Task<ILookup<string, Insurer>> GetInsurersLookupAsync(string tenantId, string clientId, InsurerFilter filters);
        Task<IDictionary<string, Insurer>> GetInsurersDictionaryAsync(string tenantId, string clientId, InsurerFilter filter);

        Task<Result> CreateInsurerAsync(string tenantId, CreateInsurerCommand command);
        Task<Result> DeleteInsurerAsync(string tenantId, string id, string deletedById);
        Task<Result> MigrateInsurersAsync(string tenantId, MigrateInsurersCommand command);
    }

    public class Insurer : SystemObject
    {
        public string Id { get; set; }
        public InsurerLogoUrls LogoUrls { get; set; }
        public string TenantId { get; set; }
    }

    public class InsurerLogoUrls
    {
        public string TypeA { get; set; }
        public string TypeB { get; set; }
        public string TypeC { get; set; }
        public string TypeD { get; set; }
        public string TypeE { get; set; }
    }

    public class InsurerFilter
    {
        public IEnumerable<string> Ids { get; set; }
        public IEnumerable<string> ProductTypes { get; set; }
    }

    public class CreateInsurerCommand
    {
        public string Id { get; set; }
        public InsurerLogoUrls LogoUrls { get; set; }
        public string CreatedById { get; set; }
    }

    public class MigrateInsurersCommand
    {
        public IEnumerable<CreateInsurerCommand> InsurerInputs { get; set; }
    }
}
