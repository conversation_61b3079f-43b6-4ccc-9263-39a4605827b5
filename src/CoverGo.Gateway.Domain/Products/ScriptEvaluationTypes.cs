﻿using System;
using System.Collections.Generic;

namespace CoverGo.Gateway.Domain.Products
{
    public class EvaluateScriptCommand
    {
        public Script Script { get; set; }
        public string Representation { get; set; }
        public string DataInput { get; set; }
        public List<DiscountCode> DiscountCodes { get; set; }
        public string Fields { get; set; }
        public string RatingFactorsTable { get; set; }
        public string Segments { get; set; }
    }

    public class EvaluateProductScriptCommand
    {
        public ProductId ProductId { get; set; }
        public ScriptTypeEnum ScriptType { get; set; }
        public string DataInput { get; set; }
        public string Fields { get; set; }
        public string RatingFactorsTable { get; set; }
        public string Segments { get; set; }

        public string? DistributorID { get; set; }
        public string[]? CampaignCodes { get; set; }
        public DateTimeOffset? StartDate { get; set; }
        public bool IsRenewal { get; set; }
    }
}
