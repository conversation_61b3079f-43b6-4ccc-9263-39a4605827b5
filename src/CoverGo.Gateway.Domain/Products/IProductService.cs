﻿using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Domain.Products
{
    public interface IProductService
    {
        Task<Result> InitializeTenantAsync(string tenantId, InitializeTenantProductsCommand command);
        Task<Result> MigrateProductsAsync(string tenantId, MigrateProductsCommand command);
        Task<Result> CleanProductTest(string tenantId, string typeId);
        Task<IEnumerable<ValidateResult>> ValidateProductsAsync(string tenantId, string clientId, ProductQuery query);
        Task<IEnumerable<Product2>> GetAsync(string tenantId, string clientId, ProductQuery query);
        Task<long> GetTotalCountAsync(string tenantId, string clientId, ProductWhere where);
        Task<IDictionary<ProductId, Product2>> GetDictionaryAsync(string tenantId, string clientId, ProductWhere where, bool loadRepresentation);
        Task<List<DetailedEventLog>> GetEventsAsync(string tenantId, Proxies.Product.EventQuery query);

        Task<Result> CreateAsync(string tenantId, CreateProductCommand command);
        Task<Result> UpdateAsync(string tenantId, UpdateProductCommand command);
        Task<Result> CloneAsync(string tenantId, CloneProductCommand command);
        Task<Result> DeleteAsync(string tenantId, DeleteProductCommand command);

        Task<IEnumerable<Script>> GetScriptsAsync(string tenantId, ScriptWhere where);
        Task<Result<CreatedStatus>> CreateScriptAsync(string tenantId, CreateScriptCommand command);
        Task<Result> UpdateScriptAsync(string tenantId, UpdateScriptCommand command);
        Task<Result> DeleteScriptAsync(string tenantId, DeleteCommand command);

        Task<Result> AddBenefitAsync(string tenantId, ProductId productId, AddBenefitCommand command);
        Task<Result> UpdateBenefitAsync(string tenantId, ProductId productId, UpdateBenefitCommand command);
        Task<Result> BenefitBatchAsync(string tenantId, ProductId productId, BenefitCommandBatch command);
        Task<Result> RemoveBenefitAsync(string tenantId, ProductId productId, RemoveBenefitCommand typeId);
        Task<Result> AddScriptToProductAsync(string tenantId, AddScriptToProductCommand command);
        Task<Result> RemoveScriptFromProductAsync(string tenantId, RemoveScriptFromProductCommand command);
        Task<Result> AddTemplateRelationshipToProductAsync(string tenantId, AddTemplateRelationshipToProductCommand command);
        Task<Result> RemoveTemplateRelationshipFromProductAsync(string tenantId, RemoveTemplateRelationshipFromProductCommand command);

        Task<Result<CreatedStatus>> AddTagAsync(string tenantId, ProductId productId, AddTagCommand command);
        Task<Result> RemoveTagAsync(string tenantId, ProductId productId, RemoveCommand typeId);

        Task<Result<CreatedStatus>> AddFactAsync(string tenantId, ProductId productId, AddFactCommand command);
        Task<Result> UpdateFactAsync(string tenantId, ProductId productId, UpdateFactCommand command);
        Task<Result> RemoveFactAsync(string tenantId, ProductId productId, RemoveCommand typeId);
        Task<Result> ProductFactBatchAsync(string tenantId, ProductId productId, FactCommandBatch command);

        Task<Result<CreatedStatus>> AddInternalReviewAsync(string tenantId, ProductId productId, AddInternalReviewCommand command);
        Task<Result> UpdateInternalReviewAsync(string tenantId, ProductId productId, UpdateInternalReviewCommand command);
        Task<Result> RemoveInternalReviewAsync(string tenantId, ProductId productId, RemoveCommand typeId);

        Task<Dictionary<string, Dictionary<string, List<string>>>> GetBenefitInfosAsync(string tenantId, string clientId);
        Task<Dictionary<string, List<string>>> GetBenefitCategoriesAsync(string tenantId, string clientId);

        Task<IEnumerable<ProductType>> GetTypesAsync(string tenantId, string clientId, ProductTypeWhere where);
        Task<IDictionary<string, ProductType>> GetTypeDictionaryAsync(string tenantId, string clientId, ProductTypeWhere where);
        Task<Result> CreateTypeAsync(string tenantId, CreateProductTypeCommand command);
        Task<Result> DeleteTypeAsync(string tenantId, string typeId, string deletedById);
        Task<Result> AddDataSchemaToProductTypeAsync(string tenantId, AddDataSchemaToProductTypeCommand command);
        Task<Result> RemoveDataSchemaFromProductTypeAsync(string tenantId, RemoveDataSchemaFromProductTypeCommand command);

        Task<Result> AddUnderwritingVariableAsync(string tenantId, ProductId productId, AddUnderwritingVariableCommand command);
        Task<Result> UpdateUnderwritingVariableAsync(string tenantId, ProductId productId, UpdateUnderwritingVariableCommand command);
        Task<Result> RemoveUnderwritingVariableAsync(string tenantId, ProductId productId, RemoveCommand command);

        Task<IEnumerable<ProductConfig>> GetProductConfigsAsync(string tenantId, QueryArguments queryArguments);
        Task<Result<CreatedStatus>> CreateConfigAsync(string tenantId, CreateProductConfigCommand command);
        Task<Result> UpdateConfigAsync(string tenantId, string id, UpdateProductConfigCommand config);
        Task<Result> DeleteConfigAsync(string tenantId, string id, DeleteCommand command);

        Task<IEnumerable<Illustration>> GetIllustrationsAsync(string tenantId, ProductQuery productQuery);

        Task<IEnumerable<ProductUnderwritingJsonLogicRules>> GetUnderwrittingJsonLogicRulesAsync(string tenantId, ProductWhere where);

        Task<IEnumerable<ProductUnderwritingJsonSchema>> GetUnderwrittingJsonSchemasAsync(string tenantId, ProductUnderwritingJsonSchemaQuery where);

        Task<IEnumerable<BenefitDefinition>> GetBenefitDefinitionsAsync(string tenantId, QueryArguments queryArguments);
        Task<long> CountBenefitDefinitionsAsync(string tenantId, QueryArguments queryArguments);
        Task<Result<CreatedStatus>> CreateBenefitDefinitionAsync(string tenantId, CreateBenefitDefinitionCommand command);
        Task<Result> UpdateBenefitDefinitionAsync(string tenantId, UpdateBenefitDefinitionCommand command);
        Task<Result> DeleteBenefitDefinitionAsync(string tenantId, string benefitDefinitionId, DeleteCommand command);
        Task<Result> BatchBenefitDefinitionAsync(string tenantId, BatchBenefitDefinitionCommand command);

        Task<IEnumerable<BenefitDefinitionType>> GetBenefitDefinitionTypesAsync(string tenantId, QueryArguments queryArguments);
        Task<Result<CreatedStatus>> CreateBenefitDefinitionTypeAsync(string tenantId, CreateBenefitDefinitionTypeCommand command);
        Task<Result> UpdateBenefitDefinitionTypeAsync(string tenantId, UpdateBenefitDefinitionTypeCommand command);
        Task<Result> DeleteBenefitDefinitionTypeAsync(string tenantId, string benefitDefinitionTypeId, DeleteCommand command);
        public Task<Result> BatchBenefitDefinitionTypeAsync(string tenantId, BatchBenefitDefinitionTypeCommand command);

        Task<Result<string>> Evaluate(string tenantId, EvaluateScriptCommand command);
        Task<Result<string>> Evaluate(string tenantId, string clientId, EvaluateProductScriptCommand command);
        Task<long> GetJacketsTotalCountAsync(string tenantId, JacketWhere where);
        Task<IEnumerable<Jacket>> GetJacketsAsync(string tenantId, QueryArguments queryArguments);
        Task<Result<string>> CreateJacketAsync(string tenantId, CreateJacketCommand command);
        Task<Result> UpdateJacketAsync(string tenantId, string jacketId, UpdateJacketCommand command);
        Task<Result> DeleteJacketAsync(string tenantId, string jacketId, DeleteCommand command);
        Task<Result<CreatedStatus>> CreateUiSchemaAsync(string tenantId, CreateUiSchemaCommand command);
        Task<IEnumerable<UiSchema>> GetUiSchemasAsync(string tenantId, UiSchemaWhere where);
        Task<Result> UpdateUiSchemaAsync(string tenantId, UpdateUiSchemaCommand command);
        Task<Result> DeleteUiSchemaAsync(string tenantId, string uiSchemaId, DeleteCommand command);
    }
}
