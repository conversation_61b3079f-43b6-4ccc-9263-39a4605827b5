﻿namespace CoverGo.Gateway.Domain.Products
{
    public class InternalReview : SystemObject
    {
        public string Id { get; set; }
        public string Status { get; set; }
        public string Comment { get; set; }
    }

    public class AddInternalReviewCommand
    {
        public string Id { get; set; }
        public string Status { get; set; }
        public string Comment { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateInternalReviewCommand
    {
        public string Id { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public string Comment { get; set; }
        public bool IsCommentChanged { get; set; }
        public string ModifiedById { get; set; }
    }
}
