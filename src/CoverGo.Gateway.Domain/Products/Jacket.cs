using System.Collections.Generic;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Templates;

namespace CoverGo.Gateway.Domain.Products
{
    public class Jacket : SystemObject
    {
        public string Id { get; set; }

        public string Title { get; set; }

        public string Status { get; set; }

        public List<Clause> Clauses { get; set; }
    }

    public class JacketInstance
    {
        public string Id { get; set; }
        public Jacket Jacket { get; set; }
        public string JacketId { get; set; }
        public int Order { get; set; }
    }

    public class JacketWhere : Where
    {
        public List<JacketWhere> Or { get; set; }

        public List<JacketWhere> And { get; set; }

        public string Id { get; set; }

        public IEnumerable<string> Id_in { get; set; }


        public string Title { get; set; }

        public string Title_contains { get; set; }

        public string Status { get; set; }

        public string Status_contains { get; set; }

        public IEnumerable<string> Status_in { get; set; }
    }

    public class CreateJacketCommand
    {
        public string Title { get; set; }

        public string Status { get; set; }

        public List<Clause> Clauses { get; set; }

        public string CreatedById { get; set; }
    }

    public class UpdateJacketCommand
    {
        public bool IsTitleChanged { get; set; }

        public string Title { get; set; }

        public bool IsStatusChanged { get; set; }

        public string Status { get; set; }

        public bool IsClausesChanged { get; set; }

        public List<Clause> Clauses { get; set; }

        public string ModifiedById { get; set; }
    }

    public class AddClauseToOfferCommand
    {
        public string Id { get; set; }
        public int Order { get; set; }
        public string TemplateId { get; set; }
        public RenderParameters RenderParameters { get; set; }
        public string HtmlOverride { get; set; }
    }
}