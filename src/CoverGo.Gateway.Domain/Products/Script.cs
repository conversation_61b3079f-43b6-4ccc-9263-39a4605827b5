﻿using System.Collections.Generic;

namespace CoverGo.Gateway.Domain.Products
{
    public class Script : SystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string InputSchema { get; set; }
        public string OutputSchema { get; set; }
        public string SourceCode { get; set; }
        public string ReferenceSourceCodeUrl { get; set; }
        public string ReferenceSourceCode { get; set; }
        public string ExternalTableDataUrl { get; set; }
        public List<string> ExternalTableDataUrls { get; set; }
        public ScriptTypeEnum? Type { get; set; }
    }

    public class CreateScriptCommand
    {
        public string Name { get; set; }
        public string InputSchema { get; set; }
        public string OutputSchema { get; set; }
        public string SourceCode { get; set; }
        public string ReferenceSourceCodeUrl { get; set; }
        public string ExternalTableDataUrl { get; set; }
        public List<string> ExternalTableDataUrls { get; set; }
        public ScriptTypeEnum? Type { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateScriptCommand
    {
        public string ScriptId { get; set; }
        public string Name { get; set; }
        public string InputSchema { get; set; }
        public string OutputSchema { get; set; }
        public string SourceCode { get; set; }
        public string ReferenceSourceCodeUrl { get; set; }
        public string ExternalTableDataUrl { get; set; }
        public List<string> ExternalTableDataUrls { get; set; }
        public ScriptTypeEnum? Type { get; set; }
        public string ModifiedById { get; set; }
    }

    public class ScriptWhere
    {
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public IEnumerable<ScriptWhere> Or { get; set; }
        public IEnumerable<ScriptWhere> And { get; set; }
    }

    public enum ScriptTypeEnum
    {
        Pricing = 1,
        Underwriting = 2,
        Cancellation = 3,
        Claim = 4,
        Pricing_standard = 5,
        Underwriting_standard = 6
    }
}
