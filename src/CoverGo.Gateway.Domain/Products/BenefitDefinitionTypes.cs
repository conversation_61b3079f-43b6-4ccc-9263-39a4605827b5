﻿using System.Collections.Generic;
using CoverGo.DomainUtils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Domain.Products
{
    public class BenefitDefinition
    {
        public string Id { get; set; }
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public JToken Fields { get; set; }
        public List<string> BenefitDefinitionTypeIds { get; set; }
    }

    public class CreateBenefitDefinitionCommand
    {
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Fields { get; set; }
        public IReadOnlyCollection<string> BenefitDefinitionTypeIds { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateBenefitDefinitionCommand
    {
        public string BenefitDefinitionId { get; set; }
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Fields { get; set; }
        public IReadOnlyCollection<string> BenefitDefinitionTypeIds { get; set; }
        public string ModifiedById { get; set; }
    }
    
    public class BatchBenefitDefinitionCommand
    {
        public List<CreateBenefitDefinitionCommand> CreateBenefitDefinitionCommands { get; set; }
        public List<UpdateBenefitDefinitionCommand> UpdateBenefitDefinitionCommands { get; set; }
    }

    public class BenefitDefinitionType
    {
        public string Id { get; set; }
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public JToken Fields { get; set; }
    }
    
    public class BenefitDefinitionTypeGraph
    {
        public string Id { get; set; }
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Fields { get; set; }
        
        public static BenefitDefinitionTypeGraph ToGraph(BenefitDefinitionType domain) => 
            domain == null ? null 
                : new BenefitDefinitionTypeGraph()
                {
                    Id = domain.Id,
                    BusinessId = domain.BusinessId,
                    Name = domain.Name,
                    Description =domain. Description,
                    Status = domain.Status,
                    Fields = domain.Fields?.ToString(Formatting.None)
                };
    }

    public class CreateBenefitDefinitionTypeCommand
    {
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Fields { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateBenefitDefinitionTypeCommand
    {
        public string BenefitDefinitionTypeId { get; set; }
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Fields { get; set; }
        public string ModifiedById { get; set; }
    }
    
    public class BatchBenefitDefinitionTypeCommand
    {
        public List<CreateBenefitDefinitionTypeCommand> CreateBenefitDefinitionTypeCommands { get; set; }
        public List<UpdateBenefitDefinitionTypeCommand> UpdateBenefitDefinitionTypeCommands { get; set; }
    }

    public class BenefitDefinitionWhere : Where
    {
        public IEnumerable<BenefitDefinitionWhere> Or { get; set; }
        public IEnumerable<BenefitDefinitionWhere> And { get; set; }
        public BenefitDefinitionTypeWhere Type { get; set; }
        public List<string> Id_in { get; set; }
        public List<string> BusinessId_in { get; set; }
        public string Status { get; set; }
        public string Name_contains { get; set; }
        public string BusinessId_contains { get; set; }
        public FieldsWhere Fields { get; set; }
    }

    public class BenefitDefinitionTypeWhere: Where
    {
        public IEnumerable<BenefitDefinitionTypeWhere> Or { get; set; }
        public IEnumerable<BenefitDefinitionTypeWhere> And { get; set; }
        public List<string> Id_in { get; set; }
        public List<string> BusinessId_in { get; set; }
        public string Name_contains { get; set; }
        public string BusinessId_contains { get; set; }
        public string Status { get; set; }
        public FieldsWhere Fields { get; set; }
    }
}