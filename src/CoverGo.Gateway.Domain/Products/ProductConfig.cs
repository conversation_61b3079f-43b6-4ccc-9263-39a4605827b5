﻿using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Domain.Products
{
    public class InitializeTenantProductsCommand
    {
        public IEnumerable<ProductConfig> ProductConfigs { get; set; }
    }

    public class ProductConfig : SystemObject
    {
        public string Id { get; set; }
        public string ClientId { get; set; }
        public Dictionary<string, IEnumerable<string>> DisplayedInsurers { get; set; }
        
        public IEnumerable<ProductId> DisplayedProductIds { get; set; }
        public IEnumerable<string> DisplayedTypes { get; set; }
        public Dictionary<string, IEnumerable<ProductId>> DisplayedProducts { get; set; }
        public Dictionary<string, Dictionary<string, IEnumerable<string>>> DisplayedBenefits { get; set; }
        public Meta Meta { get; set; }
    }

    public class Meta
    {
        public IEnumerable<EffectiveDayLimit> EffectiveDayLimits { get; set; }
    }

    public class EffectiveDayLimit
    {
        public ProductId ProductId { get; set; }
        public int Limit { get; set; }
    }

    public class ProductConfigWhere : Where
    {
        public IEnumerable<ProductConfigWhere> Or { get; set; }
        public IEnumerable<ProductConfigWhere> And { get; set; }
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string ClientId { get; set; }
    }

    public class CreateProductConfigCommand
    {
        public string ClientId { get; set; }
        public Dictionary<string, IEnumerable<string>> DisplayedInsurers { get; set; }
        public IEnumerable<string> DisplayedTypes { get; set; }
        public Dictionary<string, IEnumerable<ProductId>> DisplayedProducts { get; set; }
        public Dictionary<string, Dictionary<string, IEnumerable<string>>> DisplayedBenefits { get; set; }
        public Meta Meta { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateProductConfigCommand
    {
        public string ClientId { get; set; }
        public bool IsClientIdChanged { get; set; }
        public Dictionary<string, IEnumerable<string>> DisplayedInsurers { get; set; }
        public bool IsDisplayedInsurersChanged { get; set; }
        public IEnumerable<string> DisplayedTypes { get; set; }
        public bool IsDisplayedTypesChanged { get; set; }
        public Dictionary<string, IEnumerable<ProductId>> DisplayedProducts { get; set; }
        public bool IsDisplayedProductsChanged { get; set; }
        public Dictionary<string, Dictionary<string, List<string>>> DisplayedBenefits { get; set; }
        public bool IsDisplayedBenefitsChanged { get; set; }
        public MetaToUpdate Meta { get; set; }
        public bool IsMetaChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class MetaToUpdate
    {
        public IEnumerable<EffectiveDayLimit> EffectiveDayLimits { get; set; }
        public bool IsEffectiveDayLimitsChanged { get; set; }
    }
}
