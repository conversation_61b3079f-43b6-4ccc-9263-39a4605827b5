using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Users;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using UpdateTypes = CoverGo.Gateway.Domain.Products.ProductUpdateTypes;

namespace CoverGo.Gateway.Domain.Products
{
    public class Product
    {
        public string Id { get; set; }
        public string Version { get; set; }
        public string Type { get; set; }
        public string InsurerId { get; set; }
        public string IssuerProductId { get; set; }
        public string IssuerId { get; set; }
        public IEnumerable<Benefit> Benefits { get; set; }
    }

    public class Product2 : SystemObject
    {
        public ProductId Id { get; set; }
        public string TenantId { get; set; }
        public string InsurerId { get; set; }
        public string IssuerProductId { get; set; }
        public string ClaimFactTemplateId { get; set; }

        public IEnumerable<Benefit> Benefits { get; set; }
        public ProductSettings RejectionSettings { get; set; }
        public LoadingSettings LoadingSettings { get; set; }
        public ProductSettings ExclusionSettings { get; set; }
        public ClaimSettings ClaimSettings { get; set; }

        public Underwriting Underwriting { get; set; }
        public IEnumerable<Tag> Tags { get; set; }
        public IEnumerable<Fact> Facts { get; set; }
        public DateTime? LaunchPeriodStartDate { get; set; }
        public DateTime? LaunchPeriodEndDate { get; set; }
        public DateTime? ChangeEffectiveDate { get; set; }
        public string Status { get; set; }
        public IEnumerable<InternalReview> InternalReviews { get; set; }
        public string LifecycleStage { get; set; }
        public string Representation { get; set; }
        public string Fields { get; set; }

        public IReadOnlyCollection<string> ScriptIds { get; set; }

        public IReadOnlyCollection<string> TemplateRelationshipIds { get; set; }
        public string ProductTreeId { get; set; }
        public string TermsAndConditionsTemplateId { get; set; }
        public string TermsAndConditionsJacketId { get; set; }
        public string RatingFactorsTable { get; set; }
        public string Segments { get; set; }
        public PolicyIssuanceMethod? PolicyIssuanceMethod { get; set; }
        public TimeSpan? OfferValidityPeriod { get; set; }
        public bool? AllowCustomProduct { get; set; }
        public ProductUpdateTypes UpdateTypes { get; set; }
        public bool AutoRenewal { get; set; }
        public bool RenewalNotification { get; set; }
    }

    public class Benefit
    {
        public string TypeId { get; set; }
        public string ParentTypeId { get; set; }
        public List<string> ParentOptionKeys { get; set; }
        public JToken Value { get; set; }
        public bool IsValueInput { get; set; }
        public string ParentOptionKey { get; set; }
        public string OptionKey { get; set; }
        public bool IsOptional { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public IEnumerable<BenefitOption> Options { get; set; }

        public Condition Condition { get; set; }
    }

    public class Condition
    {
        public string Type { get; set; }
        public JObject JsonLogicRule { get; set; }
    }

    public class Underwriting
    {
        public string SourceType { get; set; }
        public List<UnderwritingVariable> Variables { get; set; }

        public JToken JsonLogicRules { get; set; } // only for `jsonLogic`

        public string ExcelPath { get; set; } // only for `excel`
        public ExcelRules ExcelRules { get; set; } // only for `excel`
    }

    public class ExcelRules
    {
        public ExcelCell ResultCell { get; set; }
    }

    public class ExcelCell
    {
        public int RowIndex { get; set; }
        public int ColumnIndex { get; set; }
        public CellType Type { get; set; } //ToDo: figure out how to display, maybe string?
    }

    public enum CellType
    {
        Unknown = -1,
        Numeric = 0,
        String = 1,
        Formula = 2,
        Blank = 3,
        Boolean = 4,
        Error = 5
    }

    public class UnderwritingVariable
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string JsonSchemaValidation { get; set; }
    }


    public class BenefitOption
    {
        public string Key { get; set; }
        public object Value { get; set; }
    }

    public class ProductFilter
    {
        public IEnumerable<string> Types { get; set; }
        public IEnumerable<ProductId> Ids { get; set; }
        public IEnumerable<string> InsurerIds { get; set; }
        public Dictionary<string, object> BenefitFilters { get; set; }
        public IEnumerable<int> Ages { get; set; }
        public int? NumberOfDays { get; set; }

        // travel
        public string InsuredType { get; set; }
        public IEnumerable<string> CountryCodes { get; set; }
        public DateTime? StartDate { get; set; }

        // motor
        public int Age { get; set; }
        public int YearsOfExperience { get; set; }
        public string Scope { get; set; }
        public MotorDetails ModelDetails { get; set; }
        public string TrimId { get; set; }
    }

    public class MotorDetails
    {
        public string Body { get; set; }
        public int EngineCapacity { get; set; }
        public int HorsePower { get; set; }
        public string EngineType { get; set; }
        public IEnumerable<int> Years { get; set; }
        public string ImageUrl { get; set; }
        public string Drive { get; set; }
        public string Gearbox { get; set; }
    }

    public class ProductId
    {
        public string Plan { get; set; }
        public string Version { get; set; }
        public string Type { get; set; }

        public override bool Equals(object obj) => obj is ProductId id &&
            Plan == id.Plan &&
            Version == id.Version &&
            Type == id.Type;

        public override int GetHashCode() => HashCode.Combine(Plan, Version, Type);

        public override string ToString() => $"{Plan}|{Version}|{Type}";

        public static bool operator ==(ProductId left, ProductId right) =>
           left?.Plan == right?.Plan &&
           left?.Type == right?.Type &&
           left?.Version == right?.Version;

        public static bool operator !=(ProductId left, ProductId right) => !(left == right);

        public static ProductId FromString(string idString)
        {
            try
            {
                string[] split = idString.Split('|');
                return new ProductId
                {
                    Plan = split[0],
                    Version = string.IsNullOrEmpty(split[1]) ? null : split[1],
                    Type = split[2]
                };
            }
            catch
            {
                return null;
            }
        }
    }

    public class CreateProductCommand
    {
        public ProductId ProductId { get; set; }
        public string InsurerId { get; set; }
        public string IssuerProductId { get; set; } //ToDo: used for chubb, maybe need to prevent others from using it
        public ProductSettings RejectionSettings { get; set; }
        public LoadingSettings LoadingSettings { get; set; }
        public ProductSettings ExclusionSettings { get; set; }
        public ClaimSettings ClaimSettings { get; set; }
        public string CreatedById { get; set; }
        public IEnumerable<AddBenefitCommand> BenefitInputs { get; set; }
        public Underwriting Underwriting { get; set; }
        public IEnumerable<Tag> Tags { get; set; }
        public IEnumerable<AddFactCommand> Facts { get; set; }
        public DateTime? LaunchPeriodStartDate { get; set; }
        public DateTime? LaunchPeriodEndDate { get; set; }
        public DateTime? ChangeEffectiveDate { get; set; }
        public string Status { get; set; }
        public string LifecycleStage { get; set; }
        public string Representation { get; set; }
        public string Fields { get; set; }
        public string ProductTreeId { get; set; }
        public PolicyIssuanceMethod? PolicyIssuanceMethod { get; set; }
        public TimeSpan? OfferValidityPeriod { get; set; }
        public bool? AllowCustomProduct { get; set; }
        public bool AutoRenewal { get; set; }
        public bool RenewalNotification { get; set; }
    }

    public class UpdateProductCommand
    {
        public ProductId ProductId { get; set; }
        public string InsurerId { get; set; }
        public bool IsInsurerIdChanged { get; set; }
        public ProductSettingsToUpdate RejectionSettings { get; set; }
        public bool IsRejectionSettingsChanged { get; set; }
        public LoadingSettingsToUpdate LoadingSettings { get; set; }
        public bool IsLoadingSettingsChanged { get; set; }
        public ProductSettingsToUpdate ExclusionSettings { get; set; }
        public bool IsExclusionSettingsChanged { get; set; }
        public ClaimSettingsToUpdate ClaimSettings { get; set; }
        public bool IsClaimSettingsChanged { get; set; }
        public UnderwritingToUpdate Underwriting { get; set; }
        public bool IsUnderwritingChanged { get; set; }
        public string ModifiedById { get; set; }
        public string LifecycleStage { get; set; }
        public bool IsLifecycleStageChanged { get; set; }
        public string Fields { get; set; }
        public string FieldsPatch { get; set; }
        public bool IsFieldsChanged { get; set; }
        public string Representation { get; set; }
        public string RepresentationPatch { get; set; }
        public bool IsRepresentationChanged { get; set; }
        public DateTime? LaunchPeriodStartDate { get; set; }
        public bool IsLaunchPeriodStartDateChanged { get; set; }
        public DateTime? LaunchPeriodEndDate { get; set; }
        public bool IsLaunchPeriodEndDateChanged { get; set; }
        public DateTime? ChangeEffectiveDate { get; set; }
        public bool IsChangeEffectiveDateChanged { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public string ProductTreeId { get; set; }
        public bool IsProductTreeIdChanged { get; set; }
        public PolicyIssuanceMethod? PolicyIssuanceMethod { get; set; }
        public bool IsPolicyIssuanceMethodChanged { get; set; }
        public bool IsOfferValidityPeriodChanged { get; set; }
        public TimeSpan? OfferValidityPeriod { get; set; }
        public bool? AllowCustomProduct { get; set; }
        public bool IsAllowCustomProductChanged { get; set; }
        public UpdateTypes UpdateTypes { get; set; }
        public bool IsUpdateTypesChanged { get; set; }
        public bool? AutoRenewal { get; set; }
        public bool? RenewalNotification { get; set; }
    }

    public class CloneProductCommand
    {
        public ProductId ProductId { get; set; }
        public ProductId CloneProductId { get; set; }
        public string IssuerProductId { get; set; }
        public string CreatedById { get; set; }
    }

    public class UnderwritingToUpdate
    {
        public bool IsSourceTypeChanged { get; set; }
        public string SourceType { get; set; }

        public bool IsJsonLogicRulesChanged { get; set; } // only for `jsonLogic`
        public JToken JsonLogicRules { get; set; } // only for `jsonLogic`

        public bool IsExcelPathChanged { get; set; } // only for `excel`
        public string ExcelPath { get; set; } // only for `excel`

        public bool IsExcelRulesChanged { get; set; } // only for `excel`
        public ExcelRules ExcelRules { get; set; } // only for `excel`
    }

    public class DeleteProductCommand
    {
        public ProductId ProductId { get; set; }
        public string DeletedById { get; set; }
    }

    public class AddBenefitCommand
    {
        public string TypeId { get; set; }
        public string ParentTypeId { get; set; }
        public List<string> ParentOptionKeys { get; set; }
        public string OptionKey { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public JToken Value { get; set; }
        public Condition Condition { get; set; }
        public bool IsValueInput { get; set; }

        public string AddedById { get; set; }
    }

    public class UpdateBenefitCommand
    {
        public string TypeId { get; set; }
        public string OptionKey { get; set; }
        public string ParentTypeId { get; set; }
        public bool IsParentTypeIdChanged { get; set; }
        public List<string> ParentOptionKeys { get; set; }
        public bool IsParentOptionKeysChanged { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public bool IsCurrencyCodeChanged { get; set; }
        public JToken Value { get; set; }
        public bool IsValueChanged { get; set; }
        public Condition Condition { get; set; }
        public bool IsConditionChanged { get; set; }
        public bool IsValueInput { get; set; }
        public bool IsIsValueInputChanged { get; set; }

        public string ModifiedById { get; set; }
    }

    public class BenefitCommandBatch
    {
        public List<AddBenefitCommand> AddBenefitCommands { get; set; }
        public List<UpdateBenefitCommand> UpdateBenefitCommands { get; set; }
        public List<RemoveBenefitCommand> RemoveBenefitCommands { get; set; }
    }

    public class RemoveBenefitCommand
    {
        public string TypeId { get; set; }
        public string OptionKey { get; set; }
        public string RemovedById { get; set; }
    }

    public class AddTagCommand
    {
        public string Type { get; set; }
        public string AddedById { get; set; }
    }

    public class ProductType : SystemObject
    {
        public string TypeId { get; set; }
        public string Code { get; set; }
        public string LogoUrl { get; set; }
        public string TenantId { get; set; }
        public IReadOnlyCollection<string> DataSchemaIds { get; set; }
    }

    public class ProductTypeWhere
    {
        public IEnumerable<ProductTypeWhere> Or { get; set; }
        public IEnumerable<ProductTypeWhere> And { get; set; }

        public string TypeId { get; set; }
        public IEnumerable<string> TypeId_in { get; set; }
        public string TenantId { get; set; }
        public IEnumerable<string> TenantId_in { get; set; }
    }

    public class CreateProductTypeCommand
    {
        public string TypeId { get; set; }
        public string LogoUrl { get; set; }
        public string CreatedById { get; set; }
    }

    public class AddDataSchemaToProductTypeCommand
    {
        public string ProductTypeId { get; set; }

        public string DataSchemaId { get; set; }

        public string AddedById { get; set; }
    }

    public class RemoveDataSchemaFromProductTypeCommand
    {
        public string ProductTypeId { get; set; }

        public string DataSchemaId { get; set; }

        public string RemovedById { get; set; }
    }

    public class ClaimSettings
    {
        public ProductSettings RejectionSettings { get; set; }
        public ProductSettings DiagnosisSettings { get; set; }
        public ProductSettings OperationSettings { get; set; }
        public ProviderSettings ProviderSettings { get; set; }
    }

    public class ClaimSettingsToUpdate
    {
        public ProductSettingsToUpdate RejectionSettings { get; set; }
        public bool IsRejectionSettingsChanged { get; set; }
        public ProductSettingsToUpdate DiagnosisSettings { get; set; }
        public bool IsDiagnosisSettingsChanged { get; set; }
        public ProductSettingsToUpdate OperationSettings { get; set; }
        public bool IsOperationSettingsChanged { get; set; }
        public ProviderSettingsToUpdate ProviderSettings { get; set; }
        public bool IsProviderSettingsChanged { get; set; }
    }

    public class ProductSettings
    {
        public List<string> Codes { get; set; }
    }

    public class ProductSettingsToUpdate //add inheritance to this if there are more props later
    {
        public List<string> Codes { get; set; }
        public bool IsCodesChanged { get; set; }
    }

    public class ProviderSettings
    {
        public List<string> EntityIds { get; set; }
    }

    public class ProviderSettingsToUpdate
    {
        public List<string> EntityIds { get; set; }
        public bool IsEntityIdsChanged { get; set; }
    }

    public class LoadingSettings : ProductSettings
    {
        public decimal? MaxLoadingMultiplier { get; set; }
    }

    public class LoadingSettingsToUpdate
    {
        public List<string> Codes { get; set; }
        public bool IsCodesChanged { get; set; }
        public decimal? MaxLoadingMultiplier { get; set; }
        public bool IsMaxLoadingMultiplierChanged { get; set; }
    }

    public class MigrateProductsCommand
    {
        public IEnumerable<CreateProductCommand> ProductInputs { get; set; }
    }

    public class AddUnderwritingVariableCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string JsonSchemaValidation { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateUnderwritingVariableCommand
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string JsonSchemaValidation { get; set; }
        public bool IsJsonSchemaValidationChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class ValidateResult
    {
        public ProductId ProductId { get; set; }
        public string Status { get; set; }
        public IEnumerable<Error> Errors { get; set; }
    }

    public class ProductUnderwritingJsonLogicRules
    {
        public ProductId Id { get; set; }
        public JToken JsonLogicRules { get; set; }
    }

    public class ProductUnderwritingJsonSchema
    {
        public string UnderwritingEngineId { get; set; }
        public ProductId ProductId { get; set; }
        public string JsonSchema { get; set; }
    }

    public class ProductUnderwritingJsonSchemaQuery
    {
        public IEnumerable<ProductUnderwritingJsonSchemaKey> Keys { get; set; }
        public JToken Factors { get; set; }
    }

    public class ProductUnderwritingJsonSchemaKey
    {
        public string UnderwritingEngineId { get; set; }
        public ProductId ProductId { get; set; }

        public override int GetHashCode() => HashCode.Combine(UnderwritingEngineId, ProductId);

        public override bool Equals(object obj) => obj is ProductUnderwritingJsonSchemaKey Key &&
           UnderwritingEngineId == Key.UnderwritingEngineId &&
           ProductId == Key.ProductId;

        public static bool operator ==(ProductUnderwritingJsonSchemaKey left, ProductUnderwritingJsonSchemaKey right) =>
         left?.UnderwritingEngineId == right?.UnderwritingEngineId &&
         left?.ProductId == right?.ProductId;

        public static bool operator !=(ProductUnderwritingJsonSchemaKey left, ProductUnderwritingJsonSchemaKey right) => !(left == right);
    }

    public class AddScriptToProductCommand
    {
        public ProductId ProductId { get; set; }

        public string ScriptId { get; set; }
    }

    public class RemoveScriptFromProductCommand
    {
        public ProductId ProductId { get; set; }

        public string ScriptId { get; set; }
    }

    public class AddTemplateRelationshipToProductCommand
    {
        public ProductId ProductId { get; set; }

        public string TemplateRelationshipId { get; set; }

        public string AddedById { get; set; }
    }

    public class RemoveTemplateRelationshipFromProductCommand
    {
        public ProductId ProductId { get; set; }

        public string TemplateRelationshipId { get; set; }

        public string RemovedById { get; set; }
    }
}
