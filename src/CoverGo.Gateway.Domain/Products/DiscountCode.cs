using System;
using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Domain.Products;

public class DiscountCode : SystemObject, IUniqueSystemObject
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string ProductTypeId { get; set; }
    public DiscountType Type { get; set; }
    public decimal Value { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public List<ProductId> ProductIds { get; set; }
}

public class DiscountCodeUpsert : IUniqueSystemObject
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string ProductTypeId { get; set; }
    public DiscountType? Type { get; set; }
    public decimal? Value { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public List<ProductId> ProductIds { get; set; }
    public string ById { get; set; }
}

public class DiscountCodeProductsUpsert
{
    public string DiscountCodeId { get; set; }
    public List<ProductId> ProductIds { get; set; }
    public string ById { get; set; }
}

public class DiscountCodeFilter
{
    public string Id { get; set; }
    public List<string> Id_in { get; set; }
    public string Name { get; set; }
    public string Name_contains { get; set; }
    public List<string> Name_in { get; set; }
    public string ProductTypeId { get; set; }
    public List<string> ProductTypeId_in { get; set; }
    public DiscountType? Type { get; set; }
    public DateTime? ValidFrom_gt { get; set; }
    public DateTime? ValidFrom_lt { get; set; }
    public DateTime? ValidFrom_gte { get; set; }
    public DateTime? ValidFrom_lte { get; set; }
    public DateTime? ValidTo_gt { get; set; }
    public DateTime? ValidTo_lt { get; set; }
    public DateTime? ValidTo_gte { get; set; }
    public DateTime? ValidTo_lte { get; set; }
    public bool? ValidTo_exists { get; set; }
    public bool? ProductIds_exists { get; set; }
    public List<ProductId> ProductIds_in { get; set; }
    public ProductIdWhere ProductIds_contains { get; set; }

}

public enum DiscountType
{
    AMOUNT,
    PERCENTAGE
}