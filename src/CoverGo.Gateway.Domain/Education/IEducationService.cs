﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Education.CourseProgressions;
using CoverGo.Gateway.Domain.Education.Courses;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Domain.Education
{
    public interface IEducationService
    {
        Task<long> GetCoursesTotalCountAsync(string tenantId, CourseWhere where);
        Task<IEnumerable<Course>> GetCoursesAsync(string tenantId, QueryArguments queryArguments);
        Task<Result<CreatedStatus>> CreateCourseAsync(string tenantId, CreateCourseCommand command);
        Task<Result> UpdateCourseAsync(string tenantId, string courseId, UpdateCourseCommand command);
        Task<Result> DeleteCourseAsync(string tenantId, string courseId, DeleteCommand command);
        Task<Result<CreatedStatus>> AddSectionToCourseAsync(string tenantId, string courseId, AddSectionToCourseCommand command);
        Task<Result> RemoveSectionFromCourseAsync(string tenantId, string courseId, RemoveSectionFromCourseCommand command);
        Task<Result<CreatedStatus>> AddSectionToSectionAsync(string tenantId, string courseId, AddSectionToSectionCommand command);
        Task<Result<CreatedStatus>> AddLessonToSectionAsync(string tenantId, string courseId, AddLessonToSectionCommand command);
        Task<Result> UpdateLessonOfSectionAsync(string tenantId, string courseId, UpdateLessonOfSectionCommand command);
        Task<Result> RemoveLessonFromSectionAsync(string tenantId, string courseId, RemoveLessonFromSectionCommand command);
        Task<Result<CreatedStatus>> AddLessonItemAsync(string tenantId, string courseId, AddLessonItemCommand command);
        Task<Result> UpdateLessonItemAsync(string tenantId, string courseId, UpdateLessonItemCommand command);
        Task<Result> RemoveLessonItemAsync(string tenantId, string courseId, RemoveLessonItemCommand command);
        Task<Result<CreatedStatus>> AddOptionToLessonItemAsync(string tenantId, string courseId, AddOptionToLessonItemCommand command);
        Task<Result> UpdateOptionOfLessonItemAsync(string tenantId, string courseId, UpdateOptionOfLessonItemCommand command);
        Task<Result> RemoveOptionFromLessonItemAsync(string tenantId, string courseId, RemoveOptionFromLessonItemCommand command);
        Task<long> GetCourseProgressionsTotalCountAsync(string tenantId, CourseProgressionWhere where);
        Task<ILookup<string, CourseProgression>> GetCourseProgressionsByEntityIdsLookupAsync(string tenantId, CourseProgressionWhere where);
        Task<IEnumerable<CourseProgression>> GetCourseProgressionsAsync(string tenantId, QueryArguments queryArguments);
        Task<Result<CreatedStatus>> CreateCourseProgressionAsync(string tenantId, CreateCourseProgressionCommand command);
        Task<Result> UpdateCourseProgressionAsync(string tenantId, string progressionId, UpdateCourseProgressionCommand command);
        Task<Result> DeleteCourseProgressionAsync(string tenantId, string progressionId, DeleteCommand command);
        Task<Result<CreatedStatus>> AddLessonCompletionToCourseProgressionAsync(string tenantId, string progressionId, AddLessonCompletionToCourseProgressionCommand command);
        Task<Result> UpdateLessonCompletionOfCourseProgressionAsync(string tenantId, string progressionId, UpdateLessonCompletionOfCourseProgressionCommand command);
        Task<Result> RemoveLessonCompletionFromCourseProgressionAsync(string tenantId, string progressionId, RemoveLessonCompletionFromCourseProgressionCommand command);
    }
}
