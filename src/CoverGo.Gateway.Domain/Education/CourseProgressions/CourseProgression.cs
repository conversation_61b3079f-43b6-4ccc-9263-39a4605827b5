﻿using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Domain.Education.CourseProgressions
{
    public class CourseProgression : SystemObject
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public string CourseId { get; set; }
        public double Score { get; set; }
        public bool IsPassed { get; set; }
        public IEnumerable<LessonCompletion> LessonsCompletions { get; set; }
    }

    public class LessonCompletion : SystemObject
    {
        public string Id { get; set; }
        public string LessonId { get; set; }
        public IEnumerable<LessonItemAnswer> ItemAnswers { get; set; }
        public double Score { get; set; }
        public bool IsPassed { get; set; }
    }

    public class LessonItemAnswer
    {
        public string ItemId { get; set; }
        public IEnumerable<string> Answer { get; set; }
    }

    public class CourseProgressionWhere : Where
    {
        public List<CourseProgressionWhere> Or { get; set; }
        public List<CourseProgressionWhere> And { get; set; }
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string EntityId { get; set; }
        public List<string> EntityId_in { get; set; }
        public string CourseId { get; set; }
        public List<string> CourseId_in { get; set; }
    }

    public class CreateCourseProgressionCommand
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public string CourseId { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateCourseProgressionCommand
    {
        public string EntityId { get; set; }
        public bool IsEntityIdChanged { get; set; }
        public string CourseId { get; set; }
        public bool IsCourseIdChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AddLessonCompletionToCourseProgressionCommand
    {
        public string Id { get; set; }
        public string AccessToken { get; set; }
        public string LessonId { get; set; }
        public IEnumerable<LessonItemAnswer> ItemAnswers { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateLessonCompletionOfCourseProgressionCommand
    {
        public string Id { get; set; }
        public string AccessToken { get; set; }
        public bool IsLessonIdChanged { get; set; }
        public string LessonId { get; set; }
        public bool IsItemAnswersChanged { get; set; }
        public IEnumerable<LessonItemAnswer> ItemAnswers { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveLessonCompletionFromCourseProgressionCommand
    {
        public string Id { get; set; }
        public string RemovedById { get; set; }
    }
}
