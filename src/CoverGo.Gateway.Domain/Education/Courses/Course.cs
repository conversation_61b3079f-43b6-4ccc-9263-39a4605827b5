﻿using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Domain.Education.Courses
{

    public class Course : SystemObject
    {
        public string Id { get; set; }
        public string Version { get; set; }
        public IEnumerable<string> Categories { get; set; }
        public IEnumerable<Section> Sections { get; set; }
    }

    public class Section : SystemObject
    {
        public string Id { get; set; }
        public IEnumerable<Section> Sections { get; set; }
        public IEnumerable<Lesson> Lessons { get; set; }
    }

    public class Lesson : SystemObject
    {
        public string Id { get; set; }
        public long DurationInSeconds { get; set; }
        public decimal PassingRatio { get; set; }
        public decimal Weight { get; set; }
        public string Version { get; set; }
        public IEnumerable<LessonItem> Items { get; set; }
    }

    public class LessonItem : SystemObject
    {
        public string Id { get; set; }
        public LessonItemTypes Type { get; set; }
        public IEnumerable<ItemOption> Options { get; set; }

    }

    public enum LessonItemTypes
    {
        Quizz,
        Headline,
        Text,
        List,
        Image,
        Video,
        Html
    }

    public class ItemOption : SystemObject
    {
        public string Id { get; set; }
        public bool IsCorrect { get; set; }
    }

    public class CourseWhere : Where
    {
        public List<CourseWhere> Or { get; set; }
        public List<CourseWhere> And { get; set; }
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public List<string> Categories_contains_some { get; set; }
        public string Name { get; set; }
        public string Name_contains { get; set; }
    }

    public class CreateCourseCommand
    {
        public string Id { get; set; }
        public IEnumerable<string> Categories { get; set; }
        public string Version { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateCourseCommand
    {
        public IEnumerable<string> Categories { get; set; }
        public bool IsCategoriesChanged { get; set; }
        public bool IsVersionChanged { get; set; }
        public string Version { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AddSectionToCourseCommand
    {
        public string Id { get; set; }
        public string AddedById { get; set; }
    }
    public class RemoveSectionFromCourseCommand
    {
        public string SectionId { get; set; }
        public string RemovedById { get; set; }
    }
    public class AddSectionToSectionCommand
    {
        public string Id { get; set; }
        public string SectionId { get; set; }
        public string AddedById { get; set; }
    }
    public class AddLessonToSectionCommand
    {
        public string Id { get; set; }
        public string SectionId { get; set; }
        public long DurationInSeconds { get; set; }
        public decimal PassingRatio { get; set; }
        public decimal Weight { get; set; }
        public string Version { get; set; }
        public string AddedById { get; set; }
    }
    public class UpdateLessonOfSectionCommand
    {
        public string SectionId { get; set; }
        public string LessonId { get; set; }
        public bool IsDurationInSecondsChanged { get; set; }
        public long DurationInSeconds { get; set; }
        public bool IsPassingRatioChanged { get; set; }
        public decimal PassingRatio { get; set; }
        public bool IsWeightChanged { get; set; }
        public decimal Weight { get; set; }
        public bool IsVersionChanged { get; set; }
        public string Version { get; set; }
        public string ModifiedById { get; set; }
    }
    public class RemoveLessonFromSectionCommand
    {
        public string SectionId { get; set; }
        public string LessonId { get; set; }
        public string RemovedById { get; set; }
    }

    public class AddLessonItemCommand
    {
        public string Id { get; set; }
        public string LessonId { get; set; }
        public LessonItemTypes Type { get; set; }
        public IEnumerable<ItemOption> Options { get; set; }
        public string AddedById { get; set; }
    }
    public class UpdateLessonItemCommand
    {
        public string ItemId { get; set; }
        public string LessonId { get; set; }
        public bool IsTypeChanged { get; set; }
        public LessonItemTypes Type { get; set; }
        public bool IsOptionsChanged { get; set; }
        public IEnumerable<ItemOption> Options { get; set; }
        public string ModifiedById { get; set; }
    }
    public class RemoveLessonItemCommand
    {
        public string LessonId { get; set; }
        public string ItemId { get; set; }
        public string RemovedById { get; set; }
    }
    public class AddOptionToLessonItemCommand
    {
        public string Id { get; set; }
        public string ItemId { get; set; }
        public string LessonId { get; set; }
        public bool IsCorrect { get; set; }
        public string AddedById { get; set; }
    }
    public class UpdateOptionOfLessonItemCommand
    {
        public string OptionId { get; set; }
        public string ItemId { get; set; }
        public string LessonId { get; set; }
        public bool IsIsCorrectChanged { get; set; }
        public bool IsCorrect { get; set; }
        public string ModifiedById { get; set; }
    }
    public class RemoveOptionFromLessonItemCommand
    {
        public string OptionId { get; set; }
        public string LessonId { get; set; }
        public string ItemId { get; set; }
        public string RemovedById { get; set; }
    }
}
