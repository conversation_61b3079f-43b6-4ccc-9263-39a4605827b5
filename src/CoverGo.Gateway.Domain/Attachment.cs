﻿namespace CoverGo.Gateway.Domain
{
    public class Attachment : SystemObject
    {
        public string Id { get; set; }
        public string Path { get; set; }
    }

    public class AddAttachmentCommand
    {
        public string Path { get; set; }
        public string Type { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateAttachmentCommand
    {
        public string Id { get; set; }
        public string Path { get; set; }
        public bool IsPathChanged { get; set; }
        public string Type { get; set; }
        public bool IsTypeChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveAttachmentCommand
    {
        public string Path { get; set; }
        public string RemovedById { get; set; }
    }
}
