﻿using System.Collections.Generic;

namespace CoverGo.Gateway.Domain.Encryptions
{
    public class InitializeTenantEncryptionsCommand
    {
        public IEnumerable<EncryptionConfig> EncryptionConfigs { get; set; }
    }

    public class EncryptionConfig
    {
        public string Type { get; set; } //e.g. users/ policies, etc.
        public Dictionary<string, object> Parameters { get; set; }
        public IEnumerable<EventEncryption> EventEncryptions { get; set; }
        public IEnumerable<DomainEncryption> DomainEncryptions { get; set; }
    }

    public class DomainEncryption
    {
        public string JsonPath { get; set; }
        public string AlgorithmId { get; set; }
        public string KeyString { get; set; }
        public Dictionary<string, object> Parameters { get; set; }
    }

    public class EventEncryption
    {
        public string EventType { get; set; }
        public string JsonPath { get; set; }
        public string AlgorithmId { get; set; }
        public string KeyString { get; set; }
        public Dictionary<string, object> Parameters { get; set; }
    }
}
