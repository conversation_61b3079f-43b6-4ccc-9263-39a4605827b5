using System;
using System.Collections.Generic;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Binder;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;

using Newtonsoft.Json.Linq;

using PolicyMemberUnderwritingResult = CoverGo.Policies.Client.PolicyMemberUnderwritingResult;
using PolicyMemberValidationResult = CoverGo.Policies.Client.PolicyMemberValidationResult;

namespace CoverGo.Gateway.Domain.Policies
{
    public class Policy : SystemObject
    {
        public string IssuerNumber { get; set; }
        public string Id { get; set; }
        public GeneratedFrom GeneratedFrom { get; set; }
        public Entity ContractHolder { get; set; }
        public List<Entity> OtherContractHolders { get; set; }
        public List<Entity> ContractInsured { get; set; }
        public List<Entity> ContractTerminated { get; set; }
        public List<BeneficiaryEligibility> ContractBeneficiaryEligibilities { get; set; }
        public List<Commission> Commissions { get; set; }
        public string Description { get; set; }
        public DateTime? IssueDate { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? CoolingOffExpiryDate { get; set; }
        public bool? IsCoolingOffSubmitted { get; set; }
        public string Status { get; set; }
        public string ReferralCode { get; set; }
        public string AcceptedOfferId { get; set; }
        public JToken Values { get; set; }
        public List<Attachment> Attachments { get; set; }
        public List<Note> Notes { get; set; }
        public List<PaymentInfo> PaymentInfos { get; set; }
        public List<Offer> Offers { get; set; }

        public List<JacketInstance> Jackets { get; set; }
        public List<Fact> Facts { get; set; }
        public string ClientId { get; set; }
        public string Source { get; set; }

        public bool IsIssued { get; set; }
        public string TransformedById { get; set; }
        public string IssuedById { get; set; }
        public string CancelledById { get; set; }
        public string CancellationReason { get; set; }
        public string RejectedById { get; set; }
        public List<string> RejectionCodes { get; set; }
        public string RejectionRemarks { get; set; }
        public AugmentedPrice Premium { get; set; } //NOTE: used in new flow
        public bool IsPremiumOverridden { get; set; }
        public ProductId ProductId { get; set; } //NOTE: used in new flow

        public List<Pricing.BenefitOption> BenefitOptions { get; set; } //NOTE: used in new flow
        public List<Exclusion> Exclusions { get; set; }
        public IEnumerable<Clause> Clauses { get; set; }
        public IEnumerable<Stakeholder> Stakeholders { get; set; }
        public IEnumerable<AssociatedContract> AssociatedContracts { get; set; }
        public IEnumerable<Endorsement> Endorsements { get; set; }
        public JToken Fields { get; set; }
        public string FieldsSchemaId { get; set; }
        public JToken ExtraFields { get; set; }
        public bool IsRenewal { get; set; }
        public string RenewalNumber { get; set; }
        public List<string> PreviousPolicyIds { get; set; }
        public string OriginalIssuerNumber { get; set; }
        public int? RenewalVersion { get; set; }
        public List<FieldsDiff> FieldsDiffs { get; set; }
        public string LapseReason { get; set; }
        public List<Tag> Tags { get; set; }
        public string ProductTreeId { get; set; }
        public List<ProductTreeRecord> ProductTreeRecords { get; set; }
        public bool IsPricingCacheAvailable { get; set; }
        public AccessPolicy? AccessPolicy { get; set; }
        public PolicyInstallmentStatus? InstallmentStatus { get; set; }
        public List<ExternalPolicyNumber> ExternalPolicyNumbers { get; set; }
        public List<ExternalPolicyStatus> ExternalStatuses { get; set; }
        public CoverGo.Policies.Client.PolicyCommission PolicyCommission { get; set; }
        public bool? IsV2 { get; set; }
        public CoverGo.Policies.Client.PolicyHolderType? HolderType { get; set; }
    }

    public class PolicyMember : IUniqueSystemObject
    {
        public string Id { get; set; }
        public string MemberId { get; set; }

        /// <summary>
        /// Internal code to be assigned to the individual when the policy is issued
        /// </summary>
        public string InternalCode { get; set; }

        /// <summary>
        /// IndividualId/MemberId the member is dependent from
        /// </summary>
        public string DependentOf { get; set; }

        /// <summary>
        /// PolicyMemberId the member is dependent from
        /// </summary>
        public string DependentOfPolicyMemberId { get; set; }

        public DateTime? CreatedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }
        public string PolicyId { get; set; }
        public string EndorsementId { get; set; }
        public string PlanId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public JToken Fields { get; set; }
        public DateTime Timestamp { get; set; }
        public string CreatedById { get; set; }
        public bool IsRemoved { get; set; }
        public bool IsPrinted { get; set; }
        public bool? IsRenewed { set; get; }
        public bool IsEndorsement { get; set; }

        public PolicyMemberValidationResult? ValidationResult { get; set; }
        public PolicyMemberUnderwritingResult? UnderwritingResult { get; set; }
        public string CertificateNumber { get; set; }
    }

    public class PolicyStatus
    {
        public string Id { get; set; }
        public string Status { get; set; }
    }

    public enum PaymentStatuses
    {
        NONE,
        PARTIAL,
        FULL
    }

    public class TransactionRedirection
    {
        public string Url { get; set; }
        public string HttpMethod { get; set; }
        public JToken Parameters { get; set; }
        public string Token { get; set; }
        public string PaymentId { get; set; }
        public string TransactionId { get; set; }
    }

    public class CreatePolicyCommand
    {
        public string IssuerNumber { get; set; }
        public GeneratedFrom GeneratedFrom { get; set; }
        public Entity ContractHolder { get; set; }
        public List<Entity> OtherContractHolders { get; set; }
        public List<Entity> ContractInsured { get; set; }
        public DateTime? IssueDate { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string Description { get; set; }
        public string ReferralCode { get; set; }
        public JToken Values { get; set; }
        public string Source { get; set; }
        public string ClientId { get; set; }
        public string Status { get; set; }
        public PremiumInput Premium { get; set; }
        public bool IsPremiumOverridden { get; set; }
        public ProductId ProductId { get; set; }
        public string CreatedById { get; set; }
        public string Fields { get; set; }
        public string FieldsSchemaId { get; set; }
        public bool IsRenewal { get; set; }
        public string RenewalNumber { get; set; }
        public List<string> PreviousPolicyIds { get; set; }
        public string OriginalIssuerNumber { get; set; }
        public int? RenewalVersion { get; set; }
    }

    public class IssuePolicyCommand
    {
        public string IssuerNumber { get; set; }
        public string OriginalIssuerNumber { get; set; }
        public string PolicyId { get; set; }
        public string Status { get; set; }
        public bool IgnoreCollateralValidation { get; set; }
        public bool IgnoreIssuanceValidation { get; set; }
        public bool SkipSendNotification { get; set; }
        public string IssuedById { get; set; }
        public List<string> ExternalPolicyNumberIssuerIds { get; set; }
        public List<ExternalPolicyNumber> ExternalPolicyNumbers { get; set; }
    }

    public class UpdatePolicyProductCommand
    {
        public string EndorsementId { get; set; } // for endorsements
        public string CommandId { get; set; } // for endorsements
        public string ModifiedById { get; set; }
        public DateTime Timestamp { get; set; } // for endorsements
        public ProductId ProductId { get; set; }
    }

    public class UpdatePolicyCommand
    {
        public string EndorsementId { get; set; } // for endorsements
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public string IssuerNumber { get; set; }
        public bool IsIssuerNumberChanged { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        [Obsolete("used by aag, should not be used by anyone else")]
        public string HolderId { get; set; }
        public bool IsHolderIdChanged { get; set; }
        [Obsolete("used by aag, should not be used by anyone else")]
        public List<string> InsuredIds { get; set; }
        public bool IsInsuredIdsChanged { get; set; }
        public UpdateIndividualCommand ContractHolderIndividual { get; set; }
        public bool IsContractHolderIndividualChanged { get; set; }
        public UpdateCompanyCommand ContractHolderCompany { get; set; }
        public bool IsContractHolderCompanyChanged { get; set; }
        public DateTime? IssueDate { get; set; }
        public bool IsIssueDateChanged { get; set; }
        public DateTime? StartDate { get; set; }
        public bool IsStartDateChanged { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsEndDateChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string ReferralCode { get; set; }
        public bool IsReferralCodeChanged { get; set; }
        public JToken Values { get; set; }
        public bool IsValuesChanged { get; set; }
        public string Source { get; set; }
        public bool IsSourceChanged { get; set; }
        public string ModifiedById { get; set; }
        public PremiumToUpdate Premium { get; set; }
        public bool IsPremiumChanged { get; set; }
        public bool? IsPremiumOverridden { get; set; }
        public ProductIdToUpdate ProductId { get; set; }
        public bool IsProductIdChanged { get; set; }
        public string Fields { get; set; }
        public string FieldsPatch { get; set; }
        public bool IsFieldsChanged { get; set; }
        public string FieldsSchemaId { get; set; }
        public bool IsFieldsSchemaIdChanged { get; set; }
        public string ExtraFields { get; set; }
        public bool IsExtraFieldsChanged { get; set; }
        public string LapseReason { get; set; }
        public bool IsLapseReasonChanged { get; set; }
    }

    public class CancelPolicyCommand
    {
        public string Reason { get; set; }
        public string CancelledById { get; set; }
    }

    public class RejectQuoteCommand
    {
        public List<string> Codes { get; set; }
        public string Remarks { get; set; }
        public string RejectedById { get; set; }
    }

    public class AddExclusionCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Code { get; set; }
        public string BenefitParentTypeId { get; set; }
        public string BenefitTypeId { get; set; }
        public string BenefitOptionKey { get; set; }
        public string Remark { get; set; }
        public string AddedById { get; set; }
    }

    public class RemoveExclusionCommand
    {
        public string Id { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string RemovedById { get; set; }
    }

    public class AddUpdatePolicyCommandToRequestCommand
    {
        public string RequestId { get; set; }
        public UpdatePolicyCommand Command { get; set; }
    }

    public class PolicyUpdateRequestFilter
    {
        public List<string> Ids { get; set; }
        public List<string> Statuses { get; set; }
    }

    public class PolicyWhere : Where
    {
        public List<PolicyWhere> Or { get; set; }
        public List<PolicyWhere> And { get; set; }

        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public EntityWhere ContractHolder { get; set; }
        public EntityWhere Holder { get; set; }
        public FieldsWhere Fields { get; set; }
        public PolicyEndorsementWhere Endorsement { get; set; }
        public EntityWhere ContractInsured_some { get; set; }
        public EntityWhere ContractTerminated_some { get; set; }
        public ProductWhere Product { get; set; }
        public ProductIdWhere ProductId { get; set; }
        public List<ProductId> ProductId_in { get; set; }

        public List<string> Status_in { get; set; }
        public List<string> Status_not_in { get; set; }
        public string Description { get; set; }
        public string Description_contains { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? StartDate_gt { get; set; }
        public DateTime? StartDate_lt { get; set; }
        public DateTime? StatusModifiedAt { get; set; }
        public DateTime? StatusModifiedAt_gt { get; set; }
        public DateTime? StatusModifiedAt_lt { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? EndDate_gt { get; set; }
        public DateTime? EndDate_lt { get; set; }
        public DateTime? IssueDate_gt { get; set; }
        public DateTime? IssueDate_lt { get; set; }
        public string IssuerNumber_contains { get; set; }
        public IEnumerable<string> IssuerNumber_in { get; set; }
        public string Source { get; set; }
        public List<string> Source_in { get; set; }
        public string ClientId { get; set; }
        public List<string> ClientId_in { get; set; }
        public bool? IsRenewal { get; set; }
        public bool? IsHighestPriorityRenewalPerPolicy { get; set; }
        public bool? IsIssued { get; set; }
        public string PreviousPolicyIds_contains { get; set; }
        public IEnumerable<string> PreviousPolicyIds_contains_some { get; set; }
        public IEnumerable<string> PreviousPolicyIds_contains_every { get; set; }

        public string OriginalIssuerNumber_contains { get; set; }
        public IEnumerable<string> OriginalIssuerNumber_in { get; set; }
        public int? RenewalVersion { get; set; }
        public int? RenewalVersion_gt { get; set; }
        public int? RenewalVersion_lt { get; set; }
        public FactWhere Facts_contains { get; set; }
        public FieldsDiffWhere FieldsDiff_contains { get; set; }
        public string ReferralCode { get; set; }
        public TagWhere Tags_some { get; set; }
        public AccessPolicy? AccessPolicy { get; set; }
        public StakeholderWhere Stakeholders_contains { get; set; }
        public PolicyInstallmentStatus? InstallmentStatus { get; set; }
        public ExternalPolicyNumberWhere ExternalPolicyNumber_in { get; set; }
        public List<ExternalPolicyStatusWhere> ExternalStatus_in { get; set; }
    }

    public class ExternalPolicyNumberWhere
    {
        public string Type { get; set; }
        public List<string> Type_in { get; set; }
        public string Value { get; set; }
        public List<string> Value_in { get; set; }

    }

    public class ExternalPolicyStatusWhere
    {
        public string Type { get; set; }
        public string Value { get; set; }
    }

    public class PolicyMembersWhere : QueryArguments<Filter<PolicyMembersFilter>>
    {
        public List<string> PolicyId_In { get; set; }
        public List<string> EndorsementId_In { get; set; }
        public bool? IsTerminated { get; set; }
        public string IndividualId { get; set; }
        public bool? FilterOutTerminatedMemberMovements { get; set; }
        public bool? FilterOutUnderwritingNotApproved { get; set; }
    }

    public class PolicyMembersFilter
    {
        public List<string> MemberId_in { get; set; }
        public string PlanId_contains { get; set; }
        public List<string> PlanId_in { get; set; }
        public bool? IsRemoved { get; set; }
        public bool? HavingStartDate { get; set; }
        public bool? IsTerminated { get; set; }
        public bool? IsPrinted { get; set; }
        public bool? IsRenewed { get; set; }
        public string InternalCode { get; set; }
        public List<string> InternalCode_in { get; set; }
        public string InternalCode_contains { get; set; }
        public FieldsWhere Fields { get; set; }
        public DateTime? LastModifiedAt_lt { get; set; }
        public DateTime? LastModifiedAt_gt { get; set; }
        public DateTime? CreatedAt_lt { get; set; }
        public DateTime? CreatedAt_gt { get; set; }
        public bool? HavingEndDate { get; set; }
        public bool? HavingMovementType { get; set; }
        public PolicyMemberUnderwritingResult? UnderwritingResult { get; set; }
        public List<PolicyMemberUnderwritingResult?> UnderwritingResult_in { get; set; }
        public PolicyMemberValidationResult? ValidationResult { get; set; }
        public List<PolicyMemberValidationResult?> ValidationResult_in { get; set; }
        public string? EndorsementId { get; set; }
        public List<string?> EndorsementId_in { get; set; }
    }

    /// <summary>
    /// Filter policies based on their endorsements.
    /// </summary>
    public class PolicyEndorsementWhere
    {
        public string? Type { get; set; }
        public List<string> Type_in { get; set; }

        public string? Status { get; set; }
        public List<string> Status_in { get; set; }
        public List<MemberMovementVersion?> MemberMovementVersion_in { get; set; }
    }

    public class EndorsementWhere : Where
    {
        public string? Id { get; set; }
        public List<string> Id_in { get; set; }

        public string? Type { get; set; }
        public List<string> Type_in { get; set; }

        public string? Status { get; set; }
        public List<string> Status_in { get; set; }

        public bool? Source_exists { get; set; }

        public List<MemberMovementVersion?> MemberMovementVersion_in { get; set; }
    }

    public class FieldsDiffWhere : Where
    {
        public DateTime? From { get; set; }
        public DateTime? To { get; set; }
    }

    public class FieldsDiff
    {
        public FieldsDiffType DiffType { get; set; }
        public string ObjectId { get; set; }
        public JToken Before { get; set; }
        public JToken After { get; set; }
        public DateTime TimeStamp { get; set; }
        public FieldsDiffItem[] Changes { get; set; }
    }

    public enum FieldsDiffType
    {
        Add = 1,
        Remove = 2,
        Update = 3
    }

    public class FieldsDiffItem
    {
        public string Path { get; set; }
        public string Before { get; set; }
        public string After { get; set; }
    }

    public class PolicyRequestReview
    {
        public string RequestId { get; set; }
        public bool IsApproved { get; set; }
        public string Note { get; set; }
        public string ReviewedById { get; set; }
    }

    public class PolicyUpdateRequest
    {
        public string Id { get; set; }
        public string PolicyId { get; set; }
        public List<UpdatePolicyCommand> UpdatePolicyCommands { get; set; }
        public List<UpdatePaymentInfoCommand> UpdatePaymentInfoCommands { get; set; }
        public List<UpdateBeneficiaryEligibilityCommand> UpdateBeneficiaryEligibilityCommands { get; set; }
        public List<UpsertBenefitOptionCommand> UpsertBenefitOptionCommands { get; set; }
        public string ApprovalStatus { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime LastModifiedAt { get; set; }
        public string CreatedById { get; set; }
        public string LastModifiedById { get; set; }
    }

    public class CreatePolicyUpdateRequestCommand
    {
        public string CreatedById { get; set; }
    }

    public class AddEntityCommand
    {
        public string CommandId { get; set; }
        public DateTime Timestamp { get; set; }
        public string EndorsementId { get; set; }
        public string EntityId { get; set; }
        public string AddedById { get; set; }
    }


    public class InitializeTenantPoliciesCommand
    {
        public IEnumerable<AddPolicyNumberConfigCommand> PolicyNumberConfigs { get; set; }
    }
    public class AddPolicyNumberConfigCommand
    {
        public string IssuerId { get; set; }
        public int PolicyNumber { get; set; }
    }

    public enum PolicyInstallmentStatus
    {
        Billed,
        Collected,
        Overdue,
        Due
    }

    public class ExternalPolicyNumber
    {
        public string Type { get; set; }
        public string Value { get; set; }
    }

    public class ExternalPolicyStatus
    {
        public string Type { get; set; }
        public string Value { get; set; }
    }
}