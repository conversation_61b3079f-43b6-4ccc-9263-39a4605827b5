using System;
using System.Collections.Generic;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Binder;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Domain.Policies
{
    public enum PaymentProcessType
    {
        /// <summary>
        /// Don't process
        /// </summary>
        None,
        /// <summary>
        /// Charge
        /// </summary>
        FutureInstallment,
        /// <summary>
        /// Charge
        /// </summary>
        AdHocBilling,
        /// <summary>
        /// Refund
        /// </summary>
        AdHocRefund
    }

    public enum EndorsementPaymentProcessStatus
    {
        Waiting,
        ReadyToApprove,
        Approved
    }

    public enum EndorsementUnderwritingStatus
    {
        Waiting,
        PassedWithOnlyTerminations,
        Passed,
        Rejected
    }

    public enum EndorsementAdjustPremiumStatus
    {
        Waiting,
        Done
    }

    public static class EndorsementStatus
    {
        public const string Draft = "DRAFT";
        public const string Created = "CREATED";
        public const string Approved = "APPROVED";
        public const string Rejected = "REJECTED";
    }

    public static class EndorsementType
    {
        public const string MemberMovement = "MEMBER_MOVEMENT";
    }

    public enum EndorsementSource
    {
        AdminPortal,
        HrPortal,
        TameeniWebsite,
        DaminWebsite,
        BcareWebsite
    }

    public enum MemberMovementVersion
    {
        MEMBER_MOVEMENT_V1,
        MEMBER_MOVEMENT_V2,
        MEMBER_MOVEMENT_V3
    }

    public class Endorsement : SystemObject
    {
        public string Id { get; set; }
        public string Status { get; set; }
        public EndorsementSource? Source { get; set; }
        public string Type { get; set; }
        public string ReasonOfChange { get; set; }
        public string ReasonRemarks { get; set; }
        public JToken? Fields { get; set; }
        public string CancellationMotive { get; set; }
        public DateTime? EffectiveDate { get; set; }
        public bool IsApproved { get; set; }
        public bool IsRejected { get; set; }
        public DateTime? EndorsementDate { get; set; }
        public DateTime? SubmissionDate { get; set; }
        public EndorsementUnderwritingStatus? UnderwritingStatus { get; set; }
        public bool? IsPremiumAdjustmentReviewed { get; set; }
        public string QuoteId { get; set; }
        public string UnderwritingCaseId { get; set; }
        public PaymentProcessType? PaymentProcessType { get; set; }
        public EndorsementPaymentProcessStatus? PaymentProcessStatus { get; set; }
        public MemberMovementVersion? MemberMovementVersion { get; set; }
        public string InternalCode { get; set; }
        public IEnumerable<UpdatePolicyCommand> UpdatePolicyCommands { get; set; }

        public IEnumerable<AddDiscountCommand> AddDiscountCommands { get; set; }
        public IEnumerable<UpdateDiscountCommand> UpdateDiscountCommands { get; set; }
        public IEnumerable<RemoveCommand> RemoveDiscountCommands { get; set; }

        public IEnumerable<AddLoadingCommand> AddLoadingCommands { get; set; }
        public IEnumerable<UpdateLoadingCommand> UpdateLoadingCommands { get; set; }
        public IEnumerable<RemoveCommand> RemoveLoadingCommands { get; set; }

        public IEnumerable<AddFactCommand> AddPolicyFactCommands { get; set; }
        public IEnumerable<UpdateFactCommand> UpdatePolicyFactCommands { get; set; }
        public IEnumerable<RemoveCommand> RemovePolicyFactCommands { get; set; }
        public IEnumerable<UpsertBenefitOptionCommand> UpsertBenefitOptionCommands { get; set; }
        public IEnumerable<RemoveBenefitOptionCommand> RemoveBenefitOptionCommands { get; set; }

        public IEnumerable<UpdateIndividualCommand> UpdateOtherContractHolderIndividualCommands { get; set; }
        public IEnumerable<UpdateIndividualCommand> UpdateContractInsuredIndividualCommands { get; set; }
        public IEnumerable<UpdateCompanyCommand> UpdateOtherContractHolderCompanyCommands { get; set; }
        public IEnumerable<UpdateCompanyCommand> UpdateContractInsuredCompanyCommands { get; set; }
        public IEnumerable<UpdateObjectCommand> UpdateContractInsuredObjectCommands { get; set; }

        public IEnumerable<AddEntityCommand> AddOtherContractHolderCommands { get; set; }
        public IEnumerable<AddEntityCommand> AddContractInsuredCommands { get; set; }
        public IEnumerable<RemoveCommand> RemoveOtherContractHolderCommands { get; set; }
        public IEnumerable<RemoveCommand> RemoveContractInsuredCommands { get; set; }

        public IEnumerable<AddFactCommand> AddContractHolderFactCommands { get; set; }
        public IEnumerable<AddFactCommand> AddOtherContractHolderFactCommands { get; set; }
        public IEnumerable<AddFactCommand> AddContractInsuredFactCommands { get; set; }
        public IEnumerable<UpdateFactCommand> UpdateContractHolderFactCommands { get; set; }
        public IEnumerable<UpdateFactCommand> UpdateOtherContractHolderFactCommands { get; set; }
        public IEnumerable<UpdateFactCommand> UpdateContractInsuredFactCommands { get; set; }
        public IEnumerable<RemoveCommand> RemoveContractHolderFactCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommand> RemoveOtherContractHolderFactCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommand> RemoveContractInsuredFactCommands { get; set; }

        public IEnumerable<AddIdentityCommand> AddContractHolderIdentityCommands { get; set; }
        public IEnumerable<AddIdentityCommand> AddOtherContractHolderIdentityCommands { get; set; }
        public IEnumerable<AddIdentityCommand> AddContractInsuredIdentityCommands { get; set; }
        public IEnumerable<UpdateIdentityCommand> UpdateContractHolderIdentityCommands { get; set; }
        public IEnumerable<UpdateIdentityCommand> UpdateOtherContractHolderIdentityCommands { get; set; }
        public IEnumerable<UpdateIdentityCommand> UpdateContractInsuredIdentityCommands { get; set; }
        public IEnumerable<RemoveCommand> RemoveContractHolderIdentityCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommand> RemoveOtherContractHolderIdentityCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommand> RemoveContractInsuredIdentityCommands { get; set; }

        public IEnumerable<AddContactCommand> AddContractHolderContactCommands { get; set; }
        public IEnumerable<AddContactCommand> AddOtherContractHolderContactCommands { get; set; }
        public IEnumerable<AddContactCommand> AddContractInsuredContactCommands { get; set; }
        public IEnumerable<UpdateContactCommand> UpdateContractHolderContactCommands { get; set; }
        public IEnumerable<UpdateContactCommand> UpdateOtherContractHolderContactCommands { get; set; }
        public IEnumerable<UpdateContactCommand> UpdateContractInsuredContactCommands { get; set; }
        public IEnumerable<RemoveCommand> RemoveContractHolderContactCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommand> RemoveOtherContractHolderContactCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommand> RemoveContractInsuredContactCommands { get; set; }

        public IEnumerable<AddAddressCommand> AddContractHolderAddressCommands { get; set; }
        public IEnumerable<AddAddressCommand> AddOtherContractHolderAddressCommands { get; set; }
        public IEnumerable<AddAddressCommand> AddContractInsuredAddressCommands { get; set; }
        public IEnumerable<UpdateAddressCommand> UpdateContractHolderAddressCommands { get; set; }
        public IEnumerable<UpdateAddressCommand> UpdateOtherContractHolderAddressCommands { get; set; }
        public IEnumerable<UpdateAddressCommand> UpdateContractInsuredAddressCommands { get; set; }
        public IEnumerable<RemoveCommand> RemoveContractHolderAddressCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommand> RemoveOtherContractHolderAddressCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommand> RemoveContractInsuredAddressCommands { get; set; }

        public IEnumerable<AddAssociatedContractCommand> AddAssociatedContractCommands { get; set; }
        public IEnumerable<RemoveAssociatedContractCommand> RemoveAssociatedContractCommands { get; set; }

        public IEnumerable<AddClauseCommand> AddClauseCommands { get; set; }
    }

    public class AddEndorsementCommand
    {
        public string AddedById { get; set; }

        public string Type { get; set; }
        public string ReasonOfChange { get; set; }
        public string Fields { get; set; }
        public DateTime? EffectiveDate { get; set; }
        public string CancellationMotive { get; set; }
        public EndorsementSource? Source { get; set; }
        public bool? IsDraft { get; set; }
        public MemberMovementVersion? MemberMovementVersion { get; set; }
        public EndorsementUnderwritingStatus? UnderwritingStatus { get; set; }
    }

    public class AcceptEndorsementCommand
    {
        public string Id { get; set; }
        public string ApprovedById { get; set; }
    }

    public class UpdateEndorsementCommand
    {
        public string Id { get; set; }
        public bool IsReasonOfChangeChanged { get; set; }
        public string ReasonOfChange { get; set; }
        public string ReasonRemarks { get; set; }
        public bool IsFieldsChanged { get; set; }
        public string Fields { get; set; }
        public DateTime? EffectiveDate { get; set; }
        public bool IsEffectiveDateChanged { get; set; }
        public string UpdatedById { get; set; }
        public bool IsStatusChanged { get; set; }
        public string Status { get; set; }
        public bool IsCancellationMotiveChanged { get; set; }
        public string CancellationMotive { get; set; }
        public bool IsUnderwritingStatusChanged { get; set; }
        public EndorsementUnderwritingStatus? UnderwritingStatus { get; set; }
        public bool IsPaymentProcessStatusChanged { get; set; }
        public EndorsementPaymentProcessStatus? PaymentProcessStatus { get; set; }
    }

    public class RejectEndorsementCommand
    {
        public string Id { get; set; }
        public string RejectedById { get; set; }
    }

    public class RemoveEntityPrimitiveCommand
    {
        public string CommandId { get; set; }
        public DateTime Timestamp { get; set; }
        public string EndorsementId { get; set; }
        public string EntityId { get; set; }
        public string PrimitiveId { get; set; }
        public string RemovedById { get; set; }
    }
}
