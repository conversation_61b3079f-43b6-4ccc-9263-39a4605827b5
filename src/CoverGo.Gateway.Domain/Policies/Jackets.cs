using System;
using System.Collections.Generic;

namespace CoverGo.Gateway.Domain.Policies
{
    public class PolicyJacketInstanceBatchCommand
    {
        public string EndorsementId { get; set; }

        public string CommandId { get; set; } //for endorsements

        public DateTime Timestamp { get; set; } //for endorsements
        
        public string ById { get; set; }
        
        public List<AddJacketInstanceCommand> AddJacketInstanceCommands { get; set; }
        
        public List<UpdateJacketInstanceCommand> UpdateJacketInstanceCommands { get; set; }
    }

    public class RemovePolicyJacketInstanceCommand
    {
        public string CommandId { get; set; }
        
        public string EndorsementId { get; set; }

        public string InstanceId { get; set; }

        public string RemovedById { get; set; }
        
        public DateTime Timestamp { get;set; }
    }
}