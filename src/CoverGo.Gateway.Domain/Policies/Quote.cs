﻿using CoverGo.Gateway.Domain.Binder;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Domain.Users;
using Newtonsoft.Json.Linq;

using System;
using System.Collections.Generic;

namespace CoverGo.Gateway.Domain.Policies
{
    public class Offer : SystemObject
    {
        public string Id { get; set; }
        public string OfferNumber { get; set; }
        public string PolicyNumber { get; set; } //use in cases when generating policies
        public string Status { get; set; }
        public ProductId ProductId { get; set; }
        public List<Pricing.BenefitOption> BenefitOptions { get; set; }
        public AugmentedPrice Premium { get; set; }
        public bool IsPremiumOverridden { get; set; }
        public List<Exclusion> Exclusions { get; set; }
        public List<Clause> Clauses { get; set; }
        public List<JacketInstance> Jackets { get; set; }
        public List<Fact> Facts { get; set; }
        public List<Stakeholder> Stakeholders { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public JToken Values { get; set; }
        public List<AssociatedContract> AssociatedContracts { get; set; }
        public List<Commission> Commissions { get; set; }
        public List<EventLog> Events { get; set; }

        public string PolicyId { get; set; } // used for Subscriptions
        public decimal Amount { get; set; } // used for Subscriptions
        public CurrencyCode CurrencyCode { get; set; } // used for Subscriptions

        public string ContractId { get; set; }
        public string Pricing { get; set; }
        public string Underwriting { get; set; }
        public string Fields { get; set; }
        public JToken Fields2 { get; set; }
        public string FieldsSchemaId { get; set; }
        public string ProductTreeId { get; set; }
        public List<ProductTreeRecord> ProductTreeRecords { get; set; }
        public string DistributorID { get; set; }
        public List<string> CampaignCodes { get; set; }
    }

    public class AddOfferCommand
    {
        public string ProposalId { get; set; }
        public string OfferNumber { get; set; }
        public string OfferNumberType { get; set; }
        public string PolicyNumber { get; set; } //used in cases when generating policies
        public string Status { get; set; }
        public JToken Values { get; set; }
        public ProductId ProductId { get; set; }
        public List<Pricing.BenefitOption> BenefitOptions { get; set; }
        public PremiumInput Premium { get; set; }
        public bool IsPremiumOverridden { get; set; }
        public string AddedById { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        public string PolicyId { get; set; } //used for subscriptions
        public decimal Amount { get; set; } // used for Subscriptions
        public CurrencyCode CurrencyCode { get; set; } // used for Subscriptions

        public string ContractId { get; set; }
        public string Pricing { get; set; }
        public string Underwriting { get; set; }
        public string Fields { get; set; }
        public string FieldsSchemaId { get; set; }
        public string ProductTreeId { get; set; }
        public List<ProductTreeRecord> ProductTreeRecords { get; set; }
        public string DistributorID { get; set; }
        public List<string> CampaignCodes { get; set; }
    }

    public class UpdateOfferCommand
    {
        public string ProposalId { get; set; }

        public string OfferNumber { get; set; }
        public bool IsOfferNumberChanged { get; set; }
        public string PolicyNumber { get; set; } // used when generating policies
        public bool IsPolicyNumberChanged { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public JToken Values { get; set; }
        public bool IsValuesChanged { get; set; }
        public string OfferId { get; set; }
        public bool IsProductIdChanged { get; set; }
        public ProductIdToUpdate ProductId { get; set; }
        public bool IsPremiumChanged { get; set; }
        public PremiumToUpdate Premium { get; set; }
        public bool? IsPremiumOverridden { get; set; }
        public ProrationBehaviour ProrationBehaviour { get; set; } // for subscription offer
        public string ModifiedById { get; set; }
        public DateTime? StartDate { get; set; }
        public bool IsStartDateChanged { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsEndDateChanged { get; set; }
        public string ContractId { get; set; }
        public bool IsContractIdChanged { get; set; }
        public string Pricing { get; set; }
        public bool IsPricingChanged { get; set; }
        public string Underwriting { get; set; }
        public bool IsUnderwritingChanged { get; set; }
        public string Fields { get; set; }
        public bool IsFieldsChanged { get; set; }
        public string FieldsSchemaId { get; set; }
        public bool IsFieldsSchemaIdChanged { get; set; }
        public string ProductTreeId { get; set; }
        public bool IsProductTreeIdChanged { get; set; }
        public string DistributorID { get; set; }
        public bool IsDistributorIDChanged { get; set; }
        public List<string> CampaignCodes { get; set; }
        public bool IsCampaignCodesChanged { get; set; }
    }

    public enum ProrationBehaviour
    {
        chargeImmediately,
        nextInterval,
        none
    }

    public class PremiumToUpdate : PremiumInput
    {
        public bool IsAmountChanged { get; set; }
        public bool IsGrossAmountChanged { get; set; }
        public bool IsCurrencyCodeChanged { get; set; }
        public bool IsDiscountCodesChanged { get; set; }
        public bool IsMetadataChanged { get; set; }
        public bool IsAppliedTaxesChanged { get; set; }
    }

    public class ConvertOfferCommand
    {
        public string OfferId { get; set; }
        public string ConvertedById { get; set; }
    }

    public class AddLoadingCommand
    {
        public string CommandId { get; set; }
        public string EndorsementId { get; set; }
        public DateTime Timestamp { get; set; }
        public string Id { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Code { get; set; }
        public decimal? Ratio { get; set; }
        public decimal? Flat { get; set; }
        public JObject CalculationJsonLogic { get; set; }
        public int Order { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateLoadingCommand
    {
        public string CommandId { get; set; }
        public string EndorsementId { get; set; }
        public DateTime Timestamp { get; set; }
        public string Id { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Code { get; set; }
        public bool IsCodeChanged { get; set; }
        public decimal? Ratio { get; set; }
        public bool IsRatioChanged { get; set; }
        public decimal? Flat { get; set; }
        public bool IsFlatChanged { get; set; }
        public JObject CalculationJsonLogic { get; set; }
        public bool IsCalculationJsonLogicChanged { get; set; }
        public int Order { get; set; }
        public bool IsOrderChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveLoadingCommand
    {
        public string Id { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string RemovedById { get; set; }
    }

    public class AddDiscountCommand
    {
        public string EndorsementId { get; set; }
        public string CommandId { get; set; }
        public DateTime Timestamp { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Name { get; set; }
        public int Order { get; set; }
        public JObject CalculationJsonLogic { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateDiscountCommand
    {
        public string EndorsementId { get; set; } // for endorsements
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public string ProposalId { get; set; }
        public string DiscountId { get; set; }
        public string OfferId { get; set; }
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public int Order { get; set; }
        public bool IsOrderChanged { get; set; }
        public JObject CalculationJsonLogic { get; set; }
        public bool IsCalculationJsonLogicChanged { get; set; }

        public string ModifiedById { get; set; }
    }

    public class RemoveDiscountFromOfferCommand
    {
        public string ProposalId { get; set; }
        public string DiscountId { get; set; }
        public string OfferId { get; set; }
        public string RemovedById { get; set; }
    }

    public class Clause
    {
        public string Id { get; set; }
        public int Order { get; set; }
        public string TemplateId { get; set; }
        public Template Template { get; set; }
        public RenderParameters RenderParameters { get; set; }
        public string HtmlOverride { get; set; }
        public string Type { get; set; }
    }

    public class AddClauseCommand
    {
        public string EndorsementId { get; set; } //for endorsements
        public string CommandId { get; set; } //for endorsements
        public DateTime Timestamp { get; set; } //for endorsements
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string ClauseId { get; set; }
        public int Order { get; set; }
        public string Type { get; set; }
        public string TemplateId { get; set; }
        public bool StoreTemplateByValue { get; set; }
        public Template Template { get; set; }
        public RenderParameters RenderParameters { get; set; }
        public string HtmlOverride { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateClauseCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string ClauseId { get; set; }
        public int Order { get; set; }
        public bool IsOrderChanged { get; set; }
        public string TemplateId { get; set; }
        public bool StoreTemplateByValue { get; set; }
        public Template Template { get; set; }

        public bool IsTemplateChanged { get; set; }
        public bool IsTemplateIdChanged { get; set; }
        public RenderParameters RenderParameters { get; set; }
        public bool IsRenderParametersChanged { get; set; }
        public string HtmlOverride { get; set; }
        public bool IsHtmlOverrideChanged { get; set; }
        public string Type { get; set; }
        public bool IsTypeChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveClauseCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string ClauseId { get; set; }
        public string RemovedById { get; set; }
    }

    public class ClauseBatchCommand
    {
        public string EndorsementId { get; set; }
        public DateTime Timestamp { get; set; }
        public string ById { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public List<AddClauseCommand> AddClauseCommands { get; set; }
        public List<UpdateClauseCommand> UpdateClauseCommands { get; set; }
        public List<RemoveClauseCommand> RemoveClauseCommands { get; set; }
    }

    public class JacketInstanceBatchCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public List<AddJacketInstanceCommand> AddJacketInstanceCommands { get; set; }
        public List<UpdateJacketInstanceCommand> UpdateJacketInstanceCommands { get; set; }
    }

    public class AddJacketInstanceCommand
    {
        public string JacketId { get; set; }
        public bool StoreJacketByValue { get; set; }
        public Jacket Jacket { get; set; }
        public int Order { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateJacketInstanceCommand
    {
        public string InstanceId { get; set; }
        public int Order { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveJacketInstanceCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string InstanceId { get; set; }
        public string RemovedById { get; set; }
    }
}
