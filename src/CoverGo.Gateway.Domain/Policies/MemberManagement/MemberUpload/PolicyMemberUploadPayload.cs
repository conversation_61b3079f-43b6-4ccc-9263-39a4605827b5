namespace CoverGo.Gateway.Domain.Policies.MemberManagement.MemberUpload;


/// Do not use <c>record struct</c> because HotChocolate does not support it

public abstract record PolicyMemberUploadPayload(string PolicyId, string UploadId);

public record PolicyMemberUploadRegisteredPayload(string PolicyId, string UploadId)
    : PolicyMemberUploadPayload(PolicyId, UploadId);

public record PolicyMemberUploadStartedValidatingPayload(string PolicyId, string UploadId, int MembersToProcess)
    : PolicyMemberUploadPayload(PolicyId, UploadId);

public record PolicyMemberUploadValidatedPayload(string PolicyId, string UploadId, int ValidMembersCount, int InvalidMembersCount)
    : PolicyMemberUploadPayload(PolicyId, UploadId);

public record PolicyMemberUploadStartedImportingPayload(string PolicyId, string UploadId, int MembersToProcess)
    : PolicyMemberUploadPayload(PolicyId, UploadId);

public record PolicyMemberUploadImportedPayload(string PolicyId, string UploadId, int MembersToProcess, int ValidMembersCount, int InvalidMembersCount)
    : PolicyMemberUploadPayload(PolicyId, UploadId);

public record PolicyMemberUploadStartedErrorRemovingPayload(string PolicyId, string UploadId, int MembersToProcess)
    : PolicyMemberUploadPayload(PolicyId, UploadId);

public record PolicyMemberUploadErrorRemovedPayload(string PolicyId, string UploadId)
    : PolicyMemberUploadPayload(PolicyId, UploadId);

public record PolicyMemberUploadStartedReversingPayload(string PolicyId, string UploadId, int MembersToProcess)
    : PolicyMemberUploadPayload(PolicyId, UploadId);

public record PolicyMemberUploadReversedPayload(string PolicyId, string UploadId)
    : PolicyMemberUploadPayload(PolicyId, UploadId);

public record PolicyMemberUploadStartedCancelingPayload(string PolicyId, string UploadId, int MembersToProcess)
    : PolicyMemberUploadPayload(PolicyId, UploadId);

public record PolicyMemberUploadCanceledPayload(string PolicyId, string UploadId)
    : PolicyMemberUploadPayload(PolicyId, UploadId);

public record PolicyMemberUploadFailedPayload(string PolicyId, string UploadId)
    : PolicyMemberUploadPayload(PolicyId, UploadId);