namespace CoverGo.Gateway.Domain.Policies.MemberManagement.MemberUpload;

/// <remarks>
/// Do not use <c>record struct</c> because HotChocolate does not support it
/// </remarks>
public record PolicyMemberUploadTopic(string TenantId, string PolicyId, SubscribePolicyMemberUploadEvent Event)
{
    public override string ToString() => $"{nameof(PolicyMemberUploadTopic)}::{Event}::{TenantId}::{PolicyId}";
}