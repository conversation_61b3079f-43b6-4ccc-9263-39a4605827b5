using System;

namespace CoverGo.Gateway.Domain.Policies.MemberManagement.MemberUpload;

/// <remarks>
/// Do not use <c>record struct</c> because HotChocolate does not support it
/// </remarks>
public record PolicyMemberUpload
{
    public string Id { get; init; }
    public string PolicyId { get; init; }
    public string Path { get; init; }
    public PolicyMemberUploadStatus Status { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime? LastModifiedAt { get; init; }
}

public enum PolicyMemberUploadStatus
{
    REGISTERED          = 0,

    VALIDATING          = 100,
    VALIDATING_ERROR    = 101,
    VALIDATED           = 102,

    IMPORTING           = 200,
    IMPORTING_ERROR     = 201,
    IMPORTED            = 202,

    ERROR_REMOVING      = 300,
    ERROR_REMOVED       = 301,

    REVERSING           = 400,
    REVERSED            = 401,

    CANCELING           = 500,
    CANCELED            = 501,

    FAILED              = 600
}