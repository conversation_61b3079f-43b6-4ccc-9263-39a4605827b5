﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Binder;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Domain.Policies
{
    public interface IPolicyService
    {
        Task<IEnumerable<string>> GetIdsAsync(string tenantId, QueryArguments queryArguments);
        Task<IEnumerable<string>> GetIdsAsync(string tenantId, PolicyWhere where);
        Task<List<Policy>> GetAsync(string tenantId, QueryArguments queryArguments);
        Task<List<Policy>> GetAsync(string tenantId, PolicyWhere where);
        Task<Policy> GetAsync(string tenantId, string policyId, AsOf? asOf = null);
        Task<Result<bool>> CheckIssuedStatusAsync(string tenantId, PolicyWhere where);
        Task<Result> CheckForAddEndorsement(string tenantId, string type, PolicyWhere where);
        Task<List<EventLog>> GetEventsAsync(string tenantId, EventQuery query);
        Task<long> GetTotalCountAsync(string tenantId, PolicyWhere where);
        Task<IDictionary<string, Policy>> GetDictionaryAsync(string tenantId, PolicyWhere where);
        Task<ILookup<string, Policy>> GetLookupAsync(string tenantId, PolicyWhere policyWhere);

        [Obsolete("To change to getAsync")]
        Task<IEnumerable<Policy>> GetBeneficiariesOfAsync(string tenantId, IEnumerable<string> beneficiaryIds);
        [Obsolete("To change to getAsync")]
        Task<IEnumerable<Policy>> GetPayorsOfAsync(string tenantId, IEnumerable<string> payersIds);

        Task<ILookup<string, Policy>> GetBeneficiariesOfLookupAsync(string tenantId, IEnumerable<string> beneficiaryIds);
        Task<ILookup<string, Policy>> GetPayorsOfLookupAsync(string tenantId, IEnumerable<string> payersIds);
        Task<ILookup<string, Policy>> GetInsuredsOfLookupAsync(string tenantId, IEnumerable<string> insuredIds);
        Task<ILookup<string, Policy>> GetContractTerminatedsOfLookupAsync(string tenantId, IEnumerable<string> insuredIds);

        [Obsolete("old policy flow")]
        Task<Result<CreatedStatus>> AddOfferAsync(string tenantId, string quoteId, AddOfferCommand command);
        [Obsolete("old policy flow")]
        Task<Result> UpdateOfferAsync(string tenantId, string quoteId, UpdateOfferCommand command);
        [Obsolete("old policy flow")]
        Task<Result> RemoveOfferAsync(string tenantId, string policyId, RemoveCommand command);
        [Obsolete("old policy flow")]
        Task<Result> ConvertOfferAsync(string tenantId, string quoteId, ConvertOfferCommand command);
        [Obsolete("old policy flow")]
        Task<Result> AddLoadingToOfferAsync(string tenantId, string policyId, AddLoadingCommand command);
        [Obsolete("old policy flow")]
        Task<Result> UpdateLoadingOfOfferAsync(string tenantId, string policyId, UpdateLoadingCommand command);
        [Obsolete("old policy flow")]
        Task<Result> RemoveLoadingFromOfferAsync(string tenantId, string policyId, RemoveLoadingCommand command);
        [Obsolete("old policy flow")]
        Task<Result> AddDiscountToOfferAsync(string tenantId, string policyId, AddDiscountCommand command);
        [Obsolete("old policy flow")]
        Task<Result> UpdateDiscountOfOfferAsync(string tenantId, string policyId, UpdateDiscountCommand command);
        [Obsolete("old policy flow")]
        Task<Result> RemoveDiscountFromOfferAsync(string tenantId, string policyId, RemoveDiscountFromOfferCommand command);
        [Obsolete("old policy flow")]
        Task<Result> AddExclusionToOfferAsync(string tenantId, string policyId, AddExclusionCommand command);
        [Obsolete("old policy flow")]
        Task<Result> RemoveExclusionFromOfferAsync(string tenantId, string policyId, RemoveExclusionCommand command);

        Task<Result<PolicyStatus>> CreatePolicyAsync(string tenantId, CreatePolicyCommand command);
        Task<Result> UpdatePolicyAsync(string tenantId, string id, UpdatePolicyCommand command);

        Task<Result<CreatedStatus>> UpdatePolicyProductAsync(string tenantId, string policyId, UpdatePolicyProductCommand command);

        Task<Result> AddOtherHolderAsync(string tenantId, string policyId, AddEntityCommand command);
        Task<Result> RemoveOtherHolderAsync(string tenantId, string policyId, RemoveCommand command);

        Task<Result> AddInsuredAsync(string tenantId, string policyId, AddEntityCommand command);
        Task<Result> RemoveInsuredAsync(string tenantId, string policyId, RemoveCommand command);

        Task<Result> UpdateContractHolderIndividualAsync(string tenantId, string policyId, UpdateIndividualCommand command);
        Task<Result> UpdateContractHolderCompanyAsync(string tenantId, string policyId, UpdateCompanyCommand command);
        Task<Result> UpdateContractInsuredIndividualAsync(string tenantId, string policyId, UpdateIndividualCommand command);
        Task<Result> UpdateContractInsuredCompanyAsync(string tenantId, string policyId, UpdateCompanyCommand command);
        Task<Result> UpdateContractInsuredObjectAsync(string tenantId, string policyId, UpdateObjectCommand command);

        Task<Result<PolicyStatus>> IssuePolicyAsync(string tenantId, IssuePolicyCommand command);
        Task<Result> CancelPolicyAsync(string tenantId, string policyId, CancelPolicyCommand command);
        Task<Result> RejectPolicyAsync(string tenantId, string policyId, RejectQuoteCommand command);
        Task<Result> DeletePolicyAsync(string tenantId, string id, string deletedById);

        Task<Result> AddAttachmentAsync(string tenantId, string policyId, AddAttachmentCommand command);
        Task<Result> UpdateAttachmentAsync(string tenantId, string policyId, UpdateAttachmentCommand command);
        Task<Result> RemoveAttachmentAsync(string tenantId, string policyId, RemoveAttachmentCommand command);
        Task<Result> AddNoteAsync(string tenantId, string policyId, AddNoteCommand command);
        Task<Result> UpdateNoteAsync(string tenantId, string policyId, UpdateNoteCommand command);
        Task<Result> RemoveNoteAsync(string tenantId, string policyId, RemoveCommand command);

        Task<Result> AddBeneficiaryEligibilityAsync(string tenantId, string policyId, AddBeneficiaryEligibilityCommand command);
        Task<Result> UpdateBeneficiaryEligibilityAsync(string tenantId, string policyId, UpdateBeneficiaryEligibilityCommand command);
        Task<Result> RemoveBeneficiaryEligibilityAsync(string tenantId, string policyId, RemoveCommand command);

        Task<Result> UpsertBenefitOptionAsync(string tenantId, string policyId, UpsertBenefitOptionCommand command);
        Task<Result> RemoveBenefitOptionAsync(string tenantId, string policyId, RemoveBenefitOptionCommand command);
        Task<Result> UpsertBenefitOptionBatchAsync(string tenantId, string policyId, UpsertBenefitOptionCommandBatch command);

        Task<Result> AddPaymentInfoAsync(string tenantId, string policyId, AddPaymentInfoCommand command);
        Task<Result> UpdatePaymentInfoAsync(string tenantId, string policyId, UpdatePaymentInfoCommand command);
        Task<Result> RemovePaymentInfoAsync(string tenantId, string policyId, RemoveCommand command);

        Task<Result> AddCommissionAsync(string tenantId, string policyId, AddCommissionCommand command);
        Task<Result> UpdateCommissionAsync(string tenantId, string policyId, UpdateCommissionCommand command);
        Task<Result> RemoveCommissionAsync(string tenantId, string policyId, RemoveCommissionCommand command);

        Task<IEnumerable<PolicyUpdateRequest>> GetUpdateRequestsAsync(string tenantId, PolicyUpdateRequestFilter filter);
        Task<ILookup<string, PolicyUpdateRequest>> GetUpdateRequestLookupAsync(string tenantId, PolicyUpdateRequestFilter filter);
        Task<IDictionary<string, PolicyUpdateRequest>> GetUpdateRequestDictionaryAsync(string tenantId, PolicyUpdateRequestFilter filter);
        Task<Result<CreatedStatus>> CreateUpdateRequestAsync(string tenantId, string policyId, CreatePolicyUpdateRequestCommand command);
        Task<Result> UpdatePolicyAsync(string tenantId, string id, AddUpdatePolicyCommandToRequestCommand command);
        Task<Result> UpsertBenefitOptionAsync(string tenantId, string policyId, AddUpsertBenefitOptionCommandToRequestCommand command);
        Task<Result> UpdateBeneficiaryEligibilityAsync(string tenantId, string policyId, AddUpdateBeneficiaryEligibilityCommandToRequestCommand command);
        Task<Result> UpdatePaymentInfoAsync(string tenantId, string policyId, AddUpdatePaymentInfoCommandToRequestCommand command);
        Task<Result> ReviewUpdateRequestAsync(string tenantId, string policyId, string requestId, PolicyRequestReview review);
        Task<Result> RemoveUpdateRequestAsync(string tenantId, string policyId, string requestId, string removedById);
        [Obsolete("ValidatePolicy_2Async is deprecated, please use ValidatePolicyAsync instead.")]
        Task<Result> ValidatePolicy_2Async(string tenantId, string policyId);
        Task<ValidatePolicyResult> ValidatePolicyAsync(string tenantId, string policyId);
        Task<Result<string>> AddStakeholderToPolicyAsync(string tenantId, string policyId, AddStakeholderCommand command);
        Task<Result> UpdateStakeholderOfPolicyAsync(string tenantId, string policyId, UpdateStakeholderCommand command);
        Task<Result> RemoveStakeholderFromPolicyAsync(string tenantId, string policyId, RemoveStakeholderCommand command);

        Task<Result<string>> AddClauseToPolicyAsync(string tenantId, string policyId, AddClauseCommand command);
        Task<Result> UpdateClauseOfPolicyAsync(string tenantId, string policyId, UpdateClauseCommand command);
        Task<Result> RemoveClauseFromPolicyAsync(string tenantId, string policyId, RemoveClauseCommand command);

        Task<Result> AddAssociatedContractToPolicyAsync(string tenantId, string policyId, AddAssociatedContractCommand command);
        Task<Result> RemoveAssociatedContractFromPolicyAsync(string tenantId, string policyId, RemoveAssociatedContractCommand command);

        Task<Result<CreatedStatus>> AddEndorsementAsync(string tenantId, string policyId, AddEndorsementCommand command);
        Task<Result> RemoveEndorsementAsync(string tenantId, string policyId, RemoveCommand command);
        Task<Result> AcceptEndorsementAsync(string tenantId, string policyId, AcceptEndorsementCommand command);
        Task<Result> UpdateEndorsementAsync(string tenantId, string policyId, UpdateEndorsementCommand command);
        Task<Result> RejectEndorsementAsync(string tenantId, string policyId, RejectEndorsementCommand command);

        Task<Policy> PreviewEndorsementAsync(string tenantId, string policyId, string endorsementId);

        Task<Result<CreatedStatus>> UpdatePolicy2Async(string tenantId, string policyId, UpdatePolicyCommand command);
        Task<Result<CreatedStatus>> AddDiscountAsync(string tenantId, string policyId, AddDiscountCommand command);
        Task<Result<CreatedStatus>> UpdateDiscountAsync(string tenantId, string policyId, UpdateDiscountCommand command);
        Task<Result<CreatedStatus>> RemoveDiscountAsync(string tenantId, string policyId, RemoveCommand command);

        Task<Result<CreatedStatus>> AddLoadingAsync(string tenantId, string policyId, AddLoadingCommand command);
        Task<Result<CreatedStatus>> UpdateLoadingAsync(string tenantId, string policyId, UpdateLoadingCommand command);
        Task<Result<CreatedStatus>> RemoveLoadingAsync(string tenantId, string policyId, RemoveCommand command);

        Task<Result<CreatedStatus>> AddExclusionAsync(string tenantId, string policyId, AddExclusionCommand command);
        Task<Result> RemoveExclusionAsync(string tenantId, string policyId, RemoveCommand command);

        Task<Result<CreatedStatus>> AddPolicyFactAsync(string tenantId, string policyId, AddFactCommand command);
        Task<Result<CreatedStatus>> UpdatePolicyFactAsync(string tenantId, string policyId, UpdateFactCommand command);
        Task<Result<CreatedStatus>> RemovePolicyFactAsync(string tenantId, string policyId, RemoveCommand command);
        Task<Result> PolicyFactBatch(string tenantId, string policyId, FactCommandBatch batch);
        Task<Result<CreatedStatus>> UpsertBenefitOptionOfPolicyAsync(string tenantId, string policyId, UpsertBenefitOptionCommand command);
        Task<Result<CreatedStatus>> RemoveBenefitOptionFromPolicyAsync(string tenantId, string policyId, RemoveBenefitOptionCommand command);

        Task<Result<CreatedStatus>> AddOtherContractHolderAsync(string tenantId, string policyId, AddEntityCommand command);
        Task<Result<CreatedStatus>> AddContractInsuredAsync(string tenantId, string policyId, AddEntityCommand command);
        Task<Result<CreatedStatus>> RemoveOtherContractHolderAsync(string tenantId, string policyId, RemoveCommand command);
        Task<Result<CreatedStatus>> RemoveContractInsuredAsync(string tenantId, string policyId, RemoveCommand command);

        Task<Result<CreatedStatus>> UpdateOtherContractHolderIndividualAsync(string tenantId, string policyId, UpdateIndividualCommand command);
        Task<Result<CreatedStatus>> UpdateContractInsuredIndividual2Async(string tenantId, string policyId, UpdateIndividualCommand command);

        Task<Result<CreatedStatus>> UpdateOtherContractHolderCompanyAsync(string tenantId, string policyId, UpdateCompanyCommand command);
        Task<Result<CreatedStatus>> UpdateContractInsuredCompany2Async(string tenantId, string policyId, UpdateCompanyCommand command);

        Task<Result<CreatedStatus>> UpdateContractInsuredObject2Async(string tenantId, string policyId, UpdateObjectCommand command);

        Task<Result> AddContractHolderFactBatch(string tenantId, string policyId, FactCommandBatch batch);
        Task<Result<CreatedStatus>> AddContractHolderFactAsync(string tenantId, string policyId, AddFactCommand command);
        Task<Result<CreatedStatus>> AddOtherContractHolderFactAsync(string tenantId, string policyId, AddFactCommand command);
        Task<Result<CreatedStatus>> AddContractInsuredFactAsync(string tenantId, string policyId, AddFactCommand command);
        Task<Result<CreatedStatus>> UpdateContractHolderFactAsync(string tenantId, string policyId, UpdateFactCommand command);
        Task<Result<CreatedStatus>> UpdateOtherContractHolderFactAsync(string tenantId, string policyId, UpdateFactCommand command);
        Task<Result<CreatedStatus>> UpdateContractInsuredFact2Async(string tenantId, string policyId, UpdateFactCommand command);
        Task<Result<CreatedStatus>> RemoveContractHolderFactAsync(string tenantId, string policyId, RemoveCommand command);
        Task<Result<CreatedStatus>> RemoveOtherContractHolderFactAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command);
        Task<Result<CreatedStatus>> RemoveContractInsuredFactAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command);

        Task<Result<CreatedStatus>> AddContractHolderIdentityAsync(string tenantId, string policyId, AddIdentityCommand command);
        Task<Result<CreatedStatus>> AddOtherContractHolderIdentityAsync(string tenantId, string policyId, AddIdentityCommand command);
        Task<Result<CreatedStatus>> AddContractInsuredIdentityAsync(string tenantId, string policyId, AddIdentityCommand command);
        Task<Result<CreatedStatus>> UpdateContractHolderIdentityAsync(string tenantId, string policyId, UpdateIdentityCommand command);
        Task<Result<CreatedStatus>> UpdateOtherContractHolderIdentityAsync(string tenantId, string policyId, UpdateIdentityCommand command);
        Task<Result<CreatedStatus>> UpdateContractInsuredIdentityAsync(string tenantId, string policyId, UpdateIdentityCommand command);
        Task<Result<CreatedStatus>> RemoveContractHolderIdentityAsync(string tenantId, string policyId, RemoveCommand command);
        Task<Result<CreatedStatus>> RemoveOtherContractHolderIdentityAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command);
        Task<Result<CreatedStatus>> RemoveContractInsuredIdentityAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command);

        Task<Result<CreatedStatus>> AddContractHolderContactAsync(string tenantId, string policyId, AddContactCommand command);
        Task<Result<CreatedStatus>> AddOtherContractHolderContactAsync(string tenantId, string policyId, AddContactCommand command);
        Task<Result<CreatedStatus>> AddContractInsuredContactAsync(string tenantId, string policyId, AddContactCommand command);
        Task<Result<CreatedStatus>> UpdateContractHolderContactAsync(string tenantId, string policyId, UpdateContactCommand command);
        Task<Result<CreatedStatus>> UpdateOtherContractHolderContactAsync(string tenantId, string policyId, UpdateContactCommand command);
        Task<Result<CreatedStatus>> UpdateContractInsuredContactAsync(string tenantId, string policyId, UpdateContactCommand command);
        Task<Result<CreatedStatus>> RemoveContractHolderContactAsync(string tenantId, string policyId, RemoveCommand command);
        Task<Result<CreatedStatus>> RemoveOtherContractHolderContactAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command);
        Task<Result<CreatedStatus>> RemoveContractInsuredContactAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command);

        Task<Result<CreatedStatus>> AddContractHolderAddressAsync(string tenantId, string policyId, AddAddressCommand command);
        Task<Result<CreatedStatus>> AddOtherContractHolderAddressAsync(string tenantId, string policyId, AddAddressCommand command);
        Task<Result<CreatedStatus>> AddContractInsuredAddressAsync(string tenantId, string policyId, AddAddressCommand command);
        Task<Result<CreatedStatus>> UpdateContractHolderAddressAsync(string tenantId, string policyId, UpdateAddressCommand command);
        Task<Result<CreatedStatus>> UpdateOtherContractHolderAddressAsync(string tenantId, string policyId, UpdateAddressCommand command);
        Task<Result<CreatedStatus>> UpdateContractInsuredAddressAsync(string tenantId, string policyId, UpdateAddressCommand command);
        Task<Result<CreatedStatus>> RemoveContractHolderAddressAsync(string tenantId, string policyId, RemoveCommand command);
        Task<Result<CreatedStatus>> RemoveOtherContractHolderAddressAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command);
        Task<Result<CreatedStatus>> RemoveContractInsuredAddressAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command);

        Task<Result<CreatedStatus>> AddAddClauseToEndorsementAsync(string tenantId, string policyId, AddClauseCommand command);

        Task<Result> RemoveCommandFromEndorsementAsync(string tenantId, string policyId, RemoveCommand command);

        Task<Result> InitializeTenantAsync(string tenantId, InitializeTenantPoliciesCommand command);

        Task<Result> BatchIntegrateAsync(string tenantId, BatchIntegrateCommand command);

        Task<Result> TriggerIssuerFunctionAsync(string tenantId, string policyId, TriggerIssuerFunctionCommand command);


        Task<Result> PolicyJacketBatchAsync(string tenantId, string policyId,
            PolicyJacketInstanceBatchCommand instanceBatch);

        public Task<Result> RemoveJacketFromPolicyAsync(string tenantId, string policyId,
            RemovePolicyJacketInstanceCommand instanceCommand);

        Task<Result<CreatedStatus>> PolicyClauseBatchAsync(string tenantId, string policyId, ClauseBatchCommand batch);

        Task<Result<CreatedStatus>> AddTagToPolicyAsync(string tenantId, string policyId, AddTagCommand command);
        Task<Result> RemoveTagFromPolicyAsync(string tenantId, string policyId, RemoveCommand command);
    }

    public class BatchIntegrateCommand
    {
        public string IssuerId { get; set; }
        public DateTime From { get; set; }
        public DateTime To { get; set; }
    }

    public class TriggerIssuerFunctionCommand
    {
        public string Purpose { get; set; }
        public JToken Input { get; set; }
    }

    public class ProductIdToUpdate
    {
        public string Plan { get; set; }
        public bool IsPlanChanged { get; set; }
        public string Version { get; set; }
        public bool IsVersionChanged { get; set; }
        public string Type { get; set; }
        public bool IsTypeChanged { get; set; }
    }

    public class ValidatePolicyResult : Result
    {
        public string PolicyNumber { get; set; }
        public string PlanName { get; set; }
    }
}