﻿using CoverGo.Gateway.Domain.Users;

namespace CoverGo.Gateway.Domain.Policies
{

    public class BeneficiaryEligibility : SystemObject
    {
        public string Id { get; set; }
        public Entity ContractEntity { get; set; }
        public string BenefitTypeId { get; set; }
        public decimal Ratio { get; set; }
        public string Notes { get; set; }
        public bool IsRevocable { get; set; }
    }

    public class AddBeneficiaryEligibilityCommand : BeneficiaryEligibilityCommand
    {
        public string AddedById { get; set; }
        public decimal Flat { get; set; }  //ToDo: not accepted in input, not in output object
        public CurrencyCode CurrencyCode { get; set; } //ToDo: not accepted in input, not in output object
    }

    public class UpdateBeneficiaryEligibilityCommand : BeneficiaryEligibilityCommand
    {
        public bool IsEntityIdChanged { get; set; }
        public bool IsBenefitTypeIdChanged { get; set; }
        public bool IsRatioChanged { get; set; }
        public bool IsNotesChanged { get; set; }
        public bool IsIsRevocableChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AddUpdateBeneficiaryEligibilityCommandToRequestCommand
    {
        public string RequestId { get; set; }
        public UpdateBeneficiaryEligibilityCommand Command { get; set; }
    }

    public class BeneficiaryEligibilityCommand
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public string BenefitTypeId { get; set; }
        public decimal Ratio { get; set; }
        public string Notes { get; set; }
        public bool IsRevocable { get; set; }
    }
}
