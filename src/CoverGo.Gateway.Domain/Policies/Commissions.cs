﻿namespace CoverGo.Gateway.Domain.Policies
{
    public class Commission : SystemObject
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public string JsonRule { get; set; }
        public decimal? Amount { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public string Remark { get; set; }
    }

    public class AddCommissionCommand
    {
        public string ProposalId { get; set; } // Only for offers
        public string OfferId { get; set; } // only for offers
        public string AddedById { get; set; }
        public string EntityId { get; set; }
        public string JsonRule { get; set; }
        public decimal? Amount { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public string Remark { get; set; }
    }

    public class UpdateCommissionCommand
    {
        public string ProposalId { get; set; } // Only for offers
        public string OfferId { get; set; } // only for offers
        public string ModifiedById { get; set; }
        public string Id { get; set; }
        public string JsonRule { get; set; }
        public bool IsJsonRuleChanged { get; set; }
        public string EntityId { get; set; }
        public bool IsEntityIdChanged { get; set; }
        public decimal? Amount { get; set; }
        public bool IsAmountChanged { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public bool IsCurrencyCodeChanged { get; set; }
        public string Remark { get; set; }
        public bool IsRemarkChanged { get; set; }
    }

    public class RemoveCommissionCommand
    {
        public string ProposalId { get; set; } // Only for offers
        public string OfferId { get; set; } // only for offers
        public string RemovedById { get; set; }
        public string Id { get; set; }
    }
}
