﻿using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Domain.Users;

using System;
using System.Collections.Generic;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Domain.Policies
{
    public class Transaction : SystemObject
    {
        public string Id { get; set; }
        public string TransactionNumber { get; set; }
        public string PolicyId { get; set; }
        public string EndorsementId { get; set; }
        public string ClaimId { get; set; }
        public string ProposalId { get; set; }
        public string SubscriptionId { get; set; }
        public string Description { get; set; }
        public DateTime? DateTime { get; set; }
        public DateTime? PostDateTime { get; set; }
        public DateTime? DueDateTime { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public decimal? Amount { get; set; }
        public string Type { get; set; }
        public string AccountType { get; set; }
        public string AccountCode { get; set; }
        public string FromAccountId { get; set; }
        public string ToAccountId { get; set; }
        public TransactionStatus? Status { get; set; }
        public PaymentMethodType? Method { get; set; }

        public string ProviderTransactionId { get; set; }

        public IEnumerable<Note> Notes { get; set; }
        public IEnumerable<Fact> Facts { get; set; }
        public IEnumerable<Stakeholder> Stakeholders { get; set; }

        public IEnumerable<Attachment> Attachments { get; set; }
        public string SettledToTransactionId { get; set; }
        public string DebitNoteNumber { get; set; }
        public JToken Fields { get; set; }
    }

    public enum TransactionStatus
    {
        Undefined,
        NotStarted,
        Pending,
        Approved,
        Rejected,
        Timeout,
        CancelledByUser,
        Refunded,
        Accepted,
        Succeed,
        Failed
    }

    public class ProcessTransactionCommand
    {
        public string Remark { get; set; }
        public string ReferralId { get; set; }
        public string ProcessedById { get; set; }

        public string ProviderToken { get; set; }
        public TransactionStatus? Status { get; set; }
    }

    public class CreateTransactionCommand
    {
        public string TransactionId { get; set; }
        public string TransactionNumber { get; set; }
        public decimal Amount { get; set; }
        public string Type { get; set; }
        public string Description { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public string PolicyId { get; set; }
        public string ClaimId { get; set; }
        public string ProposalId { get; set; }
        public string EndorsementId { get; set; }
        public string SubscriptionId { get; set; }

        public TransactionStatus Status { get; set; }
        public PaymentMethodType? Method { get; set; }
        public DateTime? DateTime { get; set; }
        public DateTime? DueDateTime { get; set; }
        public DateTime? PostDateTime { get; set; }
        public ExternalRef ExternalRef { get; set; }
        public string CreatedById { get; set; }
        public string SettledToTransactionId { get; set; }
        public string DebitNoteNumber { get; set; }
        public string Fields { get; set; }
    }

    public class UpdateTransactionCommand
    {
        public string TransactionId { get; set; }
        public string TransactionNumber { get; set; }
        public bool IsTransactionNumberChanged { get; set; }
        public string PolicyId { get; set; }
        public bool IsPolicyIdChanged { get; set; }
        public string EndorsementId { get; set; }
        public bool IsEndorsementIdChanged { get; set; }
        public string ClaimId { get; set; }
        public bool IsClaimIdChanged { get; set; }
        public string ProposalId { get; set; }
        public bool IsProposalIdChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public double? Amount { get; set; }
        public bool IsAmountChanged { get; set; }
        public string Type { get; set; }
        public bool IsTypeChanged { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public bool IsCurrencyCodeChanged { get; set; }
        public TransactionStatus? Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public PaymentMethodType? Method { get; set; }
        public bool IsMethodChanged { get; set; }
        public DateTime? DateTime { get; set; }
        public bool IsDateTimeChanged { get; set; }
        public DateTime? DueDateTime { get; set; }
        public bool IsDueDateTimeChanged { get; set; }
        public bool IsPostDateTimeChanged { get; set; }
        public DateTime? PostDateTime { get; set; }
        public string ProviderTransactionId { get; set; } //for automated subscription payments
        public bool IsProviderTransactionIdChanged { get; set; } //for automated subscription payments
        public string ProviderId { get; set; } //for automated subscription payments
        public bool IsProviderIdChanged { get; set; } //for automated subscription payments
        public ExternalRefToUpdate ExternalRef { get; set; }
        public bool IsExternalRefChanged { get; set; }
        public string ModifiedById { get; set; }
        public bool IsSettledToTransactionIdChanged { get; set; }
        public string SettledToTransactionId { get; set; }
        public string DebitNoteNumber { get; set; }
        public bool IsDebitNoteNumberChanged { get; set; }
        public string Fields { get; set; }
        public bool IsFieldsChanged { get; set; }
    }

    public class InitializeTransactionCommand
    {
        public string TransactionNumber { get; set; }
        public decimal Amount { get; set; }
        public string Description { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public PaymentMethodType Method { get; set; }
        public string PolicyId { get; set; }
        public string ProposalId { get; set; }
        public string RedirectToUrl { get; set; }
        public string ProviderConfigId { get; set; }
        public string InitializedById { get; set; }
    }

    public class DeleteTransactionCommand
    {
        public string TransactionId { get; set; }
        public string DeletedById { get; set; }
    }

    public class StripeConnectCommand
    {
        public string TenantId { get; set; }
        public string AuthorizationCode { get; set; }
    }

    public class RefundCommand
    {
        public string TransactionIdToRefund { get; set; }
        public string PolicyId { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public decimal? Amount { get; set; }
        public string Remark { get; set; }
        public string RefundedById { get; set; }
    }

    public class TransactionWhere : Where
    {
        public List<TransactionWhere> Or { get; set; }
        public List<TransactionWhere> And { get; set; }

        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string TransactionNumber { get; set; }
        public string TransactionNumber_contains { get; set; }
        public string StakeholderIds_contains { get; set; }
        public TransactionStatus? Status { get; set; }
        public string PolicyId { get; set; }
        public List<string> PolicyId_in { get; set; }
        public string EndorsementId { get; set; }
        public List<string> EndorsementId_in { get; set; }
        public string ClaimId { get; set; }
        public List<string> ClaimId_in { get; set; }
        public string ProposalId { get; set; }
        public List<string> ProposalId_in { get; set; }
        public string SubscriptionId { get; set; }
        public List<string> SubscriptionId_in { get; set; }
        public DateTime? DateTime_gt { get; set; }
        public DateTime? DateTime_lt { get; set; }
        public string Type { get; set; }
        public string PaymentProviderId { get; set; }
        public string PaymentId { get; set; }
        public string SettledToTransactionId { get; set; }
        public bool? IsSettled { get; set; }
        public string DebitNoteNumber { get; set; }
        public bool? HasDebitNoteNumber { get; set; }
        public FieldsWhere Fields { get; set; }
    }
}
