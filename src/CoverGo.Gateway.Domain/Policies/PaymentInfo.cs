﻿using System;

namespace CoverGo.Gateway.Domain.Policies
{
    public class PaymentInfo : SystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string PayorId { get; set; }
        public PaymentFrequency Frequency { get; set; }
        public PaymentMethodType Method { get; set; }
        public decimal Amount { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Comment { get; set; }
    }

    public class AddPaymentInfoCommand : PaymentInfoCommand
    {
        public string AddedById { get; set; }
    }

    public class UpdatePaymentInfoCommand : PaymentInfoCommand
    {
        public bool IsNameChanged { get; set; }
        public bool IsPayorIdChanged { get; set; }
        public bool IsFrequencyChanged { get; set; }
        public bool IsMethodChanged { get; set; }
        public bool IsAmountChanged { get; set; }
        public bool IsCurrencyCodeChanged { get; set; }
        public bool IsStartDateChanged { get; set; }
        public bool IsEndDateChanged { get; set; }
        public bool IsCommentChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AddUpdatePaymentInfoCommandToRequestCommand
    {
        public string RequestId { get; set; }
        public UpdatePaymentInfoCommand Command { get; set; }
    }

    public class PaymentInfoCommand
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string PayorId { get; set; }
        public PaymentFrequency Frequency { get; set; }
        public PaymentMethodType Method { get; set; }
        public decimal Amount { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Comment { get; set; }
    }

    public enum PaymentFrequency
    {
        OneTime,
        Monthly,
        Quarterly,
        SemiAnnually,
        Annually
    }

    public enum PaymentMethodType
    {
        Cash,
        CreditCard,
        Giro,
        Atm,
        SelfServicePaymentMachine,
        BankTransfer,
        Cheque,
        Cpf, // Central Provident Fund, a Singaporean social security fund, used by aag
        Mada,
        Sadad,
        DigitalWallet,
        Tabby,
    }
}
