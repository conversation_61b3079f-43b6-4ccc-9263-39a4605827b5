﻿using CoverGo.DomainUtils;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Domain.FileSystem
{
    public interface IFileSystemService
    {
        Task<Result> InitializeTenantAsync(string tenantId, InitializeTenantFileSystemCommand command);
        Task<Result<IEnumerable<ObjectSummary>>> QueryAsync(string tenantId, string bucketName, FileWhere where);
        Task<Result> UploadFileAsync(string tenantId, string bucketName, UploadFileCommand command);
        Task<Result<ObjectListing>> ListAsync(string tenantId, string bucketName, ListFilesCommand command);
        Task<Result<byte[]>> GetFileAsync(string tenantId, string bucketName, GetFileCommand command);
        Task<Result> CopyFileAsync(string tenantId, string bucketName, CopyFileCommand command);
        Task<Result> DeleteFileAsync(string tenantId, string bucketName, DeleteFileCommand command);
        Task<IEnumerable<FileSystemConfig>> GetConfigsAsync(string tenantId, FileSystemConfigWhere where);
        Task<IEnumerable<FileSystemConfig>> GetConfigsAsync(string tenantId, QueryArguments queryArguments);
        Task<Result<CreatedStatus>> CreateConfigAsync(string tenantId, CreateFileSystemConfigCommand command);
        Task<Result> UpdateConfigAsync(string tenantId, string id, UpdateFileSystemConfigCommand config);
        Task<Result> DeleteConfigAsync(string tenantId, string id, DeleteCommand command);

        Task<Result> CreateFolderAsync(string tenantId, string bucketName, CreateFolderCommand command);
        Task<Result> DeleteFolderAsync(string tenantId, string bucketName, DeleteFolderCommand command);
        Task<Result<bool>> IsPublicAsync(string tenantId, string bucketName, IsPublicCommand command);
        Task<Result> SetAclAsync(string tenantId, string bucketName, SetAclCommand command);
        Task<Result<string>> GenerateSharableUrlAsync(string tenantId, string bucketName, GenerateSharableUrlCommand command);
        Task<Result> AddMetadataToFileAsync(string tenantId, string bucketName, AddMetadataToFileCommand command);
        Task<Result> RemoveMetaDataFromFileAsync(string tenantId, string bucketName, RemoveMetadataFromFileCommand command);
        Task<Result> LockFileAsync(string tenantId, string bucketName, LockFileCommand command);
        Task<Result> UnlockFileAsync(string tenantId, string bucketName, UnlockFileCommand command);
    }

    public class ObjectListing
    {
        public IEnumerable<string> CommonPrefixes { get; set; }
        public IEnumerable<ObjectSummary> ObjectSummaries { get; set; }
        public string ContinuationToken { get; set; }
        public int TotalCount { get; set; }
    }

    public class ObjectSummary
    {
        public string Key { get; set; }
        public string FileName { get; set; }
        public long Size { get; set; }
        public bool IsPublic { get; set; }
        public DateTime LastModifiedAt { get; set; }
        public IEnumerable<KeyScalarValue> Metadata { get; set; }

        public string BucketName { get; set; } //used in graph for resolving 'isPublic' field
    }

    public class UploadFileCommand
    {
        public string Key { get; set; }
        public byte[] Content { get; set; }
        public bool IsPublic { get; set; }
        public TimeSpan? Ttl { get; set; }
    }

    public class ListFilesCommand
    {
        public string Path { get; set; }
        public IEnumerable<string> AllowedPrefixes { get; set; }
        public bool IsPublic { get; set; }
        public string ContinuationToken { get; set; }
        public int Limit { get; set; }
        public int Skip { get; set; }
        public String SortBy { get; set; }
        public String FilterByFileName { get; set; }
    }

    public class GetFileCommand
    {
        public string Key { get; set; }
        public bool IsPublic { get; set; }
    }

    public class CopyFileCommand
    {
        public string Key { get; set; }
        public string NewKey { get; set; }
    }

    public class DeleteFileCommand
    {
        public string Key { get; set; }
        public IEnumerable<string> Keys { get; set; }
    }

    public class CreateFolderCommand
    {
        public string Key { get; set; }
    }

    public class DeleteFolderCommand
    {
        public string Key { get; set; }
    }

    public class IsPublicCommand
    {
        public string Key { get; set; }
    }

    public class SetAclCommand
    {
        public string Key { get; set; }
        public bool IsPublic { get; set; }
    }

    public class FileWhere : Where
    {
        public string KeyPattern { get; set; }
        public IEnumerable<string> AllowedPrefixes { get; set; }
    }

    public class GenerateSharableUrlCommand
    {
        public string Key { get; set; }
        public double? ExpiresIn { get; set; }
    }

    public class AddMetadataToFileCommand
    {
        public AddMetadataToFileCommand(string key, Dictionary<string, string> metadata)
        {
            Key = key;
            Metadata = metadata;
        }
        public AddMetadataToFileCommand()
        {
        }

        public string Key { get; set; }
        public Dictionary<string, string> Metadata { get; set; }
    }

    public class RemoveMetadataFromFileCommand
    {
        public RemoveMetadataFromFileCommand(string key, IEnumerable<string> metadataKeys)
        {
            Key = key;
            MetadataKeys = metadataKeys;
        }
        public RemoveMetadataFromFileCommand()
        {
        }

        public string Key { get; set; }
        public IEnumerable<string> MetadataKeys { get; set; }
    }

    public class LockFileCommand
    {
        public LockFileCommand(IEnumerable<string> keys)
        {
            Keys = keys;
        }

        public LockFileCommand()
        {
        }

        public IEnumerable<string> Keys { get; set; }
    }

    public class UnlockFileCommand
    {
        public UnlockFileCommand(IEnumerable<string> keys)
        {
            Keys = keys;
        }

        public UnlockFileCommand()
        {
        }

        public IEnumerable<string> Keys { get; set; }
    }
}
