﻿using System;

namespace CoverGo.Gateway.Domain.Scheduler
{
    public class JobSchedule : SystemObject
    {
        public string Id { get; set; }
        public string TenantId { get; set; }
        public string DatacenterId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string CronExpression { get; set; }
        public bool IsActive { get; set; }
        public DateTime? LastCheckedAt { get; set; }

        public JobDetail JobDetail { get; set; }
    }
}