﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Achievements.Achievements;
using CoverGo.Gateway.Domain.Achievements.AchievementTypes;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Domain.Achievements
{
    public interface IAchievementService
    {
        Task<long> GetAchievementTypesTotalCountAsync(string tenantId, AchievementTypeWhere where);
        Task<IEnumerable<AchievementType>> GetAchievementTypesAsync(string tenantId, QueryArguments queryArguments);
        Task<Result<CreatedStatus>> CreateAchievementTypeAsync(string tenantId, CreateAchievementTypeCommand command);
        Task<Result> UpdateAchievementTypeAsync(string tenantId, string AchievementTypeId, UpdateAchievementTypeCommand command);
        Task<Result> DeleteAchievementTypeAsync(string tenantId, string AchievementTypeId, DeleteCommand command);
        Task<Result> ComputeAchievementTypesAsync(string tenantId, ComputeAchievementTypesCommand command);

        Task<long> GetAchievementsTotalCountAsync(string tenantId, AchievementWhere where);
        Task<IEnumerable<Achievement>> GetAchievementsAsync(string tenantId, QueryArguments queryArguments);
        Task<ILookup<string, Achievement>> GetAchievementsByEntityIdsLookupAsync(string tenantId, AchievementWhere where);

        Task<Result<CreatedStatus>> CreateAchievementAsync(string tenantId, CreateAchievementCommand command);
        Task<Result> UpdateAchievementAsync(string tenantId, string achievementId, UpdateAchievementCommand command);
        Task<Result> DeleteAchievementAsync(string tenantId, string achievementId, DeleteCommand command);
    }
}
