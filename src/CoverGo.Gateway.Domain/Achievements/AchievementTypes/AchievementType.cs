﻿using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Domain.Achievements.AchievementTypes
{

    public class AchievementType : SystemObject
    {
        public string Id { get; set; }
        public AchievementLogic Logic { get; set; }
        public string Type { get; set; }
        public string AsLoginId { get; set; }
    }

    public class AchievementLogic
    {
        public List<AchievementLogic> And { get; set; }
        public List<AchievementLogic> Or { get; set; }
        public string GraphQlQuery { get; set; }
        public string LiquidCondition { get; set; }
    }

    public class CreateAchievementTypeCommand
    {
        public string Id { get; set; }
        public AchievementLogic Logic { get; set; }
        public string CreatedById { get; set; }
        public string Type { get; set; }
        public string AsLoginId { get; set; }
        public string AsAppId { get; set; }
    }

    public class UpdateAchievementTypeCommand
    {
        public AchievementLogic Logic { get; set; }
        public bool IsLogicChanged { get; set; }
        public string Type { get; set; }
        public bool IsTypeChanged { get; set; }
        public string ModifiedById { get; set; }
        public bool IsAsLoginIdChanged { get; set; }
        public string AsLoginId { get; set; }
    }

    public class AchievementTypeWhere : Where
    {
        public List<AchievementTypeWhere> Or { get; set; }
        public List<AchievementTypeWhere> And { get; set; }
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string Type { get; set; }
        public List<string> Type_in { get; set; }
    }

    public class ComputeAchievementTypesCommand
    {
        public string EntityId { get; set; }
    }
}
