﻿using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Domain.Achievements.Achievements
{
    public class Achievement : SystemObject
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public string AchievementTypeId { get; set; }
    }
    public class CreateAchievementCommand
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public string AchievementTypeId { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateAchievementCommand
    {
        public string EntityId { get; set; }
        public bool IsEntityIdChanged { get; set; }
        public string AchievementTypeId { get; set; }
        public bool IsAchievementTypeIdChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AchievementWhere : Where
    {
        public List<AchievementWhere> Or { get; set; }
        public List<AchievementWhere> And { get; set; }
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string EntityId { get; set; }
        public List<string> EntityId_in { get; set; }
        public string AchievementTypeId { get; set; }
        public List<string> AchievementTypeId_in { get; set; }
    }
}
