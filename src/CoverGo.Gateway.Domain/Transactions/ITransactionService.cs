﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Users;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Domain.Transactions
{
    public interface ITransactionService
    {
        Task<List<Transaction>> GetAsync(string tenantId, QueryArguments queryArguments);
        Task<IEnumerable<EventLog>> GetEventsAsync(string tenantId, EventQuery eventQuery);
        Task<long> GetTotalCountAsync(string tenantId, TransactionWhere where);

        Task<PaymentConfig> GetPaymentConfigAsync(string tenantId, string id);

        Task<Result<CreatedStatus>> CreateAsync(string tenantId, CreateTransactionCommand command, string accessToken = null);
        Task<Result> ProcessAsync(string tenantId, string transactionId, ProcessTransactionCommand command);
        Task<Result<TransactionRedirection>> InitializeAsync(string tenantId, InitializeTransactionCommand command);
        Task<Result> UpdateAsync(string tenandId, UpdateTransactionCommand command);
        Task<Result> DeleteAsync(string tenandId, DeleteTransactionCommand command);
        Task<Result> RefundAsync(string tenantId, RefundCommand command);
        Task<Result> SetStripeDestinationUserIdAsync(string tenantId, string stripeDestinationUserId);
        Task<ILookup<string, Transaction>> GetByPolicyIdsLookupAsync(string tenantId, TransactionWhere where);
        Task<ILookup<string, Transaction>> GetByEndorsementIdsLookupAsync(string tenantId, TransactionWhere where);
        Task<ILookup<string, Transaction>> GetByClaimIdsLookupAsync(string tenantId, TransactionWhere where);
        Task<ILookup<string, Transaction>> GetByProposalIdsLookupAsync(string tenantId, TransactionWhere where);
        Task<ILookup<string, Transaction>> GetBySubscriptionIdsLookupAsync(string tenantId, TransactionWhere where);

        Task<Result<CreatedStatus>> AddFactAsync(string tenantId, string transactionId, AddFactCommand command);
        Task<Result> UpdateFactAsync(string tenantId, string transactionId, UpdateFactCommand command);
        Task<Result> RemoveFactAsync(string tenantId, string transactionId, RemoveFactCommand command);

        Task<Result> AddNoteAsync(string tenantId, string transactionId, AddNoteCommand command);
        Task<Result> UpdateNoteAsync(string tenantId, string transactionId, UpdateNoteCommand command);
        Task<Result> RemoveNoteAsync(string tenantId, string transactionId, RemoveNoteCommand command);

        Task<Result<string>> AddStakeholderToTransactionAsync(string tenantId, string transactionId, AddStakeholderCommand command);
        Task<Result> UpdateStakeholderOfTransactionAsync(string tenantId, string transactionId, UpdateStakeholderCommand command);
        Task<Result> RemoveStakeholderFromTransactionAsync(string tenantId, string transactionId, RemoveStakeholderCommand command);

        Task<IEnumerable<PaymentMethod>> GetPaymentMethodsAsync(string tenantId, QueryArguments where);
        Task<IDictionary<string, PaymentMethod>> GetPaymentMethodDictionaryAsync(string tenantId, PaymentMethodWhere where);
        Task<ILookup<string, PaymentMethod>> GetPaymentMethodsByEntityIdsLookupAsync(string tenantId, PaymentMethodWhere where);
        Task<Result<CreatedStatus>> CreatePaymentMethodFromTokenAsync(string tenantId, CreatePaymentMethodFromTokenCommand command);
        Task<Result<CreatedStatus>> CreateCardPaymentMethodAsync(string tenantId, CreateCardPaymentMethodCommand command);
        Task<Result<CreatedStatus>> CreateBankPaymentMethodAsync(string tenantId, CreateBankPaymentMethodCommand command);
        Task<Result> DeletePaymentMethodAsync(string tenantId, string id, DeleteCommand command);

        Task<IEnumerable<Subscription>> GetSubscriptionsAsync(string tenantId, QueryArguments where);
        Task<IDictionary<string, Subscription>> GetSubscriptionsDictionaryAsync(string tenantId, SubscriptionWhere where);
        Task<ILookup<string, Subscription>> GetSubscriptionsByEntityIdsLookupAsync(string tenantId, SubscriptionWhere where);
        Task<Result<CreatedStatus>> CreateSubscriptionAsync(string tenantId, CreateSubscriptionCommand command);
        Task<Result> UpdateSubscriptionAsync(string tenantId, string subscriptionId, UpdateSubscriptionCommand command);
        Task<Result> AddOfferToSubscriptionAsync(string tenantId, string subscriptionId, AddOfferCommand command);
        Task<Result> UpdateOfferOfSubscriptionAsync(string tenantId, string subscriptionId, UpdateOfferCommand command);
        Task<Result> RemoveOfferFromSubscriptionAsync(string tenantId, string subscriptionId, RemoveCommand command);
        Task<Result> CancelSubscriptionAsync(string tenantId, string id, CancelSubscriptionCommand command);
        Task<Result> DeleteSubscriptionAsync(string tenantId, string id, DeleteCommand command);
        //Task<Result> TransferFromTransactionAsync(string tenantId, string id, TransferFromTransactionCommand command); //TODO: implement when apeiron connects their stripe
        //Task<Result> RetrySubscriptionInvoicePaymentAsync(string tenantId, string id, RetrySubscriptionInvoicePaymentCommand command); //TODO: needs spike
        Task<Result> InitializeTenantAsync(string tenantId, InitializeTenantTransactionsCommand command);

        Task<Result> AddAttachment(string tenantId, string transactionId, AddAttachmentCommand command);

        Task<Result> RemoveAttachment(string tenantId, string transactionId, RemoveAttachmentCommand command);
    }

    public class PaymentConfig
    {
        public string ProviderId { get; set; }
        public string StripeDestinationUserId { get; set; }
        public string StripeApiTestKey { get; set; }
        public string StripeApiLiveKey { get; set; }
        public string StripePublicTestKey { get; set; }
        public string StripePublicLiveKey { get; set; }
        public string StripeTestWebHookKey { get; set; }
        public string StripeLiveWebHookKey { get; set; }
        public decimal? StripeDestinatonFeeFlat { get; set; }
        public decimal? StripeDestinatonFeeRatio { get; set; }
        public long? JetcoMerchantId { get; set; }
        public string JetcoMicroserviceBaseUrl { get; set; }
        public string DokuSharedKey { get; set; }
        public int DokuMerchantId { get; set; }
        public bool IsLiveMode { get; set; }
    }

    public class InitializeTenantTransactionsCommand
    {
        public PaymentConfig PaymentConfig { get; set; }
    }

    public class PaymentMethod : SystemObject
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public string Name { get; set; }
        public ExternalRef ExternalRef { get; set; }
        public string Type { get; set; } //only "card" for now

        public string Brand { get; set; } //only used for card
        public string Last4 { get; set; } //only used for card
        public int ExpMonth { get; set; } //only used for card
        public int ExpYear { get; set; } //only used for card
        public string CardholderName { get; set; } //only used for card
        public string BankName { get; set; } // only used for bank
        public string BranchCode { get; set; } // only used for bank
        public string BankNumber { get; set; } // only used for bank
        public string AccountHolderName { get; set; } // only used for bank
    }

    public class ExternalRef
    {
        public string ProviderConfigId { get; set; }
        public string Ref { get; set; }
    }

    public class ExternalRefToUpdate
    {
        public string ProviderConfigId { get; set; }
        public bool IsProviderConfigIdChanged { get; set; }
        public string Ref { get; set; }
        public bool IsRefChanged { get; set; }
    }

    public class PaymentMethodWhere : Where
    {
        public List<PaymentMethodWhere> Or { get; set; }
        public List<PaymentMethodWhere> And { get; set; }

        public string Id { get; set; }
        public List<string> Id_in { get; set; }

        public string EntityId { get; set; }
        public List<string> EntityId_in { get; set; }
    }

    public class CreatePaymentMethodFromTokenCommand
    {
        public string ProviderConfigId { get; set; }
        public string Token { get; set; }
        public string EntityId { get; set; }
        public string CreatedById { get; set; }
    }

    public class CreateCardPaymentMethodCommand
    {
        public string ProviderConfigId { get; set; }
        public string EntityId { get; set; }
        public string Name { get; set; }

        public string Brand { get; set; }
        public string Number { get; set; } //Note: not saved in events
        public string Last4 { get; set; } //Note: these are the last 4 digits of `number` that are saved
        public int ExpMonth { get; set; }
        public int ExpYear { get; set; }
        public string Cvc { get; set; } //Note: not saved in events
        public string CardholderName { get; set; }

        public ExternalRef ExternalRef { get; set; }

        public string CreatedById { get; set; }
    }

    public class CreateBankPaymentMethodCommand
    {
        public string EntityId { get; set; }

        public string BankName { get; set; }
        public string BranchCode { get; set; }
        public string BankNumber { get; set; }
        public string AccountHolderName { get; set; }
        public string CreatedById { get; set; }
    }

    public class Subscription : SystemObject
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public IEnumerable<Offer> Offers { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public Interval Interval { get; set; }
        public int? IntervalCount { get; set; }
        public string PaymentMethodId { get; set; }
        public ExternalRef ExternalRef { get; set; }
        public bool IsInvalidToBeProcessed { get; set; }
        public bool IsCancelled { get; set; }
        public string CancelledById { get; set; }
        public List<DateTime> PaymentDates { get; set; }
        public IEnumerable<SubscriptionDiscount> Discounts { get; set; }
    }
    public class SubscriptionWhere : Where
    {
        public List<SubscriptionWhere> Or { get; set; }
        public List<SubscriptionWhere> And { get; set; }

        public string Id { get; set; }
        public List<string> Id_in { get; set; }

        public string EntityId { get; set; }
        public List<string> EntityId_in { get; set; }

        public ExternalRef ExternalRef { get; set; }
    }

    public class CreateSubscriptionCommand
    {
        public string ProviderConfigId { get; set; }
        public string EntityId { get; set; }
        public IEnumerable<Offer> Offers { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public Interval Interval { get; set; }
        public int? IntervalCount { get; set; }
        public string PaymentMethodId { get; set; }
        public ExternalRef ExternalRef { get; set; }
        public  string? StripeCustomerId { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateSubscriptionCommand
    {
        public DateTime? EndDate { get; set; }
        public bool IsEndDateChanged { get; set; }
        public string PaymentMethodId { get; set; }
        public bool IsPaymentMethodIdChanged { get; set; }
        public bool IsInvalidToBeProcessed { get; set; }
        public bool IsIsInvalidToBeProcessedChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class CancelSubscriptionCommand
    {
        public string ProviderConfigId { get; set; }
        public string CancelledById { get; set; }
    }

    //public class TransferFromTransactionCommand
    //{
    //    public string ProviderConfigId { get; set; }
    //    public string TransferredById { get; set; }
    //}

    //public class RetrySubscriptionInvoicePaymentCommand
    //{
    //    public string RetriedById { get; set; }
    //}

    public class SubscriptionDiscount
    {
        public DateTime Date { get; set; }
        public decimal DiscountAmount { get; set; }
        public Interval Interval { get; set; }
    }

    public enum Interval
    {
        day,
        week,
        month,
        year
    }
}
