using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.Templates;

namespace CoverGo.Gateway.Domain.Notifications
{
    public static class SendNotificationCommandExtensions
    {
        public static List<string> PopulatePermissionTargetIds(
            this SendNotificationCommand command, Login loginUser, IEnumerable<string> allowedIds)
        {
            var allowedIdsList = allowedIds.ToList();

            if (allowedIdsList.Contains("all")) return null;
            
            if (allowedIdsList.Contains("{templatesOnly}"))
            {
                if (command.EmailMessage?.Subject != null || command.EmailMessage?.HtmlContent != null)
                    return new List<string> { $"You are not authorized send customised message." };

                if (command.SmsMessage?.Body != null)
                    return new List<string> { $"You are not authorized send customised message." };
            }
            
            if (allowedIdsList.Contains("{notificationConfigOnly}"))
            {
                command.UseConfig = true;
                if (command.EmailMessage?.From != null || command.EmailMessage?.FromName != null)
                    return new List<string> { $"You are not authorized to define notification sender." };
            }

            if (allowedIdsList.Contains("{entityId}"))
            {
                if (command.ToEntityIds != null)
                    return new List<string> { $"You are not authorized send notifications to multiple entities." };
                if (command.ToEntityId != null && !allowedIdsList.Contains(command.ToEntityId))
                    return new List<string> { $"You are not authorized send notifications other than yourself." };
            }

            if (allowedIdsList.Contains("{email:{self}}") && loginUser?.Email != null) allowedIdsList.Add($"{{email:{loginUser.Email}}}");

            var allowedIdsForParticularEmailSending = 
                allowedIdsList
                    .Where(id => id != "{email:{self}}")
                    .Where(id => id.StartsWith("{email:") && id.EndsWith("}"))
                    .Select(id => id.ToLower())
                    .ToList();
            
            if (allowedIdsForParticularEmailSending.Any() && command.EmailMessage?.To != null)
            {
                // {email:<to-email>}
                // => we already filtered ids startsWith "{email:" and endsWith "}"
                // so the inputted email is only valid if any ids contains the email
                // and the length of email + the prefix and suffix of the id should be equals to the length of id
                // e.g {email:<EMAIL>} => the inputted email is valid if len(<inputted email>) + len("{email:}") == len("{email:<EMAIL>}")
                bool isAnyEmailAllowed = allowedIdsForParticularEmailSending.Any(id => 
                    id.Contains(command.EmailMessage.To.ToLower()) 
                    && id.Length == command.EmailMessage.To.Length + "{email:}".Length);
                if (!isAnyEmailAllowed)
                {
                    string errorMessage = allowedIdsList.Contains("{email:{self}}")
                                          && allowedIdsForParticularEmailSending.Count == 1
                        ? "You are not authorized send notifications other than yourself."
                        : "You are not authorized send notifications.";
                    return new List<string> { errorMessage };
                }
            }
                

            if (allowedIdsList.Contains("{sms:{self}}"))
                if (command.SmsMessage?.To != null && 
                    (loginUser?.TelephoneNumber == null || command.SmsMessage?.To != loginUser.TelephoneNumber))
                    return new List<string> { $"You are not authorized send notifications other than yourself."};
          
            return null;
        }

        public static async Task<Result> PopulateFileAttachments(this SendNotificationCommand command, string tenantId, IFileSystemService fileSystemService)
        {
            if (!(command?.EmailMessage?.Attachments?.Count > 0)) return Result.Success();

            command.EmailMessage.FileAttachments ??= new List<FileAttachment>();

            foreach (EmailMessageAttachment attachment in command.EmailMessage.Attachments)
            {
                Result<byte[]> result = await fileSystemService.GetFileAsync(tenantId, null, new GetFileCommand { Key = attachment.Key });
                if (!result.IsSuccess)
                    return new Result
                    {
                        Errors = result.Errors,
                        Errors_2 = result.Errors_2,
                        Status = result.Status
                    };

                command.EmailMessage.FileAttachments.Add(new FileAttachment
                {
                    Bytes = result.Value,
                    FileName = attachment.Name
                });
            }

            return Result.Success();
        }

        public static async Task<Result> PopulateTemplateIdParameters(this SendNotificationCommand command, string tenantId, ITemplateService templateService)
        {
            var errors = new List<string>();

            var templateRenderings = new[]
                    {
                        command.EmailMessage?.TemplateRendering,
                        command.InternalMessage?.TemplateRendering,
                        command.SmsMessage?.TemplateRendering
                    }
                .Where(i => i != null)
                .ToArray();

            if (templateRenderings.Length == 0) return Result.Success();

            if (templateRenderings.Any(r => r.TemplateId == null && r.LogicalId == null))
            {
                return new Result { Status = "failure", Errors = new[] { "'templateId' or 'logicalId' must be defined." } };
            }

            templateRenderings = templateRenderings.Where(r => r.TemplateId == null && r.LogicalId != null).ToArray();

            if (templateRenderings.Length == 0) return Result.Success();

            var logicalIds = templateRenderings.Select(r => r.LogicalId).Distinct().ToArray();
            var query = new QueryArguments { Where = new TemplateWhere { LogicalId_in = logicalIds } };
            var templates = (await templateService.GetAsync(tenantId, query)).ToDictionary(i => i.LogicalId, i => i.Id);

            foreach (var item in templateRenderings)
            {
                if (!templates.ContainsKey(item.LogicalId))
                {
                    return new Result { Status = "failure", Errors = new[] { $"Template with logical id '{item.LogicalId}' not found." } };
                }

                item.TemplateId = templates[item.LogicalId];
            }

            return Result.Success();
        }
    }
}