using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Pdf;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Templates;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Domain.Notifications
{
    public interface INotificationService
    {
        Task<IEnumerable<Notification>> GetAsync(string tenantId, QueryArguments queryArguments);
        Task<long> GetTotalCountAsync(string tenantId, NotificationWhere where);
        Task<Result> DeleteAsync(string tenantId, string notificationId);

        Task<Result> SendAsync(string tenantId, SendNotificationCommand command);
        Task UpdateStatusAsync(string tenantId, string notificationId, UpdateNotificationStatusCommand command);

        Task SaveDeviceAsync(string tenantId, SaveDeviceCommand command);

        Task<IEnumerable<NotificationTrigger>> GetNotificationTriggersAsync(string tenantId, NotificationTriggerWhere where);
        Task<Result> CreateTriggerAsync(string tenantId, CreateTriggerCommand command);
        Task<Result> DeleteTriggerAsync(string tenantId, string triggerId);
        Task<Result> InitializeTenantAsync(string tenantId, InitializeTenantNotificationsCommand command);

        Task<IEnumerable<NotificationConfig>> GetNotificationConfigsAsync(string tenantId, NotificationConfigWhere where);
        Task<Result> CreateNotificationConfigAsync(string tenantId, CreateNotificationConfigCommand command);
        Task<Result> DeleteNotificationConfigAsync(string tenantId, DeleteNotificationConfigCommand command);

        Task<IEnumerable<NotificationSubscription>> GetNotificationSubscriptionsAsync(string tenantId, QueryArguments queryArguments);
        Task<long> GetNotificationSubscriptionsTotalCountAsync(string tenantId, NotificationSubscriptionWhere where);
        Task<Result<CreatedStatus>> CreateNotificationSubscriptionAsync(string tenantId, CreateNotificationSubscriptionCommand command);
        Task<Result> DeleteNotificationSubscriptionAsync(string tenantId, string notificationSubscriptionId);
        Task<Result> AddEntityToNotificationSubscriptionAsync(string tenantId, string id, AddEntityToNotificationSubscriptionCommand command);
        Task<Result> RemoveEntityFromNotificationSubscriptionAsync(string tenantId, string id, RemoveEntityFromNotificationCommand command);

        Task<Result<CreatedStatus>> AddTagToNotificationSubscriptionAsync(string tenantId, string id, AddTagCommand command);
        Task<Result> RemoveTagFromNotificationSubscriptionAsync(string tenantId, string id, RemoveCommand typeId);

        Task<ChatRoomUserList> GetUsersInRoomAsync(string tenantId, string topic);
        Task<Result> JoinChatRoomAsync(string tenantId, string id, JoinChatRoomCommand command);
        Task<Result> LeaveChatRoomAsync(string tenantId, string id, LeaveChatRoomCommand command);
    }

    public class Notification : SystemObject
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public string FromEntityId { get; set; }
        public string ToEntityId { get; set; }
        public string ToTopicId { get; set; }
        public string PolicyId { get; set; }
        public string OfferId { get; set; }
        public InternalMessage InternalMessage { get; set; }
        public PushMessage PushMessage { get; set; }
        public EmailMessage EmailMessage { get; set; }
        public SmsMessage SmsMessage { get; set; }
        public ChatMessage ChatMessage { get; set; }
        public string Status { get; set; }
        public DateTime TimeStamp { get; set; }
    }

    public class SendNotificationCommand
    {
        public string Type { get; set; }
        public string FromEntityId { get; set; }
        public string ToEntityId { get; set; }
        public List<string> ToEntityIds { get; set; }
        public string ToTopic { get; set; }
        public string ToTopicId { get; set; }
        public string PolicyId { get; set; }
        public string OfferId { get; set; }
        public InternalMessage InternalMessage { get; set; }
        public PushMessage PushMessage { get; set; }
        public EmailMessage EmailMessage { get; set; }
        public SmsMessage SmsMessage { get; set; }
        public ChatMessage ChatMessage { get; set; }
        public string SentById { get; set; }
        public DateTime? ScheduleToSendAt { get; set; }
        public bool UseConfig { get; set; }
    }

    public class ChatMessage
    {
        public string FromName { get; set; }
        public string Content { get; set; }
        public IEnumerable<string> Attachements { get; set; }
    }

    public class InternalMessage
    {
        public TemplateRendering TemplateRendering { get; set; }
        public JToken Data { get; set; }
    }

    public class PushMessage
    {
        public string Token { get; set; }
        public string Topic { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public Dictionary<string, string> Data { get; set; }
    }

    public class EmailMessage
    {
        public string From { get; set; }
        public string FromName { get; set; }
        public string To { get; set; }
        public string ReplyTo { get; set; }
        public string ReplyToName { get; set; }
        public List<string> Ccs { get; set; }
        public List<string> Bccs { get; set; }
        public string Subject { get; set; }
        public string HtmlContent { get; set; }
        public List<EmailMessageAttachment> Attachments { get; set; }
        public List<FileAttachment> FileAttachments { get; set; }
        public List<PdfAttachment> PdfAttachments { get; set; }
        public TemplateRendering TemplateRendering { get; set; }
    }

    public class TemplateRendering
    {
        public string TemplateId { get; set; }
        public string LogicalId { get; set; }
        public RenderParameters Input { get; set; }
    }

    public class PdfAttachment
    {
        public string FileName { get; set; }
        public string HtmlContent { get; set; }
        public List<byte> Bytes { get; set; }
        public string Password { get; set; }
        public MarginSettings MarginSettings { get; set; }
        public HeaderFooterSettings HeaderSettings { get; set; }
        public HeaderFooterSettings FooterSettings { get; set; }
    }
    
    public class FileAttachment
    {
        public string FileName { get; set; }
        public byte[] Bytes { get; set; }
    }

    public class EmailMessageAttachment
    {
        public string Key { get; set; }
        public string Name { get; set; }
    }

    public class SmsMessage
    {
        public string From { get; set; }
        public string To { get; set; }
        public IEnumerable<string> Tos { get; set; }
        public string Body { get; set; }
        public TemplateRendering TemplateRendering { get; set; }
    }

    public class NotificationTrigger : SystemObject
    {
        public string Id { get; set; }
        public string CronExpression { get; set; }
        public SendNotificationCommand SendNotificationCommand { get; set; }
    }

    public class CreateTriggerCommand
    {
        public string CronExpression { get; set; }
        public SendNotificationCommand SendNotificationCommand { get; set; }
    }

    public class SaveDeviceCommand
    {
        public string RefType { get; set; }
        public string RefId { get; set; }
        public string DeviceType { get; set; }
        public string DeviceId { get; set; }
    }

    public class NotificationWhere : Where
    {
        public List<NotificationWhere> Or { get; set; }
        public List<NotificationWhere> And { get; set; }

        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string FromEntityId { get; set; }
        public List<string> FromEntityId_in { get; set; }
        public string FromEntityId_not { get; set; }
        public string ToEntityId { get; set; }
        public List<string> ToEntityId_in { get; set; }
        public string PolicyId { get; set; }
        public List<string> PolicyId_in { get; set; }
        public string OfferId { get; set; }
        public List<string> OfferId_in { get; set; }
        public string Type { get; set; }
        public List<string> Type_in { get; set; }
        public string Status { get; set; }
        public List<string> Status_in { get; set; }
        public string Status_not { get; set; }
        public string ToTopic { get; set; }
        public List<string> ToTopic_in { get; set; }
        public string ToTopicId { get; set; }
        public List<string> ToTopicId_in { get; set; }
    }

    public class NotificationTriggerWhere : Where
    {
        public List<NotificationTriggerWhere> Or { get; set; }
        public List<NotificationTriggerWhere> And { get; set; }

        public string Id { get; set; }
        public List<string> Id_in { get; set; }

        public SendNotificationCommandWhere SendNotificationCommand { get; set; }
    }

    public class SendNotificationCommandWhere
    {
        public string ToEntityId { get; set; }
        public List<string> ToEntityId_in { get; set; }
    }

    public class UpdateNotificationStatusCommand
    {
        public string Status { get; set; }
    }

    public class InitializeTenantNotificationsCommand
    {
        public IEnumerable<CreateNotificationConfigCommand> Configs { get; set; }
    }

    public class NotificationConfig : SystemObject
    {
        public string ClientId { get; set; }
        public EmailConfig EmailConfig { get; set; }
    }

    public class NotificationConfigWhere : Where
    {
        public string ClientId { get; set; }
    }

    public class CreateNotificationConfigCommand
    {
        public string ClientId { get; set; }
        public EmailConfig EmailConfig { get; set; }
        public string CreatedById { get; set; }
    }

    public class DeleteNotificationConfigCommand: DeleteCommand
    {
        public string ClientId { get; set; }
    }

    public class EmailConfig
    {
        public string EmailSender { get; set; }
        public string EmailName { get; set; }
        public IEnumerable<string> Ccs { get; set; }
        public IEnumerable<string> Bccs { get; set; }
    }

    public class NotificationSubscription
    {
        public string Id { get; set; }
        public IEnumerable<string> EntitiesIds { get; set; }
        public string TopicName { get; set; }
        public IEnumerable<Tag> Tags { get; set; }
    }
    public class NotificationSubscriptionWhere : Where
    {
        public List<NotificationSubscriptionWhere> Or { get; set; }
        public List<NotificationSubscriptionWhere> And { get; set; }

        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string Topic { get; set; }
        public List<string> Topic_in { get; set; }
        public string EntitiesIds_contains { get; set; }
        public TagWhere Tags_some { get; set; }
    }
    public class CreateNotificationSubscriptionCommand
    {
        public string TopicName { get; set; }
        public string CreatedById { get; set; }

    }

    public class AddEntityToNotificationSubscriptionCommand
    {
        public string EntityId { get; set; }
        public string AddedById { get; set; }

    }
    public class RemoveEntityFromNotificationCommand
    {
        public string EntityId { get; set; }
        public string RemovedById { get; set; }

    }

    public class JoinChatRoomCommand
    {
        public string LoginId { get; set; }
        public string UserName { get; set; }
        public string ConnectionId { get; set; }
    }

    public class LeaveChatRoomCommand
    {
        public string ConnectionId { get; set; }
    }

    public class ChatRoomUserList
    {
        public string TopicName { get; set; }
        public IDictionary<string, string> LoginIdToUserName { get; set; }
    }
}
