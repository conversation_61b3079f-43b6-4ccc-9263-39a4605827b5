using System.Collections.Generic;
using System.Threading.Tasks;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Domain.Claims
{
    public interface IGuaranteeOfPaymentService
    {
        Task<long> GetTotalCountAsync(string tenantId, GuaranteeOfPaymentWhere where);

        Task<IEnumerable<GuaranteeOfPayment>> GetAsync(string tenantId, QueryArguments queryArguments);

        Task<List<GuaranteeOfPaymentEventLog>> GetEventsAsync(string tenantId, EventQuery query);

        Task<IDictionary<string, GuaranteeOfPayment>> GetDictionaryAsync(string tenantId, GuaranteeOfPaymentWhere where);

        Task<Result<string>> CreateGuaranteeOfPaymentAsync(string tenantId, CreateGuaranteeOfPaymentCommand command, string accessToken = null);

        Task<Result> UpdateGuaranteeOfPaymentAsync(string tenantId, string guaranteeOfPaymentId, UpdateGuaranteeOfPaymentCommand command);

        Task<Result> DeleteAsync(string tenantId, string guaranteeOfPaymentId, DeleteGuaranteeOfPaymentCommand command);

        Task<Result> RejectGuaranteeOfPaymentAsync(string tenantId, string guaranteeOfPaymentId, RejectGuaranteeOfPaymentCommand command);

        Task<Result> ApproveGuaranteeOfPaymentAsync(string tenantId, string guaranteeOfPaymentId, ApproveGuaranteeOfPaymentCommand command);

        Task<Result> AddNoteAsync(string tenantId, string guaranteeOfPaymentId, AddNoteCommand command);

        Task<Result> UpdateNoteAsync(string tenantId, string guaranteeOfPaymentId, UpdateNoteCommand command);

        Task<Result> RemoveNoteAsync(string tenantId, string guaranteeOfPaymentId, RemoveNoteCommand command);

        Task<Result> AddAttachmentAsync(string tenantId, string guaranteeOfPaymentId, AddAttachmentCommand command);

        Task<Result> UpdateAttachmentAsync(string tenantId, string guaranteeOfPaymentId, UpdateAttachmentCommand command);

        Task<Result> RemoveAttachmentAsync(string tenantId, string guaranteeOfPaymentId, RemoveAttachmentCommand command);
    }
}