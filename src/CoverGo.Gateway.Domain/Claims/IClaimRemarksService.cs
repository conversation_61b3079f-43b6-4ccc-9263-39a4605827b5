using System.Collections.Generic;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Domain.Claims
{
    public interface IClaimRemarksService
    {
        Task<IEnumerable<Remark>> GetAsync(string tenantId, QueryArguments<RemarkWhere> query);

        Task<long> GetTotalCountAsync(string tenantId, RemarkWhere where);

        Task UpsertAsync(string tenantId, RemarkUpsert remark);

        Task DeleteAsync(string tenantId, string id);
    }

    public class Remark
    {
        public string Id { get; set; }

        public string RemarkShort { get; set; }

        public string RemarkFull { get; set; }

        public JToken Fields { get; set; }
    }

    public class RemarkUpsert
    {
        public string Id { get; set; }

        public string RemarkShort { get; set; }

        public string RemarkFull { get; set; }

        public string Fields { get; set; }
    }

    public class RemarkWhere : Where
    {
        public List<RemarkWhere> Or { get; set; }

        public List<RemarkWhere> And { get; set; }

        public string Id { get; set; }

        public IEnumerable<string> Id_in { get; set; }

        public string RemarkShort_contains { get; set; }

        public string RemarkFull_contains { get; set; }

        public FieldsWhere Fields { get; set; }
    }
}
