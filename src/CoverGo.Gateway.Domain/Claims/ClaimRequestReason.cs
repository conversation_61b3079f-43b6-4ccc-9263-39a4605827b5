using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;


namespace CoverGo.Gateway.Domain.Claims
{
    public class ClaimRequestReason : IUniqueSystemObject
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string Type { get; set; }
        public JToken Fields { get; set; }
    }

    public class ClaimRequestReasonUpsert : IUniqueSystemObject
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string Type { get; set; }
        public string Fields { get; set; }
    }

    public class ClaimRequestReasonFilter
    {
        public string Id { get; set; }
        public string Code_in { get; set; }
        public string Type_in { get; set; }
        public string Type_contains { get; set; }
        public FieldsWhere Fields { get; set; }
    }
}