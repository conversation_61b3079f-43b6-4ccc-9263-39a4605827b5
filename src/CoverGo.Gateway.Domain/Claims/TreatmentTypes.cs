using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Domain.Claims
{
    public class Treatment
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string Category { get; set; }
        public string Name { get; set; }
        public string System { get; set; }
        public string SubSystem { get; set; }
    }
    
    public class CreateTreatmentCommand
    {
        public string Code { get; set; }
        public string Category { get; set; }
        public string Name { get; set; }
        public string System { get; set; }
        public string SubSystem { get; set; }
        public string CreatedById { get; set; }
    }
    
    public class UpdateTreatmentCommand : IUniqueSystemObject
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string Category { get; set; }
        public string Name { get; set; }
        public string System { get; set; }
        public string SubSystem { get; set; }
        public string ModifiedById { get; set; }
    }
    
    public class TreatmentBatchCommand 
        : IEntityBatch<CreateTreatmentCommand, UpdateTreatmentCommand, RemoveCommand>
    {
        public List<CreateTreatmentCommand> Create { get; set; }
        public List<UpdateTreatmentCommand> Update { get; set; }
        public List<RemoveCommand> Delete { get; set; }
    }
    
    public class TreatmentWhere : QueryArguments<Filter<TreatmentFilter>>
    {
    }
    
    public class TreatmentFilter
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string Category { get; set; }
        public string Name { get; set; }
        public string System { get; set; }
        public string SubSystem { get; set; }
        
        public string Code_contains { get; set; }
        public string Category_contains { get; set; }
        public string Name_contains { get; set; }
        public string System_contains { get; set; }
        public string SubSystem_contains { get; set; }
        
        public List<string> Id_in { get; set; }
        public List<string> Code_in { get; set; }
        public List<string> Category_in { get; set; }
        public List<string> Name_in { get; set; }
        public List<string> System_in { get; set; }
        public List<string> SubSystem_in { get; set; }
    }
}