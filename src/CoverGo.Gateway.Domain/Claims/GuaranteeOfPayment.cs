using System;
using System.Collections.Generic;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Domain.Claims
{
    public class GuaranteeOfPayment : SystemObject
    {
        public string Id { get; set; }

        public string PolicyId { get; set; }

        public string MemberId { get; set; }

        public string Doctor { get; set; }

        public string ProviderId { get; set; }

        public string Status { get; set; }

        public JToken Fields { get; set; }

        public string RejectedById { get; set; }

        public List<string> RejectionCodes { get; set; }

        public string RejectionRemarks { get; set; }

        public IEnumerable<Note> Notes { get; set; }

        public IEnumerable<Attachment> Attachments { get; set; }

        public decimal? EstimatedAmount { get; set; }

        public decimal? ApprovedAmount { get; set; }

        public decimal? ExcessAmount { get; set; }

        public CurrencyCode? CurrencyCode { get; set; }

        public string ApprovedById { get; set; }

        public string IssuerNumber { get; set; }

        public DateTime? ApprovedAt { get; set; }

        public DateTime? RejectedAt { get; set; }
        
        public AccessPolicy? AccessPolicy { get; set; }
    }

    public class CreateGuaranteeOfPaymentCommand
    {
        public string PolicyId { get; set; }

        public string MemberId { get; set; }

        public string Status { get; set; }

        public string Fields { get; set; }

        public string ProviderId { get; set; }

        public string Doctor { get; set; }

        public decimal? EstimatedAmount { get; set; }

        public CurrencyCode? CurrencyCode { get; set; }

        public string CreatedById { get; set; }

        public string IssuerNumber { get; set; }
    }

    public class UpdateGuaranteeOfPaymentCommand
    {
        public string Status { get; set; }

        public bool IsStatusChanged { get; set; }

        public string Fields { get; set; }

        public bool IsFieldsChanged { get; set; }

        public bool IsProviderChanged { get; set; }

        public string ProviderId { get; set; }

        public bool IsProviderIdChanged { get; set; }

        public string Doctor { get; set; }

        public bool IsDoctorChanged { get; set; }

        public decimal? EstimatedAmount { get; set; }

        public bool IsEstimatedAmountChanged { get; set; }

        public CurrencyCode? CurrencyCode { get; set; }

        public bool IsCurrencyCodeChanged { get; set; }

        public string FieldsPatch { get; set; }

        public string ModifiedById { get; set; }

        public string UpdateType { get; set; }
    }

    public class DeleteGuaranteeOfPaymentCommand
    {
        public string DeletedById { get; set; }
    }

    public class RejectGuaranteeOfPaymentCommand
    {
        public List<string> Codes { get; set; }

        public string Remarks { get; set; }

        public string RejectedById { get; set; }
    }

    public class ApproveGuaranteeOfPaymentCommand
    {
        public decimal? ApprovedAmount { get; set; }

        public decimal? ExcessAmount { get; set; }

        public string ApprovedById { get; set; }
    }


    public class GuaranteeOfPaymentWhere : Where
    {
        public List<GuaranteeOfPaymentWhere> Or { get; set; }

        public List<GuaranteeOfPaymentWhere> And { get; set; }

        public string Id { get; set; }

        public IEnumerable<string> Id_in { get; set; }

        public string IssuerNumber { get; set; }

        public IEnumerable<string> IssuerNumber_in { get; set; }

        public string MemberId { get; set; }

        public IEnumerable<string> MemberId_in { get; set; }

        public string Status { get; set; }

        public IEnumerable<string> Status_in { get; set; }

        public FieldsWhere Fields { get; set; }

        public string ProviderId { get; set; }

        public IEnumerable<string> ProviderId_in { get; set; }

        public AccessPolicy? AccessPolicy { get; set; }
    }

    public class AddGuaranteeOfPaymentCommand
    {
        public string GuaranteeOfPaymentId { get; set; }
        public string AddedById { get; set; }
    }

    public class RemoveGuaranteeOfPaymentCommand
    {
        public string GuaranteeOfPaymentId { get; set; }
        public string RemovedById { get; set; }
    }
}