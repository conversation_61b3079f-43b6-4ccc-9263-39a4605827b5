using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;


namespace CoverGo.Gateway.Domain.Claims
{
    public class ClaimRejectionCode : IUniqueSystemObject
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }
        public JToken Fields { get; set; }
    }

    public class ClaimRejectionCodeUpsert : IUniqueSystemObject
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }
        public string Fields { get; set; }
    }

    public class ClaimRejectionCodeFilter
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string[] Code_in { get; set; }
        public string Description_contains { get; set; }
        public FieldsWhere Fields { get; set; }
    }
}