using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Users;

using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Policies;

namespace CoverGo.Gateway.Domain.Claims
{
    public interface IClaimService
    {
        Task<long> GetTotalCountAsync(string tenantId, ClaimWhere where);
        Task<IEnumerable<Claim>> GetAsync(string tenantId, QueryArguments queryArguments);
        Task<List<ClaimEventLog>> GetEventsAsync(string tenantId, EventQuery query);
        Task<IDictionary<string, Claim>> GetDictionaryAsync(string tenantId, ClaimWhere where);
        Task<Result<string>> CreateClaimAsync(string tenantId, CreateClaimCommand command, string accessToken = null);
        Task<Result> UpdateClaimAsync(string tenantId, string claimId, UpdateClaimCommand command);
        Task<Result> DeleteAsync(string tenantId, string claimId, DeleteClaimCommand command);
        Task<Result> RejectClaimAsync(string tenantId, string claimId, RejectClaimCommand command);

        Task<Result> AddBenefitToClaimAsync(string tenantId, string claimId, AddBenefitToClaimCommand command);
        Task<Result> UpdateBenefitClaimAsync(string tenantId, string claimId, UpdateBenefitClaimCommand command);
        Task<Result> RemoveBenefitFromClaimAsync(string tenantId, string claimId, RemoveBenefitFromClaimCommand command);

        Task<Result<CreatedStatus>> AddFactAsync(string tenantId, string claimId, AddFactCommand command);
        Task<Result> UpdateFactAsync(string tenantId, string claimId, UpdateFactCommand command);
        Task<Result> RemoveFactAsync(string tenantId, string claimId, RemoveFactCommand command);
        Task<Result> FactBatchAsync(string tenantId, string claimId, FactCommandBatch command);
        Task<IEnumerable<FactTemplate>> GetFactTemplatesAsync(string tenantId, IEnumerable<string> ids);
        Task<ILookup<string, Claim>> GetByPolicyIdsLookupAsync(string tenantId, ClaimWhere claimWhere);

        Task<Result> AddNoteAsync(string tenantId, string claimId, AddNoteCommand command);
        Task<Result> UpdateNoteAsync(string tenantId, string claimId, UpdateNoteCommand command);
        Task<Result> RemoveNoteAsync(string tenantId, string claimId, RemoveNoteCommand command);

        Task<Result<string>> AddStakeholderToClaimAsync(string tenantId, string claimId, AddStakeholderCommand command);
        Task<Result> UpdateStakeholderOfClaimAsync(string tenantId, string claimId, UpdateStakeholderCommand command);
        Task<Result> RemoveStakeholderFromClaimAsync(string tenantId, string claimId, RemoveStakeholderCommand command);

        Task<Result> AddGuaranteeOfPaymentToClaimAsync(string tenantId, string claimId, AddGuaranteeOfPaymentCommand command);
        Task<Result> RemoveGuaranteeOfPaymentFromClaimAsync(string tenantId, string claimId, RemoveGuaranteeOfPaymentCommand command);

        Task<ILookup<string, IEnumerable<GuaranteeOfPayment>>> GetAddedGuaranteeOfPaymentsByClaimIds(string tenantId, ClaimWhere where);
        Task<List<(string claimId, IEnumerable<GuaranteeOfPayment> guaranteeOfPayments)>> GetAddedGuaranteeOfPaymentsByClaimIds(string tenantId, QueryArguments queryArguments);

        Task<Result<CreatedStatus>> ClaimBatchAsync(string tenantId, ClaimBatchCommand claimBatchCommand, string accessToken);
        
        Task<Result> AddAttachmentAsync(string tenantId, string claimId, AddAttachmentCommand command);

        Task<Result> UpdateAttachmentAsync(string tenantId, string claimId, UpdateAttachmentCommand command);

        Task<Result> RemoveAttachmentAsync(string tenantId, string claimId, RemoveAttachmentCommand command);

        Task<Result> ReverseClaim(string tenantId, string claimId, ReverseClaimCommand command);

        Task<Result> GenerateAdhocClaimReport(string tenantId, ClaimReportOptions command);
    }

    public class Claim : SystemObject
    {
        public string Id { get; set; }
        public string[] ClaimId { get; set; }
        public string PolicyId { get; set; }
        public string ClaimantId { get; set; }
        public string IssuerNumber { get; set; }
        public IEnumerable<BenefitClaim> BenefitClaims { get; set; }
        public IEnumerable<Fact> Facts { get; set; }
        //public IEnumerable<string> IncidentIds { get; set; }
        public string Status { get; set; }
        public List<string> DiagnosisCodes { get; set; }
        public List<string> OperationCodes { get; set; }
        public string ProviderId { get; set; }
        public string RejectedById { get; set; }
        public List<string> RejectionCodes { get; set; }
        public string RejectionRemarks { get; set; }
        public List<RejectionReason> RejectionReasons { get; set; }
        public IEnumerable<Note> Notes { get; set; }
        public IEnumerable<Stakeholder> Stakeholders { get; set; }
        
        public IEnumerable<Attachment> Attachments { get; set; }
        public JToken Fields { get; set; }
        public IEnumerable<string> GuaranteeOfPaymentIds { get; set; }
        public string PanelId { get; set; }
        public BatchId ImportBatchId { get; set; }

        public List<BatchId> ExportBatchIds { get; set; }
        public string Remark { get; set; }

        public List<ClaimReversal> Reversals { get; set; }

        public AccessPolicy? AccessPolicy { get; set; }
        public string ExternalId { get; set; }
    }

    public class ClaimReversal
    {
        public string Reason { get; set; }
        public string ReversedById { get; set; }
        public DateTime ReversedAt { get; set; }
    }

    public class ReverseClaimCommand
    {
        public string Reason { get; set; }
        public string WithStatus { get; set; }
        public string ReversedById { get; set; }
    }

    public class RejectionReason
    {
        public string Description { get; set; }
    }

    public class BatchId
    {
        public string Id { get; set; }

        public string Name { get; set; }
    }

    public class BenefitClaim
    {
        public string Id { get; set; }
        public string BenefitTypeId { get; set; }
        public string BenefitName { get; set; }
        public string BenefitDescription { get; set; }
        public JToken Value { get; set; }
        public List<ClaimValue> Values { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public string Remark { get; set; }
    }

    //public class Incident : SystemObject
    //{
    //    public string Id { get; set; }
    //    public IEnumerable<string> ClaimIds { get; set; }
    //    public string DamagedEntityId { get; set; }
    //    public Dictionary<string, object> Values { get; set; }
    //}


    public class ClaimReportOptions
    {
        public DateTime UtcFromDate { get; set; }
        public DateTime UtcToDate { get; set; }
        public string ReportName { get; set; }
    }
    public class CreateClaimCommand
    {
        public Guid? Id { get; set; }
        public  bool? CreateIncident { get; set; }
        public string PolicyId { get; set; }
        public string ClaimantId { get; set; }
        public string PanelId { get; set; }
        public string IssuerNumber { get; set; }
        public string IssuerNumberType { get; set; }
        public string Status { get; set; }
        public List<string> DiagnosisCodes { get; set; }
        public List<string> OperationCodes { get; set; }
        public string ProviderId { get; set; }
        public string CreatedById { get; set; }
        public string Fields { get; set; }
        public string Remark { get; set; }
        public BatchId ImportBatchId { get; set; }
        public string TempAttachmentFolderKey { get; set; }
    }

    public class AddBenefitToClaimCommand
    {
        public string BenefitTypeId { get; set; }
        public string BenefitName { get; set; }
        public string BenefitDescription { get; set; }
        public JToken Value { get; set; }
        public List<ClaimValue> Values { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public string Remark { get; set; }
        public string AddedById { get; set; }
    }

    public class ClaimValue
    {
        public decimal? BilledAmount { get; set; }
        public CurrencyCode? BilledCurrency { get; set; }
        public string FormattedBilledAmount { get; set; }
        public decimal? ApprovedAmount { get; set; }
        public CurrencyCode? ApprovedCurrency { get; set; }
        public string FormattedApprovedAmount { get; set; }
        public string Unit { get; set; }
    }

    public class UpdateBenefitClaimCommand
    {
        public string BenefitClaimId { get; set; }
        public string BenefitTypeId { get; set; }
        public bool IsBenefitTypeIdChanged { get; set; }
        public string BenefitName { get; set; }
        public bool IsBenefitNameChanged { get; set; }
        public string BenefitDescription { get; set; }
        public bool IsBenefitDescriptionChanged { get; set; }
        public JToken Value { get; set; }
        public bool IsValueChanged { get; set; }
        public List<ClaimValue> Values { get; set; }
        public bool IsValuesChanged { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public bool IsCurrencyCodeChanged { get; set; }
        public string Remark { get; set; }
        public bool IsRemarkChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveBenefitFromClaimCommand
    {
        public string BenefitClaimId { get; set; }
        public string RemovedById { get; set; }
    }

    public class UpdateClaimCommand
    {
        public string PolicyId { get; set; }
        public bool IsPolicyIdChanged { get; set; }
        public string IssuerNumber { get; set; }
        public bool IsIssuerNumberChanged { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public string Fields { get; set; }
        public bool IsFieldsChanged { get; set; }
        public string FieldsPatch { get; set; }
        public List<string> DiagnosisCodes { get; set; }
        public bool IsDiagnosisCodesChanged { get; set; }
        public List<string> OperationCodes { get; set; }
        public bool IsOperationCodesChanged { get; set; }
        public string ProviderId { get; set; }
        public bool IsProviderIdChanged { get; set; }
        public string ModifiedById { get; set; }

        public string ClaimId { get; set; }

        public BatchId ImportBatchId { get; set; }

        public bool IsImportBatchIdChanged { get; set; }

        public BatchId ExportBatchId { get; set; }

        public bool IsExportBatchIdChanged { get; set; }
        public string Remark { get; set; }

        public bool IsRemarkChanged { get; set; }

        public string UpdateType { get; set; }
        
        public string ClaimantId { get; set; }

        public bool IsClaimantIdChanged { get; set; }

        public string ExternalId { get; set; }
    }

    public class ClaimBatchCommand
    {
        public List<CreateClaimCommand> CreateClaimCommands { get; set; }
        public List<UpdateClaimCommand> UpdateClaimCommands { get; set; }
    }

    public class DeleteClaimCommand
    {
        public string DeletedById { get; set; }
    }

    public class RejectClaimCommand
    {
        public List<string> Codes { get; set; }
        public string Remarks { get; set; }
        public List<RejectionReason> Reasons { get; set; }
        public string RejectedById { get; set; }
    }

    //public class CreateIncidentCommand
    //{
    //    public string DamagedEntityId { get; set; }
    //    public Dictionary<string, object> Values { get; set; }

    //    public string CreatedById { get; set; }
    //}

    //public class LinkClaimAndIncidentCommand
    //{
    //    public string ClaimId { get; set; }
    //    public string IncidentId { get; set; }

    //    public string LinkedById { get; set; }
    //}

    public class ClaimWhere : Where
    {
        public List<ClaimWhere> Or { get; set; }
        public List<ClaimWhere> And { get; set; }

        public string Id { get; set; }
        public IEnumerable<string> Id_in { get; set; }
        public string IssuerNumber { get; set; }
        public string IssuerNumber_contains { get; set; }
        public string Status { get; set; }
        public string ClaimantId { get; set; }
        public IEnumerable<string> ClaimantId_in { get; set; }
        public EntityWhere Claimant_contains { get; set; }
        public IEnumerable<string> PolicyId_in { get; set; }
        public PolicyWhere Policy_contains { get; set; }
        public FieldsWhere Fields { get; set; }

        public string PanelId { get; set; }

        public bool? PanelId_exists { get; set; }

        public IEnumerable<string> PanelId_in { get; set; }

        public string ProviderId { get; set; }

        public bool? ProviderId_exists { get; set; }

        public IEnumerable<string?> ProviderId_in { get; set; }

        public string ImportBatchId { get; set; }

        public IEnumerable<string> ImportBatchId_in { get; set; }

        public string ImportBatchIdName { get; set; }

        public string ImportBatchIdName_contains { get; set; }

        public string ExportBatchId { get; set; }
        public bool? ExportBatchId_exists { get; set; }
        public DateTime? ExportedAt_lt { get; set; }
        public DateTime? ExportedAt_gt { get; set; }

        public IEnumerable<string> ExportBatchId_in { get; set; }

        public string ExportBatchIdName { get; set; }

        public string ExportBatchIdName_contains { get; set; }

        public string GuaranteeOfPaymentId { get; set; }

        public IEnumerable<string> GuaranteeOfPaymentId_in { get; set; }

        public bool? GuaranteeOfPaymentId_exists { get; set; }

        public AccessPolicy? AccessPolicy { get; set; }
    }
}
