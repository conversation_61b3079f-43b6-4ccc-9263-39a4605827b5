using System;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Domain.Claims.Asia;

public class AsiaClaimService : ITenantSpecificClaimService
{
    public decimal GetClaimApprovalAmount(Claim claim)
    {
        decimal totalAssessAmount = 0;
        decimal totalWpAmount = 0;
        decimal totalExclusionAmount = 0;
        if (claim.Fields["billingDetail"] is JArray billingDetailItems && billingDetailItems.Count > 0)
        {
            foreach (JToken billingDetail in billingDetailItems)
            {
                if (billingDetail["benefitList"] is JArray benefitList && benefitList.Count > 0)
                {
                    foreach (JToken benefit in benefitList)
                    {
                        totalAssessAmount += benefit["assessedAmount"]?.Value<decimal>() ?? 0;
                        totalWpAmount += benefit["exGratiaAmount"]?.Value<decimal>() ?? 0;
                        totalExclusionAmount += benefit["exclusionAmount"]?.Value<decimal>() ?? 0;
                    }
                }
            }
        }
        decimal totalAmountToPay = totalAssessAmount + totalWpAmount - totalExclusionAmount;
        return Math.Round(totalAmountToPay, 2);
    }
}
