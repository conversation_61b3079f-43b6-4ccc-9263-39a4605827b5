using System;
using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Domain.Claims
{
    public class ClaimReport :  SystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        [Obsolete]
        public List<string> ClaimIds { get; set; }

        public List<ClaimSnapshot> ClaimSnapshots { get; set; } = new();
        public DateTime ReportDate { get; set; }
    }
    
    public class ClaimSnapshot
    {
        public string Id { get; set; }
        public string ClaimReportId { get; set; }
        public string ClaimId { get; set; }
        public string PolicyNo { get; set; }
        public string ClaimType { get; set; }
        public string ClaimNo { get; set; }
        public DateTime? IncurredDate { get; set; }
        public DateTime? FormReceivedDate { get; set; }
        public string ClaimHandler { get; set; }
        public string ClaimApprover { get; set; }
        public DateTime? SettlementDate { get; set; }
        public DateTime? SettlementFollowingWorkingDate { get; set; }
        public string ClaimantName { get; set; }
        public string PolicyHolder { get; set; }
        public string ClaimantInternalCode { get; set; }
        public string InvoiceNo { get; set; }
        public string PanelName { get; set; }
        public decimal TotalIncurredAmount { get; set; }
        public decimal TotalAmountToPay { get; set; }
        public DateTime? ApprovedDate { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsPanel { get; set; }
        public string ClaimSettlementMode { get; set; }
        public string EmployeeName { get; set; }
        public string EmployeeInternalCode { get; set; }
        public string ExportBatchNumber { get; set; }
        public string CompanyName { get; set; }
        public string CompanyBankAccount { get; set; }
        public string CompanyBankCode { get; set; }
        public string EmployeeBankAccount { get; set; }
        public string EmployeeBankCode { get; set; }
        public string PanelBankCode { get; set; }
        public string PanelBankAccount { get; set; }
        public string Status { get; set; }
        public decimal TotalBilledAmount { get; set; }
        public bool HasReversal { get; set; }
        public string PanelAccountHolder { get; set; }
        public string PanelBranchCode { get; set; }
        public string PanelBankName { get; set; }
        public string PanelP400ClientNumber { get; set; }
        public string ProductType { get; set; }
        public string HospitalName { get; set; }
        public string RoomType { get; set; }
        public decimal HospitalizationDays { get; set; }
        public int ClaimantAge { get; set; }
        public string ClaimantGender { get; set; }
        public bool? SecondaryClaimIndicator { get; set; }
        public List<string> DiagnosisCodes { get; set; }
        public List<string> OperationCodes { get; set; }
        public List<BillingDetailItem> BillingDetail { get; set; }
    }

    public class BillingDetailItem
    {
        public string BenefitId { get; set; }
        public decimal IncurredAmount { get; set; }
        public decimal BilledAmount { get; set; }
    }
    
    public class CreateClaimReportCommand
    {
        public string Name { get; set; }
        public List<string> ClaimIds { get; set; }
        public DateTime ReportDate { get; set; }
        public string CreatedById { get; set; }
    }
    
    public class UpdateClaimReportCommand : IUniqueSystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public List<string> ClaimIds { get; set; }
        public DateTime ReportDate { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveClaimReportCommand : IUniqueSystemObject
    {
        public string Id { get; set; }
        public string RemovedById { get; set; }
        public DateTime CreatedAt_gte { get; set; }
    }

    public class ClaimReportBatchCommand 
        : IEntityBatch<CreateClaimReportCommand, UpdateClaimReportCommand, RemoveClaimReportCommand>
    {
        public List<CreateClaimReportCommand> Create { get; set; }
        public List<UpdateClaimReportCommand> Update { get; set; }
        public List<RemoveClaimReportCommand> Delete { get; set; }
    }
    
    public class ClaimReportWhere : QueryArguments<Filter<ClaimReportFilter>>
    {
    }
    
    public class ClaimReportFilter
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public DateTime? ReportDate { get; set; }
        public List<string> Id_in { get; set; }
        public List<string> Name_in { get; set; }
        public DateTime? CreatedAt_lt { get; set; }
        public DateTime? CreatedAt_gt { get; set; }
        public DateTime? LastModifiedAt_lt { get; set; }
        public DateTime? LastModifiedAt_gt { get; set; }
        public DateTime? ReportDate_lt { get; set; }
        public DateTime? ReportDate_gt { get; set; }
        public bool? IsEmpty { get; set; }
        public bool? ClaimSnapshots_IsPanel { get; set; }
        public bool? ClaimSnapshots_HasReversal { get; set; }
        public string ClaimSnapshots_ClaimSettmentMode { get; set; }
        public List<string> ClaimSnapshots_ClaimSettmentMode_in { get; set; }
        public List<string> ClaimSnapshots_PolicyHolderId_In { get; set; }
        public DateTime? ClaimSnapshots_SettlementDate_gt { get; set; }
        public DateTime? ClaimSnapshots_SettlementDate_gte { get; set; }
        public DateTime? ClaimSnapshots_SettlementDate_lt { get; set; }
        public DateTime? ClaimSnapshots_SettlementDate_lte { get; set; }
        public string Status { get; set; }
    }
    
    public class CreateClaimSnapshotCommand
    {
        public string Id { get; set; }
        public string ClaimId { get; set; }
        public string ClaimReportId { get; set; }
        public string PolicyNo { get; set; }
        public string ClaimType { get; set; }
        public string ClaimNo { get; set; }
        public DateTime? IncurredDate { get; set; }
        public DateTime? FormReceivedDate { get; set; }
        public string ClaimHandler { get; set; }
        public string ClaimApprover { get; set; }
        public DateTime? SettlementDate { get; set; }
        public DateTime? SettlementFollowingWorkingDate { get; set; }
        public string ClaimantName { get; set; }
        public string PolicyHolder { get; set; }
        public string ClaimantInternalCode { get; set; }
        public string InvoiceNo { get; set; }
        public string PanelName { get; set; }
        public decimal TotalIncurredAmount { get; set; }
        public decimal TotalAmountToPay { get; set; }
        public DateTime? ApprovedDate { get; set; }
        public DateTime? CreatedAt { get; set; }
        public string ClaimSettlementMode { get; set; }
        public string EmployeeName { get; set; }
        public string EmployeeInternalCode { get; set; }
        public string ExportBatchNumber { get; set; }
        public string CompanyName { get; set; }
        public string CompanyBankAccount { get; set; }
        public string CompanyBankCode { get; set; }
        public string EmployeeBankAccount { get; set; }
        public string EmployeeBankCode { get; set; }
        public string PanelBankCode { get; set; }
        public string PanelBankAccount { get; set; }
        public bool IsPanel { get; set; }
        public string Status { get; set; }
        public decimal TotalBilledAmount { get; set; }
        public bool HasReversal { get; set; }
        public string PanelAccountHolder { get; set; }
        public string ProductType { get; set; }
        public string HospitalName { get; set; }
        public string RoomType { get; set; }
        public decimal HospitalizationDays { get; set; }
        public int ClaimantAge { get; set; }
        public string ClaimantGender { get; set; }
        public bool? SecondaryClaimIndicator { get; set; }
        public List<string> DiagnosisCodes { get; set; }
        public List<string> OperationCodes { get; set; }
        public List<BillingDetailItem> BillingDetail { get; set; }
    }
    
    public class ClaimSnapshotBatchCommand : IEntityBatch<CreateClaimSnapshotCommand, UpdateClaimSnapshotCommand,
        RemoveCommand>
    {
        public List<CreateClaimSnapshotCommand> Create { get; set; }
        
        public List<UpdateClaimSnapshotCommand> Update { get; set; }
        
        public List<RemoveCommand> Delete { get; set; }
    }
    
    public class UpdateClaimSnapshotCommand : IUniqueSystemObject
    {
        public string Id { get; set; }
        public string ClaimId { get; set; }
        public string ClaimReportId { get; set; }
        public string PolicyNo { get; set; }
        public string ClaimType { get; set; }
        public string ClaimNo { get; set; }
        public DateTime? IncurredDate { get; set; }
        public DateTime? FormReceivedDate { get; set; }
        public string ClaimHandler { get; set; }
        public string ClaimApprover { get; set; }
        public DateTime? SettlementDate { get; set; }
        public DateTime? SettlementFollowingWorkingDate { get; set; }
        public string ClaimantName { get; set; }
        public string PolicyHolder { get; set; }
        public string ClaimantInternalCode { get; set; }
        public string InvoiceNo { get; set; }
        public string PanelName { get; set; }
        public decimal TotalIncurredAmount { get; set; }
        public decimal TotalAmountToPay { get; set; }
        public DateTime? ApprovedDate { get; set; }
        public DateTime? CreatedAt { get; set; }
        public string ClaimSettlementMode { get; set; }
        public string EmployeeName { get; set; }
        public string EmployeeInternalCode { get; set; }
        public string ExportBatchNumber { get; set; }
        public string CompanyName { get; set; }
        public string CompanyBankAccount { get; set; }
        public string CompanyBankCode { get; set; }
        public string EmployeeBankAccount { get; set; }
        public string EmployeeBankCode { get; set; }
        public string PanelBankCode { get; set; }
        public string PanelBankAccount { get; set; }
        public bool? IsPanel { get; set; }
        public string Status { get; set; }
        public decimal TotalBilledAmount { get; set; }
        public bool HasReversal { get; set; }
        public string PanelAccountHolder { get; set; }
        public string ProductType { get; set; }
        public string HospitalName { get; set; }
        public string RoomType { get; set; }
        public decimal HospitalizationDays { get; set; }
        public int ClaimantAge { get; set; }
        public string ClaimantGender { get; set; }
        public bool? SecondaryClaimIndicator { get; set; }
        public List<string> DiagnosisCodes { get; set; }
        public List<string> OperationCodes { get; set; }
        public List<BillingDetailItem> BillingDetail { get; set; }
    }
    
    public class ClaimSnapshotWhere : QueryArguments<Filter<ClaimSnapshotFilter>>
    {
    }
    
    public class ClaimSnapshotFilter
    {
        public string Id { get; set; }
        public bool? IsPanel { get; set; }
        public string ClaimReportId { get; set; }
        public string ClaimId { get; set; }
        public string PolicyNo { get; set; }
        public string ClaimType { get; set; }
        public string ClaimNo { get; set; }
        public string ClaimHandler { get; set; }
        public string ClaimApprover { get; set; }
        public string ClaimantName { get; set; }
        public string PolicyHolder { get; set; }
        public string ClaimantInternalCode { get; set; }
        public string InvoiceNo { get; set; }
        public string PanelName { get; set; }
        public string ClaimSettlementMode { get; set; }
        public string EmployeeName { get; set; }
        public string EmployeeInternalCode { get; set; }
        public string ExportBatchNumber { get; set; }
        public string CompanyName { get; set; }
        public string CompanyBankAccount { get; set; }
        public string CompanyBankCode { get; set; }
        public string EmployeeBankAccount { get; set; }
        public string EmployeeBankCode { get; set; }
        public string PanelBankCode { get; set; }
        public string PanelBankAccount { get; set; }
        public string Status { get; set; }
        public DateTime? IncurredDate_gt { get; set; }
        public DateTime? IncurredDate_gte { get; set; }
        public DateTime? IncurredDate_lt { get; set; }
        public DateTime? IncurredDate_lte { get; set; }
        public DateTime? FormReceivedDate_gt { get; set; }
        public DateTime? FormReceivedDate_gte { get; set; }
        public DateTime? FormReceivedDate_lt { get; set; }
        public DateTime? FormReceivedDate_lte { get; set; }
        public DateTime? SettlementDate_gt { get; set; }
        public DateTime? SettlementDate_gte { get; set; }
        public DateTime? SettlementDate_lt { get; set; }
        public DateTime? SettlementDate_lte { get; set; }
        public DateTime? SettlementFollowingWorkingDate_gt { get; set; }
        public DateTime? SettlementFollowingWorkingDate_gte { get; set; }
        public DateTime? SettlementFollowingWorkingDate_lt { get; set; }
        public DateTime? SettlementFollowingWorkingDate_lte { get; set; }
        public DateTime? CreatedAt_gt { get; set; }
        public DateTime? CreatedAt_gte { get; set; }
        public DateTime? CreatedAt_lt { get; set; }
        public DateTime? CreatedAt_lte { get; set; }
        public DateTime? ApprovedDate_gt { get; set; }
        public DateTime? ApprovedDate_gte { get; set; }
        public DateTime? ApprovedDate_lt { get; set; }
        public DateTime? ApprovedDate_lte { get; set; }
        public IEnumerable<string> Status_in { get; set; }
        public string ExportBatchNumber_contains { get; set; }
        public IEnumerable<string> ClaimReportId_in { get; set; }
        public bool? HasReversal { get; set; }
        public string PanelBranchCode { get; set; }
        public string PanelBankName { get; set; }
        public string PanelP400ClientNumber { get; set; }
    }
}