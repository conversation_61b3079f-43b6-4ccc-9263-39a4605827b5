using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;


namespace CoverGo.Gateway.Domain.Claims
{
    public class ClaimRejectionReason : IUniqueSystemObject
    {
        public string Id { get; set; }
        public ClaimRejectionCode Code { get; set; }
        public string Type { get; set; }
        public JToken Fields { get; set; }
    }

    public class ClaimRejectionReasonUpsert : IUniqueSystemObject
    {
        public string Id { get; set; }
        public ClaimRejectionCodeUpsert Code { get; set; }
        public string Type { get; set; }
        public string Fields { get; set; }
    }

    public class ClaimRejectionReasonFilter
    {
        public string Id { get; set; }
        public string Type_in { get; set; }
        public FieldsWhere Fields { get; set; }
    }
}