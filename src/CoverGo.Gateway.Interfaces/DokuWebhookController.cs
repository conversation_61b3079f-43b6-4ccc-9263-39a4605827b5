﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Transactions;

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Interfaces
{
    [ApiExplorerSettings(IgnoreApi = true)]
    public class DokuWebhookController : ControllerBase
    {
        private readonly IPolicyService _policyService;
        private readonly ITransactionService _transactionService;
        private readonly ILogger _logger;

        public DokuWebhookController(
                IPolicyService policyService,
                ITransactionService transactionService,
                ILogger<DokuWebhookController> logger
            )
        {
            _policyService = policyService;
            _logger = logger;
            _transactionService = transactionService;
        }

        [HttpPost("doku")]
        [Consumes("application/x-www-form-urlencoded")]
        public async Task<ActionResult> DokuNotify([FromForm] DokuNotifyCommand command)
        {
            try
            {
                string[] sessionSplits = command.SESSIONID.Split('-');
                string tenantId = sessionSplits[0];
                string transactionId = Guid.Parse(sessionSplits[1]).ToString();

                _logger.LogInformation("Handling Doku notification command for tenant {Tenant} and transaction {Transaction}", tenantId, transactionId);

                Transaction transactionToProcess = (await _transactionService.GetAsync(tenantId,
                    new QueryArguments
                    {
                        Where = new TransactionWhere
                        {
                            Id = transactionId
                        }
                    }
                )).FirstOrDefault();

                if (transactionToProcess == null)
                {
                    _logger.LogError($"The transaction {transactionId} was not found.");
                    throw new Exception($"The transaction {transactionId} was not found.");
                }

                Result result = await _transactionService.ProcessAsync(tenantId, transactionId, new ProcessTransactionCommand
                {
                    ProviderToken = JsonConvert.SerializeObject(command),
                    Status = command.RESULTMSG != "SUCCESS" ? TransactionStatus.Rejected : TransactionStatus.Approved
                });

                if (result.Status == "success")
                {
                    Policy policy = (await _policyService.GetAsync(tenantId, new QueryArguments { Where = new PolicyWhere { Id = transactionToProcess.PolicyId } })).FirstOrDefault();
                    if (policy == null || policy.IsIssued)
                    {
                        _logger.LogError($"Policy {transactionToProcess.PolicyId} not found or already issued");
                        throw new Exception($"Policy {transactionToProcess.PolicyId} not found or already issued");
                    }

                    string loginId = User.GetLoginIdFromToken(false);

                    Result<PolicyStatus> issuanceResult = await _policyService.IssuePolicyAsync(tenantId, new IssuePolicyCommand { PolicyId = transactionToProcess.PolicyId, IssuedById = loginId });
                }

                return Ok();
            }
            catch (Exception e)
            {
                _logger.LogError(e, "doku");
                throw;
            }
        }

        public class DokuNotifyCommand
        {
            public string PAYMENTDATETIME { get; set; }
            public string PURCHASECURRENCY { get; set; }
            public string LIABILITY { get; set; }
            public string PAYMENTCHANNEL { get; set; }
            public string AMOUNT { get; set; }
            public string RESULTMSG { get; set; }
            public string TRANSIDMERCHANT { get; set; }
            public string SESSIONID { get; set; }
        }
    }
}
