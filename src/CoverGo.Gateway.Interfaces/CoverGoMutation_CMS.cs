using System.Collections.Generic;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Cms;
using CoverGo.Gateway.Interfaces.Cms;
using GraphQL.Authorization;
using GraphQL.Types;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeCmsMutations(ICmsService cmsService, PermissionValidator permissionValidator)
        {
            Field<CreatedStatusResultGraphType>()
                .Name("createComponent")
                .Description("Creates a component")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateComponentInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, "writeCms");

                    CreateComponentInputGraph input = context.GetArgument<CreateComponentInputGraph>("input");

                    var command = new CreateComponentCommand
                    {
                        ComponentJson = JsonConvert.DeserializeObject<JToken>(input.ComponentJsonString),
                        AddedById = context.GetLoginIdFromToken(),
                        Name = input.Name,
                        Status = input.Status,
                        VueTemplate = input.VueTemplate,
                        Version = input.Version
                    };

                    Result<string> result = await cmsService.CreateComponentAsync(tenantId, command);
                    return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
                });

            Field<ResultGraphType>()
                .Name("updateComponent")
                .Description("Updates a component")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The component's id")
                .Argument<NonNullGraphType<UpdateComponentInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string componentId = context.GetArgument<string>("id");
                    await permissionValidator.Authorize(context, "writeCms", componentId);

                    UpdateComponentInputGraph input = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateComponentInputGraph>();

                    var command = new UpdateComponentCommand
                    {
                        ComponentJson = input.ComponentJsonString != null ? JsonConvert.DeserializeObject<JToken>(input.ComponentJsonString) : null,
                        IsComponentJsonChanged = input.IsComponentJsonStringChanged,
                        AddedById = context.GetLoginIdFromToken(),
                        Name = input.Name,
                        IsNameChanged = input.IsNameChanged,
                        Status = input.Status,
                        IsStatusChanged = input.IsStatusChanged,
                        VueTemplate = input.VueTemplate,
                        IsVueTemplateChanged = input.IsVueTemplateChanged,
                        Version = input.Version,
                        IsVersionChanged = input.IsVersionChanged
                    };

                    Result result = await cmsService.UpdateComponentAsync(tenantId, componentId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("deleteComponent")
                .Description("Deletes a component")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The component's id")
                .ResolveAsync(async context =>
                {
                    string id = context.GetArgument<string>("id");
                    await permissionValidator.Authorize(context, "writeCms", id);
                    string tenantId = context.GetTenantIdFromToken();

                    Result result = await cmsService.DeleteComponentAsync(tenantId, id, new DeleteComponentCommand
                    {
                        AddedById = context.GetLoginIdFromToken()
                    });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("rebuildComponentModules")
                .Description("Rebuild all component modules for a tenant into the fileSystem")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeCms");
                    string tenantId = context.GetTenantIdFromToken();

                    Result result = await cmsService.RebuildComponentModulesAsync(tenantId, new RebuildComponentModulesCommand { });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("upsertCmsConfig")
                .Description("Creates a cms configuration")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("appId", "The application identifier")
                .Argument<NonNullGraphType<CmsConfigInputGraphType>>("config", "The cms configuration to create")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeCmsConfigs");
                    string tenantId = context.GetTenantIdFromToken();
                    string appId = context.GetArgument<string>("appId");
                    CmsConfigGraph configGraph = context.GetArgument<CmsConfigGraph>("config");

                    CmsConfig config = CmsConfigGraph.ToDomain(configGraph);

                    Result result = await cmsService.UpsertConfigAsync(tenantId, appId, config);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("deleteCmsConfig")
                .Description("Deletes a cms config")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("appId", "The application identifier")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeCmsConfigs");
                    string tenantId = context.GetTenantIdFromToken();
                    string appId = context.GetArgument<string>("appId");

                    Result result = await cmsService.DeleteConfigAsync(tenantId, appId);
                    return result;
                });
        }
    }
}