using System;
using System.Collections.Generic;
using System.Linq;

using AutoMapper;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Achievements;
using CoverGo.Gateway.Domain.Advisor;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Binder;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Claims;
using CoverGo.Gateway.Domain.Cms;
using CoverGo.Gateway.Domain.Education;
using CoverGo.Gateway.Domain.Encryptions;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Notifications;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Scheduler;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Infrastructure.Claims;
using CoverGo.Gateway.Infrastructure.WfpServices;
using CoverGo.Gateway.Interfaces.Auth;
using CoverGo.Gateway.Interfaces.Claims;
using CoverGo.Gateway.Interfaces.Notifications;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.ScriptEvaluation;
using CoverGo.Gateway.Interfaces.Types;
using CoverGo.Gateway.Interfaces.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;

using GraphQL.Authorization;
using GraphQL.Types;

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

using Object = CoverGo.Users.Domain.Objects.Object;
using QueryArguments = GraphQL.Types.QueryArguments;
using IPoliciesClient = CoverGo.Policies.Client.IPoliciesClient;
using CoverGo.FeatureManagement;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation : ObjectGraphType<object>
    {
        private const string DefaultLanguage = "en-US";
        public CoverGoMutation(
            IAuthService authService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
            IEntityService<Object, CreateObjectCommand, UpdateObjectCommand> objectService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            IEntityService entityService,
            IPolicyService policyService,
            CoverGoPolicyMembersService policyMembersService,
            CoverGoPolicyCountersService policyCountersService,
            ITransactionService transactionService,
            IProductService productService,
            IPricingService pricingService,
            IL10nService l10nService,
            INotificationService notificationService,
            IClaimService claimService,
            ITenantSpecificClaimService tenantSpecificClaimService,
            IInsurerService insurerService,
            ICmsService cmsService,
            ITemplateService templateService,
            ICaseService caseService,
            IBinderService binderService,
            IFileSystemService fileSystemService,
            IEncryptionAlgorithm encryptionAlgorithm,
            FaceRecongitionService faceRecongitionService,
            IEducationService educationService,
            IAchievementService achievementService,
            IAdvisorService advisorService,
            ILogger<CoverGoMutation> logger,
            IGuaranteeOfPaymentService guaranteeOfPaymentService,
            IClaimRemarksService claimRemarksService,
            ISchedulerService schedulerService,
            CoverGoDisabilityService disabilityService,
            CoverGoDiagnosisService diagnosisService,
            CoverGoTreatmentService treatmentService,
            ICaptchaVerificationService captchaVerificationService,
            CoverGoClaimRejectionCodeService claimRejectionCodeService,
            CoverGoClaimRejectionReasonService claimRejectionReasonService,
            CoverGoClaimRequestReasonService claimRequestReasonService,
            CoverGoProductDiscountCodeService productDiscountCodeService,
            CoverGoClaimReportService claimReportService,
            PermissionValidator permissionValidator,
            IOptions<MvcNewtonsoftJsonOptions> mvcJsonOptions,
            IPoliciesClient policiesClient,
            IMapper mapper,
            IMultiTenantFeatureManager multiTenantFeatureManager,
            IOptions<ForgotPasswordSettings> forgotPasswordSettings,
            IOptions<IssueDebitNoteNotificationSetttings> issueDebitNoteNotificationSetttings)
        {

            Name = "covergoMutation";

            InitializeL10nMutations(l10nService, permissionValidator);

            #region auth

            Field<ResultGraphType>()
                .Name("verifyCaptcha")
                .AuthorizeWith("any")
                .Description("Verifies a captcha")
                .Argument<NonNullGraphType<StringGraphType>>("response", "")
                .Argument<StringGraphType>("remoteIp", "")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();
                    string token = context.GetArgument<string>("response");
                    string remoteIp = context.GetArgument<string>("remoteIp");

                    return await captchaVerificationService.IsCaptchaValid(tenantId, clientId, token, remoteIp);
                });

            Field<PreOtpLoginResultGraphType>()
                .Name("createPreOtpLogin")
                .AuthorizeWith("any")
                .Description("request a preOtpLogin for future login using One Time Password")
                .Argument<BooleanGraphType>("createIndividual", "boolean flag to instruct the system to create an new Individual Entity or not")
                .Argument<BooleanGraphType>("generateUniqueUsername", "boolean flag to instruct the system to generate a unique username or not")
                .Argument<NonNullGraphType<CreatePreOtpLoginInputGraphType>>("input", "the input necessary to create a preOtpLogin")
                .Argument<NonNullGraphType<StringGraphType>>("emailTemplateId", "the template Id for email notification")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string entityId = null;
                    bool isCreateIndividualRequired = context.GetArgument<bool>("createIndividual");
                    bool isGenerateUniqueUsernameRequired = context.GetArgument<bool>("generateUniqueUsername");
                    string emailTemplateId = context.GetArgument<string>("emailTemplateId");

                    CreatePreOtpLoginCommand command = context.GetArgument<CreatePreOtpLoginCommand>("input");

                    if (isCreateIndividualRequired)
                    {
                        CreateIndividualInputGraph input = new()
                        {
                            Email = command.Email,
                            TelephoneNumber = command.PhoneNumber
                        };

                        CreateEntityBatchCommand<CreateIndividualCommand> batchCommand =
                            EntityPrimitiveBatchCommands<CreateIndividualCommand, CreateIndividualInputGraph>(loginId, input);
                        Result<CreatedStatus> createIndividualResult =
                            await individualService.CreateFromBatchAsync(tenantId, batchCommand);
                        if (createIndividualResult.Status != "success")
                            return new Result<PreOtpLogin>
                            {
                                Status = createIndividualResult.Status,
                                Errors = createIndividualResult.Errors,
                                Errors_2 = createIndividualResult.Errors_2
                            };
                        entityId = createIndividualResult.Value.Id;
                    }

                    string username = command.Email + (isGenerateUniqueUsernameRequired ? Guid.NewGuid().ToString() : "");
                    string password = Guid.NewGuid().ToString();

                    var createLoginCommand = new CreateLoginCommand
                    {
                        ClientId = command.ClientId,
                        Username = username,
                        Password = password,
                        Email = command.Email,
                        TelephoneNumber = command.PhoneNumber,
                        EntityId = entityId,
                        IsEmailConfirmed = false,
                        CreatedById = loginId,
                        SendNotificationCommand = new SendNotificationCommand
                        {
                            EmailMessage = new EmailMessage
                            {
                                TemplateRendering = new TemplateRendering { TemplateId = emailTemplateId }
                            }
                        }
                    };

                    Result<CreatedStatus> createLoginResult
                        = await authService.CreateLoginAsync(tenantId, createLoginCommand);
                    if (createLoginResult.Status != "success")
                        return new Result<PreOtpLogin>
                        {
                            Status = createLoginResult.Status,
                            Errors = createLoginResult.Errors,
                            Errors_2 = createLoginResult.Errors_2
                        };

                    command.Username = username;
                    command.Password = password;
                    return await authService.CreatePreOtpLoginAsync(tenantId, command);
                });

            Field<OtpLoginResultGraphType>()
                .Name("createOtpLogin")
                .AuthorizeWith("any")
                .Description("request an otp login")
                .Argument<NonNullGraphType<CreateOtpLoginInputGraphType>>("input", "the input necessary to create a otpLogin")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    CreateOtpLoginCommand command = context.GetArgument<CreateOtpLoginCommand>("input");

                    return await authService.CreateOtpLoginAsync(tenantId, command);
                });

            Field<TokenResultGraphType>()
                .Name("createAccessTokenFromOtpLogin")
                .AuthorizeWith("any")
                .Description("get access token using otpLogin")
                .Argument<NonNullGraphType<CreateAccessTokenFromOtpLoginInputGraphType>>("input", "the input necessary to get an accessToken using otpLogin")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    CreateAccessTokenFromOtpLoginCommand command = context.GetArgument<CreateAccessTokenFromOtpLoginCommand>("input");

                    return await authService.CreateAccessTokenFromOtpLoginAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("addHostToTenantSettings")
                .AuthorizeWith("any")
                .Description("adds a host to the tenant settings")
                .Argument<StringGraphType>("host", "the host to add")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("updateApps", "writeApps"));
                    string host = context.GetArgument<string>("host");

                    return await authService.AddHostToTenantSettingsAsync(tenantId, host);
                });

            Field<ResultGraphType>()
                .Name("removeHostFromTenantSettings")
                .AuthorizeWith("any")
                .Description("removes a host from the tenant settings")
                .Argument<StringGraphType>("host", "the host to remove")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("updateApps", "writeApps"));
                    string host = context.GetArgument<string>("host");

                    return await authService.RemoveHostFromTenantSettingsAsync(tenantId, host);
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createLogin")
                .AuthorizeWith("any")
                .Description("Creates a login")
                .Argument<NonNullGraphType<CreateLoginInputGraphType>>("createLoginInput", "the input necessary to create a login")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeLogins");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    CreateLoginCommand command = context.GetArgument<CreateLoginCommand>("createLoginInput");
                    command.CreatedById = loginId;

                    Result<CreatedStatus> result = await authService.CreateLoginAsync(tenantId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateMyTelephoneNumber")
                .AuthorizeWith("any")
                .Description("Updates the login phone number")
                .Argument<NonNullGraphType<StringGraphType>>("telephoneNumber", "The telephone number")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string telephoneNumber = context.GetArgument<string>("telephoneNumber");

                    await permissionValidator.Authorize(context, "writeLogins", loginId);

                    var command = new UpdateLoginCommand
                    {
                        TelephoneNumber = telephoneNumber,
                        IsTelephoneNumberChanged = true,
                        ModifiedById = loginId
                    };

                    Result result = await authService.UpdateLoginAsync(tenantId, loginId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateMyEmail")
                .AuthorizeWith("any")
                .Description("Updates the email")
                .Argument<NonNullGraphType<StringGraphType>>("email", "The emailr")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string email = context.GetArgument<string>("email");

                    await permissionValidator.Authorize(context, "writeLogins", loginId);

                    var command = new UpdateLoginCommand
                    {
                        Email = email,
                        IsEmailChanged = true,
                        ModifiedById = loginId
                    };

                    Result result = await authService.UpdateLoginAsync(tenantId, loginId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateLoginCredentials")
                .AuthorizeWith("any")
                .Description("Updates the email for specified login")
                .Argument<NonNullGraphType<StringGraphType>>("loginId", "The loginId which credentials should be updated")
                .Argument<StringGraphType>("email", "The new email")
                .Argument<StringGraphType>("username", "The new username")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeLogins");

                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetArgument<string>("loginId");
                    string email = context.GetArgument<string>("email");
                    string userName = context.GetArgument<string>("username");

                    var command = new UpdateLoginCommand
                    {
                        Email = email,
                        IsEmailChanged = !string.IsNullOrEmpty(email),
                        UserName = userName,
                        IsUserNameChanged = !string.IsNullOrEmpty(userName),
                        ModifiedById = loginId,
                    };

                    Result result = await authService.UpdateLoginAsync(tenantId, loginId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("confirmEmail")
                .Description("Verifies the code that has been sent to the email of the login.")
                .Argument<NonNullGraphType<StringGraphType>>("tenantId", "the tenant identifier")
                .Argument<NonNullGraphType<StringGraphType>>("loginId", "The login id")
                .Argument<NonNullGraphType<StringGraphType>>("code", "The email verificiation code")
                .Argument<DateTimeOffsetGraphType>("dob", "the date of birth")
                .Argument<SendNotificationInputGraphType>("sendNotificationInput", "Send a notification part of confirming email")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetArgument<string>("tenantId");
                    string loginId = context.GetArgument<string>("loginId");
                    string code = context.GetArgument<string>("code");
                    DateTime? dateOfBirth = context.GetArgument<DateTime?>("dob");
                    SendNotificationGraph sendNotificationGraph = context.GetArgument<SendNotificationGraph>("sendNotificationInput");

                    Result result = await authService.ConfirmEmailAsync(tenantId, loginId, new ConfirmEmailCommand
                    {
                        Code = code,
                        DateOfBirth = dateOfBirth,
                        ConfirmedById = loginId,
                        SendNotificationCommand = SendNotificationGraph.ToCommand(sendNotificationGraph)
                    });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("forgotPassword")
                .Description("Use when login forgot password")
                .Argument<NonNullGraphType<StringGraphType>>("tenantId", "the tenant identifier")
                .Argument<NonNullGraphType<ForgotPassordInputGraphType>>("forgotPasswordInput", "Input to ask for username or account email.")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetArgument<string>("tenantId");

                    var isDisAllowSendNotificationParametersFeatureEnabled = await multiTenantFeatureManager.IsEnabled("DisAllowSendNotificationParameters", tenantId);
                    if (isDisAllowSendNotificationParametersFeatureEnabled)
                        return Result.Failure($"forgotPassword is deprecated for Tenant: {tenantId}. Please use forgotPassword2");

                    ForgotPasswordCommand command = context.GetArgument<ForgotPasswordCommand>("forgotPasswordInput");
                    GraphQLUserContext userContext = (GraphQLUserContext)context.UserContext;
                    if (command.SendNotificationCommand != null)
                    {
                        if (tenantId == "tcb" || tenantId == "tcb_uat")
                            command.SendNotificationCommand.EmailMessage.Subject = null; // For TCB we do not want the user to be able to set the subject. This will ensure it is overridden with data in the template, if present.

                        Tools.PopulateRenderParameters(userContext, command.SendNotificationCommand.EmailMessage?.TemplateRendering?.Input, out List<string> renderErrors);
                        if (renderErrors.Any())
                            return new Result { Status = "failure", Errors = renderErrors };
                    }


                    string acceptLanguage = userContext.Headers.AcceptLanguage.ToString();
                    command.Language = String.IsNullOrEmpty(acceptLanguage) ? DefaultLanguage : acceptLanguage;

                    Result result = await authService.ForgotPasswordAsync(tenantId, command);

                    return result;
                });

            //AXATH-300 fix specifically for AXATH Pen Test fix
            Field<ResultGraphType>()
                .Name("forgotPassword2")
                .Description("Use when login forgot password")
                .Argument<NonNullGraphType<StringGraphType>>("tenantId", "the tenant identifier")
                .Argument<NonNullGraphType<ForgotPassord2InputGraphType>>("forgotPassword2Input", "Input to ask for username or account email.")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetArgument<string>("tenantId");

                    var isDisAllowSendNotificationParametersFeatureEnabled = await multiTenantFeatureManager.IsEnabled("DisAllowSendNotificationParameters", tenantId);
                    if (!isDisAllowSendNotificationParametersFeatureEnabled)
                        return Result.Failure($"TenantId : {tenantId} is not valid for this graphQL query");

                    ForgotPassword2Command command = context.GetArgument<ForgotPassword2Command>("forgotPassword2Input");
                    GraphQLUserContext userContext = (GraphQLUserContext)context.UserContext;
                    ForgotPasswordCommand forgotPasswordCommand = new ForgotPasswordCommand();
                    forgotPasswordCommand.ClientId = command.ClientId;
                    forgotPasswordCommand.Email = command.Email;

                    var forgotPasswordEmailSettings = forgotPasswordSettings.Value.Values.GetSettingsByTenantId(tenantId,command.ClientId);

                    if (forgotPasswordEmailSettings == null)
                        return Result.Failure("ForgotPasswordEmailSettings are not configured correctly.");

                    var client =(await authService.GetAppsAsync(tenantId, new Domain.QueryArguments()
                    {
                        Where = new AppWhere()
                        {
                            AppId = command.ClientId
                        }
                    })).FirstOrDefault();

                    forgotPasswordCommand.SendNotificationCommand = new SendNotificationCommand()
                    {
                        EmailMessage = new EmailMessage()
                        {
                            From = client?.Email ?? forgotPasswordEmailSettings.From,
                            FromName = client?.EmailSenderName ?? forgotPasswordEmailSettings.FromName,
                            Subject = forgotPasswordEmailSettings.Subject ?? "Forgot Password",
                            TemplateRendering = new TemplateRendering()
                            {
                                TemplateId = forgotPasswordEmailSettings.TemplateId,
                                Input = new RenderParameters()
                                {
                                    Name = "data",
                                    Content = JObject.Parse($@"{{
                                                                'link': ""{(!string.IsNullOrEmpty(client?.UrlRouting?.Url) ? client?.UrlRouting?.Url :  forgotPasswordEmailSettings.Link)}"",
                                                                'memberName': ""{command.Email}""}}")
                                }
                            }
                        }
                    };


                        Tools.PopulateRenderParameters(userContext, forgotPasswordCommand.SendNotificationCommand.EmailMessage?.TemplateRendering?.Input, out List<string> renderErrors);
                        if (renderErrors.Any())
                            return new Result { Status = "failure", Errors = renderErrors };


                    string acceptLanguage = userContext.Headers.AcceptLanguage.ToString();
                    command.Language = String.IsNullOrEmpty(acceptLanguage) ? DefaultLanguage : acceptLanguage;

                    Result result = await authService.ForgotPasswordAsync(tenantId, forgotPasswordCommand);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("changePassword")
                .Description("Use when login wants to change password")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<ChangePasswordInputGraphType>>("changePasswordInput", "Input to change the password")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    ChangePasswordCommand command = context.GetArgument<ChangePasswordCommand>("changePasswordInput");

                    Result result = await authService.ChangePasswordAsync(tenantId, loginId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateExpiredPassword")
                .Description("Expired password update flow")
                .Argument<NonNullGraphType<StringGraphType>>("tenantId", "target tenant id")
                .Argument<NonNullGraphType<ChangeExpiredPasswordInputGraphType>>("changePasswordInput", "Input to change the password")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetArgument<string>("tenantId");
                    ChangeExpiredPasswordCommand command =
                        context.GetArgument<ChangeExpiredPasswordCommand>("changePasswordInput");

                    Result result = await authService.ChangeExpiredPasswordAsync(tenantId, command);
                    return result;
                });

            FieldAsync<ResultGraphType>("resendConfirmationEmail", "Resend confirmation email to a login",
                            arguments: new QueryArguments(
                                new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "loginId", Description = "The login which email needs to be confirmed." },
                                new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "clientId", Description = "The client identifier" },
                                new QueryArgument<StringGraphType> { Name = "callbackUrl", Description = "The url to be used to process the confirmation code" },
                                new QueryArgument<SendNotificationInputGraphType> { Name = "sendNotificationInput", Description = "Send a notification part of confirming email" }
                            ),
                            resolve: async context =>
                            {
                                await permissionValidator.Authorize(context, "resendConfirmationEmail");

                                string tenantId = context.GetTenantIdFromToken();
                                string loginId = context.GetArgument<string>("loginId");
                                string clientId = context.GetArgument<string>("clientId");
                                string callbackUrl = context.GetArgument<string>("callbackUrl");
                                SendNotificationCommand sendNotificationCommand = SendNotificationGraph.ToCommand(context.GetArgument<SendNotificationGraph>("sendNotificationInput"));
                                if (sendNotificationCommand != null)
                                {
                                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, sendNotificationCommand.EmailMessage?.TemplateRendering?.Input, out List<string> renderErrors);
                                    if (renderErrors.Any())
                                        return new Result<CreatedStatus> { Status = "failure", Errors = renderErrors };
                                }
                                Result result = await authService.ResendConfirmationEmailAsync(tenantId, loginId,
                                    new ResendEmailCommand
                                    {
                                        ClientId = clientId,
                                        SendNotificationCommand = sendNotificationCommand,
                                        CallbackUrl = callbackUrl
                                    });

                                return result;
                            }).AuthorizeWith("any");

            //        Field<ResultGraphType>()
            //.Name("removeHostFromTenantSettings")
            //.AuthorizeWith("any")
            //.Description("removes a host from the tenant settings")
            //.Argument<StringGraphType>("host", "the host to remove")
            //.ResolveAsync(async context =>
            //{
            //    string tenantId = context.GetTenantIdFromToken();
            //    await permissionValidator.Authorize(context, new PermissionRequest("updateApps", "writeApps"));
            //    string host = context.GetArgument<string>("host");

            //    return await authService.RemoveHostFromTenantSettingsAsync(tenantId, host);
            //});


            Field<ResultGraphType>()
                .Name("sendCode")
                .Description("Sends a OTP code by phone")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("tenantId", "The targetted tenant id")
                .Argument<StringGraphType>("clientId", "The targetted client id")
                .Argument<StringGraphType>("loginId", "The targetted login id")
                .Argument<StringGraphType>("purpose", "The purpose")
                .Argument<StringGraphType>("templateId", "Optional template id to use")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetArgument<string>("tenantId") ?? context.GetTenantIdFromToken();
                    string loginId = context.GetArgument<string>("loginId") ?? context.GetLoginIdFromToken();
                    string clientId = context.GetArgument<string>("clientId") ?? context.GetClientIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    string purpose = context.GetArgument<string>("purpose");

                    Result result = await authService.SendCodeAsync(tenantId, loginId, new SendCodeCommand { ClientId = clientId, TemplateId = templateId, Purpose = purpose });

                    return result;
                }).AuthorizeWith("any");

            Field<ResultGraphType>()
                .Name("verifyCode")
                .Description("Verifies the OTP code and confirm phone and enables multi factor authentication")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("code", "The OTP code.")
                .Argument<StringGraphType>("tenantId", "The targetted tenant id")
                .Argument<StringGraphType>("loginId", "The targetted login id")
                .Argument<StringGraphType>("purpose", "The purpose")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetArgument<string>("tenantId") ?? context.GetTenantIdFromToken();
                    string loginId = context.GetArgument<string>("loginId") ?? context.GetLoginIdFromToken();
                    string code = context.GetArgument<string>("code");
                    string purpose = context.GetArgument<string>("purpose");

                    Result result = await authService.VerifyCodeAsync(tenantId, loginId, new VerifyCodeCommand { Code = code, Purpose = purpose });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("verifyResetPasswordCode")
                .Description("Verifies the OTP code for reset password")
                .Argument<NonNullGraphType<StringGraphType>>("code", "The OTP code.")
                .Argument<StringGraphType>("tenantId", "The targetted tenant id")
                .Argument<StringGraphType>("loginId", "The targetted login id")
                .Argument<StringGraphType>("purpose", "The purpose")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetArgument<string>("tenantId") ?? context.GetTenantIdFromToken();
                    string loginId = context.GetArgument<string>("loginId") ?? context.GetLoginIdFromToken();
                    string code = context.GetArgument<string>("code");
                    string purpose = context.GetArgument<string>("purpose");

                    return await authService.VerifyResetPasswordCodeAsync(tenantId, loginId, new VerifyCodeCommand { Code = code, Purpose = purpose });
                });

            FieldAsync<ResultGraphType>("deleteLogin", "Deletes a login",
                            arguments: new QueryArguments(
                                new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "loginId", Description = "the loginId of the login to delete" }
                            ),
                            resolve: async context =>
                            {
                                string tenantId = context.GetTenantIdFromToken();
                                string loginId = context.GetArgument<string>("loginId");

                                await permissionValidator.Authorize(context, "writeLogins", loginId);
                                string deletedById = context.GetLoginIdFromToken();

                                Result result = await authService.DeleteLoginAsync(tenantId, loginId, new DeleteCommand { DeletedById = deletedById });
                                return result;
                            }).AuthorizeWith("any"); ;

            FieldAsync<ResultGraphType>("addTargettedPermission", "Adds a permission to a target to a login",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "loginId", Description = "the login id of the the login whose permission will be added." },
                    new QueryArgument<NonNullGraphType<AddTargettedPermissionInputGraphType>> { Name = "addTargettedPermissionInput", Description = "The input necessary to add a permission to a target to a login" }
                ),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetArgument<string>("loginId");

                    AddTargettedPermissionCommand command = context.GetArgument<AddTargettedPermissionCommand>("addTargettedPermissionInput");
                    await permissionValidator.AuthorizeWriteTargettedPermissions(context, command.Type, command.Value);
                    command.AddedById = context.GetLoginIdFromToken();

                    if (IsAccessRestrictedContentPermission(command.Type))
                        await permissionValidator.Authorize(context, UserClaim.AddAccessRestrictedContentPermission.ToString());

                    Result result = await authService.AddTargettedPermissionsAsync(tenantId, loginId, new List<AddTargettedPermissionCommand> { command });
                    return result;
                });

            Field<ResultGraphType>()
                .Name("addTargettedPermissions")
                .Description("adds a list of targettedPermissions to a login")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("loginId", "the login id of the the login to whom the permissions will be added.")
                .Argument<NonNullGraphType<ListGraphType<AddTargettedPermissionInputGraphType>>>("inputs", "the targetted permission inputs")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetArgument<string>("loginId");

                    IEnumerable<AddTargettedPermissionCommand> commands = context.GetArgument<IEnumerable<AddTargettedPermissionCommand>>("inputs");
                    string addedById = context.GetLoginIdFromToken();
                    foreach (AddTargettedPermissionCommand command in commands)
                    {
                        //TODO: Optimize
                        await permissionValidator.AuthorizeWriteTargettedPermissions(
                            context, command.Type, command.Value);

                        if (IsAccessRestrictedContentPermission(command.Type))
                            await permissionValidator.Authorize(context, UserClaim.AddAccessRestrictedContentPermission.ToString());

                        command.AddedById = addedById;
                    }

                    return await authService.AddTargettedPermissionsAsync(tenantId, loginId, commands);
                });

            Field<ResultGraphType>()
                .Name("addToPermissionGroup")
                .Description("Adds a login to a permission group")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("loginId", "The login id of the the login whose permission will be added.")
                .Argument<NonNullGraphType<StringGraphType>>("permissionGroupId", "The id of the permission group.")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeTargettedPermissions");

                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetArgument<string>("loginId");
                    string permissionGroupId = context.GetArgument<string>("permissionGroupId");

                    PermissionGroup permissionGroup = await authService.GetPermissionGroupAsync(tenantId, permissionGroupId);
                    if (permissionGroup == null)
                        return new Result { Status = "failure", Errors = new List<string> { $"Permission Group '{permissionGroupId}' not found." } };

                    var command = new AddTargettedPermissionCommand
                    {
                        Type = "groups",
                        Value = permissionGroupId,
                        AddedById = context.GetLoginIdFromToken()
                    };

                    Result result = await authService.AddTargettedPermissionsAsync(tenantId, loginId, new List<AddTargettedPermissionCommand> { command });
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeFromPermissionGroup")
                .Description("Removes a login to a permission group")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("loginId", "The login id of the the login whose permission will be removed.")
                .Argument<NonNullGraphType<StringGraphType>>("permissionGroupId", "The id of the permission group.")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeTargettedPermissions");

                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetArgument<string>("loginId");
                    string permissionGroupId = context.GetArgument<string>("permissionGroupId");

                    var command = new RemoveTargettedPermissionCommand
                    {
                        Type = "groups",
                        Value = permissionGroupId,
                        RemovedById = context.GetLoginIdFromToken()
                    };

                    Result result = await authService.RemoveTargettedPermissionAsync(tenantId, loginId, command.Type, command.Value, command.RemovedById);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("addInheritedLoginToLogin")
                .Description("Adds a login to inherit from to a login")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("loginId", "The login id of the the login whose permission will be added.")
                .Argument<NonNullGraphType<StringGraphType>>("loginIdToInheritFrom", "The id of the login to inherit from.")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeTargettedPermissions");

                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetArgument<string>("loginId");
                    string loginIdToInheritFrom = context.GetArgument<string>("loginIdToInheritFrom");

                    var command = new AddTargettedPermissionCommand
                    {
                        Type = "logins",
                        Value = loginIdToInheritFrom,
                        AddedById = context.GetLoginIdFromToken()
                    };

                    Result result = await authService.AddTargettedPermissionsAsync(tenantId, loginId, new List<AddTargettedPermissionCommand> { command });
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeInheritedLoginFromLogin")
                .Description("Removes a login to inherit from from a login")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("loginId", "The login id of the the login whose permission will be removed.")
                .Argument<NonNullGraphType<StringGraphType>>("loginIdToStopInheritFrom", "The id of the login to stop inherit from.")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeTargettedPermissions");

                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetArgument<string>("loginId");
                    string loginIdToStopInheritFrom = context.GetArgument<string>("loginIdToStopInheritFrom");

                    var command = new RemoveTargettedPermissionCommand
                    {
                        Type = "logins",
                        Value = loginIdToStopInheritFrom,
                        RemovedById = context.GetLoginIdFromToken()
                    };

                    Result result = await authService.RemoveTargettedPermissionAsync(tenantId, loginId, command.Type, command.Value, command.RemovedById);
                    return result;
                });

            FieldAsync<ResultGraphType>("removeTargettedPermission", "Removes a targetted permission from a login",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "loginId", Description = "the login id of the the login whose permission will be deleted." },
                    new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "type", Description = "the type of the permission" },
                    new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "value", Description = "the value of the target" }
                ),
                resolve: async context =>
                {
                    await permissionValidator.Authorize(context, UserClaim.WriteTargettedPermissions.ToString());

                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetArgument<string>("loginId");

                    string type = context.GetArgument<string>("type");
                    string value = context.GetArgument<string>("value");

                    if (IsAccessRestrictedContentPermission(type))
                        await permissionValidator.Authorize(context, UserClaim.RemoveAccessRestrictedContentPermission.ToString());

                    Result result = await authService.RemoveTargettedPermissionAsync(tenantId, loginId, type, value, context.GetLoginIdFromToken());
                    return result;
                }).AuthorizeWith("any");

            Field<ResultGraphType>()
                .Name("removeTargettedPermissions")
                .Description("Removes a targetted permission from a login")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("loginId", "The login id of the the login whose permissions will be removed.")
                .Argument<NonNullGraphType<ListGraphType<RemoveTargettedPermissionInputGraphType>>>("inputs", "the targetted permissions to be removed")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetArgument<string>("loginId");

                    await permissionValidator.Authorize(context, UserClaim.WriteTargettedPermissions.ToString(), loginId);

                    IEnumerable<RemoveTargettedPermissionCommand> commands = context.GetArgument<IEnumerable<RemoveTargettedPermissionCommand>>("inputs");
                    string removedById = context.GetLoginIdFromToken();
                    foreach (RemoveTargettedPermissionCommand command in commands)
                    {
                        command.RemovedById = removedById;
                        if (IsAccessRestrictedContentPermission(command.Type))
                            await permissionValidator.Authorize(context, UserClaim.RemoveAccessRestrictedContentPermission.ToString());
                    }

                    return await authService.RemoveTargettedPermissionsAsync(tenantId, loginId, commands);
                });


            Field<ResultGraphType>()
                .Name("resetPassword")
                .Description("Can be used after inviting a login or after a login used forgotPassword. Use the code to reset the password")
                .Argument<NonNullGraphType<StringGraphType>>("tenantId", "the tenant identifier")
                .Argument<NonNullGraphType<StringGraphType>>("loginId", "the login id of the the login whose permission will be deleted.")
                .Argument<NonNullGraphType<StringGraphType>>("code", "the code verifying the login")
                .Argument<DateTimeOffsetGraphType>("dob", "the date of birth")
                .Argument<NonNullGraphType<StringGraphType>>("password", "the new password")
                .Argument<SendNotificationInputGraphType>("sendNotificationInput", "Send a notification part of confirming email")
                .ResolveAsync(async context =>
               {
                   string tenantId = context.GetArgument<string>("tenantId");
                   string loginId = context.GetArgument<string>("loginId");
                   string code = context.GetArgument<string>("code");
                   DateTime? dateOfBirth = context.GetArgument<DateTime?>("dob");
                   string password = context.GetArgument<string>("password");

                   SendNotificationGraph sendNotificationGraph = context.GetArgument<SendNotificationGraph>("sendNotificationInput");

                   Result result = await authService.ResetPasswordAsync(tenantId, loginId,
                       new ResetPasswordCommand
                       {
                           Code = code.Replace(' ', '+'),
                           Password = password,
                           DateOfBirth = dateOfBirth,
                           SendNotificationCommand = SendNotificationGraph.ToCommand(sendNotificationGraph)
                       });

                   return result;
               });

            FieldAsync<ResultGraphType>("createPermissionGroup", "Creates a group of permissions",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<CreatePermissionGroupGraphType>> { Name = "createPermissionGroupInput", Description = "Input to create permission group" }
                ),
                resolve: async context =>
                {
                    await permissionValidator.Authorize(context, "writeTargettedPermissions");

                    string tenantId = context.GetTenantIdFromToken();
                    CreatePermissionGroupCommand command = context.GetArgument<CreatePermissionGroupCommand>("createPermissionGroupInput");
                    command.CreatedById = context.GetLoginIdFromToken();

                    Result result = await authService.CreatePermissionGroupAsync(tenantId, command);

                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>("updatePermissionGroup", "Updates a permission group",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<UpdatePermissionGroupGraphType>> { Name = "updatePermissionGroupInput", Description = "Input to update permission group" },
                    new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "id", Description = "The id of the permission group to update" }
                ),
                resolve: async context =>
                {
                    await permissionValidator.Authorize(context, "updatePermissionGroups");

                    string tenantId = context.GetTenantIdFromToken();
                    UpdatePermissionGroupCommand command = context.GetArgument<UpdatePermissionGroupCommand>("updatePermissionGroupInput");
                    command.ModifiedById = context.GetLoginIdFromToken();

                    string id = context.GetArgument<string>("id");

                    Result result = await authService.UpdatePermissionGroupAsync(tenantId, id, command);

                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>("deletePermissionGroup", "Deletes a permission group",
                            arguments: new QueryArguments(
                                new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "id", Description = "The id of the permission group to delete" }
                            ),
                            resolve: async context =>
                            {
                                await permissionValidator.Authorize(context, "writeTargettedPermissions");

                                string tenantId = context.GetTenantIdFromToken();
                                string loginId = context.GetLoginIdFromToken();
                                string id = context.GetArgument<string>("id");

                                Result result = await authService.DeletePermissionGroupAsync(tenantId, id, new DeleteCommand { DeletedById = loginId });

                                return result;
                            }).AuthorizeWith("any");

            Field<ResultGraphType>()
                            .Name("addPermissionToPermissionGroup")
                            .Description("Adds a permission to a permission group.")
                            .Argument<NonNullGraphType<StringGraphType>>("id", "The id of the permission group.")
                            .Argument<NonNullGraphType<StringGraphType>>("permissionId", "The id of the permission to be added.")
                            .Argument<NonNullGraphType<StringGraphType>>("targetId", "The id of the target to be added. Put 'all' to add all targets.")
                            .ResolveAsync(async context =>
                            {
                                await permissionValidator.Authorize(context, UserClaim.WriteTargettedPermissions.ToString());

                                string id = context.GetArgument<string>("id");
                                string permissionId = context.GetArgument<string>("permissionId");
                                string targetId = context.GetArgument<string>("targetId");
                                string tenantId = context.GetTenantIdFromToken();

                                if (IsAccessRestrictedContentPermission(permissionId))
                                    await permissionValidator.Authorize(context, UserClaim.AddAccessRestrictedContentPermission.ToString());

                                Result result = await authService.AddPermissionToPermissionGroupAsync(tenantId, id, new AddPermissionToPermissionGroupCommand { PermissionId = permissionId, TargetId = targetId, AddedById = context.GetLoginIdFromToken() });
                                return result;
                            })
                            .AuthorizeWith("any");

            Field<ResultGraphType>()
                            .Name("removePermissionFromPermissionGroup")
                            .Description("Removes a permission to a permission group.")
                            .Argument<NonNullGraphType<StringGraphType>>("id", "The id of the permission group.")
                            .Argument<NonNullGraphType<StringGraphType>>("permissionId", "The id of the permission to be removed.")
                            .Argument<NonNullGraphType<StringGraphType>>("targetId", "The id of the target to be added. Put 'all' to add all targets.")
                            .ResolveAsync(async context =>
                            {
                                await permissionValidator.Authorize(context, "writeTargettedPermissions");

                                string id = context.GetArgument<string>("id");
                                string permissionId = context.GetArgument<string>("permissionId");
                                string targetId = context.GetArgument<string>("targetId");
                                string tenantId = context.GetTenantIdFromToken();

                                if (IsAccessRestrictedContentPermission(permissionId))
                                    await permissionValidator.Authorize(context, UserClaim.RemoveAccessRestrictedContentPermission.ToString());

                                Result result = await authService.RemovePermissionFromPermissionGroupAsync(tenantId, id, new RemovePermissionFromPermissionGroupCommand { PermissionId = permissionId, TargetId = targetId, RemovedById = context.GetLoginIdFromToken() });
                                return result;
                            })
                            .AuthorizeWith("any");

            Field<ResultGraphType>()
                            .Name("addPermissionGroupToPermissionGroup")
                            .Description("Adds a permission group to a permission group.")
                            .Argument<NonNullGraphType<StringGraphType>>("id", "The id of the permission group.")
                            .Argument<NonNullGraphType<StringGraphType>>("permissionGroupId", "The id of the permission group to be added.")
                            .ResolveAsync(async context =>
                            {
                                await permissionValidator.Authorize(context, "writeTargettedPermissions");

                                string id = context.GetArgument<string>("id");
                                string permissionGroupId = context.GetArgument<string>("permissionGroupId");
                                string tenantId = context.GetTenantIdFromToken();
                                Result result = await authService.AddPermissionGroupToPermissionGroupAsync(tenantId, id, new AddPermissionGroupToPermissionGroupCommand { PermissionGroupId = permissionGroupId, AddedById = context.GetLoginIdFromToken() });
                                return result;
                            })
                            .AuthorizeWith("any");

            Field<ResultGraphType>()
                            .Name("removePermissionGroupFromPermissionGroup")
                            .Description("Removes a permission group to a permission group.")
                            .Argument<NonNullGraphType<StringGraphType>>("id", "The id of the permission group.")
                            .Argument<NonNullGraphType<StringGraphType>>("permissionGroupId", "The id of the permission group to be removed.")
                            .ResolveAsync(async context =>
                            {
                                await permissionValidator.Authorize(context, "writeTargettedPermissions");

                                string id = context.GetArgument<string>("id");
                                string permissionId = context.GetArgument<string>("permissionGroupId");
                                string tenantId = context.GetTenantIdFromToken();
                                Result result = await authService.RemovePermissionGroupFromPermissionGroupAsync(tenantId, id, new RemovePermissionGroupFromPermissionGroupCommand { PermissionGroupId = permissionId, RemovedById = context.GetLoginIdFromToken() });
                                return result;
                            })
                            .AuthorizeWith("any");

            Field<ResultGraphType>()
                            .Name("addInheritedLoginToPermissionGroup")
                            .Description("Adds a login to inherited from to a permission group.")
                            .Argument<NonNullGraphType<StringGraphType>>("id", "The id of the permission group.")
                            .Argument<NonNullGraphType<StringGraphType>>("loginId", "The id of the login to inherit from.")
                            .ResolveAsync(async context =>
                            {
                                await permissionValidator.Authorize(context, "writeTargettedPermissions");

                                string id = context.GetArgument<string>("id");
                                string loginId = context.GetArgument<string>("loginId");
                                string tenantId = context.GetTenantIdFromToken();
                                Result result = await authService.AddLoginIdToPermissionGroupAsync(tenantId, id, new AddLoginPermissionsToPermissionGroupCommand { LoginId = loginId, AddedById = context.GetLoginIdFromToken() });
                                return result;
                            })
                            .AuthorizeWith("any");

            Field<ResultGraphType>()
                            .Name("removeInheritedLoginFromPermissionGroup")
                            .Description("Removes a login to inherited from from a permission group.")
                            .Argument<NonNullGraphType<StringGraphType>>("id", "The id of the permission group.")
                            .Argument<NonNullGraphType<StringGraphType>>("loginId", "The id of the login to stop inherit from.")
                            .ResolveAsync(async context =>
                            {
                                await permissionValidator.Authorize(context, "writeTargettedPermissions");

                                string id = context.GetArgument<string>("id");
                                string loginId = context.GetArgument<string>("loginId");
                                string tenantId = context.GetTenantIdFromToken();
                                Result result = await authService.RemoveLoginIdFromPermissionGroupAsync(tenantId, id, new RemoveLoginPermissionsFromPermissionGroupCommand { LoginId = loginId, RemovedById = context.GetLoginIdFromToken() });
                                return result;
                            })
                            .AuthorizeWith("any");

            Field<ResultGraphType>()
                .Name("createApp")
                .AuthorizeWith("any")
                .Description("Creates a new app.")
                .Argument<NonNullGraphType<CreateAppInputGraphType>>("input", "The app to be created.")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("createApps", "writeApps"));
                    CreateAppCommand command = context.GetArgument<CreateAppCommand>("input");
                    command.CreatedById = loginId;

                    return await authService.CreateAppAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("updateApp")
                .AuthorizeWith("any")
                .Description("Updates an existing app.")
                .Argument<NonNullGraphType<StringGraphType>>("appId", "The app identifier")
                .Argument<NonNullGraphType<UpdateAppInputGraphType>>("input", "The app to be created.")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string appId = context.GetArgument<string>("appId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateApps", "writeApps").WithTargetIds(appId));
                    UpdateAppCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateAppCommand>();
                    command.ModifiedById = loginId;

                    return await authService.UpdateAppAsync(tenantId, appId, command);
                });

            Field<ResultGraphType>()
                .Name("deleteApp")
                .AuthorizeWith("any")
                .Description("deletes an app")
                .Argument<NonNullGraphType<StringGraphType>>("appId", "The app identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string appId = context.GetArgument<string>("appId");
                    await permissionValidator.Authorize(context, new PermissionRequest("deleteApps", "writeApps").WithTargetIds(appId));

                    var command = new DeleteCommand
                    {
                        DeletedById = loginId
                    };

                    return await authService.DeleteAppAsync(tenantId, appId, command);
                });

            Field<ResultGraphType>()
              .Name("transferPermissions")
              .AuthorizeWith("any")
              .Description("transfers permissions from one login to another")
              .Argument<NonNullGraphType<StringGraphType>>("sourceId", "The identifier of the login to transfer from")
              .Argument<NonNullGraphType<StringGraphType>>("targetId", "The identifier of the login to transfer to")
              .Argument<NonNullGraphType<ListGraphType<AddTargettedPermissionInputGraphType>>>("targettedPermissionsToTransfer", "permissions to delegate")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string byId = context.GetLoginIdFromToken();
                  string sourceId = context.GetArgument<string>("sourceId");
                  string targetId = context.GetArgument<string>("targetId");

                  IEnumerable<AddTargettedPermissionCommand> targettedPermissionsToTransfer = context.GetArgument<IEnumerable<AddTargettedPermissionCommand>>("targettedPermissionsToTransfer");

                  Login sourceloginDao = await authService.GetLoginById(tenantId, sourceId);
                  if (sourceloginDao == null)
                      return new Result { Status = "failure", Errors = new List<string> { $"Login not found for id '{sourceId}'" } };

                  var errors = new List<string> { };
                  foreach (AddTargettedPermissionCommand addTargettedPermissionCommand in targettedPermissionsToTransfer)
                  {
                      await permissionValidator.Authorize(context, "transferPermissions", addTargettedPermissionCommand.Type);

                      IEnumerable<string> allowedIds = sourceloginDao.TargettedPermissions
                          .GetValueOrDefault(addTargettedPermissionCommand.Type);
                      if (!allowedIds?.Contains(addTargettedPermissionCommand.Value) ?? true)
                          errors.Add($"Source login does not have the permission '{addTargettedPermissionCommand.Type}':'{addTargettedPermissionCommand.Value}'");
                      addTargettedPermissionCommand.AddedById = byId;
                  }

                  if (errors.Any())
                      return new Result { Status = "failure", Errors = errors };

                  Result addResult = await authService.AddTargettedPermissionsAsync(tenantId, targetId, targettedPermissionsToTransfer);
                  if (addResult.Status != "success")
                      return addResult;

                  return await authService.RemoveTargettedPermissionsAsync(tenantId, sourceId, targettedPermissionsToTransfer.Select(t => new RemoveTargettedPermissionCommand { Type = t.Type, Value = t.Value, RemovedById = byId }));
              });

            Field<ResultGraphType>()
                .Name("assignPermissionGroup")
                .Description("Assigns a login to a permission group")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("loginId", "the identifier of the login to assign the permission group to")
                .Argument<NonNullGraphType<StringGraphType>>("permissionGroupId", "the identitifer of the permission group to assign")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetArgument<string>("loginId");
                    string permissionGroupId = context.GetArgument<string>("permissionGroupId");

                    await permissionValidator.Authorize(context, "assignPermissionGroup", permissionGroupId);

                    PermissionGroup permissionGroup = await authService.GetPermissionGroupAsync(tenantId, permissionGroupId);
                    if (permissionGroup == null)
                        return new Result { Status = "failure", Errors = new List<string> { $"Permission Group '{permissionGroupId}' not found." } };

                    var command = new AddTargettedPermissionCommand
                    {
                        Type = "groups",
                        Value = permissionGroupId,
                        AddedById = context.GetLoginIdFromToken()
                    };

                    return await authService.AddTargettedPermissionsAsync(tenantId, loginId, new List<AddTargettedPermissionCommand> { command });
                });

            Field<ResultGraphType>()
               .Name("unassignPermissionGroup")
               .Description("Unassigns a login from a permission group")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<StringGraphType>>("loginId", "the identifier of the login to unassign the permission group from")
               .Argument<NonNullGraphType<StringGraphType>>("permissionGroupId", "the identitifer of the permission group to unassign")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetArgument<string>("loginId");
                   string permissionGroupId = context.GetArgument<string>("permissionGroupId");

                   await permissionValidator.Authorize(context, "unassignPermissionGroup", permissionGroupId);

                   var command = new RemoveTargettedPermissionCommand
                   {
                       Type = "groups",
                       Value = permissionGroupId,
                       RemovedById = context.GetLoginIdFromToken()
                   };

                   return await authService.RemoveTargettedPermissionsAsync(tenantId, loginId, new List<RemoveTargettedPermissionCommand> { command });
               });

            Field<CreatedStatusResultGraphType>()
                .Name("createPermissionSchema")
                .AuthorizeWith("any")
                .Description("Creates a new targeted permisstion schema for fields.")
                .Argument<NonNullGraphType<CreatePermissionSchemaInputGraphType>>("input", "The app to be created.")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    await permissionValidator.Authorize(context, "writePermissionSchemas");
                    CreatePermissionSchemaCommand command = context.GetArgument<CreatePermissionSchemaCommand>("input");
                    command.CreatedById = loginId;

                    return await authService.CreatePermissionSchema(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("updatePermissionSchema")
                .AuthorizeWith("any")
                .Description("Updates an existing targeted permisstion schema for fields.")
                .Argument<NonNullGraphType<UpdatePermissionSchemaInputGraphType>>("input", "The permission schema to be updated.")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string permissionSchemaId = context.GetArgument<string>("permissionSchemaId");
                    await permissionValidator.Authorize(context, "writePermissionSchemas", permissionSchemaId);
                    UpdatePermissionSchemaCommand command = context.GetArgument<UpdatePermissionSchemaCommand>("input");
                    command.ModifiedById = loginId;

                    return await authService.UpdatePermissionSchema(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("deletePermissionSchema")
                .AuthorizeWith("any")
                .Description("deletes an targeted permisstion schema for fields.")
                .Argument<NonNullGraphType<StringGraphType>>("permissionSchemaId", "The permission schema identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string permissionSchemaId = context.GetArgument<string>("permissionSchemaId");
                    await permissionValidator.Authorize(context, "writePermissionSchemas", permissionSchemaId);

                    var command = new DeleteCommand { DeletedById = loginId };

                    return await authService.DeletePermissionSchema(tenantId, permissionSchemaId, command);
                });

            Field<ResultGraphType>()
                .Name("addTargetedPermissionSchemaToLogin")
                .Argument<NonNullGraphType<AddTargetedPermissionSchemaToLoginInputGraphType>>("input", "Add a permissionSchema to the login")
                .ResolveAsync(async context =>
                {
                    AddTargetedPermissionSchemaToLoginInput input = context.GetArgument<AddTargetedPermissionSchemaToLoginInput>("input");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    AddTargetedPermissionSchemaToLoginCommand command = new()
                    {
                        LoginId = input.LoginId,
                        PermissionSchemaId = input.PermissionSchemaId,
                        TargetIds = input.TargetIds,
                        AddedById = loginId
                    };

                    return await authService.AddTargetedPermissionSchemaToLogin(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("removeTargetedPermissionSchemaFromLogin")
                .Argument<NonNullGraphType<RemoveTargetedPermissionSchemaFromLoginInputGraphType>>("input", "Remove permissionSchema from login")
                .ResolveAsync(async context =>
                {
                    RemoveTargetedPermissionSchemaFromLoginInput input = context.GetArgument<RemoveTargetedPermissionSchemaFromLoginInput>("input");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    RemoveTargetedPermissionSchemaFromLoginCommand command = new()
                    {
                        LoginId = input.LoginId,
                        PermissionSchemaId = input.PermissionSchemaId,
                        TargetIds = input.TargetIds,
                        RemovedById = loginId
                    };

                    return await authService.RemoveTargetedPermissionSchemaFromLogin(tenantId, command);
                });

            #endregion

            InitializeProductMutations(authService, insurerService, l10nService, pricingService, productService, templateService, permissionValidator);
            InitializePoliciesMutations(authService, policyService, policyMembersService, policyCountersService, transactionService, entityService, templateService, caseService, companyService,
                individualService, internalService, objectService, organizationService, permissionValidator, mvcJsonOptions, policiesClient, mapper);
            InitializeTransactionsMutations(authService, transactionService, policyService, permissionValidator);
            InitializeNotificationsMutations(authService, notificationService, captchaVerificationService, permissionValidator, fileSystemService, templateService, issueDebitNoteNotificationSetttings);
            InitializeEntitiesMutations(authService, encryptionAlgorithm, policyService,
                fileSystemService, entityService, companyService, individualService, internalService, objectService,
                organizationService, disabilityService, diagnosisService, permissionValidator);
            InitializeClaimsMutations(claimService, authService, guaranteeOfPaymentService, claimRemarksService, tenantSpecificClaimService, treatmentService, claimReportService, permissionValidator);
            InitializeBindersMutations(binderService, permissionValidator);
            InitializeFilesMutations(fileSystemService, permissionValidator);
            InitializeCmsMutations(cmsService, permissionValidator);
            InitializeTemplatesMutations(templateService, permissionValidator);
            InitializeCasesMutations(templateService, authService, caseService, policyService, productService, internalService, permissionValidator, multiTenantFeatureManager);
            InitializeMigrationsMutations(authService, productService, policyService, notificationService,
                entityService, transactionService, fileSystemService, pricingService, l10nService, insurerService, permissionValidator);
            InitializeEducationMutations(educationService, permissionValidator);
            InitializeAchievementsMutations(achievementService, permissionValidator);
            InitializeAdvisorMutations(advisorService, permissionValidator);
            InitializeScriptsMutations(productService, permissionValidator);
            InitializeBenefitDefinitionsMutations(productService, permissionValidator);
            InitializeSchedulerMutations(schedulerService, permissionValidator);

            Field<EvaluateScriptResultGraphType>()
                .Name("evaluateScript")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<EvaluateScriptInputGraphType>>("input", "Evaluate a script")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();

                    EvaluateScriptInput input = context.GetArgument<EvaluateScriptInput>("input");

                    Script script = (await productService.GetScriptsAsync(tenantId, new() { Id = input.ScriptId })).First();
                    Product2 product = null;

                    if (script.Type == ScriptTypeEnum.Pricing && input.ProductId != null)
                    {
                        product = await GetProduct();
                        if (product?.ScriptIds != null && product.ScriptIds.Contains(input.ScriptId))
                            return await productService.Evaluate(tenantId, clientId, new()
                            {
                                ProductId = input.ProductId,
                                DataInput = input.DataInput,
                                ScriptType = ScriptTypeEnum.Pricing,
                                DistributorID = input.DistributorID,
                                CampaignCodes = input.CampaignCodes,
                                IsRenewal = input.IsRenewal,
                                Fields = product.Fields,
                                RatingFactorsTable = product.RatingFactorsTable,
                                Segments = product.Segments
                            });
                    }

                    string representation = input.Representation;
                    if (string.IsNullOrEmpty(representation) && input.ProductId != null)
                    {
                        product ??= await GetProduct();
                        if (product != null)
                        {
                            representation = product?.Representation;
                        }
                    }

                    EvaluateScriptCommand command = new()
                    {
                        Script = script,
                        Representation = representation,
                        DataInput = input.DataInput,
                        Fields = product?.Fields,
                        RatingFactorsTable = product?.RatingFactorsTable,
                        Segments = product?.Segments
                    };

                    if (script.Type == ScriptTypeEnum.Pricing && input.ProductId != null)
                    {
                        DateTime now = DateTime.UtcNow;
                        IReadOnlyCollection<DiscountCode> discountCodes = await productDiscountCodeService.QueryAsync(tenantId,
                            new QueryArguments<Filter<DiscountCodeFilter>>()
                            {
                                Where = new Filter<DiscountCodeFilter>()
                                {
                                    And = new List<Filter<DiscountCodeFilter>>()
                                    {
                                        new() { Where = new DiscountCodeFilter() { ValidFrom_lte = now } },
                                        new() {
                                            Or = new List<Filter<DiscountCodeFilter>>()
                                            {
                                                new(){Where = new DiscountCodeFilter() { ValidTo_exists = false }},
                                                new(){Where = new DiscountCodeFilter() { ValidTo_gte = now }}
                                            }
                                        },
                                        new()
                                        {
                                            Where = new DiscountCodeFilter()
                                            {
                                                ProductIds_contains = new ProductIdWhere()
                                                {
                                                    Type = input.ProductId.Type
                                                }
                                            }
                                        },
                                        new()
                                        {
                                            Where = new DiscountCodeFilter()
                                            {
                                                ProductIds_contains = new ProductIdWhere()
                                                {
                                                    Plan = input.ProductId.Plan
                                                }
                                            }
                                        }
                                    }
                                }
                            });

                        command.DiscountCodes = discountCodes.ToList();
                    }

                    return await productService.Evaluate(tenantId, command);

                    async Task<Product2?> GetProduct()
                    {
                        return (await productService.GetAsync(tenantId, clientId, new()
                        {
                            LoadRepresentation = true,
                            Where = new()
                            {
                                ProductId = new()
                                {
                                    Plan = input.ProductId.Plan,
                                    Type = input.ProductId.Type,
                                    Version = input.ProductId.Version
                                }
                            }
                        })).FirstOrDefault();
                    }
                });

            Field<FaceRecognitionResultGraphType>()
                .Name("faceRecognition")
                .Argument<NonNullGraphType<ListGraphType<ByteGraphType>>>("image", "the bytes of the image")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    FaceRecognitionInput input = new FaceRecognitionInput
                    {
                        Image = context.GetArgument<byte[]>("image"),
                    };

                    Result<FaceRecognitionOutput> result = tenantId switch
                    {
                        "wfp_dev" => await faceRecongitionService.AnalyzeAsync(tenantId, input),
                        "wfp" => await faceRecongitionService.AnalyzeAsync(tenantId, input),
                        _ => Result<FaceRecognitionOutput>.Failure($"No service specified for {tenantId}"),
                    };

                    return new Result<FaceRecognitionOutputGraph>
                    {
                        Status = result.Status,
                        Errors = result.Errors,
                        Value = FaceRecognitionOutputGraph.ToGraph(result.Value)
                    };
                });



            Field<ResultGraphType>()
                .Name("createUserStorageItem")
                .Argument<UserStorageItemCreateInputGraphType>("input", "UserStorageItem create input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    var input = context.GetArgument<CreateUserStorageItemCommand>("input");

                    return await authService.UserStorageCreateAsync(tenantId, loginId, input);
                });

            Field<ResultGraphType>()
                .Name("updateUserStorageItem")
                .Argument<UserStorageItemUpdateInputGraphType>("input", "UserStorageItem update input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    var input = context.GetArgument<UpdateUserStorageItemCommand>("input");

                    return await authService.UserStorageUpdateAsync(tenantId, loginId, input);
                });

            Field<ResultGraphType>()
                .Name("deleteUserStorageItem")
                .Argument<StringGraphType>("key", "UserStorageItem key")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string key = context.GetArgument<string>("key");

                    return await authService.UserStorageDeleteAsync(tenantId, loginId, key);
                });
        }

        private bool IsAccessRestrictedContentPermission(string permissionId) => permissionId == UserClaim.AccessRestrictedContent.ToString();


        private static CreateEntityBatchCommand<T> EntityPrimitiveBatchCommands<T, W>(string loginId, CreateEntityInputGraph input)
             where T : CreateEntityCommand
        {
            var batchCommand = new CreateEntityBatchCommand<T> { };

            switch (input) //ToDo make other entity types work
            {
                case CreateIndividualInputGraph _:
                    {
                        batchCommand.CreateEntityCommand = (input as CreateIndividualInputGraph).ToDomain(loginId) as T;
                        break;
                    }
                case CreateCompanyInputGraph _:
                    {
                        batchCommand.CreateEntityCommand = (input as CreateCompanyInputGraph).ToDomain(loginId) as T;
                        break;
                    }
                case CreateInternalInputGraph _:
                    {
                        batchCommand.CreateEntityCommand = (input as CreateInternalInputGraph).ToDomain(loginId) as T;
                        break;
                    }
                case CreateObjectInputGraph _:
                    {
                        batchCommand.CreateEntityCommand = (input as CreateObjectInputGraph).ToDomain(loginId) as T;
                        break;
                    }
                default:
                    {
                        break;
                    }
            }

            if (input.Email != null)
            {
                batchCommand.AddContactCommands.Add(new AddContactCommand
                {
                    Type = "email",
                    Value = input.Email,
                    AddedById = loginId
                });
            }

            if (input.TelephoneNumber != null)
            {
                batchCommand.AddContactCommands.Add(new AddContactCommand
                {
                    Type = "telephoneNumber",
                    Value = input.TelephoneNumber,
                    AddedById = loginId
                });
            }

            if (input.Addresses?.Any() ?? false)
                foreach (AddressInputGraph addressInput in input.Addresses)
                {
                    batchCommand.AddAddressCommands.Add(addressInput.ToDomain(loginId));
                }

            if (input.Facts?.Any() ?? false)
                foreach (AddFactInputGraph factInput in input.Facts)
                {
                    batchCommand.AddFactCommands.Add(factInput.ToCommand(loginId));
                }

            if (input.Identities?.Any() ?? false)
                foreach (AddIdentityCommand identityInput in input.Identities)
                {
                    identityInput.AddedById = loginId;
                    batchCommand.AddIdentityCommands.Add(identityInput);
                }

            if (input.Contacts?.Any() ?? false)
                foreach (AddContactCommand contactInput in input.Contacts)
                {
                    contactInput.AddedById = loginId;
                    batchCommand.AddContactCommands.Add(contactInput);
                }

            return batchCommand;
        }
    }
}
