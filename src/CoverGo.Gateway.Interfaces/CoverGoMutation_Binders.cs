using System.Collections.Generic;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Binder;
using CoverGo.Gateway.Interfaces.Binders;
using GraphQL.Authorization;
using GraphQL.Types;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeBindersMutations(IBinderService binderService, PermissionValidator permissionValidator)
        {
            Field<CreatedStatusResultGraphType>()
                .Name("createBinder")
                .Description("Creates a binder")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<BinderInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, "writeBinders");

                    CreateBinderCommand command = context.GetArgument<CreateBinderCommand>("input");

                    Result<CreatedStatus> result = await binderService.CreateAsync(tenantId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateBinder")
                .Description("Updates a binder")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("binderId", "The binder identifier")
                .Argument<NonNullGraphType<BinderInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string binderId = context.GetArgument<string>("binderId");
                    await permissionValidator.Authorize(context, "writeBinders", binderId);

                    UpdateBinderCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateBinderCommand>();

                    Result result = await binderService.UpdateAsync(tenantId, binderId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("deleteBinder")
                .Description("Deletes a binder")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("binderId", "The binder identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string binderId = context.GetArgument<string>("binderId");
                    await permissionValidator.Authorize(context, "writeBinders", binderId);

                    Result result = await binderService.DeleteAsync(tenantId, binderId,
                        new DeleteBinderCommand
                        {
                            DeletedById = context.GetLoginIdFromToken()
                        });

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("addFactToBinder")
                .Description("adds a fact to a binder")
                .Argument<NonNullGraphType<StringGraphType>>("binderId", "the binder identifier")
                .Argument<NonNullGraphType<AddFactInputGraphType>>("input", "the fact to be added")
                .ResolveAsync(async context =>
                {
                    string binderId = context.GetArgument<string>("binderId");
                    await permissionValidator.Authorize(context, "writeBinders", binderId);
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    AddFactInputGraph input = context.GetArgument<AddFactInputGraph>("input");

                    var command = new AddFactCommand
                    {
                        Type = input.Type,
                        Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                        AddedById = loginId
                    };

                    Result<CreatedStatus> result = await binderService.AddFactAsync(tenantId, binderId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateFactOfBinder")
                .Argument<NonNullGraphType<StringGraphType>>("binderId", "the binder identifier")
                .Argument<NonNullGraphType<StringGraphType>>("factId", "the fact identifier")
                .Argument<NonNullGraphType<UpdateFactInputGraphType>>("input", "the updated fact")
                .ResolveAsync(async context =>
                {
                    string binderId = context.GetArgument<string>("binderId");
                    string factId = context.GetArgument<string>("factId");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    await permissionValidator.Authorize(context, "writeBinders", binderId);
                    UpdateFactInputGraph input = context.GetArgument<UpdateFactInputGraph>("input");

                    var command = new UpdateFactCommand
                    {
                        Id = factId,
                        Type = input.Type,
                        Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                        ModifiedById = loginId
                    };


                    return await binderService.UpdateFactAsync(tenantId, binderId, command);
                });

            Field<ResultGraphType>()
                .Name("removeFactFromBinder")
                .Argument<NonNullGraphType<StringGraphType>>("binderId", "the binder identifier")
                .Argument<NonNullGraphType<StringGraphType>>("factId", "the fact identifier")
                .ResolveAsync(async context =>
                {
                    string binderId = context.GetArgument<string>("binderId");
                    string factId = context.GetArgument<string>("factId");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    await permissionValidator.Authorize(context, "writeBinders", binderId);

                    var command = new RemoveFactCommand
                    {
                        Id = factId,
                        RemovedById = loginId
                    };

                    return await binderService.RemoveFactAsync(tenantId, binderId, command);
                });
        }
    }
}