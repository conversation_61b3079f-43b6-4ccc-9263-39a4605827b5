using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Encryptions;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.Notifications;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Infrastructure.DLVNServices.DLVNCommands;
using CoverGo.Gateway.Interfaces.Auth;
using CoverGo.Gateway.Interfaces.NegotiatedRate;
using CoverGo.Gateway.Interfaces.Notifications;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Templates;
using CoverGo.Gateway.Interfaces.Users;
using CoverGo.Gateway.Interfaces.Users.Kyc;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.NegotiatedRate;
using CoverGo.Users.Domain.Objects;
using CoverGo.FeatureManagement;
using GraphQL;
using GraphQL.Authorization;
using GraphQL.Types;
using GraphQL.Validation;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Object = CoverGo.Users.Domain.Objects.Object;
using Organization = CoverGo.Gateway.Domain.Users.Organization;
using QueryArguments = GraphQL.Types.QueryArguments;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeEntitiesMutations(
            IAuthService authService,
            IEncryptionAlgorithm encryptionAlgorithm,
            IPolicyService policyService,
            IFileSystemService fileSystemService,
            IEntityService entityService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
            IEntityService<Object, CreateObjectCommand, UpdateObjectCommand> objectService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            CoverGoDisabilityService disabilityService,
            CoverGoDiagnosisService diagnosisService,
            PermissionValidator permissionValidator,
            IMultiTenantFeatureManager multiTenantFeatureManager)
        {
            Field<CreatedStatusResultGraphType>()
                .Name("createCompany")
                .AuthorizeWith("any")
                .Description("Creates a company")
                .Argument<NonNullGraphType<CreateCompanyInputGraphType>>("createCompanyInput", "The company input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest(UserClaim.CreateCompanies.ToString(), UserClaim.WriteCompanies.ToString()));

                    CreateCompanyInputGraph input = context.GetArgument<CreateCompanyInputGraph>("createCompanyInput");

                    CreateEntityBatchCommand<CreateCompanyCommand> batchCommand =
                        EntityPrimitiveBatchCommands<CreateCompanyCommand, CreateCompanyInputGraph>(loginId, input);

                    bool creatingRestrictedCompany = batchCommand.CreateEntityCommand.AccessPolicy is AccessPolicy.Restricted;
                    if (creatingRestrictedCompany) 
                        await permissionValidator.Authorize(context, UserClaim.AccessRestrictedContent.ToString(), UserClaimValues.AccessRestrictedContent.Full);

                    Result<CreatedStatus> result = await companyService.CreateFromBatchAsync(tenantId, batchCommand);

                    if (await permissionValidator.HasCreatorRights(context))
                        await authService.AddTargettedPermissionsAsync(tenantId, loginId,
                            new List<AddTargettedPermissionCommand>
                            {
                                new()
                                {
                                    AddedById = loginId, Type = UserClaim.ReadCompanies.ToString(), Value = result.Value.Id
                                },
                                new()
                                {
                                    AddedById = loginId, Type = UserClaim.WriteCompanies.ToString(), Value = result.Value.Id
                                }
                            });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateCompany")
                .AuthorizeWith("any")
                .Description("Updates a company")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The company identifier")
                .Argument<NonNullGraphType<UpdateCompanyInputGraphType>>("updateCompanyInput",
                    "The update company input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string id = context.GetArgument<string>("id");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCompanies", "writeCompanies").WithTargetIds(id));

                    UpdateCompanyCommand command = context.GetArgument<Dictionary<string, object>>("updateCompanyInput")
                        .ToUpdateCommand<UpdateCompanyCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    if (!await context.IsJsonPatchPermitted(authService, command.FieldsPatch, id, "entity"))
                        throw new ValidationError("", "authorization",
                            $"{tenantId}: You are not authorized these fields" + command.FieldsPatch);

                    Result result = await companyService.UpdateAsync(tenantId, id, command);

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createIndividual")
                .AuthorizeWith("any")
                .Description("Creates an individual")
                .Argument<NonNullGraphType<CreateIndividualInputGraphType>>("createIndividualInput",
                    "The create individual input")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("createIndividuals", "writeIndividuals"));
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    CreateIndividualInputGraph input =
                        context.GetArgument<CreateIndividualInputGraph>("createIndividualInput");

                    // this is custom logic sepcifically for DLVN for the Pen Test reported ticket
                    (bool, string) validationResult = DLVNHelper.ValidateDlvnFields(input.Fields, tenantId);
                    if (!validationResult.Item1)
                        return Result.Failure(validationResult.Item2);

                    CreateEntityBatchCommand<CreateIndividualCommand> batchCommand =
                        EntityPrimitiveBatchCommands<CreateIndividualCommand, CreateIndividualInputGraph>(loginId,
                            input);
                    Result<CreatedStatus> result = await individualService.CreateFromBatchAsync(tenantId, batchCommand);
                    if (result.Status != "success")
                        return result;

                    //ToDo: clean
                    if (await permissionValidator.HasCreatorRights(context))
                        await authService.AddTargettedPermissionsAsync(tenantId, loginId,
                            new List<AddTargettedPermissionCommand>
                            {
                                new()
                                {
                                    AddedById = loginId, Type = "readIndividuals", Value = result.Value.Id
                                },
                                new()
                                {
                                    AddedById = loginId, Type = "writeIndividuals", Value = result.Value.Id
                                }
                            });

                    var addTargettedPermissionCommands = new List<AddTargettedPermissionCommand> { };
                    if (await permissionValidator.HasCreatorRightsTargetted(context, "readIndividuals"))
                        addTargettedPermissionCommands.Add(new AddTargettedPermissionCommand
                        {
                            AddedById = loginId,
                            Type = "readIndividuals",
                            Value = result.Value.Id
                        });
                    if (await permissionValidator.HasCreatorRightsTargetted(context, "writeIndividuals"))
                        addTargettedPermissionCommands.Add(new AddTargettedPermissionCommand
                        {
                            AddedById = loginId,
                            Type = "writeIndividuals",
                            Value = result.Value.Id
                        });
                    if (await permissionValidator.HasCreatorRightsTargetted(context, "writeIndividualAddresses"))
                        addTargettedPermissionCommands.Add(new AddTargettedPermissionCommand
                        {
                            AddedById = loginId,
                            Type = "writeIndividualAddresses",
                            Value = result.Value.Id
                        });
                    if (await permissionValidator.HasCreatorRightsTargetted(context, "writeIndividualIdentities"))
                        addTargettedPermissionCommands.Add(new AddTargettedPermissionCommand
                        {
                            AddedById = loginId,
                            Type = "writeIndividualIdentities",
                            Value = result.Value.Id
                        });
                    if (await permissionValidator.HasCreatorRightsTargetted(context, "writeIndividualContacts"))
                        addTargettedPermissionCommands.Add(new AddTargettedPermissionCommand
                        {
                            AddedById = loginId,
                            Type = "writeIndividualContacts",
                            Value = result.Value.Id
                        });
                    if (await permissionValidator.HasCreatorRightsTargetted(context, "writeIndividualFacts"))
                        addTargettedPermissionCommands.Add(new AddTargettedPermissionCommand
                        {
                            AddedById = loginId,
                            Type = "writeIndividualFacts",
                            Value = result.Value.Id
                        });

                    if (addTargettedPermissionCommands.Any())
                        await authService.AddTargettedPermissionsAsync(tenantId, loginId,
                            addTargettedPermissionCommands);

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createIndividuals")
                .AuthorizeWith("any")
                .Description("Creates individuals")
                .Argument<NonNullGraphType<ListGraphType<NonNullGraphType<CreateIndividualInputGraphType>>>>("individualsToCreate",
                    "Individuals to create")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("createIndividuals", "writeIndividuals"));
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    IEnumerable<CreateIndividualInputGraph> individualsToCreate = context.GetArgument<IEnumerable<CreateIndividualInputGraph>>("individualsToCreate");

                    List<CreateEntityBatchCommand<CreateIndividualCommand>> batchCommands = new();
                    foreach (CreateIndividualInputGraph individualToCreate in individualsToCreate)
                    {
                        // this is custom logic sepcifically for DLVN for the Pen Test reported ticket
                        (bool, string) validationResult = DLVNHelper.ValidateDlvnFields(individualToCreate.Fields, tenantId);
                        if (!validationResult.Item1)
                            return Result.Failure(validationResult.Item2);

                        batchCommands.Add(EntityPrimitiveBatchCommands<CreateIndividualCommand, CreateIndividualInputGraph>(loginId, individualToCreate));
                    }

                    Result<CreatedStatus> result = await individualService.CreateManyFromBatchAsync(tenantId, batchCommands);
                    if (result.Status != "success")
                        return result;

                    //ToDo: clean
                    if (await permissionValidator.HasCreatorRights(context))
                        await authService.AddTargettedPermissionsAsync(tenantId, loginId, result.Value.Ids.SelectMany(id => new[]
                        {
                            new AddTargettedPermissionCommand { AddedById = loginId, Type = "readIndividuals", Value = id },
                            new AddTargettedPermissionCommand { AddedById = loginId, Type = "writeIndividuals", Value = id }
                        }).ToList());

                    List<AddTargettedPermissionCommand> addTargettedPermissionCommands = new();
                    if (await permissionValidator.HasCreatorRightsTargetted(context, "readIndividuals"))
                        addTargettedPermissionCommands.AddRange(result.Value.Ids.Select(id => new AddTargettedPermissionCommand
                        {
                            AddedById = loginId,
                            Type = "readIndividuals",
                            Value = id
                        }));
                    if (await permissionValidator.HasCreatorRightsTargetted(context, "writeIndividuals"))
                        addTargettedPermissionCommands.AddRange(result.Value.Ids.Select(id => new AddTargettedPermissionCommand
                        {
                            AddedById = loginId,
                            Type = "writeIndividuals",
                            Value = id
                        }));
                    if (await permissionValidator.HasCreatorRightsTargetted(context, "writeIndividualAddresses"))
                        addTargettedPermissionCommands.AddRange(result.Value.Ids.Select(id => new AddTargettedPermissionCommand
                        {
                            AddedById = loginId,
                            Type = "writeIndividualAddresses",
                            Value = id
                        }));
                    if (await permissionValidator.HasCreatorRightsTargetted(context, "writeIndividualIdentities"))
                        addTargettedPermissionCommands.AddRange(result.Value.Ids.Select(id => new AddTargettedPermissionCommand
                        {
                            AddedById = loginId,
                            Type = "writeIndividualIdentities",
                            Value = id
                        }));
                    if (await permissionValidator.HasCreatorRightsTargetted(context, "writeIndividualContacts"))
                        addTargettedPermissionCommands.AddRange(result.Value.Ids.Select(id => new AddTargettedPermissionCommand
                        {
                            AddedById = loginId,
                            Type = "writeIndividualContacts",
                            Value = id
                        }));
                    if (await permissionValidator.HasCreatorRightsTargetted(context, "writeIndividualFacts"))
                        addTargettedPermissionCommands.AddRange(result.Value.Ids.Select(id => new AddTargettedPermissionCommand
                        {
                            AddedById = loginId,
                            Type = "writeIndividualFacts",
                            Value = id
                        }));

                    if (addTargettedPermissionCommands.Any())
                        await authService.AddTargettedPermissionsAsync(tenantId, loginId, addTargettedPermissionCommands);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateIndividual")
                .AuthorizeWith("any")
                .Description("Updates a individual")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The individual identifier")
                .Argument<NonNullGraphType<UpdateIndividualInputGraphType>>("updateIndividualInput",
                    "The update individual input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string id = context.GetArgument<string>("id");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateIndividuals", "writeIndividuals").WithTargetIds(id));

                    UpdateIndividualCommand command = context
                        .GetArgument<Dictionary<string, object>>("updateIndividualInput")
                        .ToUpdateCommand<UpdateIndividualCommand>();

                    // this is custom logic sepcifically for DLVN for the Pen Test reported ticket
                    (bool, string) validationResult = DLVNHelper.ValidateDlvnFields(command.Fields, tenantId);
                    if (!validationResult.Item1)
                        return Result.Failure(validationResult.Item2);

                    command.ModifiedById = context.GetLoginIdFromToken();

                    if (!await context.IsJsonPatchPermitted(authService, command.FieldsPatch, id, "entity"))
                        throw new ValidationError("", "authorization",
                            $"{tenantId}: You are not authorized these fields" + command.FieldsPatch);

                    Result result = await individualService.UpdateAsync(tenantId, id, command);

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createInternal")
                .AuthorizeWith("any")
                .Description("Creates an internal user")
                .Argument<NonNullGraphType<CreateInternalInputGraphType>>("createInternalInput",
                    "The create internal input")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeInternals");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    CreateInternalInputGraph input =
                        context.GetArgument<CreateInternalInputGraph>("createInternalInput");
                    CreateEntityBatchCommand<CreateInternalCommand> batchCommand =
                        EntityPrimitiveBatchCommands<CreateInternalCommand, CreateInternalInputGraph>(loginId, input);
                    Result<CreatedStatus> result = await internalService.CreateFromBatchAsync(tenantId, batchCommand);
                    if (result.Status != "success")
                        return result;

                    if (await permissionValidator.HasCreatorRights(context))
                        await authService.AddTargettedPermissionsAsync(tenantId, loginId,
                            new List<AddTargettedPermissionCommand>
                            {
                                new()
                                {
                                    AddedById = loginId, Type = "readInternals", Value = result.Value.Id
                                },
                                new()
                                {
                                    AddedById = loginId, Type = "writeInternals", Value = result.Value.Id
                                }
                            });

                    return result;
                });


            Field<CreatedStatusResultGraphType>()
                .Name("createOrganization")
                .AuthorizeWith("any")
                .Description("Creates an organization user")
                .Argument<NonNullGraphType<CreateOrganizationInputGraphType>>("createOrganizationInput",
                    "The create organization input")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("createOrganizations", "writeOrganizations"));
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    CreateOrganizationCommand command =
                        context.GetArgument<CreateOrganizationCommand>("createOrganizationInput");
                    command.CreatedById = loginId;

                    Result<CreatedStatus> result = await organizationService.CreateAsync(tenantId, command);

                    if (await permissionValidator.HasCreatorRights(context))
                        await authService.AddTargettedPermissionsAsync(tenantId, loginId,
                            new List<AddTargettedPermissionCommand>
                            {
                                new()
                                {
                                    AddedById = loginId, Type = "readOrganizations", Value = result.Value.Id
                                },
                                new()
                                {
                                    AddedById = loginId, Type = "writeOrganizations", Value = result.Value.Id
                                }
                            });

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("registerIndividual")
                .Description("Register new individual with an associated login.")
                .Argument<NonNullGraphType<StringGraphType>>("tenantId", "The tenant identifier")
                .Argument<NonNullGraphType<StringGraphType>>("clientId", "The client identifier")
                .Argument<NonNullGraphType<RegisterIndividualInputGraphType>>("registerIndividualInput",
                    "The register individual input")
                .ResolveAsync(context =>
                    context.ResolveRegisterIndividualAsync(authService, individualService, entityService));

            Field<ResultGraphType>()
                .Name("updateMeAsIndividual")
                .AuthorizeWith("any")
                .Description("Updates the authenticated individual")
                .Argument<NonNullGraphType<UpdateIndividualInputGraphType>>("updateIndividualInput",
                    "The update individual input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    UpdateIndividualCommand command = context
                        .GetArgument<Dictionary<string, object>>("updateIndividualInput")
                        .ToUpdateCommand<UpdateIndividualCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    if (!await context.IsJsonPatchPermitted(authService, command.FieldsPatch, userId, "entity"))
                        throw new ValidationError("", "authorization",
                            $"{tenantId}: You are not authorized these fields" + command.FieldsPatch);

                    Result result = await individualService.UpdateAsync(tenantId, userId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateMeAsInternal")
                .AuthorizeWith("any")
                .Description("Updates the authenticated internal")
                .Argument<NonNullGraphType<UpdateInternalInputGraphType>>("updateInternalInput",
                    "The update individual input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    UpdateInternalCommand command = context
                        .GetArgument<Dictionary<string, object>>("updateInternalInput")
                        .ToUpdateCommand<UpdateInternalCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    if (!await context.IsJsonPatchPermitted(authService, command.FieldsPatch, userId, "entity"))
                        throw new ValidationError("", "authorization",
                            $"{tenantId}: You are not authorized these fields" + command.FieldsPatch);

                    Result result = await internalService.UpdateAsync(tenantId, userId, command);

                    return result;
                });

            Field<LoginGraphType, LoginGraph>()
                .Name("inviteInternal")
                .AuthorizeWith("any")
                .Description("Invite an internal and creates a login and minimal permissions for him.")
                .Argument<NonNullGraphType<StringGraphType>>("clientId", "The client identifier")
                .Argument<NonNullGraphType<InviteInternalInputGraphType>>("inviteInternalInput",
                    "The invite internal input")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeLogins");

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetArgument<string>("clientId");
                    string loginId = context.GetLoginIdFromToken();
                    InviteInternalInputGraph userInput =
                        context.GetArgument<InviteInternalInputGraph>("inviteInternalInput");

                    Login existingLoginWithUsername = await authService.GetLoginByNameAsync(tenantId, userInput.Username ?? userInput.Email);
                    if (existingLoginWithUsername != null)
                        throw new ExecutionError($"Login with username '{userInput.Username ?? userInput.Email}' already exists.");

                    string internalEntityId;
                    if (userInput.EntityId != null)
                    {
                        Internal inter =
                            (await internalService.GetAsync(tenantId, new EntityWhere { Id = userInput.EntityId }))
                            .FirstOrDefault();
                        internalEntityId = inter.Id;
                    }
                    else
                    {
                        Result<CreatedStatus> createdInternalResult = await internalService.CreateAsync(tenantId,
                            new CreateInternalCommand
                            {
                                EnglishFirstName = userInput.EnglishFirstName,
                                EnglishLastName = userInput.EnglishLastName,
                                ChineseFirstName = userInput.ChineseFirstName,
                                ChineseLastName = userInput.ChineseLastName,
                                Description = userInput.Description,
                                InternalCode = userInput.InternalCode,
                                InternalCodeLength = userInput.InternalCodeLength,
                                Title = userInput.Title,
                                NameFormat = userInput.NameFormat,
                                CreatedById = loginId
                            });

                        if (createdInternalResult.Status == "failure")
                            throw new ExecutionError(createdInternalResult.Errors.FirstOrDefault());

                        internalEntityId = createdInternalResult.Value.Id;

                        // add email
                        if (userInput.Email != null)
                            await entityService.AddContactAsync(tenantId, createdInternalResult.Value.Id,
                                EntityTypes.Internal,
                                new AddContactCommand { Type = "email", Value = userInput.Email, AddedById = loginId });

                        // add telephone
                        if (userInput.TelephoneNumber != null)
                            await entityService.AddContactAsync(tenantId, createdInternalResult.Value.Id,
                                EntityTypes.Internal,
                                new AddContactCommand
                                {
                                    Type = "telephoneNumber",
                                    Value = userInput.TelephoneNumber,
                                    AddedById = loginId
                                });
                    }

                    // create login
                    Result<CreatedStatus> loginCreatedResult = await authService.CreateLoginAsync(tenantId,
                        new CreateLoginCommand
                        {
                            ClientId = clientId,
                            Email = userInput.Email,
                            Username = userInput.Username ?? userInput.Email,
                            Password = userInput.Password,
                            CreatedById = loginId,
                            EntityId = internalEntityId,
                            EntityType = "internal"
                        });
                    if (loginCreatedResult.Status == "failure")
                        throw new ExecutionError(loginCreatedResult.Errors.FirstOrDefault());

                    await authService.AddTargettedPermissionsAsync(tenantId, loginCreatedResult.Value.Id,
                        new List<AddTargettedPermissionCommand>
                        {
                            new()
                            {
                                AddedById = loginId, Type = "clientId", Value = clientId
                            }
                        });

                    if(await permissionValidator.HasCreatorRights(context))
                        await authService.AddTargettedPermissionsAsync(tenantId, loginId,
                            new List<AddTargettedPermissionCommand>
                            {
                                new()
                                {
                                    AddedById = loginId,
                                    Type = "readLogins",
                                    Value = loginCreatedResult.Value.Id
                                },
                                new()
                                {
                                    AddedById = loginId,
                                    Type = "writeLogins",
                                    Value = loginCreatedResult.Value.Id
                                },
                                new()
                                {
                                    AddedById = loginId, Type = "readInternals", Value = internalEntityId
                                },
                                new()
                                {
                                    AddedById = loginId, Type = "writeInternals", Value = internalEntityId
                                }
                            });

                    Login login = await authService.GetLoginById(tenantId, loginCreatedResult.Value.Id);

                    return LoginGraph.ToGraph(login);
                });

            Field<LoginGraphType, LoginGraph>()
                .Name("inviteIndividual")
                .AuthorizeWith("any")
                .Description("Invite an individual and creates a login and minimal permissions for him.")
                .Argument<NonNullGraphType<StringGraphType>>("clientId", "The client identifier")
                .Argument<NonNullGraphType<InviteIndividualInputGraphType>>("inviteIndividualInput",
                    "The invite individual input")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeLogins");

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetArgument<string>("clientId");
                    string loginId = context.GetLoginIdFromToken();
                    InviteIndividualInputGraph userInput =
                        context.GetArgument<InviteIndividualInputGraph>("inviteIndividualInput");

                    string individualEntityId;
                    if (userInput.EntityId != null)
                    {
                        Individual individual =
                            (await individualService.GetAsync(tenantId, new EntityWhere { Id = userInput.EntityId }))
                            .FirstOrDefault();
                        individualEntityId = individual.Id;
                    }
                    else
                    {
                        Result<CreatedStatus> createdIndividualResult = await individualService.CreateAsync(tenantId,
                            new CreateIndividualCommand
                            {
                                EnglishFirstName = userInput.EnglishFirstName,
                                EnglishLastName = userInput.EnglishLastName,
                                ChineseFirstName = userInput.ChineseFirstName,
                                ChineseLastName = userInput.ChineseLastName,
                                Salutation = userInput.Salutation,
                                InternalCode = userInput.InternalCode,
                                InternalCodeLength = userInput.InternalCodeLength,
                                NameFormat = userInput.NameFormat,
                                CreatedById = loginId
                            });

                        if (createdIndividualResult.Status == "failure")
                            throw new ExecutionError(createdIndividualResult.Errors.FirstOrDefault());

                        individualEntityId = createdIndividualResult.Value.Id;

                        // add email
                        if (userInput.Email != null)
                            await entityService.AddContactAsync(tenantId, createdIndividualResult.Value.Id,
                                EntityTypes.Individual,
                                new AddContactCommand { Type = "email", Value = userInput.Email, AddedById = loginId });

                        // add telephone
                        if (userInput.TelephoneNumber != null)
                            await entityService.AddContactAsync(tenantId, createdIndividualResult.Value.Id,
                                EntityTypes.Individual,
                                new AddContactCommand
                                {
                                    Type = "telephoneNumber",
                                    Value = userInput.TelephoneNumber,
                                    AddedById = loginId
                                });
                    }

                    // create login
                    Result<CreatedStatus> loginCreatedResult = await authService.CreateLoginAsync(tenantId,
                        new CreateLoginCommand
                        {
                            ClientId = clientId,
                            Email = userInput.Email,
                            Username = userInput.Username ?? userInput.Email,
                            Password = userInput.Password,
                            CreatedById = loginId,
                            EntityId = individualEntityId,
                            EntityType = "individual"
                        });
                    if (loginCreatedResult.Status == "failure")
                        throw new ExecutionError(loginCreatedResult.Errors.FirstOrDefault());

                    await authService.AddTargettedPermissionsAsync(tenantId, loginCreatedResult.Value.Id,
                        new List<AddTargettedPermissionCommand>
                        {
                            new()
                            {
                                AddedById = loginId, Type = "clientId", Value = clientId
                            }
                        });

                    if (await permissionValidator.HasCreatorRights(context))
                        await authService.AddTargettedPermissionsAsync(tenantId, loginId,
                            new List<AddTargettedPermissionCommand>
                            {
                                new()
                                {
                                    AddedById = loginId,
                                    Type = "readLogins",
                                    Value = loginCreatedResult.Value.Id
                                },
                                new()
                                {
                                    AddedById = loginId,
                                    Type = "writeLogins",
                                    Value = loginCreatedResult.Value.Id
                                },
                                new()
                                {
                                    AddedById = loginId, Type = "readIndividuals", Value = individualEntityId
                                },
                                new()
                                {
                                    AddedById = loginId, Type = "writeIndividuals", Value = individualEntityId
                                }
                            });

                    Login login = await authService.GetLoginById(tenantId, loginCreatedResult.Value.Id);

                    return LoginGraph.ToGraph(login);
                });

            Field<ListGraphType<CreatedStatusResultGraphType>>()
                .Name("inviteEntityToLoginBatch")
                .AuthorizeWith("any")
                .Description(
                    "Invite entities by creating logins and associating them to entities from the specified entityIds in batch.")
                .Argument<NonNullGraphType<StringGraphType>>("clientId", "The client identifier")
                .Argument<NonNullGraphType<ListGraphType<NonNullGraphType<InviteEntityInputGraphType>>>>("input", "The invite entity input")
                .Argument<StringGraphType>("permissioningToken", "")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetArgument<string>("clientId");
                    string loginId = context.GetLoginIdFromToken();
                    string? permissioningToken = context.GetArgument<string?>("permissioningToken");
                    
                    await permissionValidator.Authorize(context, "inviteEntityToLogin", clientId);
                    InviteEntityInputGraph[] input = context.GetArgument<InviteEntityInputGraph[]>("input");

                    List<Result<CreatedStatus>> results = new();

                    foreach (var inputItem in input)
                    {
                        try
                        {
                            Result<CreatedStatus> result = await ProcessInviteEntityAsync(
                                context, authService, encryptionAlgorithm, entityService, permissionValidator,
                                tenantId, clientId, loginId, inputItem, permissioningToken, multiTenantFeatureManager);
                            results.Add(result);
                        }
                        catch (Exception ex)
                        {
                            results.Add(new Result<CreatedStatus>
                            {
                                Status = "failure",
                                Errors = new List<string> { $"Error processing entity '{inputItem.EntityId ?? "unknown"}': {ex.Message}" }
                            });
                        }
                    }

                    return results;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("inviteEntityToLogin")
                .AuthorizeWith("any")
                .Description(
                    "Invite an entity by creating a login and associate it to the entity from the specified entityId.")
                .Argument<NonNullGraphType<StringGraphType>>("clientId", "The client identifier")
                .Argument<NonNullGraphType<InviteEntityInputGraphType>>("input", "The invite entity input")
                .Argument<StringGraphType>("permissioningToken", "")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetArgument<string>("clientId");
                    string loginId = context.GetLoginIdFromToken();
                    string? permissioningToken = context.GetArgument<string?>("permissioningToken");
                    
                    await permissionValidator.Authorize(context, "inviteEntityToLogin", clientId);
                    InviteEntityInputGraph input = context.GetArgument<InviteEntityInputGraph>("input");

                    return await ProcessInviteEntityAsync(
                        context, authService, encryptionAlgorithm, entityService, permissionValidator,
                        tenantId, clientId, loginId, input, permissioningToken, multiTenantFeatureManager);
                });

            Field<ResultGraphType>()
                .Name("updateInternal")
                .AuthorizeWith("any")
                .Description("Updates an internal")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The internal identifier")
                .Argument<NonNullGraphType<UpdateInternalInputGraphType>>("updateInternalInput",
                    "The update internal input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string id = context.GetArgument<string>("id");
                    await permissionValidator.Authorize(context, "writeInternals", id);

                    UpdateInternalCommand command = context
                        .GetArgument<Dictionary<string, object>>("updateInternalInput")
                        .ToUpdateCommand<UpdateInternalCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    if (!await context.IsJsonPatchPermitted(authService, command.FieldsPatch, id, "entity"))
                        throw new ValidationError("", "authorization",
                            $"{tenantId}: You are not authorized these fields" + command.FieldsPatch);


                    List<string> axaThailandsTenantIds = new List<string>() { "axaTh_test", "axaTh_uat", "axaTh_preprod", "axaTh_uatx", "axaTh_prod" };
                    if (axaThailandsTenantIds.Any(tenant => tenant == tenantId))
                        ValidateAxaThailandStatus();

                    Result result = await internalService.UpdateAsync(tenantId, id, command);

                    return result;

                    void ValidateAxaThailandStatus()
                    {
                        List<string> possibleStatuses = new List<string>() { "ACTIVE", "ARCHIVED" };

                        if (command.IsStatusChanged &&
                            !string.IsNullOrEmpty(command.Status) &&
                            !possibleStatuses.Contains(command.Status))
                        {
                            throw new ValidationError("", "Invalid Status", $"{tenantId}: The status for internal in invalid : {command.Status}");
                        }
                    }
                });

            Field<ResultGraphType>()
                .Name("updateOrganization")
                .AuthorizeWith("any")
                .Description("Updates an organization")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The organization identifier")
                .Argument<NonNullGraphType<UpdateOrganizationInputGraphType>>("updateOrganizationInput",
                    "The update organization input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string id = context.GetArgument<string>("id");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateOrganizations", "writeOrganizations").WithTargetIds(id));

                    UpdateOrganizationCommand command = context
                        .GetArgument<Dictionary<string, object>>("updateOrganizationInput")
                        .ToUpdateCommand<UpdateOrganizationCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    if (!await context.IsJsonPatchPermitted(authService, command.FieldsPatch, id, "entity"))
                        throw new ValidationError("", "authorization",
                            $"{tenantId}: You are not authorized these fields" + command.FieldsPatch);

                    Result result = await organizationService.UpdateAsync(tenantId, id, command);

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createObject")
                .AuthorizeWith("any")
                .Description("Creates an object")
                .Argument<NonNullGraphType<CreateObjectInputGraphType>>("createObjectInput", "The create object input")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeObjects");

                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    CreateObjectInputGraph input = context.GetArgument<CreateObjectInputGraph>("createObjectInput");

                    CreateEntityBatchCommand<CreateObjectCommand> batchCommand =
                        EntityPrimitiveBatchCommands<CreateObjectCommand, CreateObjectInputGraph>(loginId, input);
                    Result<CreatedStatus> result = await objectService.CreateFromBatchAsync(tenantId, batchCommand);

                    if (await permissionValidator.HasCreatorRights(context))
                        await authService.AddTargettedPermissionsAsync(tenantId, loginId,
                            new List<AddTargettedPermissionCommand>
                            {
                                new()
                                {
                                    AddedById = loginId, Type = "readObjects", Value = result.Value.Id
                                },
                                new()
                                {
                                    AddedById = loginId, Type = "writeObjects", Value = result.Value.Id
                                }
                            });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateObject")
                .AuthorizeWith("any")
                .Description("Updates an object")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The object identifier")
                .Argument<NonNullGraphType<UpdateObjectInputGraphType>>("updateObjectInput", "The update object input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string id = context.GetArgument<string>("id");
                    await permissionValidator.Authorize(context, "writeObjects", id);

                    UpdateObjectCommand command = context.GetArgument<Dictionary<string, object>>("updateObjectInput")
                        .ToUpdateCommand<UpdateObjectCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    if (!await context.IsJsonPatchPermitted(authService, command.FieldsPatch, id, "entity"))
                        throw new ValidationError("", "authorization",
                            $"{tenantId}: You are not authorized these fields" + command.FieldsPatch);

                    Result result = await objectService.UpdateAsync(tenantId, id, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("deleteEntity")
                .Description("deletes an entity")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the id of the entity to delete")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("id");
                    string deletedById = context.GetLoginIdFromToken();
                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        .FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };

                    if (entity.Type == "individual") await permissionValidator.Authorize(context, new PermissionRequest("deleteIndividuals", "writeIndividuals"));
                    else if (entity.Type == "internal") await permissionValidator.Authorize(context, "writeInternals");
                    else if (entity.Type == "company") await permissionValidator.Authorize(context, new PermissionRequest("deleteCompanies", "writeCompanies"));
                    else if (entity.Type == "object") await permissionValidator.Authorize(context, "writeObjects");
                    else if (entity.Type == "organization") await permissionValidator.Authorize(context, new PermissionRequest("deleteOrganizations", "writeOrganizations"));

                    return (entity.Type == "individual")
                        ? await individualService.DeleteAsync(tenantId, entityId, deletedById)
                        : (entity.Type == "internal")
                            ? await internalService.DeleteAsync(tenantId, entityId, deletedById)
                            : (entity.Type == "company")
                                ? await companyService.DeleteAsync(tenantId, entityId, deletedById)
                                : (entity.Type == "object")
                                    ? await objectService.DeleteAsync(tenantId, entityId, deletedById)
                                    : (entity.Type == "organization")
                                        ? await organizationService.DeleteAsync(tenantId, entityId, deletedById)
                                        : new Result { Status = "failure" };
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createServiceItem")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateServiceItemInputGraphType>>("input", "Create a ServiceItem")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeServiceItems");
                    string tenantId = context.GetTenantIdFromToken();
                    CreateServiceItemInput input = context.GetArgument<CreateServiceItemInput>("input");
                    string loginId = context.GetLoginIdFromToken();
                    var command = new CreateServiceItemCommand
                    {
                        Name = input.Name,
                        Description = input.Description,
                        CreatedById = loginId
                    };
                    return await entityService.CreateServiceItemAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("updateServiceItem")
                .AuthorizeWith("any")
                .Description("Modifies a ServiceItem")
                .Argument<NonNullGraphType<UpdateServiceItemInputGraphType>>("input", "the modified ServiceItem")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeServiceItems");
                    string tenantId = context.GetTenantIdFromToken();
                    UpdateServiceItemInput input = context.GetArgument<UpdateServiceItemInput>("input");

                    return await entityService.UpdateServiceItemAsync(tenantId, new UpdateServiceItemCommand
                    {
                        Id = input.Id,
                        Name = input.Name,
                        Description = input.Description,
                        IsNameChanged = input.IsNameChanged,
                        IsDescriptionChanged = input.IsDescriptionChanged,
                        ModifiedById = context.GetLoginIdFromToken(),
                    });
                });

            Field<ResultGraphType>()
                .Name("deleteServiceItem")
                .Argument<NonNullGraphType<DeleteServiceItemInputGraphType>>("input", "Delete a ServiceItem")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeServiceItems");
                    string tenantId = context.GetTenantIdFromToken();
                    DeleteServiceItemInput command = context.GetArgument<DeleteServiceItemInput>("input");
                    string loginId = context.GetLoginIdFromToken();

                    return await entityService.DeleteServiceItemAsync(tenantId, command.ServiceItemId,
                        new DeleteCommand { DeletedById = loginId });
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createServiceItemAgreedFee")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateServiceItemAgreedFeeInputGraphType>>("input",
                    "Create a serviceItemAgreedFee")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeServiceItems");
                    string tenantId = context.GetTenantIdFromToken();
                    CreateServiceItemAgreedFeeInput input =
                        context.GetArgument<CreateServiceItemAgreedFeeInput>("input");
                    string loginId = context.GetLoginIdFromToken();
                    var command = new CreateServiceItemAgreedFeeCommand
                    {
                        Currency = input.Currency,
                        Rate = input.Rate,
                        ServiceItemId = input.ServiceItemId,
                        CreatedById = loginId
                    };
                    return await entityService.CreateServiceItemAgreedFeeAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("updateServiceItemAgreedFee")
                .AuthorizeWith("any")
                .Description("Modifies a serviceItemAgreedFee")
                .Argument<NonNullGraphType<UpdateServiceItemAgreedFeeInputGraphType>>("input",
                    "the modified serviceItemAgreedFee")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeServiceItems");
                    string tenantId = context.GetTenantIdFromToken();
                    UpdateServiceItemAgreedFeeInput input =
                        context.GetArgument<UpdateServiceItemAgreedFeeInput>("input");

                    return await entityService.UpdateServiceItemAgreedFeeAsync(tenantId,
                        new UpdateServiceItemAgreedFeeCommand
                        {
                            Id = input.Id,
                            Rate = input.Rate,
                            Currency = input.Currency,
                            ServiceItemId = input.ServiceItemId,
                            IsRateChanged = input.IsRateChanged,
                            IsCurrencyChanged = input.IsCurrencyChanged,
                            IsServiceItemIdChanged = input.IsServiceItemIdChanged,
                            ModifiedById = context.GetLoginIdFromToken(),
                        });
                });

            Field<ResultGraphType>()
                .Name("deleteServiceItemAgreedFee")
                .Argument<NonNullGraphType<DeleteServiceItemAgreedFeeInputGraphType>>("input",
                    "Delete a serviceItemAgreedFee")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeServiceItems");
                    string tenantId = context.GetTenantIdFromToken();
                    DeleteServiceItemAgreedFeeInput command =
                        context.GetArgument<DeleteServiceItemAgreedFeeInput>("input");
                    string loginId = context.GetLoginIdFromToken();

                    return await entityService.DeleteServiceItemAgreedFeeAsync(tenantId, command.ServiceItemAgreedFeeId,
                        new DeleteCommand { DeletedById = loginId });
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createPanelProviderTier")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreatePanelProviderTierInputGraphType>>("input",
                    "Create a PanelProviderTier")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("createPanelProviderTiers", "writePanelProviderTiers"));
                    string tenantId = context.GetTenantIdFromToken();

                    CreatePanelProviderTierInputGraph input =
                        context.GetArgument<CreatePanelProviderTierInputGraph>("input");
                    var command = new PanelProviderTierCreateCommand
                    {
                        PanelId = input.PanelId,
                        Name = input.Name,
                        Description = input.Description,
                        ServiceItemAgreedFeeIds = input.ServiceItemAgreedFeeIds,
                        CreatedById = context.GetLoginIdFromToken()
                    };

                    if (input.Fields != null)
                    {
                        if (input.Fields.TryParseToJToken(out JToken jTokenFields))
                        {
                            command.Fields = jTokenFields;
                        }
                        else
                        {
                            return new Result<CreatedStatus>()
                            {
                                Status = "failure",
                                Errors = new[] { "Fields is not an invalid JSON" }
                            };
                        }
                    }

                    return await entityService.CreatePanelProviderTierAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("updatePanelProviderTier")
                .AuthorizeWith("any")
                .Description("Modifies a PanelProviderTier")
                .Argument<NonNullGraphType<StringGraphType>>("panelProviderTierId",
                    "the panel provider tier identifier")
                .Argument<NonNullGraphType<UpdatePanelProviderTierInputGraphType>>("input",
                    "the modified PanelProviderTier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string panelProviderTierId = context.GetArgument<string>("panelProviderTierId");

                    await permissionValidator.Authorize(context, new PermissionRequest("updatePanelProviderTiers", "writePanelProviderTiers").WithTargetIds(panelProviderTierId));

                    UpdatePanelProviderTierInputGraph input =
                        context.GetArgument<UpdatePanelProviderTierInputGraph>("input");
                    PanelProviderTierUpdateCommand command = context.GetArgument<Dictionary<string, object>>("input")
                        .ToUpdateCommand<PanelProviderTierUpdateCommand>();
                    if (input.Fields != null)
                    {
                        if (input.Fields.TryParseToJToken(out JToken jTokenFields))
                        {
                            command.Fields = jTokenFields;
                            command.IsFieldsChanged = true;
                        }
                        else
                        {
                            return new Result<CreatedStatus>()
                            {
                                Status = "failure",
                                Errors = new[] { "Fields is not an invalid JSON" }
                            };
                        }
                    }

                    command.ModifiedById = context.GetLoginIdFromToken();

                    return await entityService.UpdatePanelProviderTierAsync(tenantId, panelProviderTierId, command);
                });

            Field<ResultGraphType>()
                .Name("deletePanelProviderTier")
                .Description("Deletes a panel provider tier")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("panelProviderTierId",
                    "the panel provider tier identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string panelProviderTierId = context.GetArgument<string>("panelProviderTierId");

                    await permissionValidator.Authorize(context, new PermissionRequest("deletePanelProviderTiers", "writePanelProviderTiers").WithTargetIds(panelProviderTierId));

                    var command = new DeleteCommand { DeletedById = context.GetLoginIdFromToken() };
                    return await entityService.DeletePanelProviderTierAsync(tenantId, panelProviderTierId, command);
                });

            Field<ResultGraphType>()
                .Name("addPanelProviderTierServiceItemAgreedFee")
                .Description("Adds a serviceItemAgreedFee to a panelProviderTier")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("panelProviderTierId", "The panelProviderTier identifier")
                .Argument<NonNullGraphType<StringGraphType>>("serviceItemAgreedFeeId",
                    "The serviceItemAgreedFee identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string panelProviderTierId = context.GetArgument<string>("panelProviderTierId");
                    string serviceItemAgreedFeeId = context.GetArgument<string>("serviceItemAgreedFeeId");

                    await permissionValidator.Authorize(context, new PermissionRequest("updatePanelProviderTiers", "writePanelProviderTiers").WithTargetIds(panelProviderTierId));

                    var command = new ServiceItemAgreedFeeAddCommand
                    {
                        Id = serviceItemAgreedFeeId,
                        AddedById = context.GetLoginIdFromToken()
                    };

                    Result result =
                        await entityService.AddServiceItemAgreedFeeAsync(tenantId, panelProviderTierId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removePanelProviderTierServiceItemAgreedFee")
                .Description("Removes a serviceItemAgreedFee to a panelProviderTier")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("panelProviderTierId", "The panelProviderTier identifier")
                .Argument<NonNullGraphType<StringGraphType>>("serviceItemAgreedFeeId",
                    "The serviceItemAgreedFee identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string panelProviderTierId = context.GetArgument<string>("panelProviderTierId");
                    string serviceItemAgreedFeeId = context.GetArgument<string>("serviceItemAgreedFeeId");

                    await permissionValidator.Authorize(context, new PermissionRequest("updatePanelProviderTiers", "writePanelProviderTiers").WithTargetIds(panelProviderTierId));

                    var command = new ServiceItemAgreedFeeRemoveCommand
                    {
                        Id = serviceItemAgreedFeeId,
                        RemovedById = context.GetLoginIdFromToken()
                    };

                    Result result =
                        await entityService.RemoveServiceItemAgreedFeeAsync(tenantId, panelProviderTierId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("panelProviderTierServiceItemAgreedFeeBatch")
                .Description("Removes a serviceItemAgreedFee to a panelProviderTier")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("panelProviderTierId", "The panelProviderTier identifier")
                .Argument<NonNullGraphType<ServiceItemAgreedFeeBatchInputGraphType>>("input",
                    "The serviceItemAgreedFeeBatch input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string panelProviderTierId = context.GetArgument<string>("panelProviderTierId");

                    await permissionValidator.Authorize(context, new PermissionRequest("updatePanelProviderTiers", "writePanelProviderTiers").WithTargetIds(panelProviderTierId));

                    var command = context.GetArgument<ServiceItemAgreedFeeBatchCommand>("input");

                    Result result =
                        await entityService.ServiceItemAgreedFeeBatchAsync(tenantId, panelProviderTierId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("addPanelProviderTierAttachment")
                .Description("Adds an attachment to a panel provider tier")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("panelProviderTierId", "The panelProviderTier identifier")
                .Argument<NonNullGraphType<AttachmentInputGraphType>>("input", "The attachment input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string panelProviderTierId = context.GetArgument<string>("panelProviderTierId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePanelProviderTiers", "writePanelProviderTiers"));
                    AddAttachmentCommand command = context.GetArgument<AddAttachmentCommand>("input");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result result = await entityService.AddAttachmentAsync(tenantId, panelProviderTierId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removePanelProviderTierAttachment")
                .Description("removes an attachment from a panel provider tier")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("panelProviderTierId", "The panelProviderTier identifier")
                .Argument<NonNullGraphType<StringGraphType>>("path", "The attachment path")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePanelProviderTiers", "writePanelProviderTiers"));
                    string tenantId = context.GetTenantIdFromToken();
                    string panelProviderTierId = context.GetArgument<string>("panelProviderTierId");
                    string path = context.GetArgument<string>("path");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await entityService.RemoveAttachmentAsync(tenantId, panelProviderTierId,
                        new RemoveAttachmentCommand() { Path = path, RemovedById = removedById });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("addCommission")
                .AuthorizeWith("any")
                .Description("Adds a commission split to a policy")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy id")
                .Argument<NonNullGraphType<CommissionInputGraphType>>("input", "The commission input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    CommissionInputGraph graph = context.GetArgument<CommissionInputGraph>("input");

                    var command = new AddCommissionCommand
                    {
                        EntityId = graph.EntityId,
                        JsonRule = graph.JsonRule,
                        Amount = graph.Amount,
                        CurrencyCode = graph.CurrencyCode,
                        Remark = graph.Remark,
                        AddedById = loginId
                    };

                    Result result = await policyService.AddCommissionAsync(tenantId, policyId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateCommission")
                .AuthorizeWith("any")
                .Description("Updates a policy commission split")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy id")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the commission id")
                .Argument<NonNullGraphType<CommissionInputGraphType>>("input", "The commission input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string id = context.GetArgument<string>("id");

                    UpdateCommissionCommand command = context.GetArgument<Dictionary<string, object>>("input")
                        .ToUpdateCommand<UpdateCommissionCommand>();
                    command.Id = id;
                    command.ModifiedById = loginId;

                    Result result = await policyService.UpdateCommissionAsync(tenantId, policyId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeCommission")
                .AuthorizeWith("any")
                .Description("Removes a commission split from a policy")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy id")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the commission id")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string id = context.GetArgument<string>("id");

                    var command = new RemoveCommissionCommand { Id = id, RemovedById = loginId };

                    Result result = await policyService.RemoveCommissionAsync(tenantId, policyId, command);
                    return result;
                });


            Field<CreatedStatusResultGraphType>()
                .Name("addLink")
                .Description("Creates a link between two entities")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<AddLinkInputGraphType>>("linkInput", "The link input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    AddLinkCommandInputGraph input = context.GetArgument<AddLinkCommandInputGraph>("linkInput");

                    await permissionValidator.AuthorizeWriteLinks(context, input.SourceId, input.Link, input.TargetId);
                    IEnumerable<Entity> entities = await entityService.GenericQueryAsync(
                        tenantId, new EntityWhere { Id_in = new List<string> { input.SourceId, input.TargetId } });

                    Result<CreatedStatus> result = await entityService.AddLinkAsync(tenantId, new AddLinkCommand
                    {
                        AddedById = context.GetLoginIdFromToken(),
                        SourceId = input.SourceId,
                        SourceEntityType = entities?.FirstOrDefault(entity => entity.Id == input.SourceId)?.EntityType,
                        TargetId = input.TargetId,
                        TargetEntityType = entities?.FirstOrDefault(entity => entity.Id == input.TargetId)?.EntityType,
                        Link = input.Link,
                        Value = input.Value != null
                            ? JToken.FromObject(input.Value?.GetValue())
                            : input.Values != null
                                ? JToken.FromObject(input.Values?.ToDictionary(x => x.Key, x => (object)x.Value))
                                : null
                    });

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("addLinks")
                .Description("Creates links between entities")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<ListGraphType<NonNullGraphType<AddLinkInputGraphType>>>>("linksToAdd", "Links to add")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    IEnumerable<AddLinkCommandInputGraph> linksToAdd = context.GetArgument<IEnumerable<AddLinkCommandInputGraph>>("linksToAdd");

                    await permissionValidator.AuthorizeWriteLinks(context, linksToAdd.Select(l => (l.SourceId, l.Link, l.TargetId)));

                    IEnumerable<Entity> entities = await entityService.GenericQueryAsync(
                        tenantId, new EntityWhere { Id_in = linksToAdd.SelectMany(l => new[] { l.SourceId, l.TargetId }).Distinct().ToList() });
                    Dictionary<string, string> entityIdToEntityType = entities.ToDictionary(e => e.Id, e => e.EntityType);

                    Result<CreatedStatus> result = await entityService.AddLinksAsync(tenantId, linksToAdd.Select(l => new AddLinkCommand
                    {
                        AddedById = context.GetLoginIdFromToken(),
                        SourceId = l.SourceId,
                        SourceEntityType = entityIdToEntityType[l.SourceId],
                        TargetId = l.TargetId,
                        TargetEntityType = entityIdToEntityType[l.TargetId],
                        Link = l.Link,
                        Value = l.Value != null
                            ? JToken.FromObject(l.Value?.GetValue())
                            : l.Values != null
                                ? JToken.FromObject(l.Values?.ToDictionary(x => x.Key, x => (object)x.Value))
                                : null
                    }).ToList());

                    return result;
                });

            FieldAsync<CreatedStatusResultGraphType>(
                "addMyLink",
                description: "Adds a link to the authenticated login",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "link",
                        Description = "The link input"
                    },
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "targetId",
                        Description = "The link input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    string link = context.GetArgument<string>("link");
                    string targetId = context.GetArgument<string>("targetId");

                    var command = new AddLinkCommand { SourceId = userId, Link = link, TargetId = targetId };
                    command.AddedById = context.GetLoginIdFromToken();

                    Result<CreatedStatus> result = await entityService.AddLinkAsync(tenantId, command);
                    return result;
                }).AuthorizeWith("any");

            Field<ResultGraphType>()
                .Name("removeLink")
                .Description("removes a link")
                .Argument<RemoveLinkInputGraphType>("linkInput", "The link input")
                .Argument<StringGraphType>("id", "the link identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    await permissionValidator.Authorize(context, "writeLinks");
                    string linkId = context.GetArgument<string>("id");

                    if (linkId != null)
                        return await entityService.RemoveLinkAsync(tenantId, linkId,
                            new DeleteCommand { DeletedById = loginId });

                    RemoveLinkCommand command = context.GetArgument<RemoveLinkCommand>("linkInput");
                    command.RemovedById = loginId;

                    Result result = await entityService.RemoveLinkAsync(tenantId, command);
                    return result;
                });


            FieldAsync<CreatedStatusResultGraphType>(
                "addIdentity",
                description: "Adds an identity to an entity",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<AddIdentityInputGraphType>>
                    {
                        Name = "identityInput",
                        Description = "The identity input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result<CreatedStatus>
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result<CreatedStatus>
                        {
                            Status = "failure",
                            Errors = new List<string> { "Invalid Entity Type" }
                        };

                    await permissionValidator.Authorize(context, GetPermissionRequestForEntity(type, entityId));

                    AddIdentityCommand command = context.GetArgument<AddIdentityCommand>("identityInput");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result<CreatedStatus> result =
                        await entityService.AddIdentityAsync(tenantId, entityId, type, command);
                    return result;
                }).AuthorizeWith("any");

            FieldAsync<CreatedStatusResultGraphType>(
                "addMyIdentity",
                description: "Adds an identity to the authenticated login",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<AddIdentityInputGraphType>>
                    {
                        Name = "identityInput",
                        Description = "The identity input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();

                    AddIdentityCommand command = context.GetArgument<AddIdentityCommand>("identityInput");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result<CreatedStatus> result =
                        await entityService.AddIdentityAsync(tenantId, userId, type, command);
                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>(
                "updateIdentity",
                description: "Updates an identity from the entity",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<UpdateIdentityInputGraphType>>
                    {
                        Name = "updateIdentityInput",
                        Description = "The update identity input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, GetPermissionRequestForEntity(type, entityId));

                    UpdateIdentityCommand command = context.GetArgument<UpdateIdentityCommand>("updateIdentityInput");
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await entityService.UpdateIdentityAsync(tenantId, entityId, type, command);
                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>(
                "updateMyIdentity",
                description: "Updates an identity from the authenticated login",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<UpdateIdentityInputGraphType>>
                    {
                        Name = "updateIdentityInput",
                        Description = "The update identity input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    UpdateIdentityCommand command = context.GetArgument<UpdateIdentityCommand>("updateIdentityInput");
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await entityService.UpdateIdentityAsync(tenantId, userId, type, command);
                    return result;
                }).AuthorizeWith("any");

            FieldAsync<CreatedStatusResultGraphType>(
                "addAddress",
                description: "Adds an address to an entity",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<AddAddressInputGraphType>>
                    {
                        Name = "addressInput",
                        Description = "The address input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result<CreatedStatus>
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result<CreatedStatus>
                        {
                            Status = "failure",
                            Errors = new List<string> { "Invalid Entity Type" }
                        };

                    await permissionValidator.Authorize(context, GetPermissionRequestForEntity(type, entityId));

                    AddressInputGraph input = context.GetArgument<AddressInputGraph>("addressInput");

                    Result<CreatedStatus> result = await entityService.AddAddressAsync(tenantId, entityId, type,
                        new AddAddressCommand
                        {
                            Type = input.Type,
                            Fields = input.Fields.ToDictionary(k => k.Type, v => v.Value),
                            AddedById = context.GetLoginIdFromToken()
                        });

                    return result;
                }).AuthorizeWith("any");

            FieldAsync<CreatedStatusResultGraphType>(
                "addMyAddress",
                description: "Adds an address to the authenticated login",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<AddAddressInputGraphType>>
                    {
                        Name = "addressInput",
                        Description = "The address input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    AddressInputGraph command = context.GetArgument<AddressInputGraph>("addressInput");

                    Result<CreatedStatus> result = await entityService.AddAddressAsync(tenantId, userId, type,
                        new AddAddressCommand
                        {
                            Type = command.Type,
                            Fields = command.Fields.ToDictionary(k => k.Type, v => v.Value),
                            AddedById = context.GetLoginIdFromToken()
                        });

                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>(
                "updateAddress",
                description: "Adds an address to a entity",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<UpdateAddressInputGraphType>>
                    {
                        Name = "updateAddressInput",
                        Description = "The update address input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, GetPermissionRequestForEntity(type, entityId));

                    UpdateAddressInputGraph command =
                        context.GetArgument<UpdateAddressInputGraph>("updateAddressInput");

                    Result result = await entityService.UpdateAddressAsync(tenantId, entityId, type,
                        new UpdateAddressCommand
                        {
                            Id = command.Id,
                            Type = command.Type,
                            Fields = command.Fields.ToDictionary(k => k.Type, v => v.Value),
                            ModifiedById = context.GetLoginIdFromToken()
                        });

                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>(
                "updateMyAddress",
                description: "Adds an address to an authenticated login",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<UpdateAddressInputGraphType>>
                    {
                        Name = "updateAddressInput",
                        Description = "The update address input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    UpdateAddressInputGraph command =
                        context.GetArgument<UpdateAddressInputGraph>("updateAddressInput");

                    Result result = await entityService.UpdateAddressAsync(tenantId, userId, type,
                        new UpdateAddressCommand
                        {
                            Id = command.Id,
                            Type = command.Type,
                            Fields = command.Fields.ToDictionary(k => k.Type, v => v.Value),
                            ModifiedById = context.GetLoginIdFromToken()
                        });

                    return result;
                }).AuthorizeWith("any");

            FieldAsync<CreatedStatusResultGraphType>(
                "addContact",
                description: "Adds a contact detail to a entity",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<AddContactInputGraphType>>
                    {
                        Name = "contactInput",
                        Description = "The contact input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, GetPermissionRequestForEntity(type, entityId));

                    AddContactCommand command = context.GetArgument<AddContactCommand>("contactInput");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result<CreatedStatus> result =
                        await entityService.AddContactAsync(tenantId, entityId, type, command);
                    return result;
                }).AuthorizeWith("any");

            FieldAsync<CreatedStatusResultGraphType>(
                "addMyContact",
                description: "Adds a contact detail to an authenticated login",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<AddContactInputGraphType>>
                    {
                        Name = "contactInput",
                        Description = "The contact input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    AddContactCommand command = context.GetArgument<AddContactCommand>("contactInput");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result<CreatedStatus> result = await entityService.AddContactAsync(tenantId, userId, type, command);
                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>(
                "updateContact",
                description: "Updates a contact detail to a entity",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<UpdateContactInputGraphType>>
                    {
                        Name = "updateContactInput",
                        Description = "The update contact input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, GetPermissionRequestForEntity(type, entityId));

                    UpdateContactCommand command = context.GetArgument<UpdateContactCommand>("updateContactInput");
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await entityService.UpdateContactAsync(tenantId, entityId, type, command);
                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>(
                "updateMyContact",
                description: "Updates a contact detail to an authenticated contact",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<UpdateContactInputGraphType>>
                    {
                        Name = "updateContactInput",
                        Description = "The update contact input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    UpdateContactCommand command = context.GetArgument<UpdateContactCommand>("updateContactInput");
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await entityService.UpdateContactAsync(tenantId, userId, type, command);
                    return result;
                }).AuthorizeWith("any");


            FieldAsync<CreatedStatusResultGraphType>(
                "addFact",
                description: "Adds a fact to a entity",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<AddFactInputGraphType>>
                    {
                        Name = "factInput",
                        Description = "The fact input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result<CreatedStatus>
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result<CreatedStatus>
                        {
                            Status = "failure",
                            Errors = new List<string> { "Invalid Entity Type" }
                        };

                    await permissionValidator.Authorize(context, GetPermissionRequestForEntity(type, entityId));

                    AddFactInputGraph input = context.GetArgument<AddFactInputGraph>("factInput");
                    input.Value = input.Value ?? input.Values; //ToDo: remove after migration

                    var command = new AddFactCommand
                    {
                        AddedById = context.GetLoginIdFromToken(),
                        Type = input.Type,
                        Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null
                    };

                    Result<CreatedStatus> result = await entityService.AddFactAsync(tenantId, entityId, type, command);
                    return result;
                }).AuthorizeWith("any");

            FieldAsync<CreatedStatusResultGraphType>(
                "addMyFact",
                description: "Adds a fact to an authenticated user",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<AddFactInputGraphType>>
                    {
                        Name = "factInput",
                        Description = "The fact input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    AddFactInputGraph input = context.GetArgument<AddFactInputGraph>("factInput");
                    input.Value = input.Value ?? input.Values; //ToDo: remove after migration

                    var command = new AddFactCommand
                    {
                        AddedById = context.GetLoginIdFromToken(),
                        Type = input.Type,
                        Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null
                    };

                    Result<CreatedStatus> result = await entityService.AddFactAsync(tenantId, userId, type, command);
                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>(
                "updateFact",
                description: "Updates a fact of a entity",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<UpdateFactInputGraphType>>
                    {
                        Name = "updateFactInput",
                        Description = "The update fact input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, GetPermissionRequestForEntity(type, entityId));

                    UpdateFactInputGraph input = context.GetArgument<UpdateFactInputGraph>("updateFactInput");
                    input.Value = input.Value ?? input.Values; //ToDo: remove after migration

                    var command = new UpdateFactCommand
                    {
                        Id = input.Id,
                        ModifiedById = context.GetLoginIdFromToken(),
                        Type = input.Type,
                        Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null
                    };

                    Result result = await entityService.UpdateFactAsync(tenantId, entityId, type, command);
                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>(
                "updateMyFact",
                description: "Updates a fact of an authenticated login",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<UpdateFactInputGraphType>>
                    {
                        Name = "updateFactInput",
                        Description = "The update fact input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    UpdateFactInputGraph input = context.GetArgument<UpdateFactInputGraph>("updateFactInput");
                    input.Value = input.Value ?? input.Values; //ToDo: remove after migration

                    var command = new UpdateFactCommand
                    {
                        Id = input.Id,
                        ModifiedById = context.GetLoginIdFromToken(),
                        Type = input.Type,
                        Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null
                    };

                    Result result = await entityService.UpdateFactAsync(tenantId, userId, type, command);
                    return result;
                }).AuthorizeWith("any");

            Field<ResultGraphType>()
                .Name("entityFactBatch")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the entity identifier")
                .Argument<NonNullGraphType<FactCommandBatchInputGraphType>>("input", "the fact input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, GetPermissionRequestForEntity(type, entityId));
                    FactCommandBatchInputGraph input = context.GetArgument<FactCommandBatchInputGraph>("input");

                    var addFactCommands = input.AddFactInputs?.Select(i => i.ToCommand(loginId))?.ToList();

                    var updateFactCommands = input.UpdateFactInputs?.Select(i => i.ToCommand(loginId))?.ToList();

                    var factCommandBatch = new FactCommandBatch
                    {
                        AddFactCommands = addFactCommands,
                        UpdateFactCommands = updateFactCommands
                    };

                    return await entityService.FactBatchAsync(tenantId, type, entityId, factCommandBatch);
                });


            FieldAsync<ResultGraphType>(
                "addEntityNote",
                description: "Adds a note to a entity",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<AddNoteInputGraphType>>
                    {
                        Name = "noteInput",
                        Description = "The note input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result<CreatedStatus>
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result<CreatedStatus>
                        {
                            Status = "failure",
                            Errors = new List<string> { "Invalid Entity Type" }
                        };

                    await permissionValidator.Authorize(context, string.Format("write{0}Notes", type));

                    AddNoteCommand command = context.GetArgument<AddNoteCommand>("noteInput");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result result = await entityService.AddNoteAsync(tenantId, entityId, type, command);
                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>(
                "updateEntityNote",
                description: "Updates a note of a entity",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<UpdateNoteInputGraphType>>
                    {
                        Name = "updateNoteInput",
                        Description = "The update note input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, string.Format("write{0}Notes", type));

                    UpdateNoteCommand command = context.GetArgument<UpdateNoteCommand>("updateNoteInput");
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await entityService.UpdateNoteAsync(tenantId, entityId, type, command);
                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>(
                "addPolicyNote",
                description: "Adds a note to a policy",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "policyId",
                        Description = "The policy identifier"
                    },
                    new QueryArgument<NonNullGraphType<AddNoteInputGraphType>>
                    {
                        Name = "noteInput",
                        Description = "The note input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    await permissionValidator.Authorize(context, "writePolicyNotes");
                    AddNoteCommand command = context.GetArgument<AddNoteCommand>("noteInput");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result result = await policyService.AddNoteAsync(tenantId, policyId, command);
                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>(
                "updatePolicyNote",
                description: "Updates a note of a policy",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "policyId",
                        Description = "The user identifier"
                    },
                    new QueryArgument<NonNullGraphType<UpdateNoteInputGraphType>>
                    {
                        Name = "updateNoteInput",
                        Description = "The update note input"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    await permissionValidator.Authorize(context, "writePolicyNotes");
                    UpdateNoteCommand command = context.GetArgument<UpdateNoteCommand>("updateNoteInput");
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await policyService.UpdateNoteAsync(tenantId, policyId, command);
                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>(
                "deleteIdentity",
                description: "Deletes an identity of the authenticated login",
                deprecationReason: "Use 'remove' call to align with proper naming convention",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "id",
                        Description = "The identity identifier"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string removedById = context.GetLoginIdFromToken();

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, string.Format("write{0}Identities", type));

                    string id = context.GetArgument<string>("id");

                    Result result = await entityService.RemoveIdentityAsync(tenantId, entityId, type, id, removedById);

                    return result;
                }).AuthorizeWith("any");

            Field<ResultGraphType>()
                .Name("removeIdentity")
                .Description("Removes an identity of the authenticated login")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "The entity identifier")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The identity identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string removedById = context.GetLoginIdFromToken();

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, string.Format("write{0}Identities", type));

                    string id = context.GetArgument<string>("id");

                    Result result = await entityService.RemoveIdentityAsync(tenantId, entityId, type, id, removedById);

                    return result;
                });

            FieldAsync<ResultGraphType>(
                "deleteMyIdentity",
                description: "Deletes an identity of the authenticated login",
                deprecationReason: "Use 'remove' call to align with proper naming convention",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "id",
                        Description = "The identity identifier"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    string id = context.GetArgument<string>("id");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await entityService.RemoveIdentityAsync(tenantId, userId, type, id, removedById);

                    return result;
                }).AuthorizeWith("any");

            Field<ResultGraphType>()
                .Name("removeMyIdentity")
                .Description("Removes an identity of the authenticated login")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The identity identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    string id = context.GetArgument<string>("id");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await entityService.RemoveIdentityAsync(tenantId, userId, type, id, removedById);

                    return result;
                });

            FieldAsync<ResultGraphType>(
                "deleteAddress",
                description: "Deletes an address of a entity",
                deprecationReason: "Use 'remove' call to align with proper naming convention",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "id",
                        Description = "The address identifier"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string removedById = context.GetLoginIdFromToken();

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, string.Format("write{0}Addresses", type));

                    string id = context.GetArgument<string>("id");

                    Result result = await entityService.RemoveAddressAsync(tenantId, entityId, type, id, removedById);

                    return result;
                }).AuthorizeWith("any");

            Field<ResultGraphType>()
                .Name("removeAddress")
                .Description("Removes an address of a entity")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "The entity identifier")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The address identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string removedById = context.GetLoginIdFromToken();

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, string.Format("write{0}Addresses", type));

                    string id = context.GetArgument<string>("id");

                    Result result = await entityService.RemoveAddressAsync(tenantId, entityId, type, id, removedById);

                    return result;
                });

            FieldAsync<ResultGraphType>(
                "deleteMyAddress",
                description: "Deletes an address of the authenticated entity",
                deprecationReason: "Use 'remove' call to align with proper naming convention",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "id",
                        Description = "The address identifier"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    string id = context.GetArgument<string>("id");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await entityService.RemoveAddressAsync(tenantId, userId, type, id, removedById);

                    return result;
                }).AuthorizeWith("any");

            Field<ResultGraphType>()
                .Name("removeMyAddress")
                .Description("Removes an address of the authenticated entity")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The address identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    string id = context.GetArgument<string>("id");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await entityService.RemoveAddressAsync(tenantId, userId, type, id, removedById);

                    return result;
                });

            FieldAsync<ResultGraphType>(
                "deleteContact",
                description: "Deletes a contact of an entity",
                deprecationReason: "Use 'remove' call to align with proper naming convention",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "id",
                        Description = "The contact identifier"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string removedById = context.GetLoginIdFromToken();

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, string.Format("write{0}Contacts", type));

                    string id = context.GetArgument<string>("id");

                    Result result = await entityService.RemoveContactAsync(tenantId, entityId, type, id, removedById);

                    return result;
                }).AuthorizeWith("any");

            Field<ResultGraphType>()
                .Name("removeContact")
                .Description("Removes a contact of an entity")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "The entity identifier")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The contact identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string removedById = context.GetLoginIdFromToken();

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, string.Format("write{0}Contacts", type));

                    string id = context.GetArgument<string>("id");

                    Result result = await entityService.RemoveContactAsync(tenantId, entityId, type, id, removedById);

                    return result;
                });

            FieldAsync<ResultGraphType>(
                "deleteMyContact",
                description: "Deletes a contact of the authenticated login",
                deprecationReason: "Use 'remove' call to align with proper naming convention",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "id",
                        Description = "The contact identifier"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    string removedById = context.GetLoginIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    string id = context.GetArgument<string>("id");

                    Result result = await entityService.RemoveContactAsync(tenantId, userId, type, id, removedById);

                    return result;
                }).AuthorizeWith("any");

            Field<ResultGraphType>()
                .Name("removeMyContact")
                .Description("Removes a contact of the authenticated login")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The contact identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    string removedById = context.GetLoginIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    string id = context.GetArgument<string>("id");

                    Result result = await entityService.RemoveContactAsync(tenantId, userId, type, id, removedById);

                    return result;
                });

            FieldAsync<ResultGraphType>(
                "deleteFact",
                description: "Deletes a fact of an entity",
                deprecationReason: "Use 'remove' call to align with proper naming convention",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "id",
                        Description = "The contact identifier"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string removedById = context.GetLoginIdFromToken();

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, string.Format("write{0}Facts", type));

                    string id = context.GetArgument<string>("id");

                    Result result = await entityService.RemoveFactAsync(tenantId, entityId, type, id, removedById);

                    return result;
                }).AuthorizeWith("any");

            Field<ResultGraphType>()
                .Name("removeFact")
                .Description("removes a fact of an entity")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "The entity identifier")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The contact identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string removedById = context.GetLoginIdFromToken();

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, string.Format("write{0}Facts", type));

                    string id = context.GetArgument<string>("id");

                    Result result = await entityService.RemoveFactAsync(tenantId, entityId, type, id, removedById);

                    return result;
                });

            FieldAsync<ResultGraphType>(
                "deleteMyFact",
                description: "Deletes a fact of an authenticated login",
                deprecationReason: "Use 'remove' call to align with proper naming convention",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "id",
                        Description = "The contact identifier"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    string id = context.GetArgument<string>("id");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await entityService.RemoveFactAsync(tenantId, userId, type, id, removedById);

                    return result;
                }).AuthorizeWith("any");

            Field<ResultGraphType>()
                .Name("RemoveMyFact")
                .Description("Remove a fact of an authenticated login")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The contact identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string userId = context.GetEntityIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    string id = context.GetArgument<string>("id");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await entityService.RemoveFactAsync(tenantId, userId, type, id, removedById);

                    return result;
                });

            FieldAsync<ResultGraphType>(
                "removeEntityAttachment",
                description: "removes an attachment.",
                arguments: new QueryArguments(
                    new QueryArgument<StringGraphType> { Name = "bucketName", Description = "The bucket identifier" },
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "fileName",
                        Description = "The attachment identifier"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string removedById = context.GetLoginIdFromToken();

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, string.Format("write{0}Attachments", type));

                    string fileName = context.GetArgument<string>("fileName");

                    string bucketName = context.GetArgument<string>("bucketName");

                    await fileSystemService.DeleteFileAsync(tenantId, bucketName,
                        new DeleteFileCommand { Key = $"entities/{type}/{entityId}/{fileName}" });

                    Result result = await entityService.RemoveAttachmentAsync(tenantId, entityId, type,
                        new RemoveAttachmentCommand
                        {
                            Path = $"entities/{type}/{entityId}/{fileName}",
                            RemovedById = removedById
                        });

                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>(
                "removeMyAttachment",
                description: "removes an attachment.",
                arguments: new QueryArguments(
                    new QueryArgument<StringGraphType> { Name = "bucketName", Description = "The bucket identifier" },
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "fileName",
                        Description = "The attachment identifier"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetEntityIdFromToken();
                    string removedById = context.GetLoginIdFromToken();
                    EntityTypes type = context.GetEntityTypeFromToken();
                    string fileName = context.GetArgument<string>("fileName");

                    string bucketName = context.GetArgument<string>("bucketName");

                    await fileSystemService.DeleteFileAsync(tenantId, bucketName,
                        new DeleteFileCommand { Key = $"entities/{type}/{entityId}/{fileName}" });

                    Result result = await entityService.RemoveAttachmentAsync(tenantId, entityId, type,
                        new RemoveAttachmentCommand
                        {
                            Path = $"entities/{type}/{entityId}/{fileName}",
                            RemovedById = removedById
                        });

                    return result;
                }).AuthorizeWith("any");

            Field<ResultGraphType>()
                .Name("renameEntityAttachment")
                .Description("Renames an entity attachment")
                .DeprecationReason("use moveFile")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "The entity identifier")
                .Argument<NonNullGraphType<StringGraphType>>("fileName", "The attachment identifier")
                .Argument<NonNullGraphType<StringGraphType>>("newFileName", "The new filename to rename to")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string renamedById = context.GetLoginIdFromToken();

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, string.Format("write{0}Attachments", type));

                    string fileName = context.GetArgument<string>("fileName");
                    string newFileName = context.GetArgument<string>("newFileName");
                    if (fileName == newFileName)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string>
                            {
                                "The new file name has to be different from the current file name"
                            }
                        };


                    Result<byte[]> result = await fileSystemService.GetFileAsync(tenantId, null,
                        new GetFileCommand { Key = $"entities/{type}/{entityId}/{fileName}", IsPublic = false });
                    if (result.Status == "failure")
                        return new Result { Status = result.Status, Errors = result.Errors?.ToList() };

                    await fileSystemService.UploadFileAsync(tenantId, null,
                        new UploadFileCommand
                        {
                            Content = result.Value,
                            Key = $"entities/{type}/{entityId}/{newFileName}",
                            IsPublic = false
                        });

                    Result addResult = await entityService.AddAttachmentAsync(tenantId, entityId, type,
                        new AddAttachmentCommand
                        {
                            Path = $"entities/{type}/{entityId}/{newFileName}",
                            AddedById = renamedById
                        });

                    await fileSystemService.DeleteFileAsync(tenantId, null,
                        new DeleteFileCommand { Key = $"entities/{type}/{entityId}/{fileName}" });
                    Result deleteResult = await entityService.RemoveAttachmentAsync(tenantId, entityId, type,
                        new RemoveAttachmentCommand
                        {
                            Path = $"entities/{type}/{entityId}/{fileName}",
                            RemovedById = renamedById
                        });

                    return addResult;
                });

            Field<ResultGraphType>()
                .Name("renamePolicyAttachment")
                .Description("Renames an policy attachment")
                .DeprecationReason("use moveFile")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy identifier")
                .Argument<NonNullGraphType<StringGraphType>>("fileName", "The attachment identifier")
                .Argument<NonNullGraphType<StringGraphType>>("newFileName", "The new filename to rename to")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string renamedById = context.GetLoginIdFromToken();


                    string fileName = context.GetArgument<string>("fileName");
                    string newFileName = context.GetArgument<string>("newFileName");
                    if (fileName == newFileName)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string>
                            {
                                "The new file name has to be different from the current file name"
                            }
                        };

                    Result<byte[]> result = await fileSystemService.GetFileAsync(tenantId, null,
                        new GetFileCommand { Key = $"policies/{policyId}/{fileName}", IsPublic = false });
                    if (result.Status == "failure")
                        return new Result { Status = result.Status, Errors = result.Errors?.ToList() };

                    await fileSystemService.UploadFileAsync(tenantId, null,
                        new UploadFileCommand
                        {
                            Key = $"policies/{policyId}/{newFileName}",
                            Content = result.Value,
                            IsPublic = false
                        });

                    Result addResult = await policyService.AddAttachmentAsync(tenantId, policyId,
                        new AddAttachmentCommand
                        {
                            Path = $"policies/{policyId}/{newFileName}",
                            AddedById = renamedById
                        });

                    await fileSystemService.DeleteFileAsync(tenantId, null,
                        new DeleteFileCommand { Key = $"policies/{policyId}/{fileName}" });
                    Result deleteResult = await policyService.RemoveAttachmentAsync(tenantId, policyId,
                        new RemoveAttachmentCommand
                        {
                            Path = $"policies/{policyId}/{fileName}",
                            RemovedById = renamedById
                        });

                    return addResult;
                });

            Field<ResultGraphType>()
                .Name("addPolicyAttachment")
                .Description("Adds an attachment to a policy")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy identifier")
                .Argument<NonNullGraphType<AttachmentInputGraphType>>("input", "The attachment input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    await permissionValidator.Authorize(context, new PermissionRequest("createPolicyAttachments", "writePolicyAttachments"));
                    AddAttachmentCommand command = context.GetArgument<AddAttachmentCommand>("input");
                    command.AddedById = context.GetLoginIdFromToken();

                    return await policyService.AddAttachmentAsync(tenantId, policyId, command);
                });

            FieldAsync<ResultGraphType>(
                "removePolicyAttachment",
                description: "removes an attachment.",
                arguments: new QueryArguments(
                    new QueryArgument<StringGraphType> { Name = "bucketName", Description = "The bucket identifier" },
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "policyId",
                        Description = "The policy identifier"
                    },
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "fileName",
                        Description = "The attachment identifier"
                    }),
                resolve: async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("deletePolicyAttachments", "writePolicyAttachments"));
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string fileName = context.GetArgument<string>("fileName");
                    string bucketName = context.GetArgument<string>("bucketName");
                    string removedById = context.GetLoginIdFromToken();

                    string fullPath = $"policies/{policyId}/{fileName}";
                    await fileSystemService.DeleteFileAsync(tenantId, bucketName,
                        new DeleteFileCommand { Key = fullPath });

                    return await policyService.RemoveAttachmentAsync(tenantId, policyId,
                        new RemoveAttachmentCommand { Path = fullPath, RemovedById = removedById });
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>(
                "removeEntityNote",
                description: "removes a note.",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "entityId",
                        Description = "The entity identifier"
                    },
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "id",
                        Description = "The note identifier"
                    }),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string removedById = context.GetLoginIdFromToken();

                    EntityId entity =
                        (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }))
                        ?.FirstOrDefault();
                    if (entity == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"Entity '{entityId}' not found." }
                        };
                    if (!Enum.TryParse(entity?.Type, true, out EntityTypes type))
                        return new Result { Status = "failure", Errors = new List<string> { "Invalid Entity Type" } };

                    await permissionValidator.Authorize(context, string.Format("write{0}Notes", type));

                    string id = context.GetArgument<string>("id");

                    Result result = await entityService.RemoveNoteAsync(tenantId, entityId, type, id, removedById);

                    return result;
                }).AuthorizeWith("any");

            FieldAsync<ResultGraphType>(
                "removePolicyNote",
                description: "removes a note.",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "policyId",
                        Description = "The policy identifier"
                    },
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = "id",
                        Description = "The note identifier"
                    }),
                resolve: async context =>
                {
                    await permissionValidator.Authorize(context, "writePolicyNotes");
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string id = context.GetArgument<string>("id");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await policyService.RemoveNoteAsync(tenantId, policyId,
                        new RemoveCommand { Id = id, RemovedById = removedById });

                    return result;
                }).AuthorizeWith("any");

            Field<CreatedStatusResultGraphType>()
                 .Name("createDisability")
                 .Description("Creates a disability")
                 .AuthorizeWith("any")
                 .Argument<NonNullGraphType<DisabilityInputGraph>>("input", "The input")
                 .ResolveAsync(async context =>
                 {
                     string tenantId = context.GetTenantIdFromToken();
                     string loginId = context.GetLoginIdFromToken();
                     await permissionValidator.Authorize(context, new PermissionRequest("createDisabilities", "writeDisabilities"));

                     CreateDisabilityCommand command = context.GetArgument<CreateDisabilityCommand>("input");
                     command.CreatedById = loginId;

                     return await disabilityService.CreateAsync(tenantId, command);
                 });

            Field<ResultGraphType>()
                .Name("updateDisability")
                .Description("Updates a disability")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("disabilityId", "The disability identifier")
                .Argument<NonNullGraphType<DisabilityInputGraph>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string disabilityId = context.GetArgument<string>("disabilityId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateDisabilities", "writeDisabilities").WithTargetIds(disabilityId));

                    UpdateDisabilityCommand command = context.GetArgument<UpdateDisabilityCommand>("input");
                    command.Id = disabilityId;
                    command.ModifiedById = loginId;

                    return await disabilityService.UpdateAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("deleteDisability")
                .Description("Deletes a disability")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("disabilityId", "The disability identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string disabilityId = context.GetArgument<string>("disabilityId");
                    await permissionValidator.Authorize(context, new PermissionRequest("deleteDisabilities", "writeDisabilities").WithTargetIds(disabilityId));

                    var command = new RemoveCommand { RemovedById = loginId, Id = disabilityId };

                    return await disabilityService.DeleteAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("disabilityBatch")
                .Description("disability Batch")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<DisabilityBatchInputGraph>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    await permissionValidator.Authorize(context, new PermissionRequest("updateDisabilities", "deleteDisabilities", "writeDisabilities"));

                    DisabilityBatchCommand command = context.GetArgument<DisabilityBatchCommand>("input");
                    command.Create?.ForEach(c => c.CreatedById = loginId);

                    if (command.Update != null)
                    {
                        foreach (var c in command.Update)
                        {
                            c.ModifiedById = loginId;
                            await permissionValidator.Authorize(context, new PermissionRequest("updateDisabilities", "writeDisabilities").WithTargetIds(c.Id));
                        }
                    }

                    if (command.Delete != null)
                    {
                        foreach (var c in command.Delete)
                        {
                            c.RemovedById = loginId;
                            await permissionValidator.Authorize(context, new PermissionRequest("deleteDisabilities", "writeDisabilities").WithTargetIds(c.Id));
                        }
                    }

                    return await disabilityService.BatchAsync(tenantId, command);
                });

            Field<CreatedStatusResultGraphType>()
                 .Name("createDiagnosis")
                 .Description("Creates a diagnosis")
                 .AuthorizeWith("any")
                 .Argument<NonNullGraphType<DiagnosisInputGraph>>("input", "The input")
                 .ResolveAsync(async context =>
                 {
                     string tenantId = context.GetTenantIdFromToken();
                     string loginId = context.GetLoginIdFromToken();
                     await permissionValidator.Authorize(context, "writeDiagnoses");

                     CreateDiagnosisCommand command = context.GetArgument<CreateDiagnosisCommand>("input");
                     command.CreatedById = loginId;

                     return await diagnosisService.CreateAsync(tenantId, command);
                 });

            Field<ResultGraphType>()
                .Name("updateDiagnosis")
                .Description("Updates a diagnosis")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("diagnosisId", "The diagnosis identifier")
                .Argument<NonNullGraphType<DiagnosisInputGraph>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string diagnosisId = context.GetArgument<string>("diagnosisId");
                    await permissionValidator.Authorize(context, "writeDiagnoses", diagnosisId);

                    UpdateDiagnosisCommand command = context.GetArgument<UpdateDiagnosisCommand>("input");
                    command.Id = diagnosisId;
                    command.ModifiedById = loginId;

                    return await diagnosisService.UpdateAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("deleteDiagnosis")
                .Description("Deletes a diagnosis")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("diagnosisId", "The diagnosis identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string diagnosisId = context.GetArgument<string>("diagnosisId");
                    await permissionValidator.Authorize(context, "writeDiagnoses", diagnosisId);

                    RemoveCommand command = new()
                    {
                        RemovedById = loginId,
                        Id = diagnosisId
                    };

                    return await diagnosisService.DeleteAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("diagnosisBatch")
                .Description("diagnosis Batch")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<DiagnosisBatchInputGraph>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    await permissionValidator.Authorize(context, "writeDiagnoses");

                    DiagnosisBatchCommand command = context.GetArgument<DiagnosisBatchCommand>("input");
                    command.Create?.ForEach(c => c.CreatedById = loginId);

                    if (command.Update != null)
                    {
                        foreach (var c in command.Update)
                        {
                            c.ModifiedById = loginId;
                            await permissionValidator.Authorize(context, "writeDiagnoses", c.Id);
                        }
                    }

                    if (command.Delete != null)
                    {
                        foreach (var c in command.Delete)
                        {
                            c.RemovedById = loginId;
                            await permissionValidator.Authorize(context, "writeDiagnoses", c.Id);
                        }
                    }

                    return await diagnosisService.BatchAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("addDiagnosisToDisability")
                .Description("Add a diagnosis into a disability")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("diagnosisId", "The diagnosis identifier")
                .Argument<NonNullGraphType<StringGraphType>>("disabilityId", "The disability identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string diagnosisId = context.GetArgument<string>("diagnosisId");
                    string disabilityId = context.GetArgument<string>("disabilityId");

                    await permissionValidator.Authorize(context, new PermissionRequest("updateDisabilities", "writeDisabilities").WithTargetIds(disabilityId));

                    return await disabilityService.AddDiagnosisAsync(tenantId, disabilityId, diagnosisId);
                });

            Field<ResultGraphType>()
                .Name("removeDiagnosisFromDisability")
                .Description("Remove a diagnosis from a disability")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("diagnosisId", "The diagnosis identifier")
                .Argument<NonNullGraphType<StringGraphType>>("disabilityId", "The disability identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string diagnosisId = context.GetArgument<string>("diagnosisId");
                    string disabilityId = context.GetArgument<string>("disabilityId");

                    await permissionValidator.Authorize(context, new PermissionRequest("updateDisabilities", "writeDisabilities").WithTargetIds(disabilityId));

                    return await disabilityService.RemoveDiagnosisAsync(tenantId, disabilityId, diagnosisId);
                });

            Field<ResultGraphType>()
                .Name("addDisabilityToIndividual")
                .Description("Add a disability into an individual")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("disabilityId", "The disability identifier")
                .Argument<NonNullGraphType<StringGraphType>>("individualId", "The individual identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string individualId = context.GetArgument<string>("individualId");
                    string disabilityId = context.GetArgument<string>("disabilityId");

                    await permissionValidator.Authorize(context, new PermissionRequest("createIndividualDisabilities", "writeIndividualDisabilities"));

                    return await individualService.AddDisabilityAsync(tenantId, individualId, new AddCommand { Id = disabilityId, AddedById = loginId });
                });

            Field<ResultGraphType>()
                .Name("removeDisabilityFromIndividual")
                .Description("Remove a disability from an individual")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("disabilityId", "The disability identifier")
                .Argument<NonNullGraphType<StringGraphType>>("individualId", "The individual identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string individualId = context.GetArgument<string>("individualId");
                    string disabilityId = context.GetArgument<string>("disabilityId");

                    await permissionValidator.Authorize(context, new PermissionRequest("updateIndividualDisabilities", "writeIndividualDisabilities"));

                    return await individualService.RemoveDisabilityAsync(tenantId, individualId, new RemoveCommand { Id = disabilityId, RemovedById = loginId });
                });

            Field<ResultGraphType>()
                .Name("individualDisabilityBatch")
                .Description("individual's disability batch")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("individualId", "The individual identifier")
                .Argument<NonNullGraphType<IndividualDisabilityBatchInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string individualId = context.GetArgument<string>("individualId");

                    IndividualDisabilityBatchInput input = context.GetArgument<IndividualDisabilityBatchInput>("input");

                    if (!await permissionValidator.VerifyHasPermission(context, new PermissionRequest("writeIndividualDisabilities")))
                    {
                        if (input?.DisabilityIdsToAdd?.Any() == true)
                            await permissionValidator.Authorize(context, "createIndividualDisabilities");
                        if (input?.DisabilityIdsToRemove?.Any() == true)
                            await permissionValidator.Authorize(context, "updateIndividualDisabilities");
                    }

                    var command = new BatchCommand()
                    {
                        AddCommands = input?.DisabilityIdsToAdd?.Select(id => new AddCommand
                        {
                            Id = id,
                            AddedById = loginId
                        }).ToList() ?? new List<AddCommand>(),
                        RemoveCommands = input?.DisabilityIdsToRemove?.Select(id => new RemoveCommand
                        {
                            Id = id,
                            RemovedById = loginId
                        }).ToList() ?? new List<RemoveCommand>()
                    };

                    return await individualService.DisabilityBatchAsync(tenantId, individualId, command);
                });

            Field<StringResultGraphType>()
                .Name("createKycApplicant")
                .AuthorizeWith("any")
                .Description("create applicant for an Individual in eKYC provider")
                .Argument<NonNullGraphType<StringGraphType>>("individualId", "The individual identifier")
                .Argument<NonNullGraphType<CreateKycApplicantInputGraphType>>("input", "Input for creating an KYC applicant")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string individualId = context.GetArgument<string>("individualId");
                    CreateKycApplicantCommand command = context.GetArgument<CreateKycApplicantCommand>("input");

                    await permissionValidator.Authorize(context, new PermissionRequest("updateIndividuals", "writeIndividuals").WithTargetIds(individualId));

                    return await individualService.CreateKycApplicantAsync(tenantId, individualId, command);
                });

            Field<StringResultGraphType>()
                .Name("generateKycAuthToken")
                .AuthorizeWith("any")
                .Description("generate an auth token for client apps to perform kyc processes (eg. upload images) in eKYC provider")
                .Argument<NonNullGraphType<StringGraphType>>("individualId", "The individual identifier")
                .Argument<NonNullGraphType<GenerateKycAuthTokenInputGraphType>>("input", "Input for getting an auth token from a eKYC provider")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string individualId = context.GetArgument<string>("individualId");
                    GenerateKycAuthTokenCommand command = context.GetArgument<GenerateKycAuthTokenCommand>("input");

                    await permissionValidator.Authorize(context, new PermissionRequest("updateIndividuals", "writeIndividuals").WithTargetIds(individualId));

                    return await individualService.GenerateKycAuthTokenAsync(tenantId, individualId, command);
                });
        }

        private static async Task<Result<CreatedStatus>> ProcessInviteEntityAsync(
            ResolveFieldContext<object> context,
            IAuthService authService,
            IEncryptionAlgorithm encryptionAlgorithm,
            IEntityService entityService,
            PermissionValidator permissionValidator,
            string tenantId,
            string clientId,
            string loginId,
            InviteEntityInputGraph input,
            string? permissioningToken,
            IMultiTenantFeatureManager multiTenantFeatureManager)
        {
            // Validate app permissions
            if (input.AppIdsToBeGrantedAccessTo?.Any() ?? false)
            {
                foreach (string appId in input.AppIdsToBeGrantedAccessTo)
                    await permissionValidator.Authorize(context, "inviteEntityToLogin", appId);
            }

            EntityId? entityId = (await entityService.GenericQueryIdsAndTypesAsync(tenantId,
                new EntityWhere { Id = input.EntityId })).FirstOrDefault();
            if (entityId == null)
            {
                return new Result<CreatedStatus>
                {
                    Status = "failure",
                    Errors = new List<string> { $"The entity id '{input.EntityId ?? "null"}' doesn't exist." }
                };
            }

            // Create login command
            var loginCommand = CreateLoginCommand(clientId, loginId, entityId, input);
            var alwayAllowConfirmEmail = await multiTenantFeatureManager.IsEnabled(tenantId, "AlwaysAllowConfirmEmail");
            if ()

            // Handle notification
            var notificationResult = await HandleNotificationAsync(context, tenantId, input);
            if (notificationResult.HasErrors)
            {
                return new Result<CreatedStatus> { Status = "failure", Errors = notificationResult.Errors };
            }
            loginCommand.SendNotificationCommand = notificationResult.NotificationCommand;

            // Create login
            Result<CreatedStatus> loginCreatedResult = await authService.CreateLoginAsync(tenantId, loginCommand);
            if (loginCreatedResult.Status == "failure")
                return loginCreatedResult;

            // Add permissions
            await AddPermissionsAsync(authService, tenantId, loginId, clientId, loginCreatedResult.Value.Id, input);

            // Handle permissioning token
            if (permissioningToken != null)
            {
                await HandlePermissioningTokenAsync(authService, encryptionAlgorithm, tenantId, loginId, input.EntityId, permissioningToken);
            }

            return loginCreatedResult;
        }

        private static CreateLoginCommand CreateLoginCommand(string clientId, string loginId, EntityId entityId, InviteEntityInputGraph input)
        {
            return new CreateLoginCommand
            {
                ClientId = clientId,
                Email = input.Email,
                Username = input.Username ?? input.Email,
                Password = input.IsSsoUser ? Guid.NewGuid().ToString("N").Substring(0, 12) : input.Password,
                TelephoneNumber = input.TelephoneNumber,
                AppIdsToBeGrantedAccessTo = input.AppIdsToBeGrantedAccessTo,
                CreatedById = loginId,
                EntityId = entityId.Id,
                EntityType = entityId.Type,
                RedirectQueryString = input.RedirectQueryString,
                IsPasswordValidationDobRequire = input.IsSsoUser ? false : input.IsPasswordValidationDobRequire,
                UseDefaultPermissions = input.UseDefaultPermissions,
                IsEmailConfirmed = input.IsSsoUser,
                IgnorePasswordValidation = input.IsSsoUser,
                ActiveFrom = input.ActiveFrom,
            };
        }

        private static Task<NotificationResult> HandleNotificationAsync(ResolveFieldContext<object> context, string tenantId, InviteEntityInputGraph input)
        {
            SendNotificationCommand? sendNotificationCommand = SendNotificationGraph.ToCommand(input.SendNotification);
            if (sendNotificationCommand == null)
                return Task.FromResult(new NotificationResult { NotificationCommand = null });

            if (tenantId == "tcb" || tenantId == "tcb_uat")
                sendNotificationCommand.EmailMessage.Subject = null;

            if (sendNotificationCommand.EmailMessage?.TemplateRendering?.Input != null)
            {
                Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext,
                    sendNotificationCommand.EmailMessage.TemplateRendering.Input,
                    out List<string> renderErrors);
                
                if (renderErrors.Any())
                    return Task.FromResult(new NotificationResult { HasErrors = true, Errors = renderErrors });
            }

            return Task.FromResult(new NotificationResult { NotificationCommand = sendNotificationCommand });
        }

        private static async Task AddPermissionsAsync(
            IAuthService authService,
            string tenantId,
            string loginId,
            string clientId,
            string createdLoginId,
            InviteEntityInputGraph input)
        {
            // Add default permissions
            if (input.UseDefaultPermissions ?? true)
            {
                await authService.AddTargettedPermissionsAsync(tenantId, createdLoginId,
                    new List<AddTargettedPermissionCommand>
                    {
                        new()
                        {
                            AddedById = loginId, Type = "clientId", Value = clientId
                        }
                    });
            }

            // Add permission groups
            if (input.PermissionGroupIds?.Any() ?? false)
            {
                IEnumerable<PermissionGroup> permissionGroups = await authService.GetPermissionGroupsAsync(
                    tenantId,
                    new PermissionGroupWhere { Id_in = input.PermissionGroupIds });

                List<AddTargettedPermissionCommand> commands = permissionGroups.Select(g =>
                    new AddTargettedPermissionCommand
                    {
                        Type = "groups",
                        Value = g.Id,
                        AddedById = loginId
                    }).ToList();

                if (commands.Count > 0)
                    await authService.AddTargettedPermissionsAsync(tenantId, createdLoginId, commands);
            }
        }

        private static async Task HandlePermissioningTokenAsync(
            IAuthService authService,
            IEncryptionAlgorithm encryptionAlgorithm,
            string tenantId,
            string loginId,
            string? entityId,
            string permissioningToken)
        {
            var encryptionParameters = new Dictionary<string, object>
            {
                {"keyString", "E98912CB4BA6FFAB2EC0E24ED92B0D8B"}
            };

            string result = await encryptionAlgorithm.DecryptAsync(tenantId, permissioningToken, encryptionParameters);
            string interpolatedResult = result.Replace("{entityId}", entityId ?? string.Empty);

            PermissioningObject? permissioningObject = JsonConvert.DeserializeObject<PermissioningObject>(interpolatedResult);
            if (permissioningObject != null)
            {
                foreach (AddTargettedPermissionCommand command in permissioningObject.ToRequest)
                    command.AddedById = loginId;
                foreach (AddTargettedPermissionCommand command in permissioningObject.ToDelegate)
                    command.AddedById = loginId;

                await authService.AddTargettedPermissionsAsync(tenantId, loginId, permissioningObject.ToDelegate);
                await authService.AddTargettedPermissionsAsync(tenantId, permissioningObject.DelegatinLoginId, permissioningObject.ToRequest);
            }
        }

        private class NotificationResult
        {
            public bool HasErrors { get; set; }
            public List<string> Errors { get; set; } = new();
            public SendNotificationCommand? NotificationCommand { get; set; }
        }

        private static PermissionRequest GetPermissionRequestForEntity(EntityTypes type, string entityId) => new PermissionRequest(
                $"update{GraphQLToolsExtensions.FormatPluralEntityType(type)}", $"write{GraphQLToolsExtensions.FormatPluralEntityType(type)}").WithTargetIds(entityId);
    }
}