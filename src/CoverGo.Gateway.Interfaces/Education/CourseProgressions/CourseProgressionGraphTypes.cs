using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Education;
using CoverGo.Gateway.Domain.Education.CourseProgressions;
using CoverGo.Gateway.Domain.Education.Courses;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Education.Courses;
using CoverGo.Gateway.Interfaces.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;
using GraphQL.DataLoader;
using GraphQL.Types;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Interfaces.Education.CourseProgressions
{
    public class CourseProgressionsGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<CourseProgressionGraph> List { get; set; }
    }

    public class CourseProgressionsGraphType : ObjectGraphType<CourseProgressionsGraph>
    {
        public CourseProgressionsGraphType(IEducationService educationService, PermissionValidator permissionValidator)
        {
            Name = "courseProgressions";
            Description = "Gets all course progressions";

            Field(c => c.TotalCount)
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();

                   IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readCourseProgressions");
                   CourseProgressionWhere where = allowedIds.Contains("all")
                       ? context.ComputeArgAndVar<CourseProgressionWhere, CourseProgressionsGraph>("where") ?? new CourseProgressionWhere()
                       : new CourseProgressionWhere
                       {
                           And = new List<CourseProgressionWhere>
                           {
                                context.ComputeArgAndVar<CourseProgressionWhere, CourseProgressionsGraph>("where") ?? new CourseProgressionWhere(),
                                new() { Id_in = allowedIds.ToList() }
                           }
                       };

                   return await educationService.GetCourseProgressionsTotalCountAsync(tenantId, where);
               });

            Field(c => c.List, type: typeof(ListGraphType<CourseProgressionGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readCourseProgressions");
                    CourseProgressionWhere where = allowedIds.Contains("all")
                        ? context.ComputeArgAndVar<CourseProgressionWhere, CourseProgressionsGraph>("where") ?? new CourseProgressionWhere()
                        : new CourseProgressionWhere
                        {
                            And = new List<CourseProgressionWhere>
                            {
                                context.ComputeArgAndVar<CourseProgressionWhere, CourseProgressionsGraph>("where") ?? new CourseProgressionWhere(),
                                new() { Id_in = allowedIds.ToList() }
                            }
                        };

                    int? skip = context.ComputeArgAndVar<int?, CourseProgressionsGraph>("skip");
                    int? first = context.ComputeArgAndVar<int?, CourseProgressionsGraph>("limit");
                    SortGraph sort = context.ComputeArgAndVar<SortGraph, CourseProgressionsGraph>("sort");
                    DateTime? asOf = context.ComputeArgAndVar<DateTime?, CourseProgressionsGraph>("asOf");

                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy, AsOf = asOf };
                    IEnumerable<CourseProgression> courseProgressions = await educationService.GetCourseProgressionsAsync(tenantId, queryArguments);

                    return courseProgressions.Select(c => CourseProgressionGraph.ToGraph(c));
                });
        }
    }

    public class CourseProgressionGraphType : ObjectGraphType<CourseProgressionGraph>
    {

        public CourseProgressionGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<CoverGo.Users.Domain.Objects.Object, CreateObjectCommand, UpdateObjectCommand> objectService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            IEducationService educationService,
            PermissionValidator permissionValidator)
        {
            Name = "courseProgression";
            Description = "Course Progression";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.Id);
            Field(c => c.Entity, type: typeof(EntityInterfaceGraphType), nullable: true)
                            .ResolveAsync(async context =>
                            {
                                if (context.Source.Entity?.Id == null)
                                    return null;
                                string tenantId = context.GetTenantIdFromToken();
                                var internalLoader = accessor.Context.GetOrAddBatchLoader<string, Internal>("GetInternals",
                                    i => internalService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));
                                var customerLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                                    i => individualService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));
                                var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>("GetCompanies",
                                    i => companyService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));
                                var objectLoader = accessor.Context.GetOrAddBatchLoader<string, CoverGo.Users.Domain.Objects.Object>("GetObjects",
                                    i => objectService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));
                                var organizationsLoader = accessor.Context.GetOrAddBatchLoader<string, Organization>("GetOrganizations",
                                  i => organizationService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));
                                Task<Internal> internalDtoTask = internalLoader.LoadAsync(context.Source.Entity.Id);
                                Task<Individual> customerDtoTask = customerLoader.LoadAsync(context.Source.Entity.Id);
                                Task<Company> companyDtoTask = companyLoader.LoadAsync(context.Source.Entity.Id);
                                Task<CoverGo.Users.Domain.Objects.Object> objectDtoTask = objectLoader.LoadAsync(context.Source.Entity.Id);
                                Task<Organization> organizationDtoTask = organizationsLoader.LoadAsync(context.Source.Entity.Id);
                                await Task.WhenAll(internalDtoTask, customerDtoTask, companyDtoTask, objectDtoTask, organizationDtoTask);
                                return
                                    internalDtoTask.Result?.ToGraph() ??
                                    customerDtoTask.Result?.ToGraph() ??
                                    companyDtoTask.Result?.ToGraph() ??
                                    objectDtoTask.Result?.ToGraph() ??
                                    organizationDtoTask.Result?.ToGraph();
                            });
            Field(c => c.Course, type: typeof(CourseGraphType)).ResolveAsync(async context =>
            {
                IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,"readCourses");
                var andFilter = new List<CourseWhere> { };
                if (!allowedTargetIds.Contains("all"))
                    andFilter.Add(new CourseWhere { Id_in = allowedTargetIds?.ToList() });
                string tenantId = context.GetTenantIdFromToken();
                var loader = accessor.Context.GetOrAddBatchLoader<string, Course>("GetCourseById",
                                        async i =>
                                        {
                                            andFilter.Add(new CourseWhere { Id_in = i?.ToList() });
                                            IEnumerable<Course> courses = await educationService.GetCoursesAsync(tenantId, new Domain.QueryArguments
                                            {
                                                Where = new CourseWhere { And = andFilter }
                                            });
                                            return courses.ToDictionary(x => x.Id, x => x);
                                        });
                return CourseGraph.ToGraph(await loader.LoadAsync(context.Source.Course.Id));
            });
            Field(c => c.LessonsCompletions, type: typeof(ListGraphType<LessonCompletionGraphType>));
            Field(c => c.Score, nullable: true);
            Field(c => c.IsPassed, nullable: true);
        }
    }

    public class CourseProgressionGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public EntityGraph Entity { get; set; }
        public CourseGraph Course { get; set; }
        public List<LessonCompletionGraph> LessonsCompletions { get; set; }
        public double Score { get; set; }
        public bool IsPassed { get; set; }

        public static CourseProgressionGraph ToGraph(CourseProgression domain) =>
            domain != null
            ? new CourseProgressionGraph
            {
                Id = domain.Id,
                Entity = new EntityGraph { Id = domain.EntityId },
                Course = new CourseGraph { Id = domain.CourseId },
                LessonsCompletions = domain.LessonsCompletions?.Select(lc => new LessonCompletionGraph { Id = lc.Id, CourseId = domain.CourseId, Lesson = new LessonGraph { Id = lc.LessonId }, ItemAnswers = lc.ItemAnswers?.ToList(), Score = lc.Score, IsPassed = lc.IsPassed })?.ToList(),
                Score = domain.Score,
                IsPassed = domain.IsPassed
            }.PopulateSystemGraphFields(domain)
            : null;

    }

    public class LessonCompletionGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string CourseId { get; set; }
        public double Score { get; set; }
        public bool IsPassed { get; set; }
        public LessonGraph Lesson { get; set; }
        public List<LessonItemAnswer> ItemAnswers { get; set; }

        public static LessonCompletionGraph ToGraph(LessonCompletion domain) =>
            domain != null
            ? new LessonCompletionGraph
            {
                Id = domain.Id,
                Score = domain.Score,
                IsPassed = domain.IsPassed,
                Lesson = new LessonGraph { Id = domain.LessonId },
                ItemAnswers = domain.ItemAnswers.ToList()
            }.PopulateSystemGraphFields(domain)
            : null;
    }

    public class LessonCompletionGraphType : ObjectGraphType<LessonCompletionGraph>
    {

        public LessonCompletionGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IEducationService educationService,
            PermissionValidator permissionValidator)
        {
            Name = "LessonCompletion";
            Description = "Lesson completion";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.Id);
            Field(c => c.Lesson, type: typeof(LessonGraphType)).ResolveAsync(async context =>
            {
                if (context.Source.Lesson == null) return null;
                IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,"readCourses");
                var andFilter = new List<CourseWhere> { };
                if (!allowedTargetIds.Contains("all"))
                    andFilter.Add(new CourseWhere { Id_in = allowedTargetIds?.ToList() });
                string tenantId = context.GetTenantIdFromToken();
                var loader = accessor.Context.GetOrAddBatchLoader<string, Course>("GetCourseById",
                                        async i =>
                                        {
                                            andFilter.Add(new CourseWhere { Id_in = i?.ToList() });
                                            IEnumerable<Course> courses = await educationService.GetCoursesAsync(tenantId, new Domain.QueryArguments
                                            {
                                                Where = new CourseWhere { And = andFilter }
                                            });
                                            return courses.ToDictionary(x => x.Id, x => x);
                                        });
                Course course = await loader.LoadAsync(context.Source.CourseId);
                if (course == null) return null;
                Lesson lesson = CourseProgressionExtensions.FindLesson(course.Sections, context.Source.Lesson.Id);
                return LessonGraph.ToGraph(lesson);
            });
            Field(c => c.Score, nullable: true);
            Field(c => c.IsPassed, nullable: true);
            Field(lc => lc.ItemAnswers, type: typeof(ListGraphType<LessonItemAnswerGraphType>));
        }
    }

    public class LessonItemAnswerGraphType : ObjectGraphType<LessonItemAnswer>
    {
        public LessonItemAnswerGraphType()
        {
            Name = "lessonItemAnswer";
            Description = "An answer to an item of a lesson";
            Field(f => f.ItemId);
            Field(f => f.Answer, type: typeof(ListGraphType<StringGraphType>));
        }
    }

    public class CourseProgressionWhereInputGraphType : InputObjectGraphType<CourseProgressionWhere>
    {
        public CourseProgressionWhereInputGraphType()
        {
            Name = "courseProgressionWhereInput";
            Description = "A course progression search filter";
            Field(f => f.Or, type: typeof(ListGraphType<CourseProgressionWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<CourseProgressionWhereInputGraphType>));
            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);
            Field(f => f.EntityId, nullable: true);
            Field(f => f.EntityId_in, nullable: true);
            Field(f => f.CourseId, nullable: true);
            Field(f => f.CourseId_in, nullable: true);
            this.PopulateSystemWhereFields();
        }
    }

    public class CreateCourseProgressionInputGraphType : InputObjectGraphType<CreateCourseProgressionCommand>
    {
        public CreateCourseProgressionInputGraphType()
        {
            Name = "createCourseProgressionInput";
            Description = "Create Course Progression input";
            Field(c => c.EntityId, nullable: true);
            Field(c => c.CourseId, nullable: true);
        }
    }

    public class UpdateCourseProgressionInputGraphType : InputObjectGraphType<UpdateCourseProgressionCommand>
    {
        public UpdateCourseProgressionInputGraphType()
        {
            Name = "updateCourseProgressionInput";
            Description = "update Course Progression input";
            Field(c => c.EntityId, nullable: true);
            Field(c => c.CourseId, nullable: true);
        }
    }

    public class AddLessonCompletionInputGraphType : InputObjectGraphType<AddLessonCompletionToCourseProgressionCommand>
    {
        public AddLessonCompletionInputGraphType()
        {
            Name = "addLessonCompletionInput";
            Description = "Add lesson completion input";
            Field(lc => lc.ItemAnswers, type: typeof(ListGraphType<LessonItemAnswerInputGraphType>));
            Field(lc => lc.LessonId);
        }
    }

    public class UpdateLessonCompletionInputGraphType : InputObjectGraphType<UpdateLessonCompletionOfCourseProgressionCommand>
    {
        public UpdateLessonCompletionInputGraphType()
        {
            Name = "updateLessonCompletionInput";
            Description = "update course progression input";
            Field(lc => lc.ItemAnswers, type: typeof(ListGraphType<LessonItemAnswerInputGraphType>));
            Field(lc => lc.LessonId, nullable: true);
        }
    }

    public class LessonItemAnswerInputGraphType : InputObjectGraphType<LessonItemAnswer>
    {
        public LessonItemAnswerInputGraphType()
        {
            Name = "lessonItemAnswerInput";
            Description = "The input for lesson item answers";
            Field(f => f.ItemId);
            Field(f => f.Answer, type: typeof(ListGraphType<StringGraphType>));
        }
    }

    public static class CourseProgressionExtensions
    {
        public static Lesson FindLesson(IEnumerable<Section> sections, string toFind)
        {
            foreach (Section section in sections)
            {
                Lesson lesson = section?.Lessons.FirstOrDefault(l => l.Id == toFind);
                if (lesson != null) return lesson;
                lesson = FindLesson(section?.Sections, toFind);
                if (lesson != null) return lesson;
            }
            return null;
        }
    }
}
