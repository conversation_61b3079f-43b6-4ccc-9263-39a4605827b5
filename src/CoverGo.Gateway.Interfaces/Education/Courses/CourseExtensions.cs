﻿using CoverGo.Gateway.Domain.Education.Courses;
using CoverGo.Gateway.Domain.L10n;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Interfaces.Education.Courses
{
    public static class CourseExtensions
    {
        public static async Task ReplaceNameContainsWithCourseIdFilterAsync(string tenantId, CourseWhere where, IL10nService l10nService)
        {
            if (where.And != null)
                foreach (CourseWhere wh in where.And)
                    await ReplaceNameContainsWithCourseIdFilterAsync(tenantId, wh, l10nService);

            if (where.Or != null)
                foreach (CourseWhere wh in where.Or)
                    await ReplaceNameContainsWithCourseIdFilterAsync(tenantId, wh, l10nService);

            if (where.Name_contains != null)
            {
                IEnumerable<string> keys = await l10nService.GetKeysFromValueContainsAsync(tenantId, CultureInfo.CurrentCulture.Name, where.Name_contains, "courses-", "-name");
                where.Name_contains = null;
                IEnumerable<string> courseIds = keys.Select(k => k.<PERSON>lace("courses-", "").Replace("-name", "")).Where(pid => pid != null);

                CourseWhere whereDeepCopy = JsonConvert.DeserializeObject<CourseWhere>(JsonConvert.SerializeObject(where));
                where.And = new List<CourseWhere> { whereDeepCopy, new() { Id_in = courseIds?.ToList() } };
            }
        }
    }
}
