using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Cms;
using CoverGo.Gateway.Domain.Education;
using CoverGo.Gateway.Domain.Education.Courses;
using CoverGo.Gateway.Domain.L10n;
using GraphQL.DataLoader;
using GraphQL.Types;
using System;
using System.Collections.Generic;
using System.Linq;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Interfaces.Education.Courses
{
    public class CoursesGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<CourseGraph> List { get; set; }
    }

    public class CoursesGraphType : ObjectGraphType<CoursesGraph>
    {
        public CoursesGraphType(
            IEducationService educationService,
            IL10nService l10nService,
            PermissionValidator permissionValidator)
        {
            Name = "courses";
            Description = "Gets all courses";

            Field(c => c.TotalCount)
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();

                   IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readCourses");
                   CourseWhere where = allowedIds.Contains("all")
                       ? context.ComputeArgAndVar<CourseWhere, CoursesGraph>("where") ?? new CourseWhere()
                       : new CourseWhere
                       {
                           And = new List<CourseWhere>
                           {
                                context.ComputeArgAndVar<CourseWhere, CoursesGraph>("where") ?? new CourseWhere(),
                                new() { Id_in = allowedIds.ToList() }
                           }
                       };

                   await CourseExtensions.ReplaceNameContainsWithCourseIdFilterAsync(tenantId, where, l10nService);

                   return await educationService.GetCoursesTotalCountAsync(tenantId, where);
               });

            Field(c => c.List, type: typeof(ListGraphType<CourseGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readCourses");
                    CourseWhere where = allowedIds.Contains("all")
                        ? context.ComputeArgAndVar<CourseWhere, CoursesGraph>("where") ?? new CourseWhere()
                        : new CourseWhere
                        {
                            And = new List<CourseWhere>
                            {
                                context.ComputeArgAndVar<CourseWhere, CoursesGraph>("where") ?? new CourseWhere(),
                                new() { Id_in = allowedIds.ToList() }
                            }
                        };

                    int? skip = context.ComputeArgAndVar<int?, CoursesGraph>("skip");
                    int? first = context.ComputeArgAndVar<int?, CoursesGraph>("limit");
                    SortGraph sort = context.ComputeArgAndVar<SortGraph, CoursesGraph>("sort");
                    DateTime? asOf = context.ComputeArgAndVar<DateTime?, CoursesGraph>("asOf");

                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    await CourseExtensions.ReplaceNameContainsWithCourseIdFilterAsync(tenantId, where, l10nService);

                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy, AsOf = asOf };
                    IEnumerable<Course> courses = await educationService.GetCoursesAsync(tenantId, queryArguments);

                    return courses.Select(c => CourseGraph.ToGraph(c));
                });
        }
    }

    public class CourseGraphType : ObjectGraphType<CourseGraph>
    {

        public CourseGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IL10nService l10nService,
            PermissionValidator permissionValidator)
        {
            Name = "course";
            Description = "course";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.Id);

            Field(p => p.Name, nullable: true).ResolveAsync(context =>
                context.GetL10nAsync(accessor, l10nService, $"courses-{context.Source.Id}-name"));

            Field(p => p.Description, nullable: true).ResolveAsync(context =>
                context.GetL10nAsync(accessor, l10nService, $"courses-{context.Source.Id}-description"));

            Field(c => c.Version, nullable: true);
            Field(c => c.Categories, nullable: true);
            Field(c => c.Sections, type: typeof(ListGraphType<SectionGraphType>));
        }
    }

    public class CourseGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Version { get; set; }
        public IEnumerable<string> Categories { get; set; }
        public List<SectionGraph> Sections { get; set; }

        public static CourseGraph ToGraph(Course domain) =>
            domain != null
            ? new CourseGraph
            {
                Id = domain.Id,
                Categories = domain.Categories,
                Version = domain.Version,
                Sections = domain.Sections.Select(l => SectionGraph.ToGraph(l)).ToList()
            }.PopulateSystemGraphFields(domain)
            : null;
    }
    public class SectionGraphType : ObjectGraphType<SectionGraph>
    {

        public SectionGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IL10nService l10nService,
            PermissionValidator permissionValidator)
        {
            Name = "section";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.Id);

            Field(p => p.Name, nullable: true).ResolveAsync(context =>
                context.GetL10nAsync(accessor, l10nService, $"sections-{context.Source.Id}-name"));

            Field(p => p.Description, nullable: true).ResolveAsync(context =>
                context.GetL10nAsync(accessor, l10nService, $"sections-{context.Source.Id}-description"));

            Field(c => c.Lessons, type: typeof(ListGraphType<LessonGraphType>));
            Field(c => c.Sections, type: typeof(ListGraphType<SectionGraphType>));
        }
    }
    public class SectionGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public List<SectionGraph> Sections { get; set; }
        public List<LessonGraph> Lessons { get; set; }

        public static SectionGraph ToGraph(Section domain) =>
            domain != null
            ? new SectionGraph
            {
                Id = domain.Id,
                Sections = domain.Sections.Select(l => ToGraph(l)).ToList(),
                Lessons = domain.Lessons.Select(l => LessonGraph.ToGraph(l)).ToList()
            }.PopulateSystemGraphFields(domain)
            : null;
    }


    public class LessonGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public decimal PassingRatio { get; set; }
        public decimal Weight { get; set; }
        public string Version { get; set; }
        public long DurationInSeconds { get; set; }
        public List<LessonItemGraph> Items { get; set; }

        public static LessonGraph ToGraph(Lesson domain) =>
        domain != null
        ? new LessonGraph
        {
            Id = domain.Id,
            PassingRatio = domain.PassingRatio,
            Weight = domain.Weight,
            Version = domain.Version,
            DurationInSeconds = domain.DurationInSeconds,
            Items = domain.Items.Select(i => LessonItemGraph.ToGraph(i)).ToList()
        }.PopulateSystemGraphFields(domain)
        : null;
    }

    public class LessonGraphType : ObjectGraphType<LessonGraph>
    {

        public LessonGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IL10nService l10nService,
            PermissionValidator permissionValidator)
        {
            Name = "lesson";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.Id);

            Field(p => p.Name, nullable: true).ResolveAsync(context =>
                context.GetL10nAsync(accessor, l10nService, $"lessons-{context.Source.Id}-name"));

            Field(p => p.Description, nullable: true).ResolveAsync(context =>
                context.GetL10nAsync(accessor, l10nService, $"lessons-{context.Source.Id}-description"));

            Field(c => c.PassingRatio, nullable: true);

            Field(c => c.Weight, nullable: true);

            Field(c => c.Version, nullable: true);

            Field(c => c.DurationInSeconds, nullable: true);

            Field(c => c.Items, type: typeof(ListGraphType<LessonItemGraphType>));
        }
    }

    public class LessonItemGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public LessonItemTypes Type { get; set; }
        public string LocalizedContent { get; set; }
        public List<ItemOptionGraph> Options { get; set; }

        public static LessonItemGraph ToGraph(LessonItem domain) =>
        domain != null
        ? new LessonItemGraph
        {
            Id = domain.Id,
            Type = domain.Type,
            Options = domain.Options.Select(o => ItemOptionGraph.ToGraph(o)).ToList()
        }.PopulateSystemGraphFields(domain)
        : null;
    }
    public class LessonItemGraphType : ObjectGraphType<LessonItemGraph>
    {
        public LessonItemGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IL10nService l10nService,
            PermissionValidator permissionValidator)
        {
            Name = "lessonItem";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.Id);

            Field(p => p.LocalizedContent, nullable: true).ResolveAsync(context =>
                context.GetL10nAsync(accessor, l10nService, $"lessonItems-{context.Source.Id}-content"));

            Field(c => c.Type, type: typeof(LessonItemTypesGraphType));
            Field(c => c.Options, type: typeof(ListGraphType<ItemOptionGraphType>));
        }
    }

    public class ItemOptionGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string LocalizedContent { get; set; }
        public bool IsCorrect { get; set; }

        public static ItemOptionGraph ToGraph(ItemOption domain) =>
        domain != null
        ? new ItemOptionGraph
        {
            Id = domain.Id,
            IsCorrect = domain.IsCorrect,
        }.PopulateSystemGraphFields(domain)
        : null;
    }
    public class ItemOptionGraphType : ObjectGraphType<ItemOptionGraph>
    {
        public ItemOptionGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IL10nService l10nService,
            PermissionValidator permissionValidator)
        {
            Name = "itemOption";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.Id);

            Field(p => p.LocalizedContent, nullable: true).ResolveAsync(context =>
                context.GetL10nAsync(accessor, l10nService, $"lessonItemsOptions-{context.Source.Id}-content"));

            Field(c => c.IsCorrect, nullable: true).ResolveAsync(async context =>
            {
                await permissionValidator.Authorize(context, "readCorrectAnswers");
                return context.Source.IsCorrect;
            });
        }
    }



    public class CourseWhereInputGraphType : InputObjectGraphType<CourseWhere>
    {
        public CourseWhereInputGraphType()
        {
            Name = "courseWhereInput";
            Description = "A course search filter";
            Field(f => f.Or, type: typeof(ListGraphType<CourseWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<CourseWhereInputGraphType>));
            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);
            Field(f => f.Categories_contains_some, nullable: true);
            Field(f => f.Name_contains, nullable: true);
            this.PopulateSystemWhereFields();
        }
    }

    public class CreateCourseInputGraphType : InputObjectGraphType<CreateCourseCommand>
    {
        public CreateCourseInputGraphType()
        {
            Name = "createCourseInput";
            Description = "Create course input";
            Field(c => c.Categories, nullable: true);
            Field(c => c.Version, nullable: true);
        }
    }

    public class UpdateCourseInputGraphType : InputObjectGraphType<UpdateCourseCommand>
    {
        public UpdateCourseInputGraphType()
        {
            Name = "updateCourseInput";
            Description = "update course input";
            Field(c => c.Categories, nullable: true);
            Field(c => c.Version, nullable: true);
        }
    }

    public class AddLessonToSectionInputGraphType : InputObjectGraphType<AddLessonToSectionCommand>
    {
        public AddLessonToSectionInputGraphType()
        {
            Name = "addLessonToSectionInput";
            Description = "Adds a lesson to section of a course";

            Field(b => b.AddedById, nullable: true);
            Field(c => c.Version, nullable: true);
            Field(c => c.PassingRatio, nullable: true);
            Field(c => c.Weight, nullable: true);
            Field(c => c.DurationInSeconds, nullable: true);
        }
    }

    public class UpdateLessonOfSectionInputGraphType : InputObjectGraphType<UpdateLessonOfSectionCommand>
    {
        public UpdateLessonOfSectionInputGraphType()
        {
            Name = "updateLessonOfSectionInput";
            Description = "Updates a lesson of a section of a course";

            Field(b => b.ModifiedById, nullable: true);
            Field(c => c.Version, nullable: true);
            Field(c => c.PassingRatio, nullable: true);
            Field(c => c.Weight, nullable: true);
            Field(c => c.DurationInSeconds, nullable: true);
        }
    }

    public class LessonItemTypesGraphType : EnumerationGraphType<LessonItemTypes>
    {
        public LessonItemTypesGraphType()
        {
            Name = "lessonItemTypes";
            Description = "An enumeration of different types of lesson item";
        }
    }

    public class AddLessonItemInputGraphType : InputObjectGraphType<AddLessonItemCommand>
    {
        public AddLessonItemInputGraphType()
        {
            Name = "addLessonItemInput";
            Description = "Adds an item to a lesson";

            Field(b => b.AddedById, nullable: true);
            Field(c => c.Type, type: typeof(LessonItemTypesGraphType));
        }
    }

    public class UpdateLessonItemInputGraphType : InputObjectGraphType<UpdateLessonItemCommand>
    {
        public UpdateLessonItemInputGraphType()
        {
            Name = "updateLessonItemInput";
            Description = "Updates an item of a lesson";

            Field(b => b.ModifiedById, nullable: true);
            Field(c => c.Type, type: typeof(LessonItemTypesGraphType));
        }
    }
    public class AddOptionToLessonItemInputGraphType : InputObjectGraphType<AddOptionToLessonItemCommand>
    {
        public AddOptionToLessonItemInputGraphType()
        {
            Name = "addOptionToLessonItemInput";
            Description = "Adds an option to a lesson item";

            Field(b => b.AddedById, nullable: true);
            Field(c => c.IsCorrect, nullable: true);
        }
    }

    public class UpdateOptionOfLessonItemInputGraphType : InputObjectGraphType<UpdateOptionOfLessonItemCommand>
    {
        public UpdateOptionOfLessonItemInputGraphType()
        {
            Name = "updateOptionOfLessonItemInput";
            Description = "Updates an option of a lesson item";

            Field(b => b.ModifiedById, nullable: true);
            Field(c => c.IsCorrect, nullable: true);
        }
    }
}
