using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Interfaces.L10ns;
using GraphQL.Authorization;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeL10nMutations(IL10nService l10nService, PermissionValidator permissionValidator)
        {
             Field<ResultGraphType>()
                .Name("upsertL10n")
                .Description("Upserts a l10n")
                .Argument<UpsertL10nInputGraphType>("l10n", "The l10n to upsert")
                .AuthorizeWith("any")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, new PermissionRequest("createL10ns", "writeL10ns"));
                    string tenantId = ctx.GetTenantIdFromToken();
                    UpsertL10nCommand command = ctx.GetArgument<UpsertL10nCommand>("l10n");
                    await l10nService.UpsertAsync(tenantId, command);
                    return new Result { Status = "success" };
                });

            Field<ResultGraphType>()
               .Name("deleteL10n")
               .Description("deletes a l10n")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<StringGraphType>>("locale", "The local to delete from")
               .Argument<NonNullGraphType<StringGraphType>>("key", "The key to delete")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("deleteL10ns", "writeL10ns"));
                   string tenantId = ctx.GetTenantIdFromToken();
                   string locale = ctx.GetArgument<string>("locale");
                   string key = ctx.GetArgument<string>("key");
                   return await l10nService.DeleteAsync(tenantId, new DeleteL10nCommand
                   {
                       Locale = locale,
                       Key = key
                   });
               });

            Field<ResultGraphType>()
               .Name("deleteAllL10n")
               .Description("deletes all l10n for a certain key")
               .Argument<NonNullGraphType<StringGraphType>>("key", "The key to delete")
               .AuthorizeWith("any")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("deleteL10ns", "writeL10ns"));
                   string tenantId = ctx.GetTenantIdFromToken();
                   string key = ctx.GetArgument<string>("key");
                   return await l10nService.DeleteAllAsync(tenantId, new DeleteAllL10nCommand
                   {
                       Key = key
                   });
               });
        }
    }
}