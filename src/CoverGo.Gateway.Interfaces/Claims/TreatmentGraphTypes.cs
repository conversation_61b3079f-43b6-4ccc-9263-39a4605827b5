using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Claims;
using CoverGo.GraphQLGenerators;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Claims
{
    public class TreatmentsGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<TreatmentGraph> List { get; set; }
    }
    
    public class TreatmentsGraphType : ObjectGraphType<TreatmentsGraph>
    {
        public TreatmentsGraphType(CoverGoTreatmentService treatmentService, PermissionValidator permissionValidator)
        {
            Name = "treatments";
            
            Field(c => c.TotalCount)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var where = await FilterFromContextBuildInternal(permissionValidator, context);
                    return await treatmentService.CountAsync(tenantId, where);
                });

            Field(c => c.List, type: typeof(ListGraphType<TreatmentGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var where = await FilterFromContextBuildInternal(permissionValidator, context);

                    IReadOnlyCollection<Treatment> treatments = await treatmentService.QueryAsync(tenantId, where);
                    return treatments.Select(TreatmentGraph.ToGraph);
                });
        }

        private async Task<TreatmentWhere> FilterFromContextBuildInternal(
            PermissionValidator permissionValidator,
            ResolveFieldContext<TreatmentsGraph> context)
        {
            var allowedIds = (await permissionValidator.GetTargetIdsFromClaim(context,"readTreatments")).ToList();
                   
            Filter<TreatmentFilter> filter =  context.ComputeArgAndVar<Filter<TreatmentFilter>, TreatmentsGraph>("where") ?? new Filter<TreatmentFilter>();
            filter.And ??= new List<Filter<TreatmentFilter>>();
                    
            if (!allowedIds.Contains("all"))
            {
                filter.And.Add(new Filter<TreatmentFilter>
                {
                    Where = new TreatmentFilter { Id_in = allowedIds.ToList() }
                });
            }
                   
            int? skip = context.ComputeArgAndVar<int?, TreatmentsGraph>("skip");
            int? first = context.ComputeArgAndVar<int?, TreatmentsGraph>("limit");
            SortGraph sort = context.ComputeArgAndVar<SortGraph, TreatmentsGraph>("sort");

            OrderBy orderBy = null;
            if (sort != null)
            {
                orderBy = sort.ToOrderBy();
            }

            return new TreatmentWhere()
            {
                Skip = skip ?? 0,
                First = first ?? 25,
                OrderBy = orderBy,
                Where = filter
            };
        }
    }
    
    public class TreatmentGraph
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string Category { get; set; }
        public string Name { get; set; }
        public string System { get; set; }
        public string SubSystem { get; set; }
        
        public static TreatmentGraph ToGraph(Treatment domain)
        {
            if (domain == null)
                return null;

            return new TreatmentGraph
            {
                Id = domain.Id,
                Name =  domain.Name,
                System = domain.System,
                SubSystem = domain.SubSystem,
                Code = domain.Code,
                Category = domain.Category
            };
        }
    }

    public class TreatmentGraphType : AutoObjectGraphType<TreatmentGraph>
    {
    }
    
    
    public class TreatmentInputGraph : InputObjectGraphType<CreateTreatmentCommand>
    {
        public TreatmentInputGraph()
        {
            Name = "treatmentInput";
            Description = "treatment input";

            Field(x => x.Name, true);
            Field(x => x.Code, true);
            Field(x => x.System, true);
            Field(x => x.SubSystem, true);
            Field(x => x.Category, true);
        }
    }
    
    public class TreatmentUpdateInputGraph : InputObjectGraphType<UpdateTreatmentCommand>
    {
        public TreatmentUpdateInputGraph()
        {
            Name = "treatmentUpdateInput";
            Description = "treatment update input";

            Field(x => x.Id, true);
            Field(x => x.Name, true);
            Field(x => x.Code, true);
            Field(x => x.System, true);
            Field(x => x.SubSystem, true);
            Field(x => x.Category, true);
        }
    }
    
    public class TreatmentRemoveInputGraph : InputObjectGraphType<RemoveCommand>
    {
        public TreatmentRemoveInputGraph()
        {
            Name = "treatmentRemoveInput";
            Description = "treatment remove input";

            Field(x => x.Id, true);
        }
    }
    
    public class TreatmentBatchInputGraph : InputObjectGraphType<TreatmentBatchCommand>
    {
        public TreatmentBatchInputGraph()
        {
            Name = "treatmentBatchInput";
            Description = "Treatment Batch Input";

            Field(x => x.Create, true, typeof(ListGraphType<TreatmentInputGraph>));
            Field(x => x.Update, true, typeof(ListGraphType<TreatmentUpdateInputGraph>));
            Field(x => x.Delete, true, typeof(ListGraphType<TreatmentRemoveInputGraph>));
        }
    }
    
    public class TreatmentFilterInputGraph : InputObjectGraphType<Filter<TreatmentFilter>>
    {
        public TreatmentFilterInputGraph()
        {
            Name = "treatmentFilterInput";
            Description = "Treatment Filter Input";

            Field(x => x.And, true, typeof(ListGraphType<TreatmentFilterInputGraph>));
            Field(x => x.Or, true, typeof(ListGraphType<TreatmentFilterInputGraph>));
            Field(x => x.Where, true, typeof(TreatmentFilterWhereInputGraph));
        }
    }
    
    public class TreatmentFilterWhereInputGraph : InputObjectGraphType<TreatmentFilter>
    {
        public TreatmentFilterWhereInputGraph()
        {
            Name = "treatmentFilterWhereInput";
            Description = "Treatment Filter Where Input";

            Field(x => x.Id, true);
            Field(x => x.Name, true);
            Field(x => x.Code, true);
            Field(x => x.Category, true);
            Field(x => x.System, true);
            Field(x => x.SubSystem, true);
            
            Field(x => x.Name_contains, true);
            Field(x => x.Code_contains, true);
            Field(x => x.Category_contains, true);
            Field(x => x.System_contains, true);
            Field(x => x.SubSystem_contains, true);
            
            Field(x => x.Id_in, true);
            Field(x => x.Name_in, true);
            Field(x => x.Code_in, true);
            Field(x => x.Category_in, true);
            Field(x => x.System_in, true);
            Field(x => x.SubSystem_in, true);
        }
    }
}