using System;
using System.Collections.Generic;
using System.Linq;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Claims;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Auth;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using GraphQL.DataLoader;
using GraphQL.Types;
using Newtonsoft.Json;
using CurrencyCode = CoverGo.Gateway.Domain.CurrencyCode;

namespace CoverGo.Gateway.Interfaces.Claims
{
    public class GuaranteeOfPaymentsGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<GuaranteeOfPaymentGraphType.GuaranteeOfPaymentGraph> List { get; set; }
    }

    public class GuaranteeOfPaymentsGraphType : ObjectGraphType<GuaranteeOfPaymentsGraph>
    {
        public GuaranteeOfPaymentsGraphType(
            IGuaranteeOfPaymentService guaranteeOfPaymentService,
            PermissionValidator permissionValidator)
        {
            Name = "guaranteeOfPayments";
            Description = "Gets all guarantee of payments";

            Field(c => c.TotalCount)
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();

                   var allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readGOPs");
                   GuaranteeOfPaymentWhere where = allowedIds.Contains("all")
                       ? context.ComputeArgAndVar<GuaranteeOfPaymentWhere, GuaranteeOfPaymentsGraph>("where") ?? new GuaranteeOfPaymentWhere()
                       : new GuaranteeOfPaymentWhere
                       {
                           And = new List<GuaranteeOfPaymentWhere>
                           {
                                context.ComputeArgAndVar<GuaranteeOfPaymentWhere, GuaranteeOfPaymentsGraph>("where") ?? new GuaranteeOfPaymentWhere(),
                                new() { Id_in = allowedIds.ToList() }
                           }
                       };

                   return await guaranteeOfPaymentService.GetTotalCountAsync(tenantId, where);
               });

            Field(c => c.List, type: typeof(ListGraphType<GuaranteeOfPaymentGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readGOPs");
                    GuaranteeOfPaymentWhere where = allowedIds.Contains("all")
                        ? context.ComputeArgAndVar<GuaranteeOfPaymentWhere, GuaranteeOfPaymentsGraph>("where") ?? new GuaranteeOfPaymentWhere()
                        : new GuaranteeOfPaymentWhere
                        {
                            And = new List<GuaranteeOfPaymentWhere>
                            {
                                context.ComputeArgAndVar<GuaranteeOfPaymentWhere, GuaranteeOfPaymentsGraph>("where") ?? new GuaranteeOfPaymentWhere(),
                                new() { Id_in = allowedIds.ToList() }
                            }
                        };

                    int? skip = context.ComputeArgAndVar<int?, GuaranteeOfPaymentsGraph>("skip");
                    int? first = context.ComputeArgAndVar<int?, GuaranteeOfPaymentsGraph>("limit");
                    SortGraph sort = context.ComputeArgAndVar<SortGraph, GuaranteeOfPaymentsGraph>("sort");
                    DateTime? asOf = context.ComputeArgAndVar<DateTime?, GuaranteeOfPaymentsGraph>("asOf");

                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy, AsOf = asOf };
                    IEnumerable<GuaranteeOfPayment> guaranteeOfPayments = await guaranteeOfPaymentService.GetAsync(tenantId, queryArguments);
                    return guaranteeOfPayments.Select(c => GuaranteeOfPaymentGraphType.GuaranteeOfPaymentGraph.ToGraph(c));
                });
        }
    }

    public class GuaranteeOfPaymentGraphType : ObjectGraphType<GuaranteeOfPaymentGraphType.GuaranteeOfPaymentGraph>
    {
        public GuaranteeOfPaymentGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IPolicyService policyService,
            IGuaranteeOfPaymentService guaranteeOfPaymentService,
            PermissionValidator permissionValidator)
        {
            Name = "guaranteeOfPayment";
            Description = "guaranteeOfPayment";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.Id, type: typeof(IdGraphType));

            Field(c => c.Policy, type: typeof(PolicyGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.Policy?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var dataLoader = accessor.Context.GetOrAddBatchLoader<string, Policy>("GetPolicies",
                        i => policyService.GetDictionaryAsync(tenantId, new PolicyWhere { Id_in = i?.ToList() }));

                    Policy policy = await dataLoader.LoadAsync(context.Source.Policy.Id);

                    return PolicyGraph.ToGraph(policy);
                });

            Field(c => c.Member, type: typeof(CustomerInterfaceGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.Member?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var individualLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                        i => individualService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                    Individual individualDto = await individualLoader.LoadAsync(context.Source.Member.Id);

                    return individualDto?.ToGraph();
                });



            Field(c => c.Status, nullable: true);

            Field(c => c.Fields, nullable: true);

            Field(c => c.Doctor, nullable: true);

            Field(c => c.ProviderId, nullable: true);

            Field(c => c.ApprovedBy, type: typeof(LoginGraphType)).ResolveAsync(async context =>
            {
                if (context.Source.ApprovedBy?.Id == null)
                    return null;

                var allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,
                    "readLogins", new List<string> { context.Source.ApprovedBy.Id });
                if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.ApprovedBy.Id))
                    return null;

                string tenantId = context.GetTenantIdFromToken();

                var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                    i => authService.GetLoginsDictionaryAsync(tenantId, i));

                Login loginDao = await loginLoader.LoadAsync(context.Source.ApprovedBy.Id);

                return LoginGraph.ToGraph(loginDao);
            });

            Field(c => c.Provider, type: typeof(EntityInterfaceGraphType),  nullable: true)
                .ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();
                var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>(
                        "GetCompanies",
                        ids => companyService.GetDictionaryAsync(tenantId, new CompanyWhere 
                        {
                            Id = context.Source.ProviderId,
                            Tags_contains = "provider"
                        }));
                Company companyDto = await companyLoader.LoadAsync(context.Source.ProviderId);
    
                return companyDto?.ToGraph();
            });

            Field(c => c.RejectedBy, type: typeof(LoginGraphType)).ResolveAsync(async context =>
            {
                if (context.Source.RejectedBy?.Id == null)
                    return null;

                var allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,
                    "readLogins", new List<string> { context.Source.RejectedBy.Id });
                if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.RejectedBy.Id))
                    return null;

                string tenantId = context.GetTenantIdFromToken();

                var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                    i => authService.GetLoginsDictionaryAsync(tenantId, i));

                Login loginDao = await loginLoader.LoadAsync(context.Source.RejectedBy.Id);

                return LoginGraph.ToGraph(loginDao);
            });

            Field(c => c.RejectionCodes, type: typeof(ListGraphType<RejectionCodeNameGraphType>), nullable: true);

            Field(c => c.RejectionRemarks, nullable: true);

            Field(p => p.Attachments, type: typeof(ListGraphType<AttachmentGraphType>), nullable: true);

            Field(c => c.Notes, type: typeof(ListGraphType<NoteGraphType>), nullable: true);

            Field(c => c.ApprovedAmount, nullable: true);

            Field(c => c.EstimatedAmount, nullable: true);

            Field(c => c.ExcessAmount, nullable: true);

            Field(c => c.CurrencyCode, nullable: true, typeof(CurrencyCodeEnumerationGraphType));
            Field(c => c.IssuerNumber, nullable: true);
            Field(c => c.ApprovedAt, type: typeof(DateTimeGraphType), nullable: true);
            Field(c => c.RejectedAt, type: typeof(DateTimeGraphType), nullable: true);
            Field(c => c.AccessPolicy, type: typeof(AccessPolicyTypeEnumGraphType), nullable: true);
            Field(c => c.Events, type: typeof(ListGraphType<GuaranteeOfPaymentEventLogGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    var dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, GuaranteeOfPaymentEventLog>(
                        "GetGopEventLogs",
                        async i => (await guaranteeOfPaymentService.GetEventsAsync(tenantId, new EventQuery { Ids = i?.ToList() })).ToLookup(e => e.RelatedId)
                    );

                    var logs = await dataLoader.LoadAsync(context.Source.Id);

                    return logs.Select(GuaranteeOfPaymentEventLogGraph.ToGraph);
                });
        }

        public class GuaranteeOfPaymentGraph : SystemObjectGraph
        {
            public string Id { get; set; }

            public EntityGraph Member { get; set; }

            public PolicyGraph Policy { get; set; }

            public string Status { get; set; }

            public string ProviderId { get; set; }

            public EntityGraph Provider { get; set; }

            public string Doctor { get; set; }

            public string Fields { get; set; }

            public Decimal? EstimatedAmount { get; set; }

            public Decimal? ApprovedAmount { get; set; }

            public Decimal? ExcessAmount { get; set; }

            public CurrencyCode? CurrencyCode { get; set; }

            public LoginGraph RejectedBy { get; set; }

            public LoginGraph ApprovedBy { get; set; }

            public List<CodeNameGraph> RejectionCodes { get; set; }

            public string RejectionRemarks { get; set; }

            public IEnumerable<AttachmentGraph> Attachments { get; set; }

            public IEnumerable<NoteGraph> Notes { get; set; }

            public string IssuerNumber { get; set; }

            public DateTime? ApprovedAt { get; set; }

            public DateTime? RejectedAt { get; set; }

            public AccessPolicy? AccessPolicy { get; set; }

            public IEnumerable<GuaranteeOfPaymentEventLogGraph> Events { get; set; }

            public static GuaranteeOfPaymentGraph ToGraph(GuaranteeOfPayment domain) =>
                domain != null
                    ? new GuaranteeOfPaymentGraph
                    {
                        Id = domain.Id,
                        Member = domain.MemberId != null ? new EntityGraph {Id = domain.MemberId} : null,
                        Policy = domain.PolicyId != null ? new PolicyGraph {Id = domain.PolicyId} : null,
                        Status = domain.Status,
                        ProviderId = domain.ProviderId,
                        Doctor = domain.Doctor,
                        RejectedBy = domain.RejectedById != null ? new LoginGraph {Id = domain.RejectedById} : null,
                        ApprovedBy = domain.ApprovedById != null ? new LoginGraph {Id = domain.ApprovedById} : null,
                        RejectionCodes = domain.RejectionCodes?.Select(c => new CodeNameGraph {Code = c}).ToList(),
                        RejectionRemarks = domain.RejectionRemarks,
                        Notes = domain.Notes.Select(NoteGraph.ToGraph),
                        Attachments = domain.Attachments?.Select(s => AttachmentGraph.ToGraph(s)),
                        Fields = domain.Fields?.ToString(Formatting.None),
                        EstimatedAmount = domain.EstimatedAmount,
                        ApprovedAmount = domain.ApprovedAmount,
                        ExcessAmount = domain.ExcessAmount,
                        CurrencyCode = domain.CurrencyCode,
                        IssuerNumber = domain.IssuerNumber,
                        ApprovedAt = domain.ApprovedAt,
                        RejectedAt = domain.RejectedAt,
                        AccessPolicy = domain.AccessPolicy
                    }.PopulateSystemGraphFields(domain)
                    : null;
        }
    }

    public class CreateGuaranteeOfPaymentInputGraph
    {
        public string PolicyId { get; set; }

        public string MemberId { get; set; }
        public string Status { get; set; }

        public string ProviderId { get; set; }

        public string Doctor { get; set; }

        public string Fields { get; set; }

        public decimal? EstimatedAmount { get; set; }

        public CurrencyCode? CurrencyCode { get; set; }
        
        public string IssuerNumber { get; set; }
    }

    public class CreateGuaranteeOfPaymentInputGraphType : InputObjectGraphType<CreateGuaranteeOfPaymentInputGraph>
    {
        public CreateGuaranteeOfPaymentInputGraphType()
        {
            Name = "createGOPInput";
            Description = "Create guarantee of payment input";

            Field(c => c.MemberId, nullable: true);
            Field(c => c.PolicyId, nullable: true);
            Field(c => c.Status, nullable: true);
            Field(c => c.ProviderId, nullable: true);
            Field(c => c.Doctor, nullable: true);
            Field(c => c.Fields, nullable: true);
            Field(c => c.EstimatedAmount, nullable: true);
            Field(c => c.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType), nullable: true);
            Field(c => c.IssuerNumber, nullable: true);
        }
    }

    public class UpdateGuaranteeOfPaymentInputGraph
    {

        public string Status { get; set; }

        public string ProviderId { get; set; }

        public string Doctor { get; set; }

        public string Fields { get; set; }

        public string FieldsPatch { get; set; }

        public decimal? EstimatedAmount { get; set; }

        public CurrencyCode? CurrencyCode { get; set; }
        
        public string UpdateType { get; set; }
    }

    public class UpdateGuaranteeOfPaymentInputGraphType : InputObjectGraphType<UpdateGuaranteeOfPaymentInputGraph>
    {
        public UpdateGuaranteeOfPaymentInputGraphType()
        {
            Name = "updateGOPInput";
            Description = "Update guarantee of payment input";

            Field(c => c.Status, nullable: true);
            Field(c => c.ProviderId, nullable: true);
            Field(c => c.Doctor, nullable: true);
            Field(c => c.Fields, nullable: true);
            Field(c => c.FieldsPatch, nullable: true);
            Field(c => c.EstimatedAmount, nullable: true);
            Field(c => c.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType), nullable: true);
            Field(c => c.UpdateType, nullable: true);
        }
    }

    public class RejectGuaranteeOfPaymentInputGraph
    {

        public List<string> Codes { get; set; }

        public string Remarks { get; set; }
    }

    public class RejectGuaranteeOfPaymentInputGraphType : InputObjectGraphType<RejectGuaranteeOfPaymentInputGraph>
    {
        public RejectGuaranteeOfPaymentInputGraphType()
        {
            Name = "rejectGOPInput";
            Description = "Reject guarantee of payment input";

            Field(c => c.Codes, nullable: true, type: typeof(ListGraphType<StringGraphType>));
            Field(c => c.Remarks, nullable: true);
        }
    }

    public class ApproveGuaranteeOfPaymentInputGraph
    {
        public decimal ApprovedAmount { get; set; }

        public decimal ExcessAmount { get; set; }
    }

    public class ApproveGuaranteeOfPaymentInputGraphType : InputObjectGraphType<ApproveGuaranteeOfPaymentInputGraph>
    {
        public ApproveGuaranteeOfPaymentInputGraphType()
        {
            Name = "approveGOPInput";
            Description = "Approve guarantee of payment input";

            Field(c => c.ApprovedAmount, nullable: true);
            Field(c => c.ExcessAmount, nullable: true);
        }
    }

    public class GuaranteeOfPaymentWhereInputGraphType : InputObjectGraphType<GuaranteeOfPaymentWhere>
    {
        public GuaranteeOfPaymentWhereInputGraphType()
        {
            Name = "gopWhereInput";
            Description = "A guarantee of payment search filter";

            Field(f => f.Or, type: typeof(ListGraphType<GuaranteeOfPaymentWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<GuaranteeOfPaymentWhereInputGraphType>));

            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);

            Field(f => f.IssuerNumber, nullable: true);
            Field(f => f.IssuerNumber_in, nullable: true);

            Field(f => f.MemberId, nullable: true);
            Field(f => f.MemberId_in, nullable: true);

            Field(f => f.Fields, nullable: true, type:typeof(FieldsWhereInputGraphType));

            Field(f => f.Status, nullable: true);
            Field(f => f.Status_in, nullable: true);

            Field(f => f.ProviderId, nullable: true);
            Field(f => f.ProviderId_in, nullable: true);
            
            Field(f => f.AccessPolicy, nullable: true, type: typeof(AccessPolicyTypeEnumGraphType));

            this.PopulateSystemWhereFields();
        }
    }
}
