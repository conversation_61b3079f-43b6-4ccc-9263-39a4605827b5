using System.Net.Http;
using CoverGo.Applications.Clients;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Claims;

namespace CoverGo.Gateway.Interfaces.Claims
{
    public class CoverGoTreatmentService : CoverGoGenericGenericServiceRestClientBase<Treatment,
        CreateTreatmentCommand, UpdateTreatmentCommand, RemoveCommand, TreatmentBatchCommand, TreatmentWhere,
        TreatmentFilter>
    {
        private readonly HttpClient _client;

        public CoverGoTreatmentService(HttpClient client) : base(client)
        {
            _client = client;
        }

        protected override string ApiBaseUrlBuildInternal(string tenantId) => 
            $"{tenantId}/api/v2/treatments";
    }
}