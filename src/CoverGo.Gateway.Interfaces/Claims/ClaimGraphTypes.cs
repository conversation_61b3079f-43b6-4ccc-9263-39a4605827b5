using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Claims;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Auth;
using CoverGo.Gateway.Interfaces.Cases;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Transactions;
using CoverGo.Gateway.Interfaces.Users;
using CoverGo.GraphQLGenerators;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using GraphQL.DataLoader;
using GraphQL.Types;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using CurrencyCode = CoverGo.Gateway.Domain.CurrencyCode;

namespace CoverGo.Gateway.Interfaces.Claims
{
    public class ClaimsGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<ClaimGraph> List { get; set; }
    }

    public class ClaimsGraphType : ObjectGraphType<ClaimsGraph>
    {
        public ClaimsGraphType(IClaimService claimService, 
            IEntityService entityService,
            IPolicyService policyService,
            PermissionValidator permissionValidator)
        {
            Name = "claims";
            Description = "Gets all claims";

            ClaimWhere GetWhereFromContext(ResolveFieldContext<ClaimsGraph> context) =>
                context.ComputeArgAndVar<ClaimWhere, ClaimsGraph>("where");

            async Task<ClaimWhere> GetWhereFromContextAndCombineWithAllowedIds(ResolveFieldContext<ClaimsGraph> context)
            {
                var allowedIds = (await permissionValidator.GetTargetIdsFromClaim(context,"readClaims")).ToList();
                ClaimWhere whereFromContext = GetWhereFromContext(context);

                return allowedIds.Contains("all")
                    ? whereFromContext
                    : whereFromContext != null 
                        ? new ClaimWhere
                        {
                                And = new List<ClaimWhere>
                                {
                                    GetWhereFromContext(context),
                                    new() { Id_in = allowedIds }
                                }
                            }
                        : new ClaimWhere { Id_in = allowedIds } ;
            }
            
            async Task ReplaceClaimantContainsToClaimantIdIn(string tenantId, ClaimWhere where)
            {
                if (where.Claimant_contains != null) {
                    IEnumerable<string> ids = await entityService.GenericQueryIds(tenantId, where.Claimant_contains);
                    where.ClaimantId_in = ids.ToList();
                    where.Claimant_contains = null;
                }
                if (where.And != null)
                    foreach (ClaimWhere item in where.And)
                        await ReplaceClaimantContainsToClaimantIdIn(tenantId, item);
                if (where.Or != null)
                    foreach (ClaimWhere item in where.Or)
                        await ReplaceClaimantContainsToClaimantIdIn(tenantId, item);
            }
            
            async Task ReplacePolicyContainsToPolicyIdIn(string tenantId, ClaimWhere where)
            {
                if (where.Policy_contains != null)
                {
                    await ReplaceHolderToHolderIdIn(tenantId, where.Policy_contains);
                    IEnumerable<string> ids = await policyService.GetIdsAsync(tenantId, where.Policy_contains);
                    where.PolicyId_in = ids.ToList();
                    where.Policy_contains = null;
                }
                if (where.And != null)
                    foreach (ClaimWhere item in where.And)
                        await ReplacePolicyContainsToPolicyIdIn(tenantId, item);
                if (where.Or != null)
                    foreach (ClaimWhere item in where.Or)
                        await ReplacePolicyContainsToPolicyIdIn(tenantId, item);
            }
            
            async Task ReplaceHolderToHolderIdIn(string tenantId, PolicyWhere where)
            {
                if (where.Holder != null) {
                    IEnumerable<string> ids = await entityService.GenericQueryIds(tenantId, where.Holder);
                    where.ContractHolder = new EntityWhere { Id_in = ids.ToList() };
                    where.Holder = null;
                }
                if (where.And != null)
                    foreach (PolicyWhere item in where.And)
                        await ReplaceHolderToHolderIdIn(tenantId, item);
                if (where.Or != null)
                    foreach (PolicyWhere item in where.Or)
                        await ReplaceHolderToHolderIdIn(tenantId, item);
            }

            Field(c => c.TotalCount)
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();

                   ClaimWhere where = await GetWhereFromContextAndCombineWithAllowedIds(context);
                   GroupByGraph? group = context.ComputeArgAndVar<GroupByGraph, ClaimsGraph>("groupBy");

                   if (where != null)
                   {
                       await ReplaceClaimantContainsToClaimantIdIn(tenantId, where);
                       await ReplacePolicyContainsToPolicyIdIn(tenantId, where);
                   }
                   if (group != null)
                   {
                       GroupBy groupBy = group.ToGroupBy();
                       var queryArguments = new Domain.QueryArguments { Where = where, GroupBy = groupBy };
                       IEnumerable<Claim> claims = await claimService.GetAsync(tenantId, queryArguments);
                       return claims.Count();
                   }

                   return await claimService.GetTotalCountAsync(tenantId, where);
               });

            Field(c => c.List, type: typeof(ListGraphType<ClaimGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    ClaimWhere where = await GetWhereFromContextAndCombineWithAllowedIds(context);
                    if (where != null)
                    {
                        await ReplaceClaimantContainsToClaimantIdIn(tenantId, where);
                        await ReplacePolicyContainsToPolicyIdIn(tenantId, where);
                    }

                    int? skip = context.ComputeArgAndVar<int?, ClaimsGraph>("skip");
                    int? first = context.ComputeArgAndVar<int?, ClaimsGraph>("limit");
                    SortGraph sort = context.ComputeArgAndVar<SortGraph, ClaimsGraph>("sort");
                    DateTime? asOf = context.ComputeArgAndVar<DateTime?, ClaimsGraph>("asOf");
                    GroupByGraph? group = context.ComputeArgAndVar<GroupByGraph, ClaimsGraph>("groupBy");

                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    GroupBy groupBy = null;
                    if (group != null)
                    {
                        groupBy = group.ToGroupBy();
                    }
                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy, AsOf = asOf,GroupBy = groupBy };
                    IEnumerable<Claim> claims = await claimService.GetAsync(tenantId, queryArguments);
                    return claims.Select(c => ClaimGraph.ToGraph(c));
                    
                });
        }

        
    }

    public class ClaimGraphType : ObjectGraphType<ClaimGraph>
    {
        public ClaimGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            IPolicyService policyService,
            ITransactionService transactionService,
            IProductService productService,
            IClaimService claimService,
            PermissionValidator permissionValidator,
            CoverGoPolicyMembersService policyMembersService)
        {
            Name = "claim";
            Description = "claim";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.Id, type: typeof(IdGraphType));

            Field(c => c.IssuerNumber, nullable: true);

            Field(c => c.ExternalId, nullable: true);

            Field(c => c.Policy, type: typeof(PolicyGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.Policy?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var dataLoader = accessor.Context.GetOrAddBatchLoader<string, Policy>("GetPolicies",
                        i => policyService.GetDictionaryAsync(tenantId, new PolicyWhere { Id_in = i?.ToList() }));

                    Policy policy = await dataLoader.LoadAsync(context.Source.Policy.Id);

                    return PolicyGraph.ToGraph(policy);
                });

            Field(c => c.PolicyMember, type: typeof(PolicyMemberGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.Policy?.Id == null)
                        return null;

                    if (context.Source.Claimant?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    PolicyMembersWhere policyMembersFilter = new()
                    {
                        PolicyId_In = new List<string> {context.Source.Policy.Id},
                        IndividualId = context.Source.Claimant.Id
                    };

                    IReadOnlyCollection<PolicyMember> members = await policyMembersService.QueryAsync(tenantId, policyMembersFilter, context.CancellationToken);

                    return members.FirstOrDefault();
                });

            Field(c => c.Claimant, type: typeof(CustomerInterfaceGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.Claimant?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var individualLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                        i => individualService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                    var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>("GetCompanies",
                        i => companyService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                    Task<Individual> individualDtoTask = individualLoader.LoadAsync(context.Source.Claimant.Id);
                    Task<Company> companyDtoTask = companyLoader.LoadAsync(context.Source.Claimant.Id);
                    await Task.WhenAll(individualDtoTask, companyDtoTask);

                    return individualDtoTask.Result?.ToGraph() ?? companyDtoTask.Result?.ToGraph();
                });

            Field(c => c.Panel, type: typeof(OrganizationGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.Panel?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    IEnumerable<string> allowedOrganizationIds = await permissionValidator.GetTargetIdsFromClaim(context,"readOrganizations");
                    var orgLoader = accessor.Context.GetOrAddBatchLoader<string, Organization>("GetOrganizations",
                        ids => organizationService.GetDictionaryAsync(tenantId, allowedOrganizationIds.Contains("all")
                            ? new OrganizationWhere { Id_in = ids?.ToList() }
                            : new OrganizationWhere
                            {
                                And = new List<OrganizationWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedOrganizationIds.ToList() }
                                }
                            }));

                    Organization org = await orgLoader.LoadAsync(context.Source.Panel.Id);
                    return org?.ToGraph() as OrganizationGraph;
                });

            Field(p => p.Transactions, type: typeof(ListGraphType<TransactionGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readTransactions");

                    var loader = accessor.Context.GetOrAddCollectionBatchLoader<string, Transaction>(
                        "GetTransactionsFromClaimIds",
                        claimIds => transactionService.GetByClaimIdsLookupAsync(tenantId, allowedIds.Contains("all")
                        ? new TransactionWhere { ClaimId_in = claimIds?.ToList() }
                        : new TransactionWhere
                        {
                            And = new List<TransactionWhere>
                            {
                                new() { ClaimId_in = claimIds?.ToList() },
                                new() { Id_in = allowedIds.ToList() }
                            }
                        }));

                    IEnumerable<Transaction> transactions = await loader.LoadAsync(context.Source.Id);

                    return transactions?.Select(l => TransactionGraph.ToGraph(l));
                });

            Field(c => c.BenefitClaims, type: typeof(ListGraphType<BenefitClaimGraphType>));

            Field(c => c.TotalValues, type: typeof(ClaimValueGraphType));

            Field(c => c.Facts, type: typeof(ListGraphType<FactGraphType>));

            Field(c => c.Status, nullable: true);
            Field(c => c.ClaimId, nullable: true);

            Field(c => c.Remark, nullable: true);

            Field(c => c.Fields, nullable: true)
                .ResolveAsync(context => context.GetPermittedProjection(authService, accessor, context.Source.Fields, context.Source.Id, "claim"));

            Field(c => c.ImportBatchId, type: typeof(BatchIdGraphType), nullable: true);

            Field(c => c.ExportBatchIds, type: typeof(ListGraphType<BatchIdGraphType>), nullable: true);

            Field(c => c.DiagnosisCodes, type: typeof(ListGraphType<DiagnosisCodeNameGraphType>));

            Field(c => c.OperationCodes, type: typeof(ListGraphType<OperationCodeNameGraphType>));

            Field(c => c.Provider, type: typeof(EntityInterfaceGraphType))
                .ResolveAsync(async context =>
                {
                    string entityId = context.Source.Provider?.Id;

                    if (entityId == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var organizationLoader = accessor.Context.GetOrAddBatchLoader<string, Organization>(
                        "GetOrganizations",
                        ids => organizationService.GetDictionaryAsync(tenantId, new OrganizationWhere { Id_in = ids?.ToList() }));

                    var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>(
                        "GetCompanies",
                        ids => companyService.GetDictionaryAsync(tenantId, new CompanyWhere { Id_in = ids?.ToList() }));

                    Task<Organization> organizationDtoTask = organizationLoader.LoadAsync(entityId);
                    Task<Company> companyDtoTask = companyLoader.LoadAsync(entityId);
                    await Task.WhenAll(organizationDtoTask, companyDtoTask);

                    return organizationDtoTask?.Result?.ToGraph() ?? companyDtoTask?.Result?.ToGraph();
                });

            Field(c => c.RejectedBy, type: typeof(LoginGraphType)).ResolveAsync(async context =>
            {
                if (context.Source.RejectedBy?.Id == null)
                    return null;

                IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,"readLogins", new List<string> { context.Source.RejectedBy.Id });
                if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.RejectedBy.Id))
                    return null;

                string tenantId = context.GetTenantIdFromToken();

                var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                    i => authService.GetLoginsDictionaryAsync(tenantId, i));

                Login loginDao = await loginLoader.LoadAsync(context.Source.RejectedBy.Id);

                return LoginGraph.ToGraph(loginDao);
            });

            Field(c => c.RejectionCodes, type: typeof(ListGraphType<RejectionCodeNameGraphType>));

            Field(c => c.RejectionRemarks, nullable: true);

            Field(c => c.RejectionReasons, type: typeof(ListGraphType<RejectionReasonGraphType>), nullable: true);

            Field(c => c.Events, type: typeof(ListGraphType<ClaimEventLogGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    var dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, ClaimEventLog>(
                        "GetClaimEvents",
                        async i => (await claimService.GetEventsAsync(tenantId, new EventQuery { Ids = i?.ToList() })).ToLookup(e => e.RelatedId)
                    );

                    IEnumerable<ClaimEventLog> logs = await dataLoader.LoadAsync(context.Source.Id);

                    return logs.Select(ClaimEventLogGraph.ToGraph);
                });

            Field(c => c.Notes, type: typeof(ListGraphType<NoteGraphType>));

            Field(c => c.Stakeholders, type: typeof(ListGraphType<StakeholderGraphType>));

            Field(c => c.GuaranteeOfPayments, type: typeof(ListGraphType<GuaranteeOfPaymentGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readGOPs");

                    var dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, IEnumerable<GuaranteeOfPayment>>(
                        "GetGuaranteeOfPaymentsByClaimId",
                        async claimIds =>
                        {
                            var claimWhere = allowedIds.Contains("all")
                                ? new ClaimWhere()
                                {
                                    Id_in = claimIds?.ToList()
                                }
                                : new ClaimWhere
                                {
                                    And = new List<ClaimWhere>
                                    {
                                        new() {Id_in = allowedIds.ToList()},
                                        new() {Id_in = claimIds.ToList()}
                                    }
                                };
                            return await claimService.GetAddedGuaranteeOfPaymentsByClaimIds(tenantId, claimWhere);
                        });

                    var guaranteeOfPayments = await dataLoader.LoadAsync(context.Source.Id);

                    return guaranteeOfPayments.SelectMany(x => x).Select(GuaranteeOfPaymentGraphType.GuaranteeOfPaymentGraph.ToGraph);
                });

            Field(p => p.Attachments, type: typeof(ListGraphType<AttachmentGraphType>), nullable: true);
            
            Field(c => c.ClaimBalance, type: typeof(ListGraphType<BenefitGraphType>))
                .ResolveAsync(async context =>
                {
                    if (!context.Source.BenefitClaims?.Any() ?? true)
                        return null;

                    if (context.Source.Policy?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var dataLoader = accessor.Context.GetOrAddBatchLoader<string, Policy>("GetPolicies",
                        i => policyService.GetDictionaryAsync(tenantId, new PolicyWhere { Id_in = i?.ToList() }));

                    Policy policy = await dataLoader.LoadAsync(context.Source.Policy.Id);

                    Offer acceptedOffer = policy?.Offers?.FirstOrDefault(o => o.Id == policy.AcceptedOfferId);
                    ProductId productId = acceptedOffer?.ProductId;
                    if (productId == null)
                        return null;

                    string clientId = context.GetClientIdFromToken();

                    Product2 productDto = (await productService.GetAsync(tenantId, clientId, new ProductQuery
                    {
                        Where = new ProductWhere { Id_in = new List<ProductId> { productId } }
                    })).FirstOrDefault();

                    var summarizedBenefits = ProductExtensions.SummarizeBenefits(acceptedOffer.BenefitOptions?.Select(bo =>
                        new Benefit
                        {
                            TypeId = bo.TypeId,
                            OptionKey = bo.Key,
                            Value = bo.Value,
                            CurrencyCode = bo.CurrencyCode
                        }
                    ), productDto?.Benefits).ToList();

                    // Get claim summary
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readClaims");
                    var requestedWhere = new ClaimWhere { And = new List<ClaimWhere> { new() { PolicyId_in = new List<string> { policy?.Id } }, new() { Status = "APPROVED" } } };
                    IEnumerable<Claim> claims = await claimService.GetAsync(tenantId, new Domain.QueryArguments
                    {
                        Where = allowedIds.Contains("all")
                            ? requestedWhere
                            : new ClaimWhere { And = new List<ClaimWhere> { requestedWhere, new() { Id_in = allowedIds } } }
                    });

                    IEnumerable<BenefitClaim> benefitClaims = context.Source.Status != "APPROVED"
                    ? claims.SelectMany(c => c.BenefitClaims)?.Concat(
                        context.Source.BenefitClaims?.Select(bc => new BenefitClaim
                        {
                            BenefitTypeId = bc.Benefit?.TypeId,
                            Values = bc.Values
                        })
                    )
                    : claims.SelectMany(c => c.BenefitClaims);

                    // Substract
                    foreach (BenefitClaim benefitClaim in benefitClaims)
                        GraphQLToolsExtensions.RecursiveSubstract(summarizedBenefits, benefitClaim, benefitClaim.BenefitTypeId);

                    return summarizedBenefits?.Select(b => b.ToGraph(acceptedOffer.ProductId))?.ToList();
                });

            Field(c => c.Reversals, type: typeof(ListGraphType<ClaimReversalGraphType>), nullable: true);
            Field(c => c.ClaimHandler, type: typeof(LoginGraphType), nullable: true);
            Field(c => c.ClaimApprover, type: typeof(LoginGraphType), nullable: true);
            Field(c => c.AccessPolicy, type: typeof(AccessPolicyTypeEnumGraphType), nullable: true);
        }
    }

    public class ClaimGraph : SystemObjectGraph
    {
        public string[] ClaimId { get; set; }
        public string Id { get; set; }
        public string IssuerNumber { get; set; }
        public string ExternalId { get; set; }
        public PolicyGraph Policy { get; set; }
        public PolicyMember PolicyMember { get; set; }
        public EntityGraph Claimant { get; set; }
        public EntityGraph Panel { get; set; }
        public IEnumerable<BenefitClaimGraph> BenefitClaims { get; set; }
        public ClaimValue TotalValues { get; set; }
        public IEnumerable<FactGraph> Facts { get; set; }
        public IEnumerable<TransactionGraph> Transactions { get; set; }
        //public IEnumerable<IncidentGraph> Incidents { get; set; }
        public string Status { get; set; }
        public List<CodeNameGraph> DiagnosisCodes { get; set; }
        public List<CodeNameGraph> OperationCodes { get; set; }
        public EntityGraph Provider { get; set; }
        public LoginGraph RejectedBy { get; set; }
        public List<CodeNameGraph> RejectionCodes { get; set; }
        public string RejectionRemarks { get; set; }
        public List<RejectionReason> RejectionReasons { get; set; }
        public IEnumerable<ClaimEventLogGraph> Events { get; set; }
        public IEnumerable<BenefitGraph> ClaimBalance { get; set; }
        public IEnumerable<NoteGraph> Notes { get; set; }
        public IEnumerable<StakeholderGraph> Stakeholders { get; set; }

        public IEnumerable<AttachmentGraph> Attachments { get; set; }
        public IEnumerable<GuaranteeOfPaymentGraphType.GuaranteeOfPaymentGraph> GuaranteeOfPayments { get; set; }
        public string Fields { get; set; }

        public BatchId ImportBatchId { get; set; }

        public List<BatchId> ExportBatchIds { get; set; }

        public string Remark { get; set; }

        public List<ClaimReversalGraph> Reversals { get; set; }

        public LoginGraph ClaimApprover { get; set; }
        public LoginGraph ClaimHandler { get; set; }

        public AccessPolicy? AccessPolicy { get; set; }

        public static ClaimGraph ToGraph(Claim domain)
        {
            if (domain == null) return null;
            LoginGraph claimHandler = null;
            LoginGraph claimApprover = null;
            try
            {
                claimHandler = domain.Fields?.Type == JTokenType.Array
                    ? null
                    : domain.Fields?["claimHandler"]?.ToObject<LoginGraph>();
            
                claimApprover = domain.Fields?.Type == JTokenType.Array
                    ? null
                    : domain.Fields?["claimApprover"]?.ToObject<LoginGraph>();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            
            return new ClaimGraph
            {
                Id = domain.Id,
                IssuerNumber = domain.IssuerNumber,
                ExternalId = domain.ExternalId,
                ClaimId = domain.ClaimId,
                Claimant = domain.ClaimantId != null ? new EntityGraph {Id = domain.ClaimantId} : null,
                Panel = domain.PanelId != null ? new EntityGraph {Id = domain.PanelId} : null,
                Policy = domain.PolicyId != null ? new PolicyGraph {Id = domain.PolicyId} : null,
                Status = domain.Status,
                DiagnosisCodes = domain.DiagnosisCodes?.Select(c => new CodeNameGraph {Code = c})?.ToList(),
                OperationCodes = domain.OperationCodes?.Select(c => new CodeNameGraph {Code = c})?.ToList(),
                Provider = domain.ProviderId != null ? new EntityGraph {Id = domain.ProviderId} : null,
                BenefitClaims = domain.BenefitClaims?.Select(BenefitClaimGraph.ToGraph),
                TotalValues = new ClaimValue
                {
                    BilledAmount = domain.BenefitClaims.Sum(bc => bc.Values?.Sum(v => v.BilledAmount)),
                    BilledCurrency =
                        domain.BenefitClaims?.FirstOrDefault()?.Values?.FirstOrDefault()?.BilledCurrency,
                    ApprovedAmount = domain.BenefitClaims.Sum(bc => bc.Values?.Sum(v => v.ApprovedAmount)),
                    ApprovedCurrency = domain.BenefitClaims?.FirstOrDefault()?.Values?.FirstOrDefault()
                        ?.ApprovedCurrency,
                },
                Facts = domain.Facts?.Select(FactGraph.ToGraph),
                RejectedBy = domain.RejectedById != null ? new LoginGraph {Id = domain.RejectedById} : null,
                RejectionCodes = domain.RejectionCodes?.Select(c => new CodeNameGraph {Code = c})?.ToList(),
                RejectionRemarks = domain.RejectionRemarks,
                RejectionReasons = domain.RejectionReasons,
                Notes = domain.Notes.Select(NoteGraph.ToGraph),
                Stakeholders = domain.Stakeholders?.Select(s => StakeholderGraph.ToGraph(s)),
                Attachments = domain.Attachments?.Select(x => AttachmentGraph.ToGraph(x)),
                Fields = domain.Fields?.ToString(Formatting.None),
                ImportBatchId = domain.ImportBatchId,
                ExportBatchIds = domain.ExportBatchIds,
                Remark = domain.Remark,
                Reversals = domain.Reversals?.Select(ClaimReversalGraph.ToGraph).ToList(),
                ClaimHandler = claimHandler?.Id != null ? claimHandler : null,
                ClaimApprover = claimApprover?.Id != null ? claimApprover : null,
                AccessPolicy = domain.AccessPolicy
            }.PopulateSystemGraphFields(domain);
            
        }
    }

    public class BatchIdGraphType : ObjectGraphType<BatchId>
    {
        public BatchIdGraphType()
        {
            Name = "batchId";

            Field(bi => bi.Id);

            Field(bi => bi.Name, nullable: true);
        }
    }

    public class ClaimReversalGraphType : ObjectGraphType<ClaimReversalGraph>
    {
        public ClaimReversalGraphType()
        {
            Name = "claimReversal";

            Field(f => f.Reason, nullable: false);

            Field(f => f.ReversedBy, type: typeof(NonNullGraphType<LoginGraphType>), nullable: false);

            Field(f => f.ReversedAt, type: typeof(NonNullGraphType<DateTimeGraphType>), nullable: false);
        }
    }

    public class ClaimReversalGraph
    {
        public string Reason { get; set; }
        public LoginGraph ReversedBy{ get; set; }
        public DateTime ReversedAt { get; set; }

        public static ClaimReversalGraph ToGraph(ClaimReversal claimReversal) =>
            new()
            {
                Reason = claimReversal.Reason,
                ReversedAt = claimReversal.ReversedAt,
                ReversedBy = new Auth.LoginGraph { Id = claimReversal.ReversedById }
            };

    }

    public class BenefitClaimGraph
    {
        public string Id { get; set; }
        public BenefitGraph Benefit { get; set; }
        public string BenefitName { get; set; }
        public string BenefitDescription { get; set; }
        public ScalarValue Value { get; set; }
        public List<ClaimValue> Values { get; set; }
        public ClaimValue TotalValues { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public string Remark { get; set; }

        public static BenefitClaimGraph ToGraph(BenefitClaim domain) =>
            domain == null
            ? null
            : new BenefitClaimGraph
            {
                Id = domain.Id,
                Benefit = domain.BenefitTypeId != null ? new BenefitGraph { TypeId = domain.BenefitTypeId } : null,
                BenefitName = domain.BenefitName,
                BenefitDescription = domain.BenefitDescription,
                Value = domain.Value?.ToScalarValue(),
                Values = domain.Values,
                TotalValues = new ClaimValue
                {
                    BilledAmount = domain.Values?.Sum(v => v.BilledAmount),
                    BilledCurrency = domain.Values?.FirstOrDefault()?.BilledCurrency,
                    ApprovedAmount = domain.Values?.Sum(v => v.ApprovedAmount),
                    ApprovedCurrency = domain.Values?.FirstOrDefault()?.ApprovedCurrency
                },
                CurrencyCode = domain.CurrencyCode,
                Remark = domain.Remark
            };
    }

    public class BenefitClaimGraphType : ObjectGraphType<BenefitClaimGraph>
    {
        public BenefitClaimGraphType()
        {
            Name = "benefitClaim";

            Field(bc => bc.Id, type: typeof(NonNullGraphType<IdGraphType>));
            Field(bc => bc.Benefit, type: typeof(BenefitGraphType));
            Field(bc => bc.BenefitName, nullable: true);
            Field(bc => bc.BenefitDescription, nullable: true);
            Field(bc => bc.Value, type: typeof(ScalarValueGraphType));
            Field(bc => bc.Values, type: typeof(ListGraphType<ClaimValueGraphType>));
            Field(bc => bc.TotalValues, type: typeof(ClaimValueGraphType));
            Field(bc => bc.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(bc => bc.Remark, nullable: true);
        }
    }

    public class CreateClaimInputGraphType : InputObjectGraphType<CreateClaimInputGraph>
    {
        public CreateClaimInputGraphType()
        {
            Name = "createClaimInput";
            Description = "Create claim input";
            
            Field(c => c.Id, type: typeof(IdGraphType), nullable: true);
            Field(c => c.ClaimantId, nullable: true);
            Field(c => c.CreateIncident, nullable: true);
            Field(c => c.PanelId, nullable: true);
            Field(c => c.PolicyId, nullable: true);
            Field(c => c.Status, nullable: true);
            Field(c => c.IssuerNumber, nullable: true);
            Field(c => c.IssuerNumberType, nullable: true);
            Field(c => c.DiagnosisCodes, nullable: true);
            Field(c => c.OperationCodes, nullable: true);
            Field(c => c.ProviderId, nullable: true);
            Field(c => c.Fields, nullable: true);
            Field(c => c.Remark, nullable: true);
            Field(c => c.TempAttachmentFolderKey, nullable: true);
        }
    }

    public class AddBenefitToClaimGraph
    {
        public string BenefitTypeId { get; set; }
        public string BenefitName { get; set; }
        public string BenefitDescription { get; set; }
        public ScalarValue Value { get; set; }
        public List<ClaimValue> Values { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public string Remark { get; set; }
    }

    public class AddBenefitToClaimInputGraphType : InputObjectGraphType<AddBenefitToClaimGraph>
    {
        public AddBenefitToClaimInputGraphType()
        {
            Name = "addBenefitToClaimInput";
            Description = "Adds a benefit to claim";

            Field(b => b.BenefitTypeId, nullable: true);
            Field(b => b.BenefitName, nullable: true);
            Field(b => b.BenefitDescription, nullable: true);
            Field(b => b.Value, type: typeof(ScalarValueInputGraphType));
            Field(b => b.Values, type: typeof(ListGraphType<ClaimValueInputGraphType>));
            Field(b => b.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(b => b.Remark, nullable: true);
        }
    }

    public class UpdateBenefitClaimGraph
    {
        public string BenefitTypeId { get; set; }
        public string BenefitName { get; set; }
        public string BenefitDescription { get; set; }
        public ScalarValue Value { get; set; }
        public List<ClaimValue> Values { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public string Remark { get; set; }
    }

    public class UpdateBenefitClaimInputGraphType : InputObjectGraphType<UpdateBenefitClaimGraph>
    {
        public UpdateBenefitClaimInputGraphType()
        {
            Name = "updateBenefitClaimInput";
            Description = "Updates a benefit of a claim";

            Field(b => b.BenefitTypeId, nullable: true);
            Field(b => b.BenefitName, nullable: true);
            Field(b => b.BenefitDescription, nullable: true);
            Field(b => b.Value, type: typeof(ScalarValueInputGraphType));
            Field(b => b.Values, type: typeof(ListGraphType<ClaimValueInputGraphType>));
            Field(b => b.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(b => b.Remark, nullable: true);
        }
    }

    public class CreateClaimInputGraph
    {
        public Guid? Id { get; set; }
        public bool? CreateIncident { get; set; }
        public string PolicyId { get; set; }
        public string ClaimantId { get; set; }
        public string PanelId { get; set; }
        public string Status { get; set; }
        public string IssuerNumber { get; set; }
        public string IssuerNumberType { get; set; }
        public List<string> DiagnosisCodes { get; set; }
        public List<string> OperationCodes { get; set; }
        public string ProviderId { get; set; }
        public string Fields { get; set; }
        public string Remark { get; set; }
        public string TempAttachmentFolderKey { get; set; }
    }

    public class UpdateClaimInputGraphType : InputObjectGraphType<UpdateClaimCommand>
    {
        public UpdateClaimInputGraphType()
        {
            Name = "updateClaimInput";
            Description = "Update claim input";

            Field(c => c.Status, nullable: true);
            Field(c => c.PolicyId, nullable: true);
            Field(c => c.IssuerNumber, nullable: true);
            Field(c => c.DiagnosisCodes, nullable: true);
            Field(c => c.OperationCodes, nullable: true);
            Field(c => c.ProviderId, nullable: true);
            Field(c => c.Fields, nullable: true);
            Field(c => c.FieldsPatch, nullable: true);
            Field(c => c.Remark, nullable: true);
            Field(c => c.UpdateType, nullable: true);
            Field(c => c.ClaimantId, nullable: true);
            Field(c => c.ExternalId, nullable: true);
        }
    }

    public class ClaimWhereInputGraphType : InputObjectGraphType<ClaimWhere>
    {
        public ClaimWhereInputGraphType()
        {
            Name = "claimWhereInput";
            Description = "A claim search filter";

            Field(f => f.Or, type: typeof(ListGraphType<ClaimWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<ClaimWhereInputGraphType>));

            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);

            Field(f => f.IssuerNumber, nullable: true);
            Field(f => f.IssuerNumber_contains, nullable: true);

            Field(f => f.Status, nullable: true);
            Field(f => f.Fields, type: typeof(FieldsWhereInputGraphType), nullable: true);

            Field(f => f.ClaimantId, nullable: true);
            Field(f => f.ClaimantId_in, nullable: true);
            Field(f => f.Claimant_contains, type: typeof(EntityWhereInputGraphType), nullable: true);

            Field(f => f.PolicyId_in, nullable: true);
            Field(f => f.Policy_contains, type: typeof(PolicyWhereInputGraphType), nullable: true);

            Field(f => f.PanelId, nullable: true);
            Field(f => f.PanelId_exists, nullable: true);
            Field(f => f.PanelId_in, nullable: true);

            Field(f => f.ProviderId, nullable: true);
            Field(f => f.ProviderId_exists, nullable: true);
            Field(f => f.ProviderId_in, nullable: true);

            Field(f => f.ImportBatchId, nullable: true);
            Field(f => f.ImportBatchId_in, nullable: true);
            Field(f => f.ImportBatchIdName, nullable: true);
            Field(f => f.ImportBatchIdName_contains, nullable: true);

            Field(f => f.ExportBatchId, nullable: true);
            Field(f => f.ExportBatchId_exists, nullable: true);
            Field(f => f.ExportedAt_gt, nullable: true);
            Field(f => f.ExportedAt_lt, nullable: true);
            Field(f => f.ExportBatchId_in, nullable: true);
            Field(f => f.ExportBatchIdName, nullable: true);
            Field(f => f.ExportBatchIdName_contains, nullable: true);
            Field(f => f.GuaranteeOfPaymentId, nullable: true);
            Field(f => f.GuaranteeOfPaymentId_in, nullable: true);
            Field(f => f.GuaranteeOfPaymentId_exists, nullable: true);

            Field(f => f.AccessPolicy, nullable: true, typeof(AccessPolicyTypeEnumGraphType));

            this.PopulateSystemWhereFields();
        }
    }

    public class ClaimValueGraphType : ObjectGraphType<ClaimValue>
    {
        public ClaimValueGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService)
        {
            Name = "claimValue";
            Description = "The value of the claim";

            Field(c => c.BilledAmount, nullable: true);
            Field(c => c.BilledCurrency, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(c => c.FormattedBilledAmount, nullable: true).ResolveAsync(context => context.GetFormattedPriceAsync(accessor, l10nService, context.Source.BilledAmount, context.Source.BilledCurrency));
            Field(c => c.ApprovedAmount, nullable: true);
            Field(c => c.ApprovedCurrency, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(c => c.FormattedApprovedAmount, nullable: true).ResolveAsync(context => context.GetFormattedPriceAsync(accessor, l10nService, context.Source.ApprovedAmount, context.Source.ApprovedCurrency));
            Field(c => c.Unit, nullable: true);
        }
    }

    public class ClaimValueInputGraphType : InputObjectGraphType<ClaimValue>
    {
        public ClaimValueInputGraphType()
        {
            Name = "claimValueInput";
            Description = "Input for the value of the claim";

            Field(c => c.BilledAmount, nullable: true);
            Field(c => c.BilledCurrency, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(c => c.ApprovedAmount, nullable: true);
            Field(c => c.ApprovedCurrency, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(c => c.Unit, nullable: true);
        }
    }

    public class ClaimBatchCommandInputGraphType : InputObjectGraphType<ClaimBatchCommand>
    {
        public ClaimBatchCommandInputGraphType()
        {
            Name = "claimBatchInput";

            Field(b => b.CreateClaimCommands, type: typeof(ListGraphType<CreateClaimBatchInputGraphType>));
            Field(b => b.UpdateClaimCommands, type: typeof(ListGraphType<UpdateClaimBatchInputGraphType>));
        }
    }

    public class CreateClaimBatchInputGraphType : InputObjectGraphType<CreateClaimCommand>
    {
        public CreateClaimBatchInputGraphType()
        {
            Name = "createClaimBatchInput";
            Description = "Create Claim Batch Input";

            Field(c => c.Id, type: typeof(IdGraphType), nullable: true);
            Field(c => c.ClaimantId, nullable: true);
            Field(c => c.CreateIncident, nullable: true);
            Field(c => c.PanelId, nullable: true);
            Field(c => c.PolicyId, nullable: true);
            Field(c => c.Status, nullable: true);
            Field(c => c.IssuerNumber, nullable: true);
            Field(c => c.IssuerNumberType, nullable: true);
            Field(c => c.DiagnosisCodes, nullable: true);
            Field(c => c.OperationCodes, nullable: true);
            Field(c => c.ProviderId, nullable: true);
            Field(c => c.Fields, nullable: true);
            Field(c => c.ImportBatchId, type: typeof(BatchIdInputGraphType), nullable: true);
            Field(c => c.TempAttachmentFolderKey, nullable: true);
        }
    }

    public class BatchIdInputGraphType : InputObjectGraphType<BatchId>
    {
        public BatchIdInputGraphType()
        {
            Name = "batchIdInput";

            Field(bi => bi.Id);

            Field(bi => bi.Name, nullable: true);
        }
    }

    public class UpdateClaimBatchInputGraphType : InputObjectGraphType<UpdateClaimCommand>
    {
        public UpdateClaimBatchInputGraphType()
        {
            Name = "updateClaimBatchInput";
            Description = "Update Claim Batch Input";

            Field(c => c.ClaimId);
            Field(c => c.Status, nullable: true);
            Field(c => c.PolicyId, nullable: true);
            Field(c => c.IssuerNumber, nullable: true);
            Field(c => c.DiagnosisCodes, nullable: true);
            Field(c => c.OperationCodes, nullable: true);
            Field(c => c.ProviderId, nullable: true);
            Field(c => c.Fields, nullable: true);
            Field(c => c.FieldsPatch, nullable: true);
            Field(c => c.UpdateType, nullable: true);
            Field(c => c.ImportBatchId, type: typeof(BatchIdInputGraphType), nullable: true);
            Field(c => c.ExportBatchId, type: typeof(BatchIdInputGraphType), nullable: true);
        }
    }

    public class RejectionReasonInputGraphType : AutoInputObjectGraphType<RejectionReason> { }

    public class RejectionReasonGraphType : AutoObjectGraphType<RejectionReason> { }

    public class ReverseClaimInputGraphType : InputObjectGraphType<ReverseClaimCommand>
    {
        public ReverseClaimInputGraphType()
        {
            Name = "reverseClaimInput";
            Description = "reverse a claim";

            Field(b => b.Reason, nullable: false);
            Field(b => b.WithStatus, nullable: false);
        }
    }
}
