using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Claims;
using GraphQL.DataLoader;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Claims
{
    public class ClaimReportsGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<ClaimReportGraph> List { get; set; }
    }
    
    public class ClaimReportsGraphType : ObjectGraphType<ClaimReportsGraph>
    {
        public ClaimReportsGraphType(
            CoverGoClaimReportService claimReportService,
            PermissionValidator permissionValidator)
        {
            Name = "claimReports";
            
            Field(c => c.TotalCount)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var where = await FilterFromContextBuildInternal(permissionValidator, context);
                    return await claimReportService.CountAsync(tenantId, where);
                });

            Field(c => c.List, type: typeof(ListGraphType<ClaimReportGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var where = await FilterFromContextBuildInternal(permissionValidator, context);

                    IReadOnlyCollection<ClaimReport> claimReports =
                        await claimReportService.QueryAsync(tenantId, where);
                    return claimReports.Select(ClaimReportGraph.ToGraph);
                });
        }

        private async Task<ClaimReportWhere> FilterFromContextBuildInternal(
            PermissionValidator permissionValidator,
            ResolveFieldContext<ClaimReportsGraph> context)
        {
            var allowedIds = await permissionValidator.GetTargetIdsFromClaim(
                context, "readClaimReports");


            Filter <ClaimReportFilter> filter =  context.ComputeArgAndVar<Filter<ClaimReportFilter>, ClaimReportsGraph>("where") ?? new Filter<ClaimReportFilter>();
            filter.And ??= new List<Filter<ClaimReportFilter>>();
            filter.And.Add(new Filter<ClaimReportFilter>
            {
                Where = filter.Where
            });
            filter.And.Add(new Filter<ClaimReportFilter>
            {
                Where = new ClaimReportFilter { IsEmpty = false }
            });
                    
            if (!allowedIds.Contains("all"))
            {
                filter.And.Add(new Filter<ClaimReportFilter>
                {
                    Where = new ClaimReportFilter { Id_in = allowedIds.ToList() }
                });
            }
                   
            int? skip = context.ComputeArgAndVar<int?, ClaimReportsGraph>("skip");
            int? first = context.ComputeArgAndVar<int?, ClaimReportsGraph>("limit");
            SortGraph sort = context.ComputeArgAndVar<SortGraph, ClaimReportsGraph>("sort");

            OrderBy orderBy = null;
            if (sort != null)
            {
                orderBy = sort.ToOrderBy();
            }

            return new ClaimReportWhere()
            {
                Skip = skip ?? 0,
                First = first ?? 25,
                OrderBy = orderBy,
                Where = filter
            };
        }
    }
    
    public class ClaimReportGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public List<ClaimSnapshotGraph> ClaimSnapshots { get; set; }
        public DateTime ReportDate { get; set; }
        
        public static ClaimReportGraph ToGraph(ClaimReport domain)
        {
            if (domain == null)
                return null;

            return new ClaimReportGraph
            {
                Id = domain.Id,
                Name =  domain.Name,
                ClaimSnapshots = domain.ClaimSnapshots?.Select(ClaimSnapshotGraph.ToGraph).ToList() ?? new List<ClaimSnapshotGraph>(),
                ReportDate = domain.ReportDate
            }.PopulateSystemGraphFields(domain);
        }
    }

    public class ClaimReportGraphType : ObjectGraphType<ClaimReportGraph>
    {
        public ClaimReportGraphType(IDataLoaderContextAccessor accessor,
            IAuthService authService,
            CoverGoClaimSnapshotService claimSnapshotService,
            PermissionValidator permissionValidator)
        {
            Name = "claimReport";
            Description = "claimReport";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
            Field(c => c.Id, nullable: true);
            Field(c => c.Name, nullable: true);
            Field(c => c.ClaimSnapshots, nullable: true, typeof(ListGraphType<ClaimSnapshotGraphType>))
                .Argument<ClaimSnapshotFilterInputGraph>("where", "A claim snapshot search filter")
                .ResolveAsync(async context =>
                {
                    if (context.Source.Id == null)
                        return null;

                    var allowedIds = await permissionValidator.GetTargetIdsFromClaim(
                        context, "readClaimReports");

                    Filter <ClaimSnapshotFilter> filter =  context.ComputeArgAndVar<Filter<ClaimSnapshotFilter>, ClaimReportGraph>("where") ?? new Filter<ClaimSnapshotFilter>();
                    filter.And ??= new List<Filter<ClaimSnapshotFilter>>();
                    filter.And.Add(new Filter<ClaimSnapshotFilter>
                    {
                        Where = filter.Where
                    });
                    
                    if (!allowedIds.Contains("all"))
                    {
                        filter.And.Add(new Filter<ClaimSnapshotFilter>
                        {
                            Where = new ClaimSnapshotFilter { ClaimReportId_in = allowedIds.ToList() }
                        });
                    }
                    
                    string tenantId = context.GetTenantIdFromToken();

                    var loader = accessor.Context.GetOrAddCollectionBatchLoader<string, ClaimSnapshot>("GetClaimSnapshots",
                        async ids =>
                        {
                            filter.And.Add(new Filter<ClaimSnapshotFilter>
                            {
                                Where = new ClaimSnapshotFilter { ClaimReportId_in = ids }
                            });
                            return (await claimSnapshotService.QueryAsync(tenantId,
                                new ClaimSnapshotWhere() {Where = filter})).ToLookup(x => x.ClaimReportId);
                        });

                    IEnumerable<ClaimSnapshot> claimSnapshots = await loader.LoadAsync(context.Source.Id);

                    return claimSnapshots.Select(ClaimSnapshotGraph.ToGraph).ToList();
                });;
            Field(c => c.ReportDate, nullable: true, typeof(DateTimeGraphType));
        }
    }

    public class ClaimSnapshotGraphType : ObjectGraphType<ClaimSnapshotGraph>
    {
        public ClaimSnapshotGraphType()
        {
            Name = "claimSnapshot";
            Description = "A claim snapshot at the time when we generate the claim report";
            
            Field(c => c.Id, nullable: true);
            Field(c => c.ClaimId, nullable: true);
            Field(c => c.PolicyNo, nullable: true);
            Field(c => c.ClaimType, nullable: true);
            Field(c => c.ClaimNo, nullable: true);
            Field(c => c.IncurredDate, nullable: true, typeof(DateTimeGraphType));
            Field(c => c.FormReceivedDate, nullable: true, typeof(DateTimeGraphType));
            Field(c => c.ClaimHandler, nullable: true);
            Field(c => c.ClaimApprover, nullable: true);
            Field(c => c.SettlementDate, nullable: true, typeof(DateTimeGraphType));
            Field(c => c.SettlementFollowingWorkingDate, nullable: true, typeof(DateTimeGraphType));
            Field(c => c.ClaimantName, nullable: true);
            Field(c => c.PolicyHolder, nullable: true);
            Field(c => c.ClaimantInternalCode, nullable: true);
            Field(c => c.InvoiceNo, nullable: true);
            Field(c => c.PanelName, nullable: true);
            Field(c => c.TotalIncurredAmount, nullable: true);
            Field(c => c.TotalAmountToPay, nullable: true);
            Field(c => c.ApprovedDate, nullable: true, typeof(DateTimeGraphType));
            Field(c => c.CreatedAt, nullable: true, typeof(DateTimeGraphType));
            Field(c => c.ClaimSettlementMode, nullable: true);
            Field(c => c.EmployeeName, nullable: true);
            Field(c => c.EmployeeInternalCode, nullable: true);
            Field(c => c.ExportBatchNumber, nullable: true);
            Field(c => c.CompanyName, nullable: true);
            Field(c => c.CompanyBankAccount, nullable: true);
            Field(c => c.CompanyBankCode, nullable: true);
            Field(c => c.EmployeeBankAccount, nullable: true);
            Field(c => c.EmployeeBankCode, nullable: true);
            Field(c => c.PanelBankCode, nullable: true);
            Field(c => c.PanelBankAccount, nullable: true);
            Field(c => c.IsPanel, nullable: true);
            Field(c => c.Status, nullable: true);
            Field(c => c.TotalBilledAmount, nullable: true);
            Field(c => c.HasReversal, nullable: true);
            Field(c => c.PanelAccountHolder, nullable: true);
            Field(c => c.PanelBranchCode, nullable: true);
            Field(c => c.PanelBankName, nullable: true);
            Field(c => c.PanelP400ClientNumber, nullable: true);
            Field(c => c.ProductType, nullable: true);
            Field(c => c.HospitalName, nullable: true);
            Field(c => c.RoomType, nullable: true);
            Field(c => c.HospitalizationDays, nullable: true);
            Field(c => c.ClaimantAge, nullable: true);
            Field(c => c.ClaimantGender, nullable: true);
            Field(c => c.SecondaryClaimIndicator, nullable: true);
            Field(c => c.DiagnosisCodes, nullable: true);
            Field(c => c.OperationCodes, nullable: true);
            Field(c => c.BillingDetail, nullable: true, typeof(ListGraphType<BillingDetailGraphType>));
        }
    }

    public class ClaimSnapshotGraph
    {
        public string Id { get; set; }
        public string ClaimId { get; set; }
        public string PolicyNo { get; set; }
        public string ClaimType { get; set; }
        public string ClaimNo { get; set; }
        public DateTime? IncurredDate { get; set; }
        public DateTime? FormReceivedDate { get; set; }
        public string ClaimHandler { get; set; }
        public string ClaimApprover { get; set; }
        public DateTime? SettlementDate { get; set; }
        public DateTime? SettlementFollowingWorkingDate { get; set; }
        public string ClaimantName { get; set; }
        public string PolicyHolder { get; set; }
        public string ClaimantInternalCode { get; set; }
        public string InvoiceNo { get; set; }
        public string PanelName { get; set; }
        public decimal TotalIncurredAmount { get; set; }
        public decimal TotalAmountToPay { get; set; }
        public DateTime? ApprovedDate { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsPanel { get; set; }
        public string ClaimSettlementMode { get; set; }
        public string EmployeeName { get; set; }
        public string EmployeeInternalCode { get; set; }
        public string ExportBatchNumber { get; set; }
        public string CompanyName { get; set; }
        public string CompanyBankAccount { get; set; }
        public string CompanyBankCode { get; set; }
        public string EmployeeBankAccount { get; set; }
        public string EmployeeBankCode { get; set; }
        public string PanelBankCode { get; set; }
        public string PanelBankAccount { get; set; }
        public string Status { get; set; }
        public decimal TotalBilledAmount { get; set; }
        public bool HasReversal { get; set; }
        public string PanelAccountHolder { get; set; }
        public string PanelBranchCode { get; set; }
        public string PanelBankName { get; set; }
        public string PanelP400ClientNumber { get; set; }
        public string ProductType { get; set; }
        public string HospitalName { get; set; }
        public string RoomType { get; set; }
        public decimal HospitalizationDays { get; set; }
        public int ClaimantAge { get; set; }
        public string ClaimantGender { get; set; }
        public bool? SecondaryClaimIndicator { get; set; }
        public List<string> DiagnosisCodes { get; set; }
        public List<string> OperationCodes { get; set; }
        public List<BillingDetailItemGraph> BillingDetail { get; set; }

        public static ClaimSnapshotGraph ToGraph(ClaimSnapshot domain)
        {
            if (domain == null)
                return null;

            return new ClaimSnapshotGraph
            {
                Id = domain.Id,
                ClaimId = domain.ClaimId,
                PolicyNo = domain.PolicyNo,
                ClaimType = domain.ClaimType,
                ClaimNo = domain.ClaimNo,
                IncurredDate = domain.IncurredDate,
                FormReceivedDate = domain.FormReceivedDate,
                ClaimHandler = domain.ClaimHandler,
                ClaimApprover = domain.ClaimApprover,
                SettlementDate = domain.SettlementDate,
                SettlementFollowingWorkingDate = domain.SettlementFollowingWorkingDate,
                ClaimantName = domain.ClaimantName,
                IsPanel = domain.IsPanel,
                PolicyHolder = domain.PolicyHolder,
                InvoiceNo = domain.InvoiceNo,
                PanelName = domain.PanelName,
                TotalIncurredAmount = domain.TotalIncurredAmount,
                TotalAmountToPay = domain.TotalAmountToPay,
                ApprovedDate = domain.ApprovedDate,
                CreatedAt = domain.CreatedAt,
                ClaimSettlementMode = domain.ClaimSettlementMode,
                EmployeeName = domain.EmployeeName,
                EmployeeInternalCode = domain.EmployeeInternalCode,
                ExportBatchNumber = domain.ExportBatchNumber,
                CompanyName = domain.CompanyName,
                CompanyBankAccount = domain.CompanyBankAccount,
                CompanyBankCode = domain.CompanyBankCode,
                EmployeeBankAccount = domain.EmployeeBankAccount,
                EmployeeBankCode = domain.EmployeeBankCode,
                PanelBankCode = domain.PanelBankCode,
                PanelBankAccount = domain.PanelBankAccount,
                ClaimantInternalCode = domain.ClaimantInternalCode,
                Status = domain.Status,
                TotalBilledAmount = domain.TotalBilledAmount,
                HasReversal = domain.HasReversal,
                PanelAccountHolder = domain.PanelAccountHolder,
                PanelBranchCode = domain.PanelBranchCode,
                PanelBankName = domain.PanelBankName,
                PanelP400ClientNumber = domain.PanelP400ClientNumber,
                ProductType = domain.ProductType,
                HospitalName = domain.HospitalName,
                RoomType = domain.RoomType,
                HospitalizationDays = domain.HospitalizationDays,
                ClaimantAge = domain.ClaimantAge,
                ClaimantGender = domain.ClaimantGender,
                SecondaryClaimIndicator = domain.SecondaryClaimIndicator,
                DiagnosisCodes = domain.DiagnosisCodes,
                OperationCodes = domain.OperationCodes,
                BillingDetail = domain.BillingDetail?.ConvertAll(BillingDetailItemGraph.ToGraph) ?? new List<BillingDetailItemGraph>()
            };
        }
    }

    public class BillingDetailItemGraph
    {
        public string BenefitId { get; set; }
        public decimal IncurredAmount { get; set; }
        public decimal BilledAmount { get; set; }

        public static BillingDetailItemGraph ToGraph(BillingDetailItem domain)
        {
            if (domain == null)
                return null;

            return new BillingDetailItemGraph
            {
                BenefitId = domain.BenefitId,
                IncurredAmount = domain.IncurredAmount,
                BilledAmount = domain.BilledAmount
            };
        }
    }

    public class BillingDetailGraphType : ObjectGraphType<BillingDetailItemGraph>
    {
        public BillingDetailGraphType()
        {
            Name = "billingDetail";
            Description = "Billing Detail";

            Field(x => x.BenefitId, true);
            Field(x => x.IncurredAmount, true);
            Field(x => x.BilledAmount, true);
        }
    }

    public class ClaimReportFilterInputGraph : InputObjectGraphType<Filter<ClaimReportFilter>>
    {
        public ClaimReportFilterInputGraph()
        {
            Name = "claimReportFilterInput";
            Description = "ClaimReport Filter Input";

            Field(x => x.And, true, typeof(ListGraphType<ClaimReportFilterInputGraph>));
            Field(x => x.Or, true, typeof(ListGraphType<ClaimReportFilterInputGraph>));
            Field(x => x.Where, true, typeof(ClaimReportFilterWhereInputGraph));
        }
    }
    
    public class ClaimReportFilterWhereInputGraph : InputObjectGraphType<ClaimReportFilter>
    {
        public ClaimReportFilterWhereInputGraph()
        {
            Name = "claimReportFilterWhereInput";
            Description = "ClaimReport Filter Where Input";

            Field(x => x.Id, true);
            Field(x => x.Id_in, true);
            Field(x => x.Name, true);
            Field(x => x.Name_in, true);
            Field(x => x.ReportDate, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.ReportDate_gt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.ReportDate_lt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.LastModifiedAt_gt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.LastModifiedAt_lt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.CreatedAt_gt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.CreatedAt_lt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.ClaimSnapshots_IsPanel, true);
            Field(x => x.ClaimSnapshots_HasReversal, true);
            Field(x => x.ClaimSnapshots_ClaimSettmentMode, true);
            Field(x => x.ClaimSnapshots_ClaimSettmentMode_in, true);
            Field(x => x.ClaimSnapshots_PolicyHolderId_In, true);
            Field(x => x.ClaimSnapshots_SettlementDate_gt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.ClaimSnapshots_SettlementDate_gte, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.ClaimSnapshots_SettlementDate_lt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.ClaimSnapshots_SettlementDate_lte, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.Status, true);
        }
    }
    
    public class ClaimSnapshotFilterInputGraph : InputObjectGraphType<Filter<ClaimSnapshotFilter>>
    {
        public ClaimSnapshotFilterInputGraph()
        {
            Name = "claimSnapshotFilterInput";
            Description = "ClaimSnapshot Filter Input";

            Field(x => x.And, true, typeof(ListGraphType<ClaimSnapshotFilterInputGraph>));
            Field(x => x.Or, true, typeof(ListGraphType<ClaimSnapshotFilterInputGraph>));
            Field(x => x.Where, true, typeof(ClaimSnapshotFilterWhereInputGraph));
        }
    }
    
    public class ClaimSnapshotFilterWhereInputGraph : InputObjectGraphType<ClaimSnapshotFilter>
    {
        public ClaimSnapshotFilterWhereInputGraph()
        {
            Name = "claimSnapshotFilterWhereInput";
            Description = "ClaimSnapshot Filter Where Input";

            Field(x => x.Id, true);
            Field(x => x.ClaimReportId, true);
            Field(x => x.ClaimId, true);
            Field(x => x.PolicyNo, true);
            Field(x => x.ClaimType, true);
            Field(x => x.ClaimNo, true);
            Field(x => x.ClaimHandler, true);
            Field(x => x.ClaimApprover, true);
            Field(x => x.ClaimantName, true);
            Field(x => x.PolicyHolder, true);
            Field(x => x.ClaimantInternalCode, true);
            Field(x => x.InvoiceNo, true);
            Field(x => x.PanelName, true);
            Field(x => x.ClaimSettlementMode, true);
            Field(x => x.EmployeeName, true);
            Field(x => x.EmployeeInternalCode, true);
            Field(x => x.ExportBatchNumber, true);
            Field(x => x.CompanyName, true);
            Field(x => x.CompanyBankAccount, true);
            Field(x => x.CompanyBankCode, true);
            Field(x => x.EmployeeBankAccount, true);
            Field(x => x.EmployeeBankCode, true);
            Field(x => x.PanelBankCode, true);
            Field(x => x.PanelBankAccount, true);
            Field(x => x.Status, true);
            Field(x => x.CreatedAt_lte, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.CreatedAt_gte, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.CreatedAt_gt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.CreatedAt_lt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.IncurredDate_gt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.IncurredDate_gte, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.IncurredDate_lt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.IncurredDate_lte, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.FormReceivedDate_gt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.FormReceivedDate_gte, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.FormReceivedDate_lt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.FormReceivedDate_lte, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.SettlementDate_gt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.SettlementDate_gte, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.SettlementDate_lt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.SettlementDate_lte, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.SettlementFollowingWorkingDate_gt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.SettlementFollowingWorkingDate_gte, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.SettlementFollowingWorkingDate_lt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.SettlementFollowingWorkingDate_lte, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.ApprovedDate_gt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.ApprovedDate_gte, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.ApprovedDate_lt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.ApprovedDate_lte, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.IsPanel, true);
            Field(x => x.Status_in, true, typeof(ListGraphType<StringGraphType>));
            Field(x => x.ClaimReportId_in, true);
            Field(x => x.ExportBatchNumber_contains, true);
            Field(x => x.HasReversal, true);
            Field(x => x.PanelBranchCode, true);
            Field(x => x.PanelBankName, true);
            Field(x => x.PanelP400ClientNumber, true);
        }
    }
}