using System.Collections.Generic;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Claims;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Claims
{
    public class Remarks
    {
        public long Count { get; set; }

        public IEnumerable<Remark> List { get; set; }
    }

    public class RemarksGraphType : ObjectGraphType<Remarks>
    {
        public RemarksGraphType(IClaimRemarksService service)
        {
            Name = "remarks";
            Description = "Remarks";

            Field(x => x.Count, nullable: true).ResolveAsync(context =>
            {
                string tenantId = context.GetTenantIdFromToken();
                RemarkWhere where = context.ComputeArgAndVar<RemarkWhere, Remarks>("where");
                return service.GetTotalCountAsync(tenantId, where);
            });

            Field(x => x.List, nullable: true, type: typeof(ListGraphType<RemarkGraphType>)).ResolveAsync(context =>
            {
                string tenantId = context.GetTenantIdFromToken();
                RemarkWhere where = context.ComputeArgAndVar<RemarkWhere, Remarks>("where");

                int? skip = context.ComputeArgAndVar<int?, Remarks>("skip");
                int? limit = context.ComputeArgAndVar<int?, Remarks>("limit");
                SortGraph sort = context.ComputeArgAndVar<SortGraph, Remarks>("sort");
                OrderBy orderBy = sort?.ToOrderBy();

                return service.GetAsync(tenantId, new QueryArguments<RemarkWhere>
                {
                    Where = where,
                    OrderBy = orderBy,
                    Skip = skip,
                    First = limit
                });
            });
        }
    }

    public class RemarkUpsertGraphType : InputObjectGraphType<RemarkUpsert>
    {
        public RemarkUpsertGraphType()
        {
            Name = "remarkUpsert";
            Description = "Remark upsert model";

            Field(x => x.Id, nullable: false);
            Field(x => x.RemarkFull, nullable: true);
            Field(x => x.RemarkShort, nullable: true);
            Field(x => x.Fields, nullable: true);
        }
    }

    public class RemarkGraphType : ObjectGraphType<Remark>
    {
        public RemarkGraphType()
        {
            Name = "remark";
            Description = "remark for a claim";

            Field(x => x.Id, nullable: false);
            Field(x => x.RemarkFull);
            Field(x => x.RemarkShort);
            Field<StringGraphType>().Name("fields").Resolve(x => x.Source.Fields?.ToString());
        }
    }


    public class RemarkWhereGraphType : InputObjectGraphType<RemarkWhere>
    {
        public RemarkWhereGraphType()
        {
            Name = "remarkWhere";
            Description = "Claim remarks filter";

            Field(x => x.And, nullable: true, type: typeof(ListGraphType<RemarkWhereGraphType>));
            Field(x => x.Or, nullable: true, type: typeof(ListGraphType<RemarkWhereGraphType>));

            Field(x => x.RemarkFull_contains, nullable: true);
            Field(x => x.RemarkShort_contains, nullable: true);

            Field(x => x.Fields, nullable: true, type: typeof(FieldsWhereInputGraphType));
            Field(x => x.Id, nullable: true);
            Field(x => x.Id_in, nullable: true, type: typeof(ListGraphType<StringGraphType>));
        }
    }
}
