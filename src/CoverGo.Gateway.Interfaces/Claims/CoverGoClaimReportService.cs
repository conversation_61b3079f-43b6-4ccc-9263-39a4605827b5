using System.Net.Http;
using CoverGo.Applications.Clients;
using CoverGo.Gateway.Domain.Claims;

namespace CoverGo.Gateway.Interfaces.Claims
{
    public class CoverGoClaimReportService: CoverGoGenericGenericServiceRestClientBase<ClaimReport,
        Create<PERSON>laimReportCommand, UpdateClaimReportCommand, RemoveClaimReportCommand, ClaimReportBatchCommand, ClaimReportWhere,
        ClaimReportFilter>
    {
        private readonly HttpClient _client;

        public CoverGoClaimReportService(HttpClient client) : base(client)
        {
            _client = client;
        }

        protected override string ApiBaseUrlBuildInternal(string tenantId) => 
            $"{tenantId}/api/v2/claimReports";
    }
}