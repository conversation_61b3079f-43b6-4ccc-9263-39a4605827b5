using System.Net.Http;
using CoverGo.Applications.Clients;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Claims;

namespace CoverGo.Gateway.Interfaces.Claims
{
    public class CoverGoClaimSnapshotService: CoverGoGenericGenericServiceRestClientBase<ClaimSnapshot,
        CreateClaimSnapshotCommand, UpdateClaimSnapshotCommand, RemoveCommand, ClaimSnapshotBatchCommand, ClaimSnapshotWhere,
        ClaimSnapshotFilter>
    {
        private readonly HttpClient _client;

        public CoverGoClaimSnapshotService(HttpClient client) : base(client)
        {
            _client = client;
        }

        protected override string ApiBaseUrlBuildInternal(string tenantId) => 
            $"{tenantId}/api/v2/claimSnapshots";
    }
}