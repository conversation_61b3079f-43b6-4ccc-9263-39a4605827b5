using System.Collections.Generic;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Interfaces.Commissions;
using CoverGo.Gateway.Interfaces.Templates;
using GraphQL.Authorization;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeTemplatesMutations(ITemplateService templateService, PermissionValidator permissionValidator)
        {
            Field<ResultGraphType>()
                .Name("createPdfDrawingTemplate")
                .Description("Creates a PDF Drawing template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreatePdfDrawingTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("createTemplates", "writeTemplates"));
                    CreatePdfDrawingTemplateCommand command = context.GetArgument<CreatePdfDrawingTemplateCommand>("input");
                    command.CreatedById = context.GetLoginIdFromToken();

                    Result result = await templateService.CreatePdfDrawingTemplateAsync(tenantId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updatePdfDrawingTemplate")
                .Description("Updates a PDF Drawing template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id to update")
                .Argument<NonNullGraphType<CreatePdfDrawingTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));

                    UpdatePdfDrawingTemplateCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdatePdfDrawingTemplateCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await templateService.UpdatePdfDrawingTemplateAsync(tenantId, templateId, command);
                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createWkhtmltopdfTemplate")
                .Description("Creates a wkhtmltopdf template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateWkhtmltopdfTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("createTemplates", "writeTemplates"));
                    CreateWkhtmltopdfTemplateCommand command = context.GetArgument<CreateWkhtmltopdfTemplateCommand>("input");
                    command.CreatedById = context.GetLoginIdFromToken();
                    var result = await templateService.CreateWkhtmltopdfTemplateAsync(tenantId, command);
                    
                    return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
                });

            Field<ResultGraphType>()
                .Name("updateWkhtmltopdfTemplate")
                .Description("Updates a wkhtmltopdf template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id to update")
                .Argument<NonNullGraphType<UpdateWkhtmltopdfTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));

                    UpdateWkhtmltopdfTemplateCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateWkhtmltopdfTemplateCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await templateService.UpdateWkhtmltopdfTemplateAsync(tenantId, templateId, command);
                    return result;
                });

            Field<StringResultGraphType>()
                .Name("addPageObjectToWkhtmltopdfTemplate")
                .Description("Adds a page object with settings to a wkhtmltopdf template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id")
                .Argument<NonNullGraphType<AddPageObjectInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates"));

                    AddPageObjectCommand command = context.GetArgument<AddPageObjectCommand>("input");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result<string> result = await templateService.AddPageObjectAsync(tenantId, templateId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updatePageObjectOfWkhtmltopdfTemplate")
                .Description("Updates a page object with settings of a wkhtmltopdf template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id")
                .Argument<NonNullGraphType<UpdatePageObjectInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates"));

                    UpdatePageObjectCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdatePageObjectCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await templateService.UpdatePageObjectAsync(tenantId, templateId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removePageObjectFromWkhtmltopdfTemplate")
                .Description("Removes a page object with settings from a wkhtmltopdf template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id")
                .Argument<NonNullGraphType<StringGraphType>>("pageObjectId", "The page object id to remove")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    string pageObjectId = context.GetArgument<string>("pageObjectId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates"));

                    RemovePageObjectCommand command = new()
                    {
                        RemovedById = context.GetLoginIdFromToken(), Id = pageObjectId
                    };

                    Result result = await templateService.RemovePageObjectAsync(tenantId, templateId, command);
                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createClauseHtmlTemplate")
                .Description("Creates a clauseHtml template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateClauseHtmlTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("createTemplates", "writeTemplates"));
                    CreateClauseHtmlTemplateCommand command = context.GetArgument<CreateClauseHtmlTemplateCommand>("input");
                    command.CreatedById = context.GetLoginIdFromToken();

                    Result<CreatedStatus> result = await templateService.CreateClauseHtmlTemplateAsync(tenantId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateClauseHtmlTemplate")
                .Description("Updates a clauseHtml template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id to update")
                .Argument<NonNullGraphType<UpdateClauseHtmlTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));

                    UpdateClauseHtmlTemplateCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateClauseHtmlTemplateCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await templateService.UpdateClauseHtmlTemplateAsync(tenantId, templateId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("createSmsTemplate")
                .Description("Creates an Sms template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateSmsTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("createTemplates", "writeTemplates"));
                    CreateSmsTemplateCommand command = context.GetArgument<CreateSmsTemplateCommand>("input");
                    command.CreatedById = context.GetLoginIdFromToken();

                    Result result = await templateService.CreateSmsTemplateAsync(tenantId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateSmsTemplate")
                .Description("Updates a Sms template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id to update")
                .Argument<NonNullGraphType<UpdateSmsTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));

                    UpdateSmsTemplateCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateSmsTemplateCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await templateService.UpdateSmsTemplateAsync(tenantId, templateId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("createEmailMjmlTemplate")
                .Description("Creates an email MJML template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateEmailMjmlTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("createTemplates", "writeTemplates"));
                    CreateEmailMjmlTemplateCommand command = context.GetArgument<CreateEmailMjmlTemplateCommand>("input");
                    command.CreatedById = context.GetLoginIdFromToken();

                    Result result = await templateService.CreateEmailMjmlTemplateAsync(tenantId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateEmailMjmlTemplate")
                .Description("Updates a email MJML template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id to update")
                .Argument<NonNullGraphType<UpdateEmailMjmlTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));

                    UpdateEmailMjmlTemplateCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateEmailMjmlTemplateCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await templateService.UpdateEmailMjmlTemplateAsync(tenantId, templateId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("deleteTemplate")
                .Description("Deletes a PDF Drawing template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id to update")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("deleteTemplates", "writeTemplates").WithTargetIds(templateId));

                    Result result = await templateService.DeleteTemplateAsync(tenantId, templateId, new DeleteTemplateCommand
                    {
                        DeletedById = context.GetLoginIdFromToken()
                    });

                    return result;
                });

            Field<StringResultGraphType>()
                .Name("addPdfDrawing")
                .Description("Adds a drawing to a pdf template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id to update")
                .Argument<NonNullGraphType<AddPdfDrawingInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));

                    AddPdfDrawingCommand command = context.GetArgument<AddPdfDrawingCommand>("input");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result<string> result = await templateService.AddPdfDrawingAsync(tenantId, templateId, command);
                    return result;
                });


            Field<ResultGraphType>()
                .Name("updatePdfDrawing")
                .Description("Updates a drawing of a pdf template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id to update")
                .Argument<NonNullGraphType<StringGraphType>>("drawingId", "The drawing to update")
                .Argument<NonNullGraphType<UpdatePdfDrawingInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));
                    string drawingId = context.GetArgument<string>("drawingId");

                    UpdatePdfDrawingCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdatePdfDrawingCommand>();
                    command.DrawingId = drawingId;
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await templateService.UpdatePdfDrawingAsync(tenantId, templateId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removePdfDrawing")
                .Description("Removes a drawing from a pdf template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id to update")
                .Argument<NonNullGraphType<StringGraphType>>("drawingId", "The drawing to remove")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));
                    string drawingId = context.GetArgument<string>("drawingId");

                    var command = new RemovePdfDrawingCommand { DrawingId = drawingId, RemovedById = context.GetLoginIdFromToken() };
                    Result result = await templateService.RemovePdfDrawingAsync(tenantId, templateId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("addEmailAttachmentTemplate")
                .Description("Adds an email attachment template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The email template id on which to attach the attachment template")
                .Argument<NonNullGraphType<AddEmailAttachmentTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));

                    AddEmailAttachmentTemplateCommand command = context.GetArgument<AddEmailAttachmentTemplateCommand>("input");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result result = await templateService.AddEmailAttachmentTemplateAsync(tenantId, templateId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateEmailAttachmentTemplate")
                .Description("Updates an email attachment template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The email template id on which to update the attachment template")
                .Argument<NonNullGraphType<UpdateEmailAttachmentTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));

                    UpdateEmailAttachmentTemplateCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateEmailAttachmentTemplateCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await templateService.UpdateEmailAttachmentTemplateAsync(tenantId, templateId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeEmailAttachmentTemplate")
                .Description("Removes an email attachment template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "")
                .Argument<NonNullGraphType<StringGraphType>>("attachmentTemplateId", "")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));
                    string attachmentTemplateId = context.GetArgument<string>("attachmentTemplateId");

                    var command = new RemoveEmailAttachmentTemplateCommand
                    {
                        AttachmentTemplateId = attachmentTemplateId,
                        RemovedById = context.GetClientIdFromToken()
                    };

                    Result result = await templateService.RemoveEmailAttachmentTemplateAsync(tenantId, templateId, command);
                    return result;
                });

            Field<ResultGraphType>()
               .Name("addEmailAttachmentReference")
               .Description("Adds an email attachment reference")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<StringGraphType>>("templateId", "The email template id on which to attach the attachment reference")
               .Argument<NonNullGraphType<AddEmailAttachmentReferenceInputGraphType>>("input", "The input")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string templateId = context.GetArgument<string>("templateId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));

                   AddEmailAttachmentReferenceCommand command = context.GetArgument<AddEmailAttachmentReferenceCommand>("input");
                   command.AddedById = context.GetLoginIdFromToken();

                   Result result = await templateService.AddEmailAttachmentReferenceAsync(tenantId, templateId, command);
                   return result;
               });

            Field<ResultGraphType>()
                .Name("updateEmailAttachmentReference")
                .Description("Updates an email attachment template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The email template id on which to update the attachment reference")
                 .Argument<NonNullGraphType<StringGraphType>>("attachmentReferenceId", "The attachment reference id on which to update")
                .Argument<NonNullGraphType<UpdateEmailAttachmentReferenceInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));

                    UpdateEmailAttachmentReferenceCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateEmailAttachmentReferenceCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await templateService.UpdateEmailAttachmentReferenceAsync(tenantId, templateId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeEmailAttachmentReference")
                .Description("Removes an email attachment template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "the template to remove an attachment reference from")
                .Argument<NonNullGraphType<StringGraphType>>("attachmentReferenceId", "The attachment reference id to remove")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));
                    string attachmentReferenceId = context.GetArgument<string>("attachmentReferenceId");

                    var command = new RemoveCommand
                    {
                        Id = attachmentReferenceId,
                        RemovedById = context.GetClientIdFromToken()
                    };

                    Result result = await templateService.RemoveEmailAttachmentReferenceAsync(tenantId, templateId, command);
                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createDynamicTemplate")
                .Description("Creates a dynamic template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateDynamicTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("createTemplates", "writeTemplates"));
                    CreateDynamicTemplateCommand command = context.GetArgument<CreateDynamicTemplateCommand>("input");
                    command.CreatedById = context.GetLoginIdFromToken();

                    Result<CreatedStatus> result = await templateService.CreateDynamicTemplateAsync(tenantId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateDynamicTemplate")
                .Description("Updates a dynamic template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id to update")
                .Argument<NonNullGraphType<UpdateDynamicTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));

                    UpdateDynamicTemplateCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateDynamicTemplateCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await templateService.UpdateDynamicTemplateAsync(tenantId, templateId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("addDynamicValueToTemplate")
                .Description("Adds a dynamic value to a dynamic template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id to update")
                .Argument<NonNullGraphType<AddDynamicValueInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));

                    DynamicValueGraph input = context.GetArgument<DynamicValueGraph>("input");
                    AddDynamicValueCommand command = DynamicValueGraph.ToAddCommand(input, context.GetLoginIdFromToken());

                    Result result = await templateService.AddDynamicValueAsync(tenantId, templateId, command);
                    return result;
                });


            Field<ResultGraphType>()
                .Name("updateDynamicValueOfTemplate")
                .Description("Updates a dynamic value of a dynamic template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id to update")
                .Argument<NonNullGraphType<StringGraphType>>("valueId", "The value to update")
                .Argument<NonNullGraphType<UpdateDynamicValueInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));
                    string valueId = context.GetArgument<string>("valueId");

                    DynamicValueGraph input = context.GetArgument<DynamicValueGraph>("input");
                    UpdateDynamicValueCommand command = DynamicValueGraph.ToUpdateCommand(valueId, input, context.GetLoginIdFromToken());

                    Result result = await templateService.UpdateDynamicValueAsync(tenantId, templateId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeDynamicValueFromTemplate")
                .Description("Removes a dynamic value from a pdf template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id to update")
                .Argument<NonNullGraphType<StringGraphType>>("valueId", "The value to remove")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));
                    string valueId = context.GetArgument<string>("valueId");

                    var command = new RemoveDynamicValueCommand { ValueId = valueId, RemovedById = context.GetLoginIdFromToken() };
                    Result result = await templateService.RemoveDynamicValueAsync(tenantId, templateId, command);
                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createFunctionTemplate")
                .Description("creates a function template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateFunctionTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("createTemplates", "writeTemplates"));
                    CreateFunctionTemplateCommand command = context.GetArgument<CreateFunctionTemplateCommand>("input");
                    command.CreatedById = context.GetLoginIdFromToken();

                    Result<CreatedStatus> result = await templateService.CreateFunctionTemplateAsync(tenantId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateFunctionTemplate")
                .Description("updates a function template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the template identifier")
                .Argument<NonNullGraphType<UpdateFunctionTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates"));
                    string templateId = context.GetArgument<string>("id");
                    UpdateFunctionTemplateCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateFunctionTemplateCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await templateService.UpdateFunctionTemplateAsync(tenantId, templateId, command);
                    return result;
                });

            Field<CreatedStatusResultGraphType>()
               .Name("addInputToFunctionTemplate")
               .Description("adds an input to a function template")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<StringGraphType>>("id", "the template identifier")
               .Argument<NonNullGraphType<FunctionInputInputGraphType>>("input", "The input")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates"));
                   string templateId = context.GetArgument<string>("id");
                   AddFunctionInputCommand command = context.GetArgument<AddFunctionInputCommand>("input");

                   command.AddedById = context.GetLoginIdFromToken();

                   return await templateService.AddInputToFunctionTemplateAsync(tenantId, templateId, command);
               });

            Field<ResultGraphType>()
               .Name("updateInputOfFunctionTemplate")
               .Description("updates an input of a function template")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<StringGraphType>>("templateId", "the template identifier")
               .Argument<NonNullGraphType<StringGraphType>>("inputId", "the input identifier")
               .Argument<NonNullGraphType<FunctionInputInputGraphType>>("input", "The input")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates"));
                   string templateId = context.GetArgument<string>("templateId");
                   string inputId = context.GetArgument<string>("inputId");
                   UpdateFunctionInputCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateFunctionInputCommand>();

                   command.Id = inputId;
                   command.ModifiedById = context.GetLoginIdFromToken();

                   return await templateService.UpdateInputOfFunctionTemplateAsync(tenantId, templateId, command);
               });

            Field<ResultGraphType>()
              .Name("removeInputFromFunctionTemplate")
              .Description("removes an input from a function template")
              .AuthorizeWith("any")
              .Argument<NonNullGraphType<StringGraphType>>("templateId", "the template identifier")
              .Argument<NonNullGraphType<StringGraphType>>("inputId", "The input identifier")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates"));
                  string templateId = context.GetArgument<string>("templateId");
                  string inputId = context.GetArgument<string>("inputId");
                  var command = new RemoveCommand
                  {
                      Id = inputId,
                      RemovedById = context.GetLoginIdFromToken()
                  };

                  return await templateService.RemoveInputFromFunctionTemplateAsync(tenantId, templateId, command);
              });

            Field<CreatedStatusResultGraphType>()
             .Name("addOutputToFunctionTemplate")
             .Description("adds an output to a function template")
             .AuthorizeWith("any")
             .Argument<NonNullGraphType<StringGraphType>>("id", "the template identifier")
             .Argument<NonNullGraphType<FunctionOutputInputGraphType>>("input", "The input")
             .ResolveAsync(async context =>
             {
                 string tenantId = context.GetTenantIdFromToken();
                 await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates"));
                 string templateId = context.GetArgument<string>("id");
                 AddFunctionOutputCommand command = context.GetArgument<AddFunctionOutputCommand>("input");

                 command.AddedById = context.GetLoginIdFromToken();

                 return await templateService.AddOutputToFunctionTemplateAsync(tenantId, templateId, command);
             });

            Field<ResultGraphType>()
              .Name("updateOutputOfFunctionTemplate")
              .Description("updates an output of a function template")
              .AuthorizeWith("any")
              .Argument<NonNullGraphType<StringGraphType>>("templateId", "the template identifier")
              .Argument<NonNullGraphType<StringGraphType>>("outputId", "the output identifier")
              .Argument<NonNullGraphType<FunctionInputInputGraphType>>("input", "The input")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates"));
                  string templateId = context.GetArgument<string>("templateId");
                  string outputId = context.GetArgument<string>("outputId");
                  UpdateFunctionOutputCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateFunctionOutputCommand>();

                  command.Id = outputId;
                  command.ModifiedById = context.GetLoginIdFromToken();

                  return await templateService.UpdateOutputOfFunctionTemplateAsync(tenantId, templateId, command);
              });

            Field<ResultGraphType>()
              .Name("removeOutputFromFunctionTemplate")
              .Description("removes an output from a function template")
              .AuthorizeWith("any")
              .Argument<NonNullGraphType<StringGraphType>>("templateId", "the template identifier")
              .Argument<NonNullGraphType<StringGraphType>>("outputId", "The input identifier")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates"));
                  string templateId = context.GetArgument<string>("templateId");
                  string outputId = context.GetArgument<string>("outputId");
                  var command = new RemoveCommand
                  {
                      Id = outputId,
                      RemovedById = context.GetLoginIdFromToken()
                  };

                  return await templateService.RemoveOutputFromFunctionTemplateAsync(tenantId, templateId, command);
              });

            Field<CreatedStatusResultGraphType>()
                .Name("createNotificationTemplate")
                .Description("Creates a notification template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateNotificationTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("createTemplates", "writeTemplates"));
                    CreateNotificationTemplateCommand command = context.GetArgument<CreateNotificationTemplateCommand>("input");
                    command.CreatedById = context.GetLoginIdFromToken();

                    Result<CreatedStatus> result = await templateService.CreateNotificationTemplateAsync(tenantId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateNotificationTemplate")
                .Description("Updates a notification template")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template id to update")
                .Argument<NonNullGraphType<UpdateNotificationTemplateInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTemplates", "writeTemplates").WithTargetIds(templateId));

                    UpdateNotificationTemplateCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateNotificationTemplateCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await templateService.UpdateNotificationTemplateAsync(tenantId, templateId, command);
                    return result;
                });
        }
    }
}