﻿using GraphQL.Authorization;

using Microsoft.AspNetCore.Http;

using System.Security.Claims;

namespace CoverGo.Gateway.Interfaces
{
    public class GraphQLUserContext : IProvideClaimsPrincipal
    {
        public ClaimsPrincipal User { get; set; }
        public IHeaderDictionary Headers { get; set; }
        public string BaseUrl { get; set; }
        public bool PermissionLazyLoadingRequired { get; set; }
    }
}
