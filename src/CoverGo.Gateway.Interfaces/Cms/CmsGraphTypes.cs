﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Cms;
using CoverGo.Gateway.Interfaces.Auth;
using GraphQL.DataLoader;
using GraphQL.Types;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Gateway.Interfaces.Cms
{
    public class ComponentsGraphType : ObjectGraphType<ComponentsGraph>
    {
        public ComponentsGraphType(ICmsService cmsService, PermissionValidator permissionValidator)
        {
            Name = "components";

            Field(c => c.TotalCount)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    var allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readCms");
                    ComponentWhere where = allowedIds.Contains("all")
                        ? context.ComputeArgAndVar<ComponentWhere, ComponentsGraph>("where") ?? new ComponentWhere() ?? new ComponentWhere()
                        : new ComponentWhere
                        {
                            And = new List<ComponentWhere>
                            {
                                context.ComputeArgAndVar<ComponentWhere, ComponentsGraph>("where") ?? new ComponentWhere() ?? new ComponentWhere(),
                                new() { Id_in = allowedIds.ToList() }
                            }
                        };

                    long totalCount = await cmsService.GetTotalCountAsync(tenantId, where);
                    return totalCount;
                });

            Field(c => c.List, type: typeof(ListGraphType<ComponentGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    var allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readCms");
                    ComponentWhere where = allowedIds.Contains("all")
                        ? context.ComputeArgAndVar<ComponentWhere, ComponentsGraph>("where") ?? new ComponentWhere()
                        : new ComponentWhere
                        {
                            And = new List<ComponentWhere>
                            {
                                context.ComputeArgAndVar<ComponentWhere, ComponentsGraph>("where") ?? new ComponentWhere(),
                                new() { Id_in = allowedIds.ToList() }
                            }
                        };

                    int? skip = context.ComputeArgAndVar<int?, ComponentsGraph>("skip");
                    int? first = context.ComputeArgAndVar<int?, ComponentsGraph>("limit");
                    SortGraph sort = context.ComputeArgAndVar<SortGraph, ComponentsGraph>("sort");
                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy };
                    IEnumerable<Component> components = await cmsService.GetComponentsAsync(tenantId, queryArguments);
                    return components.Select(p => ComponentGraph.ToGraph(p));
                });
        }
    }

    public class ComponentsGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<ComponentGraph> List { get; set; }
    }

    public class ComponentVersionGraphType : ObjectGraphType<ComponentVersion>
    {
        public ComponentVersionGraphType()
        {
            Name = "componentVersion";

            Field(c => c.Framework);
            Field(c => c.Version);
        }
    }

    public class ComponentGraphType : ObjectGraphType<ComponentGraph>
    {
        public ComponentGraphType(IDataLoaderContextAccessor context, IAuthService authService, PermissionValidator permissionValidator)
        {
            Name = "component";

            Field(c => c.Id, type: typeof(IdGraphType));
            Field(c => c.Name);
            Field(c => c.Status, type: typeof(NonNullGraphType<ComponentStatusEnumerationGraphType>));
            Field(c => c.VueTemplate);
            Field(c => c.ComponentJson);
            Field(c => c.Version, type: typeof(ComponentVersionGraphType));

            this.PopulateSystemGraphTypeFields(context, authService, permissionValidator);
        }
    }

    public class CmsConfigGraph
    {
        public string TenantId { get; set; }
        public string AppId { get; set; }
        public AppGraph App { get; set; }
        public string HttpHeadersJsonString { get; set; }
        public string PaymentProvidersJsonString { get; set; }
        public string AnalyticsProvidersJsonString { get; set; }
        public CmsHeadConfigGraph Head { get; set; }
        public CmsEndpointConfigsGraph Endpoints { get; set; }
        public string EndpointsJsonString { get; set; }
        public CmsGuestLoginCredentials GuestLoginCredentials { get; set; }
        public IEnumerable<string> AllowedLocales { get; set; }
        public string DefaultLocale { get; set; }
        public IEnumerable<CmsComponentRouteGraph> ComponentRoutes { get; set; }
        public string ThemeJsonString { get; set; }
        public string BaseComponentsVariantsJsonString { get; set; }

        public static CmsConfig ToDomain(CmsConfigGraph graph) =>
            graph == null
            ? null
            : new CmsConfig
            {
                AnalyticsProviders = graph.AnalyticsProvidersJsonString != null ? JObject.Parse(graph.AnalyticsProvidersJsonString) : null,
                HttpHeaders = graph.HttpHeadersJsonString != null ? JObject.Parse(graph.HttpHeadersJsonString) : null,
                PaymentProviders = graph.PaymentProvidersJsonString != null ? JObject.Parse(graph.PaymentProvidersJsonString) : null,
                Head = CmsHeadConfigGraph.ToDomain(graph.Head),
                AllowedLocales = graph.AllowedLocales,
                ComponentRoutes = graph.ComponentRoutes?.Select(c => CmsComponentRouteGraph.ToDomain(c))?.ToList(),
                DefaultLocale = graph.DefaultLocale,
                Endpoints = graph.EndpointsJsonString != null ? JObject.Parse(graph.EndpointsJsonString) : null,
                GuestLoginCredentials = graph.GuestLoginCredentials,
                Theme = graph.ThemeJsonString != null ? JToken.Parse(graph.ThemeJsonString) : null,
                BaseComponentsVariants = graph.BaseComponentsVariantsJsonString != null ? JToken.Parse(graph.BaseComponentsVariantsJsonString) : null
            };

        public static CmsConfigGraph ToGraph(CmsConfig domain, string tenantId) =>
            domain == null
            ? null
            : new CmsConfigGraph
            {
                TenantId = tenantId,
                AppId = domain.AppId,
                App = domain.AppId != null ? new AppGraph { AppId = domain.AppId } : null,
                AnalyticsProvidersJsonString = domain.AnalyticsProviders != null ? JsonConvert.SerializeObject(domain.AnalyticsProviders) : null,
                HttpHeadersJsonString = domain.HttpHeaders != null ? JsonConvert.SerializeObject(domain.HttpHeaders) : null,
                PaymentProvidersJsonString = domain.PaymentProviders != null ? JsonConvert.SerializeObject(domain.PaymentProviders) : null,
                Head = CmsHeadConfigGraph.ToGraph(domain.Head),
                AllowedLocales = domain.AllowedLocales,
                ComponentRoutes = domain.ComponentRoutes?.Select(c => CmsComponentRouteGraph.ToGraph(c)),
                DefaultLocale = domain.DefaultLocale,
                EndpointsJsonString = domain.Endpoints != null ? JsonConvert.SerializeObject(domain.Endpoints) : null,
                GuestLoginCredentials = domain.GuestLoginCredentials,
                ThemeJsonString = domain.Theme != null ? JsonConvert.SerializeObject(domain.Theme) : null,
                BaseComponentsVariantsJsonString = domain.BaseComponentsVariants != null ? JsonConvert.SerializeObject(domain.BaseComponentsVariants) : null
            };
    }

    public class CmsConfigResultGraphType : ObjectGraphType<Result<CmsConfigGraph>>
    {
        public CmsConfigResultGraphType()
        {
            Name = "cmsConfigResult";

            Field(c => c.Status, nullable: true);
            Field(c => c.Errors, nullable: true);
            Field(c => c.Errors_2, type: typeof(ListGraphType<NonNullGraphType<ErrorGraphType>>));
            Field(c => c.Value, type: typeof(CmsConfigGraphType));
        }
    }

    public class CmsConfigGraphType : ObjectGraphType<CmsConfigGraph>
    {
        public CmsConfigGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator
            )
        {
            Name = "cmsConfig";

            Field(c => c.TenantId);
            Field(c => c.AppId, nullable: true);
            Field(c => c.App, type: typeof(AppGraphType))
                .ResolveAsync(async context =>
                    {
                        if (context.Source.App.AppId == null)
                            return null;

                        string tenantId = context.GetTenantIdFromToken(false);
                        if (tenantId == null)
                            return null;

                        var appIds = await permissionValidator.GetTargetIdsFromClaim(context, "readApps");

                        var loader = accessor.Context.GetOrAddBatchLoader<string, App>("GetApps",
                            ids => authService.GetAppsDictionaryAsync(tenantId, appIds.Contains("all")
                                ? new AppWhere { AppId_in = ids?.ToList() }
                                : new AppWhere
                                {
                                    And = new List<AppWhere>
                                    {
                                new() { AppId_in = ids?.ToList() },
                                new() { AppId_in = ids.ToList() }
                                    }
                                }));

                        App app = await loader.LoadAsync(context.Source.App.AppId);
                        return AppGraph.ToGraph(app);
                    });
            Field(c => c.HttpHeadersJsonString, nullable: true);
            Field(c => c.PaymentProvidersJsonString, nullable: true);
            Field(c => c.AnalyticsProvidersJsonString, nullable: true);
            Field(c => c.Head, type: typeof(CmsHeadConfigGraphType));
            Field(c => c.EndpointsJsonString, nullable: true);
            Field(c => c.GuestLoginCredentials, type: typeof(CmsGuestLoginCredentialsGraphType));
            Field(c => c.AllowedLocales, type: typeof(ListGraphType<NonNullGraphType<StringGraphType>>));
            Field(c => c.DefaultLocale, nullable: true);
            Field(c => c.ComponentRoutes, type: typeof(ListGraphType<NonNullGraphType<CmsComponentRouteGraphType>>));
            Field(c => c.ThemeJsonString, nullable: true);
            Field(c => c.BaseComponentsVariantsJsonString, nullable: true);
        }
    }

    public class CmsConfigInputGraphType : InputObjectGraphType<CmsConfigGraph>
    {
        public CmsConfigInputGraphType()
        {
            Name = "cmsConfigInput";

            Field(c => c.HttpHeadersJsonString, nullable: true);
            Field(c => c.PaymentProvidersJsonString, nullable: true);
            Field(c => c.AnalyticsProvidersJsonString, nullable: true);
            Field(c => c.Head, type: typeof(CmsHeadConfigInputGraphType));
            Field(c => c.EndpointsJsonString, nullable: true);
            Field(c => c.GuestLoginCredentials, type: typeof(CmsGuestLoginCredentialsInputGraphType));
            Field(c => c.AllowedLocales, type: typeof(ListGraphType<NonNullGraphType<StringGraphType>>));
            Field(c => c.DefaultLocale, nullable: true);
            Field(c => c.ComponentRoutes, type: typeof(ListGraphType<NonNullGraphType<CmsComponentRouteInputGraphType>>));
            Field(c => c.ThemeJsonString, nullable: true);
            Field(c => c.BaseComponentsVariantsJsonString, nullable: true);
        }
    }

    public class CmsHeadConfigGraph
    {
        public string Title { get; set; }
        public string MetaJsonString { get; set; }
        public string LinkJsonString { get; set; }
        public IEnumerable<CmsScriptConfigGraph> Script { get; set; }
        public IEnumerable<CmsScriptConfigGraph> NoScript { get; set; }

        public static CmsHeadConfig ToDomain(CmsHeadConfigGraph graph) =>
            graph == null
            ? null
            : new CmsHeadConfig
            {
                Title = graph.Title,
                Meta = graph.MetaJsonString != null ? JArray.Parse(graph.MetaJsonString) : null,
                Link = graph.LinkJsonString != null ? JArray.Parse(graph.LinkJsonString) : null,
                Script = graph.Script?.Select(s => CmsScriptConfigGraph.ToDomain(s))?.ToList(),
                NoScript = graph.NoScript?.Select(s => CmsScriptConfigGraph.ToDomain(s))?.ToList(),
            };
        public static CmsHeadConfigGraph ToGraph(CmsHeadConfig domain) =>
            domain == null
            ? null
            : new CmsHeadConfigGraph
            {
                Title = domain.Title,
                MetaJsonString = domain.Meta != null ? JsonConvert.SerializeObject(domain.Meta) : null,
                LinkJsonString = domain.Link != null ? JsonConvert.SerializeObject(domain.Link) : null,
                Script = domain.Script?.Select(s => CmsScriptConfigGraph.ToGraph(s)),
                NoScript = domain.NoScript?.Select(s => CmsScriptConfigGraph.ToGraph(s)),
            };
    }

    public class CmsHeadConfigGraphType : ObjectGraphType<CmsHeadConfigGraph>
    {
        public CmsHeadConfigGraphType()
        {
            Name = "cmsHeadConfig";

            Field(c => c.Title, nullable: true);
            Field(c => c.MetaJsonString, nullable: true);
            Field(c => c.LinkJsonString, nullable: true);
            Field(c => c.Script, type: typeof(ListGraphType<NonNullGraphType<CmsScriptConfigGraphType>>));
            Field(c => c.NoScript, type: typeof(ListGraphType<NonNullGraphType<CmsScriptConfigGraphType>>));
        }
    }

    public class CmsHeadConfigInputGraphType : InputObjectGraphType<CmsHeadConfigGraph>
    {
        public CmsHeadConfigInputGraphType()
        {
            Name = "cmsHeadConfigInput";

            Field(c => c.Title, nullable: true);
            Field(c => c.MetaJsonString, nullable: true);
            Field(c => c.LinkJsonString, nullable: true);
            Field(c => c.Script, type: typeof(ListGraphType<NonNullGraphType<CmsScriptConfigInputGraphType>>));
            Field(c => c.NoScript, type: typeof(ListGraphType<NonNullGraphType<CmsScriptConfigInputGraphType>>));
        }
    }

    public class CmsScriptConfigGraph
    {
        public string Type { get; set; }
        public string Src { get; set; }
        public bool Async { get; set; }
        public bool Defer { get; set; }
        public bool AfterLoad { get; set; }
        public bool Body { get; set; }
        public string InnerHTML { get; set; }

        public static CmsScriptConfig ToDomain(CmsScriptConfigGraph graph) =>
            graph == null
            ? null
            : new CmsScriptConfig
            {
                Type = graph.Type,
                AfterLoad = graph.AfterLoad,
                Async = graph.Async,
                Body = graph.Body,
                Defer = graph.Defer,
                InnerHTML = graph.InnerHTML,
                Src = graph.Src,
            };

        public static CmsScriptConfigGraph ToGraph(CmsScriptConfig domain) =>
            domain == null
            ? null
            : new CmsScriptConfigGraph
            {
                Type = domain.Type,
                AfterLoad = domain.AfterLoad,
                Async = domain.Async,
                Body = domain.Body,
                Defer = domain.Defer,
                InnerHTML = domain.InnerHTML,
                Src = domain.Src
            };
    }

    public class CmsScriptConfigGraphType : ObjectGraphType<CmsScriptConfigGraph>
    {
        public CmsScriptConfigGraphType()
        {
            Name = "cmsScriptConfig";

            Field(c => c.Type, nullable: true);
            Field(c => c.Src, nullable: true);
            Field(c => c.Async, type: typeof(BooleanGraphType));
            Field(c => c.Defer, type: typeof(BooleanGraphType));
            Field(c => c.AfterLoad, type: typeof(BooleanGraphType));
            Field(c => c.Body, type: typeof(BooleanGraphType));
            Field(c => c.InnerHTML, nullable: true);
        }
    }

    public class CmsScriptConfigInputGraphType : InputObjectGraphType<CmsScriptConfigGraph>
    {
        public CmsScriptConfigInputGraphType()
        {
            Name = "cmsScriptConfigInput";

            Field(c => c.Src, nullable: true);
            Field(c => c.Async, type: typeof(BooleanGraphType));
            Field(c => c.Defer, type: typeof(BooleanGraphType));
            Field(c => c.AfterLoad, type: typeof(BooleanGraphType));
            Field(c => c.Body, type: typeof(BooleanGraphType));
            Field(c => c.InnerHTML, nullable: true);
        }
    }

    public class CmsEndpointConfigsGraph
    {
        public CmsEndpointConfigGraph Api { get; set; }
        public CmsEndpointConfigGraph Cms { get; set; }

        public static CmsEndpointConfigs ToDomain(CmsEndpointConfigsGraph graph) =>
            graph == null
            ? null
            : new CmsEndpointConfigs
            {
                Api = CmsEndpointConfigGraph.ToDomain(graph.Api),
                Cms = CmsEndpointConfigGraph.ToDomain(graph.Cms)
            };

        public static CmsEndpointConfigsGraph ToGraph(CmsEndpointConfigs domain) =>
            domain == null
            ? null
            : new CmsEndpointConfigsGraph
            {
                Api = CmsEndpointConfigGraph.ToGraph(domain.Api),
                Cms = CmsEndpointConfigGraph.ToGraph(domain.Cms)
            };
    }

    public class CmsEndpointConfigsGraphType : ObjectGraphType<CmsEndpointConfigsGraph>
    {
        public CmsEndpointConfigsGraphType()
        {
            Name = "cmsEndpointConfigs";

            Field(c => c.Api, type: typeof(CmsEndpointConfigGraphType));
            Field(c => c.Cms, type: typeof(CmsEndpointConfigGraphType));
        }
    }

    public class CmsEndpointConfigsInputGraphType : InputObjectGraphType<CmsEndpointConfigs>
    {
        public CmsEndpointConfigsInputGraphType()
        {
            Name = "cmsEndpointConfigsInput";

            Field(c => c.Api, type: typeof(CmsEndpointConfigInputGraphType));
            Field(c => c.Cms, type: typeof(CmsEndpointConfigInputGraphType));
        }
    }

    public class CmsEndpointConfigGraph
    {
        public string HeadersJsonString { get; set; }
        public string Url { get; set; }
        public string Querystring { get; set; }

        public static CmsEndpointConfig ToDomain(CmsEndpointConfigGraph graph) =>
            graph == null
            ? null
            : new CmsEndpointConfig
            {
                Headers = graph.HeadersJsonString != null ? JToken.Parse(graph.HeadersJsonString) : null,
                Url = graph.Url,
                Querystring = graph.Querystring,
            };

        public static CmsEndpointConfigGraph ToGraph(CmsEndpointConfig domain) =>
            domain == null
            ? null
            : new CmsEndpointConfigGraph
            {
                HeadersJsonString = domain.Headers != null ? JsonConvert.SerializeObject(domain.Headers) : null,
                Url = domain.Url,
                Querystring = domain.Querystring
            };
    }

    public class CmsEndpointConfigGraphType : ObjectGraphType<CmsEndpointConfigGraph>
    {
        public CmsEndpointConfigGraphType()
        {
            Name = "cmsEndpointConfig";

            Field(c => c.HeadersJsonString, nullable: true);
            Field(c => c.Url, nullable: true);
            Field(c => c.Querystring, nullable: true);
        }
    }

    public class CmsEndpointConfigInputGraphType : InputObjectGraphType<CmsEndpointConfigGraph>
    {
        public CmsEndpointConfigInputGraphType()
        {
            Name = "cmsEndpointConfigInput";

            Field(c => c.HeadersJsonString, nullable: true);
            Field(c => c.Url, nullable: true);
            Field(c => c.Querystring, nullable: true);
        }
    }

    public class CmsGuestLoginCredentialsGraphType : ObjectGraphType<CmsGuestLoginCredentials>
    {
        public CmsGuestLoginCredentialsGraphType()
        {
            Name = "cmsGuestLoginCredentials";

            Field(c => c.Username);
            Field(c => c.Password);
        }
    }

    public class CmsGuestLoginCredentialsInputGraphType : InputObjectGraphType<CmsGuestLoginCredentials>
    {
        public CmsGuestLoginCredentialsInputGraphType()
        {
            Name = "cmsGuestLoginCredentialsInput";

            Field(c => c.Username);
            Field(c => c.Password);
        }
    }

    public class CmsComponentRouteGraph
    {
        public string Path { get; set; }
        public string Component { get; set; }
        public string Name { get; set; }
        public string MetaJsonString { get; set; }
        public string Redirect { get; set; }
        public string PropsJsonString { get; set; }
        public IEnumerable<CmsComponentRouteGraph> Children { get; set; }

        public static CmsComponentRoute ToDomain(CmsComponentRouteGraph graph) =>
            graph == null
            ? null
            : new CmsComponentRoute
            {
                Path = graph.Path,
                Component = graph.Component,
                Name = graph.Name,
                Meta = graph.MetaJsonString != null ? JToken.Parse(graph.MetaJsonString) : null,
                Redirect = graph.Redirect,
                Props = graph.PropsJsonString != null ? JToken.Parse(graph.PropsJsonString) : null,
                Children = graph.Children?.Select(c => ToDomain(c))
            };

        public static CmsComponentRouteGraph ToGraph(CmsComponentRoute domain) =>
            domain == null
            ? null
            : new CmsComponentRouteGraph
            {
                Path = domain.Path,
                Component = domain.Component,
                Name = domain.Name,
                MetaJsonString = domain.Meta != null ? JsonConvert.SerializeObject(domain.Meta) : null,
                Redirect = domain.Redirect,
                PropsJsonString = domain.Meta != null ? JsonConvert.SerializeObject(domain.Props) : null,
                Children = domain.Children?.Select(c => ToGraph(c))
            };
    }

    public class CmsComponentRouteGraphType : ObjectGraphType<CmsComponentRouteGraph>
    {
        public CmsComponentRouteGraphType()
        {
            Name = "cmsComponentRoute";

            Field(c => c.Path, nullable: true);
            Field(c => c.Component, nullable: true);
            Field(c => c.Name, nullable: true);
            Field(c => c.MetaJsonString, nullable: true);
            Field(c => c.Redirect, nullable: true);
            Field(c => c.PropsJsonString, nullable: true);
            Field(c => c.Children, type: typeof(CmsComponentRouteGraphType));
        }
    }

    public class CmsComponentRouteInputGraphType : InputObjectGraphType<CmsComponentRouteGraph>
    {
        public CmsComponentRouteInputGraphType()
        {
            Name = "cmsComponentRouteInput";

            Field(c => c.Path, nullable: true);
            Field(c => c.Component, nullable: true);
            Field(c => c.Name, nullable: true);
            Field(c => c.MetaJsonString, nullable: true);
            Field(c => c.Redirect, nullable: true);
            Field(c => c.PropsJsonString, nullable: true);
            Field(c => c.Children, type: typeof(CmsComponentRouteInputGraphType));
        }
    }

    public class ComponentGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string ComponentJson { get; set; }
        public string VueTemplate { get; set; }
        public ComponentStatus Status { get; set; }
        public ComponentVersion Version { get; set; }

        public static ComponentGraph ToGraph(Component domain) =>
            domain == null
                ? null
                : new ComponentGraph
                {
                    Id = domain.Id,
                    Name = domain.Name,
                    ComponentJson = JsonConvert.SerializeObject(domain.ComponentJson),
                    VueTemplate = domain.VueTemplate,
                    Status = domain.Status,
                    Version = domain.Version
                }.PopulateSystemGraphFields(domain);
    }

    public class ComponentVersionInputGraphType : InputObjectGraphType<ComponentVersion>
    {
        public ComponentVersionInputGraphType()
        {
            Name = "componentVersionInput";

            Field(c => c.Framework);
            Field(c => c.Version);
        }
    }

    public class CreateComponentInputGraphType : InputObjectGraphType<CreateComponentInputGraph>
    {
        public CreateComponentInputGraphType()
        {
            Name = "createComponentInput";

            Field(c => c.Name);
            Field(c => c.Status, type: typeof(ComponentStatusEnumerationGraphType));
            Field(c => c.VueTemplate, nullable: true);
            Field(c => c.ComponentJsonString);
            Field(c => c.Version, type: typeof(ComponentVersionInputGraphType));
        }
    }

    public class UpdateComponentInputGraphType : InputObjectGraphType<UpdateComponentInputGraph>
    {
        public UpdateComponentInputGraphType()
        {
            Name = "updateComponentInput";

            Field(c => c.Name, nullable: true);
            Field(c => c.Status, type: typeof(ComponentStatusEnumerationGraphType));
            Field(c => c.VueTemplate, nullable: true);
            Field(c => c.ComponentJsonString, nullable: true);
            Field(c => c.Version, type: typeof(ComponentVersionInputGraphType));
        }
    }

    public class CreateComponentInputGraph
    {
        public string Name { get; set; }
        public string VueTemplate { get; set; }
        public string ComponentJsonString { get; set; }
        public ComponentStatus Status { get; set; }
        public ComponentVersion Version { get; set; }
    }

    public class UpdateComponentInputGraph
    {
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string VueTemplate { get; set; }
        public bool IsVueTemplateChanged { get; set; }
        public string ComponentJsonString { get; set; }
        public bool IsComponentJsonStringChanged { get; set; }
        public ComponentStatus Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public ComponentVersion Version { get; set; }
        public bool IsVersionChanged { get; set; }
    }

    public class ComponentStatusEnumerationGraphType : EnumerationGraphType<ComponentStatus>
    {
        public ComponentStatusEnumerationGraphType()
        {
            Name = "componentStatusEnumeration";
        }
    }

    public class ComponentWhereInputGraphType : InputObjectGraphType<ComponentWhere>
    {
        public ComponentWhereInputGraphType()
        {
            Name = "componentWhereInput";
            Description = "A claim search filter";

            Field(f => f.Or, type: typeof(ListGraphType<ComponentWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<ComponentWhereInputGraphType>));

            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);

            Field(f => f.Name, nullable: true);
            Field(f => f.Status, nullable: true);

            this.PopulateSystemWhereFields();
        }
    }

    public class MigrateCmsInputGraphType : InputObjectGraphType<MigrateCmsCommand>
    {
        public MigrateCmsInputGraphType()
        {
            Name = "migrateCmsInput";

            Field(c => c.ConnectionString, nullable: true);
            Field(c => c.DbName, nullable: true);
        }
    }

    public class CmsConfigWhereInputGraphType : InputObjectGraphType<CmsConfigWhere>
    {
        public CmsConfigWhereInputGraphType()
        {
            Field(f => f.Or, type: typeof(ListGraphType<CmsConfigWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<CmsConfigWhereInputGraphType>));

            Field(f => f.AppId, nullable: true);
            Field(f => f.AppId_in, nullable: true);

            this.PopulateSystemWhereFields();
        }
    }
}
