﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Interfaces.Products;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.ScriptEvaluation
{
    public class EvaluateScriptInputGraphType : InputObjectGraphType<EvaluateScriptInput>
    {
        public EvaluateScriptInputGraphType()
        {
            Name = "evaluateScriptInput";
            Field(f => f.ScriptId, type: typeof(StringGraphType), nullable: false);
            Field(f => f.DataInput, type: typeof(StringGraphType), nullable: false);
            Field(f => f.ProductId, type: typeof(ProductIdInputGraphType), nullable: true);
            Field(f => f.Representation, type: typeof(StringGraphType), nullable: true);
            Field(f => f.DistributorID, type: typeof(StringGraphType), nullable: true);
            Field(f => f.CampaignCodes, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            Field(f => f.<PERSON>, type: typeof(BooleanGraphType), nullable: true);
        }
    }

    public class EvaluateScriptResultGraphType : ObjectGraphType<Result<string>>
    {
        public EvaluateScriptResultGraphType()
        {
            Name = "evaluateScriptResult";
            Field(e => e.Status);
            Field(e => e.Value, nullable: true);
            Field(e => e.Errors, type: typeof(ListGraphType<StringGraphType>), nullable: true);
        }
    }

    public class EvaluateScriptInput
    {
        public string ScriptId { get; set; }
        public ProductId ProductId { get; set; }
        public string DataInput { get; set; }
        public string Representation { get; set; }
        public string? DistributorID { get; set; }
        public string[]? CampaignCodes { get; set; }
        public bool IsRenewal { get; set; }
    }
}
