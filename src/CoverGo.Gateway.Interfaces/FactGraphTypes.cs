﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Auth;
using GraphQL.DataLoader;
using GraphQL.Types;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Gateway.Interfaces
{
    public class FactGraphType : ObjectGraphType<FactGraph>
    {
        public FactGraphType()
        {
            Name = "fact";
            Description = "A fact";

            Field(c => c.Id);
            Field(c => c.Type, nullable: true);
            Field(c => c.Value, type: typeof(ScalarValueGraphType), nullable: true);
            Field(c => c.Values, type: typeof(ScalarValueGraphType), nullable: true).DeprecationReason("use 'Value'");
        }
    }

    public class FactGraph
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public ScalarValue Value { get; set; }
        [Obsolete("Use only 'Value'")]
        public ScalarValue Values { get; set; }

        public static FactGraph ToGraph(Fact domain) =>
            new()
            {
                Id = domain.Id,
                Type = domain.Type,
                Value = domain.Value?.ToScalarValue(),
                Values = domain.Value?.ToScalarValue()
            };
    }

    public class AddFactInputGraph
    {
        public string Type { get; set; }
        public ScalarValue Value { get; set; }
        
        public ScalarValue Values { get; set; }

        public AddFactCommand ToCommand(string loginId) =>
            new()
            {
                AddedById = loginId,
                Type = Type,
                Value = Value?.GetValue() != null
                    ? JToken.FromObject(Value?.GetValue())
                    : Values?.GetValue() != null //ToDo: remove check for values after migration
                        ? JToken.FromObject(Values?.GetValue())
                        : null
            };
    }

    public class UpdateFactInputGraph
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public bool IsTypeChanged { get; set; }
        public ScalarValue Value { get; set; }
        public bool IsValueChanged { get; set; }
        
        public ScalarValue Values { get; set; }

        public UpdateFactCommand ToCommand(string loginId) => new()
        {
            Id = Id,
            Type = Type,
            IsTypeChanged = IsTypeChanged,
            Value = Value?.GetValue() != null ? JToken.FromObject(Value?.GetValue()) : null,
            IsValueChanged = IsValueChanged,
            ModifiedById = loginId
        };
    }

    public class AddFactCommandGraph
    {
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public string EntityId { get; set; }
        public string Id { get; set; }
        public string Type { get; set; }

        public ScalarValue Value { get; set; }
        public LoginGraph AddedBy { get; set; }

        public static AddFactCommandGraph ToGraph(AddFactCommand command)
            => new()
            {
                CommandId = command.CommandId,
                Timestamp = command.Timestamp,
                EntityId = command.EntityId,
                Id = command.Id,
                Type = command.Type,
                Value = command.Value?.ToScalarValue(),
                AddedBy = new LoginGraph { Id = command.AddedById }
            };
    }

    public class UpdateFactCommandGraph
    {
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public string EntityId { get; set; }
        public string Id { get; set; }
        public string Type { get; set; }
        public bool IsTypeChanged { get; set; }
        public ScalarValue Value { get; set; }
        public bool IsValueChanged { get; set; }
        public LoginGraph ModifiedBy { get; set; }

        public static UpdateFactCommandGraph ToGraph(UpdateFactCommand command)
            => new()
            {
                CommandId = command.CommandId,
                Timestamp = command.Timestamp,
                EntityId = command.EntityId,
                Id = command.Id,
                IsTypeChanged = command.IsTypeChanged,
                Type = command.Type,
                IsValueChanged = command.IsValueChanged,
                Value = command.Value?.ToScalarValue(),
                ModifiedBy = new LoginGraph { Id = command.ModifiedById }
            };
    }

    public class AddFactInputGraphType : InputObjectGraphType<AddFactInputGraph>
    {
        public AddFactInputGraphType()
        {
            Name = "addFactInput";
            Description = "Add fact input";

            Field(c => c.Type);
            Field(c => c.Value, type: typeof(ScalarValueInputGraphType));
            Field(c => c.Values, type: typeof(ScalarValueInputGraphType));
        }
    }

    public class UpdateFactInputGraphType : InputObjectGraphType<UpdateFactInputGraph>
    {
        public UpdateFactInputGraphType()
        {
            Name = "updateFactInput";
            Description = "Update fact input";

            Field(c => c.Id, nullable: true);
            Field(c => c.Type, nullable: true);
            Field(c => c.Value, type: typeof(ScalarValueInputGraphType));
            Field(c => c.Values, type: typeof(ScalarValueInputGraphType)).DeprecationReason("Use value instead");
        }
    }

    public class AddFactCommandGraphType : ObjectGraphType<AddFactCommandGraph>
    {
        public AddFactCommandGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Field(c => c.CommandId, nullable: true);
            Field(p => p.Timestamp, type: typeof(DateTimeGraphType));
            Field(c => c.EntityId, nullable: true);
            Field(c => c.Id, nullable: true);
            Field(c => c.Type, nullable: true);
            Field(c => c.Value, type: typeof(ScalarValueGraphType));
            Field(c => c.AddedBy, type: typeof(LoginGraphType))
                 .ResolveAsync(async context =>
                 {
                     if (context.Source.AddedBy?.Id == null)
                         return null;

                     IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readLogins", new List<string> { context.Source.AddedBy.Id });
                     if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.AddedBy.Id))
                         return null;

                     string tenantId = context.GetTenantIdFromToken();

                     var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                     i => authService.GetLoginsDictionaryAsync(tenantId, i));

                     Login loginDao = await loginLoader.LoadAsync(context.Source.AddedBy.Id);

                     return LoginGraph.ToGraph(loginDao);
                 });
        }
    }

    public class UpdateFactCommandGraphType : ObjectGraphType<UpdateFactCommandGraph>
    {
        public UpdateFactCommandGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "updateFactCommand";
            Description = "An update fact command";

            Field(c => c.CommandId, nullable: true);
            Field(p => p.Timestamp, type: typeof(DateTimeGraphType));
            Field(c => c.EntityId, nullable: true);
            Field(c => c.Id, nullable: true);
            Field(c => c.IsTypeChanged);
            Field(c => c.Type, nullable: true);
            Field(c => c.IsValueChanged);
            Field(c => c.Value, type: typeof(ScalarValueGraphType));
            Field(c => c.ModifiedBy, type: typeof(LoginGraphType))
                 .ResolveAsync(async context =>
                 {
                     if (context.Source.ModifiedBy?.Id == null)
                         return null;

                     IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readLogins", new List<string> { context.Source.ModifiedBy.Id });
                     if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.ModifiedBy.Id))
                         return null;

                     string tenantId = context.GetTenantIdFromToken();

                     var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                     i => authService.GetLoginsDictionaryAsync(tenantId, i));

                     Login loginDao = await loginLoader.LoadAsync(context.Source.ModifiedBy.Id);

                     return LoginGraph.ToGraph(loginDao);
                 });
        }
    }

    public class FactCommandBatchInputGraph
    {
        public List<AddFactInputGraph> AddFactInputs { get; set; }
        public List<UpdateFactInputGraph> UpdateFactInputs { get; set; }
    }

    public class FactCommandBatchInputGraphType : InputObjectGraphType<FactCommandBatchInputGraph>
    {
        public FactCommandBatchInputGraphType()
        {
            Name = "factBatchInput";

            Field(b => b.AddFactInputs, type: typeof(ListGraphType<AddFactInputGraphType>));
            Field(b => b.UpdateFactInputs, type: typeof(ListGraphType<UpdateFactInputGraphType>));
        }
    }

    public class FactTemplateGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public IEnumerable<PossibleValueTemplateGraph> PossibleValueTemplates { get; set; }

        public static FactTemplateGraph ToGraph(FactTemplate domain) =>
            domain != null
            ? new FactTemplateGraph
            {
                Id = domain.Id,
                Name = domain.Name,
                PossibleValueTemplates = domain.PossibleValueTemplates?.Select(pvt => new PossibleValueTemplateGraph
                {
                    FactType = pvt.FactType,
                    ParentFactTemplateId = domain.Id,
                    PossibleValues = pvt.PossibleValues?.Select(pv => new PossibleValueGraph
                    {
                        ScalarType = pv.ScalarType,
                        Values = pv.Values?.Select(pvv => pvv.ToScalarValue())
                    })
                })
            } : null;
    }

    public class PossibleValueTemplateGraph
    {
        public string ParentFactTemplateId { get; set; } // needed only for localization
        public string FactType { get; set; }
        public string FactName { get; set; }
        public string FactDescription { get; set; }
        public IEnumerable<PossibleValueGraph> PossibleValues { get; set; }
    }

    public class PossibleValueGraph
    {
        public string ScalarType { get; set; }
        public IEnumerable<ScalarValue> Values { get; set; }
    }

    public class FactTemplateGraphType : ObjectGraphType<FactTemplateGraph>
    {
        public FactTemplateGraphType()
        {
            Name = "factTemplate";
            Description = "A fact template";

            Field(ft => ft.Id, type: typeof(NonNullGraphType<IdGraphType>));
            Field(ft => ft.Name, nullable: true);
            Field(ft => ft.PossibleValueTemplates, type: typeof(ListGraphType<PossibleValueTemplateGraphType>));
        }
    }

    public class PossibleValueTemplateGraphType : ObjectGraphType<PossibleValueTemplateGraph>
    {
        public PossibleValueTemplateGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService)
        {
            Name = "possibleValueTemplate";
            Description = "A template to get possible values";

            Field(pvt => pvt.FactType);
            Field(pvt => pvt.FactName, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"factTemplates-{context.Source.ParentFactTemplateId}-type-{context.Source.FactType}-name"));
            Field(pvt => pvt.FactDescription, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"factTemplates-{context.Source.ParentFactTemplateId}-type-{context.Source.FactType}-description"));
            Field(pvt => pvt.PossibleValues, type: typeof(ListGraphType<PossibleValueGraphType>));
        }
    }

    public class PossibleValueGraphType : ObjectGraphType<PossibleValueGraph>
    {
        public PossibleValueGraphType()
        {
            Name = "possibleValue";
            Description = "A possible value coming from a template";

            Field(pv => pv.ScalarType).Description("Can be only 'Boolean', 'Date', 'Number', 'String'");
            Field(pv => pv.Values, type: typeof(ListGraphType<ScalarValueGraphType>));
        }
    }
}
