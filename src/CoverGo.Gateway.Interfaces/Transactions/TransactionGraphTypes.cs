﻿using System;
using System.Collections.Generic;
using System.Linq;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Claims;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Interfaces.Auth;
using CoverGo.Gateway.Interfaces.Cases;
using CoverGo.Gateway.Interfaces.Claims;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Users;
using GraphQL.DataLoader;
using GraphQL.Types;
using GraphQL.Utilities;
using Newtonsoft.Json;
using CurrencyCode = CoverGo.Gateway.Domain.CurrencyCode;
using EventLog = CoverGo.Gateway.Domain.EventLog;

namespace CoverGo.Gateway.Interfaces.Transactions
{
    public class TransactionsGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<TransactionGraph> List { get; set; }
        public TransactionWhere Where { get; set; } // populated in covergoQuery
        public DateTime? AsOf { get; set; } // populated in asOf
        public int? Skip { get; set; }
        public int? First { get; set; }
        public SortGraph Sort { get; set; }
    }

    public class TransactionsGraphType : ObjectGraphType<TransactionsGraph>
    {
        public TransactionsGraphType(ITransactionService transactionService, PermissionValidator permissionValidator)
        {
            Name = "transactions";
            Description = "Gets all transactions";

            Field(c => c.TotalCount)
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();

                   IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readTransactions");
                   TransactionWhere where = allowedIds.Contains("all")
                       ? context.Source.Where ?? new TransactionWhere()
                       : new TransactionWhere
                       {
                           And = new List<TransactionWhere>
                           {
                                context.Source.Where ?? new TransactionWhere(),
                                new() { Id_in = allowedIds.ToList() }
                           }
                       };

                   return await transactionService.GetTotalCountAsync(tenantId, where);
               });

            Field(c => c.List, type: typeof(ListGraphType<TransactionGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readTransactions");
                    TransactionWhere where = allowedIds.Contains("all")
                        ? context.Source.Where ?? new TransactionWhere()
                        : new TransactionWhere
                        {
                            And = new List<TransactionWhere>
                            {
                                context.Source.Where ?? new TransactionWhere(),
                                new() { Id_in = allowedIds.ToList() }
                            }
                        };

                    SortGraph sort = context.Source.Sort;
                    OrderBy orderBy = null;

                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = context.Source.Skip, First = context.Source.First, OrderBy = orderBy, AsOf = context.Source.AsOf };
                    IEnumerable<Transaction> transactions = await transactionService.GetAsync(tenantId, queryArguments);
                    return transactions.Select(TransactionGraph.ToGraph);
                });
        }
    }

    public class TransactionGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string TransactionNumber { get; set; }
        public string Status { get; set; }
        public string Description { get; set; }
        public string ClaimId { get; set; }
        public PolicyGraph Policy { get; set; }
        public string EndorsementId { get; set; }
        public string ProposalId { get; set; }
        public SubscriptionGraph Subscription { get; set; }
        public DateTime? DateTime { get; set; }
        public DateTime? PostDateTime { get; set; }
        public DateTime? DueDateTime { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public PaymentMethodType? Method { get; set; }
        public decimal? Amount { get; set; }
        public string Type { get; set; }
        public string AccountType { get; set; }
        public string AccountCode { get; set; }
        public string FormattedTransaction { get; set; }
        public string ProviderTransactionId { get; set; }
        public string ApprovalCode { get; set; }
        public IEnumerable<NoteGraph> Notes { get; set; }
        public IEnumerable<FactGraph> Facts { get; set; }
        public IEnumerable<StakeholderGraph> Stakeholders { get; set; }
        public object Object { get; set; }
        public IEnumerable<EventLogGraph> Events { get; set; }
        public IEnumerable<AttachmentGraph> Attachments { get; set; }
        public string SettledToTransactionId { get; set; }
        public string DebitNoteNumber { get; set; }
        public TransactionsGraph SettledTransactions { get; set; }
        public string Fields { get; set; }

        public static TransactionGraph ToGraph(Transaction domain) =>
            domain == null
                ? null
                : new TransactionGraph
                {
                    Id = domain.Id,
                    TransactionNumber = domain.TransactionNumber,
                    Description = domain.Description,
                    Policy = domain.PolicyId != null ? new PolicyGraph { Id = domain.PolicyId } : null,
                    Subscription = domain.SubscriptionId != null ? new SubscriptionGraph { Id = domain.SubscriptionId } : null,
                    EndorsementId = domain.EndorsementId,
                    ClaimId = domain.ClaimId,
                    ProposalId = domain.ProposalId,
                    Status = StringUtils.ToConstantCase(domain.Status.ToString()),
                    Amount = domain.Amount,
                    Type = domain.Type,
                    AccountCode = domain.AccountCode,
                    AccountType = domain.AccountType,
                    CurrencyCode = domain.CurrencyCode,
                    DateTime = domain.DateTime,
                    DueDateTime = domain.DueDateTime,
                    PostDateTime = domain.PostDateTime,
                    Method = domain.Method,
                    ProviderTransactionId = domain.ProviderTransactionId,
                    ApprovalCode = $"{(domain.ProviderTransactionId ?? "000000").ToString().PadLeft(6, '0')}{domain.Amount?.ToString("0.00").Replace(".", "").PadLeft(11, '0')}{domain.DateTime:yyyyMMdd}021001{"",27}",
                    Notes = domain.Notes.Select(NoteGraph.ToGraph),
                    Facts = domain.Facts.Select(FactGraph.ToGraph),
                    Stakeholders = domain.Stakeholders?.Select(s => StakeholderGraph.ToGraph(s)),
                    Attachments = domain.Attachments?.Select(AttachmentGraph.ToGraph),
                    SettledToTransactionId = domain.SettledToTransactionId,
                    DebitNoteNumber = domain.DebitNoteNumber,
                    Fields = domain.Fields?.ToString(Formatting.None)
                }.PopulateSystemGraphFields(domain);
    }

    public class ObjectUnionGraphType : UnionGraphType
    {
        public ObjectUnionGraphType()
        {
            Name = "objectUnion";

            Type<PolicyGraphType>();
            Type<EndorsementGraphType>();
            Type<ClaimGraphType>();
            Type<ProposalGraphType>();
        }
    }

    public class TransactionGraphType : ObjectGraphType<TransactionGraph>
    {
        public TransactionGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IPolicyService policyService,
            IClaimService claimService,
            ICaseService caseService,
            ITransactionService transactionService,
            PermissionValidator permissionValidator)
        {
            Name = "transaction";
            Description = "A transaction";

            Field(t => t.Id, type: typeof(NonNullGraphType<IdGraphType>));
            Field(t => t.TransactionNumber, nullable: true);
            Field(t => t.Status, nullable: true);
            Field(t => t.Description, nullable: true);
            Field(t => t.Policy, type: typeof(PolicyGraphType));
            Field(t => t.Object, type: typeof(ObjectUnionGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.Policy?.Id != null)
                    {
                        string tenantId = context.GetTenantIdFromToken();

                        var dataLoader = accessor.Context.GetOrAddBatchLoader<string, Policy>("GetPolicies",
                            i => policyService.GetDictionaryAsync(tenantId, new PolicyWhere { Id_in = i?.ToList() }));

                        Policy policy = await dataLoader.LoadAsync(context.Source.Policy.Id);
                        if (policy == null)
                            return null;

                        var policyGraph = PolicyGraph.ToGraph(policy);

                        if (context.Source.EndorsementId == null)
                            return policyGraph;

                        return policyGraph.Endorsements?.FirstOrDefault(e => e.Id == context.Source.EndorsementId);
                    }

                    else if (context.Source.ClaimId != null)
                    {
                        string tenantId = context.GetTenantIdFromToken();

                        var dataLoader = accessor.Context.GetOrAddBatchLoader<string, Claim>("GetClaims",
                            i => claimService.GetDictionaryAsync(tenantId, new ClaimWhere { Id_in = i?.ToList() }));

                        Claim claim = await dataLoader.LoadAsync(context.Source.ClaimId);

                        return ClaimGraph.ToGraph(claim);
                    }

                    else if (context.Source.ProposalId != null)
                    {
                        string tenantId = context.GetTenantIdFromToken();

                        var dataLoader = accessor.Context.GetOrAddBatchLoader<string, Proposal>("GetCasesByProposalId",
                            i => caseService.GetProposalDictionaryAsync(tenantId, new CaseWhere { Proposals_contains = new ProposalWhere { Id_in = i?.ToList() } }));

                        Proposal proposal = await dataLoader.LoadAsync(context.Source.ProposalId);

                        return ProposalGraph.ToGraph(proposal);
                    }

                    return null;
                });
            Field(t => t.Subscription, type: typeof(SubscriptionGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.Subscription?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var dataLoader = accessor.Context.GetOrAddBatchLoader<string, Subscription>("GetSubscriptions",
                        i => transactionService.GetSubscriptionsDictionaryAsync(tenantId, new SubscriptionWhere { Id_in = i?.ToList() }));

                    Subscription subscription = await dataLoader.LoadAsync(context.Source.Subscription.Id);
                    return subscription == null
                        ? null
                        : SubscriptionGraph.ToGraph(subscription);
                });
            Field(t => t.Amount, nullable: true);
            Field(t => t.Type, nullable: true);
            Field(t => t.AccountCode, nullable: true);
            Field(t => t.AccountType, nullable: true);
            Field(t => t.FormattedTransaction, nullable: true)
                .Resolve(context =>
                    context.Source.Amount != null
                        ? ((context.Source.CurrencyCode != CurrencyCode.Undefined ? context.Source.CurrencyCode.ToString() : "") + context.Source.Amount.GetValueOrDefault().ToString("n2"))
                        : null);
            Field(t => t.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(t => t.Method, type: typeof(PaymentMethodEnumerationGraphType), nullable: true);
            Field(t => t.DateTime, type: typeof(DateTimeOffsetGraphType));
            Field(t => t.DueDateTime, type: typeof(DateTimeOffsetGraphType));
            Field(t => t.PostDateTime, type: typeof(DateTimeOffsetGraphType));
            Field(t => t.ProviderTransactionId, nullable: true);
            Field(t => t.ApprovalCode, nullable: true);
            Field(t => t.Notes, type: typeof(ListGraphType<NoteGraphType>));
            Field(t => t.Facts, type: typeof(ListGraphType<FactGraphType>));
            Field(t => t.Stakeholders, type: typeof(ListGraphType<StakeholderGraphType>));
            Field<BooleanGraphType>().Name("isSettled").Resolve(x => !string.IsNullOrEmpty(x.Source.SettledToTransactionId));
            Field(x => x.SettledToTransactionId, nullable: true);
            Field(x => x.DebitNoteNumber, nullable: true);
            Field(t => t.SettledTransactions, type: typeof(TransactionsGraphType))
                .GetPaginationArguments()
                .Resolve(context =>
                {
                    int? skip = context.GetArgument<int?>("skip");
                    int? first = context.GetArgument<int?>("limit");
                    SortGraph sort = context.GetArgument<SortGraph>("sort");

                    var where = new TransactionWhere { SettledToTransactionId = context.Source.Id };
                    return new TransactionsGraph { Where = where, Skip = skip, First = first, Sort = sort };
                });

            Field(t => t.Events, type: typeof(ListGraphType<EventLogGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    var dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, EventLog>(
                        "GetTransactionEvents",
                        async i => (await transactionService.GetEventsAsync(tenantId, new EventQuery { Ids = i?.ToList() })).ToLookup(e => e.RelatedId)
                    );

                    IEnumerable<EventLog> logs = await dataLoader.LoadAsync(context.Source.Id);

                    return logs.Select(EventLogGraph.ToGraph);
                });

            Field(p => p.Attachments, type: typeof(ListGraphType<AttachmentGraphType>), nullable: true);
            Field(p => p.Fields, nullable: true);
            Field(p => p.EndorsementId, nullable: true);
            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class ProcessTransactionInputGraphType : InputObjectGraphType<ProcessTransactionCommand>
    {
        public ProcessTransactionInputGraphType()
        {
            Name = "processTransactionInput";
            Description = "an input for processing a transaction";

            Field(t => t.ProviderToken);
            Field(t => t.Remark, nullable: true);
            Field(t => t.ReferralId, nullable: true);
        }
    }

    public class CreateTransactionInputGraphType : InputObjectGraphType<CreateTransactionCommand>
    {
        public CreateTransactionInputGraphType()
        {
            Name = "createTransactionInput";
            Description = "an input for creating a transaction";

            Field(t => t.TransactionNumber, nullable: true);
            Field(t => t.Amount);
            Field(t => t.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType), nullable: true);
            Field(t => t.Type, nullable: true);
            Field(t => t.PolicyId, nullable: true);
            Field(t => t.EndorsementId, nullable: true);
            Field(t => t.ClaimId, nullable: true);
            Field(t => t.ProposalId, nullable: true);
            Field(t => t.Description, nullable: true);
            Field(t => t.SettledToTransactionId, nullable: true);
            Field(t => t.DebitNoteNumber, nullable: true);
            Field(t => t.Status, type: typeof(TransactionStatusEnumerationGraphType));
            Field(t => t.Method, type: typeof(PaymentMethodEnumerationGraphType), nullable: true);
            Field(t => t.DateTime, type: typeof(DateTimeGraphType), nullable: true);
            Field(t => t.DueDateTime, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(t => t.PostDateTime, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(t => t.Fields, nullable: true);
        }
    }

    public class UpdateTransactionInputGraphType : InputObjectGraphType<UpdateTransactionCommand>
    {
        public UpdateTransactionInputGraphType()
        {
            Name = "updateTransactionInput";
            Description = "an input for creating a transaction";

            Field(t => t.TransactionNumber, nullable: true);
            Field(t => t.Amount, nullable: true);
            Field(t => t.Type, nullable: true);
            Field(t => t.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType), nullable: true);
            Field(t => t.PolicyId, nullable: true);
            Field(t => t.EndorsementId, nullable: true);
            Field(t => t.ClaimId, nullable: true);
            Field(t => t.TransactionId, nullable: true);
            Field(t => t.ProposalId, nullable: true);
            Field(t => t.Description, nullable: true);
            Field(t => t.SettledToTransactionId, nullable: true);
            Field(t => t.DebitNoteNumber, nullable: true);
            Field(t => t.Status, type: typeof(TransactionStatusEnumerationGraphType), nullable: true);
            Field(t => t.Method, type: typeof(PaymentMethodEnumerationGraphType), nullable: true);
            Field(t => t.DateTime, type: typeof(DateTimeGraphType), nullable: true);
            Field(t => t.DueDateTime, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(t => t.PostDateTime, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(t => t.Fields, nullable: true);
        }
    }

    public class TransactionStatusEnumerationGraphType : EnumerationGraphType<TransactionStatus>
    {
        public TransactionStatusEnumerationGraphType()
        {
            Name = "transactionStatusEnumeration";
            Description = "A list of all possible transactionStatuses";
        }
    }

    public class InitializeTenantTransactionsInputGraphType : InputObjectGraphType<InitializeTenantTransactionsCommand>
    {
        public InitializeTenantTransactionsInputGraphType()
        {
            Name = "initializeTenantTransactionsInput";

            Field(t => t.PaymentConfig, type: typeof(NonNullGraphType<PaymentConfigInputGraphType>));
        }
    }

    public class PaymentConfigInputGraphType : InputObjectGraphType<PaymentConfig>
    {
        public PaymentConfigInputGraphType()
        {
            Name = "paymentConfigInput";

            Field(p => p.ProviderId);
            Field(p => p.DokuMerchantId, nullable: true);
            Field(p => p.DokuSharedKey, nullable: true);
            Field(p => p.JetcoMerchantId, nullable: true);
            Field(p => p.JetcoMicroserviceBaseUrl, nullable: true);
            Field(p => p.StripeDestinatonFeeFlat, nullable: true);
            Field(p => p.StripeDestinatonFeeRatio, nullable: true);
            Field(p => p.StripeApiTestKey, nullable: true);
            Field(p => p.StripeApiLiveKey, nullable: true);
            Field(p => p.StripeDestinationUserId, nullable: true);
            Field(p => p.IsLiveMode, nullable: true);
        }
    }

    public class TransactionWhereInputGraphType : InputObjectGraphType<TransactionWhere>
    {
        public TransactionWhereInputGraphType()
        {
            Name = "transactionWhereInput";
            Description = "A transaction search filter";

            Field(f => f.Or, type: typeof(ListGraphType<TransactionWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<TransactionWhereInputGraphType>));

            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);

            Field(f => f.TransactionNumber, nullable: true);
            Field(f => f.TransactionNumber_contains, nullable: true);
            Field(f => f.StakeholderIds_contains, nullable: true);
            Field(f => f.PolicyId, nullable: true);
            Field(f => f.PolicyId_in, nullable: true);
            Field(f => f.Type, nullable: true);
            Field(f => f.PaymentProviderId, nullable: true);

            Field(f => f.Status, type: typeof(TransactionStatusEnumerationGraphType));
            Field(f => f.DateTime_gt, type: typeof(DateTimeGraphType));
            Field(f => f.DateTime_lt, type: typeof(DateTimeGraphType));
            Field(f => f.IsSettled, nullable: true);
            Field(t => t.SettledToTransactionId, nullable: true);
            Field(t => t.DebitNoteNumber, nullable: true);
            Field(t => t.HasDebitNoteNumber, nullable: true);
            Field(t => t.Fields, type: typeof(FieldsWhereInputGraphType), nullable: true);
            Field(t => t.EndorsementId, nullable: true);
            Field(t => t.EndorsementId_in, nullable: true, typeof(ListGraphType<StringGraphType>));
        }
    }

    public class PaymentMethodGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public string Name { get; set; }

        public static PaymentMethodGraph ToGraph(PaymentMethod domain)
        {
            if (domain == null)
                return null;

            if (domain.Type == "card")
            {
                return new CardPaymentMethodGraph
                {
                    Brand = domain.Brand,
                    Last4 = domain.Last4,
                    ExpMonth = domain.ExpMonth,
                    ExpYear = domain.ExpYear,
                    CardholderName = domain.CardholderName
                }.PopulatePaymentMethodGraphFields(domain).PopulateSystemGraphFields(domain);
            }

            if (domain.Type == "bank")
            {
                return new BankPaymentMethodGraph
                {
                    BankName = domain.BankName,
                    BranchCode = domain.BranchCode,
                    BankNumber = domain.BankNumber,
                    AccountHolderName = domain.AccountHolderName,
                }.PopulatePaymentMethodGraphFields(domain).PopulateSystemGraphFields(domain);
            }
            
            if (domain.Type == "token")
                return new TokenPaymentMethodGraph().PopulatePaymentMethodGraphFields(domain).PopulateSystemGraphFields(domain);

            else return null;
        }
    }

    public class PaymentMethodInterfaceGraphType : InterfaceGraphType<PaymentMethodGraph>
    {
        public PaymentMethodInterfaceGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "paymentMethodInterface";
            Description = "a payment method interface";

            this.PopulatePaymentMethodGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class CardPaymentMethodGraphType : ObjectGraphType<CardPaymentMethodGraph>
    {
        public CardPaymentMethodGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "cardPaymentMethod";

            this.PopulatePaymentMethodGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.Brand, nullable: true);
            Field(c => c.ExpMonth, nullable: true);
            Field(c => c.ExpYear, nullable: true);
            Field(c => c.Last4, nullable: true);
            Field(c => c.CardholderName, nullable: true);

            Interface<PaymentMethodInterfaceGraphType>();
        }
    }

    public class CardPaymentMethodGraph : PaymentMethodGraph
    {
        public string Brand { get; set; }
        public string Last4 { get; set; }
        public int ExpMonth { get; set; }
        public int ExpYear { get; set; }
        public string CardholderName { get; set; }
    }
    public class BankPaymentMethodGraphType : ObjectGraphType<BankPaymentMethodGraph>
    {
        public BankPaymentMethodGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "bankPaymentMethod";

            this.PopulatePaymentMethodGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.BankName, nullable: true);
            Field(c => c.BranchCode, nullable: true);
            Field(c => c.BankNumber, nullable: true);
            Field(c => c.AccountHolderName, nullable: true);

            Interface<PaymentMethodInterfaceGraphType>();
        }
    }

    public class BankPaymentMethodGraph : PaymentMethodGraph
    {
        public string BankName { get; set; }
        public string BranchCode { get; set; }
        public string BankNumber { get; set; }
        public string AccountHolderName { get; set; }
    }
    
    public class TokenPaymentMethodGraph : PaymentMethodGraph
    { }
    
    public class TokenPaymentMethodGraphType : ObjectGraphType<TokenPaymentMethodGraph>
    {
        public TokenPaymentMethodGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "tokenPaymentMethod";

            this.PopulatePaymentMethodGraphTypeFields(accessor, authService, permissionValidator);

            Interface<PaymentMethodInterfaceGraphType>();
        }
    }

    public class CardPaymentMethodInputGraphType : InputObjectGraphType<CreateCardPaymentMethodCommand>
    {
        public CardPaymentMethodInputGraphType()
        {
            Name = "cardPaymentMethodInput";

            Field(c => c.Name, nullable: true);
            Field(c => c.Brand, nullable: true);
            Field(c => c.Number);
            Field(c => c.ExpMonth);
            Field(c => c.ExpYear);
            Field(c => c.Cvc);
            Field(c => c.CardholderName, nullable: true);
        }
    }

    public class BankPaymentMethodInputGraphType : InputObjectGraphType<CreateBankPaymentMethodCommand>
    {
        public BankPaymentMethodInputGraphType()
        {
            Name = "bankPaymentMethodInput";

            Field(c => c.BankName);
            Field(c => c.BranchCode, nullable: true);
            Field(c => c.BankNumber);
            Field(c => c.AccountHolderName);
        }
    }

    public class SubscriptionGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public IEnumerable<OfferGraph> Offers { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public Interval Interval { get; set; }
        public int? IntervalCount { get; set; }
        public PaymentMethodGraph PaymentMethod { get; set; }
        public bool IsInvalidToBeProcessed { get; set; }
        public bool IsCancelled { get; set; }
        public IEnumerable<TransactionGraph> Transactions { get; set; }
        public List<DateTime> PaymentDates { get; set; }
        public IEnumerable<SubscriptionDiscountGraph> Discounts { get; set; }
        public LoginGraph CancelledBy { get; set; }

        public static SubscriptionGraph ToGraph(Subscription domain) => domain == null
            ? null
            : new SubscriptionGraph
            {
                Id = domain.Id,
                Offers = domain.Offers?.Select(d => OfferGraph.ToGraph(d)),
                StartDate = domain.StartDate,
                EndDate = domain.EndDate,
                Interval = domain.Interval,
                IntervalCount = domain.IntervalCount,
                PaymentMethod = domain.PaymentMethodId != null ? new PaymentMethodGraph { Id = domain.PaymentMethodId } : null,
                PaymentDates = domain.PaymentDates,
                Discounts = domain.Discounts?.Select(d => SubscriptionDiscountGraph.ToGraph(d)),
                IsInvalidToBeProcessed = domain.IsInvalidToBeProcessed,
                IsCancelled = domain.IsCancelled,
                CancelledBy = new LoginGraph { Id = domain.CancelledById }
            }.PopulateSystemGraphFields(domain);
    }

    public class SubscriptionGraphType : ObjectGraphType<SubscriptionGraph>
    {
        public SubscriptionGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            ITransactionService transactionService,
            PermissionValidator permissionValidator
            )
        {
            Name = "subscription";

            Field(s => s.Id, nullable: true);
            Field(s => s.Offers, type: typeof(ListGraphType<OfferGraphType>));
            Field(s => s.StartDate, type: typeof(DateTimeGraphType));
            Field(s => s.EndDate, type: typeof(DateTimeGraphType));
            Field(s => s.Interval, type: typeof(IntervalEnumerationGraphType));
            Field(s => s.IntervalCount, nullable: true);
            Field(s => s.PaymentMethod, type: typeof(PaymentMethodInterfaceGraphType)).ResolveAsync(async context =>
            {
                if (context.Source.PaymentMethod?.Id == null)
                    return null;

                string tenantId = context.GetTenantIdFromToken();
                IEnumerable<string> paymentMethodIds = await permissionValidator.GetTargetIdsFromClaim(context,"readPaymentMethods");

                var loader = accessor.Context.GetOrAddBatchLoader<string, PaymentMethod>("GetPaymentMethods",
                    paymentMethodId => transactionService.GetPaymentMethodDictionaryAsync(tenantId, paymentMethodIds.Contains("all")
                        ? new PaymentMethodWhere { Id_in = paymentMethodId?.ToList() }
                        : new PaymentMethodWhere
                        {
                            And = new List<PaymentMethodWhere>
                            {
                                new() { Id_in = paymentMethodId?.ToList() },
                                new() { Id_in = paymentMethodIds.ToList() }
                            }
                        }));

                PaymentMethod paymentMethod = await loader.LoadAsync(context.Source.PaymentMethod.Id);

                return PaymentMethodGraph.ToGraph(paymentMethod);
            });
            Field(s => s.Transactions, type: typeof(ListGraphType<TransactionGraphType>)).ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();
                IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readTransactions");

                var loader = accessor.Context.GetOrAddCollectionBatchLoader<string, Transaction>(
                    "GetTransactionsFromSubscriptionIds",
                    subscriptionIds => transactionService.GetBySubscriptionIdsLookupAsync(tenantId, allowedIds.Contains("all")
                    ? new TransactionWhere { SubscriptionId_in = subscriptionIds?.ToList() }
                    : new TransactionWhere
                    {
                        And = new List<TransactionWhere>
                        {
                                new() { SubscriptionId_in = subscriptionIds?.ToList() },
                                new() { Id_in = allowedIds.ToList() }
                        }
                    }));

                IEnumerable<Transaction> transactions = await loader.LoadAsync(context.Source.Id);

                return transactions?.Select(l => TransactionGraph.ToGraph(l));
            });
            Field(s => s.PaymentDates, type: typeof(ListGraphType<DateTimeGraphType>));
            Field(s => s.Discounts, type: typeof(ListGraphType<SubscriptionDiscountGraphType>));
            Field(s => s.IsInvalidToBeProcessed);
            Field(s => s.IsCancelled);
            Field(s => s.CancelledBy, type: typeof(LoginGraphType))
               .ResolveAsync(async context =>
               {
                   if (context.Source.CancelledBy?.Id == null)
                       return null;

                   IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,"readLogins", new List<string> { context.Source.CancelledBy.Id });
                   if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.CancelledBy.Id))
                       return null;

                   string tenantId = context.GetTenantIdFromToken();

                   var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                       i => authService.GetLoginsDictionaryAsync(tenantId, i));

                   Login loginDao = await loginLoader.LoadAsync(context.Source.CancelledBy.Id);

                   return LoginGraph.ToGraph(loginDao);
               });

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class IntervalEnumerationGraphType : EnumerationGraphType<Interval>
    {
        public IntervalEnumerationGraphType()
        {
            Name = "intervalType";
        }
    }

    public class CreateSubscriptionInputGraphType : InputObjectGraphType<CreateSubscriptionCommand>
    {
        public CreateSubscriptionInputGraphType()
        {
            Name = "createSubscriptionInput";

            Field(s => s.Offers, type: typeof(NonNullGraphType<ListGraphType<AddOfferInputGraphType>>));
            Field(p => p.StartDate, type: typeof(DateTimeGraphType), nullable: true);
            Field(p => p.EndDate, type: typeof(DateTimeGraphType), nullable: true);
            Field(c => c.Interval, type: typeof(NonNullGraphType<IntervalEnumerationGraphType>));
            Field(c => c.IntervalCount, nullable: true);
        }
    }

    public class UpdateSubscriptionInputGraphType : InputObjectGraphType<UpdateSubscriptionCommand>
    {
        public UpdateSubscriptionInputGraphType()
        {
            Name = "updateSubscriptionInput";

            Field(p => p.EndDate, type: typeof(DateTimeGraphType), nullable: true);
            Field(c => c.PaymentMethodId, nullable: true);
            Field(c => c.IsInvalidToBeProcessed, nullable: true);
        }
    }

    public class SubscriptionDiscountGraph : SystemObjectGraph
    {
        public DateTime Date { get; set; }
        public Interval Interval { get; set; }
        public decimal DiscountAmount { get; set; }

        public static SubscriptionDiscountGraph ToGraph(SubscriptionDiscount domain) => domain == null
            ? null
            : new SubscriptionDiscountGraph
            {
                Date = domain.Date,
                Interval = domain.Interval,
                DiscountAmount = domain.DiscountAmount,
            };
    }

    public class SubscriptionDiscountGraphType : ObjectGraphType<SubscriptionDiscountGraph>
    {
        public SubscriptionDiscountGraphType()
        {
            Name = "subscriptionDiscount";

            Field(p => p.Date, type: typeof(DateTimeGraphType), nullable: true);
            Field(c => c.DiscountAmount, nullable: true);
            Field(c => c.Interval, type: typeof(IntervalEnumerationGraphType), nullable: true);
        }
    }

    public static class TemplateExtensions
    {
        public static PaymentMethodGraph PopulatePaymentMethodGraphFields(this PaymentMethodGraph graph, PaymentMethod domain)
        {
            graph.Id = domain.Id;
            graph.Name = domain.Name;
            graph.EntityId = domain.EntityId;

            return graph;
        }

        public static void PopulatePaymentMethodGraphTypeFields<T>(
           this ComplexGraphType<T> graphType,
           IDataLoaderContextAccessor accessor,
           IAuthService authService,
           PermissionValidator permissionValidator)
           where T : PaymentMethodGraph
        {
            graphType.Field(c => c.Id);
            graphType.Field(c => c.Name, nullable: true);
            graphType.Field(c => c.EntityId, nullable: true);

            //graphType.Field(u => u.Events, type: typeof(ListGraphType<EventLogGraphType>));

            graphType.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }
}
