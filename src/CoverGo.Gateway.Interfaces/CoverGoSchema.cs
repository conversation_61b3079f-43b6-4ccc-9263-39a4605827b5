﻿using CoverGo.Gateway.Interfaces.Notifications;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Templates;
using CoverGo.Gateway.Interfaces.Transactions;
using CoverGo.Gateway.Interfaces.Types;
using CoverGo.Gateway.Interfaces.Users;

using GraphQL;
using GraphQL.Resolvers;
using GraphQL.Subscription;
using GraphQL.Types;

using System;
using System.Globalization;

namespace CoverGo.Gateway.Interfaces
{
    public class CoverGoSchema : Schema
    {
        public CoverGoSchema(IDependencyResolver resolver)
            : base(resolver)
        {
            Query = resolver.Resolve<CoverGoQuery>();
            Mutation = resolver.Resolve<CoverGoMutation>();
            Subscription = resolver.Resolve<CoverGoSubscriptions>();

            RegisterType<IndividualGraphType>();
            RegisterType<CompanyGraphType>();
            RegisterType<InternalGraphType>();
            RegisterType<ObjectGraphType>();

            RegisterType<PdfDrawingTemplateGraphType>();
            RegisterType<EmailMjmlTemplateGraphType>();
            RegisterType<SmsTemplateGraphType>();
            RegisterType<WkhtmltopdfTemplateGraphType>();
            RegisterType<ClauseHtmlTemplateGraphType>();
            RegisterType<DynamicTemplateGraphType>();
            RegisterType<FunctionTemplateGraphType>();
            RegisterType<NotificationTemplateGraphType>();

            RegisterType<CardPaymentMethodGraphType>();
            RegisterType<BankPaymentMethodGraphType>();
            RegisterType<TokenPaymentMethodGraphType>();
            RegisterType<ScriptGraphType>();
            RegisterType<CustomDateGraphType>();

            //this.Execute(_ => _.FieldMiddleware.Use<SortingMiddleware>());
            ValueConverter.Register(
                typeof(long),
                typeof(double),
                value => Convert.ToDouble((long)value, NumberFormatInfo.InvariantInfo));

            ValueConverter.Register(
                typeof(long),
                typeof(decimal),
                value => Convert.ToDecimal((long)value, NumberFormatInfo.InvariantInfo));

            RegisterValueConverter(new ByteValueConverter());
            RegisterValueConverter(new JsonValueConverter());
        }
    }

    //public class SortingMiddleware
    //{
    //    public Task<object> Resolve(
    //      ResolveFieldContext context,
    //      FieldMiddlewareDelegate next)
    //    {
    //        var metadata = new Dictionary<string, object>
    //        {
    //          {"typeName", context.ParentType.Name},
    //          {"fieldName", context.FieldName}
    //        };

    //        using (context.Metrics.Subject("field", context.FieldName, metadata))
    //        {
    //            return next(context);
    //        }
    //    }
    //}\

    public class CoverGoSubscriptions : ObjectGraphType<object>
    {
        public CoverGoSubscriptions()
        {
            AddField(new EventStreamFieldType
            {
                Name = "test",
                Type = typeof(StringGraphType),
                Resolver = new FuncFieldResolver<string>(ResolveMessage),
                Subscriber = new EventStreamResolver<string>(Subscribe)
            });
        }

        private string ResolveMessage(ResolveFieldContext context)
        {
            string message = context.Source as string;
            return message;
        }

        private IObservable<string> Subscribe(ResolveEventStreamContext context) => throw new NotImplementedException();
    }
}
