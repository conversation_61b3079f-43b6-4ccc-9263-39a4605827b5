﻿using System.Linq;

namespace CoverGo.Gateway.Interfaces
{
    public class ForgotPasswordSettings
    {
        public ForgotPasswordSettingValues[] Values { get; set; }
    }

    public class ForgotPasswordSettingValues
    {
        public string TenantId { get; set; }
        public string ClientId { get; set; }
        public string From { get; set; }
        public string FromName { get; set; }
        public string Subject { get; set; }
        public string TemplateId { get; set; }
        public string Link { get; set; }
    }

    public static class ForgotPasswordSettingsExtensions
    {
        public static ForgotPasswordSettingValues GetSettingsByTenantId(this ForgotPasswordSettingValues[] values, string tenantId, string clientId)
        {
            return values.SingleOrDefault(x => x.TenantId == tenantId && x.ClientId == clientId);
        }
    }
}
