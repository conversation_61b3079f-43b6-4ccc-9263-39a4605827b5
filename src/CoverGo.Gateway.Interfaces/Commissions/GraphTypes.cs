﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Commissions;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Users;

using GraphQL.DataLoader;
using GraphQL.Types;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Gateway.Interfaces.Commissions
{
    public class CommissionRulesGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<CommissionRuleGraph> List { get; set; }
    }

    public class CommissionRuleInputGraphType : InputObjectGraphType<CreateCommissionRuleCommand>
    {
        public CommissionRuleInputGraphType()
        {
            Name = "commissionRuleInput";
            Description = "Commission rule input";

            Field(c => c.Name, nullable: true);
            Field(c => c.Description, nullable: true);
            Field(c => c.JsonRule);
            Field(c => c.ProductId, type: typeof(ProductIdInputGraphType));
        }
    }

    public class CommissionRuleGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string JsonRule { get; set; }
        public ProductGraph Product { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public IEnumerable<EntityGraph> Entities { get; set; }

        public static CommissionRuleGraph ToGraph(CommissionRule p)
        {
            if (p == null)
                return null;

            CommissionRuleGraph graph = new CommissionRuleGraph
            {
                Id = p.Id,
                JsonRule = p.JsonRule,
                Product = new ProductGraph { ProductId = p.ProductId },
                Entities = p.EntityIds?.Select(i => new EntityGraph { Id = i }),
                Name = p.Name,
                Description = p.Description,
                CreatedAt = p.CreatedAt,
                LastModifiedAt = p.LastModifiedAt,
                CreatedBy = new Auth.LoginGraph { Id = p.CreatedById },
                LastModifiedBy = new Auth.LoginGraph { Id = p.LastModifiedById }
            }.PopulateSystemGraphFields(p);

            return graph;
        }
    }

    public class CommissionRuleGraphType : ObjectGraphType<CommissionRuleGraph>
    {
        public CommissionRuleGraphType(IDataLoaderContextAccessor accessor, IAuthService authService, PermissionValidator permissionValidator)
        {
            Name = "commissionRule";

            Field(cr => cr.Id);
            Field(cr => cr.JsonRule);
            Field(cr => cr.Name, nullable: true);
            Field(cr => cr.Description, nullable: true);
            Field(cr => cr.Entities, type: typeof(ListGraphType<EntityInterfaceGraphType>));
            Field(cr => cr.Product, type: typeof(ProductGraphType));

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class CommissionRuleWhereInputGraphType : InputObjectGraphType<CommissionRuleWhere>
    {
        public CommissionRuleWhereInputGraphType()
        {
            Name = "commissionRuleWhereInput";
            Description = "A commission rule search filter";

            Field(f => f.Or, type: typeof(ListGraphType<CommissionRuleWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<CommissionRuleWhereInputGraphType>));

            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);
            Field(f => f.ProductId, type: typeof(ProductIdWhereInputGraphType), nullable: true);
            Field(f => f.EntityId, nullable: true);

            this.PopulateSystemWhereFields();
        }
    }

    public class FunctionGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public Engine Engine { get; set; }
        public IEnumerable<Input> Inputs { get; set; }
        public IEnumerable<Output> Outputs { get; set; }

        public static FunctionGraph ToGraph(Function domain) =>
            domain == null
            ? null
            : new FunctionGraph
            {
                Id = domain.Id,
                Engine = domain.Engine,
                Inputs = domain.Inputs,
                Outputs = domain.Outputs
            }.PopulateSystemGraphFields(domain);
    }

    public class FunctionGraphType : ObjectGraphType<FunctionGraph>
    {
        public FunctionGraphType()
        {
            Name = "function";

            Field(c => c.Id, nullable: true);
            Field(c => c.Engine, type: typeof(EngineGraphType));
            Field(c => c.Inputs, type: typeof(ListGraphType<FunctionInputGraphType>));
            Field(c => c.Outputs, type: typeof(ListGraphType<FunctionOutputGraphType>));
        }
    }

    public class FunctionRenderOutputGraphType : ObjectGraphType<Result<IEnumerable<OutputGraph>>>
    {
        public FunctionRenderOutputGraphType()
        {
            Name = "functionRenderOutput";
            Description = "A result object with a created status object and potential errors";

            Field(r => r.Status, nullable: true);
            Field(r => r.Errors, type: typeof(ListGraphType<StringGraphType>));
            Field(r => r.Value, type: typeof(ListGraphType<FunctionOutputGraphType>));
        }
    }

    public class CreateFunctionInputGraphType : InputObjectGraphType<CreateFunctionCommand>
    {
        public CreateFunctionInputGraphType()
        {
            Name = "createFunctionInput";
            Field(c => c.Engine, type: typeof(EngineInputGraphType));
            Field(c => c.Inputs, type: typeof(ListGraphType<FunctionInputInputGraphType>));
            Field(c => c.Outputs, type: typeof(ListGraphType<FunctionOutputInputGraphType>));
        }
    }

    //public class EngineGraph
    //{
    //    public string Id { get; set; }
    //    public string Name { get; set; } //should maybe be localized?
    //    public string RunTime { get; set; } //is only 'excelTemplate` for now
    //    public string Version { get; set; } //maybe not needed as these can be on the template
    //    //public TemplateGraph Template { get; set; } //only for excelTemplate

    //    public static EngineGraph ToGraph(Engine domain) =>
    //        domain == null
    //        ? null
    //        : new EngineGraph
    //        {
    //            Id = domain.Id,
    //            Name = domain.Name,
    //            RunTime = domain.RunTime,
    //            Version = domain.Version,
    //            Template = new TemplateGraph { Id = domain.TemplateId },
    //        };
    //}

    public class EngineInputGraph
    {
        public string Id { get; set; }
        public string Name { get; set; } //should maybe be localized?
        public string RunTime { get; set; } //is only 'excelTemplate` for now
        public string Version { get; set; } //maybe not needed as these can be on the template
        public string TemplateId { get; set; } //only for excelTemplate
    }

    public class EngineGraphType : ObjectGraphType<Engine>
    {
        public EngineGraphType()
        {
            Name = "engine";

            Field(c => c.Id, nullable: true);
            Field(c => c.Name, nullable: true);
            Field(c => c.RunTime, nullable: true);
            Field(c => c.Version, nullable: true);
            //Field(c => c.Template, type: typeof(TemplateInterfaceGraphType));
        }
    }

    public class EngineInputGraphType : InputObjectGraphType<Engine>
    {
        public EngineInputGraphType()
        {
            Name = "engineInput";

            Field(c => c.Id, nullable: true);
            Field(c => c.Name, nullable: true);
            Field(c => c.RunTime, nullable: true);
            Field(c => c.Version, nullable: true);
            //Field(c => c.TemplateId, nullable: true);
        }
    }

    public class OutputGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Var { get; set; } //for excel, this is cell address mapping
        public string ValueJsonString { get; set; }

        public static OutputGraph ToGraph(Output domain) =>
            domain == null
            ? null
            : new OutputGraph
            {
                Id = domain.Id,
                Name = domain.Name,
                Var = domain.Var,
                ValueJsonString = domain.Value?.ToString()
            };
    }

    public class FunctionOutputGraphType : ObjectGraphType<OutputGraph>
    {
        public FunctionOutputGraphType()
        {
            Name = "functionOutput";

            Field(c => c.Id, nullable: true);
            Field(c => c.Name, nullable: true);
            Field(c => c.Var, nullable: true);
            Field(c => c.ValueJsonString, nullable: true);
        }
    }

    public class FunctionOutputInputGraphType : InputObjectGraphType<Output>
    {
        public FunctionOutputInputGraphType()
        {
            Name = "functionOutputInput";

            Field(c => c.Name, nullable: true);
            Field(c => c.Var, nullable: true);
        }
    }

    public class FunctionInputGraphType : ObjectGraphType<Input>
    {
        public FunctionInputGraphType()
        {
            Name = "functionInput";

            Field(c => c.Id, nullable: true);
            Field(c => c.Name, nullable: true);
            Field(c => c.Type, nullable: true);
            Field(c => c.Liquid, nullable: true);
            Field(c => c.Var, nullable: true);
        }
    }

    public class FunctionInputInputGraphType : InputObjectGraphType<Input>
    {
        public FunctionInputInputGraphType()
        {
            Name = "functionInputInput";

            Field(c => c.Name, nullable: true);
            Field(c => c.Type, nullable: true);
            Field(c => c.Liquid, nullable: true);
            Field(c => c.Var, nullable: true);
        }
    }
}
