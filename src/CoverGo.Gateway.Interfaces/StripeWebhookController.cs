﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Transactions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Stripe;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Users;
using Newtonsoft.Json.Linq;
using CurrencyCode = CoverGo.Gateway.Domain.CurrencyCode;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Interfaces
{
    namespace workspace.Controllers
    {
        [ApiExplorerSettings(IgnoreApi = true)]
        [Route("api/v1/stripe")]
        public class StripeWebhookController : ControllerBase
        {
            private readonly ITransactionService _transactionService;
            private readonly ICaseService _caseService;
            private readonly IPolicyService _policyService;
            private readonly ILogger<StripeWebhookController> _logger;
            private IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> _entityService;
            public string CovergoApiTestKey = "sk_test_3cTCcTh7QqyKp5KLhjCB4zbs";
            public string CovergoApiLiveKey = "********************************";
            public List<string> _tenantIdsToTriggerRenewalWithNewPolicy = new()
            {
                "worldwideDelight_uat",
                "worldwideDelight"
            };

            public StripeWebhookController(ITransactionService transactionService, ICaseService caseService, IPolicyService policyService, ILogger<StripeWebhookController> logger
            , IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> entityService)
            {
                _transactionService = transactionService;
                _caseService = caseService;
                _policyService = policyService;
                _logger = logger;
                _entityService = entityService;
            }

            [HttpPost]
            public async Task<IActionResult> HandleEvents([FromBody] Event stripeEvent) //for targeting our stripe
            {
                try
                {
                    _logger.LogInformation("Handling stripe webhook event with id {StripeEventId} and type {StripeEventType}", stripeEvent.Id, stripeEvent.Type);
                    //if (stripeEvent.Request.Id != null) //this means the event was triggered by an api call by us and not automatic, maybe unnecessary
                    //    return Ok();

                    // Handle the event
                    if (stripeEvent.Type == Events.InvoiceCreated)
                    {
                        string apiKey = stripeEvent.Livemode ? CovergoApiLiveKey : CovergoApiTestKey;

                        Result result = await HandleInvoiceCreatedEventAsync(null, apiKey, stripeEvent);
                        if (result.Status != "success")
                            return BadRequest();
                    }

                    else if (stripeEvent.Type == Events.InvoicePaymentSucceeded)
                    {
                        string apiKey = stripeEvent.Livemode ? CovergoApiLiveKey : CovergoApiTestKey;
                        var stripeClient = new StripeClient(apiKey);

                        Result result = await HandleInvoicePaymentSucceededEventAsync(null, apiKey, stripeEvent);
                        if (result.Status != "success")
                            return BadRequest();

                        //transfer appropriate amount to connected account
                        //Result transferResult = await _transactionService.
                    }

                    //TODO: needs spike
                    //if (stripeEvent.Type == Events.InvoicePaymentFailed)
                    //{
                    //    var invoice = stripeEvent.Data.Object as Invoice;
                    //    string chargeId = invoice.ChargeId;
                    //    Stripe.Subscription subscription = invoice.Subscription;

                    //    string tenantId = subscription.Metadata.FirstOrDefault(d => d.Key == "tenantId").Value;

                    //    var updateTransactionCommand = new UpdateTransactionCommand
                    //    {
                    //        Status = TransactionStatus.Rejected,
                    //        IsStatusChanged = true,
                    //        ProviderTransactionId = chargeId,
                    //        IsProviderTransactionIdChanged = true,
                    //        ProviderId = "stripe",
                    //        IsProviderIdChanged = true
                    //    };

                    //    //record transaction as failed
                    //    Result updateResult = await _transactionService.UpdateAsync(tenantId, updateTransactionCommand);

                    //    //set subscription as unpaid
                    //    //var updateSubscriptionCommand = new UpdateSubscriptionCommand { IsUnpaid = true, IsIsUnpaidChanged = true };

                    //    //Result updateSubscriptionResult = await _transactionService.UpdateAsync(tenantId, updateTransactionCommand);
                    //}

                    else
                    {
                        // Unexpected event type
                        _logger.LogWarning("Unexpected stripe webhook event type received");
                        return Ok();
                    }
                    return Ok();
                }
                catch (StripeException e)
                {
                    _logger.LogError($"The stripe webhook event handling failed ||| {e}", e.StripeError.Message);
                    return BadRequest();
                }
            }

            [HttpPost("{tenantId}/{configId}")]
            public async Task<IActionResult> HandleDirectEvents(string tenantId, string configId, [FromBody] Event stripeEvent)
            {
                try
                {
                    _logger.LogInformation("Handling direct stripe webhook event with id {StripeEventId} and type {StripeEventType} for tenant {Tenant}", stripeEvent.Id, stripeEvent.Type, tenantId);
                    // Handle the event
                    if (stripeEvent.Type == Events.InvoiceCreated)
                    {
                        PaymentConfig config = await _transactionService.GetPaymentConfigAsync(tenantId, configId);
                        string apiKey = stripeEvent.Livemode ? config.StripeApiLiveKey : config.StripeApiTestKey;

                        Result result = await HandleInvoiceCreatedEventAsync(tenantId, apiKey, stripeEvent);
                        if (result.Status != "success")
                            return BadRequest();
                    }

                    else if (stripeEvent.Type == Events.InvoicePaymentSucceeded)
                    {
                        var invoice = stripeEvent.Data.Object as Invoice;

                        PaymentConfig config = await _transactionService.GetPaymentConfigAsync(tenantId, configId);
                        string apiKey = stripeEvent.Livemode ? config.StripeApiLiveKey : config.StripeApiTestKey;

                        Result result = await HandleInvoicePaymentSucceededEventAsync(null, apiKey, stripeEvent);
                        if (result.Status != "success")
                            return BadRequest();
                    }

                    else
                    {
                        // Unexpected event type
                        _logger.LogWarning("Unexpected direct stripe webhook event type received");
                        return Ok();
                    }
                    return Ok();
                }
                catch (StripeException e)
                {
                    _logger.LogError($"The stripe webhook event handling failed ||| {e}", e.StripeError.Message);
                    return BadRequest();
                }
            }



            [HttpPost("HandleCustomerEvents/{tenantId}/{configId}")]
            public async Task<IActionResult> HandleCustomerEvents(string tenantId, string configId, CancellationToken token)
            {
                string json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
                _logger.LogInformation("Handling direct stripe webhook event with configid {configId}  for tenant {Tenant} event Signature {eventSignature} DateTime {DateTime} request body {body}", configId, tenantId, Request.Headers["Stripe-Signature"], DateTime.Now, json);
                PaymentConfig config = await _transactionService.GetPaymentConfigAsync(tenantId, configId);
                string webHookKey = config.IsLiveMode ? config.StripeLiveWebHookKey : config.StripeTestWebHookKey;
                string apiKey = config.IsLiveMode
                    ? config.StripeApiLiveKey
                    : config.StripeApiTestKey;


                try
                {
                    Event stripeEvent = EventUtility.ConstructEvent(
                        json,
                        Request.Headers["Stripe-Signature"],
                        webHookKey
                    );

                    switch (stripeEvent.Type)
                    {
                        // Handle the event
                        case Events.CustomerSubscriptionCreated:
                        {
                            Result result = await HandleCustomerSubscription(tenantId, stripeEvent, apiKey, token);
                            if (result.Status != "success")
                            {
                                _logger.LogInformation("Customer result log {result}", result);
                                return BadRequest();
                            }
                               
                            break;
                        }
                        case Events.CustomerCreated:
                        {
                            Result result = await HandleCustomerCreation(tenantId, stripeEvent);
                            if (result.Status != "success")
                            {
                                _logger.LogInformation("Customer result log {result}", result);
                                return BadRequest();
                            }
                                
                            break;
                        }
                        case Events.CustomerSubscriptionUpdated:
                        {
                            var subscription = stripeEvent.Data.Object as Stripe.Subscription;
                            return Ok();
                        }

                        case Events.CheckoutSessionCompleted:
                        {
                            dynamic rawObject = stripeEvent.Data.RawObject;
                            string userId = rawObject.SelectToken("metadata").userId.Value;
                            string subscriptionId = rawObject.SelectToken("subscription").Value;
                            Result result = await UpdateSubscription(tenantId, apiKey, subscriptionId, userId);
                            if (result.Status != "success")
                            {
                                _logger.LogInformation("Customer result log {result}", result);
                                return BadRequest();
                            }
                              
                            break;
                        }
                        default:
                            // Unexpected event type
                            Console.WriteLine("Unhandled event type: {0}", stripeEvent.Type);
                            break;
                    }
                    return Ok();
                }
                catch (StripeException e)
                {
                    _logger.LogError($"The stripe webhook event handling failed ||| {e}", e);
                    return BadRequest();
                }
            }

            private async Task<Result> HandleInvoiceCreatedEventAsync(string tenantId, string apiKey, Event stripeEvent)
            {
                var invoice = stripeEvent.Data.Object as Invoice;
                var stripeClient = new StripeClient(apiKey);
                var subscriptionService = new SubscriptionService(stripeClient);
                Stripe.Subscription subscription = await subscriptionService.GetAsync(invoice.SubscriptionId);
                if (tenantId == null)
                {
                    tenantId = subscription.Metadata.FirstOrDefault(d => d.Key == "tenantId").Value;
                    if (tenantId == null)
                    {
                        _logger.LogError($"The stripe webhook event handling for event `{stripeEvent.Id}` failed | No tenantId was found in metadata for the subscription '{invoice.SubscriptionId}' | {JsonConvert.SerializeObject(stripeEvent)}");
                        return Result.Failure($"No tenantId was found in metadata for the subscription '{invoice.SubscriptionId}'");
                    }
                }

                string policyIdReference = subscription.Items?.Select(i => i.Metadata?.FirstOrDefault(f => f.Key == "policyId").Value)?.FirstOrDefault(); // refactor when there are multiple policies/ tied to proposal
                Domain.Transactions.Subscription existingSubscription = (await _transactionService.GetSubscriptionsAsync(tenantId, new QueryArguments
                {
                    Where = new SubscriptionWhere
                    {
                        ExternalRef = new ExternalRef
                        {
                            Ref = subscription.Id
                        }
                    }
                })).FirstOrDefault();

                if (existingSubscription == null)
                {
                    _logger.LogError($"The stripe webhook event handling for event `{stripeEvent.Id}` failed | subscription with external ref `{subscription.Id}` was not found for tenantId '{tenantId}' | {JsonConvert.SerializeObject(stripeEvent)}");
                    return Result.Failure($"No existing subscription for with external ref `{subscription.Id}`for tenantId '{tenantId}' was found.");
                }

                var createTransactionCommand = new CreateTransactionCommand
                {
                    Amount = Convert.ToDecimal(invoice.AmountDue) / 100,
                    Status = TransactionStatus.Pending,
                    CurrencyCode = Enum.Parse<CurrencyCode>(invoice.Currency.ToUpperInvariant()),
                    SubscriptionId = existingSubscription.Id,
                    PolicyId = policyIdReference,
                    ExternalRef = new ExternalRef
                    {
                        ProviderConfigId = existingSubscription.ExternalRef.ProviderConfigId
                    }
                };

                Result<CreatedStatus> createTransactionResult = await _transactionService.CreateAsync(tenantId, createTransactionCommand);

                var invoiceService = new InvoiceService(stripeClient);
                await invoiceService.UpdateAsync(invoice.Id, new InvoiceUpdateOptions { Metadata = new Dictionary<string, string> { { "transactionId", createTransactionResult.Value.Id } } });

                return Result.Success();
            }

            private async Task<Result> HandleInvoicePaymentSucceededEventAsync(string tenantId, string apiKey, Event stripeEvent)
            {
                try
                {
                    var invoice = stripeEvent.Data.Object as Invoice;
                    string transactionId = invoice.Metadata.FirstOrDefault(d => d.Key == "transactionId").Value;

                    var stripeClient = new StripeClient(apiKey);
                    if (transactionId == null)
                    {
                        var invoiceService = new InvoiceService(stripeClient);
                        int maxTries = 10;
                        for (int i = 0; i < maxTries; i++)
                        {
                            invoice = await invoiceService.GetAsync(invoice.Id);
                            transactionId = invoice.Metadata.FirstOrDefault(d => d.Key == "transactionId").Value;
                            if (transactionId != null)
                                break;
                            await Task.Delay(500);
                        };
                    }

                    var subscriptionService = new SubscriptionService(stripeClient);
                    Stripe.Subscription subscription = await subscriptionService.GetAsync(invoice.SubscriptionId);

                    string chargeId = invoice.ChargeId;
                    if (tenantId == null)
                    {
                        tenantId = subscription.Metadata.FirstOrDefault(d => d.Key == "tenantId").Value;
                        if (tenantId == null)
                        {
                            _logger.LogError($"The stripe webhook event handling for event `{stripeEvent.Id}` failed | No tenantId was found in metadata for the subscription '{invoice.SubscriptionId}' | {JsonConvert.SerializeObject(stripeEvent)}");
                            return Result.Failure($"No tenantId was found in metadata for the subscription '{invoice.SubscriptionId}'");
                        }
                    }

                    Transaction transaction = (await _transactionService.GetAsync(tenantId, new QueryArguments { Where = new TransactionWhere { Id = transactionId } })).FirstOrDefault();
                    if (transaction == null)
                        return Result.Failure($"Transaction '{transactionId}' not found.");

                    Domain.Transactions.Subscription existingSubscription = (await _transactionService.GetSubscriptionsAsync(tenantId, new QueryArguments
                    {
                        Where = new SubscriptionWhere
                        {
                            ExternalRef = new ExternalRef
                            {
                                Ref = transaction.SubscriptionId ?? subscription.Id
                            }
                        }
                    })).FirstOrDefault();

                    if (existingSubscription == null)
                    {
                        _logger.LogError($"The stripe webhook event handling for event `{stripeEvent.Id}` failed | subscription with external ref `{subscription.Id}` was not found for tenantId '{tenantId}' | {JsonConvert.SerializeObject(stripeEvent)}");
                        return Result.Failure($"No existing subscription for with external ref `{subscription.Id}`for tenantId '{tenantId}' was found.");
                    }


                    UpdateTransactionCommand updateTransactionCommand = new()
                    {
                        TransactionId = transactionId,
                        Status = TransactionStatus.Approved,
                        IsStatusChanged = true,
                        ProviderTransactionId = chargeId,
                        IsProviderTransactionIdChanged = true,
                        ProviderId = "stripe",
                        IsProviderIdChanged = true,
                        ExternalRef = new ExternalRefToUpdate
                        {
                            Ref = chargeId,
                            IsRefChanged = true
                        },
                        IsExternalRefChanged = true
                    };
                    Result updateTransactionResult = await _transactionService.UpdateAsync(tenantId, updateTransactionCommand);
                    updateTransactionResult.ThrowIfNotSuccess();

                    if (_tenantIdsToTriggerRenewalWithNewPolicy.Contains(tenantId))
                        await TriggerRenewalWithNewPolicy(tenantId, transaction);

                    return updateTransactionResult;


                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to handle stripe payment event for stripe event {stripeEventId} ||| Message: {message} ", stripeEvent.Id, ex.Message);
                    return Result.Failure(ex.Message);
                }

                async Task<Result> TriggerRenewalWithNewPolicy(string tenantId, Transaction transaction)
                {
                    Result<CreatedStatus> createRenewalPolicyResult = await CreateRenewalProposalAndPolicy();
                    string renewedPolicyId = createRenewalPolicyResult.Value.Ids.FirstOrDefault();
                    await _transactionService.UpdateAsync(tenantId, new UpdateTransactionCommand
                    {
                        TransactionId = transaction.Id,
                        PolicyId = renewedPolicyId,
                        IsPolicyIdChanged = true
                    });

                    Result<PolicyStatus> issuePolicyResult = await _policyService.IssuePolicyAsync(tenantId, new IssuePolicyCommand { PolicyId = renewedPolicyId, IgnoreIssuanceValidation = true, IgnoreCollateralValidation = true });
                    issuePolicyResult.ThrowIfNotSuccess();

                    return Result.Success();

                    async Task<Result<CreatedStatus>> CreateRenewalProposalAndPolicy()
                    {
                        string policyId = transaction.PolicyId;

                        Case @case = (await _caseService.GetAsync(tenantId, new QueryArguments { Where = new CaseWhere { Proposals_contains = new ProposalWhere { PolicyId_contains = policyId } } })).FirstOrDefault();
                        Proposal proposalToRenew = @case.Proposals.FirstOrDefault(p => p.PolicyIds.Contains(policyId));
                        Result<string> renewProposalResult = await _caseService.RenewProposalAsync(tenantId, @case.Id, new RenewProposalCommand { RenewedFromId = proposalToRenew.Id, OverrideStartDateAndEndDateAutomatically = true }, null);
                        renewProposalResult.ThrowIfNotSuccess();

                        Result<CreatedStatus> generatePolicyResult = await _caseService.GeneratePoliciesFromProposalAsync(tenantId, @case.Id, new GeneratePoliciesFromProposalCommand { ProposalId = renewProposalResult.Value });
                        generatePolicyResult.ThrowIfNotSuccess();

                        return generatePolicyResult;
                    }
                }
            }

            private async Task<Result> HandleCustomerSubscription(string tenantId, Event stripeEvent, string apiKey, CancellationToken token)
            {
                if (stripeEvent.Data.Object is not Stripe.Subscription subscription)
                    return new Result() { Status = "failure" };

                FieldsWhere fieldsWhere = new() { Path = "fields.StripeCustomerId", Condition = FieldsWhereCondition.Equals, Value = new ScalarValue() { StringValue = subscription.CustomerId } };
                EntityWhere where = new() { Fields = fieldsWhere };
                QueryArguments queryArguments = new() { Where = where };
                IEnumerable<Internal> entities = await _entityService.GetAsync(tenantId, queryArguments).ConfigureAwait(false);
                Internal internalUser = entities.FirstOrDefault();
                StripeClient stripeClient = new(apiKey);
                SubscriptionService subscriptionService = new(stripeClient);
                if (internalUser == null) return new Result { Status = "failure" };
                CreateSubscriptionCommand createSubscriptionCommand = new()
                {
                    StartDate = subscription.StartDate,
                    EndDate = subscription.EndedAt,
                    StripeCustomerId = subscription.CustomerId,
                    CreatedById = internalUser.Id

                };
                IList<Offer> offerList = new List<Offer>();
                foreach (SubscriptionItem item in subscription.Items)
                {

                    Offer offer = new()
                    {
                        Id = item.Plan.Id,
                        ProductId = new ProductId() { Plan = item.Plan.ProductId, Type = item.Plan.Interval },
                        Status = item.Plan.Active ? "Active" : "InActive",
                        Amount = Convert.ToDecimal(item.Plan.Amount / 100),

                    };
                    offerList.Add(offer);
                    createSubscriptionCommand.Interval = item.Plan.Interval.ToLower() switch
                    {
                        "month" => Interval.month,
                        "year" => Interval.year,
                        "week" => Interval.week,
                        _ => createSubscriptionCommand.Interval
                    };

                }
                createSubscriptionCommand.Offers = offerList;
                ExternalRef externalRef = new() { Ref = subscription.Id };
                createSubscriptionCommand.ExternalRef = externalRef;
                Result<CreatedStatus> result = await _transactionService.CreateSubscriptionAsync(tenantId, createSubscriptionCommand).ConfigureAwait(false);
                return new Result { Status = result.Status };
            }

            private async Task<Result> HandleCustomerCreation(string tenantId, Event stripeEvent)
            {
                if (stripeEvent.Data.Object is Customer customer)
                {

                    EntityWhere where = new() { Email = customer.Email };
                    QueryArguments queryArguments = new() { Where = where };
                    IEnumerable<Internal> entities = await _entityService.GetAsync(tenantId, queryArguments);
                    Internal internalUser = entities.FirstOrDefault();
                    if (internalUser != null)
                    {
                        JObject jsonParse = JObject.Parse(internalUser.Fields.ToString());
                        if (string.IsNullOrEmpty(jsonParse["StripeCustomerId"]?.ToString()))
                        {
                            JProperty jProperty = new JProperty("StripeCustomerId", customer.Id);
                            jsonParse.Last?.AddAfterSelf(jProperty);
                        }
                        else
                        {
                            jsonParse["StripeCustomerId"] = customer.Id;
                        }

                        internalUser.Fields = jsonParse;
                    }
                    if (internalUser == null)
                        return new Result() { Status = "failure" };
                    UpdateInternalCommand command = new()
                    {
                        Fields = internalUser.Fields.ToString(),
                        IsFieldsChanged = true
                    };
                    Result result = await _entityService.UpdateAsync(tenantId, internalUser.Id, command);
                    return result;

                }

                return new Result() { Status = "failure" };
            }

            private async Task<Result> UpdateSubscription(string tenantId, string apiKey, string subscriptionId, string userId)
            {
                StripeClient stripeClient = new(apiKey);
                SubscriptionService subscriptionService = new(stripeClient);
                SubscriptionUpdateOptions options = new()
                {
                    Metadata = new Dictionary<string, string> {
                        { "tenantId", tenantId},{"userId",userId}
                    }
                };
                await subscriptionService.UpdateAsync(subscriptionId, options);
                return new Result { Status = "success" };
            }
        }
    }
}