using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Interfaces.L10ns;
using CoverGo.Gateway.Interfaces.Products;
using GraphQL;
using GraphQL.Authorization;
using GraphQL.Types;
using GraphQL.Validation;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeProductMutations(
            IAuthService authService,
            IInsurerService insurerService,
            IL10nService l10nService,
            IPricingService pricingService,
            IProductService productService,
            ITemplateService templateService,
            PermissionValidator permissionValidator)
        {
            Field<ResultGraphType>()
                .Name("cleanProductTest")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("typeId", "The type of product to clean data of")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "role", "admin");
                    string tentantId = context.GetTenantIdFromToken();
                    string typeId = context.GetArgument<string>("typeId");
                    await pricingService.CleanPricingTest(tentantId, typeId);
                    return await productService.CleanProductTest(tentantId, typeId);
                });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("createInsurer")
                .AuthorizeWith("any")
                .Description("Creates an insurer")
                .Argument<NonNullGraphType<InsurerInputGraphType>>("input", "The insurer to be created")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, "writeInsurers");

                    string tenantId = ctx.GetTenantIdFromToken();
                    CreateInsurerCommand command = ctx.GetArgument<CreateInsurerCommand>("input");
                    string loginId = ctx.GetLoginIdFromToken();
                    command.CreatedById = loginId;

                    return await insurerService.CreateInsurerAsync(tenantId, command);
                });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("deleteInsurer")
                .AuthorizeWith("any")
                .Description("Deletes an insurer")
                .Argument<StringGraphType>("id", "The insurer identifier")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, "writeInsurers");

                    string tenantId = ctx.GetTenantIdFromToken();
                    string id = ctx.GetArgument<string>("id");
                    string removedById = ctx.GetLoginIdFromToken();

                    return await insurerService.DeleteInsurerAsync(tenantId, id, removedById);
                });

            Field<ProductGraphType>()
                .Name("createProduct")
                .AuthorizeWith("any")
                .Description("Creates a product")
                .Argument<CreateProductInputGraphType>("product", "The product to create")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, new PermissionRequest("createProducts", "writeProducts"));

                    string tenantId = ctx.GetTenantIdFromToken();
                    string clientId = ctx.GetClientIdFromToken();
                    string loginId = ctx.GetLoginIdFromToken();

                    bool loadRepresentation = ctx.ProductRepresentationRequested();

                    CreateProductInputGraph input = ctx.GetArgument<CreateProductInputGraph>("product");

                    if (input.UnderwritingRules != null)
                    {
                        if (Tools.TryParseJsonStringToJObject(input.UnderwritingRules).Status == "failure")
                            throw new ExecutionError($"'{input.UnderwritingRules}' is not a valid json object.");
                    }

                    var command = new CreateProductCommand
                    {
                        ProductId = input.ProductId,
                        InsurerId = input.InsurerId,
                        IssuerProductId = input.IssuerProductId,
                        ExclusionSettings = input.ExclusionSettings,
                        LoadingSettings = input.LoadingSettings,
                        LaunchPeriodStartDate = input.LaunchPeriodStartDate,
                        LaunchPeriodEndDate = input.LaunchPeriodEndDate,
                        ChangeEffectiveDate = input.ChangeEffectiveDate,
                        Status = input.Status,
                        LifecycleStage = input.LifecycleStage,
                        Representation = input.Representation,
                        Fields = input.Fields,
                        RejectionSettings = input.RejectionSettings,
                        ClaimSettings = input.ClaimSettings,
                        BenefitInputs = input.BenefitInputs?.Select(b => new AddBenefitCommand
                        {
                            TypeId = b.TypeId,
                            ParentTypeId = b.ParentTypeId,
                            CurrencyCode = b.CurrencyCode,
                            OptionKey = b.OptionKey,
                            ParentOptionKeys = b.ParentOptionKeys,
                            Value = b.Value != null ? JToken.FromObject(b.Value?.GetValue()) : (b.ValueJsonString != null ? JsonConvert.DeserializeObject<JToken>(b.ValueJsonString) : null),
                            Condition = b.Condition != null
                                ? new Condition
                                {
                                    Type = b.Condition.Type,
                                    JsonLogicRule = b.Condition.JsonLogicRuleString != null
                                    ? JObject.Parse(b.Condition.JsonLogicRuleString)
                                    : null
                                } : null,
                            IsValueInput = b.IsValueInput
                        }),
                        Underwriting = input.Underwriting != null
                        ? new Underwriting
                        {
                            SourceType = input.Underwriting.SourceType,
                            JsonLogicRules = input.Underwriting.JsonLogicRules != null ? JObject.Parse(input.Underwriting.JsonLogicRules) : null,
                            ExcelPath = input.Underwriting.ExcelPath,
                            ExcelRules = input.Underwriting.ExcelRules
                        } : input.UnderwritingRules != null
                            ? new Underwriting
                            {
                                SourceType = "jsonLogic",
                                JsonLogicRules = input.UnderwritingRules != null ? JObject.Parse(input.UnderwritingRules) : null,
                            } : null,
                        Tags = input.Tags,
                        Facts = input.Facts?.Select(f => f.ToCommand(loginId)),
                        ProductTreeId = input.ProductTreeId,
                        PolicyIssuanceMethod = input.PolicyIssuanceMethod,
                        OfferValidityPeriod = input.OfferValidityPeriod.HasValue ? TimeSpan.FromSeconds(input.OfferValidityPeriod.Value) : null,
                        AllowCustomProduct = input.AllowCustomProduct,
                        AutoRenewal = input.AutoRenewal ?? false,
                        RenewalNotification = input.RenewalNotification ?? false,
                    };
                    command.CreatedById = loginId;
                    Result result = await productService.CreateAsync(tenantId, command);
                    if (result.Status != "success") throw new ExecutionError(result.Errors.FirstOrDefault());
                    var getResult = await productService.GetAsync(tenantId, clientId,
                        new ProductQuery
                        {
                            Where = new ProductWhere
                            {
                                ProductId = new ProductIdWhere { Plan = command.ProductId.Plan, Version = command.ProductId.Version, Type = command.ProductId.Type }
                            },
                            LoadRepresentation = loadRepresentation
                        });
                    return getResult.First().ToGraph();
                });

            Field<ProductGraphType>()
                .Name("updateProduct")
                .AuthorizeWith("any")
                .Description("Modifies a product")
                .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
                .Argument<UpdateProductInputGraphType>("input", "the modified product")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                    string tenantId = ctx.GetTenantIdFromToken();
                    ProductId productId = ctx.GetArgument<ProductId>("productId");
                    UpdateProductInputGraph input = ctx.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateProductInputGraph>();

                    bool loadRepresentation = ctx.ProductRepresentationRequested();

                    if (input.RepresentationPatch != null && !await ctx.IsJsonPatchPermitted(authService, input.RepresentationPatch, productId.ToString(), "product"))
                        throw new ValidationError("", "authorization", $"{tenantId}: You are not authorized to patch these fields" + input.RepresentationPatch);
                    if (input.FieldsPatch != null && !await ctx.IsJsonPatchPermitted(authService, input.FieldsPatch, productId.ToString(), "product"))
                        throw new ValidationError("", "authorization", $"{tenantId}: You are not authorized to patch these fields" + input.FieldsPatch);
                    if (input.UnderwritingRules != null)
                    {
                        Result<JObject> tryParseResult = Tools.TryParseJsonStringToJObject(input.UnderwritingRules);
                        if (tryParseResult.Status == "failure")
                            throw new ExecutionError($"'{input.UnderwritingRules}' is not a valid json object.");
                    }
                    if (input.Underwriting?.JsonLogicRules != null)
                    {
                        Result<JObject> tryParseResult = Tools.TryParseJsonStringToJObject(input.Underwriting?.JsonLogicRules);
                        if (tryParseResult.Status == "failure")
                            throw new ExecutionError($"'{input.Underwriting?.JsonLogicRules}' is not a valid json object.");
                    }

                    var command = new UpdateProductCommand
                    {
                        ProductId = productId,
                        InsurerId = input.InsurerId,
                        IsInsurerIdChanged = input.IsInsurerIdChanged,
                        RejectionSettings = input.RejectionSettings,
                        IsRejectionSettingsChanged = input.IsRejectionSettingsChanged,
                        LoadingSettings = input.LoadingSettings,
                        IsLoadingSettingsChanged = input.IsLoadingSettingsChanged,
                        ExclusionSettings = input.ExclusionSettings,
                        IsExclusionSettingsChanged = input.IsExclusionSettingsChanged,
                        ClaimSettings = input.ClaimSettings,
                        IsClaimSettingsChanged = input.IsClaimSettingsChanged,
                        LifecycleStage = input.LifecycleStage,
                        IsLifecycleStageChanged = input.IsLifecycleStageChanged,
                        Representation = input.Representation,
                        RepresentationPatch = input.RepresentationPatch,
                        IsRepresentationChanged = input.IsRepresentationChanged,
                        Fields = input.Fields,
                        FieldsPatch = input.FieldsPatch,
                        IsFieldsChanged = input.IsFieldsChanged,
                        LaunchPeriodStartDate = input.LaunchPeriodStartDate,
                        IsLaunchPeriodStartDateChanged = input.IsLaunchPeriodStartDateChanged,
                        LaunchPeriodEndDate = input.LaunchPeriodEndDate,
                        IsLaunchPeriodEndDateChanged = input.IsLaunchPeriodEndDateChanged,
                        ChangeEffectiveDate = input.ChangeEffectiveDate,
                        IsChangeEffectiveDateChanged = input.IsChangeEffectiveDateChanged,
                        Status = input.Status,
                        IsStatusChanged = input.IsStatusChanged,
                        ProductTreeId = input.ProductTreeId,
                        IsProductTreeIdChanged = input.IsProductTreeIdChanged,
                        ModifiedById = ctx.GetLoginIdFromToken(),
                        IsUnderwritingChanged = input.IsUnderwritingChanged || input.IsUnderwritingRulesChanged,
                        Underwriting = input.Underwriting != null
                        ? new UnderwritingToUpdate
                        {
                            IsSourceTypeChanged = input.Underwriting.IsSourceTypeChanged,
                            IsJsonLogicRulesChanged = input.Underwriting.IsJsonLogicRulesChanged,
                            IsExcelPathChanged = input.Underwriting.IsExcelPathChanged,
                            IsExcelRulesChanged = input.Underwriting.IsExcelRulesChanged,
                            SourceType = input.Underwriting.SourceType,
                            JsonLogicRules = input.Underwriting.JsonLogicRules != null ? JObject.Parse(input.Underwriting.JsonLogicRules) : null,
                            ExcelPath = input.Underwriting.ExcelPath,
                            ExcelRules = input.Underwriting.ExcelRules
                        } : input.UnderwritingRules != null
                            ? new UnderwritingToUpdate
                            {
                                IsSourceTypeChanged = true,
                                IsJsonLogicRulesChanged = true,
                                SourceType = "jsonLogic",
                                JsonLogicRules = input.UnderwritingRules != null ? JObject.Parse(input.UnderwritingRules) : null,
                            } : null,
                        PolicyIssuanceMethod = input.PolicyIssuanceMethod,
                        IsPolicyIssuanceMethodChanged = input.IsPolicyIssuanceMethodChanged,
                        IsOfferValidityPeriodChanged = input.IsOfferValidityPeriodChanged,
                        OfferValidityPeriod = input.OfferValidityPeriod.HasValue ? TimeSpan.FromSeconds(input.OfferValidityPeriod.Value) : null,
                        AllowCustomProduct = input.AllowCustomProduct,
                        IsAllowCustomProductChanged = input.IsAllowCustomProductChanged,
                        UpdateTypes = input.UpdateTypes,
                        IsUpdateTypesChanged = input.IsUpdateTypesChanged,
                        AutoRenewal = input.AutoRenewal,
                        RenewalNotification = input.RenewalNotification,
                    };

                    Result result = await productService.UpdateAsync(tenantId, command);
                    if (result.Status != "success") throw new ExecutionError(result.Errors.FirstOrDefault());
                    string clientId = ctx.GetClientIdFromToken();
                    return (await productService.GetAsync(tenantId, clientId, new ProductQuery { LoadRepresentation = loadRepresentation, Where = new ProductWhere { ProductId = new ProductIdWhere { Plan = command.ProductId.Plan, Version = command.ProductId.Version, Type = command.ProductId.Type } } })).First().ToGraph();
                });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("cloneProduct")
                .AuthorizeWith("any")
                .Description("Clones a product")
                .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
                .Argument<NonNullGraphType<CloneProductInputGraphType>>("input", "the cloned product")
                .Argument<UpsertL10nCloneProductNameInputGraphType>("name", "the l10n name to upsert")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, new PermissionRequest("createProducts", "writeProducts"));

                    string tenantId = ctx.GetTenantIdFromToken();
                    ProductId productId = ctx.GetArgument<ProductId>("productId");
                    CloneProductInputGraph input = ctx.GetArgument<CloneProductInputGraph>("input");


                    var command = new CloneProductCommand
                    {
                        ProductId = productId,
                        CloneProductId = input.CloneProductId,
                        IssuerProductId = input.IssuerProductId,
                        CreatedById = ctx.GetLoginIdFromToken()
                    };

                    Result result = await productService.CloneAsync(tenantId, command);
                    if (result.Status != "success")
                        return result;

                    UpsertL10nCommand l10nCommand = ctx.GetArgument<UpsertL10nCommand>("name");
                    if (l10nCommand != null)
                    {
                        await permissionValidator.Authorize(ctx, new PermissionRequest("createL10ns", "writeL10ns"));
                        l10nCommand.Key = $"products-{input.CloneProductId.ToString()}-name";
                        await l10nService.UpsertAsync(tenantId, l10nCommand);
                    }

                    return result;
                });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("createPriceLogic")
                .AuthorizeWith("any")
                .Description("creates pricing rules to a product")
                .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
                .Argument<NonNullGraphType<PriceLogicInputGraphType>>("input", "the price logic input")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                    string tenantId = ctx.GetTenantIdFromToken();
                    string clientId = ctx.GetClientIdFromToken();
                    ProductId productId = ctx.GetArgument<ProductId>("productId");

                    Product2 product = (await productService.GetAsync(tenantId, clientId, new ProductQuery
                    {
                        Where = new ProductWhere
                        {
                            Id_in = new List<ProductId> { productId }
                        }
                    })).FirstOrDefault();

                    if (product == null)
                        return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };

                    CreatePriceLogicCommand command = ctx.GetArgument<CreatePriceLogicCommand>("input");

                    if (command.AmountLogic != null)
                    {
                        if (Tools.TryParseJsonStringToJObject(command.AmountLogic).Status == "failure")
                            return new Result { Status = "failure", Errors = new List<string> { $"'{command.AmountLogic}' is not a valid json object." } };
                    }
                    command.CreatedById = ctx.GetLoginIdFromToken();

                    return await pricingService.CreatePriceLogic(tenantId, productId, command);
                });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("updatePriceLogic")
                .AuthorizeWith("any")
                .Description("creates pricing rules to a product")
                .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
                .Argument<NonNullGraphType<PriceLogicInputGraphType>>("input", "the price logic input")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                    string tenantId = ctx.GetTenantIdFromToken();
                    string clientId = ctx.GetClientIdFromToken();
                    ProductId productId = ctx.GetArgument<ProductId>("productId");

                    Product2 product = (await productService.GetAsync(tenantId, clientId, new ProductQuery
                    {
                        Where = new ProductWhere
                        {
                            Id_in = new List<ProductId> { productId }
                        }
                    })).FirstOrDefault();

                    if (product == null)
                        return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };

                    UpdatePriceLogicCommand command = ctx.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdatePriceLogicCommand>();
                    if (command.AmountLogic != null)
                    {
                        if (Tools.TryParseJsonStringToJObject(command.AmountLogic).Status == "failure")
                            return new Result { Status = "failure", Errors = new List<string> { $"'{command.AmountLogic}' is not a valid json object." } };
                    }
                    command.ModifiedById = ctx.GetLoginIdFromToken();

                    return await pricingService.UpdatePriceLogic(tenantId, productId, command);
                });

            Field<NonNullGraphType<ResultGraphType>>()
               .Name("deletePriceLogic")
               .AuthorizeWith("any")
               .Description("deletes pricing rules of a product")
               .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                   string tenantId = ctx.GetTenantIdFromToken();
                   ProductId productId = ctx.GetArgument<ProductId>("productId");

                   var command = new DeletePriceLogicCommand { DeletedById = ctx.GetLoginIdFromToken() };

                   return await pricingService.DeletePriceLogic(tenantId, productId, command);
               });

            Field<NonNullGraphType<CreatedStatusResultGraphType>>()
               .Name("addPricingOption")
               .AuthorizeWith("any")
               .Description("adds a pricing option to a product")
               .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
               .Argument<NonNullGraphType<PricingOptionInputGraphType>>("input", "the pricing option")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                   string tenantId = ctx.GetTenantIdFromToken();
                   ProductId productId = ctx.GetArgument<ProductId>("productId");

                   PricingOptionGraph graph = ctx.GetArgument<PricingOptionGraph>("input");

                   var command = new AddPricingOptionCommand
                   {
                       Key = graph.Key,
                       Value = graph.ValueJsonString != null ? JToken.Parse(graph.ValueJsonString) : null,
                       AddedById = ctx.GetLoginIdFromToken()
                   };

                   return await pricingService.AddPricingOptionAsync(tenantId, productId, command);
               });

            Field<NonNullGraphType<ResultGraphType>>()
               .Name("updatePricingOption")
               .AuthorizeWith("any")
               .Description("updates a pricing option of a product")
               .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
               .Argument<NonNullGraphType<StringGraphType>>("pricingOptionId", "the pricing option identifier")
               .Argument<NonNullGraphType<UpdatePricingOptionInputGraphType>>("input", "the pricing option")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                   string tenantId = ctx.GetTenantIdFromToken();
                   ProductId productId = ctx.GetArgument<ProductId>("productId");
                   string pricingOptionId = ctx.GetArgument<string>("pricingOptionId");

                   PricingOptionGraph input = ctx.GetArgument<PricingOptionGraph>("input");
                   Dictionary<string, object> dict = ctx.GetArgument<Dictionary<string, object>>("input");

                   if (input.ValueJsonString != null)
                       dict["Value"] = JToken.Parse(input.ValueJsonString);

                   UpdatePricingOptionCommand command = dict.ToUpdateCommand<UpdatePricingOptionCommand>();
                   command.Id = pricingOptionId;
                   command.ModifiedById = ctx.GetLoginIdFromToken();

                   return await pricingService.UpdatePricingOptionAsync(tenantId, productId, command);
               });

            Field<NonNullGraphType<ResultGraphType>>()
               .Name("removePricingOption")
               .AuthorizeWith("any")
               .Description("removes a pricing option from a product")
               .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
               .Argument<NonNullGraphType<StringGraphType>>("pricingOptionId", "the pricing option")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                   string tenantId = ctx.GetTenantIdFromToken();
                   ProductId productId = ctx.GetArgument<ProductId>("productId");
                   string pricingOptionId = ctx.GetArgument<string>("pricingOptionId");

                   var command = new RemoveCommand
                   {
                       Id = pricingOptionId,
                       RemovedById = ctx.GetLoginIdFromToken()
                   };

                   return await pricingService.RemovePricingOptionAsync(tenantId, productId, command);
               });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("createPlanPriceLogic")
                .AuthorizeWith("any")
                .Description("creates pricing rules to a plan")
                .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the plan identifier")
                .Argument<NonNullGraphType<PriceLogicInputGraphType>>("input", "the price logic input")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                    string tenantId = ctx.GetTenantIdFromToken();
                    string clientId = ctx.GetClientIdFromToken();
                    ProductId productId = ctx.GetArgument<ProductId>("productId");

                    Product2 plan = (await productService.GetAsync(tenantId, clientId, new ProductQuery { Where = new ProductWhere { Id_in = new List<ProductId> { productId } } })).FirstOrDefault();

                    if (plan == null)
                        return new Result { Status = "failure", Errors = new List<string> { $"The plan {productId.ToString()} was not found." } };

                    CreatePriceLogicCommand command = ctx.GetArgument<CreatePriceLogicCommand>("input");

                    if (command.AmountLogic != null)
                    {
                        if (Tools.TryParseJsonStringToJObject(command.AmountLogic).Status == "failure")
                            return new Result { Status = "failure", Errors = new List<string> { $"'{command.AmountLogic}' is not a valid json object." } };
                    }
                    command.CreatedById = ctx.GetLoginIdFromToken();

                    return await pricingService.CreatePriceLogic(tenantId, productId, command);
                });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("updatePlanPriceLogic")
                .AuthorizeWith("any")
                .Description("creates pricing rules to a plan")
                .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the plan identifier")
                .Argument<NonNullGraphType<PriceLogicInputGraphType>>("input", "the price logic input")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                    string tenantId = ctx.GetTenantIdFromToken();
                    string clientId = ctx.GetClientIdFromToken();
                    ProductId productId = ctx.GetArgument<ProductId>("productId");

                    Product2 plan = (await productService.GetAsync(tenantId, clientId, new ProductQuery { Where = new ProductWhere { Id_in = new List<ProductId> { productId } } })).FirstOrDefault();

                    if (plan == null)
                        return new Result { Status = "failure", Errors = new List<string> { $"The plan {productId.ToString()} was not found." } };

                    UpdatePriceLogicCommand command = ctx.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdatePriceLogicCommand>();
                    if (command.AmountLogic != null)
                    {
                        if (Tools.TryParseJsonStringToJObject(command.AmountLogic).Status == "failure")
                            return new Result { Status = "failure", Errors = new List<string> { $"'{command.AmountLogic}' is not a valid json object." } };
                    }
                    command.ModifiedById = ctx.GetLoginIdFromToken();

                    return await pricingService.UpdatePriceLogic(tenantId, productId, command);
                });

            Field<NonNullGraphType<ResultGraphType>>()
               .Name("deletePlanPriceLogic")
               .AuthorizeWith("any")
               .Description("deletes pricing rules of a plan")
               .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                   string tenantId = ctx.GetTenantIdFromToken();
                   ProductId productId = ctx.GetArgument<ProductId>("productId");

                   var command = new DeletePriceLogicCommand { DeletedById = ctx.GetLoginIdFromToken() };

                   return await pricingService.DeletePriceLogic(tenantId, productId, command);
               });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("addBenefit")
                .AuthorizeWith("any")
                .Description("Adds a benefit to a custom product")
                .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
                .Argument<NonNullGraphType<BenefitInputGraphType>>("input", "the benefit input")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                    string tenantId = ctx.GetTenantIdFromToken();
                    ProductId productId = ctx.GetArgument<ProductId>("productId");
                    BenefitInputGraph benefitInput = ctx.GetArgument<BenefitInputGraph>("input");

                    var command = new AddBenefitCommand
                    {
                        TypeId = benefitInput.TypeId,
                        ParentTypeId = benefitInput.ParentTypeId,
                        ParentOptionKeys = benefitInput.ParentOptionKeys,
                        Value = benefitInput.Value?.GetValue() != null ? JToken.FromObject(benefitInput.Value?.GetValue()) : (benefitInput.ValueJsonString != null ? JsonConvert.DeserializeObject<JToken>(benefitInput.ValueJsonString) : null),
                        OptionKey = benefitInput.OptionKey,
                        CurrencyCode = benefitInput.CurrencyCode,
                        Condition = benefitInput.Condition != null
                                ? new Condition
                                {
                                    Type = benefitInput.Condition.Type,
                                    JsonLogicRule = benefitInput.Condition.JsonLogicRuleString != null
                                    ? JObject.Parse(benefitInput.Condition.JsonLogicRuleString)
                                    : null
                                } : null,
                        AddedById = ctx.GetLoginIdFromToken(),
                        IsValueInput = benefitInput.IsValueInput
                    };

                    return await productService.AddBenefitAsync(tenantId, productId, command);
                });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("updateBenefit")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
                .Argument<NonNullGraphType<StringGraphType>>("typeId", "the benefit identifier")
                .Argument<StringGraphType>("optionKey", "the benefit optionKey identifier")
                .Argument<NonNullGraphType<UpdateBenefitInputGraphType>>("input", "the benefit input")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                    string tenantId = ctx.GetTenantIdFromToken();
                    ProductId productId = ctx.GetArgument<ProductId>("productId");
                    string typeId = ctx.GetArgument<string>("typeId");
                    string optionKey = ctx.GetArgument<string>("optionKey");
                    string loginId = ctx.GetLoginIdFromToken();

                    BenefitInputGraph benefitInput = ctx.GetArgument<BenefitInputGraph>("input");
                    IDictionary<string, object> dict = Tools.ToDictionary<object>(benefitInput);
                    if (benefitInput.Value != null)
                        dict["Value"] = benefitInput.Value != null ? JToken.FromObject(benefitInput.Value?.GetValue()) : null;
                    else if (benefitInput.ValueJsonString != null)
                        dict["Value"] = JsonConvert.DeserializeObject<JToken>(benefitInput.ValueJsonString);

                    if (benefitInput.Condition?.JsonLogicRuleString != null)
                    {
                        IDictionary<string, object> condition = Tools.ToDictionary<object>(dict["Condition"]);
                        condition["JsonLogicRule"] = JObject.Parse(benefitInput.Condition.JsonLogicRuleString);
                        dict["Condition"] = condition;
                    }

                    UpdateBenefitCommand command = dict.ToUpdateCommand<UpdateBenefitCommand>();

                    command.TypeId = typeId;
                    command.OptionKey = optionKey;
                    command.ModifiedById = loginId;

                    return await productService.UpdateBenefitAsync(tenantId, productId, command);
                });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("productBenefitBatch")
                .AuthorizeWith("any")
                .Description("Adds and update benefits in batch for a custom product")
                .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
                .Argument<NonNullGraphType<BenefitCommandBatchInputGraphType>>("input", "the benefits identifier")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                    string tenantId = ctx.GetTenantIdFromToken();
                    string loginId = ctx.GetLoginIdFromToken();
                    ProductId productId = ctx.GetArgument<ProductId>("productId");
                    BenefitInputGraph benefitInput = ctx.GetArgument<BenefitInputGraph>("input");

                    BenefitCommandBatchInputGraph input = ctx.GetArgument<BenefitCommandBatchInputGraph>("input");

                    var addBenefitCommands = input.AddBenefitInputs?.Select(i => i.ToAddBenefitCommand(loginId))?.ToList();
                    var updateBenefitCommands = input.UpdateBenefitInputs?.Select(i => i.ToUpdateBenefitCommand(loginId)).ToList();
                    var removeBenefitCommands = input.RemoveBenefitInputs;

                    foreach (var command in removeBenefitCommands ?? new List<RemoveBenefitCommand> { })
                        command.RemovedById = loginId;

                    var benefitCommandBatch = new BenefitCommandBatch
                    {
                        AddBenefitCommands = addBenefitCommands,
                        UpdateBenefitCommands = updateBenefitCommands,
                        RemoveBenefitCommands = removeBenefitCommands
                    };

                    return await productService.BenefitBatchAsync(tenantId, productId, benefitCommandBatch);
                });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("removeBenefit")
                .Description("Removes a benefit from a custom product")
                .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
                .Argument<NonNullGraphType<StringGraphType>>("typeId", "the benefit identifier")
                .Argument<StringGraphType>("optionKey", "the option key identifier")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                    string tenantId = ctx.GetTenantIdFromToken();
                    ProductId productId = ctx.GetArgument<ProductId>("productId");
                    string typeId = ctx.GetArgument<string>("typeId");
                    string optionKey = ctx.GetArgument<string>("optionKey");
                    string loginId = ctx.GetLoginIdFromToken();
                    var command = new RemoveBenefitCommand
                    {
                        TypeId = typeId,
                        OptionKey = optionKey,
                        RemovedById = loginId
                    };

                    return await productService.RemoveBenefitAsync(tenantId, productId, command);
                });

            Field<CreatedStatusResultGraphType>()
               .Name("addTagToProduct")
               .Description("adds a tag to a custom product")
               .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
               .Argument<NonNullGraphType<TagInputGraphType>>("input", "the tag identifier")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                   string tenantId = ctx.GetTenantIdFromToken();
                   ProductId productId = ctx.GetArgument<ProductId>("productId");
                   AddTagCommand command = ctx.GetArgument<AddTagCommand>("input");

                   command.AddedById = ctx.GetLoginIdFromToken();

                   return await productService.AddTagAsync(tenantId, productId, command);
               });

            Field<NonNullGraphType<ResultGraphType>>()
               .Name("removeTagFromProduct")
               .Description("Removes a tag from a custom product")
               .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
               .Argument<NonNullGraphType<StringGraphType>>("id", "the tag identifier")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                   string tenantId = ctx.GetTenantIdFromToken();
                   ProductId productId = ctx.GetArgument<ProductId>("productId");
                   string id = ctx.GetArgument<string>("id");
                   string optionKey = ctx.GetArgument<string>("optionKey");
                   string loginId = ctx.GetLoginIdFromToken();
                   var command = new RemoveCommand
                   {
                       Id = id,
                       RemovedById = loginId
                   };

                   return await productService.RemoveTagAsync(tenantId, productId, command);
               });

            Field<CreatedStatusResultGraphType>()
               .Name("addFactToProduct")
               .Description("adds a fact to a custom product")
               .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
               .Argument<NonNullGraphType<AddFactInputGraphType>>("input", "the fact identifier")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                   string tenantId = ctx.GetTenantIdFromToken();
                   ProductId productId = ctx.GetArgument<ProductId>("productId");
                   AddFactInputGraph input = ctx.GetArgument<AddFactInputGraph>("input");

                   var command = new AddFactCommand
                   {
                       Type = input.Type,
                       Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                       AddedById = ctx.GetLoginIdFromToken()
                   };

                   return await productService.AddFactAsync(tenantId, productId, command);
               });

            Field<ResultGraphType>()
               .Name("updateFactOfProduct")
               .Description("update a fact of a custom product")
               .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
               .Argument<NonNullGraphType<UpdateFactInputGraphType>>("input", "the fact identifier")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                   string tenantId = ctx.GetTenantIdFromToken();
                   ProductId productId = ctx.GetArgument<ProductId>("productId");
                   UpdateFactInputGraph input = ctx.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateFactInputGraph>();

                   var command = new UpdateFactCommand
                   {
                       Id = input.Id,
                       Type = input.Type,
                       IsTypeChanged = input.IsTypeChanged,
                       Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                       IsValueChanged = input.IsValueChanged,
                       ModifiedById = ctx.GetLoginIdFromToken()
                   };

                   return await productService.UpdateFactAsync(tenantId, productId, command);
               });

            Field<ResultGraphType>()
               .Name("productFactBatch")
               .Description("add and update multiple facts of a custom product")
               .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
               .Argument<NonNullGraphType<FactCommandBatchInputGraphType>>("input", "the facts identifier")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                   string tenantId = ctx.GetTenantIdFromToken();
                   ProductId productId = ctx.GetArgument<ProductId>("productId");
                   string loginId = ctx.GetLoginIdFromToken();

                   FactCommandBatchInputGraph input = ctx.GetArgument<FactCommandBatchInputGraph>("input");

                   var addFactCommands = input.AddFactInputs?.Select(i => i.ToCommand(loginId))?.ToList();

                   var updateFactCommands = input.UpdateFactInputs?.Select(
                       i => i.GetType().GetProperties().ToDictionary(prop => prop.Name, prop => (object)prop.GetValue(i, null)))
                   .Select(dict => dict.ToUpdateCommand<UpdateFactInputGraph>())
                   .Select(f => f.ToCommand(loginId))
                   .ToList();

                   var factCommandBatch = new FactCommandBatch
                   {
                       AddFactCommands = addFactCommands,
                       UpdateFactCommands = updateFactCommands
                   };

                   return await productService.ProductFactBatchAsync(tenantId, productId, factCommandBatch);
               });

            Field<NonNullGraphType<ResultGraphType>>()
               .Name("removeFactFromProduct")
               .Description("Removes a fact from a custom product")
               .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
               .Argument<NonNullGraphType<StringGraphType>>("id", "the fact identifier")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                   string tenantId = ctx.GetTenantIdFromToken();
                   ProductId productId = ctx.GetArgument<ProductId>("productId");
                   string id = ctx.GetArgument<string>("id");
                   string optionKey = ctx.GetArgument<string>("optionKey");
                   string loginId = ctx.GetLoginIdFromToken();
                   var command = new RemoveCommand
                   {
                       Id = id,
                       RemovedById = loginId
                   };

                   return await productService.RemoveFactAsync(tenantId, productId, command);
               });

            Field<CreatedStatusResultGraphType>()
               .Name("addInternalReviewToProduct")
               .Description("add an internal review to a custom product")
               .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
               .Argument<NonNullGraphType<InternalReviewInputGraphType>>("input", "the review identifier")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                   string tenantId = ctx.GetTenantIdFromToken();
                   ProductId productId = ctx.GetArgument<ProductId>("productId");
                   AddInternalReviewCommand command = ctx.GetArgument<AddInternalReviewCommand>("input");

                   command.AddedById = ctx.GetLoginIdFromToken();

                   return await productService.AddInternalReviewAsync(tenantId, productId, command);
               });

            Field<ResultGraphType>()
               .Name("updateInternalReviewOfProduct")
               .Description("update the content of an internal review of a custom product")
               .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
               .Argument<NonNullGraphType<UpdateInternalReviewInputGraphType>>("input", "the review identifier")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                   string tenantId = ctx.GetTenantIdFromToken();
                   ProductId productId = ctx.GetArgument<ProductId>("productId");
                   UpdateInternalReviewInputGraph input = ctx.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateInternalReviewInputGraph>();

                   var command = new UpdateInternalReviewCommand
                   {
                       Id = input.Id,
                       Status = input.Status,
                       IsStatusChanged = input.IsStatusChanged,
                       Comment = input.Comment,
                       IsCommentChanged = input.IsCommentChanged,
                       ModifiedById = ctx.GetLoginIdFromToken(),
                   };

                   return await productService.UpdateInternalReviewAsync(tenantId, productId, command);
               });


            Field<NonNullGraphType<ResultGraphType>>()
               .Name("removeInternalReviewFromProduct")
               .Description("Removes an internal review from a custom product")
               .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
               .Argument<NonNullGraphType<StringGraphType>>("id", "the internal review identifier")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                   string tenantId = ctx.GetTenantIdFromToken();
                   ProductId productId = ctx.GetArgument<ProductId>("productId");
                   string id = ctx.GetArgument<string>("id");
                   string loginId = ctx.GetLoginIdFromToken();
                   var command = new RemoveCommand
                   {
                       Id = id,
                       RemovedById = loginId
                   };

                   return await productService.RemoveInternalReviewAsync(tenantId, productId, command);
               });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("deleteProduct")
                .AuthorizeWith("any")
                .Description("Deletes a product")
                .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, new PermissionRequest("deleteProducts", "writeProducts"));

                    string tenantId = ctx.GetTenantIdFromToken();
                    string loginId = ctx.GetLoginIdFromToken();
                    ProductId productId = ctx.GetArgument<ProductId>("productId");

                    return await productService.DeleteAsync(tenantId, new DeleteProductCommand { ProductId = productId, DeletedById = loginId });
                });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("createProductType")
                .AuthorizeWith("any")
                .Description("Creates a product type")
                .Argument<NonNullGraphType<ProductTypeInputGraphType>>("input", "The product type to be created")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, "writeProductTypes");

                    string tenantId = ctx.GetTenantIdFromToken();
                    CreateProductTypeCommand command = ctx.GetArgument<CreateProductTypeCommand>("input");
                    string loginId = ctx.GetLoginIdFromToken();
                    command.CreatedById = loginId;

                    return await productService.CreateTypeAsync(tenantId, command);
                });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("deleteProductType")
                .AuthorizeWith("any")
                .Description("Deletes a product type")
                .Argument<StringGraphType>("typeId", "The type identifier")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, "writeProductTypes");

                    string tenantId = ctx.GetTenantIdFromToken();
                    string typeId = ctx.GetArgument<string>("typeId");
                    string removedById = ctx.GetLoginIdFromToken();

                    return await productService.DeleteTypeAsync(tenantId, typeId, removedById);
                });

            Field<NonNullGraphType<ResultGraphType>>()
               .Name("addUnderwritingVariable")
               .AuthorizeWith("any")
               .Description("Adds an underwriting variable")
               .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
               .Argument<NonNullGraphType<AddUnderwritingVariableInputGraphType>>("input", "The underwriting variable")
               .ResolveAsync(async ctx =>
               {
                   await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                   string tenantId = ctx.GetTenantIdFromToken();
                   string loginId = ctx.GetLoginIdFromToken();

                   ProductId productId = ctx.GetArgument<ProductId>("productId");
                   AddUnderwritingVariableCommand command = ctx.GetArgument<AddUnderwritingVariableCommand>("input");

                   command.AddedById = loginId;

                   return await productService.AddUnderwritingVariableAsync(tenantId, productId, command);
               });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("updateUnderwritingVariable")
                .AuthorizeWith("any")
                .Description("Adds an underwriting variable")
                .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
                .Argument<NonNullGraphType<StringGraphType>>("variableId", "the variable identifier")
                .Argument<NonNullGraphType<AddUnderwritingVariableInputGraphType>>("input", "The underwriting variable")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                    string tenantId = ctx.GetTenantIdFromToken();
                    string loginId = ctx.GetLoginIdFromToken();

                    ProductId productId = ctx.GetArgument<ProductId>("productId");
                    string variableId = ctx.GetArgument<string>("variableId");
                    UpdateUnderwritingVariableCommand command = ctx.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateUnderwritingVariableCommand>();

                    command.Id = variableId;
                    command.ModifiedById = loginId;

                    return await productService.UpdateUnderwritingVariableAsync(tenantId, productId, command);
                });

            Field<NonNullGraphType<ResultGraphType>>()
             .Name("removeUnderwritingVariable")
             .AuthorizeWith("any")
             .Description("Removes an underwriting variable")
             .Argument<NonNullGraphType<ProductIdInputGraphType>>("productId", "the product identifier")
             .Argument<NonNullGraphType<StringGraphType>>("variableId", "the variable identifier")
             .ResolveAsync(async ctx =>
             {
                 await permissionValidator.Authorize(ctx, new PermissionRequest("updateProducts", "writeProducts"));

                 string tenantId = ctx.GetTenantIdFromToken();
                 string loginId = ctx.GetLoginIdFromToken();

                 ProductId productId = ctx.GetArgument<ProductId>("productId");
                 string variableId = ctx.GetArgument<string>("variableId");

                 var command = new RemoveCommand
                 {
                     Id = variableId,
                     RemovedById = loginId
                 };

                 return await productService.RemoveUnderwritingVariableAsync(tenantId, productId, command);
             });

            Field<ResultGraphType>()
                .Name("createPlan")
                .AuthorizeWith("any")
                .Description("creates a plan")
                .Argument<NonNullGraphType<PlanInputGraphType>>("input", "the plan input")
                .ResolveAsync(async ctx =>
                {
                    await permissionValidator.Authorize(ctx, "writePlans");

                    string tenantId = ctx.GetTenantIdFromToken();
                    string loginId = ctx.GetLoginIdFromToken();

                    CreateProductCommand command = ctx.GetArgument<CreateProductCommand>("input");
                    command.CreatedById = loginId;

                    return await productService.CreateAsync(tenantId, command);
                });

            Field<ResultGraphType>()
             .Name("deletePlan")
             .AuthorizeWith("any")
             .Description("deletes a plan")
             .Argument<NonNullGraphType<ProductIdInputGraphType>>("id", "the identifier of the plan")
             .ResolveAsync(async ctx =>
             {
                 string tenantId = ctx.GetTenantIdFromToken();
                 string loginId = ctx.GetLoginIdFromToken();

                 ProductId id = ctx.GetArgument<ProductId>("id");
                 await permissionValidator.Authorize(ctx, "writePlans", id.ToString());

                 DeleteProductCommand command = new()
                 {
                     ProductId = id,
                     DeletedById = loginId
                 };

                 return await productService.DeleteAsync(tenantId, command);
             });

            Field<CreatedStatusResultGraphType>()
             .Name("createProductConfig")
             .AuthorizeWith("any")
             .Description("creates a productConfig")
             .Argument<NonNullGraphType<ProductConfigInputGraphType>>("input", "the config input")
             .ResolveAsync(async ctx =>
             {
                 string tenantId = ctx.GetTenantIdFromToken();
                 string loginId = ctx.GetLoginIdFromToken();

                 ProductConfigGraph graph = ctx.GetArgument<ProductConfigGraph>("input");
                 CreateProductConfigCommand command = ProductConfigGraph.ToCreateCommand(graph);

                 await permissionValidator.Authorize(ctx, "writeProductConfigs");
                 command.CreatedById = loginId;

                 return await productService.CreateConfigAsync(tenantId, command);
             });

            Field<ResultGraphType>()
             .Name("updateProductConfig")
             .AuthorizeWith("any")
             .Description("updates a productConfig")
             .Argument<NonNullGraphType<StringGraphType>>("id", "the identifier of the config")
             .Argument<NonNullGraphType<UpdateProductConfigInputGraphType>>("input", "the config input")
             .ResolveAsync(async ctx =>
             {
                 string tenantId = ctx.GetTenantIdFromToken();
                 string loginId = ctx.GetLoginIdFromToken();

                 string id = ctx.GetArgument<string>("id");

                 ProductConfigGraph input = ctx.GetArgument<ProductConfigGraph>("input");
                 IDictionary<string, object> dict = Tools.ToDictionary<object>(input);
                 if (input.DisplayedProducts != null)
                     dict["DisplayedProducts"] = input.DisplayedProducts.Select(p => DisplayedProductsGraph.ToDomain(p))?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

                 if (input.DisplayedBenefits != null)
                     dict["DisplayedBenefits"] = input.DisplayedBenefits.Select(b => DisplayedBenefitsGraph.ToDomain(b))?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

                 if (input.DisplayedInsurers != null)
                     dict["DisplayedInsurers"] = input.DisplayedInsurers.Select(i => DisplayedInsurersGraph.ToDomain(i))?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

                 UpdateProductConfigCommand command = dict.ToUpdateCommand<UpdateProductConfigCommand>();

                 await permissionValidator.Authorize(ctx, "writeProductConfigs", id);
                 command.ModifiedById = loginId;

                 return await productService.UpdateConfigAsync(tenantId, id, command);
             });

            Field<ResultGraphType>()
             .Name("deleteProductConfig")
             .AuthorizeWith("any")
             .Description("deletes a productConfig")
             .Argument<NonNullGraphType<StringGraphType>>("id", "the identifier of the config")
             .ResolveAsync(async ctx =>
             {
                 string tenantId = ctx.GetTenantIdFromToken();
                 string loginId = ctx.GetLoginIdFromToken();

                 string id = ctx.GetArgument<string>("id");

                 await permissionValidator.Authorize(ctx, "writeProductConfigs", id);
                 var command = new DeleteCommand
                 {
                     DeletedById = loginId
                 };

                 return await productService.DeleteConfigAsync(tenantId, id, command);
             });

            Field<ResultGraphType>()
                .Name("addScriptToProduct")
                .Argument<NonNullGraphType<AddScriptToProductInputGraphType>>("input", "Add a script to the product")
                .ResolveAsync(async context =>
                {
                    AddScriptToProductInput input = context.GetArgument<AddScriptToProductInput>("input");

                    try
                    {
                        await permissionValidator.Authorize(context, new PermissionRequest("updateProducts", "writeProducts").WithTargetIds("all"));
                    }
                    catch
                    {
                        await permissionValidator.Authorize(context, "writeProductScripts", "all");
                    }

                    string tenantId = context.GetTenantIdFromToken();

                    var command = new AddScriptToProductCommand { ProductId = input.ProductId, ScriptId = input.ScriptId };

                    return await productService.AddScriptToProductAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("removeScriptFromProduct")
                .Argument<NonNullGraphType<RemoveScriptFromProductInputGraphType>>("input", "Remove script from product")
                .ResolveAsync(async context =>
                {
                    try
                    {
                        await permissionValidator.Authorize(context, new PermissionRequest("updateProducts", "writeProducts").WithTargetIds("all"));
                    }
                    catch
                    {
                        await permissionValidator.Authorize(context, "writeProductScripts", "all");
                    }

                    RemoveScriptFromProductInput input = context.GetArgument<RemoveScriptFromProductInput>("input");
                    string tenantId = context.GetTenantIdFromToken();

                    var command = new RemoveScriptFromProductCommand { ProductId = input.ProductId, ScriptId = input.ScriptId };

                    return await productService.RemoveScriptFromProductAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("addTemplateRelationshipToProduct")
                .Argument<NonNullGraphType<AddTemplateRelationshipToProductInputGraphType>>("input", "Add a template relationship to the product")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updateProducts", "writeProducts").WithTargetIds("all"));
                    AddTemplateRelationshipToProductInput input = context.GetArgument<AddTemplateRelationshipToProductInput>("input");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    var templateCommand = new CreateTemplateRelationshipCommand
                    {
                        Action = input.Action,
                        TemplateId = input.TemplateId,
                        CreatedById = loginId
                    };

                    Result<CreatedStatus> status = await templateService.CreateTemplateRelationshipAsync(tenantId, templateCommand);

                    if (status.Status != "success")
                        return status;

                    var productCommand = new AddTemplateRelationshipToProductCommand
                    {
                        ProductId = input.ProductId,
                        TemplateRelationshipId = status.Value.Id,
                        AddedById = loginId
                    };

                    return await productService.AddTemplateRelationshipToProductAsync(tenantId, productCommand);
                });

            Field<ResultGraphType>()
                .Name("removeTemplateRelationshipFromProduct")
                .Argument<NonNullGraphType<RemoveTemplateRelationshipFromProductInputGraphType>>("input", "Remove template relationship from product")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updateProducts", "writeProducts").WithTargetIds("all"));
                    RemoveTemplateRelationshipFromProductInput input = context.GetArgument<RemoveTemplateRelationshipFromProductInput>("input");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    var productCommand = new RemoveTemplateRelationshipFromProductCommand
                    {
                        ProductId = input.ProductId,
                        TemplateRelationshipId = input.TemplateRelationshipId,
                        RemovedById = loginId
                    };

                    Result status = await productService.RemoveTemplateRelationshipFromProductAsync(tenantId, productCommand);

                    if (status.Status != "success")
                        return status;

                    var templateCommand = new DeleteCommand
                    {
                        DeletedById = loginId,
                    };

                    return await templateService.DeleteTemplateRelationshipAsync(tenantId, input.TemplateRelationshipId, templateCommand);
                });

            Field<ResultGraphType>()
                .Name("addDataSchemaToProductType")
                .Argument<NonNullGraphType<AddDataSchemaToProductTypeInputGraphType>>("input", "Add a dataSchema to the productType")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updateProducts", "writeProducts").WithTargetIds("all"));
                    AddDataSchemaToProductTypeInput input = context.GetArgument<AddDataSchemaToProductTypeInput>("input");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    AddDataSchemaToProductTypeCommand command = new()
                    {
                        ProductTypeId = input.ProductTypeId,
                        DataSchemaId = input.DataSchemaId,
                        AddedById = loginId
                    };

                    return await productService.AddDataSchemaToProductTypeAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("removeDataSchemaFromProductType")
                .Argument<NonNullGraphType<RemoveDataSchemaFromProductTypeInputGraphType>>("input", "Remove dataSchema from productType")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updateProducts", "writeProducts").WithTargetIds("all"));
                    RemoveDataSchemaFromProductTypeInput input = context.GetArgument<RemoveDataSchemaFromProductTypeInput>("input");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    RemoveDataSchemaFromProductTypeCommand command = new()
                    {
                        ProductTypeId = input.ProductTypeId,
                        DataSchemaId = input.DataSchemaId,
                        RemovedById = loginId
                    };

                    return await productService.RemoveDataSchemaFromProductTypeAsync(tenantId, command);
                });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("updateLoginLockoutEndDate")
                .AuthorizeWith("any")
                .Description("updates a users lockout end date")
                .Argument<NonNullGraphType<StringGraphType>>("loginId", "the login identifier")
                .Argument<DateTimeOffsetGraphType>("dateTime", "the login identifier")
                .ResolveAsync(async ctx =>
                {
                    string tenantId = ctx.GetTenantIdFromToken();
                    String loginId = ctx.GetArgument<string>("loginId");
                    DateTime? endDateTime = ctx.GetArgument<DateTime?>("dateTime");

                    await permissionValidator.Authorize(ctx, "updateLoginLockout", loginId);

                    var command = new ChangeUserLockoutDateCommand { ModifiedById = ctx.GetLoginIdFromToken(), EndDateTime = endDateTime };

                    return await authService.UpdateLockoutEndDate(tenantId, loginId, command);
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createUiSchema")
                .Argument<NonNullGraphType<CreateUiSchemaInputGraphType>>("input", "Create a uiSchema")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updateProducts", "writeProducts").WithTargetIds("all"));
                    string tenantId = context.GetTenantIdFromToken();
                    CreateUiSchemaInput input = context.GetArgument<CreateUiSchemaInput>("input");
                    string loginId = context.GetLoginIdFromToken();
                    var command = new CreateUiSchemaCommand
                    {
                        Name = input.Name,
                        Schema = input.Schema,
                        Standard = input.Standard,
                        CreatedById = loginId
                    };
                    return await productService.CreateUiSchemaAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("updateUiSchema")
                .AuthorizeWith("any")
                .Description("Modifies a uiSchema")
                .Argument<NonNullGraphType<UpdateUiSchemaInputGraphType>>("input", "the modified uiSchema")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updateProducts", "writeProducts").WithTargetIds("all"));
                    string tenantId = context.GetTenantIdFromToken();
                    UpdateUiSchemaInput input = context.GetArgument<UpdateUiSchemaInput>("input");

                    return await productService.UpdateUiSchemaAsync(tenantId, new UpdateUiSchemaCommand
                    {
                        Id = input.Id,
                        Name = input.Name,
                        Schema = input.Schema,
                        Standard = input.Standard,
                        ModifiedById = context.GetLoginIdFromToken(),
                    });
                });

            Field<ResultGraphType>()
               .Name("deleteUiSchema")
               .Argument<NonNullGraphType<DeleteUiSchemaInputGraphType>>("input", "Delete a uiSchema")
               .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updateProducts", "writeProducts").WithTargetIds("all"));
                    string tenantId = context.GetTenantIdFromToken();
                    DeleteUiSchemaInput command = context.GetArgument<DeleteUiSchemaInput>("input");
                    string loginId = context.GetLoginIdFromToken();

                    return await productService.DeleteUiSchemaAsync(tenantId, command.UiSchemaId,
                        new DeleteCommand { DeletedById = loginId });
                });


            Field<CreatedStatusResultGraphType>()
                .Name("createJacket")
                .Description("Creates a jacket")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateJacketInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    //TODO: Add write permission later

                    CreateJacketCommand command = context.GetArgument<CreateJacketCommand>("input");
                    command.CreatedById = context.GetLoginIdFromToken();

                    Result<string> result = await productService.CreateJacketAsync(tenantId, command);

                    return new Result<CreatedStatus>
                    {
                        Status = result.Status,
                        Errors = result.Errors,
                        Value = new CreatedStatus { Id = result.Value }
                    };
                });

            Field<ResultGraphType>()
                .Name("updateJacket")
                .Description("Updates a jacket")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("jacketId", "the jacket identifier")
                .Argument<NonNullGraphType<UpdateJacketInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string jacketId = context.GetArgument<string>("jacketId");

                    //TODO: Add write permission later

                    Jacket jacket = (await productService.GetJacketsAsync(tenantId,
                        new Domain.QueryArguments { Where = new JacketWhere { Id = jacketId } })).FirstOrDefault();

                    if (jacket == null) return Result.Failure("The jacket doesn't exist");

                    UpdateJacketCommand command = context.GetArgument<Dictionary<string, object>>("input")
                        .ToUpdateCommand<UpdateJacketCommand>();
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await productService.UpdateJacketAsync(tenantId, jacketId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("deleteJacket")
                .Description("Deletes a jacket")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("jacketId", "the jacket identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string jacketId = context.GetArgument<string>("jacketId");

                    var command = new DeleteCommand { DeletedById = context.GetLoginIdFromToken() };
                    Result result = await productService.DeleteJacketAsync(tenantId, jacketId, command);
                    return result;
                });
        }
    }
}
