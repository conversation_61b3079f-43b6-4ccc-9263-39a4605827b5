using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GraphQL.DataLoader;

namespace CoverGo.Gateway.Interfaces
{
    public static class IDataLoaderExtensions
    {
        public static async Task<T[]> LoadAsync<TKey, T>(this IDataLoader<TKey, T> dataLoader, IEnumerable<TKey> keys)
        {
            var tasks = new List<Task<T>>(keys.Count());

            foreach (var key in keys)
            {
                tasks.Add(dataLoader.LoadAsync(key));
            }

            return await Task.WhenAll(tasks);
        }
    }
}