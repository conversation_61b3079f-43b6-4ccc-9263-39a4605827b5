﻿
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;

using GraphQL.DataLoader;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces
{
    public class NoteGraphType : ObjectGraphType<NoteGraph>
    {
        public NoteGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService, PermissionValidator permissionValidator)
        {
            Name = "note";
            Description = "Note with title and description";

            Field(c => c.Id);
            Field(c => c.Title, nullable: true);
            Field(c => c.Content, nullable: true);

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class AddNoteInputGraphType : InputObjectGraphType<AddNoteCommand>
    {
        public AddNoteInputGraphType()
        {
            Name = "addNoteInput";
            Description = "A command to create a note for a user or a policy";

            Field(c => c.Title, nullable: true);
            Field(c => c.Content, nullable: true);
        }
    }

    public class UpdateNoteInputGraphType : InputObjectGraphType<UpdateNoteCommand>
    {
        public UpdateNoteInputGraphType()
        {
            Name = "updateNoteInput";
            Description = "A command to update a note for a user or a policy";

            Field(c => c.Id, nullable: true); //ToDo: add id arguments to updatePolicyNote and updateEntityNote and ask FE to migrate
            Field(c => c.Title, nullable: true);
            Field(c => c.Content, nullable: true);
        }
    }

    public class NoteGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }

        public static NoteGraph ToGraph(Note domain) =>
            domain == null
                ? null
                : new NoteGraph
                {
                    Id = domain.Id,
                    Title = domain.Title,
                    Content = domain.Content
                }.PopulateSystemGraphFields(domain);
    }
}
