using System;
using CoverGo.Policies.Client;
using GraphQL.Language.AST;
using GraphQL.Types;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Interfaces.Policies.Pricing
{
    public class PricingBillingGraphType : ObjectGraphType<PricingBilling>
    {
        public PricingBillingGraphType()
        {
            Field(x => x.BillingCycle, type: typeof(PricingBillingCycleGraphType), nullable: true);
            Field(x => x.PremiumBaseFrequency, nullable: true);
        }
    }
    public class PricingBillingCycleGraphType : ObjectGraphType<PricingBillingCycle>
    {
        public PricingBillingCycleGraphType()
        {
            Field(x => x.BillingAmount, nullable: true);
            Field(x => x.Name, nullable: true);
            Field(x => x.Value, nullable: true);
            Field(x => x.FormulaId, nullable: true);
            Field(x => x.Params, type: typeof(PricingParamsGraphType), nullable: true);
        }
    }
    public class PricingDirectPriceDetailGraphType : ObjectGraphType<PricingDirectPriceDetail>
    {
        public PricingDirectPriceDetailGraphType()
        {
            Field(x => x.NodeId, nullable: true);
            Field(x => x.Amount, nullable: true);
            Field(x => x.Unit, nullable: true);
            Field(x => x.ActiveDays, nullable: true);
        }
    }
    public class PricingDirectPricesDefinitionGraphType : ObjectGraphType<PricingDirectPricesDefinition>
    {
        public PricingDirectPricesDefinitionGraphType()
        {
            Field(x => x.Currency, nullable: true);
            Field(x => x.Amount, nullable: true);
            Field(x => x.Unit, nullable: true);
            Field(x => x.Condition, nullable: true);
            Field(x => x.ProRate, nullable: true);
        }
    }
    public class PricingInsuredGroupGraphType : ObjectGraphType<PricingInsuredGroup>
    {
        public PricingInsuredGroupGraphType()
        {
            Field(x => x.NumberOfInsureds, nullable: true);
            Field(x => x.PlanSelected, nullable: true);
            Field(x => x.Key, nullable: true);
        }
    }
    public class PricingLevyFlatGraphType : ObjectGraphType<PricingLevyFlat>
    {
        public PricingLevyFlatGraphType()
        {
            Field(x => x.Amount, nullable: true);
            Field(x => x.Unit, nullable: true);
            Field(x => x.Currency, nullable: true);
        }
    }
    public class PricingParamsGraphType : ObjectGraphType<PricingParams>
    {
        public PricingParamsGraphType()
        {
            Field(x => x.Rate, nullable: true);
        }
    }
    public class PricingPolicyGraphType : ObjectGraphType<PricingPolicy>
    {
        public PricingPolicyGraphType()
        {
            Field(x => x.Policy, type: typeof(PricingPolicyGraphType), nullable: true);
            Field(x => x.InsuredGroups, type: typeof(ListGraphType<PricingInsuredGroupGraphType>), nullable: true);
            Field(x => x.ExpectedStartDate, nullable: true);
            Field(x => x.EndDate, nullable: true);
            Field(x => x.P400_Client_Code, nullable: true);
            Field(x => x.P400_Agent_Code, nullable: true);
            Field(x => x.Agent_Name, nullable: true);
            Field(x => x.ClaimSettlementMode, nullable: true);
            Field(x => x.BenefitCalculationMode, nullable: true);
            Field(x => x.MembersValidation, nullable: true);
            Field(x => x.TotalMemberNotApproved, nullable: true);
            Field(x => x.StartDate, nullable: true);
        }
    }
    public class PricingPriceAffectGraphType : ObjectGraphType<PricingPriceAffect>
    {
        public PricingPriceAffectGraphType()
        {
            Field(x => x.Id, nullable: true);
            Field(x => x.Amount, nullable: true);
            Field(x => x.StdAmount, nullable: true);
            Field(x => x.ActiveDays, nullable: true);
            Field(x => x.Affected, nullable: true);
            Field(x => x.ParentId, nullable: true);
            Field(x => x.Unit, nullable: true);
        }
    }

    public class PricingPlanGraphType : ObjectGraphType<PricingPlan>
    {
        public PricingPlanGraphType()
        {
            Field(x => x.Id, nullable: true);
            Field(x => x.Name, nullable: true);
            Field(x => x.DirectPriceDetails, type: typeof(ListGraphType<PricingDirectPriceDetailGraphType>), nullable: true);
            Field(x => x.DirectBandedPriceDetails, type: typeof(ListGraphType<PricingDirectPriceDetailGraphType>), nullable: true);
            Field(x => x.DirectBandedPremiumNodes, type: typeof(ListGraphType<PricingDirectPriceDetailGraphType>), nullable: true);

            Field(x => x.TotalPrice, nullable: true);
            Field(x => x.TotalDirectPrice, nullable: true);
            Field(x => x.InsuredCount, nullable: true);
            Field(x => x.NodeName, nullable: true);
            Field(x => x.ParentId, nullable: true);
            Field(x => x.Label, nullable: true);
        }
    }
    public class PricingPriceDetailGraphType : ObjectGraphType<PricingPriceDetail>
    {
        public PricingPriceDetailGraphType()
        {
            Field(x => x.DirectPriceDetails, type: typeof(ListGraphType<PricingDirectPriceDetailGraphType>), nullable: true);
            Field(x => x.DirectBandedPriceDetails, type: typeof(ListGraphType<PricingDirectPriceDetailGraphType>), nullable: true);
            Field(x => x.DirectBandedPremiumNodes, type: typeof(ListGraphType<PricingDirectPriceDetailGraphType>), nullable: true);
            Field(x => x.DirectPricesDefinition, type: typeof(ListGraphType<PricingDirectPricesDefinitionGraphType>), nullable: true);
            Field(x => x.Id, nullable: true);
            Field(x => x.Name, nullable: true);
            Field(x => x.TotalPrice, nullable: true);
            Field(x => x.TotalDirectPrice, nullable: true);
            Field(x => x.InsuredCount, nullable: true);
            Field(x => x.NodeName, nullable: true);
            Field(x => x.ParentId, nullable: true);
            Field(x => x.Label, nullable: true);
        }
    }
    public class PricingPricePerInsuredGraphType : ObjectGraphType<PricingPricePerInsured>
    {
        public PricingPricePerInsuredGraphType()
        {
            Field(x => x.Inherited, nullable: true);
            Field(x => x.EndorsementId, nullable: true);
            Field(x => x.MovementType, nullable: true);
            Field(x => x.SequenceNo, nullable: true);
            Field(x => x.IsUpdated, nullable: true);
            Field(x => x.FullName, nullable: true);
            Field(x => x.StaffNo, nullable: true);
            Field(x => x.PassportNo, nullable: true);
            Field(x => x.Hkid, nullable: true);
            Field(x => x.DateOfBirth, nullable: true);
            Field(x => x.Gender, nullable: true);
            Field(x => x.MemberType, nullable: true);
            Field(x => x.RelationshipToEmployee, nullable: true);
            Field(x => x.ClaimsSettlementMode, nullable: true);
            Field(x => x.IsNeedManualApproval, nullable: true);
            Field(x => x.EffectiveDate, nullable: true);
            Field(x => x.MemberId, nullable: true);
            Field(x => x.PlanId, nullable: true);
            Field(x => x.PolicyId, nullable: true);
            Field(x => x.StartDate, nullable: true);
            Field(x => x.EndDate, nullable: true);
            Field(x => x.InternalCode, nullable: true);
            Field(x => x.ValidationResult, nullable: true);
            Field(x => x.UnderwritingResult, type: typeof(PolicyMemberUnderwritingResultGraphType), nullable: true);
            Field(x => x.CreatedById, nullable: true);
            Field(x => x.CreatedAt, nullable: true);
            Field(x => x.Amount, nullable: true);
            Field(x => x.AccurateAmount, nullable: true);
            Field(x => x.StdPremium, nullable: true);
            Field(x => x.Policy, type: typeof(PricingPolicyGraphType), nullable: true);
            Field(x => x.Plan, type: typeof(PricingPlanGraphType), nullable: true);
            Field(x => x.PriceAffects, type: typeof(ListGraphType<PricingPriceAffectGraphType>), nullable: true);
        }
    }
    public class PolicyMemberUnderwritingResultGraphType : EnumerationGraphType<PolicyMemberUnderwritingResult> { }

    public class PricingRootGraphType : ObjectGraphType<PricingRoot>
    {
        public PricingRootGraphType()
        {
            Field(x => x.Id, nullable: true);
            Field(x => x.TotalPriceAllPlans, type: typeof(PricingTotalPriceAllPlansGraphType), nullable: true);
            Field(x => x.FormatedTotalPriceAllPlans, nullable: true);
            Field(x => x.TotalPremium, type: typeof(PricingTotalPremiumGraphType), nullable: true);
            Field(x => x.FormatedTotalPremium, nullable: true);
            Field(x => x.TotalInsureds, nullable: true);
            Field(x => x.Levy, nullable: true);
            Field(x => x.LevyPercentage, nullable: true);
            Field(x => x.LevyFlat, type: typeof(PricingLevyFlatGraphType), nullable: true);
            Field(x => x.FormatedLevyFlat, nullable: true);
            Field(x => x.PriceDetails, type: typeof(ListGraphType<PricingPriceDetailGraphType>), nullable: true);
            Field(x => x.PricePerInsureds, type: typeof(ListGraphType<PricingPricePerInsuredGraphType>), nullable: true);
            Field(x => x.Plans, type: typeof(ListGraphType<PricingPlanGraphType>), nullable: true);
            Field(x => x.Billing, type: typeof(PricingBillingGraphType), nullable: true);
            Field(x => x.Summary, type: typeof(PricingSummaryGraphType), nullable: true);
            Field(x => x.Currency, nullable: true);
            Field(x => x.Meta, type: typeof(AnyType), nullable: true);
        }
    }
    public class PricingSummaryGraphType : ObjectGraphType<PricingSummary>
    {
        public PricingSummaryGraphType()
        {
            Field(x => x.TotalPriceAllPlans, type: typeof(PricingTotalPriceAllPlansGraphType), nullable: true);
            Field(x => x.TotalPremium, type: typeof(PricingTotalPremiumGraphType), nullable: true);
            Field(x => x.LevyFlat, type: typeof(PricingLevyFlatGraphType), nullable: true);
            Field(x => x.Billing, type: typeof(PricingBillingGraphType), nullable: true);
            Field(x => x.ProfitAmount, nullable: true);
            Field(x => x.Profit, type: typeof(PricingLoadingGraphType), nullable: true);
            Field(x => x.AdminFeeAmount, nullable: true);
            Field(x => x.AdminFee, type: typeof(PricingLoadingGraphType), nullable: true);
            Field(x => x.CommissionAmount, nullable: true);
            Field(x => x.Commission, type: typeof(PricingLoadingGraphType), nullable: true);
            Field(x => x.IndustryAmount, nullable: true);
            Field(x => x.Industry, type: typeof(PricingLoadingGraphType), nullable: true);
            Field(x => x.BasicCommission, type: typeof(PricingLoadingGraphType), nullable: true);
            Field(x => x.OverrideCommission, type: typeof(PricingLoadingGraphType), nullable: true);
            Field(x => x.OverrideCommissionAmount, nullable: true);
            Field(x => x.BasicCommissionAmount, nullable: true);
            Field(x => x.TotalDiscountAmount, nullable: true);
        }
    }

    public class PricingLoadingGraphType : ObjectGraphType<PricingLoading>
    {
        public PricingLoadingGraphType()
        {
            Field(x => x.Rate, nullable: true);
            Field(x => x.Percent, nullable: true);
        }
    }

    public class PricingTotalPremiumGraphType : ObjectGraphType<PricingTotalPremium>
    {
        public PricingTotalPremiumGraphType()
        {
            Field(x => x.Amount, nullable: true);
            Field(x => x.Unit, nullable: true);
            Field(x => x.Currency, nullable: true);
        }
    }
    public class PricingTotalPriceAllPlansGraphType : ObjectGraphType<PricingTotalPriceAllPlans>
    {
        public PricingTotalPriceAllPlansGraphType()
        {
            Field(x => x.Amount, nullable: true);
            Field(x => x.Unit, nullable: true);
            Field(x => x.Currency, nullable: true);
        }
    }

    public class AnyType : ScalarGraphType
    {
        public AnyType()
        {
            Name = "Any";
            Description = "The `Any` scalar type represents a value that can be any valid JSON object.";
        }

        public override object Serialize(object value) => JToken.FromObject(value);

        public override object ParseLiteral(IValue value)
        {
            if (value is StringValue stringValue)
            {
                // return JToken.Parse(stringValue.Value);
                return JValue.FromObject(stringValue.Value);
            }
            if (value is IntValue intValue)
            {
                return JValue.FromObject(intValue.Value);
            }
            if (value is FloatValue floatValue)
            {
                return JValue.FromObject(floatValue.Value);
            }
            if (value is BooleanValue booleanValue)
            {
                return JValue.FromObject(booleanValue.Value);
            }
            if (value is EnumValue enumValue)
            {
                return JValue.FromObject(enumValue.Name);
            }
            if (value is ObjectValue objectValue)
            {
                var jobject = new JObject();
                foreach (var field in objectValue.ObjectFields)
                {
                    var v = ParseLiteral(field.Value);
                    if (v != null) jobject[field.Name] = JToken.FromObject(v);
                }
                return jobject;
            }
            if (value is ListValue listValue)
            {
                var jarray = new JArray();
                foreach (var item in listValue.Values)
                {
                    var v = ParseLiteral(item);
                    if (v != null) jarray.Add(JToken.FromObject(v));
                }
                return jarray;
            }
            return null;
        }

        public override object ParseValue(object value) => value is string stringValue ? JToken.Parse(stringValue) : (object)JToken.FromObject(value);
    }
}
