using System.Linq;
using System.Text;

using GraphQL.Language.AST;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Policies.Pricing;

public static class GraphqlToOutputSchemaConverter
{
    public static string ConvertGraphQlSubfieldsToOutputSchema(ResolveFieldContext<PolicyGraph> context)
    {
        var outputSchemaSb = new StringBuilder();

        void TransformFieldToOutputSchema(string key, Field value)
        {
            if (key.StartsWith("__"))
            {
                return;
            }

            outputSchemaSb.Append("\"");
            outputSchemaSb.Append(key);
            outputSchemaSb.Append("\"");
            outputSchemaSb.Append(":");
            var fields = value.SelectionSet.Selections.OfType<Field>().ToList();
            if (fields.Count > 0)
            {
                outputSchemaSb.Append("{");
                foreach (var field in fields)
                {
                    TransformFieldToOutputSchema(field.Name, field);
                    outputSchemaSb.Append(",");
                }
                outputSchemaSb.Remove(outputSchemaSb.Length - 1, 1);
                outputSchemaSb.Append("}");
            }
            else
            {
                outputSchemaSb.Append("\"\"");
            }
        }

        outputSchemaSb.Append("{");
        foreach (var subfield in context.SubFields)
        {
            TransformFieldToOutputSchema(subfield.Key, subfield.Value);
            outputSchemaSb.Append(",");
        }
        outputSchemaSb.Remove(outputSchemaSb.Length - 1, 1);
        outputSchemaSb.Append("}");
        return outputSchemaSb.ToString();
    }
}
