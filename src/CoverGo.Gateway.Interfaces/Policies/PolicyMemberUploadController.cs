using System;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Policies.Client;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CoverGo.Gateway.Interfaces;

[Route("api/v1/policies/{policyId}/members/uploads")]
public class PolicyMemberUploadController : ControllerBase
{
    private readonly IPoliciesClient _policyClient;
    private readonly PermissionValidator _permissionValidator;

    public PolicyMemberUploadController(IPoliciesClient policyClient,
        PermissionValidator permissionValidator)
    {
        _policyClient = policyClient ?? throw new ArgumentNullException(nameof(policyClient));
        _permissionValidator = permissionValidator ?? throw new ArgumentNullException(nameof(permissionValidator));
    }

    [Authorize]
    [HttpGet("{policyMemberUploadId}/error_members")]
    public async Task<IActionResult> DownloadErrorMembers([FromRoute] string policyId, [FromRoute] string policyMemberUploadId, CancellationToken cancellationToken)
    {
        var allowedIds = await _permissionValidator.GetTargetIdsFromClaim(User, true, new PermissionRequest("updatePolicies", "writePolicies"));
        if (!allowedIds.Contains("all") && !allowedIds.Contains(policyId))
            return Unauthorized("User does not have updatePolicies or writePolicies permissions");

        string tenantId = User.Claims.FirstOrDefault(c => c.Type == "tenantId")?.Value;
        if (tenantId == null)
            return Unauthorized("No tenantId detected from current user.");

        try
        {
            var response = await _policyClient.PolicyMemberUpload_ErrorMembersAsync(policyId, policyMemberUploadId, cancellationToken);
            return new FileStreamResult(response.Stream, "text/csv") { FileDownloadName = $"policies_error_members_{policyId}_members_uploads_{policyMemberUploadId}" };
        }
        catch (Exception exception)
        {
            return BadRequest(exception.Message);
        }
    }

    [Authorize]
    [HttpGet("{policyMemberUploadId}/error_details")]
    public async Task<IActionResult> DownloadErrorDetails([FromRoute] string policyId, [FromRoute] string policyMemberUploadId, CancellationToken cancellationToken)
    {
        var allowedIds = await _permissionValidator.GetTargetIdsFromClaim(User, true, new PermissionRequest("updatePolicies", "writePolicies"));
        if (!allowedIds.Contains("all") && !allowedIds.Contains(policyId))
            return Unauthorized("User does not have updatePolicies or writePolicies permissions");

        string tenantId = User.Claims.FirstOrDefault(c => c.Type == "tenantId")?.Value;
        if (tenantId == null)
            return Unauthorized("No tenantId detected from current user.");

        try
        {
            var response = await _policyClient.PolicyMemberUpload_ErrorDetailsAsync(policyId, policyMemberUploadId, cancellationToken);
            return new FileStreamResult(response.Stream, "text/csv") { FileDownloadName = $"policies_error_details_{policyId}_members_uploads_{policyMemberUploadId}" };
        }
        catch (Exception exception)
        {
            return BadRequest(exception.Message);
        }
    }
}
