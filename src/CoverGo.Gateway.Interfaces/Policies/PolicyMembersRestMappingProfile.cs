using AutoMapper;

using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Policies.Client;

namespace CoverGo.Gateway.Infrastructure.Policies;

public class PolicyMembersRestMappingProfile : Profile
{
    public PolicyMembersRestMappingProfile()
    {
        AllowNullCollections = true;

        CreateMap<PolicyMemberInput, PolicyMemberCreateCommand>()
            .ForMember(
                it => it.DependentOf,
                opt => opt.MapFrom(src => src.DependentOf.Value))
            .ForMember(
                it => it.DependentOfPolicyMemberId,
                opt => opt.MapFrom(src => src.DependentOfPolicyMemberId.Value))
            .ForMember(
                it => it.UnderwritingResult,
                opt => opt.MapFrom(src => src.UnderwritingResult.Value))
            .ForMember(
                it => it.ValidationResult,
                opt => opt.MapFrom(src => src.ValidationResult.Value))
            .ForMember(
                it => it.IsRenewed,
                opt => opt.MapFrom(src => src.IsRenewed.Value));
        CreateMap<PolicyMemberInput, PolicyMemberUpdateCommand>();
        CreateMap<PolicyMemberInput, PolicyMemberRemoveCommand>().ForMember(
                it => it.UnderwritingResult,
                opt => opt.MapFrom(src => src.UnderwritingResult.Value))
            .ForMember(
                it => it.ValidationResult,
                opt => opt.MapFrom(src => src.ValidationResult.Value));
        CreateMap<Interfaces.Policies.PolicyMembersBatchCommand, CoverGo.Policies.Client.PolicyMembersBatchCommand>();
    }
}
