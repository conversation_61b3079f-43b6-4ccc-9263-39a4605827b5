using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Context;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.SettableValues;
using GraphQL.Language.AST;
using GraphQL.Types;

using Newtonsoft.Json;

using NodaTime;

using PolicyMemberUnderwritingResult = CoverGo.Policies.Client.PolicyMemberUnderwritingResult;
using PolicyMemberValidationResult = CoverGo.Policies.Client.PolicyMemberValidationResult;

namespace CoverGo.Gateway.Interfaces.Policies
{

    public class PolicyMembersGraph
    {
        public PolicyMembersGraph(string policyId, List<string> endorsementId_In, DateTime? asOf)
        {
            AsOf = asOf;
            PolicyId = policyId;
            EndorsementId_In = endorsementId_In;
        }

        public DateTime? AsOf { get; }
        public List<PolicyMember> List { get; set; }
        public long TotalCount { get; set; }
        public string PolicyId { get; }
        public List<string> EndorsementId_In;
    }

    public abstract class PolicyMembersBaseGraphType : ObjectGraphType<PolicyMembersGraph>
    {
        protected readonly CoverGoPolicyMembersService PolicyMembersService;
        protected readonly ICoverGoContextAccessor CoverGoContextAccessor;
        protected readonly PermissionValidator PermissionValidator;

        protected abstract Task<IReadOnlyCollection<PolicyMember>> QueryAsync(string tenantId, PolicyMembersWhere where,
            CancellationToken cancellationToken);

        protected abstract Task<long> CountAsync(string tenantId, PolicyMembersWhere where,
            CancellationToken cancellationToken);

        private async Task<(string tenantId, PolicyMembersWhere query)> QueryBuildInternalAsync(
            ResolveFieldContext<PolicyMembersGraph> context)
        {
            CoverGoContext coverGoContext = await CoverGoContextAccessor.CoverGoContextGetAsync();

            string tenantId = coverGoContext.TenantId;
            string policyId = context.Source.PolicyId;

            int? skip = context.ComputeArgAndVar<int?, PolicyMembersGraph>("skip");
            int? first = context.ComputeArgAndVar<int?, PolicyMembersGraph>("limit");
            bool? isTerminated = context.ComputeArgAndVar<bool?, PolicyMembersGraph>("isTerminated");
            DateTime? asOf = context.ComputeArgAndVar<DateTime?, PolicyMembersGraph>("asOf") ?? context.Source.AsOf;
            List<SortGraph>? sort = context.ComputeArgAndVar<List<SortGraph>, PolicyMembersGraph>("sort2");
            if (sort == null)
            {
                var sortItem = context.ComputeArgAndVar<SortGraph, PolicyMembersGraph>("sort");
                if (sortItem != null)
                {
                    sort = new List<SortGraph> { sortItem };
                }
            }
            bool? filterOutTerminatedMemberMovements = context.ComputeArgAndVar<bool?, PolicyMembersGraph>("filterOutTerminatedMemberMovements");
            bool? filterOutUnderwritingNotApproved = context.ComputeArgAndVar<bool?, PolicyMembersGraph>("filterOutUnderwritingNotApproved");

            List<OrderBy>? orderBy2 = null;
            if (sort != null && sort.Count > 0)
            {
                orderBy2 = sort.Select(it => it.ToOrderBy()).ToList();
            }

            var query = new PolicyMembersWhere
            {
                PolicyId_In = new List<string> { policyId },
                IsTerminated = isTerminated,
                AsOf = asOf,
                Skip = skip ?? 0,
                First = first ?? 25,
                OrderBy2 = orderBy2,
                FilterOutTerminatedMemberMovements = filterOutTerminatedMemberMovements,
                FilterOutUnderwritingNotApproved = filterOutUnderwritingNotApproved,
                Where = await BuildWhereFilterInternal(PermissionValidator, context, coverGoContext.App.DefaultTimeZone),
                EndorsementId_In = context.Source.EndorsementId_In,
            };

            return (tenantId, query);
        }

        private static async Task<Filter<PolicyMembersFilter>> BuildWhereFilterInternal(PermissionValidator permissionValidator,
            ResolveFieldContext<PolicyMembersGraph> context, string defaultTimeZone)
        {
            var allowedReadIndividualIds = (await permissionValidator.GetTargetIdsFromClaim(
                    context, new[] { "readIndividuals", "readPolicyMembers" }))
                .Where(id => !(id.Contains('{') || id.Contains('}')))
                .Distinct()
                .ToList();

            var serializer = new JsonSerializer();
            serializer.Converters.Add(new TimeZoneDateTimeConverter(defaultTimeZone));

            if (allowedReadIndividualIds.Contains("all"))
                return context.ComputeArgAndVar<Filter<PolicyMembersFilter>, PolicyMembersGraph>("filter", serializer);

            FieldsWhere fieldsWhere = new()
            {
                Path = "fields.individualId",
                Condition = FieldsWhereCondition.In,
                Value = new ScalarValue
                {
                    ArrayValue = allowedReadIndividualIds.ConvertAll(id => new ScalarValue { StringValue = id })
                }
            };

            // allow to read members who has no individuals assigned
            fieldsWhere.Value.ArrayValue.Add(new ScalarValue());

            return new Filter<PolicyMembersFilter>()
            {
                And = new List<Filter<PolicyMembersFilter>>()
                {
                    new()
                    {
                        Where = new PolicyMembersFilter
                        {
                            Fields = fieldsWhere
                        }
                    },
                    context.ComputeArgAndVar<Filter<PolicyMembersFilter>, PolicyMembersGraph>("filter", serializer)
                }
            };
        }

        protected PolicyMembersBaseGraphType(
            CoverGoPolicyMembersService policyMembersService,
            ICoverGoContextAccessor coverGoContextAccessor,
            PermissionValidator permissionValidator)
        {
            CoverGoContextAccessor = coverGoContextAccessor;
            PolicyMembersService = policyMembersService;
            PermissionValidator = permissionValidator;
            Field(x => x.List, true, typeof(ListGraphType<PolicyMemberGraphType>))
                .Description("Gets list")
                .ResolveAsync(async context =>
                {
                    (string tenantId, PolicyMembersWhere query) = await QueryBuildInternalAsync(context);
                    IReadOnlyCollection<PolicyMember> result =
                        await QueryAsync(tenantId, query, context.CancellationToken);
                    return result.ToList();
                });

            Field(x => x.TotalCount, true)
                .Description("Gets a reportItems")
                .ResolveAsync(async context =>
                {
                    (string tenantId, PolicyMembersWhere query) = await QueryBuildInternalAsync(context);
                    long result = await CountAsync(tenantId, query, context.CancellationToken);
                    return result;
                });
        }
    }

    public class PolicyMembersGraphType : PolicyMembersBaseGraphType
    {
        public PolicyMembersGraphType(
            CoverGoPolicyMembersService policyMembersService,
            ICoverGoContextAccessor coverGoContextAccessor,
            PermissionValidator permissionValidator)
            : base(policyMembersService, coverGoContextAccessor, permissionValidator)
        {
            Name = "members";
            Description = "Policy members";
        }

        protected override Task<IReadOnlyCollection<PolicyMember>> QueryAsync(string tenantId,
            PolicyMembersWhere @where, CancellationToken cancellationToken) =>
            PolicyMembersService.QueryAsync(tenantId, where, cancellationToken);

        protected override Task<long> CountAsync(string tenantId, PolicyMembersWhere @where,
            CancellationToken cancellationToken) =>
            PolicyMembersService.CountAsync(tenantId, where, cancellationToken);
    }

    public class PolicyMembersMovementsGraphType : PolicyMembersBaseGraphType
    {
        public PolicyMembersMovementsGraphType(
            CoverGoPolicyMembersService policyMembersService,
            ICoverGoContextAccessor coverGoContextAccessor,
            PermissionValidator permissionValidator)
            : base(policyMembersService, coverGoContextAccessor, permissionValidator)
        {
            Name = "membersMovements";
            Description = "Policy members movements";
        }

        protected override Task<IReadOnlyCollection<PolicyMember>> QueryAsync(string tenantId,
            PolicyMembersWhere @where, CancellationToken cancellationToken) =>
            PolicyMembersService.MovementsLogQueryAsync(tenantId, where, cancellationToken);

        protected override Task<long> CountAsync(string tenantId, PolicyMembersWhere @where,
            CancellationToken cancellationToken) =>
            PolicyMembersService.MovementsLogCountAsync(tenantId, where, cancellationToken);
    }

    public class PolicyMembersActivityGraphType : PolicyMembersBaseGraphType
    {
        public PolicyMembersActivityGraphType(
            CoverGoPolicyMembersService policyMembersService,
            ICoverGoContextAccessor coverGoContextAccessor,
            PermissionValidator permissionValidator) : base(policyMembersService, coverGoContextAccessor, permissionValidator)
        {
            Name = "membersActivity";
            Description = "Policy members movements";
        }

        protected override Task<IReadOnlyCollection<PolicyMember>> QueryAsync(string tenantId,
            PolicyMembersWhere @where, CancellationToken cancellationToken) =>
            PolicyMembersService.ActivityQueryAsync(tenantId, where, cancellationToken);

        protected override Task<long> CountAsync(string tenantId, PolicyMembersWhere @where,
            CancellationToken cancellationToken) =>
            PolicyMembersService.ActivityCountAsync(tenantId, where, cancellationToken);
    }


    public class PolicyMembersMovementsGraph
    {
        public List<PolicyMember> List { get; set; }
        public long Count { get; set; }
    }

    public class PolicyMemberGraphType : ObjectGraphType<PolicyMember>
    {
        public PolicyMemberGraphType()
        {
            Name = "policyMember";
            Description = "Policy member";

            Field(x => x.Id).Description("Unique state identifier, will be new each time when the member gets updated or removed.");
            Field(x => x.MemberId).Description("Member unique id(HKID, passport number, etc)");
            Field(x => x.CreatedAt, true, typeof(DateTimeGraphType)).Description("DateTime when the member was created the first time");
            Field(x => x.LastModifiedAt, true, typeof(DateTimeGraphType)).Description("Date time when the member was last modified");
            Field(x => x.DependentOf, true).Description("IndividualId/MemberId the member is dependent of");
            Field(x => x.DependentOfPolicyMemberId, true).Description("PolicyMemberId the member is dependent of");
            Field(x => x.InternalCode, true).Description("Internal code of the member, assigned when the policy gets issued, supposed to be used for individual creation");
            Field(x => x.PolicyId);
            Field(x => x.EndorsementId, true);
            Field(x => x.Fields, true, typeof(StringGraphType)).Resolve(context => context.Source?.Fields?.ToString());
            Field(x => x.PlanId, true);
            Field(x => x.StartDate, true, typeof(DateGraphType));
            Field(x => x.EndDate, true, typeof(DateGraphType));
            Field(x => x.Timestamp, true, typeof(DateTimeGraphType));
            Field(x => x.CreatedById, true);
            Field(x => x.IsRemoved);
            Field(x => x.IsPrinted);
            Field(x => x.IsRenewed, true);
            Field(x => x.UnderwritingResult, true, typeof(PolicyMemberUnderwritingResultGraph));
            Field(x => x.ValidationResult, true, typeof(PolicyMemberValidationResultGraph));
            Field(x => x.CertificateNumber, true).Description("Certificate number of the member unique within the policy");
        }
    }

#nullable enable
    public class PolicyMemberInput
    {
        public string? PolicyId { get; set; }
        public string? EndorsementId { get; set; }
        public string? PlanId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? MemberId { get; set; }
        public string? Network { get; set; }
        public string? NetworkID { get; set; }
        public Settable<string?>? DependentOf { get; set; }
        public Settable<string?>? DependentOfPolicyMemberId { get; set; }
        public string? InternalCode { get; set; }
        public DateTime? CreatedAt { get; set; }
        public string? Fields { get; set; }
        public string? CreatedById { get; set; }
        public Settable<PolicyMemberUnderwritingResult?>? UnderwritingResult { get; set; }
        public Settable<PolicyMemberValidationResult?>? ValidationResult { get; set; }
        public Settable<bool>? IsRenewed { get; set; }
    }
#nullable disable

    public class PolicyMemberUnderwritingResultGraph : EnumerationGraphType<PolicyMemberUnderwritingResult>
    {
    }

    public class PolicyMemberValidationResultGraph : EnumerationGraphType<PolicyMemberValidationResult>
    {
    }

    public class CustomDateGraphType : DateGraphType
    {
        public CustomDateGraphType()
        {
            Name = "CustomDate";
            Description = "Date custom scalar supports ISO 8601 and dd/MM/yyyy formats";
        }

        public override object ParseLiteral(IValue value)
        {
            if (value is StringValue stringValue)
            {
                // Try to parse as dd/MM/yyyy first
                if (DateTime.TryParseExact(stringValue.Value, "dd/MM/yyyy", CultureInfo.InvariantCulture, 
                    DateTimeStyles.None, out var dateTime))
                {
                    return dateTime;
                }
                
                // Fall back to default ISO 8601 parsing
                return base.ParseLiteral(value);
            }
            
            return base.ParseLiteral(value);
        }

        public override object ParseValue(object value)
        {
            if (value is string stringValue)
            {
                // Try to parse as dd/MM/yyyy first
                if (DateTime.TryParseExact(stringValue, "dd/MM/yyyy", CultureInfo.InvariantCulture, 
                    DateTimeStyles.None, out var dateTime))
                {
                    return dateTime;
                }
            }
            
            return base.ParseValue(value);
        }
    }

    public class PolicyMemberInputGraph : InputObjectGraphType<PolicyMemberInput>
    {
        public PolicyMemberInputGraph()
        {
            Name = "policyMemberInput";
            Description = "Policy member create input";

            Field(x => x.MemberId, true, typeof(StringGraphType));
            Field(x => x.Network, true, typeof(StringGraphType));
            Field(x => x.NetworkID, true, typeof(StringGraphType));
            Field(x => x.PlanId, true, typeof(StringGraphType));
            Field(x => x.StartDate, true, typeof(CustomDateGraphType));
            Field(x => x.EndDate, true, typeof(CustomDateGraphType));
            Field(x => x.Fields, true, typeof(StringGraphType));
            Field(x => x.CreatedAt, true, typeof(DateTimeGraphType)).Description("DateTime when the member was created.");
            Field(x => x.DependentOf, true, typeof(StringGraphType)).Description("IndividualId/MemberId the member is dependent of");
            Field(x => x.DependentOfPolicyMemberId, true, typeof(StringGraphType)).Description("PolicyMemberId the member is dependent of");
            Field(x => x.InternalCode, true, typeof(StringGraphType)).Description("Internal code of the member, assigned when the policy gets issued, supposed to be used for individual creation");
            Field(x => x.UnderwritingResult, true, typeof(PolicyMemberUnderwritingResultGraph));
            Field(x => x.ValidationResult, true, typeof(PolicyMemberValidationResultGraph));
            Field(x => x.IsRenewed, true, typeof(BooleanGraphType));
        }
    }

    public class PolicyMembersBatchCommand : IEntityBatch<PolicyMemberInput, PolicyMemberInput, PolicyMemberInput>
    {
        public List<PolicyMemberInput> Create { get; set; }
        public List<PolicyMemberInput> Update { get; set; }
        public List<PolicyMemberInput> Delete { get; set; }
    }

    public class PolicyMembersBatchInputGraph : InputObjectGraphType<PolicyMembersBatchCommand>
    {
        public PolicyMembersBatchInputGraph()
        {
            Name = "policyMembersBatchInput";
            Description = "Policy Members Batch Input";

            Field(x => x.Create, true, typeof(ListGraphType<PolicyMemberInputGraph>));
            Field(x => x.Update, true, typeof(ListGraphType<PolicyMemberInputGraph>));
            Field(x => x.Delete, true, typeof(ListGraphType<PolicyMemberInputGraph>));
        }
    }

    public class PolicyMembersFilterInputGraph : InputObjectGraphType<PolicyMembersFilter>
    {
        public PolicyMembersFilterInputGraph()
        {
            Name = "policyMembersFilterInput";
            Description = "Policy Members Filter Input";

            Field(x => x.IsRemoved, true);
            Field(x => x.MemberId_in, true);
            Field(x => x.PlanId_contains, true);
            Field(x => x.PlanId_in, true);
            Field(x => x.HavingStartDate, true);
            Field(x => x.IsTerminated, true);
            Field(x => x.IsPrinted, true);
            Field(x => x.IsRenewed, true);
            Field(x => x.InternalCode, true);
            Field(x => x.InternalCode_in, true);
            Field(x => x.InternalCode_contains, true);
            Field(x => x.Fields, true, typeof(FieldsWhereInputGraphType));
            Field(x => x.LastModifiedAt_lt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.LastModifiedAt_gt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.CreatedAt_lt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.CreatedAt_gt, true, typeof(DateTimeOffsetGraphType));
            Field(x => x.HavingMovementType, true);
            Field(x => x.HavingEndDate, true);
            Field(x => x.UnderwritingResult, true, typeof(PolicyMemberUnderwritingResultGraph));
            Field(x => x.UnderwritingResult_in, true, typeof(ListGraphType<PolicyMemberUnderwritingResultGraph>));
            Field(x => x.ValidationResult, true, typeof(PolicyMemberValidationResultGraph));
            Field(x => x.ValidationResult_in, true, typeof(ListGraphType<PolicyMemberValidationResultGraph>));
            Field(x => x.EndorsementId, true);
            Field(x => x.EndorsementId_in, true);
        }
    }

    public class PolicyMembersFilterAggregateInputGraph : InputObjectGraphType<Filter<PolicyMembersFilter>>
    {
        public PolicyMembersFilterAggregateInputGraph()
        {
            Name = "policyMembersFilterAggregateInput";
            Description = "Policy Members Filter Aggregate Input";

            Field(x => x.And, true, typeof(ListGraphType<PolicyMembersFilterAggregateInputGraph>));
            Field(x => x.Or, true, typeof(ListGraphType<PolicyMembersFilterAggregateInputGraph>));
            Field(x => x.Where, true, typeof(PolicyMembersFilterInputGraph));
        }
    }
    public class PolicyMembersMovementsReport
    {
        public long TotalCount { get; set; }
        public List<PolicyMemberMovementReportItem> List { get; set; }
    }

    public class PolicyMembersMovementsReportGraph : ObjectGraphType<PolicyMembersMovementsReport>
    {

        private static void MembersMovementsReportFillPolicyInfo(IReadOnlyCollection<PolicyMemberMovementReportItem> reportItems, List<Policy> policies)
        {
            var policiesMap = policies.ToDictionary(x => x.Id, x => x);
            var endorsementsMap = policies.SelectMany(x => x.Endorsements).ToDictionary(x => x.Id, x => x);
            foreach (var reportItem in reportItems)
            {
                if (policiesMap.TryGetValue(reportItem.PolicyId, out var policy))
                {
                    reportItem.Policy = PolicyGraph.ToGraph(policiesMap[reportItem.PolicyId]);
                }
                if (endorsementsMap.TryGetValue(reportItem.EndorsementId, out var endorsement))
                {
                    reportItem.Endorsement = EndorsementGraph.ToGraph(reportItem.PolicyId, endorsement);
                }
            }
        }

        public PolicyMembersMovementsReportGraph(CoverGoPolicyMembersService policyMembersService, IPolicyService policyService)
        {
            Name = "policyMembersMovementsReport";
            Description = "Policy Members Movements Report";

            Field(x => x.TotalCount, true)
                .Description("Report total reportItems")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    PolicyMembersWhere where =
                        context.ComputeArgAndVar<PolicyMembersWhere, PolicyMembersMovementsReport>("where");
                    long count =
                        await policyMembersService.ReportCountAsync(tenantId, where, context.CancellationToken);
                    return count;
                });

            Field(x => x.List, true, typeof(ListGraphType<PolicyMemberMovementReportItemGraph>))
                .Description("Report")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    PolicyMembersWhere where =
                        context.ComputeArgAndVar<PolicyMembersWhere, PolicyMembersMovementsReport>("where");

                    IReadOnlyCollection<PolicyMemberMovementReportItem> reportItems =
                        await policyMembersService.ReportQueryAsync(tenantId, where, context.CancellationToken);

                    var policiesIds = reportItems.Select(x => x.PolicyId).ToList();
                    var policies = await policyService.GetAsync(tenantId, new PolicyWhere { Id_in = policiesIds });

                    MembersMovementsReportFillPolicyInfo(reportItems, policies);

                    return reportItems.ToList();
                });
        }
    }

    public class PolicyMembersWhereInputGraph : InputObjectGraphType<PolicyMembersWhere>
    {
        public PolicyMembersWhereInputGraph()
        {
            Name = "policyMembersWhereInput";

            Field(x => x.PolicyId_In, nullable: true, typeof(ListGraphType<StringGraphType>));
            Field(x => x.EndorsementId_In, nullable: true, typeof(ListGraphType<StringGraphType>));
            Field(x => x.First, true);
            Field(x => x.Skip, true);
            Field(x => x.AsOf, true, typeof(DateTimeGraphType));
            Field(x => x.OrderBy, true, typeof(SortGraphType));
        }
    }

    public class PolicyMemberMovementReportItem
    {
        public string PolicyId { get; set; }
        public string EndorsementId { get; set; }
        public decimal Amount { get; set; }
        public string MovementType { get; set; }
        public PolicyGraph Policy { get; set; }
        public EndorsementGraph Endorsement { get; set; }
    }

    public class PolicyMemberMovementReportItemGraph : ObjectGraphType<PolicyMemberMovementReportItem>
    {
        public PolicyMemberMovementReportItemGraph()
        {
            Name = "policyMemberMovementReportItem";
            Field(x => x.Amount, true);
            Field(x => x.MovementType, true);
            Field(x => x.Policy, type: typeof(PolicyGraphType), nullable: true);
            Field(x => x.Endorsement, type: typeof(EndorsementGraphType), nullable: true);
        }
    }

    public class PoliciesPerIndividual
    {
        public string IndividualId { get; set; }
        public string[] PolicyIds { get; set; }
    }

    public class PoliciesPerIndividualFilter
    {
        public string[] IndividualIds { get; set; }
        public DateTime? AsOf { get; set; }
    }

    public class PolicyMembersAssignInternalCodesCommand
    {
        public string PolicyId { get; set; }

        public string EndorsementId { get; set; }

        public List<string> MemberIds { get; set; }
    }

    public class TimeZoneDateTimeConverter : JsonConverter
    {
        private readonly DateTimeZone _defaultTimeZone;
        public TimeZoneDateTimeConverter(string timeZoneId)
        {
            _defaultTimeZone = TimeZoneGetById(timeZoneId);
        }

        public override bool CanConvert(Type objectType) => objectType == typeof(DateTime) || objectType == typeof(DateTime?);
        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            object value = reader.Value;
            if (value == null) return null;

            (DateTime dateTime, bool success) = DateTimeGetFromValue(value);
            if (!success)
            {
                bool isNullable = !objectType.IsValueType || objectType.IsGenericType && objectType.GetGenericTypeDefinition() == typeof(Nullable<>);
                return isNullable ? (DateTime?)null : DateTime.MinValue;
            }

            DateTimeOffset offset = new(dateTime);

            if (dateTime.Kind == DateTimeKind.Unspecified)
            {
                Offset utcOffset = _defaultTimeZone.GetUtcOffset(Instant.FromDateTimeOffset(offset));
                offset = new DateTimeOffset(dateTime, utcOffset.ToTimeSpan());
            }

            return offset.UtcDateTime;
        }
        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            // do nothing
        }

        static (DateTime dateTime, bool success) DateTimeGetFromValue(object value) =>
            value switch
            {
                string stringValue when DateTime.TryParse(stringValue, out DateTime dateTimeParsed) => (dateTimeParsed, true),
                DateTime time => (time, true),
                _ => (DateTime.MinValue, false)
            };

        private static DateTimeZone TimeZoneGetById(string timeZoneId)
        {
            if (string.IsNullOrEmpty(timeZoneId)) return DateTimeZone.Utc;

            try
            {
                return DateTimeZoneProviders.Tzdb[timeZoneId];
            }
            catch
            {
                return DateTimeZone.Utc;
            }
        }
    }
}