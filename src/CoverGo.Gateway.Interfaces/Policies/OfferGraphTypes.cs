﻿using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Interfaces.Binders;
using CoverGo.Gateway.Interfaces.Cases;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Users;

using GraphQL.DataLoader;
using GraphQL.Types;

using Newtonsoft.Json.Linq;

using System;
using System.Collections.Generic;
using System.Linq;
using CoverGo.DomainUtils;
using Newtonsoft.Json;
using CurrencyCode = CoverGo.Gateway.Domain.CurrencyCode;

namespace CoverGo.Gateway.Interfaces.Policies
{
    public class OfferGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string OfferNumber { get; set; }
        public string PolicyId { get; set; }
        public string PolicyNumber { get; set; }
        public string Status { get; set; }
        public ProductGraph Product { get; set; }
        public IEnumerable<BenefitOptionGraph> BenefitOptions { get; set; }
        public PricingGraph Premium { get; set; }
        public bool IsPremiumOverridden { get; set; }
        public List<ExclusionGraph> Exclusions { get; set; }
        public BenefitSummaryGraph BenefitSummary { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public IEnumerable<ClauseGraph> Clauses { get; set; }
        public IEnumerable<JacketInstanceGraph> Jackets { get; set; }
        public IEnumerable<FactGraph> Facts { get; set; }
        public string ValuesJsonString { get; set; }
        public IEnumerable<KeyScalarValue> Values { get; set; }
        public IEnumerable<StakeholderGraph> Stakeholders { get; set; }
        public IEnumerable<AssociatedContractGraph> AssociatedContracts { get; set; }
        public IEnumerable<CommissionGraph> CommissionSplitRules { get; set; }
        public IEnumerable<CommissionGraph> CommissionSummary { get; set; }
        public IEnumerable<EventLogGraph> Events { get; set; }
        public decimal? Amount { get; set; } // used for subscriptions
        public CurrencyCode CurrencyCode { get; set; } // used for subscriptions

        public string Pricing { get; set; }
        public string Underwriting { get; set; }
        public string Fields { get; set; }
        public DataSchemaGraph FieldsSchema { get; set; }
        public string ProductTreeId { get; set; }
        public List<ProductTreeRecord> ProductTreeRecords { get; set; }
        public string DistributorID { get; set; }
        public List<string> CampaignCodes { get; set; }

        public static OfferGraph ToGraph(Offer o)
        {
            if (o == null)
                return null;

            var offerPremium = PricingGraph.ToGraph(o.Premium, o.ProductId);

            IEnumerable<CommissionGraph> commissionGraphs = offerPremium != null
                 ? o.Commissions?.Select(c => c.ToGraph(offerPremium, o.Commissions))
                 : o.Premium != null
                    ? o.Commissions?.Select(c => c.ToGraph(offerPremium, o.Commissions))
                    : Enumerable.Empty<CommissionGraph>();

            var offerGraph = new OfferGraph
            {
                Id = o.Id,
                OfferNumber = o.OfferNumber,
                PolicyId = o.PolicyId,
                PolicyNumber = o.PolicyNumber,
                Status = o.Status,
                Product = new ProductGraph { ProductId = o.ProductId },
                StartDate = o.StartDate,
                EndDate = o.EndDate,
                ValuesJsonString = o.Values?.ToString(),
                Values = o.Values?.Children<JProperty>().Select(p => new KeyScalarValue { Key = p.Name, Value = p.Value?.ToScalarValue() }),
                Clauses = o.Clauses?.Select(c => ClauseGraph.ToGraph(c))?.OrderBy(c => c.Order),
                Jackets = o.Jackets?.Select(JacketInstanceGraph.ToGraph).OrderBy(c => c.Order),
                AssociatedContracts = o.AssociatedContracts?.Select(c => new AssociatedContractGraph { Id = c.Id, Contract = new BinderGraph { Id = c.ContractId } }),
                CommissionSplitRules = commissionGraphs,
                CommissionSummary = commissionGraphs?.Summarize(offerPremium),
                Facts = o.Facts?.Select(f => FactGraph.ToGraph(f)),
                Stakeholders = o.Stakeholders?.Select(s => StakeholderGraph.ToGraph(s)),
                BenefitOptions = o.BenefitOptions?.Select(b => new BenefitOptionGraph
                {
                    ProductId = o.ProductId,
                    BenefitTypeId = b.TypeId,
                    Key = b.Key,
                    Value = b.Value?.ToScalarValue(),
                    RawData = b.Value,
                    CurrencyCode = o.Premium?.CurrencyCode ?? CurrencyCode.Undefined,
                }.PopulateSystemGraphFields(b)),
                Exclusions = o.Exclusions?.Select(e => new ExclusionGraph
                {
                    Id = e.Id,
                    BenefitOptionKey = e.BenefitOptionKey,
                    BenefitParentTypeId = e.BenefitParentTypeId,
                    BenefitTypeId = e.BenefitTypeId,
                    CodeName = new CodeNameGraph { Code = e.Code, ProductId = o.ProductId },
                    Remark = e.Remark,
                }.PopulateSystemGraphFields(e)).ToList(),
                Premium = offerPremium,
                IsPremiumOverridden = o.IsPremiumOverridden,
                Events = o.Events?.Select(e => new EventLogGraph
                {
                    Id = e.Id,
                    ById = e.ById,
                    Timestamp = e.Timestamp,
                    Type = e.Type
                }),
                DistributorID = o.DistributorID,
                CampaignCodes = o.CampaignCodes,
            }.PopulateSystemGraphFields(o);

            offerGraph.BenefitSummary = new BenefitSummaryGraph
            {
                ProductId = offerGraph.Product?.ProductId,
                Values = o.Values,
                BenefitOptions = o.BenefitOptions?.Select(s => new Benefit
                {
                    TypeId = s.TypeId,
                    OptionKey = s.Key,
                    Value = s.Value,
                    CurrencyCode = s.CurrencyCode
                })?.ToList()
            };

            offerGraph.Pricing = o.Pricing;
            offerGraph.Underwriting = o.Underwriting;
            offerGraph.Fields = o.Fields2 != null ? o.Fields2.ToString(Formatting.None) : o.Fields;
            offerGraph.FieldsSchema = o.FieldsSchemaId == null ? null : new DataSchemaGraph { Id = o.FieldsSchemaId };
            offerGraph.ProductTreeId = o.ProductTreeId;
            offerGraph.ProductTreeRecords = o.ProductTreeRecords;
            offerGraph.DistributorID = o.DistributorID;
            offerGraph.CampaignCodes = o.CampaignCodes;

            return offerGraph;
        }
    }

    public class BenefitSummaryGraph
    {
        public ProductId ProductId { get; set; } // Passed only for resolving graph type
        public List<Benefit> BenefitOptions { get; set; } // Passed only for resolving graph type
        public JToken Values { get; set; } // Passed only for resolving graph type
        public List<BenefitGraph> Benefits { get; set; }
        public List<BenefitGraph2> BenefitGraphs { get; set; }
    }

    public class BenefitSummaryGraphType : ObjectGraphType<BenefitSummaryGraph>
    {
        public BenefitSummaryGraphType(IDataLoaderContextAccessor accessor,
            IProductService productService)
        {
            Name = "benefitSummary";
            Description = "A benefitSummary";

            Field(b => b.Benefits, type: typeof(ListGraphType<BenefitGraphType>))
                .ResolveAsync(async context =>
                {
                    if (context.Source.ProductId == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();

                    Product2 productDto = (await productService.GetAsync(tenantId, clientId, new ProductQuery { Where = new ProductWhere { Id_in = new List<ProductId> { context.Source.ProductId } }, Factors = context.Source.Values })).FirstOrDefault();
                    if (productDto == null)
                        return null;

                    IEnumerable<Benefit> summarizedBenefits = ProductExtensions.SummarizeBenefits(context.Source.BenefitOptions, productDto?.Benefits);
                    return summarizedBenefits?.Select(b => b.ToGraph(context.Source.ProductId))?.ToList();
                });

            Field(b => b.BenefitGraphs, type: typeof(ListGraphType<BenefitGraph2GraphType>))
                .ResolveAsync(async context =>
                {
                    if (context.Source.ProductId == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();

                    Product2 productDto = (await productService.GetAsync(tenantId, clientId, new ProductQuery { Where = new ProductWhere { Id_in = new List<ProductId> { context.Source.ProductId } }, Factors = context.Source.Values })).FirstOrDefault();
                    if (productDto == null)
                        return null;

                    IEnumerable<Benefit> summarizedBenefits = ProductExtensions.SummarizeBenefits(context.Source.BenefitOptions, productDto?.Benefits);

                    return ProductExtensions.ToGraph(summarizedBenefits, context.Source.ProductId)?.ToList();
                });
        }
    }

    public class OfferGraphType : ObjectGraphType<OfferGraph>
    {
        public OfferGraphType(
            IDataLoaderContextAccessor accessor,
            IProductService productService,
            ICaseService caseService,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "offer";
            Description = "An offer";

            Field(o => o.Id);
            Field(o => o.Status, nullable: true);
            Field(o => o.OfferNumber, nullable: true);
            Field(o => o.PolicyId, nullable: true);
            Field(o => o.PolicyNumber, nullable: true);
            Field(o => o.Premium, type: typeof(PricingGraphType));
            Field(o => o.IsPremiumOverridden);
            Field(p => p.BenefitOptions, type: typeof(ListGraphType<BenefitOptionChoiceGraphType>));
            Field(p => p.Product, type: typeof(ProductGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.Product?.ProductId == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();

                    bool loadRepresentation = context.ProductRepresentationRequested();

                    var productLoader = accessor.Context.GetOrAddBatchLoader<ProductId, Product2>("GetProducts",
                        p => productService.GetDictionaryAsync(tenantId, clientId, new ProductWhere { Id_in = p?.ToList() }, loadRepresentation));

                    Product2 productDto = await productLoader.LoadAsync(context.Source.Product.ProductId);

                    return productDto.ToGraph();
                });

            Field(p => p.Exclusions, type: typeof(ListGraphType<ExclusionGraphType>));
            Field(p => p.BenefitSummary, type: typeof(BenefitSummaryGraphType));
            Field(p => p.StartDate, type: typeof(DateTimeGraphType));
            Field(p => p.EndDate, type: typeof(DateTimeGraphType));
            Field(p => p.Clauses, type: typeof(ListGraphType<ClauseGraphType>))
                .GetPaginationArguments()
                .Resolve(context => context.Source.Clauses.Sort(context));
            Field(p => p.Jackets, type: typeof(ListGraphType<JacketInstanceGraphType>))
                .GetPaginationArguments()
                .Resolve(context => context.Source.Jackets.Sort(context));

            Field(p => p.Facts, type: typeof(ListGraphType<FactGraphType>));
            Field(p => p.ValuesJsonString, nullable: true);
            Field(p => p.Values, type: typeof(ListGraphType<KeyValueGraphType>));
            Field(p => p.Stakeholders, type: typeof(ListGraphType<StakeholderGraphType>));

            Field(p => p.AssociatedContracts, type: typeof(ListGraphType<AssociatedContractGraphType>));
            Field(p => p.CommissionSplitRules, type: typeof(ListGraphType<CommissionGraphType>));
            Field(p => p.CommissionSummary, type: typeof(ListGraphType<CommissionGraphType>))
            //.Argument<NonNullGraphType<ExchangeRateConversionOptionsInputGraphType>>("options", "The conversion options")
            //.ResolveAsync(async context =>
            //{
            //    string tenantId = context.GetTenantIdFromToken();

            //    ExchangeRateConversionOptions options = context.GetArgument<ExchangeRateConversionOptions>("options");
            //    var pricesLoader = accessor.Context.GetOrAddCollectionBatchLoader<PriceDto, PriceDto>("GetConvertedPrices",
            //        async p => (await pricingService.GetConvertedPricingsAsync(tenantId, new ExchangeRateConversionInput
            //        {
            //            Options = options,
            //            Prices = p
            //        }))
            //        .ToLookup(x => x, new PriceDtoDataLoaderEqualityComparer()));

            //    PriceDto priceDto = PricingGraph.ToDomain(context.Source.CommissionSummary.Select(c => c.);
            //    priceDto.DataLoaderCacheId = Guid.NewGuid().ToString();
            //    IEnumerable<PriceDto> priceDtos = await pricesLoader.LoadAsync(priceDto);

            //    return priceDtos.Select(p => PricingGraph.ToGraph(p, null));
            //});
            ;


            Field(p => p.Amount, nullable: true);
            Field(p => p.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));

            Field(p => p.Pricing, nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.Fields == null)
                        return null;
                    if (context.Source.Product?.ProductId == null)
                        return null;
                    if (context.Source.Pricing != null)
                        return context.Source.Pricing;

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();

                    Result<string> result = await productService.Evaluate(tenantId, clientId, new EvaluateProductScriptCommand
                    {
                        DataInput = context.Source.Fields,
                        ProductId = context.Source.Product.ProductId,
                        ScriptType = ScriptTypeEnum.Pricing
                    });

                    return result.Value;
                });
            Field(p => p.Underwriting, nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.Fields == null)
                        return null;
                    if (context.Source.Product?.ProductId == null)
                        return null;
                    if (context.Source.Underwriting != null)
                        return context.Source.Underwriting;

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();

                    Result<string> result = await productService.Evaluate(tenantId, clientId, new EvaluateProductScriptCommand
                    {
                        DataInput = context.Source.Fields,
                        ProductId = context.Source.Product.ProductId,
                        ScriptType = ScriptTypeEnum.Underwriting
                    });

                    return result.Value;
                });
            Field(p => p.Fields, nullable: true);

            Field(p => p.FieldsSchema, type: typeof(DataSchemaGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.FieldsSchema?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var batchLoader = accessor.Context.GetOrAddBatchLoader<string, DataSchema>("GetDataSchemas",
                        async ids => (await caseService.GetDataSchemasAsync(tenantId, new DataSchemaWhere { Id_in = ids?.ToList() })).ToDictionary(p => p.Id));

                    return DataSchemaGraph.ToGraph(await batchLoader.LoadAsync(context.Source.FieldsSchema.Id));
                });

            Field(p => p.ProductTreeId, nullable: true);
            Field(p => p.ProductTreeRecords, type: typeof(ListGraphType<ProductTreeRecordGraphType>));
            Field(p => p.DistributorID, nullable: true);
            Field(p => p.CampaignCodes, nullable: true);

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class OfferWhereInputGraphType : InputObjectGraphType<OfferWhere>
    {
        public OfferWhereInputGraphType()
        {
            Name = "offerWhere";

            Field(o => o.Id, nullable: true);
            Field(o => o.Id_in, nullable: true);

            Field(o => o.OfferNumber, nullable: true);
            Field(o => o.OfferNumber_in, nullable: true);

            Field(o => o.Status, nullable: true);
            Field(o => o.ProductId_in, type: typeof(ListGraphType<ProductIdInputGraphType>), nullable: true);
        }
    }

    public class AddOfferInputGraphType : InputObjectGraphType<AddOfferInputGraph>
    {
        public AddOfferInputGraphType()
        {
            Name = "addOfferInput";
            Description = "adds an offer to a quote";

            Field(o => o.Status, nullable: true);
            Field(o => o.OfferNumberType, nullable: true);
            Field(o => o.PolicyNumber, nullable: true);
            Field(o => o.ProductId, type: typeof(ProductIdInputGraphType));
            Field(o => o.Values, type: typeof(ListGraphType<KeyValueInputGraphType>));
            Field(o => o.BenefitOptions, type: typeof(ListGraphType<BenefitOptionInputGraphType>), nullable: true);
            Field(o => o.Premium, type: typeof(PremiumInputGraphType));
            Field(o => o.StartDate, type: typeof(DateGraphType));
            Field(o => o.EndDate, type: typeof(DateGraphType));
            Field(o => o.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(o => o.Amount, nullable: true);
            Field(o => o.PolicyId, nullable: true);
            Field(o => o.ContractId, nullable: true);
            Field(o => o.Pricing, nullable: true);
            Field(o => o.Underwriting, nullable: true);
            Field(o => o.Fields, nullable: true);
            Field(o => o.FieldsSchemaId, nullable: true);
            Field(o => o.ProductTreeId, nullable: true);
            Field(o => o.ProductTreeRecords, type: typeof(ListGraphType<ProductTreeRecordInputGraphType>));
            Field(o => o.DistributorID, nullable: true);
            Field(o => o.CampaignCodes, nullable: true);
        }
    }

    public class AddOfferInputGraph
    {
        public string OfferNumber { get; set; }
        public string PolicyNumber { get; set; } //used in cases when generating policies
        public string Status { get; set; }
        public string OfferNumberType { get; set; }
        public List<KeyScalarValue> Values { get; set; }
        public ProductId ProductId { get; set; }
        public List<BenefitOptionInputGraph> BenefitOptions { get; set; }
        public PremiumInput Premium { get; set; }
        public string AddedById { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        public string PolicyId { get; set; } // used for Subscriptions
        public decimal Amount { get; set; } // used for Subscriptions
        public CurrencyCode CurrencyCode { get; set; } // used for Subscriptions

        public string ContractId { get; set; }
        public string Pricing { get; set; }
        public string Underwriting { get; set; }
        public string Fields { get; set; }
        public string FieldsSchemaId { get; set; }
        public string ProductTreeId { get; set; }
        public List<ProductTreeRecord> ProductTreeRecords { get; set; }
        public string DistributorID { get; set; }
        public List<string> CampaignCodes { get; set; }
    }

    public class UpdateOfferGraphType : InputObjectGraphType<UpdateOfferInputGraph>
    {
        public UpdateOfferGraphType()
        {
            Name = "updateOfferInput";
            Description = "updates an offer of a quote";

            Field(o => o.Status, nullable: true);
            Field(o => o.OfferNumber, nullable: true);
            Field(o => o.PolicyNumber, nullable: true);
            Field(o => o.ProductId, type: typeof(ProductIdInputGraphType));
            Field(o => o.Premium, type: typeof(PremiumInputGraphType));
            Field(o => o.Values, type: typeof(ListGraphType<KeyValueInputGraphType>));
            Field(o => o.ProrationBehaviour, type: typeof(ProrationBehaviourEnumerationGraphType));
            Field(o => o.StartDate, type: typeof(DateGraphType));
            Field(o => o.EndDate, type: typeof(DateGraphType));
            Field(o => o.ContractId, nullable: true);
            Field(o => o.Pricing, nullable: true);
            Field(o => o.Underwriting, nullable: true);
            Field(o => o.Fields, nullable: true);
            Field(o => o.FieldsSchemaId, nullable: true);
            Field(o => o.ProductTreeId, nullable: true);
            Field(o => o.DistributorID, nullable: true);
            Field(o => o.CampaignCodes, nullable: true);
        }
    }

    public class UpdateOfferInputGraph
    {
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public string OfferNumber { get; set; }
        public bool IsOfferNumberChanged { get; set; }
        public string PolicyNumber { get; set; } // used when generating policies
        public bool IsPolicyNumberChanged { get; set; }
        public List<KeyScalarValue> Values { get; set; }
        public bool IsValuesChanged { get; set; }
        public string OfferId { get; set; }
        public bool IsProductIdChanged { get; set; }
        public ProductIdToUpdate ProductId { get; set; }
        public bool IsPremiumChanged { get; set; }
        public PremiumToUpdate Premium { get; set; }
        public DateTime? StartDate { get; set; }
        public bool IsStartDateChanged { get; set; }
        public ProrationBehaviour ProrationBehaviour { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsEndDateChanged { get; set; }
        public string ContractId { get; set; }
        public bool IsContractIdChanged { get; set; }
        public string Pricing { get; set; }
        public bool IsPricingChanged { get; set; }
        public string Underwriting { get; set; }
        public bool IsUnderwritingChanged { get; set; }
        public string Fields { get; set; }
        public bool IsFieldsChanged { get; set; }
        public string FieldsSchemaId { get; set; }
        public bool IsFieldsSchemaIdChanged { get; set; }
        public string ProductTreeId { get; set; }
        public bool IsProductTreeIdChanged { get; set; }
        public string DistributorID { get; set; }
        public bool IsDistributorIDChanged { get; set; }
        public List<string> CampaignCodes { get; set; }
        public bool IsCampaignCodesChanged { get; set; }
    }

    public class ProrationBehaviourEnumerationGraphType : EnumerationGraphType<ProrationBehaviour>
    {
        public ProrationBehaviourEnumerationGraphType()
        {
            Name = "prorationBehaviourEnumeration";
            Description = "A list of possible proration behaviours";
        }
    }
}
