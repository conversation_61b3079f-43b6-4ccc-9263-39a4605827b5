using System;
using System.Collections.Generic;
using System.Linq;

using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Interfaces.Auth;
using CoverGo.Gateway.Interfaces.Binders;
using CoverGo.Gateway.Interfaces.Cases;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Transactions;
using CoverGo.Gateway.Interfaces.Users;

using GraphQL.DataLoader;
using GraphQL.Types;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Interfaces.Policies
{
    public class EndorsementSourceGraphType : EnumerationGraphType<EndorsementSource>
    {
        public EndorsementSourceGraphType()
        {
            Name = "endorsementSource";
            Description = "ui where endorsement was created";
        }
    }

    public sealed class EndorsementPaymentProcessTypeGraphType : EnumerationGraphType<PaymentProcessType>
    {
        public EndorsementPaymentProcessTypeGraphType()
        {
            Name = "paymentProcessType";
            Description = "paymentProcess type";
        }
    }

    public sealed class EndorsementUnderwritingStatusGraphType : EnumerationGraphType<EndorsementUnderwritingStatus>
    {
        public EndorsementUnderwritingStatusGraphType()
        {
            Name = "underwritingStatus";
            Description = "underwriting status";
        }
    }

    public sealed class EndorsementPaymentProcessStatusGraphType : EnumerationGraphType<EndorsementPaymentProcessStatus>
    {
        public EndorsementPaymentProcessStatusGraphType()
        {
            Name = "paymentProcessStatus";
            Description = "paymentProcess status";
        }
    }

    public sealed class EndorsementMemberMovementVersionGraphType : EnumerationGraphType<MemberMovementVersion>
    {
        public EndorsementMemberMovementVersionGraphType()
        {
            Name = "memberMovementVersions";
            Description = "member movement versions";
        }

        protected override string ChangeEnumCase(string val) => val;
    }

    public class EndorsementGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Status { get; set; }
        public EndorsementSource? Source { get; set; }
        public string Type { get; set; }
        public string ReasonOfChange { get; set; }
        public string ReasonRemarks { get; set; }
        public string? Fields { get; set; }
        public string CancellationMotive { get; set; }
        public DateTime? EffectiveDate { get; set; }
        public DateTime? SubmissionDate { get; set; }
        public bool IsApproved { get; set; }
        public bool IsRejected { get; set; }
        public DateTime? EndorsementDate { get; set; }
        public List<TransactionGraph> Transactions { get; set; }
        public string PolicyId { get; set; } //used only for resolving original and modified policy in graphType
        public PolicyGraph BeforeEndorsement { get; set; }
        public PolicyGraph AfterEndorsement { get; set; }
        public PolicyGraph Policy { get; set; }
        public string QuoteId { get; set; }
        public string UnderwritingCaseId { get; set; }
        public EndorsementUnderwritingStatus? UnderwritingStatus { get; set; }
        public bool? IsPremiumAdjustmentReviewed { get; set; }
        public EndorsementPaymentProcessStatus? PaymentProcessStatus { get; set; }
        public PaymentProcessType? PaymentProcessType { get; set; }
        public MemberMovementVersion? MemberMovementVersion { get; set; }
        public string InternalCode { get; set; }
        public IEnumerable<UpdatePolicyCommandGraph> UpdatePolicyCommands { get; set; }

        public IEnumerable<AddDiscountCommandGraph> AddDiscountCommands { get; set; }
        public IEnumerable<UpdateDiscountCommandGraph> UpdateDiscountCommands { get; set; }
        public IEnumerable<RemoveCommandGraph> RemoveDiscountCommands { get; set; }
        public IEnumerable<AddLoadingCommandGraph> AddLoadingCommands { get; set; }
        public IEnumerable<UpdateLoadingCommandGraph> UpdateLoadingCommands { get; set; }
        public IEnumerable<RemoveCommandGraph> RemoveLoadingCommands { get; set; }

        public IEnumerable<AddFactCommandGraph> AddPolicyFactCommands { get; set; }
        public IEnumerable<UpdateFactCommandGraph> UpdatePolicyFactCommands { get; set; }
        public IEnumerable<RemoveCommandGraph> RemovePolicyFactCommands { get; set; }
        public IEnumerable<UpsertBenefitOptionCommandGraph> UpsertBenefitOptionCommands { get; set; }
        public IEnumerable<RemoveBenefitOptionCommandGraph> RemoveBenefitOptionCommands { get; set; }

        public IEnumerable<UpdateContractIndividualCommandGraph> UpdateOtherContractHolderIndividualCommands { get; set; }
        public IEnumerable<UpdateContractIndividualCommandGraph> UpdateContractInsuredIndividualCommands { get; set; }
        public IEnumerable<UpdateContractCompanyCommandGraph> UpdateOtherContractHolderCompanyCommands { get; set; }
        public IEnumerable<UpdateContractCompanyCommandGraph> UpdateContractInsuredCompanyCommands { get; set; }
        public IEnumerable<UpdateContractObjectCommandGraph> UpdateContractInsuredObjectCommands { get; set; }

        public IEnumerable<AddEntityCommandGraph> AddOtherContractHolderCommands { get; set; }
        public IEnumerable<AddEntityCommandGraph> AddContractInsuredCommands { get; set; }
        public IEnumerable<RemoveCommandGraph> RemoveOtherContractHolderCommands { get; set; }
        public IEnumerable<RemoveCommandGraph> RemoveContractInsuredCommands { get; set; }

        public IEnumerable<AddFactCommandGraph> AddContractHolderFactCommands { get; set; }
        public IEnumerable<AddFactCommandGraph> AddOtherContractHolderFactCommands { get; set; }
        public IEnumerable<AddFactCommandGraph> AddContractInsuredFactCommands { get; set; }
        public IEnumerable<UpdateFactCommandGraph> UpdateContractHolderFactCommands { get; set; }
        public IEnumerable<UpdateFactCommandGraph> UpdateOtherContractHolderFactCommands { get; set; }
        public IEnumerable<UpdateFactCommandGraph> UpdateContractInsuredFactCommands { get; set; }
        public IEnumerable<RemoveCommandGraph> RemoveContractHolderFactCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommandGraph> RemoveOtherContractHolderFactCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommandGraph> RemoveContractInsuredFactCommands { get; set; }

        public IEnumerable<AddIdentityCommandGraph> AddContractHolderIdentityCommands { get; set; }
        public IEnumerable<AddIdentityCommandGraph> AddOtherContractHolderIdentityCommands { get; set; }
        public IEnumerable<AddIdentityCommandGraph> AddContractInsuredIdentityCommands { get; set; }
        public IEnumerable<UpdateIdentityCommandGraph> UpdateContractHolderIdentityCommands { get; set; }
        public IEnumerable<UpdateIdentityCommandGraph> UpdateOtherContractHolderIdentityCommands { get; set; }
        public IEnumerable<UpdateIdentityCommandGraph> UpdateContractInsuredIdentityCommands { get; set; }
        public IEnumerable<RemoveCommandGraph> RemoveContractHolderIdentityCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommandGraph> RemoveOtherContractHolderIdentityCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommandGraph> RemoveContractInsuredIdentityCommands { get; set; }

        public IEnumerable<AddContactCommandGraph> AddContractHolderContactCommands { get; set; }
        public IEnumerable<AddContactCommandGraph> AddOtherContractHolderContactCommands { get; set; }
        public IEnumerable<AddContactCommandGraph> AddContractInsuredContactCommands { get; set; }
        public IEnumerable<UpdateContactCommandGraph> UpdateContractHolderContactCommands { get; set; }
        public IEnumerable<UpdateContactCommandGraph> UpdateOtherContractHolderContactCommands { get; set; }
        public IEnumerable<UpdateContactCommandGraph> UpdateContractInsuredContactCommands { get; set; }
        public IEnumerable<RemoveCommandGraph> RemoveContractHolderContactCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommandGraph> RemoveOtherContractHolderContactCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommandGraph> RemoveContractInsuredContactCommands { get; set; }

        public IEnumerable<AddAddressCommandGraph> AddContractHolderAddressCommands { get; set; }
        public IEnumerable<AddAddressCommandGraph> AddOtherContractHolderAddressCommands { get; set; }
        public IEnumerable<AddAddressCommandGraph> AddContractInsuredAddressCommands { get; set; }
        public IEnumerable<UpdateAddressCommandGraph> UpdateContractHolderAddressCommands { get; set; }
        public IEnumerable<UpdateAddressCommandGraph> UpdateOtherContractHolderAddressCommands { get; set; }
        public IEnumerable<UpdateAddressCommandGraph> UpdateContractInsuredAddressCommands { get; set; }
        public IEnumerable<RemoveCommandGraph> RemoveContractHolderAddressCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommandGraph> RemoveOtherContractHolderAddressCommands { get; set; }
        public IEnumerable<RemoveEntityPrimitiveCommandGraph> RemoveContractInsuredAddressCommands { get; set; }

        public IEnumerable<AddAssociatedContractCommandGraph> AddAssociatedContractCommands { get; set; }
        public IEnumerable<RemoveAssociatedContractCommandGraph> RemoveAssociatedContractCommands { get; set; }
        public IEnumerable<AddClauseCommandGraph> AddClauseCommands { get; set; }

        public static EndorsementGraph ToGraph(string policyId, Endorsement domain)
        {
            var graph = new EndorsementGraph
            {
                Id = domain.Id,
                Status = domain.Status,
                Source = domain.Source,
                IsApproved = domain.IsApproved,
                Type = domain.Type,
                ReasonOfChange = domain.ReasonOfChange,
                ReasonRemarks = domain.ReasonRemarks,
                Fields = domain.Fields?.ToString(),
                CancellationMotive = domain.CancellationMotive,
                EffectiveDate = domain.EffectiveDate,
                IsRejected = domain.IsRejected,
                EndorsementDate = domain.EndorsementDate,
                SubmissionDate = domain.SubmissionDate,
                QuoteId = domain.QuoteId,
                UnderwritingCaseId = domain.UnderwritingCaseId,
                UnderwritingStatus = domain.UnderwritingStatus,
                IsPremiumAdjustmentReviewed = domain.IsPremiumAdjustmentReviewed,
                PaymentProcessType = domain.PaymentProcessType,
                PaymentProcessStatus = domain.PaymentProcessStatus,
                CreatedAt = domain.CreatedAt,
                PolicyId = policyId,
                MemberMovementVersion = domain.MemberMovementVersion,
                InternalCode = domain.InternalCode,
                UpdatePolicyCommands = domain.UpdatePolicyCommands?.Select(p => UpdatePolicyCommandGraph.ToGraph(p)),

                AddDiscountCommands = domain.AddDiscountCommands?.Select(d => AddDiscountCommandGraph.ToGraph(d)),
                UpdateDiscountCommands = domain.UpdateDiscountCommands?.Select(d => UpdateDiscountCommandGraph.ToGraph(d)),
                RemoveDiscountCommands = domain.RemoveDiscountCommands?.Select(d => RemoveCommandGraph.ToGraph(d)),

                AddLoadingCommands = domain.AddLoadingCommands?.Select(d => AddLoadingCommandGraph.ToGraph(d)),
                UpdateLoadingCommands = domain.UpdateLoadingCommands?.Select(d => UpdateLoadingCommandGraph.ToGraph(d)),
                RemoveLoadingCommands = domain.RemoveLoadingCommands?.Select(d => RemoveCommandGraph.ToGraph(d)),

                AddPolicyFactCommands = domain.AddPolicyFactCommands?.Select(f => AddFactCommandGraph.ToGraph(f)),
                UpdatePolicyFactCommands = domain.UpdatePolicyFactCommands?.Select(f => UpdateFactCommandGraph.ToGraph(f)),
                RemovePolicyFactCommands = domain.RemovePolicyFactCommands?.Select(d => RemoveCommandGraph.ToGraph(d)),
                UpsertBenefitOptionCommands = domain.UpsertBenefitOptionCommands?.Select(f => UpsertBenefitOptionCommandGraph.ToGraph(f)),
                RemoveBenefitOptionCommands = domain.RemoveBenefitOptionCommands?.Select(d => RemoveBenefitOptionCommandGraph.ToGraph(d)),

                UpdateOtherContractHolderIndividualCommands = domain.UpdateOtherContractHolderIndividualCommands?.Select(h => UpdateContractIndividualCommandGraph.ToGraph(h)),
                UpdateContractInsuredIndividualCommands = domain.UpdateContractInsuredIndividualCommands?.Select(i => UpdateContractIndividualCommandGraph.ToGraph(i)),
                UpdateOtherContractHolderCompanyCommands = domain.UpdateOtherContractHolderCompanyCommands?.Select(h => UpdateContractCompanyCommandGraph.ToGraph(h)),
                UpdateContractInsuredCompanyCommands = domain.UpdateContractInsuredCompanyCommands?.Select(i => UpdateContractCompanyCommandGraph.ToGraph(i)),
                UpdateContractInsuredObjectCommands = domain.UpdateContractInsuredObjectCommands?.Select(o => UpdateContractObjectCommandGraph.ToGraph(o)),

                AddOtherContractHolderCommands = domain.AddOtherContractHolderCommands?.Select(d => AddEntityCommandGraph.ToGraph(d)),
                AddContractInsuredCommands = domain.AddContractInsuredCommands?.Select(d => AddEntityCommandGraph.ToGraph(d)),
                RemoveOtherContractHolderCommands = domain.RemoveOtherContractHolderCommands?.Select(d => RemoveCommandGraph.ToGraph(d)),
                RemoveContractInsuredCommands = domain.RemoveContractInsuredCommands?.Select(d => RemoveCommandGraph.ToGraph(d)),

                AddContractHolderFactCommands = domain.AddContractHolderFactCommands?.Select(f => AddFactCommandGraph.ToGraph(f)),
                AddOtherContractHolderFactCommands = domain.AddOtherContractHolderFactCommands?.Select(f => AddFactCommandGraph.ToGraph(f)),
                AddContractInsuredFactCommands = domain.AddContractInsuredFactCommands?.Select(f => AddFactCommandGraph.ToGraph(f)),
                UpdateContractHolderFactCommands = domain.UpdateContractHolderFactCommands?.Select(f => UpdateFactCommandGraph.ToGraph(f)),
                UpdateOtherContractHolderFactCommands = domain.UpdateOtherContractHolderFactCommands?.Select(f => UpdateFactCommandGraph.ToGraph(f)),
                UpdateContractInsuredFactCommands = domain.UpdateContractInsuredFactCommands?.Select(f => UpdateFactCommandGraph.ToGraph(f)),
                RemoveContractHolderFactCommands = domain.RemoveContractHolderFactCommands?.Select(f => RemoveCommandGraph.ToGraph(f)),
                RemoveOtherContractHolderFactCommands = domain.RemoveOtherContractHolderFactCommands?.Select(f => RemoveEntityPrimitiveCommandGraph.ToGraph(f)),
                RemoveContractInsuredFactCommands = domain.RemoveContractInsuredFactCommands?.Select(f => RemoveEntityPrimitiveCommandGraph.ToGraph(f)),

                AddContractHolderIdentityCommands = domain.AddContractHolderIdentityCommands?.Select(i => AddIdentityCommandGraph.ToGraph(i)),
                AddOtherContractHolderIdentityCommands = domain.AddOtherContractHolderIdentityCommands?.Select(i => AddIdentityCommandGraph.ToGraph(i)),
                AddContractInsuredIdentityCommands = domain.AddContractInsuredIdentityCommands?.Select(i => AddIdentityCommandGraph.ToGraph(i)),
                UpdateContractHolderIdentityCommands = domain.UpdateContractHolderIdentityCommands?.Select(i => UpdateIdentityCommandGraph.ToGraph(i)),
                UpdateOtherContractHolderIdentityCommands = domain.UpdateOtherContractHolderIdentityCommands?.Select(i => UpdateIdentityCommandGraph.ToGraph(i)),
                UpdateContractInsuredIdentityCommands = domain.UpdateContractInsuredIdentityCommands?.Select(i => UpdateIdentityCommandGraph.ToGraph(i)),
                RemoveContractHolderIdentityCommands = domain.RemoveContractHolderIdentityCommands?.Select(f => RemoveCommandGraph.ToGraph(f)),
                RemoveOtherContractHolderIdentityCommands = domain.RemoveOtherContractHolderIdentityCommands?.Select(f => RemoveEntityPrimitiveCommandGraph.ToGraph(f)),
                RemoveContractInsuredIdentityCommands = domain.RemoveContractInsuredIdentityCommands?.Select(f => RemoveEntityPrimitiveCommandGraph.ToGraph(f)),

                AddContractHolderContactCommands = domain.AddContractHolderContactCommands?.Select(c => AddContactCommandGraph.ToGraph(c)),
                AddOtherContractHolderContactCommands = domain.AddOtherContractHolderContactCommands?.Select(c => AddContactCommandGraph.ToGraph(c)),
                AddContractInsuredContactCommands = domain.AddContractInsuredContactCommands?.Select(c => AddContactCommandGraph.ToGraph(c)),
                UpdateContractHolderContactCommands = domain.UpdateContractHolderContactCommands?.Select(c => UpdateContactCommandGraph.ToGraph(c)),
                UpdateOtherContractHolderContactCommands = domain.UpdateOtherContractHolderContactCommands?.Select(c => UpdateContactCommandGraph.ToGraph(c)),
                UpdateContractInsuredContactCommands = domain.UpdateContractInsuredContactCommands?.Select(c => UpdateContactCommandGraph.ToGraph(c)),
                RemoveContractHolderContactCommands = domain.RemoveContractHolderContactCommands?.Select(f => RemoveCommandGraph.ToGraph(f)),
                RemoveOtherContractHolderContactCommands = domain.RemoveOtherContractHolderContactCommands?.Select(f => RemoveEntityPrimitiveCommandGraph.ToGraph(f)),
                RemoveContractInsuredContactCommands = domain.RemoveContractInsuredContactCommands?.Select(f => RemoveEntityPrimitiveCommandGraph.ToGraph(f)),

                AddContractHolderAddressCommands = domain.AddContractHolderAddressCommands?.Select(c => AddAddressCommandGraph.ToGraph(c)),
                AddOtherContractHolderAddressCommands = domain.AddOtherContractHolderAddressCommands?.Select(c => AddAddressCommandGraph.ToGraph(c)),
                AddContractInsuredAddressCommands = domain.AddContractInsuredAddressCommands?.Select(c => AddAddressCommandGraph.ToGraph(c)),
                UpdateContractHolderAddressCommands = domain.UpdateContractHolderAddressCommands?.Select(a => UpdateAddressCommandGraph.ToGraph(a)),
                UpdateOtherContractHolderAddressCommands = domain.UpdateOtherContractHolderAddressCommands?.Select(a => UpdateAddressCommandGraph.ToGraph(a)),
                UpdateContractInsuredAddressCommands = domain.UpdateContractInsuredAddressCommands?.Select(a => UpdateAddressCommandGraph.ToGraph(a)),
                RemoveContractHolderAddressCommands = domain.RemoveContractHolderAddressCommands?.Select(f => RemoveCommandGraph.ToGraph(f)),
                RemoveOtherContractHolderAddressCommands = domain.RemoveOtherContractHolderAddressCommands?.Select(f => RemoveEntityPrimitiveCommandGraph.ToGraph(f)),
                RemoveContractInsuredAddressCommands = domain.RemoveContractInsuredAddressCommands?.Select(f => RemoveEntityPrimitiveCommandGraph.ToGraph(f)),

                AddAssociatedContractCommands = domain.AddAssociatedContractCommands?.Select(a => AddAssociatedContractCommandGraph.ToGraph(a)),
                RemoveAssociatedContractCommands = domain.RemoveAssociatedContractCommands?.Select(a => RemoveAssociatedContractCommandGraph.ToGraph(a)),

                AddClauseCommands = domain.AddClauseCommands?.Select(c => AddClauseCommandGraph.ToGraph(c))
            };

            graph.PopulateSystemGraphFields(domain);

            return graph;
        }
    }

    public class UpdateEndorsementInputGraph
    {
        public bool? IsEffectiveDateChanged { get; set; }
        public DateTime? EffectiveDate { get; set; }

        public bool? IsReasonOfChangeChanged { get; set; }
        public string ReasonOfChange { get; set; }
        public string ReasonRemarks { get; set; }

        public bool? IsFieldsChanged { get; set; }
        public string Fields { get; set; }

        public bool? IsStatusChanged { get; set; }
        public string Status { get; set; }

        public bool? IsCancellationMotiveChanged { get; set; }
        public string CancellationMotive { get; set; }
        public bool? IsUnderwritingStatusChanged { get; set; }
        public EndorsementUnderwritingStatus? UnderwritingStatus { get; set; }
        public bool? IsPaymentProcessStatusChanged { get; set; }
        public EndorsementPaymentProcessStatus? PaymentProcessStatus { get; set; }
    }

    public class UpdateEndorsementInputGraphType : InputObjectGraphType<UpdateEndorsementInputGraph>
    {
        public UpdateEndorsementInputGraphType()
        {
            Name = "updateEndorsementInput";
            Description = "Update Endorsement Input";

            Field(p => p.IsEffectiveDateChanged, nullable: true);
            Field(p => p.EffectiveDate, nullable: true, type: typeof(DateTimeGraphType));

            Field(p => p.IsReasonOfChangeChanged, nullable: true);
            Field(p => p.ReasonOfChange, nullable: true);
            Field(p => p.ReasonRemarks, nullable: true);

            Field(p => p.IsFieldsChanged, nullable: true);
            Field(p => p.Fields, nullable: true);

            Field(p => p.IsCancellationMotiveChanged, nullable: true);
            Field(p => p.CancellationMotive, nullable: true);

            Field(p => p.IsStatusChanged, nullable: true);
            Field(p => p.Status, nullable: true);

            Field(p => p.IsUnderwritingStatusChanged, nullable: true);
            Field(p => p.UnderwritingStatus, nullable: true, type:typeof(EndorsementUnderwritingStatusGraphType));

            Field(p => p.IsPaymentProcessStatusChanged, nullable: true);
            Field(p => p.PaymentProcessStatus, nullable: true, type: typeof(EndorsementPaymentProcessStatusGraphType));
        }
    }

    class EndorsementGraphType : ObjectGraphType<EndorsementGraph>
    {
        public EndorsementGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IPolicyService policyService,
            ITransactionService transactionService,
            PermissionValidator permissionValidator)
        {
            Name = "endorsement";

            Field(e => e.Id);
            Field(e => e.Status, nullable: true);
            Field(e => e.Source, nullable: true, typeof(EndorsementSourceGraphType));
            Field(e => e.Type, nullable: true);
            Field(e => e.ReasonOfChange, nullable: true);
            Field(e => e.ReasonRemarks, nullable: true);
            Field(e => e.Fields, nullable: true);
            Field(e => e.CancellationMotive, nullable: true);
            Field(e => e.EffectiveDate, type: typeof(DateTimeGraphType), nullable: true);
            Field(e => e.IsApproved);
            Field(e => e.IsRejected);
            Field(e => e.QuoteId, nullable: true);
            Field(e => e.UnderwritingCaseId, nullable: true);
            Field(e => e.EndorsementDate, type: typeof(DateTimeGraphType), nullable: true);
            Field(e => e.SubmissionDate, type: typeof(DateTimeGraphType), nullable: true);
            Field(e => e.MemberMovementVersion, type: typeof(EndorsementMemberMovementVersionGraphType), nullable: true);
            Field(e => e.UnderwritingStatus, type: typeof(EndorsementUnderwritingStatusGraphType), nullable: true);
            Field(e => e.PaymentProcessStatus, type: typeof(EndorsementPaymentProcessStatusGraphType), nullable: true);
            Field(e => e.IsPremiumAdjustmentReviewed, nullable: true);
            Field(e => e.PaymentProcessType, type: typeof(EndorsementPaymentProcessTypeGraphType), nullable: true);
            Field(e => e.InternalCode, nullable: true);
            Field(p => p.Transactions, type: typeof(ListGraphType<TransactionGraphType>))
                .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readTransactions");

                   var loader = accessor.Context.GetOrAddCollectionBatchLoader<string, Transaction>("GetTransactionsFromEndorsementIds",
                       endorsementIds => transactionService.GetByEndorsementIdsLookupAsync(tenantId, allowedIds.Contains("all")
                           ? new TransactionWhere { EndorsementId_in = endorsementIds?.ToList() }
                           : new TransactionWhere
                           {
                               And = new List<TransactionWhere>
                               {
                                new() { EndorsementId_in = endorsementIds?.ToList() },
                                new() { Id_in = allowedIds.ToList() }
                               }
                           }));

                   IEnumerable<Transaction> transactions = await loader.LoadAsync(context.Source.Id);

                   return transactions?.Select(l => TransactionGraph.ToGraph(l))?.ToList();
               });
            Field(e => e.BeforeEndorsement, type: typeof(PolicyGraphType))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.Source.PolicyId;

                    bool useEffectiveDate = context.Source.EffectiveDate.HasValue && context.Source.EffectiveDate.Value != DateTime.MinValue && context.Source.EffectiveDate > DateTime.UtcNow;
                    DateTime? asOf = (useEffectiveDate ? context.Source.EffectiveDate.Value : context.Source.CreatedAt)?.AddMilliseconds(-1);

                    Policy policy = await policyService.GetAsync(tenantId, policyId, new Domain.AsOf { DateTime = asOf });

                    PolicyGraph policyGraph = PolicyGraph.ToGraph(policy);
                    policyGraph.AsOf = asOf;
                    return policyGraph;
                });
            Field(e => e.AfterEndorsement, type: typeof(PolicyGraphType))
                .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string policyId = context.Source.PolicyId;
                   Policy policy = null;
                   PolicyGraph policyGraph;
                   if (!context.Source.IsApproved) //preview of endorsement
                   {
                       string previewEndorsementId = context.Source.Id;

                       policy = await policyService.PreviewEndorsementAsync(tenantId, policyId, previewEndorsementId);
                       if (policy != null)
                           policy.IsPricingCacheAvailable = false;  // preview of endorsement shall not use cached pricing

                       policyGraph = PolicyGraph.ToGraph(policy);
                       policyGraph.PreviewEndorsementId = previewEndorsementId;
                       return policyGraph;
                   }

                   bool useEffectiveDate = context.Source.EffectiveDate.HasValue && context.Source.EffectiveDate.Value != DateTime.MinValue && context.Source.EffectiveDate > DateTime.UtcNow;
                   DateTime? asOf = (useEffectiveDate ? context.Source.EffectiveDate.Value : DateTime.UtcNow).AddMilliseconds(1);

                   policy = await policyService.GetAsync(tenantId, policyId, new Domain.AsOf { DateTime = asOf });

                   policyGraph = PolicyGraph.ToGraph(policy);
                   policyGraph.AfterEndorsementId = context.Source.Id;
                   policyGraph.AsOf = asOf;
                   return policyGraph;
               });
            Field(e => e.Policy, type: typeof(PolicyGraphType))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.Source.PolicyId;

                    var dataLoader = accessor.Context.GetOrAddBatchLoader<string, Policy>("GetPolicies",
                          i => policyService.GetDictionaryAsync(tenantId, new PolicyWhere { Id_in = i?.ToList() }));

                    Policy policy = await dataLoader.LoadAsync(policyId);

                    return PolicyGraph.ToGraph(policy);
                });

            Field(e => e.UpdatePolicyCommands, type: typeof(ListGraphType<UpdatePolicyGraphType>));

            Field(e => e.AddDiscountCommands, type: typeof(ListGraphType<AddDiscountCommandGraphType>));
            Field(e => e.UpdateDiscountCommands, type: typeof(ListGraphType<UpdateDiscountCommandGraphType>));
            Field(e => e.RemoveDiscountCommands, type: typeof(ListGraphType<RemoveCommandGraphType>));

            Field(e => e.AddLoadingCommands, type: typeof(ListGraphType<AddLoadingCommandGraphType>));
            Field(e => e.UpdateLoadingCommands, type: typeof(ListGraphType<UpdateLoadingCommandGraphType>));
            Field(e => e.RemoveLoadingCommands, type: typeof(ListGraphType<RemoveCommandGraphType>));

            Field(e => e.AddPolicyFactCommands, type: typeof(ListGraphType<AddFactCommandGraphType>));
            Field(e => e.UpdatePolicyFactCommands, type: typeof(ListGraphType<UpdateFactCommandGraphType>));
            Field(e => e.RemovePolicyFactCommands, type: typeof(ListGraphType<RemoveCommandGraphType>));
            Field(e => e.UpsertBenefitOptionCommands, type: typeof(ListGraphType<UpsertBenefitOptionCommandGraphType>));
            Field(e => e.RemoveBenefitOptionCommands, type: typeof(ListGraphType<RemoveBenefitOptionCommandGraphType>));

            Field(e => e.UpdateOtherContractHolderIndividualCommands, type: typeof(ListGraphType<UpdateContractIndividualCommandGraphType>));
            Field(e => e.UpdateContractInsuredIndividualCommands, type: typeof(ListGraphType<UpdateContractIndividualCommandGraphType>));
            Field(e => e.UpdateOtherContractHolderCompanyCommands, type: typeof(ListGraphType<UpdateContractCompanyCommandGraphType>));
            Field(e => e.UpdateContractInsuredCompanyCommands, type: typeof(ListGraphType<UpdateContractCompanyCommandGraphType>));
            Field(e => e.UpdateContractInsuredObjectCommands, type: typeof(ListGraphType<UpdateContractObjectCommandGraphType>));

            Field(e => e.AddOtherContractHolderCommands, type: typeof(ListGraphType<AddEntityCommandGraphType>));
            Field(e => e.AddContractInsuredCommands, type: typeof(ListGraphType<AddEntityCommandGraphType>));
            Field(e => e.RemoveOtherContractHolderCommands, type: typeof(ListGraphType<RemoveCommandGraphType>));
            Field(e => e.RemoveContractInsuredCommands, type: typeof(ListGraphType<RemoveCommandGraphType>));

            Field(e => e.AddContractHolderFactCommands, type: typeof(ListGraphType<AddFactCommandGraphType>));
            Field(e => e.AddOtherContractHolderFactCommands, type: typeof(ListGraphType<AddFactCommandGraphType>));
            Field(e => e.AddContractInsuredFactCommands, type: typeof(ListGraphType<AddFactCommandGraphType>));
            Field(e => e.UpdateOtherContractHolderFactCommands, type: typeof(ListGraphType<UpdateFactCommandGraphType>));
            Field(e => e.UpdateContractHolderFactCommands, type: typeof(ListGraphType<UpdateFactCommandGraphType>));
            Field(e => e.UpdateContractInsuredFactCommands, type: typeof(ListGraphType<UpdateFactCommandGraphType>));
            Field(e => e.RemoveContractHolderFactCommands, type: typeof(ListGraphType<RemoveCommandGraphType>));
            Field(e => e.RemoveOtherContractHolderFactCommands, type: typeof(ListGraphType<RemoveEntityPrimitiveCommandGraphType>));
            Field(e => e.RemoveContractInsuredFactCommands, type: typeof(ListGraphType<RemoveEntityPrimitiveCommandGraphType>));

            Field(e => e.AddContractHolderIdentityCommands, type: typeof(ListGraphType<AddIdentityCommandGraphType>));
            Field(e => e.AddOtherContractHolderIdentityCommands, type: typeof(ListGraphType<AddIdentityCommandGraphType>));
            Field(e => e.AddContractInsuredIdentityCommands, type: typeof(ListGraphType<AddIdentityCommandGraphType>));
            Field(e => e.UpdateContractHolderIdentityCommands, type: typeof(ListGraphType<UpdateIdentityCommandGraphType>));
            Field(e => e.UpdateOtherContractHolderIdentityCommands, type: typeof(ListGraphType<UpdateIdentityCommandGraphType>));
            Field(e => e.UpdateContractInsuredIdentityCommands, type: typeof(ListGraphType<UpdateIdentityCommandGraphType>));
            Field(e => e.RemoveContractHolderIdentityCommands, type: typeof(ListGraphType<RemoveCommandGraphType>));
            Field(e => e.RemoveOtherContractHolderIdentityCommands, type: typeof(ListGraphType<RemoveEntityPrimitiveCommandGraphType>));
            Field(e => e.RemoveContractInsuredIdentityCommands, type: typeof(ListGraphType<RemoveEntityPrimitiveCommandGraphType>));

            Field(e => e.AddContractHolderContactCommands, type: typeof(ListGraphType<AddContactCommandGraphType>));
            Field(e => e.AddOtherContractHolderContactCommands, type: typeof(ListGraphType<AddContactCommandGraphType>));
            Field(e => e.AddContractInsuredContactCommands, type: typeof(ListGraphType<AddContactCommandGraphType>));
            Field(e => e.UpdateContractHolderContactCommands, type: typeof(ListGraphType<UpdateContactCommandGraphType>));
            Field(e => e.UpdateOtherContractHolderContactCommands, type: typeof(ListGraphType<UpdateContactCommandGraphType>));
            Field(e => e.UpdateContractInsuredContactCommands, type: typeof(ListGraphType<UpdateContactCommandGraphType>));
            Field(e => e.RemoveContractHolderContactCommands, type: typeof(ListGraphType<RemoveCommandGraphType>));
            Field(e => e.RemoveOtherContractHolderContactCommands, type: typeof(ListGraphType<RemoveEntityPrimitiveCommandGraphType>));
            Field(e => e.RemoveContractInsuredContactCommands, type: typeof(ListGraphType<RemoveEntityPrimitiveCommandGraphType>));

            Field(e => e.AddContractHolderAddressCommands, type: typeof(ListGraphType<AddAddressCommandGraphType>));
            Field(e => e.AddOtherContractHolderAddressCommands, type: typeof(ListGraphType<AddAddressCommandGraphType>));
            Field(e => e.AddContractInsuredAddressCommands, type: typeof(ListGraphType<AddAddressCommandGraphType>));
            Field(e => e.UpdateContractHolderAddressCommands, type: typeof(ListGraphType<UpdateAddressCommandGraphType>));
            Field(e => e.UpdateOtherContractHolderAddressCommands, type: typeof(ListGraphType<UpdateAddressCommandGraphType>));
            Field(e => e.UpdateContractInsuredAddressCommands, type: typeof(ListGraphType<UpdateAddressCommandGraphType>));
            Field(e => e.RemoveContractHolderAddressCommands, type: typeof(ListGraphType<RemoveCommandGraphType>));
            Field(e => e.RemoveOtherContractHolderAddressCommands, type: typeof(ListGraphType<RemoveEntityPrimitiveCommandGraphType>));
            Field(e => e.RemoveContractInsuredAddressCommands, type: typeof(ListGraphType<RemoveEntityPrimitiveCommandGraphType>));

            Field(e => e.AddAssociatedContractCommands, type: typeof(ListGraphType<AddAssociatedContractCommandGraphType>));
            Field(e => e.RemoveAssociatedContractCommands, type: typeof(ListGraphType<RemoveAssociatedContractCommandGraphType>));

            Field(e => e.AddClauseCommands, type: typeof(ListGraphType<AddClauseCommandGraphType>));

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class AddEntityCommandGraph
    {
        public string CommandId { get; set; }
        public DateTime Timestamp { get; set; }
        public string EndorsementId { get; set; }
        public string EntityId { get; set; }
        public LoginGraph AddedBy { get; set; }
        public static AddEntityCommandGraph ToGraph(AddEntityCommand command)
           => new()
           {
               CommandId = command.CommandId,
               Timestamp = command.Timestamp,
               EndorsementId = command.EndorsementId,
               EntityId = command.EntityId,
               AddedBy = new LoginGraph { Id = command.AddedById }
           };
    }
    public class AddEntityCommandGraphType : ObjectGraphType<AddEntityCommandGraph>
    {
        public AddEntityCommandGraphType(
             IDataLoaderContextAccessor accessor,
            IAuthService authService,
             PermissionValidator permissionValidator)
        {
            Name = "addEntityCommand";

            Field(r => r.CommandId, nullable: true);
            Field(r => r.Timestamp, type: typeof(DateTimeGraphType));
            Field(r => r.EndorsementId, nullable: true);
            Field(r => r.EntityId, nullable: true);
            Field(r => r.AddedBy, type: typeof(LoginGraphType))
                 .ResolveAsync(async context =>
                 {
                     if (context.Source.AddedBy?.Id == null)
                         return null;

                     IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readLogins", new List<string> { context.Source.AddedBy.Id });
                     if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.AddedBy.Id))
                         return null;

                     string tenantId = context.GetTenantIdFromToken();

                     var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                     i => authService.GetLoginsDictionaryAsync(tenantId, i));

                     Login loginDao = await loginLoader.LoadAsync(context.Source.AddedBy.Id);

                     return LoginGraph.ToGraph(loginDao);
                 });
        }
    }


    public class RemoveCommandGraph
    {
        public string CommandId { get; set; }
        public DateTime Timestamp { get; set; }
        public string EndorsementId { get; set; }
        public string Id { get; set; }
        public LoginGraph RemovedBy { get; set; }

        public static RemoveCommandGraph ToGraph(RemoveCommand command)
            => new()
            {
                CommandId = command.CommandId,
                Timestamp = command.Timestamp ?? default,
                EndorsementId = command.EndorsementId,
                Id = command.Id,
                RemovedBy = new LoginGraph { Id = command.RemovedById }
            };
    }

    public class RemoveCommandGraphType : ObjectGraphType<RemoveCommandGraph>
    {
        public RemoveCommandGraphType(
             IDataLoaderContextAccessor accessor,
            IAuthService authService,
             PermissionValidator permissionValidator)
        {
            Name = "removeCommand";

            Field(r => r.CommandId, nullable: true);
            Field(r => r.Timestamp, type: typeof(DateTimeGraphType));
            Field(r => r.EndorsementId, nullable: true);
            Field(r => r.Id, nullable: true);
            Field(r => r.RemovedBy, type: typeof(LoginGraphType))
                 .ResolveAsync(async context =>
                 {
                     if (context.Source.RemovedBy?.Id == null)
                         return null;

                     IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readLogins", new List<string> { context.Source.RemovedBy.Id });
                     if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.RemovedBy.Id))
                         return null;

                     string tenantId = context.GetTenantIdFromToken();

                     var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                     i => authService.GetLoginsDictionaryAsync(tenantId, i));

                     Login loginDao = await loginLoader.LoadAsync(context.Source.RemovedBy.Id);

                     return LoginGraph.ToGraph(loginDao);
                 });
        }
    }

    public class RemoveEntityPrimitiveCommandGraph
    {
        public string CommandId { get; set; }
        public DateTime Timestamp { get; set; }
        public string EndorsementId { get; set; }
        public string EntityId { get; set; }
        public string PrimitiveId { get; set; }
        public LoginGraph RemovedBy { get; set; }

        public static RemoveEntityPrimitiveCommandGraph ToGraph(RemoveEntityPrimitiveCommand command)
          => new()
          {
              CommandId = command.CommandId,
              Timestamp = command.Timestamp,
              EndorsementId = command.EndorsementId,
              EntityId = command.EntityId,
              PrimitiveId = command.PrimitiveId,
              RemovedBy = new LoginGraph { Id = command.RemovedById }
          };
    }

    public class RemoveEntityPrimitiveCommandGraphType : ObjectGraphType<RemoveEntityPrimitiveCommandGraph>
    {
        public RemoveEntityPrimitiveCommandGraphType(
             IDataLoaderContextAccessor accessor,
            IAuthService authService,
             PermissionValidator permissionValidator)
        {
            Name = "removeEntityPrimitiveCommand";

            Field(r => r.CommandId, nullable: true);
            Field(r => r.Timestamp, type: typeof(DateTimeGraphType));
            Field(r => r.EndorsementId, nullable: true);
            Field(r => r.EntityId, nullable: true);
            Field(r => r.PrimitiveId, nullable: true);
            Field(r => r.RemovedBy, type: typeof(LoginGraphType))
                 .ResolveAsync(async context =>
                 {
                     if (context.Source.RemovedBy?.Id == null)
                         return null;

                     IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readLogins", new List<string> { context.Source.RemovedBy.Id });
                     if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.RemovedBy.Id))
                         return null;

                     string tenantId = context.GetTenantIdFromToken();

                     var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                     i => authService.GetLoginsDictionaryAsync(tenantId, i));

                     Login loginDao = await loginLoader.LoadAsync(context.Source.RemovedBy.Id);

                     return LoginGraph.ToGraph(loginDao);
                 });
        }
    }
}
