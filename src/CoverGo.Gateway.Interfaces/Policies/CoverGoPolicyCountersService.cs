using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Clients;

namespace CoverGo.Gateway.Interfaces.Policies
{
    public class CoverGoPolicyCountersService : CoverGoGenericGenericServiceRestClientBase<PolicyCounter, PolicyCounterCommand, PolicyCounterFilter>
    {
        private readonly HttpClient _client;

        public CoverGoPolicyCountersService(HttpClient client) : base(client)
        {
            _client = client;
        }

        protected override string ApiBaseUrlBuildInternal(string tenantId) => $"{tenantId}/api/v1/policies/counters";
        
        public Task<long> GetNextAsync(string tenantId, PolicyCounterCommand upsert, CancellationToken cancellationToken) => _client.GenericGetAsync<long, PolicyCounterCommand>($"{ApiBaseUrlBuildInternal(tenantId)}/next", upsert, cancellationToken);
    }
}