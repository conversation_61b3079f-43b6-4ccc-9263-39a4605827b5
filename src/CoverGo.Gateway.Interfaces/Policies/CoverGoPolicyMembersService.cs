using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Clients;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Policies;

namespace CoverGo.Gateway.Interfaces.Policies
{
    public class CoverGoPolicyMembersService : CoverGoGenericGenericServiceRestClientBase<PolicyMember,
        PolicyMemberInput, PolicyMemberInput, PolicyMemberInput, PolicyMembersBatchCommand, PolicyMembersWhere,
        PolicyMembersFilter>, IPolicyMemberService
    {
        private readonly HttpClient _client;

        public CoverGoPolicyMembersService(HttpClient client) : base(client)
        {
            _client = client;
        }

        protected override string ApiBaseUrlBuildInternal(string tenantId) => $"{tenantId}/api/v1/policies/members";

        public Task<long> MovementsLogCountAsync(string tenantId, PolicyMembersWhere where,
            CancellationToken cancellation = default)
            => _client.GenericGetAsync<long, PolicyMembersWhere>($"{ApiBaseUrlBuildInternal(tenantId)}/log/count",
                where, cancellation);

        public Task<IReadOnlyCollection<PolicyMember>> MovementsLogQueryAsync(string tenantId, PolicyMembersWhere where,
            CancellationToken cancellation = default)
            => _client.GenericGetAsync<IReadOnlyCollection<PolicyMember>, PolicyMembersWhere>(
                $"{ApiBaseUrlBuildInternal(tenantId)}/log", where, cancellation);

        public Task<long> ActivityCountAsync(string tenantId, PolicyMembersWhere where,
            CancellationToken cancellation = default)
            => _client.GenericGetAsync<long, PolicyMembersWhere>($"{ApiBaseUrlBuildInternal(tenantId)}/activity/count",
                where, cancellation);

        public Task<IReadOnlyCollection<PolicyMember>> ActivityQueryAsync(string tenantId, PolicyMembersWhere where,
            CancellationToken cancellation = default)
            => _client.GenericGetAsync<IReadOnlyCollection<PolicyMember>, PolicyMembersWhere>(
                $"{ApiBaseUrlBuildInternal(tenantId)}/activity", where, cancellation);

        public Task<long> ReportCountAsync(string tenantId, PolicyMembersWhere where,
            CancellationToken cancellation = default)
            => _client.GenericGetAsync<long, PolicyMembersWhere>($"{ApiBaseUrlBuildInternal(tenantId)}/movements/report/count",
                where, cancellation);

        public Task<IReadOnlyCollection<PolicyMemberMovementReportItem>> ReportQueryAsync(string tenantId, PolicyMembersWhere where,
            CancellationToken cancellation = default)
            => _client.GenericGetAsync<IReadOnlyCollection<PolicyMemberMovementReportItem>, PolicyMembersWhere>(
                $"{ApiBaseUrlBuildInternal(tenantId)}/movements/report", where, cancellation);

        public Task<Result> MovementLogItemMarkAsPrintedAsync(string tenantId, string itemId, CancellationToken cancellation = default) =>
            _client.GenericPutAsync<Result, object>($"{ApiBaseUrlBuildInternal(tenantId)}/movements/item/{itemId}/set-printed", new object(), cancellation);

        public Task<List<PoliciesPerIndividual>> PoliciesPerIndividualsQueryAsync(string tenantId, PoliciesPerIndividualFilter filter, CancellationToken cancellationToken) =>
            _client.GenericGetAsync<List<PoliciesPerIndividual>, PoliciesPerIndividualFilter>($"{ApiBaseUrlBuildInternal(tenantId)}/policiesPerIndividual", filter, cancellationToken);

        public Task<Result> AssignInternalCodesAsync(string tenantId, PolicyMembersAssignInternalCodesCommand command, CancellationToken cancellation = default) =>
            _client.GenericPostAsync<Result, PolicyMembersAssignInternalCodesCommand>($"{ApiBaseUrlBuildInternal(tenantId)}/assign-internal-codes", command, cancellation);
    }
}