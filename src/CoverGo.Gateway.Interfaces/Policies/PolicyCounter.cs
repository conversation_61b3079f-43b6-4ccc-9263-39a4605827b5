using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Interfaces.Policies
{
    public class PolicyCounter : SystemObject, IUniqueSystemObject
    {
        public const string DebitNoteNumber = "DebitNoteNumber";
        public const string MemberMovementNumber = "MemberMovementNumber";
        public string Id { get; set; }
        public string PolicyId { get; set; }
        public Dictionary<string, long> Counters { get; set; }
    }

    public class PolicyCounterFilter
    {
        public string PolicyId { get; set; }
    }

    public class PolicyCounterCommand : SystemObject, IUniqueSystemObject
    {
        public string Id { get; set; }
        public string PolicyId { get; set; }
        public string CounterKey { get; set; }
    }
}