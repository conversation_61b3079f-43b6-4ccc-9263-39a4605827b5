using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Context;
using CoverGo.Gateway.Domain.Policies;
using GraphQL.Types;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Interfaces.Policies
{
    public class PolicyUnderwriting
    {
        public string Id { get; set; }
        public string PolicyId { get; set; }
        public string PolicyMemberId { get; set; }
        public PolicyMember PolicyMember { get; set; }
        public List<PolicyUnderwritingRemark> Remarks { get; set; }
        public string Status { get; set; }
        public JToken Fields { get; set; }
    }

    public class PolicyUnderwritingGraphType : ObjectGraphType<PolicyUnderwriting>
    {
        public PolicyUnderwritingGraphType()
        {
            Name = "policyUnderwriting";

            Field(x => x.Id, nullable: true);
            Field(x => x.PolicyId, nullable: true);
            Field(x => x.PolicyMember, nullable: true, type: typeof(PolicyMemberGraphType));
            Field(x => x.Remarks, nullable: true, type: typeof(ListGraphType<PolicyUnderwritingRemarkGraphType>));
            Field(x => x.Status, nullable: true);
            Field(x => x.Fields, nullable: true, type: typeof(StringGraphType)).Resolve(x => x.Source.Fields?.ToString());
        }
    }

    public class PolicyUnderwritingFilter
    {
        public List<string> Id_in { get; set; }
        public List<string> MemberId_in { get; set; }
        public List<string> Status_in { get; set; }
        public FieldsWhere Fields { get; set; }
    }

    public class PolicyUnderwritingFilterGraphType : InputObjectGraphType<PolicyUnderwritingFilter>
    {
        public PolicyUnderwritingFilterGraphType()
        {
            Name = "policyUnderwritingFilter";

            Field(x => x.Id_in, nullable: true);
            Field(x => x.MemberId_in, nullable: true);
            Field(x => x.Status_in, nullable: true);
            Field(x => x.Fields, nullable: true, type: typeof(FieldsWhereInputGraphType));
        }
    }

    public class PolicyUnderwritingRemark
    {
        public string Id { get; set; }
        public string Remark { get; set; }
        public string RemarkType { get; set; }
        public JToken Fields { get; set; }

    }

    public class PolicyUnderwritingRemarkGraphType : ObjectGraphType<PolicyUnderwritingRemark>
    {
        public PolicyUnderwritingRemarkGraphType()
        {
            Name = "policyUnderwritingRemark";

            Field(x => x.Id, nullable: true);
            Field(x => x.Remark, nullable: true);
            Field(x => x.RemarkType, nullable: true);
            Field(x => x.Fields, nullable: true, type: typeof(StringGraphType)).Resolve(x => x.Source?.ToString());
        }
    }

    public class PolicyUnderwritingWhere : QueryArguments<Filter<PolicyUnderwritingFilter>>
    {
        public List<string> EndorsementId_In { get; set; }
        public List<string> PolicyId_In { get; set; }
    }

    public class PolicyUnderwritingFilterFilterGraphType : InputObjectGraphType<Filter<PolicyUnderwritingFilter>>
    {
        public PolicyUnderwritingFilterFilterGraphType()
        {
            Name = "policyUnderwritingFilterInput";

            Field(x => x.Where, nullable: true, type: typeof(PolicyUnderwritingFilterGraphType));
            Field(x => x.And, nullable: true, type: typeof(ListGraphType<PolicyUnderwritingFilterGraphType>));
            Field(x => x.Or, nullable: true, type: typeof(ListGraphType<PolicyUnderwritingFilterGraphType>));
        }
    }

    public class PolicyUnderwritings
    {
        public PolicyUnderwritings(string policyId, string endorsementId)
        {
            PolicyId = policyId;
            EndorsementId = endorsementId;
        }

        public string PolicyId { get; }
        public string EndorsementId { get; }
        public long TotalCount { get; set; }
        public List<PolicyUnderwriting> List { get; set; }
    }

    public class PolicyUnderwritingsGraphType : ObjectGraphType<PolicyUnderwritings>
    {
        private readonly ICoverGoContextAccessor _ccoverGoContextAccessor;
        private readonly CoverGoPolicyUnderwritingsService _service;

        private async Task<(string tenantId, PolicyUnderwritingWhere query)> QueryBuildInternalAsync(
            ResolveFieldContext<PolicyUnderwritings> context)
        {
            CoverGoContext coverGoContext = await _ccoverGoContextAccessor.CoverGoContextGetAsync();

            string tenantId = coverGoContext.TenantId;
            string policyId = context.Source.PolicyId;
            string previewEndorsementId = context.Source.EndorsementId;

            int? skip = context.ComputeArgAndVar<int?, PolicyUnderwritings>("skip");
            int? first = context.ComputeArgAndVar<int?, PolicyUnderwritings>("limit");
            DateTime? asOf = context.ComputeArgAndVar<DateTime?, PolicyUnderwritings>("asOf");
            SortGraph sort = context.ComputeArgAndVar<SortGraph, PolicyUnderwritings>("sort");

            OrderBy orderBy = null;
            if (sort != null)
            {
                orderBy = sort.ToOrderBy();
            }

            var serializer = new JsonSerializer();

            var query = new PolicyUnderwritingWhere
            {
                PolicyId_In = new List<string> { policyId },
                AsOf = asOf,
                Skip = skip ?? 0,
                First = first ?? 25,
                OrderBy = orderBy,
                Where = context.ComputeArgAndVar<Filter<PolicyUnderwritingFilter>, PolicyUnderwritings>("filter", serializer)
            };

            if (!string.IsNullOrEmpty(previewEndorsementId))
            {
                query.EndorsementId_In = new List<string> { previewEndorsementId };
            }

            return (tenantId, query);
        }

        public PolicyUnderwritingsGraphType(CoverGoPolicyUnderwritingsService service, ICoverGoContextAccessor coverGoContextAccessor)
        {
            _ccoverGoContextAccessor = coverGoContextAccessor;
            _service = service;

            Name = "policiesUnderwritings";

            Field(x => x.TotalCount, nullable: true).ResolveAsync(async context =>
            {
                var (tenantId, query) = await QueryBuildInternalAsync(context);
                return await _service.Count(tenantId, query, context.CancellationToken);
            });

            Field(x => x.List, nullable: true, typeof(ListGraphType<PolicyUnderwritingGraphType>)).ResolveAsync(async context =>
            {
                var (tenantId, query) = await QueryBuildInternalAsync(context);
                return await _service.Query(tenantId, query, context.CancellationToken);
            });
        }
    }
}