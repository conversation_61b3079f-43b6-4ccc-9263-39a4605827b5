﻿
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Users;

using GraphQL.DataLoader;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Policies
{
    public class BeneficiaryEligibilityGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public EntityGraph Entity { get; set; }
        public BenefitGraph Benefit { get; set; }
        public decimal Ratio { get; set; }
        public string Notes { get; set; }
        public bool IsRevocable { get; set; }

        public static BeneficiaryEligibilityGraph ToGraph(BeneficiaryEligibility domain) =>
            domain == null
            ? null
            : new BeneficiaryEligibilityGraph
            {
                Id = domain.Id,
                Benefit = domain.BenefitTypeId != null ? new BenefitGraph { TypeId = domain.BenefitTypeId } : null,
                Entity = domain.ContractEntity?.ToGraph(),
                IsRevocable = domain.IsRevocable,
                Notes = domain.Notes,
                Ratio = domain.Ratio,
                CreatedBy = domain.CreatedById != null ? new Auth.LoginGraph { Id = domain.CreatedById } : null,
                LastModifiedBy = domain.LastModifiedById != null ? new Auth.LoginGraph { Id = domain.LastModifiedById } : null,
                CreatedAt = domain.CreatedAt,
                LastModifiedAt = domain.LastModifiedAt
            };
    }

    public class BeneficiaryEligibilityGraphType : ObjectGraphType<BeneficiaryEligibilityGraph>
    {
        public BeneficiaryEligibilityGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "beneficiaryEligibility";
            Description = "Specifies the proceeds eligibility of a beneficiary";

            Field(b => b.Id);
            Field(b => b.Entity, type: typeof(EntityInterfaceGraphType)).Description("The entity");
            Field(b => b.Benefit, type: typeof(BenefitGraphType)).Description("The benefit");
            Field(b => b.Ratio).Description("The ratio of the policy benefit for this user");
            Field(b => b.Notes, nullable: true);
            Field(b => b.IsRevocable, nullable: true);

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class BeneficiaryEligibilityInputGraph
    {
        public string EntityId { get; set; }
        public string BenefitTypeId { get; set; }
        public decimal Ratio { get; set; }
        public string Notes { get; set; }
        public bool IsRevocable { get; set; }
    }

    public class BeneficiaryEligibilityInputGraphType : InputObjectGraphType<BeneficiaryEligibilityInputGraph>
    {
        public BeneficiaryEligibilityInputGraphType()
        {
            Name = "beneficiaryEligibilityInput";
            Description = "Specifies the proceeds eligibility of a beneficiary";

            Field(b => b.EntityId).Description("The user id");
            Field(b => b.BenefitTypeId, nullable: true).Description("The benefit type id on which the benefit will be earned for the beneficiary.");
            Field(b => b.Ratio).Description("The ratio of the policy benefit for this user");
            Field(b => b.Notes, nullable: true);
            Field(b => b.IsRevocable, nullable: true);
        }
    }

    public class UpdateBenefificiaryEligibilityGraphType : ObjectGraphType<UpdateBeneficiaryEligibilityCommand>
    {
        public UpdateBenefificiaryEligibilityGraphType()
        {
            Name = "updateBeneficiaryEligibility";
            Description = "Chosen beneficiary eligibility";

            Field(e => e.EntityId, nullable: true);
            Field(e => e.IsEntityIdChanged);
            Field(e => e.BenefitTypeId, nullable: true);
            Field(e => e.IsBenefitTypeIdChanged);
            Field(e => e.Ratio, nullable: true);
            Field(e => e.IsRatioChanged);
            Field(e => e.Notes, nullable: true);
            Field(e => e.IsNotesChanged);
            Field(e => e.IsRevocable, nullable: true);
            Field(e => e.IsIsRevocableChanged);
        }
    }
}
