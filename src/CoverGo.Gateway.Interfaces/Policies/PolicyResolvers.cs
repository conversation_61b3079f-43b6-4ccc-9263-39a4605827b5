﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;
using GraphQL.DataLoader;
using GraphQL.Types;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Object = CoverGo.Users.Domain.Objects.Object;

namespace CoverGo.Gateway.Interfaces.Policies
{
    public static class PolicyResolvers
    {
        public static async Task<Result> ResolveUpdatePolicyAsync(
           this ResolveFieldContext<object> context,
           IPolicyService policyService,
           IAuthService authService,
           IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
           IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
           IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
           IEntityService<Object, CreateObjectCommand, UpdateObjectCommand> objectService,
           IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
           IEntityService entityService)
        {
            string tenantId = context.GetTenantIdFromToken();
            string loginId = context.GetLoginIdFromToken();
            string policyId = context.GetArgument<string>("policyId");

            UpdatePolicyInputGraph graph = context.GetArgument<UpdatePolicyInputGraph>("updatedPolicy");
            IDictionary<string, object> dict = Tools.ToDictionary<object>(graph);
            if (graph.Values != null)
            {
                dict["Values"] = JToken.FromObject(graph.Values?.ToDictionary(x => x.Key, x => x.Value?.GetValue()));
            }
            UpdatePolicyCommand command = dict.ToUpdateCommand<UpdatePolicyCommand>();

            await context.PatchOperationPermittedCheckAsync(authService, tenantId, command.FieldsPatch,
                policyId, "policy", async () =>
                {
                    Policy policy = (await policyService.GetAsync(tenantId,
                        new Domain.QueryArguments {Where = new PolicyWhere {Id = policyId}})).FirstOrDefault();

                    return policy?.Fields;
                });

            command.ModifiedById = loginId;

            if (command.HolderId != null)
                if (await Tools.GetEntityAsync(individualService, internalService, companyService, objectService, organizationService, tenantId, command.HolderId) == null)
                    return new Result { Status = "failure", Errors = new List<string> { $"The holder '{command?.HolderId}' doesn't exist." } };

            IEnumerable<string> neededEntityIds = Enumerable.Empty<string>();
            if (command?.InsuredIds != null)
                neededEntityIds = command?.InsuredIds?.Append(command?.HolderId).Distinct();

            if (neededEntityIds != Enumerable.Empty<string>())
            {
                IEnumerable<EntityId> entityIdsAndTypes = await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id_in = neededEntityIds.ToList() });
                IEnumerable<string> entityIds = entityIdsAndTypes.Select(e => e.Id);

                if (command?.InsuredIds != null)
                    foreach (string insuredId in command?.InsuredIds)
                    {
                        if (entityIds.Any(c => c == insuredId) == false)
                            return new Result { Status = "failure", Errors = new List<string> { $"The insured '{insuredId}' doesn't exist." } };
                    }
            }

            Result result = await policyService.UpdatePolicyAsync(tenantId, policyId, command);

            return result;
        }

        public static async Task<object> ResolveUpdateContractInsuredIndividualAsync(
           this ResolveFieldContext<object> context,
           IPolicyService policyService,
           IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService)
        {
            string tenantId = context.GetTenantIdFromToken();
            string loginId = context.GetLoginIdFromToken();
            string entityId = context.GetArgument<string>("entityId");
            string policyId = context.GetArgument<string>("policyId");

            UpdateIndividualCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateIndividualCommand>();
            command.EntityId = entityId;
            command.ModifiedById = loginId;

            Result result = await policyService.UpdateContractInsuredIndividualAsync(tenantId, policyId, command);

            return result;
        }

        public static async Task<object> ResolveUpdateContractInsuredObjectAsync(
           this ResolveFieldContext<object> context,
           IPolicyService policyService,
           IEntityService<Object, CreateObjectCommand, UpdateObjectCommand> objectService)
        {
            string tenantId = context.GetTenantIdFromToken();
            string loginId = context.GetLoginIdFromToken();
            string entityId = context.GetArgument<string>("entityId");
            string policyId = context.GetArgument<string>("policyId");

            UpdateObjectCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateObjectCommand>();
            command.EntityId = entityId;
            command.ModifiedById = loginId;

            Result result = await policyService.UpdateContractInsuredObjectAsync(tenantId, policyId, command);

            return result;
        }

        //public static async Task<object> ResolveUpdateContractInsuredFactAsync(
        //   this ResolveFieldContext<object> context,
        //   IDataLoaderContextAccessor accessor,
        //   IAuthService authService,
        //   IPolicyService policyService,
        //   IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
        //   IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
        //   IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
        //   IEntityService<Object, CreateObjectCommand, UpdateObjectCommand> objectService,
        //   IProductService productService)
        //{
        //    string tenantId = context.GetTenantIdFromToken();
        //    string loginId = context.GetLoginIdFromToken();
        //    string entityId = context.GetArgument<string>("entityId");
        //    string policyId = context.GetArgument<string>("policyId");

        //    UpdateFactCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateFactCommand>();
        //    command.EntityId = entityId;
        //    command.ModifiedById = loginId;

        //    if (await Tools.GetEntityAsync(individualService, internalService, companyService, objectService, tenantId, entityId) == null)
        //        return new Result<PolicyStatus> { Status = "failure", Errors = new List<string> { $"The insured '{entityId}' doesn't exist." } };

        //    Result result = await policyService.UpdateContractInsuredFactAsync(tenantId, policyId, command);

        //    return result;
        //}

        public static async Task<Result> ResolveAddBeneficiaryEligibilityAsync(
            this ResolveFieldContext<object> context,
            IPolicyService policyService,
            PermissionValidator permissionValidator)
        {
            string tenantId = context.GetTenantIdFromToken();
            string policyId = context.GetArgument<string>("policyId");
            IEnumerable<string> allowedTargetIds = await permissionValidator.GetPermittedTargetIds(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));
            if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, policyId))
                return null;

            AddBeneficiaryEligibilityCommand command = context.GetArgument<AddBeneficiaryEligibilityCommand>("beneficiaryEligibilityInput");
            command.AddedById = context.GetLoginIdFromToken();

            Result result = await policyService.AddBeneficiaryEligibilityAsync(tenantId, policyId, command);

            return result;
        }

        public static async Task<Result> ResolveUpdateBeneficiaryEligibilityAsync(
            this ResolveFieldContext<object> context,
            IPolicyService policyService,
            PermissionValidator permissionValidator)
        {
            string tenantId = context.GetTenantIdFromToken();
            string policyId = context.GetArgument<string>("policyId");
            string beneficiaryEligibilityId = context.GetArgument<string>("beneficiaryEligibilityId");
            IEnumerable<string> allowedTargetIds = await permissionValidator.GetPermittedTargetIds(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));
            if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, policyId))
                return null;

            UpdateBeneficiaryEligibilityCommand command = context.GetArgument<Dictionary<string, object>>("beneficiaryEligibilityInput").ToUpdateCommand<UpdateBeneficiaryEligibilityCommand>();
            command.Id = beneficiaryEligibilityId;
            command.ModifiedById = context.GetLoginIdFromToken();

            Result result = await policyService.UpdateBeneficiaryEligibilityAsync(tenantId, policyId, command);

            return result;
        }

        public static async Task<Result> ResolveRemoveBeneficiaryEligibilityAsync(
            this ResolveFieldContext<object> context,
            IPolicyService policyService,
            PermissionValidator permissionValidator)
        {
            string tenantId = context.GetTenantIdFromToken();
            string policyId = context.GetArgument<string>("policyId");
            string beneficiaryEligibilityId = context.GetArgument<string>("beneficiaryEligibilityId");
            string removedById = context.GetLoginIdFromToken();

            IEnumerable<string> allowedTargetIds = await permissionValidator.GetPermittedTargetIds(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));
            if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, policyId))
                return null;

            Result result = await policyService.RemoveBeneficiaryEligibilityAsync(tenantId, policyId, new RemoveCommand
            {
                Id = beneficiaryEligibilityId,
                RemovedById = removedById
            });

            return result;
        }

        public static async Task<Result> ResolveRemoveBenefitOptionAsync(
          this ResolveFieldContext<object> context,
          IPolicyService policyService,
          PermissionValidator permissionValidator)
        {
            string tenantId = context.GetTenantIdFromToken();
            string policyId = context.GetArgument<string>("policyId");
            string typeId = context.GetArgument<string>("typeId");
            string offerId = context.GetArgument<string>("offerId");
            string removedById = context.GetLoginIdFromToken();
            IEnumerable<string> allowedTargetIds = await permissionValidator.GetPermittedTargetIds(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));//ToDo: change permission to writeOffers after aag transitions to offers
            if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, policyId))
                return null;

            Result result = await policyService.RemoveBenefitOptionAsync(tenantId, policyId, new RemoveBenefitOptionCommand
            {
                OfferId = offerId,
                TypeId = typeId,
                RemovedById = removedById
            });

            return result;
        }

        public static async Task<Result> ResolveUpdatePaymentInfoAsync(
           this ResolveFieldContext<object> context,
           IPolicyService policyService,
           PermissionValidator permissionValidator)
        {
            string tenantId = context.GetTenantIdFromToken();
            string policyId = context.GetArgument<string>("id");
            string paymentId = context.GetArgument<string>("paymentId");
            IEnumerable<string> allowedTargetIds = await permissionValidator.GetPermittedTargetIds(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));
            if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, policyId))
                return null;

            UpdatePaymentInfoCommand command = context.GetArgument<Dictionary<string, object>>("updateIssuedPaymentInput").ToUpdateCommand<UpdatePaymentInfoCommand>();
            command.Id = paymentId;
            command.ModifiedById = context.GetLoginIdFromToken();

            Result result = await policyService.UpdatePaymentInfoAsync(tenantId, policyId, command);

            return result;
        }

        public static async Task<Result> ResolveRemovePaymentInfoAsync(
          this ResolveFieldContext<object> context,
          IPolicyService policyService,
          PermissionValidator permissionValidator)
        {
            string tenantId = context.GetTenantIdFromToken();
            string policyId = context.GetArgument<string>("id");
            string paymentId = context.GetArgument<string>("paymentId");
            string removedById = context.GetLoginIdFromToken();
            IEnumerable<string> allowedTargetIds = await permissionValidator.GetPermittedTargetIds(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));
            if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, policyId))
                return null;

            Result result = await policyService.RemovePaymentInfoAsync(tenantId, policyId, new RemoveCommand
            {
                Id = paymentId,
                RemovedById = removedById
            });

            return result;
        }

        public static async Task<Result> ResolveDeletePolicyAsync(
            this ResolveFieldContext<object> context,
            IPolicyService policyService,
            PermissionValidator permissionValidator)
        {
            string tenantId = context.GetTenantIdFromToken();
            string id = context.GetArgument<string>("id");
            string deletedBy = context.GetLoginIdFromToken();
            IEnumerable<string> allowedTargetIds = await permissionValidator.GetPermittedTargetIds(context, new PermissionRequest("deletePolicies", "writePolicies").WithTargetIds(id));
            if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, id))
                return null;

            Result policyResult = await policyService.DeletePolicyAsync(tenantId, id, deletedBy);

            return policyResult;
        }

        public static async Task<IEnumerable<PolicyUpdateRequestGraph>> ResolveGetPolicyUpdateRequestsAsync<T>(
            this ResolveFieldContext<T> context,
            IDataLoaderContextAccessor accessor,
            IPolicyService policyService,
            PermissionValidator permissionValidator,
            IEnumerable<string> statusFilter)
        {
            string tenantId = context.GetTenantIdFromToken();
            IEnumerable<string> targetIds = await permissionValidator.GetTargetIdsFromClaim(context,"readPolicies");

            var requestLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, PolicyUpdateRequest>("GetPolicyRequestsCollection",
                  i => policyService.GetUpdateRequestLookupAsync(tenantId, new PolicyUpdateRequestFilter { Ids = i.Contains("all") ? null : i?.ToList(), Statuses = statusFilter?.ToList() }));

            IEnumerable<PolicyUpdateRequest> requests = (await Task.WhenAll(targetIds.Select(t => requestLoader.LoadAsync(t)))).SelectMany(r => r);

            return requests.Select(r => new PolicyUpdateRequestGraph
            {
                Id = r.Id,
                ApprovalStatus = r.ApprovalStatus,
                CreatedAt = r.CreatedAt,
                CreatedBy = new Auth.LoginGraph { Id = r.CreatedById },
                LastModifiedBy = new Auth.LoginGraph { Id = r.LastModifiedById },
                PolicyId = r.PolicyId,
                LastModifiedAt = r.LastModifiedAt,
                UpdateBeneficiaryEligibilityCommands = r.UpdateBeneficiaryEligibilityCommands,
                UpdatePaymentInfoCommands = r.UpdatePaymentInfoCommands,
                UpdatePolicyCommands = r.UpdatePolicyCommands?.Select(p => UpdatePolicyCommandGraph.ToGraph(p)),
                UpsertBenefitOptionCommands = r.UpsertBenefitOptionCommands?.Select(b => new BenefitOptionInputGraph
                {
                    OfferId = b.OfferId,
                    Key = b.Key,
                    TypeId = b.TypeId,
                    Value = b.Value?.ToScalarValue(),
                    InsuredId = b.InsuredId
                })
            });
        }
    }
}
