﻿using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;

using GraphQL.DataLoader;
using GraphQL.Types;
using JsonLogic.Net;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Interfaces.Policies
{
    public class CommissionGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public EntityGraph Entity { get; set; }
        public string JsonRule { get; set; }
        public string Remark { get; set; }
        public decimal Amount { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public decimal Ratio { get; set; }
        public CommissionRatioGraph BaseCommissionRatio { get; set; }
        public string Type { get; set; }
    }

    public class CommissionRatioGraph
    {
        public decimal Ratio { get; set; }
        public CommissionGraph Commission { get; set; }
    }

    public class CommissionRatioGraphType : ObjectGraphType<CommissionRatioGraph>
    {
        public CommissionRatioGraphType()
        {
            Name = "commissionRatio";

            Field(c => c.Commission, type: typeof(CommissionGraphType));
            Field(c => c.Ratio);
        }
    }

    public class CommissionGraphType : ObjectGraphType<CommissionGraph>
    {
        public CommissionGraphType(
            IDataLoaderContextAccessor accessor,
            IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "commission";

            Field(c => c.Id);
            Field(c => c.Entity, type: typeof(EntityInterfaceGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.Entity?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var individualLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                        i => individualService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));
                    var internalLoader = accessor.Context.GetOrAddBatchLoader<string, Internal>("GetInternals",
                        i => internalService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));
                    var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>("GetCompanies",
                        i => companyService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));
                    var organizationLoader = accessor.Context.GetOrAddBatchLoader<string, Organization>("GetOrganizations",
                        i => organizationService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                    var individualTask = Task.FromResult<Individual>(null);
                    var internalTask = Task.FromResult<Internal>(null);
                    var companyTask = Task.FromResult<Company>(null);
                    var organizationTask = Task.FromResult<Organization>(null);

                    if (await permissionValidator.HasTarget(context, "readIndividuals", context.Source.Entity.Id))
                    {
                        individualTask = individualLoader.LoadAsync(context.Source.Entity.Id);
                    }

                    if (await permissionValidator.HasTarget(context, "readInternals", context.Source.Entity.Id))
                    {
                        internalTask = internalLoader.LoadAsync(context.Source.Entity.Id);
                    }

                    if (await permissionValidator.HasTarget(context, "readCompanies", context.Source.Entity.Id))
                    {
                        companyTask = companyLoader.LoadAsync(context.Source.Entity.Id);
                    }

                    if (await permissionValidator.HasTarget(context, "readOrganizations", context.Source.Entity.Id))
                    {
                        organizationTask = organizationLoader.LoadAsync(context.Source.Entity.Id);
                    }

                    await Task.WhenAll(individualTask, internalTask, companyTask, organizationTask);

                    return
                        individualTask.Result?.ToGraph() ??
                        internalTask.Result?.ToGraph() ??
                        companyTask.Result?.ToGraph() ??
                        organizationTask.Result?.ToGraph();
                });

            Field(c => c.JsonRule, nullable: true);
            Field(c => c.Remark, nullable: true);

            Field(c => c.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType), nullable: true);
            Field(c => c.Amount, nullable: true);
            Field(c => c.BaseCommissionRatio, type: typeof(CommissionRatioGraphType), nullable: true);
            Field(c => c.Ratio, nullable: true);
            Field(c => c.Type, nullable: true);

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class CommissionInputGraph
    {
        public string EntityId { get; set; }
        public string JsonRule { get; set; }
        public decimal? Amount { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public string Remark { get; set; }
    }

    public class CommissionInputGraphType : InputObjectGraphType<CommissionInputGraph>
    {
        public CommissionInputGraphType()
        {
            Name = "commissionInput";

            Field(c => c.EntityId, nullable: true);
            Field(c => c.JsonRule, nullable: true);
            Field(c => c.Amount, nullable: true);
            Field(c => c.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(c => c.Remark, nullable: true);
        }
    }

    public static class CommissionGraphExtensions
    {
        private static readonly JsonLogicEvaluator _evaluator = new(EvaluateOperators.Default);

        public static CommissionGraph ToGraph(this Commission domain, PricingGraph price, IEnumerable<Commission> otherCommissions)
        {
            if (domain == null)
                return null;

            var graph = new CommissionGraph
            {
                Id = domain.Id,
                JsonRule = domain.JsonRule,
                Entity = new EntityGraph { Id = domain.EntityId },
                Remark = domain.Remark,
                Amount = domain.Amount.GetValueOrDefault(),
                CurrencyCode = domain.CurrencyCode.GetValueOrDefault(),
                Type = GetCommissionType(domain.JsonRule)
            }.PopulateSystemGraphFields(domain);

            try
            {
                decimal? priceBeforeIaLevy = price.AppliedTaxes?.FirstOrDefault(t => t.Code == "hkIALevy")?.OriginalPrice ?? price.Amount;

                if (domain.Amount != null)
                    return graph;

                var rule = JToken.Parse(domain.JsonRule);
                if (graph.Type == "flat")
                {
                    object[] evalPrice = _evaluator.Apply(rule, null) as object[];
                    graph.Amount = Convert.ToDecimal(evalPrice[0]);
                    graph.CurrencyCode = Enum.Parse<CurrencyCode>(evalPrice[1].ToString());
                }
                else if (graph.Type == "ratio")
                {
                    if (priceBeforeIaLevy.HasValue && price.CurrencyCode.HasValue)
                    {
                        graph.Amount = Convert.ToDecimal(_evaluator.Apply(rule, new { grossPremium = priceBeforeIaLevy, totalPremium = priceBeforeIaLevy /* HACK to keep until launch of Asia */ }));
                        graph.CurrencyCode = price.CurrencyCode.Value;
                    }
                }
                else if (graph.Type == "ratioBasedOnOtherRule")
                {
                    if (price.CurrencyCode.HasValue)
                    {
                        string baseId = rule["*"][0]["var"].ToString();
                        graph.BaseCommissionRatio = new CommissionRatioGraph
                        {
                            Ratio = Convert.ToDecimal(rule["*"][1]),
                            Commission = otherCommissions.First(c => c.Id == baseId).ToGraph(price, otherCommissions)
                        };

                        rule["*"][0].Replace(graph.BaseCommissionRatio.Commission.Amount);

                        graph.Amount = Convert.ToDecimal(_evaluator.Apply(rule, null));
                        graph.CurrencyCode = price.CurrencyCode.Value;
                    }
                }

                graph.Ratio = priceBeforeIaLevy.HasValue && priceBeforeIaLevy != 0 ? (graph.Amount / priceBeforeIaLevy.Value) : 0;
            }
            catch { }

            return graph;
        }

        public static IEnumerable<CommissionGraph> Summarize(this IEnumerable<CommissionGraph> commissions, PricingGraph price) =>
            commissions
                .GroupBy(c => c.Entity?.Id)
                .Select(g =>
                {
                    decimal amount = g.Sum(c => c.Amount - commissions.Where(c2 => c2.BaseCommissionRatio?.Commission.Id == c.Id).Sum(c2 => c2.Amount));
                    decimal? priceBeforeIaLevy = price.AppliedTaxes?.FirstOrDefault(t => t.Code == "hkIALevy")?.OriginalPrice ?? price.Amount;
                    return new CommissionGraph
                    {
                        Id = $"group-{g.Key}",
                        Entity = new EntityGraph { Id = g.Key },
                        Amount = g.Sum(c => c.Amount - commissions.Where(c2 => c2.BaseCommissionRatio?.Commission.Id == c.Id).Sum(c2 => c2.Amount)),
                        CurrencyCode = g.First().CurrencyCode,
                        Ratio = priceBeforeIaLevy.HasValue && priceBeforeIaLevy != 0 ? (amount / priceBeforeIaLevy.Value) : 0
                    };
                });

        private static string GetCommissionType(string jsonRule)
        {
            if (jsonRule?.Contains("totalPremium") ?? false) return "ratio";
            else if (jsonRule?.Contains("grossPremium") ?? false) return "ratio";
            else if (jsonRule?.Contains("var") ?? false) return "ratioBasedOnOtherRule";
            else return "flat";
        }
    }
}
