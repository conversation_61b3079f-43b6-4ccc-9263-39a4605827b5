using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Binder;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Claims;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Policies.PricingCache;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Infrastructure.Policies;
using CoverGo.Gateway.Interfaces.Auth;
using CoverGo.Gateway.Interfaces.Binders;
using CoverGo.Gateway.Interfaces.Cases;
using CoverGo.Gateway.Interfaces.Claims;
using CoverGo.Gateway.Interfaces.Notifications;
using CoverGo.Gateway.Interfaces.Policies.Pricing;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Transactions;
using CoverGo.Gateway.Interfaces.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;
using GraphQL.Authorization;
using GraphQL.DataLoader;
using GraphQL.Language.AST;
using GraphQL.Types;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using CurrencyCode = CoverGo.Gateway.Domain.CurrencyCode;
using EventLog = CoverGo.Gateway.Domain.EventLog;

namespace CoverGo.Gateway.Interfaces.Policies
{
    public class ValidatePolicyResultGraphType : ObjectGraphType<ValidatePolicyResult>
    {
        public ValidatePolicyResultGraphType()
        {
            Name = "validatePolicyResult";
            Description = "A result object with potential errors";

            Field<NonNullGraphType<StringGraphType>>("status", "'success' or 'failure'");
            Field<ListGraphType<NonNullGraphType<StringGraphType>>>("errors");
            Field(r => r.Errors_2, type: typeof(ListGraphType<NonNullGraphType<ErrorGraphType>>));

            Field(p => p.PolicyNumber);
            Field(p => p.PlanName);
        }
    }

    public class PolicyGraph : SystemObjectGraph
    {
        public string IssuerNumber { get; set; }
        public string Id { get; set; }
        public GeneratedFromGraph GeneratedFrom { get; set; }
        public string Status { get; set; }
        public string Description { get; set; }
        public DateTime? IssueDate { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? CoolingOffExpiryDate { get; set; }
        public bool? IsCoolingOffSubmitted { get; set; }
        public EntityGraph Holder { get; set; }
        public EntityGraph ContractHolder { get; set; }
        public IEnumerable<EntityGraph> OtherHolders { get; set; }
        public IEnumerable<EntityGraph> OtherContractHolders { get; set; }
        public IEnumerable<EntityGraph> Insured { get; set; }

        public IEnumerable<JacketInstanceGraph> Jackets { get; set; }
        public IEnumerable<EntityGraph> ContractInsured { get; set; }
        public IEnumerable<BeneficiaryEligibilityGraph> BeneficiaryEligibilities { get; set; }
        public IEnumerable<BeneficiaryEligibilityGraph> ContractBeneficiaryEligibilities { get; set; }
        public IEnumerable<CommissionGraph> CommissionSplitRules { get; set; }
        public IEnumerable<CommissionGraph> CommissionSummary { get; set; }
        public InternalGraph Referrer { get; set; }
        public IEnumerable<AttachmentGraph> Attachments { get; set; }
        public IEnumerable<NoteGraph> Notes { get; set; }
        public IEnumerable<PaymentInfoGraph> PaymentInfos { get; set; }
        public IEnumerable<FactGraph> Facts { get; set; }
        public IEnumerable<TransactionGraph> Transactions { get; set; }
        public IEnumerable<OfferGraph> Offers { get; set; }
        public OfferGraph AcceptedOffer { get; set; }
        public string Source { get; set; }

        public IEnumerable<EventLogGraph> Events { get; set; }

        public bool IsIssued { get; set; }
        public LoginGraph TransformedBy { get; set; }
        public LoginGraph IssuedBy { get; set; }
        public LoginGraph CancelledBy { get; set; }
        public string CancellationReason { get; set; }
        public string CancellationSummary { get; set; }

        public LoginGraph RejectedBy { get; set; }
        public List<CodeNameGraph> RejectionCodes { get; set; }
        public string RejectionRemarks { get; set; }
        public string ValuesJsonString { get; set; }
        public List<KeyScalarValue> Values { get; set; }
        public ClientGraph Client { get; set; }

        public IEnumerable<StakeholderGraph> Stakeholders { get; set; }
        public IEnumerable<ClaimGraph> Claims { get; set; }
        public IEnumerable<BenefitGraph> ClaimBalance { get; set; }
        public ProposalGraph Proposal { get; set; }
        public PricingGraph Premium { get; set; }
        public bool IsPremiumOverriden { get; set; }
        public ProductGraph Product { get; set; }
        public IEnumerable<BenefitOptionGraph> BenefitOptions { get; set; }
        public IEnumerable<ExclusionGraph> Exclusions { get; set; }
        public BenefitSummaryGraph BenefitSummary { get; set; }
        public IEnumerable<ClauseGraph> Clauses { get; set; }
        public IEnumerable<EndorsementGraph> Endorsements { get; set; }
        public IEnumerable<AssociatedContractGraph> AssociatedContracts { get; set; }
        public string Fields { get; set; }
        public List<FieldsDiff> FieldsDiffs { get; set; }
        public DataSchemaGraph FieldsSchema { get; set; }
        public string ExtraFields { get; set; }
        public string Pricing { get; set; }
        public bool IsPricingCacheAvailable { get; set; }
        public bool IsRenewal { get; set; }
        public string RenewalNumber { get; set; }
        public List<PolicyGraph> PreviousPolicies { get; set; }
        public string OriginalIssuerNumber { get; set; }
        public int? RenewalVersion { get; set; }
        public string LapseReason { get; set; }
        public string PreviewEndorsementId { get; set; }
        public string AfterEndorsementId { get; set; }
        public List<string> EndorsementId_In
        {
            get
            {
                List<string> endorsementId_In = null;

                if (!string.IsNullOrEmpty(PreviewEndorsementId))
                {
                    endorsementId_In = new List<string>
                    {
                        PreviewEndorsementId
                    };
                }

                if (!string.IsNullOrEmpty(AfterEndorsementId))
                {
                    endorsementId_In ??= new List<string>();
                    endorsementId_In.Add(AfterEndorsementId);
                }

                return endorsementId_In;
            }
        }

        public List<TagGraph> Tags { get; set; }
        public string ProductTreeId { get; set; }
        public List<ProductTreeRecord> ProductTreeRecords { get; set; }
        public DateTime? AsOf { get; set; }
        public AccessPolicy? AccessPolicy { get; set; }
        public PolicyInstallmentStatus? InstallmentStatus { get; set; }
        public List<ExternalPolicyNumberGraph> ExternalPolicyNumbers { get; set; }
        public List<ExternalPolicyStatusGraph> ExternalStatuses { get; set; }

        public CoverGo.Policies.Client.PolicyCommission PolicyCommission { get; set; }
        public bool? IsV2 { get; set; }
        public CoverGo.Policies.Client.PolicyHolderType? HolderType { get; set; }

        public static PolicyGraph ToGraph(Policy domain)
        {
            if (domain == null)
                return null;

            IEnumerable<OfferGraph> offerGraphs = domain.Offers?.Select(o => OfferGraph.ToGraph(o));

            var jacketGraphs = domain.Jackets?.Select(JacketInstanceGraph.ToGraph)?.ToArray();

            OfferGraph acceptedOffer = offerGraphs?.FirstOrDefault(o => o.Id == domain.AcceptedOfferId);

            var policyPremium = PricingGraph.ToGraph(domain.Premium, domain.ProductId);

            IEnumerable<CommissionGraph> commissionGraphs = acceptedOffer?.Premium != null
                 ? domain.Commissions?.Select(c => c.ToGraph(acceptedOffer.Premium, domain.Commissions))
                 : domain.Premium != null
                    ? domain.Commissions?.Select(c => c.ToGraph(policyPremium, domain.Commissions))
                    : Enumerable.Empty<CommissionGraph>();

            var graph = new PolicyGraph
            {
                IssuerNumber = domain.IssuerNumber,
                Id = domain.Id,
                GeneratedFrom = domain.GeneratedFrom != null ? GeneratedFromGraph.ToGraph(domain.GeneratedFrom) : null,
                Description = domain.Description,
                EndDate = domain.EndDate,
                StartDate = domain.StartDate,
                CoolingOffExpiryDate = domain.CoolingOffExpiryDate,
                IsCoolingOffSubmitted = domain.IsCoolingOffSubmitted,
                Status = domain.Status,
                IssueDate = domain.IssueDate,
                LastModifiedAt = domain.LastModifiedAt,
                CreatedAt = domain.CreatedAt,
                Source = domain.Source,
                IsIssued = domain.IsIssued,
                Attachments = domain.Attachments?.Select(a => new AttachmentGraph
                {
                    Path = a.Path,
                    CreatedBy = new LoginGraph { Id = a.CreatedById },
                    LastModifiedBy = new LoginGraph { Id = a.LastModifiedById },
                    CreatedAt = a.CreatedAt,
                    LastModifiedAt = a.LastModifiedAt
                }).ToList(),
                Notes = domain.Notes?.Select(a => NoteGraph.ToGraph(a)),
                PaymentInfos = domain.PaymentInfos?.Select(p => PaymentInfoGraph.ToGraph(p)),
                Facts = domain.Facts?.Select(f => FactGraph.ToGraph(f)),
                Referrer = new InternalGraph { InternalCode = domain.ReferralCode },
                CreatedBy = new LoginGraph { Id = domain.CreatedById },
                LastModifiedBy = new LoginGraph { Id = domain.LastModifiedById },
                TransformedBy = new LoginGraph { Id = domain.TransformedById },
                IssuedBy = new LoginGraph { Id = domain.IssuedById },
                Jackets = jacketGraphs,
                CancelledBy = new LoginGraph { Id = domain.CancelledById },
                CancellationReason = domain.CancellationReason,
                ContractHolder = domain.ContractHolder?.ToGraph(),
                OtherContractHolders = domain.OtherContractHolders?.Select(i => i.ToGraph()) ?? new List<EntityGraph> { },
                ContractInsured = domain.ContractInsured?.Select(i => i.ToGraph()) ?? new List<EntityGraph> { },
                ContractBeneficiaryEligibilities = domain.ContractBeneficiaryEligibilities?.Select(b => BeneficiaryEligibilityGraph.ToGraph(b)),
                CommissionSplitRules = commissionGraphs,
                CommissionSummary = commissionGraphs?.Summarize(acceptedOffer?.Premium ?? policyPremium),
                Offers = offerGraphs,
                AcceptedOffer = acceptedOffer,
                Client = domain.ClientId != null ? new ClientGraph { Id = domain.ClientId } : null,
                RejectedBy = domain.RejectedById != null ? new LoginGraph { Id = domain.RejectedById } : null,
                RejectionCodes = domain.RejectionCodes?.Select(c => new CodeNameGraph { Code = c, ProductId = acceptedOffer?.Product?.ProductId })?.ToList(),
                RejectionRemarks = domain.RejectionRemarks,
                ValuesJsonString = domain.Values?.ToString(),
                Values = domain.Values?.Children<JProperty>().Select(p => new KeyScalarValue { Key = p.Name, Value = p.Value?.ToScalarValue() }).ToList(),
                Premium = policyPremium,
                IsPremiumOverriden = domain.IsPremiumOverridden,
                Product = domain.ProductId == null ? null : new ProductGraph { ProductId = domain.ProductId },
                BenefitOptions = domain.BenefitOptions?.Select(b => new BenefitOptionGraph
                {
                    ProductId = domain.ProductId,
                    BenefitTypeId = b.TypeId,
                    Key = b.Key,
                    Value = b.Value?.ToScalarValue(),
                    RawData = b.Value,
                    CurrencyCode = domain.Premium?.CurrencyCode ?? CurrencyCode.Undefined,
                }.PopulateSystemGraphFields(b)),
                Exclusions = domain.Exclusions?.Select(e => new ExclusionGraph
                {
                    Id = e.Id,
                    BenefitOptionKey = e.BenefitOptionKey,
                    BenefitParentTypeId = e.BenefitParentTypeId,
                    BenefitTypeId = e.BenefitTypeId,
                    CodeName = new CodeNameGraph { Code = e.Code, ProductId = domain.ProductId },
                    Remark = e.Remark,
                }.PopulateSystemGraphFields(e)).ToList(),
                BenefitSummary = new BenefitSummaryGraph
                {
                    ProductId = domain.ProductId,
                    Values = domain.Values,
                    BenefitOptions = domain.BenefitOptions?.Select(s => new Benefit
                    {
                        TypeId = s.TypeId,
                        OptionKey = s.Key,
                        Value = s.Value,
                        CurrencyCode = s.CurrencyCode
                    })?.ToList()
                },
                Clauses = domain.Clauses?.Select(c => ClauseGraph.ToGraph(c)),
                Stakeholders = domain.Stakeholders?.Select(s => StakeholderGraph.ToGraph(s)),
                Endorsements = domain.Endorsements?.Select(e => EndorsementGraph.ToGraph(domain.Id, e)),
                AssociatedContracts = domain.AssociatedContracts?.Select(c => new AssociatedContractGraph
                {
                    Id = c.Id,
                    Contract = c.ContractId == null ? null : new BinderGraph { Id = c.ContractId }
                }),
                Fields = domain.Fields?.ToString(),
                FieldsSchema = new DataSchemaGraph { Id = domain.FieldsSchemaId },
                ExtraFields = domain.ExtraFields?.ToString(Formatting.None),
                IsRenewal = domain.IsRenewal,
                RenewalNumber = domain.RenewalNumber,
                PreviousPolicies = domain.PreviousPolicyIds?.Select(id => new PolicyGraph { Id = id }).ToList() ?? new List<PolicyGraph>(),
                RenewalVersion = domain.RenewalVersion,
                OriginalIssuerNumber = domain.OriginalIssuerNumber,
                FieldsDiffs = domain.FieldsDiffs,
                LapseReason = domain.LapseReason,
                ProductTreeId = domain.ProductTreeId,
                ProductTreeRecords = domain.ProductTreeRecords,
                Tags = domain.Tags?.Select(TagGraph.ToGraph).ToList(),
                IsPricingCacheAvailable = domain.IsPricingCacheAvailable,
                AccessPolicy = domain.AccessPolicy,
                InstallmentStatus = domain.InstallmentStatus,
                ExternalPolicyNumbers = domain.ExternalPolicyNumbers?.Select(ExternalPolicyNumberGraph.ToGraph).ToList(),
                ExternalStatuses = domain.ExternalStatuses?.Select(ExternalPolicyStatusGraph.ToGraph).ToList(),
                PolicyCommission = domain.PolicyCommission,
                IsV2 = domain.IsV2,
                HolderType = domain.HolderType
            };

            return graph;
        }
    }

    public class PoliciesGraphType : ObjectGraphType<PoliciesGraph>
    {
        public PoliciesGraphType(IPolicyService policyService, IEntityService entityService,
            IAuthService authService, PermissionValidator permissionValidator, IProductService productService)
        {
            Name = "policies";
            Description = "Gets all policies";

            PolicyWhere GetWhereFromContext(ResolveFieldContext<PoliciesGraph> context) =>
                context.ComputeArgAndVar<PolicyWhere, PoliciesGraph>("where") ?? new PolicyWhere();

            async Task<PolicyWhere> GetWhereFromContextAndCombineWithAllowedIds(ResolveFieldContext<PoliciesGraph> context)
            {
                var allowedIds = (await permissionValidator.GetTargetIdsFromClaim(
                    context, "readPolicies"))?.ToList();

                PolicyWhere where = allowedIds!.Contains("all")
                    ? GetWhereFromContext(context)
                    : new PolicyWhere
                    {
                        And = new List<PolicyWhere>
                        {
                            GetWhereFromContext(context),
                            new() { Id_in = allowedIds }
                        }
                    };

                List<string> allowedProductTypes = await context.GetAllowedProductTypes(authService);

                if (allowedProductTypes?.Any() ?? false)
                {
                    where = new PolicyWhere
                    {
                        And = new List<PolicyWhere>
                            {
                                where,
                                new()
                                {
                                    ProductId = new ProductIdWhere{ Type_in = allowedProductTypes }
                                }
                            }
                    };
                }

                return where;
            }

            Field(c => c.TotalCount)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();

                    PolicyWhere where = await GetWhereFromContextAndCombineWithAllowedIds(context);

                    if (PolicyWhereContainsHolderFilter(where))
                        await ReplaceHolderToHolderIdIn(tenantId, where, entityService);

                    if (PolicyWhereContainsProductFilter(where))
                        await ReplaceProductToProductIdIn(tenantId, clientId, where, productService);

                    long totalCount = await policyService.GetTotalCountAsync(tenantId, where);
                    return totalCount;
                });

            Field(c => c.List, type: typeof(ListGraphType<PolicyGraphType>))
                .RequirePageLimit()
                .ResolveAsync(async context =>
                {

                    context.PassArgumentsToChildren("asOf");

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();

                    PolicyWhere where = await GetWhereFromContextAndCombineWithAllowedIds(context);

                    if (PolicyWhereContainsHolderFilter(where))
                        await ReplaceHolderToHolderIdIn(tenantId, where, entityService);

                    if (PolicyWhereContainsProductFilter(where))
                        await ReplaceProductToProductIdIn(tenantId, clientId, where, productService);

                    int? skip = context.ComputeArgAndVar<int?, PoliciesGraph>("skip");
                    int? first = context.ComputeArgAndVar<int?, PoliciesGraph>("limit");
                    SortGraph sort = context.ComputeArgAndVar<SortGraph, PoliciesGraph>("sort");
                    DateTime? asOf = context.ComputeArgAndVar<DateTime?, PoliciesGraph>("asOf");

                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy, AsOf = asOf };
                    List<Policy> policies = await policyService.GetAsync(tenantId, queryArguments);
                    return policies.Select(p => PolicyGraph.ToGraph(p));
                });
        }

        static bool PolicyWhereContainsHolderFilter(PolicyWhere where)
        {
            if (where == null) return false;
            return where.Holder != null
                || where.And?.Any(PolicyWhereContainsHolderFilter) == true
                || where.Or?.Any(PolicyWhereContainsHolderFilter) == true;
        }

        async Task ReplaceHolderToHolderIdIn(string tenantId, PolicyWhere where, IEntityService entityService)
        {
            if (where.Holder != null)
            {
                IEnumerable<string> ids = await entityService.GenericQueryIds(tenantId, where.Holder);
                where.ContractHolder = new EntityWhere { Id_in = ids.ToList() };
                where.Holder = null;
            }
            if (where.And != null)
                foreach (PolicyWhere item in where.And)
                    await ReplaceHolderToHolderIdIn(tenantId, item, entityService);
            if (where.Or != null)
                foreach (PolicyWhere item in where.Or)
                    await ReplaceHolderToHolderIdIn(tenantId, item, entityService);
        }

        static bool PolicyWhereContainsProductFilter(PolicyWhere where)
        {
            if (where == null) return false;
            return where.Product != null
                || where.And?.Any(PolicyWhereContainsProductFilter) == true
                || where.Or?.Any(PolicyWhereContainsProductFilter) == true;
        }

        async Task ReplaceProductToProductIdIn(string tenantId, string clientId, PolicyWhere where, IProductService productService)
        {
            if (where.Product != null)
            {
                IEnumerable<Product2> products = await productService.GetAsync(
                    tenantId,
                    clientId,
                    new ProductQuery { Where = where.Product });

                where.ProductId_in = products.Select(p => p.Id).ToList();
                where.Product = null;
            }
            if (where.And != null)
                foreach (PolicyWhere item in where.And)
                    await ReplaceProductToProductIdIn(tenantId, clientId, item, productService);
            if (where.Or != null)
                foreach (PolicyWhere item in where.Or)
                    await ReplaceProductToProductIdIn(tenantId, clientId, item, productService);
        }
    }

    public class PoliciesGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<PolicyGraph> List { get; set; }
    }

    public class PolicyGraphType : ObjectGraphType<PolicyGraph>
    {
        public PolicyGraphType(
            IDataLoaderContextAccessor accessor,
            IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<CoverGo.Users.Domain.Objects.Object, CreateObjectCommand, UpdateObjectCommand> objectService,
            IAuthService authService,
            IPolicyService policyService,
            CoverGoPolicyPricingCachesService policyPricingCachesService,
            IBinderService binderService,
            ITransactionService transactionService,
            IClaimService claimService,
            IProductService productService,
            ICaseService caseService,
            CoverGo.Policies.Client.IPoliciesClient policiesClient,
            PermissionValidator permissionValidator)
        {
            Name = "policy";
            Description = "A policy";

            Field(p => p.Id);
            Field(p => p.IssuerNumber, nullable: true);
            Field(p => p.GeneratedFrom, type: typeof(GeneratedFromGraphType), nullable: true);
            Field(p => p.Status, nullable: true);
            Field(p => p.Description, nullable: true);
            Field(p => p.IssueDate, type: typeof(DateTimeGraphType), nullable: true);
            Field(p => p.CoolingOffExpiryDate, type: typeof(DateTimeGraphType), nullable: true);
            Field(p => p.IsCoolingOffSubmitted, type: typeof(BooleanGraphType));
            Field(p => p.StartDate, nullable: true);
            Field(p => p.EndDate, nullable: true);
            Field(p => p.Holder, type: typeof(EntityInterfaceGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.ContractHolder?.Id == null)
                        return null;
                    string tenantId = context.GetTenantIdFromToken();

                    var allowedIndividualIds = await permissionValidator.GetTargetIdsFromClaim(
                        context, new[] { "readIndividuals", "readPolicyMembers" });
                    var individualLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                        ids => individualService.GetDictionaryAsync(tenantId, allowedIndividualIds.Contains("all")
                            ? new IndividualWhere { Id_in = ids?.ToList() }
                            : new IndividualWhere { Id_in = ids?.Intersect(allowedIndividualIds).ToList() }));

                    var allowedCompanyIds = await permissionValidator.GetTargetIdsFromClaim(
                        context, new[] { "readCompanies", "readPolicyMembers" });
                    var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>("GetCompanies",
                        ids => companyService.GetDictionaryAsync(tenantId, allowedCompanyIds.Contains("all")
                            ? new CompanyWhere { Id_in = ids?.ToList() }
                            : new CompanyWhere { Id_in = ids?.Intersect(allowedCompanyIds).ToList() }));

                    Task<Individual> customerDtoTask = individualLoader.LoadAsync(context.Source.ContractHolder.Id);
                    Task<Company> companyDtoTask = companyLoader.LoadAsync(context.Source.ContractHolder.Id);
                    await Task.WhenAll(customerDtoTask, companyDtoTask);

                    return customerDtoTask.Result?.ToGraph() ?? companyDtoTask.Result?.ToGraph();
                });

            Field(p => p.ContractHolder, type: typeof(EntityInterfaceGraphType), nullable: true);

            Field(p => p.OtherHolders, type: typeof(ListGraphType<EntityInterfaceGraphType>), nullable: true)
                   .ResolveAsync(async context =>
                   {
                       if (context.Source.OtherContractHolders == null || context.Source.OtherContractHolders.Count() == 0)
                           return new List<EntityGraph> { };

                       string tenantId = context.GetTenantIdFromToken();

                       var allowedIndividualIds = await permissionValidator.GetTargetIdsFromClaim(
                           context, new[] { "readIndividuals", "readPolicyMembers" });
                       var individualLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                           ids => individualService.GetDictionaryAsync(tenantId, allowedIndividualIds.Contains("all")
                            ? new IndividualWhere { Id_in = ids?.ToList() }
                            : new IndividualWhere
                            {
                                And = new List<IndividualWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedIndividualIds.ToList() }
                                }
                            }));

                       var allowedCompanyIds = await permissionValidator.GetTargetIdsFromClaim(
                           context, new[] { "readCompanies", "readPolicyMembers" });
                       var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>("GetCompanies",
                           ids => companyService.GetDictionaryAsync(tenantId, allowedCompanyIds.Contains("all")
                            ? new CompanyWhere { Id_in = ids?.ToList() }
                            : new CompanyWhere
                            {
                                And = new List<CompanyWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedCompanyIds.ToList() }
                                }
                            }));

                       IEnumerable<Task<Individual>> individualDtosTasks = context.Source.OtherContractHolders?.Select(i => individualLoader.LoadAsync(i.Id));
                       IEnumerable<Task<Company>> companyDtosTasks = context.Source.OtherContractHolders?.Select(i => companyLoader.LoadAsync(i.Id));

                       var individualCollectionTasks = Task.WhenAll(individualDtosTasks);
                       var companyCollectionTasks = Task.WhenAll(companyDtosTasks);

                       await Task.WhenAll(individualCollectionTasks, companyCollectionTasks);

                       var otherHolders = new List<Entity>();
                       otherHolders.AddRange(individualCollectionTasks.Result?.Select(t => t)?.Where(t => t != null));
                       otherHolders.AddRange(companyCollectionTasks.Result?.Select(t => t)?.Where(t => t != null));

                       return context.Source.OtherContractHolders.Select(g => otherHolders?.FirstOrDefault(o => o?.Id == g?.Id)?.ToGraph())?.Where(e => e != null); //to preserve the order
                   });

            Field(p => p.OtherContractHolders, type: typeof(ListGraphType<EntityInterfaceGraphType>), nullable: true);
            Field(p => p.Jackets, type: typeof(ListGraphType<JacketInstanceGraphType>), nullable: true);
            Field(p => p.Insured, type: typeof(ListGraphType<EntityInterfaceGraphType>), nullable: true)
                    .ResolveAsync(async context =>
                    {
                        if (context.Source.ContractInsured == null || context.Source.ContractInsured.Count() == 0)
                            return new List<EntityGraph> { };

                        string tenantId = context.GetTenantIdFromToken();

                        var allowedIndividualIds = await permissionValidator.GetTargetIdsFromClaim(
                            context, new[] { "readIndividuals", "readPolicyMembers" });
                        var individualLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                            ids => individualService.GetDictionaryAsync(tenantId, allowedIndividualIds.Contains("all")
                            ? new IndividualWhere { Id_in = ids?.ToList() }
                            : new IndividualWhere
                            {
                                And = new List<IndividualWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedIndividualIds.ToList() }
                                }
                            }));

                        var allowedCompanyIds = await permissionValidator.GetTargetIdsFromClaim(
                            context, new[] { "readCompanies", "readPolicyMembers" });
                        var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>("GetCompanies",
                            ids => companyService.GetDictionaryAsync(tenantId, allowedCompanyIds.Contains("all")
                            ? new CompanyWhere { Id_in = ids?.ToList() }
                            : new CompanyWhere
                            {
                                And = new List<CompanyWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedCompanyIds.ToList() }
                                }
                            }));

                        var allowedObjectIds = await permissionValidator.GetTargetIdsFromClaim(
                            context, new[] { "readObjects", "readPolicyMembers" });
                        var objectLoader = accessor.Context.GetOrAddBatchLoader<string, CoverGo.Users.Domain.Objects.Object>("GetObjects",
                            ids => objectService.GetDictionaryAsync(tenantId, allowedObjectIds.Contains("all")
                            ? new ObjectWhere { Id_in = ids?.ToList() }
                            : new ObjectWhere
                            {
                                And = new List<ObjectWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedObjectIds.ToList() }
                                }
                            }));

                        IEnumerable<Task<Individual>> individualDtosTasks = context.Source.ContractInsured?.Select(i => individualLoader.LoadAsync(i.Id));
                        IEnumerable<Task<Company>> companyDtosTasks = context.Source.ContractInsured?.Select(i => companyLoader.LoadAsync(i.Id));
                        IEnumerable<Task<CoverGo.Users.Domain.Objects.Object>> objectDtosTasks = context.Source.ContractInsured?.Select(i => objectLoader.LoadAsync(i.Id));

                        var individualCollectionTasks = Task.WhenAll(individualDtosTasks);
                        var companyCollectionTasks = Task.WhenAll(companyDtosTasks);
                        var objectCollectionTasks = Task.WhenAll(objectDtosTasks);

                        await Task.WhenAll(individualCollectionTasks, companyCollectionTasks, objectCollectionTasks);

                        var insured = new List<Entity>();
                        insured.AddRange(individualCollectionTasks.Result?.Select(t => t)?.Where(t => t != null));
                        insured.AddRange(companyCollectionTasks.Result?.Select(t => t)?.Where(t => t != null));
                        insured.AddRange(objectCollectionTasks.Result?.Select(t => t)?.Where(t => t != null));

                        return context.Source.ContractInsured.Select(g => insured?.FirstOrDefault(i => i?.Id == g?.Id)?.ToGraph())?.Where(e => e != null); //to preserve the order
                    });

            Field(p => p.ContractInsured, type: typeof(ListGraphType<EntityInterfaceGraphType>), nullable: true);

            Field(p => p.BeneficiaryEligibilities, type: typeof(ListGraphType<BeneficiaryEligibilityGraphType>), nullable: true)
                    .ResolveAsync(async context =>
                    {
                        if (context.Source.ContractBeneficiaryEligibilities == null || context.Source.ContractBeneficiaryEligibilities.Count() == 0)
                            return null;

                        string tenantId = context.GetTenantIdFromToken();

                        var individualLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                         i => individualService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                        var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>("GetCompanies",
                        i => companyService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                        IEnumerable<string> ids = context.Source.ContractBeneficiaryEligibilities.Where(i => i.Entity != null).Select(i => i.Entity.Id);

                        IEnumerable<Task<Individual>> individualDtosTasks = ids.Select(i => individualLoader.LoadAsync(i));
                        IEnumerable<Task<Company>> companyDtosTasks = ids.Select(i => companyLoader.LoadAsync(i));

                        var individualCollectionTasks = Task.WhenAll(individualDtosTasks);
                        var companyCollectionTasks = Task.WhenAll(companyDtosTasks);
                        await Task.WhenAll(individualCollectionTasks, companyCollectionTasks);

                        var beneficiaries = new List<Entity>();
                        beneficiaries.AddRange(individualCollectionTasks.Result.Select(t => t).Where(t => t != null));
                        beneficiaries.AddRange(companyCollectionTasks.Result.Select(t => t).Where(t => t != null));

                        return context.Source.ContractBeneficiaryEligibilities.Select(ben => new BeneficiaryEligibilityGraph
                        {
                            Id = ben.Id,
                            Entity = beneficiaries.FirstOrDefault(b => b.Id == ben?.Entity?.Id).ToGraph(),
                            Benefit = ben.Benefit,
                            IsRevocable = ben.IsRevocable,
                            Notes = ben.Notes,
                            Ratio = ben.Ratio
                        });
                    });

            Field(p => p.ContractBeneficiaryEligibilities, type: typeof(ListGraphType<BeneficiaryEligibilityGraphType>), nullable: true);

            Field(p => p.Referrer, type: typeof(InternalGraphType), nullable: true)
                    .ResolveAsync(async context =>
                    {
                        if (context.Source.Referrer?.InternalCode == null)
                            return null;

                        string tenantId = context.GetTenantIdFromToken();

                        var internalLoader = accessor.Context.GetOrAddBatchLoader<string, Internal>("GetInternals",
                        i => internalService.GetDictionaryAsync(tenantId, new EntityWhere { InternalCode_in = i.ToList() }));

                        Internal internalDto = await internalLoader.LoadAsync(context.Source.Referrer.InternalCode);

                        return internalDto.ToGraph() as InternalGraph;
                    });

            Field(p => p.Attachments, type: typeof(ListGraphType<AttachmentGraphType>), nullable: true);
            Field(p => p.Notes, type: typeof(ListGraphType<NoteGraphType>), nullable: true);
            Field(p => p.PaymentInfos, type: typeof(ListGraphType<PaymentInfoGraphType>), nullable: true);
            Field(p => p.Facts, type: typeof(ListGraphType<FactGraphType>), nullable: true);

            Field(p => p.Transactions, type: typeof(ListGraphType<TransactionGraphType>))
                .Argument<BooleanGraphType>("excludeEndorsements", "excludes transactions tied to an endorsement when set to true")
                .Argument<TransactionWhereInputGraphType>("where", "A transaction search filter")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readTransactions");
                    TransactionWhere whereArg = context.GetArgument<TransactionWhere>("where");

                    IDataLoader<string, IEnumerable<Transaction>> loader = accessor.Context.GetOrAddCollectionBatchLoader<string, Transaction>("GetTransactionsFromPolicyIds",
                        policyIds =>
                        {
                            TransactionWhere where = allowedIds.Contains("all")
                                ? new TransactionWhere
                                {
                                    And = new List<TransactionWhere> { new() { PolicyId_in = policyIds?.ToList() } }
                                }
                                : new TransactionWhere
                                {
                                    And = new List<TransactionWhere>
                                    {
                                        new() { PolicyId_in = policyIds?.ToList() },
                                        new() { Id_in = allowedIds.ToList() }
                                    }
                                };
                            if (whereArg != null) where.And.Add(whereArg);
                            return transactionService.GetByPolicyIdsLookupAsync(tenantId, where);
                        });

                    IEnumerable<Transaction> transactions = await loader.LoadAsync(context.Source.Id);
                    bool excludeEndorsements = context.GetArgument<bool>("excludeEndorsements");

                    return excludeEndorsements
                        ? transactions?.Where(t => t.EndorsementId == null)?.Select(l => TransactionGraph.ToGraph(l))
                        : transactions?.Select(l => TransactionGraph.ToGraph(l));
                });

            Field(p => p.Offers, type: typeof(ListGraphType<OfferGraphType>), nullable: true);
            Field(p => p.AcceptedOffer, type: typeof(OfferGraphType));
            Field(p => p.CommissionSplitRules, type: typeof(ListGraphType<CommissionGraphType>));
            Field(p => p.CommissionSummary, type: typeof(ListGraphType<CommissionGraphType>));

            Field(p => p.Source, nullable: true);

            Field(p => p.IsIssued);

            Field(p => p.ValuesJsonString, nullable: true);

            Field(p => p.Values, type: typeof(ListGraphType<KeyValueGraphType>), nullable: true);

            Field(c => c.TransformedBy, type: typeof(LoginGraphType), nullable: true)
                    .ResolveAsync(async context =>
                    {
                        if (context.Source.TransformedBy?.Id == null)
                            return null;

                        var allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,
                            "readLogins", new List<string> { context.Source.TransformedBy.Id });
                        if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.TransformedBy.Id))
                            return null;

                        string tenantId = context.GetTenantIdFromToken();

                        var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                        i => authService.GetLoginsDictionaryAsync(tenantId, i));

                        Login loginDao = await loginLoader.LoadAsync(context.Source.TransformedBy.Id);

                        return LoginGraph.ToGraph(loginDao);
                    });

            Field(c => c.IssuedBy, type: typeof(LoginGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.IssuedBy?.Id == null)
                        return null;

                    var allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,
                        "readLogins", new List<string> { context.Source.IssuedBy.Id });

                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.IssuedBy.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                        i => authService.GetLoginsDictionaryAsync(tenantId, i));

                    Login loginDao = await loginLoader.LoadAsync(context.Source.IssuedBy.Id);

                    return LoginGraph.ToGraph(loginDao);
                });

            Field(c => c.CancelledBy, type: typeof(LoginGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.CancelledBy?.Id == null)
                        return null;

                    var allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,
                        "readLogins", new List<string> { context.Source.CancelledBy.Id });
                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.CancelledBy.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                        i => authService.GetLoginsDictionaryAsync(tenantId, i));

                    Login loginDao = await loginLoader.LoadAsync(context.Source.CancelledBy.Id);

                    return LoginGraph.ToGraph(loginDao);
                });

            Field(c => c.CancellationReason, nullable: true);

            Field(p => p.CancellationSummary, nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.Fields == null)
                        return null;
                    if (context.Source.Product?.ProductId == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();

                    Result<string> result = await productService.Evaluate(tenantId, clientId, new EvaluateProductScriptCommand
                    {
                        DataInput = context.Source.Fields,
                        ProductId = context.Source.Product.ProductId,
                        ScriptType = ScriptTypeEnum.Cancellation
                    });

                    return result.Value;
                });

            Field(c => c.RejectedBy, type: typeof(LoginGraphType), nullable: true)
                    .ResolveAsync(async context =>
                    {
                        if (context.Source.RejectedBy?.Id == null)
                            return null;

                        var allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,
                            "readLogins", new List<string> { context.Source.RejectedBy.Id });
                        if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.RejectedBy.Id))
                            return null;

                        string tenantId = context.GetTenantIdFromToken();

                        var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                        i => authService.GetLoginsDictionaryAsync(tenantId, i));

                        Login loginDao = await loginLoader.LoadAsync(context.Source.RejectedBy.Id);

                        return LoginGraph.ToGraph(loginDao);
                    });

            Field(c => c.Claims, type: typeof(ListGraphType<ClaimGraphType>))
                .Argument<ClaimWhereInputGraphType>("where", "A claim search filter")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readClaims");

                    ClaimWhere whereFromContext = context.ComputeArgAndVar<ClaimWhere, PolicyGraph>("where");
                    ClaimWhere where = allowedIds.Contains("all")
                        ? whereFromContext ?? new ClaimWhere()
                        : whereFromContext != null
                            ? new ClaimWhere
                            {
                                And = new List<ClaimWhere>
                                {
                                    whereFromContext,
                                    new() { Id_in = allowedIds }
                                }
                            }
                            : new ClaimWhere { Id_in = allowedIds };

                    var loader = accessor.Context.GetOrAddCollectionBatchLoader<string, Claim>("GetClaimsFromPolicyIds",
                        policyIds => claimService.GetByPolicyIdsLookupAsync(tenantId, new ClaimWhere
                        {
                            And = new List<ClaimWhere>
                            {
                                new() { PolicyId_in = policyIds },
                                where
                            }
                        }));

                    IEnumerable<Claim> claims = await loader.LoadAsync(context.Source.Id);

                    return claims?.Select(l => ClaimGraph.ToGraph(l));
                });

            Field(c => c.ClaimBalance, type: typeof(ListGraphType<BenefitGraphType>))
                .ResolveAsync(async context =>
                {
                    if (context.Source.AcceptedOffer?.Product?.ProductId == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();

                    bool loadRepresentation = context.ProductRepresentationRequested();

                    // Get offer summary
                    var productLoader = accessor.Context.GetOrAddBatchLoader<ProductId, Product2>("GetProducts",
                        p => productService.GetDictionaryAsync(tenantId, clientId, new ProductWhere { Id_in = p?.ToList() }, loadRepresentation));

                    Product2 productDto = await productLoader.LoadAsync(context.Source.AcceptedOffer?.Product?.ProductId);

                    var summarizedBenefits = ProductExtensions.SummarizeBenefits(context.Source.AcceptedOffer.BenefitSummary.BenefitOptions, productDto?.Benefits).ToList();

                    // Get claim summary
                    var allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readClaims");
                    var requestedWhere = new ClaimWhere { And = new List<ClaimWhere> { new() { PolicyId_in = new List<string> { context.Source.Id } }, new() { Status = "APPROVED" } } };
                    IEnumerable<Claim> claims = await claimService.GetAsync(tenantId, new Domain.QueryArguments
                    {
                        Where = allowedIds.Contains("all")
                            ? requestedWhere
                            : new ClaimWhere { And = new List<ClaimWhere> { requestedWhere, new() { Id_in = allowedIds } } }
                    });

                    // Substract
                    foreach (BenefitClaim benefitClaim in claims.SelectMany(c => c.BenefitClaims))
                        GraphQLToolsExtensions.RecursiveSubstract(summarizedBenefits, benefitClaim, benefitClaim.BenefitTypeId);

                    return summarizedBenefits?.Select(b => b.ToGraph(context.Source.AcceptedOffer?.Product?.ProductId))?.ToList();
                });

            Field(c => c.RejectionCodes, type: typeof(ListGraphType<RejectionCodeNameGraphType>), nullable: true);

            Field(c => c.RejectionRemarks, nullable: true);

            Field(c => c.Client, type: typeof(ClientGraphType));

            Field(c => c.Events, type: typeof(ListGraphType<EventLogGraphType>))
                  .ResolveAsync(async context =>
                  {
                      string tenantId = context.GetTenantIdFromToken();

                      var dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, EventLog>(
                          "GetPolicyEvents",
                          async i => (await policyService.GetEventsAsync(tenantId, new EventQuery { Ids = i?.ToList() })).ToLookup(e => e.RelatedId)
                      );

                      IEnumerable<EventLog> logs = await dataLoader.LoadAsync(context.Source.Id);

                      return logs.Select(EventLogGraph.ToGraph);
                  });

            Field(c => c.Stakeholders, type: typeof(ListGraphType<StakeholderGraphType>));

            Field(c => c.Proposal, type: typeof(ProposalGraphType))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    var allowedCaseIds = await permissionValidator.GetTargetIdsFromClaim(context, "readCases");
                    var proposalLoader = accessor.Context.GetOrAddBatchLoader<string, Proposal>("GetProposalByPolicyId",
                      i => caseService.GetProposalDictionaryByPolicyIdAsync(tenantId, allowedCaseIds.Contains("all")
                          ? new CaseWhere { Proposals_contains = new ProposalWhere { PolicyId_contains_some = i?.ToList() } }
                          : new CaseWhere
                          {
                              And = new List<CaseWhere>
                              {
                                new() { Proposals_contains = new ProposalWhere { PolicyId_contains_some = i?.ToList() } },
                                new() { Id_in = allowedCaseIds.ToList() }
                              }
                          }));

                    Proposal proposal = await proposalLoader.LoadAsync(context.Source.Id);

                    return ProposalGraph.ToGraph(proposal);
                });

            Field(p => p.Premium, type: typeof(PricingGraphType));
            Field(p => p.IsPremiumOverriden);
            Field(p => p.Product, type: typeof(ProductGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.Product?.ProductId == null)
                        return null;

                    bool loadRepresentation = context.ProductRepresentationRequested();

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken(false);

                    var productLoader = accessor.Context.GetOrAddBatchLoader<ProductId, Product2>("GetProducts",
                        p => productService.GetDictionaryAsync(tenantId, clientId, new ProductWhere { Id_in = p?.ToList() }, loadRepresentation));

                    Product2 productDto = await productLoader.LoadAsync(context.Source.Product.ProductId);

                    return productDto.ToGraph();
                });
            Field(p => p.BenefitOptions, type: typeof(ListGraphType<BenefitOptionChoiceGraphType>));
            Field(p => p.BenefitSummary, type: typeof(BenefitSummaryGraphType));
            Field(p => p.Exclusions, type: typeof(ListGraphType<ExclusionGraphType>));
            Field(p => p.Clauses, type: typeof(ListGraphType<ClauseGraphType>))
                .GetPaginationArguments()
                .Resolve(context => context.Source.Clauses.Sort(context));


            Field(p => p.Endorsements, type: typeof(ListGraphType<EndorsementGraphType>))
                .Argument<EndorsementWhereInputGraphType>("where", "the endorsement where input")
                .Resolve(context =>
                {
                    IEnumerable<EndorsementGraph> endorsements = context.Source.Endorsements;
                    if (!endorsements?.Any() ?? true)
                        return Enumerable.Empty<EndorsementGraph>();

                    EndorsementWhere where = context.GetArgument<EndorsementWhere>("where");
                    if (where != null)
                    {
                        if (where.Id != null)
                        {
                            var endorsement = endorsements.FirstOrDefault(o => o.Id == where.Id);
                            endorsements = new List<EndorsementGraph> { endorsement };
                            if (endorsement == null)
                            {
                                return endorsements;
                            }
                        }

                        if (where.Id_in != null)
                            endorsements = endorsements.Where(o => where.Id_in.Contains(o.Id));

                        if (where.Type != null)
                            endorsements = endorsements.Where(o => o.Type == where.Type);

                        if (where.Type_in != null)
                            endorsements = endorsements.Where(o => where.Type_in.Contains(o.Type));

                        if (where.Status != null)
                            endorsements = endorsements.Where(x => x.Status == where.Status);

                        if (where.Status_in != null)
                            endorsements = endorsements.Where(x => where.Status_in.Contains(x.Status));

                        if (where.Source_exists != null)
                            endorsements = endorsements.Where(x => (x.Source == null) ^ (where.Source_exists == true));

                        if (where.LastModifiedAt_gt.HasValue)
                            endorsements = endorsements.Where(x => x.LastModifiedAt > where.LastModifiedAt_gt.Value);

                        if (where.LastModifiedAt_lt.HasValue)
                            endorsements = endorsements.Where(x => x.LastModifiedAt < where.LastModifiedAt_lt.Value);

                        if (where.MemberMovementVersion_in != null)
                            endorsements = endorsements.Where(x => where.MemberMovementVersion_in.Contains(x.MemberMovementVersion));
                    }

                    return endorsements;
                });

            Field(p => p.AssociatedContracts, type: typeof(ListGraphType<AssociatedContractGraphType>));

            Field(c => c.Fields, nullable: true)
                .ResolveAsync(context => context.GetPermittedProjection(authService, accessor, context.Source.Fields, context.Source.Id, "policy"));

            Field(c => c.FieldsDiffs, type: typeof(ListGraphType<FieldsDiffGraphType>), nullable: true)
                .Argument<FieldsDiffWhereInputGraphType>("where", "the FieldsDiff where input")
                .Resolve(context =>
                {
                    List<FieldsDiff> diffs = context.Source.FieldsDiffs;
                    if (!diffs?.Any() ?? true)
                        return new List<FieldsDiff>();

                    FieldsDiffWhere where = context.GetArgument<FieldsDiffWhere>("where");

                    if (where == null) return diffs;

                    if (where.From != null)
                        diffs = diffs.Where(o => o.TimeStamp >= where.From).ToList();

                    if (where.To != null)
                        diffs = diffs.Where(o => o.TimeStamp <= where.To).ToList();

                    return diffs;
                });

            Field(c => c.FieldsSchema, type: typeof(DataSchemaGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.FieldsSchema?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var batchLoader = accessor.Context.GetOrAddBatchLoader<string, DataSchema>("GetDataSchemas",
                        async ids =>
                        {
                            IReadOnlyCollection<DataSchema> dataSchemas
                                = await caseService.GetDataSchemasAsync(tenantId, new DataSchemaWhere { Id_in = ids?.ToList() });
                            return ids?.Join(dataSchemas, i => i, ds => ds.Id, (i, ds) => (i, ds))
                                .ToDictionary(p => p.i, p => p.ds);
                        });

                    return DataSchemaGraph.ToGraph(await batchLoader.LoadAsync(context.Source.FieldsSchema.Id));
                });

            Field(c => c.ExtraFields, nullable: true)
                .ResolveAsync(context => context.GetPermittedProjection(authService, accessor, context.Source.ExtraFields, context.Source.Id, "policy"));

            Field(p => p.Pricing, nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.Fields == null)
                        return null;
                    if (context.Source.Product?.ProductId == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();

                    if (context.Source.IsPricingCacheAvailable)
                    {
                        DateTime? asOf = context.ComputeArgAndVar<DateTime?, PolicyGraph>("asOf");
                        QueryArguments<Filter<PolicyPricingCacheFilter>> queryArguments = new()
                        {
                            Where = new Filter<PolicyPricingCacheFilter>
                            {
                                Where = new PolicyPricingCacheFilter
                                {
                                    Id = context.Source.Id
                                }
                            },
                            AsOf = asOf
                        };
                        List<PolicyPricingCache> caches = await policyPricingCachesService.Query(tenantId, queryArguments, context.CancellationToken);
                        if (caches.Any())
                            return caches.FirstOrDefault()?.PricingCache;
                    }

                    Result<string> result = await productService.Evaluate(tenantId, clientId, new EvaluateProductScriptCommand
                    {
                        DataInput = context.Source.Fields,
                        ProductId = context.Source.Product.ProductId,
                        ScriptType = ScriptTypeEnum.Pricing
                    });

                    return result.Value;
                });

            Field(p => p.IsRenewal, nullable: true);
            Field(p => p.RenewalNumber, nullable: true);

            Field(p => p.PreviousPolicies, type: typeof(ListGraphType<PolicyGraphType>), nullable: true)
                   .ResolveAsync(async context =>
                   {
                       if (context.Source.PreviousPolicies == null || !context.Source.PreviousPolicies.Any())
                           return new List<PolicyGraph>();

                       string tenantId = context.GetTenantIdFromToken();

                       var allowedPolicyIds = await permissionValidator.GetTargetIdsFromClaim(context, "readPolicies");
                       var policyLoader = accessor.Context.GetOrAddBatchLoader<string, Policy>("GetPolicies",
                           ids => policyService.GetDictionaryAsync(tenantId, allowedPolicyIds.Contains("all")
                            ? new PolicyWhere { Id_in = ids?.ToList() }
                            : new PolicyWhere
                            {
                                And = new List<PolicyWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedPolicyIds.ToList() }
                                }
                            }));

                       IEnumerable<Task<Policy>> policyTasks = context.Source.PreviousPolicies.Select(i => policyLoader.LoadAsync(i.Id));

                       Policy[] policies = await Task.WhenAll(policyTasks);

                       return policies.Select(PolicyGraph.ToGraph).Where(p => p != null).ToList();
                   });

            Field(p => p.Tags, type: typeof(ListGraphType<TagGraphType>), nullable: true);
            Field(p => p.AccessPolicy, type: typeof(AccessPolicyTypeEnumGraphType), nullable: true);
            Field(p => p.InstallmentStatus, type: typeof(InstallmentStatusGraphType), nullable: true);
            Field(p => p.PolicyCommission, type: typeof(PolicyCommissionGraphType), nullable: true);

            Field<PolicyMembersGraphType>()
                .Name("members")
                .Description("Policy members info")
                .GetPaginationArguments2()
                .Argument<BooleanGraphType>("isTerminated", "true for the members who already reached the end date")
                .Argument<BooleanGraphType>("filterOutTerminatedMemberMovements", "set to false in order to get terminated movememnts")
                .Argument<BooleanGraphType>("filterOutUnderwritingNotApproved", "set to false in order to account for underwriting not approved movements")
                .Argument<PolicyMembersFilterAggregateInputGraph>("filter", "filter")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();
                    return new PolicyMembersGraph(context.Source.Id, context.Source.EndorsementId_In, context.Source.AsOf);
                });

            Field<PolicyMembersMovementsGraphType>()
                .Name("membersMovements")
                .Description("Policy members movements")
                .GetPaginationArguments2()
                .Argument<PolicyMembersFilterAggregateInputGraph>("filter", "filter")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();
                    return new PolicyMembersGraph(context.Source.Id, context.Source.EndorsementId_In, context.Source.AsOf);
                });

            Field<PolicyMembersActivityGraphType>()
                .Name("membersActivity")
                .Description("Policy members activity")
                .GetPaginationArguments2()
                .Argument<PolicyMembersFilterAggregateInputGraph>("filter", "filter")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();
                    return new PolicyMembersGraph(context.Source.Id, context.Source.EndorsementId_In, context.Source.AsOf);
                });

            Field<PricingRootGraphType>()
                .Name("pricingData")
                .Description("Policy pricing")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    DateTime? asOf = context.ComputeArgAndVar<DateTime?, PolicyGraph>("asOf");

                    var where = new CoverGo.Policies.Client.PricingBreakdownWhere
                    {
                        PolicyId = context.Source.Id,
                        EndorsementId_In = context.Source.PreviewEndorsementId == null ? null : new List<string> { context.Source.PreviewEndorsementId },
                        AsOf = asOf ?? context.Source.AsOf,
                        OutputSchema = GraphqlToOutputSchemaConverter.ConvertGraphQlSubfieldsToOutputSchema(context),
                    };

                    var result = await policiesClient.PolicyPricing_PricingAsync(tenantId, where, context.CancellationToken);
                    return result;
                });

            Field<PolicyUnderwritingsGraphType>()
                .Name("underwritings")
                .Description("Underwritings")
                .GetPaginationArguments()
                .Argument<PolicyUnderwritingFilterFilterGraphType>("filter", "filter")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();
                    return new PolicyUnderwritings(context.Source.Id, context.Source.PreviewEndorsementId);
                });

            Field(p => p.OriginalIssuerNumber, nullable: true)
                .Description("Issuer number of policy originally renewed from, supposed to keep the same value each renewal");
            Field(p => p.RenewalVersion, nullable: true)
                .Description("Supposed to be incremented each time when we renewing policy");

            Field(c => c.LapseReason, nullable: true);

            Field(p => p.ProductTreeId, nullable: true);

            Field(p => p.IsV2, nullable: true);

            Field(p => p.ProductTreeRecords, type: typeof(ListGraphType<ProductTreeRecordGraphType>));

            Field(p => p.ExternalPolicyNumbers, type: typeof(ListGraphType<ExternalPolicyNumberGraphType>));

            Field(p => p.ExternalStatuses, type: typeof(ListGraphType<ExternalPolicyStatusGraphType>), nullable: true);

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class ExternalPolicyNumberGraph
    {
        public string Type { get; set; }
        public string Value { get; set; }

        public static ExternalPolicyNumberGraph ToGraph(ExternalPolicyNumber domain) =>
            domain == null
                ? null
                : new ExternalPolicyNumberGraph
                {
                    Type = domain.Type,
                    Value = domain.Value
                };
    }

    public class ExternalPolicyNumberGraphType : ObjectGraphType<ExternalPolicyNumberGraph>
    {
        public ExternalPolicyNumberGraphType()
        {
            Name = "externalPolicyNumber";
            Description = "An external policy number";

            Field(t => t.Type, nullable: true);
            Field(t => t.Value, nullable: true);
        }
    }

    public class ExternalPolicyStatusGraph
    {
        public string Type { get; set; }
        public string Value { get; set; }

        public static ExternalPolicyStatusGraph ToGraph(ExternalPolicyStatus domain) =>
            domain == null
                ? null
                : new ExternalPolicyStatusGraph
                {
                    Type = domain.Type,
                    Value = domain.Value
                };
    }

    public class ExternalPolicyStatusGraphType : ObjectGraphType<ExternalPolicyStatusGraph>
    {
        public ExternalPolicyStatusGraphType()
        {
            Name = "externalPolicyStatus";
            Description = "An external policy status";

            Field(t => t.Type, nullable: true);
            Field(t => t.Value, nullable: true);
        }
    }

    public class PolicyEndorsementWhereInputGraphType : InputObjectGraphType<PolicyEndorsementWhere>
    {
        public PolicyEndorsementWhereInputGraphType()
        {
            Name = "policyEndorsementWhereInput";
            Description = "Filter policies based on their endorsements";

            Field(f => f.Type, nullable: true);
            Field(f => f.Type_in, nullable: true);
            Field(x => x.Status, nullable: true);
            Field(x => x.Status_in, nullable: true);
            Field(x => x.MemberMovementVersion_in, nullable: true, typeof(ListGraphType<EndorsementMemberMovementVersionGraphType>));
        }
    }

    public class EndorsementWhereInputGraphType : InputObjectGraphType<EndorsementWhere>
    {
        public EndorsementWhereInputGraphType()
        {
            Name = "endorsementWhereInput";
            Description = "An endorsemet search filter";

            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);
            Field(f => f.Type, nullable: true);
            Field(f => f.Type_in, nullable: true);
            Field(x => x.Status, nullable: true);
            Field(x => x.Status_in, nullable: true);
            Field(x => x.Source_exists, nullable: true);
            Field(x => x.LastModifiedAt_gt, nullable: true);
            Field(x => x.LastModifiedAt_lt, nullable: true);
            Field(x => x.MemberMovementVersion_in, nullable: true, typeof(ListGraphType<EndorsementMemberMovementVersionGraphType>));
        }
    }

    public class FieldsDiffWhereInputGraphType : InputObjectGraphType<FieldsDiffWhere>
    {
        public FieldsDiffWhereInputGraphType()
        {
            Name = "fieldsDiffWhereInput";
            Description = "A FieldsDiff search filter";

            Field(f => f.From, nullable: true);
            Field(f => f.To, nullable: true);
        }
    }

    public class FieldsDiffGraphType : ObjectGraphType<FieldsDiff>
    {
        public FieldsDiffGraphType()
        {
            Name = "fieldsDiff";

            Field(f => f.DiffType, type: typeof(FieldsDiffTypeGraphType), nullable: true);
            Field(f => f.TimeStamp, nullable: true);
            Field(name: "before", type: typeof(StringGraphType), resolve: f => f.Source.Before?.ToString());
            Field(name: "after", type: typeof(StringGraphType), resolve: f => f.Source.After?.ToString());
            Field(f => f.Changes, type: typeof(ListGraphType<FieldsDiffItemGraphType>), nullable: true);
        }
    }

    public class FieldsDiffItemGraphType : ObjectGraphType<FieldsDiffItem>
    {
        public FieldsDiffItemGraphType()
        {
            Name = "fieldsDiffItem";

            Field(f => f.Before, nullable: true);
            Field(f => f.After, nullable: true);
            Field(f => f.Path, nullable: true);
        }
    }

    public class FieldsDiffTypeGraphType : EnumerationGraphType<FieldsDiffType> { }

    public class PolicyWhereInputGraphType : InputObjectGraphType<PolicyWhere>
    {
        public PolicyWhereInputGraphType()
        {
            Name = "policyWhereInput";
            Description = "A policy search filter";

            Field(f => f.Or, type: typeof(ListGraphType<PolicyWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<PolicyWhereInputGraphType>));

            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);
            Field(f => f.ContractHolder, type: typeof(IndividualWhereInputGraphType), nullable: true);
            Field(f => f.Holder, type: typeof(EntityWhereInputGraphType), nullable: true);
            Field(f => f.Fields, type: typeof(FieldsWhereInputGraphType), nullable: true);
            Field(f => f.FieldsDiff_contains, type: typeof(FieldsDiffWhereInputGraphType), nullable: true);
            Field(f => f.Endorsement, type: typeof(PolicyEndorsementWhereInputGraphType), nullable: true);
            Field(f => f.Product, type: typeof(ProductWhereInputGraphType), nullable: true);
            Field(f => f.ProductId, type: typeof(ProductIdWhereInputGraphType), nullable: true).DeprecationReason("Deprecated");
            Field(f => f.Status_in, nullable: true);
            Field(f => f.Status_not_in, nullable: true);
            Field(f => f.Description, nullable: true);
            Field(f => f.Description_contains, nullable: true);
            Field(f => f.StartDate, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(f => f.StartDate_gt, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(f => f.StartDate_lt, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(f => f.StatusModifiedAt, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(f => f.StatusModifiedAt_gt, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(f => f.StatusModifiedAt_lt, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(f => f.EndDate, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(f => f.EndDate_gt, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(f => f.EndDate_lt, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(f => f.IssueDate_gt, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(f => f.IssueDate_lt, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(f => f.IssuerNumber_contains, nullable: true);
            Field(f => f.IssuerNumber_in, nullable: true);
            Field(f => f.Source, nullable: true);
            Field(f => f.Source_in, nullable: true);
            Field(f => f.IsRenewal, nullable: true);
            Field(f => f.IsHighestPriorityRenewalPerPolicy, nullable: true);
            Field(f => f.IsIssued, nullable: true);
            Field(f => f.PreviousPolicyIds_contains, nullable: true);
            Field(f => f.PreviousPolicyIds_contains_every, nullable: true);
            Field(f => f.PreviousPolicyIds_contains_some, nullable: true);
            Field(f => f.OriginalIssuerNumber_contains, nullable: true);
            Field(f => f.OriginalIssuerNumber_in, nullable: true);
            Field(f => f.RenewalVersion, nullable: true);
            Field(f => f.RenewalVersion_gt, nullable: true);
            Field(f => f.RenewalVersion_lt, nullable: true);
            Field(f => f.Facts_contains, type: typeof(FactWhereInputGraphType));
            Field(f => f.ReferralCode, nullable: true);
            Field(f => f.Tags_some, type: typeof(TagWhereInputGraphType), nullable: true);
            Field(f => f.AccessPolicy, type: typeof(AccessPolicyTypeEnumGraphType), nullable: true);
            Field(f => f.Stakeholders_contains, type: typeof(StakeholderWhereInputGraphType));
            Field(f => f.InstallmentStatus, type: typeof(InstallmentStatusGraphType), nullable: true);
            Field(f => f.ExternalPolicyNumber_in, type: typeof(ExternalPolicyNumberWhereInputGraphType), nullable: true);
            Field(f => f.ExternalStatus_in, type: typeof(ListGraphType<ExternalPolicyStatusWhereInputGraphType>), nullable: true);

            this.PopulateSystemWhereFields();
        }
    }

    public class InitializeTransactionInputGraphType : InputObjectGraphType<InitializeTransactionCommand>
    {
        public InitializeTransactionInputGraphType()
        {
            Name = "initializeTransactionInput";

            Field(p => p.Amount);
            Field(p => p.TransactionNumber, nullable: true);
            Field(p => p.Description, nullable: true);
            Field(p => p.CurrencyCode, type: typeof(NonNullGraphType<CurrencyCodeEnumerationGraphType>));
            Field(p => p.Method, type: typeof(PaymentMethodEnumerationGraphType));
            Field(p => p.PolicyId, nullable: true);
            Field(p => p.ProposalId, nullable: true);
            Field(p => p.RedirectToUrl, nullable: true);
            Field(p => p.ProviderConfigId, nullable: true);
        }
    }

    public class RefundTransactionInputGraphType : InputObjectGraphType<RefundCommand>
    {
        public RefundTransactionInputGraphType()
        {
            Name = "refundInput";

            Field(p => p.Amount, nullable: true);
            Field(p => p.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType), nullable: true);
            Field(p => p.Remark, nullable: true);
        }
    }

    public class PolicyResultGraphType : ObjectGraphType<Result<PolicyStatus>>
    {
        public PolicyResultGraphType()
        {
            Name = "policyResult";
            Description = "A result of a policy operation";

            Field<NonNullGraphType<StringGraphType>>("status");
            Field<ListGraphType<StringGraphType>>("errors");

            Field<PolicyStatusGraphType>("policyStatus", resolve: ctx => ctx.Source.Value);
        }
    }

    public class TransactionRedirectionResultGraphType : ObjectGraphType<Result<TransactionRedirection>>
    {
        public TransactionRedirectionResultGraphType()
        {
            Name = "transactionRedirectionResult";

            Field(p => p.Status, nullable: true);
            Field(p => p.Errors, nullable: true);
            Field(p => p.Value, type: typeof(TransactionRedirectionGraphType));
        }
    }

    public class TransactionRedirectionGraphType : ObjectGraphType<TransactionRedirection>
    {
        public TransactionRedirectionGraphType()
        {
            Name = "transactionRedirection";

            Field(p => p.Token, nullable: true);
            Field(p => p.TransactionId);
            Field(p => p.Url, nullable: true);
            Field<ScalarValueGraphType>("parameters", resolve: ctx => ctx.Source.Parameters?.ToScalarValue());
            Field<StringGraphType>("parametersJsonString", resolve: ctx => ctx.Source.Parameters?.ToString());
            Field(p => p.HttpMethod);
        }
    }

    public class PolicyStatusGraphType : ObjectGraphType<PolicyStatus>
    {
        public PolicyStatusGraphType()
        {
            Name = "policyStatus";
            Description = "A policy status";

            Field(s => s.Id);
            Field(s => s.Status, nullable: true);
        }
    }

    public class InitializePolicyInputGraphType : InputObjectGraphType<InitializePolicyInputGraph>
    {
        public InitializePolicyInputGraphType()
        {
            Name = "initializePolicyInput";
            Description = "Input to initialize a policy";

            Field(p => p.HolderId, nullable: true);
            Field(p => p.GeneratedFrom, type: typeof(PolicyGeneratedFromInputGraphType), nullable: true);
            Field(p => p.IssueDate, type: typeof(DateGraphType), nullable: true);
            Field(p => p.StartDate, type: typeof(DateGraphType), nullable: true);
            Field(p => p.EndDate, type: typeof(DateGraphType), nullable: true);
            Field(p => p.OtherHolderIds, nullable: true);
            Field(p => p.InsuredIds, nullable: true);
            Field(p => p.Values, type: typeof(ListGraphType<KeyValueInputGraphType>), nullable: true);
            Field(p => p.Facts, type: typeof(ListGraphType<AddFactInputGraphType>));
            Field(p => p.IssuerNumber, nullable: true);
            Field(p => p.Description, nullable: true);
            Field(p => p.ReferralCode, nullable: true);
            Field(p => p.Source, nullable: true);
            Field(p => p.Status, nullable: true);
            Field(p => p.ProductId, type: typeof(ProductIdInputGraphType), nullable: true);
            Field(p => p.Premium, type: typeof(PremiumInputGraphType), nullable: true);
            Field(p => p.Fields, nullable: true);
            Field(p => p.FieldsSchemaId, nullable: true);
            Field(p => p.IsRenewal, nullable: true);
            Field(p => p.RenewalNumber, nullable: true);
            Field(p => p.PreviousPolicyIds, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            Field(p => p.OriginalIssuerNumber, nullable: true)
                .Description("Issuer number of policy originally renewed from, supposed to keep the same value each renewal");
            Field(p => p.RenewalVersion, nullable: true)
                .Description("Supposed to be incremented each time when we renewing policy");
        }
    }

    public class InitializePolicyInputGraph
    {
        public string IssuerNumber { get; set; }
        public PolicyGeneratedFromInputGraph GeneratedFrom { get; set; }
        public List<string> OtherHolderIds { get; set; }
        public List<string> InsuredIds { get; set; }
        public DateTime? IssueDate { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string ReferralCode { get; set; }
        public string Description { get; set; }
        public List<KeyScalarValue> Values { get; set; }
        public List<AddFactInputGraph> Facts { get; set; }
        public string HolderId { get; set; }
        public string Source { get; set; }
        public string Status { get; set; }
        public ProductId ProductId { get; set; }
        public PremiumInput Premium { get; set; }
        public string Fields { get; set; }
        public string FieldsSchemaId { get; set; }
        public bool IsRenewal { get; set; }
        public string RenewalNumber { get; set; }
        public List<string> PreviousPolicyIds { get; set; }
        public string OriginalIssuerNumber { get; set; }
        public int? RenewalVersion { get; set; }
    }

    public class UpdatePolicyInputGraphType : InputObjectGraphType<UpdatePolicyInputGraph>
    {
        public UpdatePolicyInputGraphType()
        {
            Name = "updatePolicyInput";
            Description = "Input to update a policy";

            Field(p => p.HolderId, nullable: true);
            Field(p => p.InsuredIds, nullable: true);
            Field(p => p.ContractHolderIndividual, type: typeof(UpdateContractIndividualInputGraphType))
                .Description("only populate either `ContractHolderIndividual` or `ContractHolderCompany` but not both.");
            Field(p => p.ContractHolderCompany, type: typeof(UpdateContractCompanyInputGraphType))
                .Description("only populate either `ContractHolderIndividual` or `ContractHolderCompany` but not both.");
            Field(p => p.IssuerNumber, nullable: true);
            Field(p => p.Status, nullable: true);
            Field(p => p.IssueDate, type: typeof(DateGraphType), nullable: true);
            Field(p => p.StartDate, type: typeof(DateGraphType), nullable: true);
            Field(p => p.EndDate, type: typeof(DateGraphType), nullable: true);
            Field(p => p.Description, nullable: true);
            Field(p => p.ReferralCode, nullable: true);
            Field(p => p.Values, type: typeof(ListGraphType<KeyValueInputGraphType>));
            Field(p => p.Source, nullable: true);
            Field(p => p.Premium, type: typeof(PremiumInputGraphType));
            Field(p => p.ProductId, type: typeof(ProductIdInputToUpdateInputGraphType));
            Field(p => p.Fields, nullable: true);
            Field(p => p.FieldsPatch, nullable: true);
            Field(p => p.FieldsSchemaId, nullable: true);
            Field(p => p.ExtraFields, nullable: true);
            Field(p => p.LapseReason, nullable: true);
        }
    }

    public class UpdatePolicyInputGraph
    {
        public string IssuerNumber { get; set; }
        public bool IsIssuerNumberChanged { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }

        public string HolderId { get; set; }

        public bool IsHolderIdChanged { get; set; }

        public List<string> InsuredIds { get; set; }

        public bool IsInsuredIdsChanged { get; set; }
        public UpdateIndividualCommand ContractHolderIndividual { get; set; }
        public bool IsContractHolderIndividualChanged { get; set; }
        public UpdateCompanyCommand ContractHolderCompany { get; set; }
        public bool IsContractHolderCompanyChanged { get; set; }
        public DateTime? IssueDate { get; set; }
        public bool IsIssueDateChanged { get; set; }
        public DateTime? StartDate { get; set; }
        public bool IsStartDateChanged { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsEndDateChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string ReferralCode { get; set; }
        public bool IsReferralCodeChanged { get; set; }
        public List<KeyScalarValue> Values { get; set; }
        public bool IsValuesChanged { get; set; }
        public PremiumToUpdate Premium { get; set; }
        public bool IsPremiumChanged { get; set; }
        public ProductIdToUpdate ProductId { get; set; }
        public bool IsProductIdChanged { get; set; }
        public string Source { get; set; }
        public bool IsSourceChanged { get; set; }
        public string Fields { get; set; }
        public string FieldsPatch { get; set; }
        public bool IsFieldsChanged { get; set; }
        public string FieldsSchemaId { get; set; }
        public bool IsFieldsSchemaIdChanged { get; set; }
        public string ExtraFields { get; set; }
        public bool IsExtraFieldsChanged { get; set; }
        public string LapseReason { get; set; }
        public bool IsLapseReasonChanged { get; set; }
    }


    public class RequestPolicyReviewInputGraphType : InputObjectGraphType<PolicyRequestReview>
    {
        public RequestPolicyReviewInputGraphType()
        {
            Name = "requestPolicyReviewInput";
            Description = "The input to review a policy";

            Field(r => r.IsApproved);
            Field(r => r.Note, nullable: true);
        }
    }

    public class PolicyUpdateRequestGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string PolicyId { get; set; }
        public IEnumerable<UpdatePolicyCommandGraph> UpdatePolicyCommands { get; set; }
        public IEnumerable<UpdatePaymentInfoCommand> UpdatePaymentInfoCommands { get; set; }
        public IEnumerable<UpdateBeneficiaryEligibilityCommand> UpdateBeneficiaryEligibilityCommands { get; set; }
        public IEnumerable<BenefitOptionInputGraph> UpsertBenefitOptionCommands { get; set; }
        public string ApprovalStatus { get; set; }
    }

    public class PolicyUpdateRequestGraphType : ObjectGraphType<PolicyUpdateRequestGraph>
    {
        public PolicyUpdateRequestGraphType(IDataLoaderContextAccessor accessor, IAuthService authService, PermissionValidator permissionValidator)
        {
            Name = "policyUpdateRequest";
            Description = "A policy update request";

            Field(r => r.Id);
            Field(r => r.PolicyId);
            Field(r => r.ApprovalStatus, nullable: true);

            Field(r => r.UpdatePolicyCommands, type: typeof(ListGraphType<UpdatePolicyGraphType>), nullable: true);
            Field(r => r.UpdatePaymentInfoCommands, type: typeof(ListGraphType<UpdatePaymentInfoGraphType>), nullable: true);
            Field(r => r.UpdateBeneficiaryEligibilityCommands, type: typeof(ListGraphType<UpdateBenefificiaryEligibilityGraphType>), nullable: true);
            Field(r => r.UpsertBenefitOptionCommands, type: typeof(ListGraphType<UpsertBenefitOptionGraphType>), nullable: true);

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class PolicyUpdateRequestsGraphType : ObjectGraphType<PolicyUpdateRequestsGraph>
    {
        public PolicyUpdateRequestsGraphType()
        {
            Name = "policyUpdateRequests";
            Description = "All policy update request";

            Field(c => c.TotalCount);
            Field(c => c.List, type: typeof(ListGraphType<PolicyUpdateRequestGraphType>));
        }
    }

    public class PolicyUpdateRequestsGraph
    {
        public int TotalCount { get; set; }
        public IEnumerable<PolicyUpdateRequestGraph> List { get; set; }
    }

    public class UpdatePolicyCommandGraph
    {
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public string IssuerNumber { get; set; }
        public bool IsIssuerNumberChanged { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }

        public string HolderId { get; set; }

        public bool IsHolderIdChanged { get; set; }

        public List<string> InsuredIds { get; set; }

        public bool IsInsuredIdsChanged { get; set; }
        public UpdateContractIndividualCommandGraph ContractHolderIndividual { get; set; }
        public bool IsContractHolderIndividualChanged { get; set; }
        public UpdateContractCompanyCommandGraph ContractHolderCompany { get; set; }
        public bool IsContractHolderCompanyChanged { get; set; }
        public DateTime? IssueDate { get; set; }
        public bool IsIssueDateChanged { get; set; }
        public DateTime? StartDate { get; set; }
        public bool IsStartDateChanged { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsEndDateChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string ReferralCode { get; set; }
        public bool IsReferralCodeChanged { get; set; }
        public List<KeyScalarValue> Values { get; set; }
        public bool IsValuesChanged { get; set; }
        public string Source { get; set; }
        public bool IsSourceChanged { get; set; }
        public PremiumInput Premium { get; set; }
        public bool IsPremiumChanged { get; set; }
        public ProductIdToUpdate ProductId { get; set; }
        public bool IsProductIdChanged { get; set; }
        public LoginGraph ModifiedBy { get; set; }

        public static UpdatePolicyCommandGraph ToGraph(UpdatePolicyCommand command)
            => new()
            {
                CommandId = command.CommandId,
                Timestamp = command.Timestamp,
                IssuerNumber = command.IssuerNumber,
                IsIssuerNumberChanged = command.IsIssuerNumberChanged,
                Status = command.Status,
                IsStatusChanged = command.IsStatusChanged,

                HolderId = command.HolderId,
                IsHolderIdChanged = command.IsHolderIdChanged,
                InsuredIds = command.InsuredIds,
                IsInsuredIdsChanged = command.IsInsuredIdsChanged,

                ContractHolderIndividual = command.ContractHolderIndividual != null ? UpdateContractIndividualCommandGraph.ToGraph(command.ContractHolderIndividual) : null,
                IsContractHolderIndividualChanged = command.IsContractHolderIndividualChanged,
                ContractHolderCompany = command.ContractHolderCompany != null ? UpdateContractCompanyCommandGraph.ToGraph(command.ContractHolderCompany) : null,
                IsContractHolderCompanyChanged = command.IsContractHolderCompanyChanged,

                IssueDate = command.IssueDate,
                IsIssueDateChanged = command.IsIssueDateChanged,
                StartDate = command.StartDate,
                IsStartDateChanged = command.IsStartDateChanged,
                EndDate = command.EndDate,
                IsEndDateChanged = command.IsEndDateChanged,
                Description = command.Description,
                IsDescriptionChanged = command.IsDescriptionChanged,
                ReferralCode = command.ReferralCode,
                IsReferralCodeChanged = command.IsReferralCodeChanged,
                Values = command.Values?.Children<JProperty>().Select(p => new KeyScalarValue { Key = p.Name, Value = p.Value?.ToScalarValue() }).ToList(),
                IsValuesChanged = command.IsValuesChanged,
                Source = command.Source,
                IsSourceChanged = command.IsSourceChanged,
                Premium = command.Premium,
                IsPremiumChanged = command.IsPremiumChanged,
                ProductId = command.ProductId,
                IsProductIdChanged = command.IsProductIdChanged,
                ModifiedBy = new LoginGraph { Id = command.ModifiedById }
            };
    }

    public class UpdatePolicyGraphType : ObjectGraphType<UpdatePolicyCommandGraph>
    {
        public UpdatePolicyGraphType(
                IDataLoaderContextAccessor accessor,
            IAuthService authService,
                PermissionValidator permissionValidator)
        {
            Name = "updatePolicyGraphType";
            Description = "initializes or updates a policy";

            Field(p => p.CommandId, nullable: true);
            Field(p => p.Timestamp, type: typeof(DateTimeGraphType));

            Field(p => p.HolderId, nullable: true).DeprecationReason("use the specific entity field updates instead");
            Field(p => p.IsHolderIdChanged).DeprecationReason("use the specific entity field updates instead");
            Field(p => p.InsuredIds, nullable: true).DeprecationReason("use the specific entity field updates instead");
            Field(p => p.IsInsuredIdsChanged).DeprecationReason("use the specific entity field updates instead");

            Field(p => p.ContractHolderIndividual, type: typeof(UpdateContractIndividualCommandGraphType));
            Field(p => p.IsContractHolderIndividualChanged);
            Field(p => p.ContractHolderCompany, type: typeof(UpdateContractCompanyCommandGraphType));
            Field(p => p.IsContractHolderCompanyChanged);

            Field(p => p.IssueDate, type: typeof(DateGraphType), nullable: true);
            Field(p => p.IsIssueDateChanged);
            Field(p => p.StartDate, type: typeof(DateGraphType), nullable: true);
            Field(p => p.IsStartDateChanged);
            Field(p => p.EndDate, type: typeof(DateGraphType), nullable: true);
            Field(p => p.IsEndDateChanged);
            Field(p => p.IssuerNumber, nullable: true);
            Field(p => p.IsIssuerNumberChanged);
            Field(p => p.Status, nullable: true);
            Field(p => p.IsStatusChanged);
            Field(p => p.Description, nullable: true);
            Field(p => p.IsDescriptionChanged);
            Field(p => p.ReferralCode, nullable: true);
            Field(p => p.IsReferralCodeChanged);
            Field(p => p.Source, nullable: true);
            Field(p => p.IsSourceChanged);
            Field(p => p.Values, type: typeof(ListGraphType<KeyValueGraphType>));
            Field(p => p.IsValuesChanged);
            Field(p => p.Premium, type: typeof(PremiumToUpdateGraphType), nullable: true);
            Field(p => p.IsPremiumChanged);
            Field(p => p.ProductId, type: typeof(ProductIdToUpdateGraphType), nullable: true);
            Field(p => p.IsProductIdChanged);
            Field(p => p.ModifiedBy, type: typeof(LoginGraphType))
                  .ResolveAsync(async context =>
                  {
                      if (context.Source.ModifiedBy?.Id == null)
                          return null;

                      var allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,
                          "readLogins", new List<string> { context.Source.ModifiedBy.Id });
                      if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.ModifiedBy.Id))
                          return null;

                      string tenantId = context.GetTenantIdFromToken();

                      var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                      i => authService.GetLoginsDictionaryAsync(tenantId, i));

                      Login loginDao = await loginLoader.LoadAsync(context.Source.ModifiedBy.Id);

                      return LoginGraph.ToGraph(loginDao);
                  });
        }
    }

    public class UpsertBenefitOptionGraphType : ObjectGraphType<BenefitOptionInputGraph>
    {
        public UpsertBenefitOptionGraphType()
        {
            Name = "upsertBenefitOption";
            Description = "Chosen benefit option";

            Field(o => o.OfferId, nullable: true);
            Field(o => o.TypeId);
            Field(o => o.Key, nullable: true).Description("Input the key if the benefit has a set of options");
            Field(o => o.Value, type: typeof(ScalarValueGraphType), nullable: true);
        }
    }

    public class UpsertBenefitOptionCommandGraph
    {
        public string CommandId { get; set; }
        public DateTime Timestamp { get; set; } // for endorsements
        public string TypeId { get; set; }
        public string Key { get; set; }
        public ScalarValue Value { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public string InsuredId { get; set; }
        public LoginGraph UpsertedBy { get; set; }

        public static UpsertBenefitOptionCommandGraph ToGraph(UpsertBenefitOptionCommand command)
            => new()
            {
                CommandId = command.CommandId,
                Timestamp = command.Timestamp,
                TypeId = command.TypeId,
                Key = command.Key,
                Value = command.Value?.ToScalarValue(),
                UpsertedBy = new LoginGraph { Id = command.UpsertedById }
            };
    }

    public class UpsertBenefitOptionCommandGraphType : ObjectGraphType<UpsertBenefitOptionCommandGraph>
    {
        public UpsertBenefitOptionCommandGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "upsertBenefitOptionCommand";

            Field(c => c.CommandId, nullable: true);
            Field(p => p.Timestamp, type: typeof(DateTimeGraphType));
            Field(c => c.TypeId, nullable: true);
            Field(c => c.Key, nullable: true);
            Field(c => c.Value, type: typeof(ScalarValueGraphType));
            Field(c => c.InsuredId, nullable: true);
            Field(a => a.UpsertedBy, type: typeof(LoginGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.UpsertedBy?.Id == null)
                        return null;

                    var allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,
                        "readLogins", new List<string> { context.Source.UpsertedBy.Id });
                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.UpsertedBy.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                    i => authService.GetLoginsDictionaryAsync(tenantId, i));

                    Login loginDao = await loginLoader.LoadAsync(context.Source.UpsertedBy.Id);

                    return LoginGraph.ToGraph(loginDao);
                });
        }
    }

    public class RemoveBenefitOptionCommandGraph
    {
        public string CommandId { get; set; }
        public string EndorsementId { get; set; }
        public DateTime Timestamp { get; set; }
        public string TypeId { get; set; }
        public LoginGraph RemovedBy { get; set; }

        public static RemoveBenefitOptionCommandGraph ToGraph(RemoveBenefitOptionCommand command)
           => new()
           {
               CommandId = command.CommandId,
               Timestamp = command.Timestamp,
               EndorsementId = command.EndorsementId,
               TypeId = command.TypeId,
               RemovedBy = new LoginGraph { Id = command.RemovedById }
           };
    }

    public class RemoveBenefitOptionCommandGraphType : ObjectGraphType<RemoveBenefitOptionCommandGraph>
    {
        public RemoveBenefitOptionCommandGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "removeBenefitOptionCommand";

            Field(c => c.CommandId, nullable: true);
            Field(c => c.EndorsementId, nullable: true);
            Field(p => p.Timestamp, type: typeof(DateTimeGraphType));
            Field(c => c.TypeId, nullable: true);
            Field(a => a.RemovedBy, type: typeof(LoginGraphType))
             .ResolveAsync(async context =>
             {
                 if (context.Source.RemovedBy?.Id == null)
                     return null;

                 var allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,
                     "readLogins", new List<string> { context.Source.RemovedBy.Id });
                 if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.RemovedBy.Id))
                     return null;

                 string tenantId = context.GetTenantIdFromToken();

                 var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                 i => authService.GetLoginsDictionaryAsync(tenantId, i));

                 Login loginDao = await loginLoader.LoadAsync(context.Source.RemovedBy.Id);

                 return LoginGraph.ToGraph(loginDao);
             });
        }
    }

    public class ClientGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class ClientGraphType : ObjectGraphType<ClientGraph>
    {
        public ClientGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService)
        {
            Name = "client";

            Field(c => c.Id, type: typeof(IdGraphType));

            Field(c => c.Name, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"clients-{context.Source.Id}-name"));
            Field(c => c.Description, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"clients-{context.Source.Id}-description"));
        }
    }

    public class InitializeTenantPoliciesInputGraphType : InputObjectGraphType<InitializeTenantPoliciesCommand>
    {
        public InitializeTenantPoliciesInputGraphType()
        {
            Name = "initializeTenantPoliciesInput";

            Field(p => p.PolicyNumberConfigs, type: typeof(NonNullGraphType<ListGraphType<AddPolicyNumberConfigInputGraphType>>));
        }
    }

    public class AddPolicyNumberConfigInputGraphType : InputObjectGraphType<AddPolicyNumberConfigCommand>
    {
        public AddPolicyNumberConfigInputGraphType()
        {
            Name = "addPolicyNumberConfigInput";

            Field(c => c.IssuerId);
            Field(c => c.PolicyNumber, nullable: true);
        }
    }

    public class PolicyJacketInstanceCommandBatchInputGraph
    {
        public List<AddJacketInstanceCommand> AddJacketInstanceInputs { get; set; }
        public List<UpdateJacketInstanceCommand> UpdateJacketInstanceInputs { get; set; }
    }

    public class PolicyJacketInstanceCommandBatchInputGraphType : InputObjectGraphType<PolicyJacketInstanceCommandBatchInputGraph>
    {
        public PolicyJacketInstanceCommandBatchInputGraphType()
        {
            Name = "policyJacketInstanceBatchInput";

            Field(b => b.AddJacketInstanceInputs, type: typeof(ListGraphType<AddPolicyJacketInstanceInputGraphType>), nullable: true);
            Field(b => b.UpdateJacketInstanceInputs, type: typeof(ListGraphType<UpdatePolicyJacketInstanceInputGraphType>), nullable: true);
        }
    }

    public class AddPolicyJacketInstanceInputGraphType : InputObjectGraphType<AddJacketInstanceCommand>
    {
        public AddPolicyJacketInstanceInputGraphType()
        {
            Name = "addPolicyJacketInstanceInput";
            Description = "instance of jacket to add into policy";

            Field(c => c.JacketId);
            Field(c => c.StoreJacketByValue, nullable: true);
            Field(c => c.Order, nullable: true);

        }
    }

    public class UpdatePolicyJacketInstanceInputGraphType : InputObjectGraphType<UpdateJacketInstanceCommand>
    {
        public UpdatePolicyJacketInstanceInputGraphType()
        {
            Name = "updatePolicyJacketInstanceInput";
            Description = "instance of jacket to update from policy";

            Field(c => c.InstanceId);
            Field(c => c.Order, nullable: true);
        }
    }

    public class PolicyClauseCommandBatchInput
    {
        public List<AddClauseCommand> AddClauseInputs { get; set; }
        public List<UpdateClauseCommand> UpdateClauseInputs { get; set; }
        public List<RemoveClauseCommand> RemoveClauseInputs { get; set; }
    }

    public class PolicyClauseCommandBatchInputGraphType : InputObjectGraphType<PolicyClauseCommandBatchInput>
    {
        public PolicyClauseCommandBatchInputGraphType()
        {
            Name = "policyClauseBatchInput";

            Field(b => b.AddClauseInputs, type: typeof(ListGraphType<AddClauseToPolicyInputGraphType>), nullable: true);
            Field(b => b.UpdateClauseInputs, type: typeof(ListGraphType<UpdateClauseOnPolicyInputGraphType>), nullable: true);
            Field(b => b.RemoveClauseInputs, type: typeof(ListGraphType<RemoveClauseFromPolicyInputGraphType>), nullable: true);
        }
    }

    public class AddClauseToPolicyInputGraphType : InputObjectGraphType<AddClauseCommand>
    {
        public AddClauseToPolicyInputGraphType()
        {
            Name = "addClauseToPolicyInput";

            Field(c => c.EndorsementId, nullable: true);
            Field(c => c.CommandId, nullable: true);
            Field(c => c.Timestamp, nullable: true);

            Field(c => c.Order, nullable: true);
            Field(c => c.Type, nullable: true);
            Field(c => c.TemplateId, nullable: true);
            Field(c => c.HtmlOverride, nullable: true);
            Field(c => c.StoreTemplateByValue, nullable: true);
            Field(c => c.RenderParameters, type: typeof(RenderParametersInputGraphType), nullable: true);
        }
    }

    public class UpdateClauseOnPolicyInputGraphType : InputObjectGraphType<UpdateClauseCommand>
    {
        public UpdateClauseOnPolicyInputGraphType()
        {
            Name = "updateClauseOnPolicyInput";

            Field(c => c.ClauseId);
            Field(c => c.Order, nullable: true);
            Field(c => c.Type, nullable: true);
            Field(c => c.TemplateId, nullable: true);
            Field(c => c.HtmlOverride, nullable: true);
            Field(c => c.RenderParameters, type: typeof(RenderParametersInputGraphType));
            Field(c => c.IsTypeChanged, nullable: true);
            Field(c => c.IsOrderChanged);
            Field(c => c.IsHtmlOverrideChanged, nullable: true);
            Field(c => c.IsTemplateIdChanged, nullable: true);
            Field(c => c.StoreTemplateByValue, nullable: true);
            Field(c => c.IsRenderParametersChanged, nullable: true);
        }
    }

    public class RemoveClauseFromPolicyInputGraphType : InputObjectGraphType<RemoveClauseCommand>
    {
        public RemoveClauseFromPolicyInputGraphType()
        {
            Name = "removeClauseFromPolicyInput";
            Field(c => c.ClauseId);
        }
    }

    public class PolicyDebitNoteNumberGraph
    {
        public string PolicyId { get; set; }
        public string DebitNoteNumber { get; set; }
    }

    public class PolicyDebitNoteNumberGraphType : ObjectGraphType<PolicyDebitNoteNumberGraph>
    {
        public PolicyDebitNoteNumberGraphType()
        {
            Name = "policyDebitNoteNumber";

            Field(x => x.PolicyId);
            Field(x => x.DebitNoteNumber);
        }
    }

    public class PolicyMemberMovementNumberGraph
    {
        public string PolicyId { get; set; }
        public string MemberMovementNumber { get; set; }
    }

    public class PolicyMemberMovementNumberGraphType : ObjectGraphType<PolicyMemberMovementNumberGraph>
    {
        public PolicyMemberMovementNumberGraphType()
        {
            Name = "policyMemberMovementNumber";

            Field(x => x.PolicyId);
            Field(x => x.MemberMovementNumber);
        }
    }

    public class PolicyGeneratedFromInputGraph
    {
        public string CaseId { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
    }

    public class PolicyGeneratedFromInputGraphType : InputObjectGraphType<PolicyGeneratedFromInputGraph>
    {
        public PolicyGeneratedFromInputGraphType()
        {
            Name = "policyGeneratedFromInput";

            Field(x => x.CaseId);
            Field(x => x.OfferId);
            Field(x => x.ProposalId);
        }
    }

    public class InstallmentStatusGraphType : EnumerationGraphType<PolicyInstallmentStatus>
    {
        public InstallmentStatusGraphType()
        {
            Name = "InstallmentStatus";
            Description = "The status of policy installments";
        }
    }

    public class PolicyCommissionGraphType : ObjectGraphType<CoverGo.Policies.Client.PolicyCommission>
    {
        public PolicyCommissionGraphType()
        {
            Name = "policyCommission";
            Description = "The commission of a policy";
            Field(c => c.DistributorID, nullable: true);
            Field(c => c.CommissionID, nullable: true);
            Field(c => c.CampaignCodes, type: typeof(ListGraphType<StringGraphType>), nullable: true);
        }
    }

    public class ExternalPolicyNumberWhereInputGraphType : InputObjectGraphType<ExternalPolicyNumberWhere>
    {
        public ExternalPolicyNumberWhereInputGraphType()
        {
            Name = "externalPolicyNumberWhere";
            Description = "A external policy number filter";

            Field(a => a.Value, nullable: true);
            Field(a => a.Value_in, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            Field(a => a.Type, nullable: true);
            Field(a => a.Type_in, type: typeof(ListGraphType<StringGraphType>), nullable: true);
        }
    }

    public class ExternalPolicyStatusWhereInputGraphType : InputObjectGraphType<ExternalPolicyStatusWhere>
    {
        public ExternalPolicyStatusWhereInputGraphType()
        {
            Name = "externalPolicyStatusWhere";
            Description = "A external policy status filter";

            Field(a => a.Value, nullable: true);
            Field(a => a.Type, nullable: true);
        }
    }
}