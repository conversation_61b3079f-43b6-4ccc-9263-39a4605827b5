using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Clients;

namespace CoverGo.Gateway.Interfaces.Policies
{
    public class CoverGoPolicyUnderwritingsService
    {
        private readonly HttpClient _client;
        public CoverGoPolicyUnderwritingsService(HttpClient client)
        {
            _client = client;
        }

        public Task<List<PolicyUnderwriting>> Query(string tenantId, PolicyUnderwritingWhere where, CancellationToken cancellationToken)
        {
            return _client.GenericPostAsync<List<PolicyUnderwriting>, PolicyUnderwritingWhere>($"{tenantId}/api/v1/policies/members/underwriting/query", where, cancellationToken);
        }

        public Task<long> Count(string tenantId, PolicyUnderwritingWhere where, CancellationToken cancellationToken)
        {
            return _client.GenericPostAsync<long, PolicyUnderwritingWhere>($"{tenantId}/api/v1/policies/members/underwriting/count", where, cancellationToken);
        }
    }
}