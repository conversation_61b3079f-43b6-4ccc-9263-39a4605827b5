﻿using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;

using GraphQL.DataLoader;
using GraphQL.Types;

using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Interfaces.Policies
{
    public class PaymentInfoGraphType : ObjectGraphType<PaymentInfoGraph>
    {
        public PaymentInfoGraphType(
            IDataLoaderContextAccessor accessor,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "paymentInfo";
            Description = "A payment info";

            Field(p => p.Id);
            Field(p => p.Amount, nullable: true);
            Field(p => p.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(p => p.FormattedPayment, nullable: true)
                .Resolve(context =>
                    context.Source.Amount != null
                        ? ((context.Source.CurrencyCode != CurrencyCode.Undefined ? context.Source.CurrencyCode.ToString() : "") + context.Source.Amount.GetValueOrDefault().ToString("n2"))
                        : null);
            Field(p => p.Name, nullable: true);
            Field(p => p.Comment, nullable: true);
            Field(p => p.StartDate, nullable: true);
            Field(p => p.EndDate, nullable: true);
            Field(p => p.Frequency, type: typeof(PaymentFrequencyEnumerationGraphType));
            Field(p => p.Method, type: typeof(PaymentMethodEnumerationGraphType));
            Field(p => p.Payor, type: typeof(EntityInterfaceGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.Payor?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var individualLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                        i => individualService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                    var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>("GetCompanies",
                        i => companyService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                    Task<Individual> individualDtoTask = individualLoader.LoadAsync(context.Source.Payor.Id);
                    Task<Company> companyDtoTask = companyLoader.LoadAsync(context.Source.Payor.Id);
                    await Task.WhenAll(individualDtoTask, companyDtoTask);

                    return individualDtoTask.Result?.ToGraph() ?? companyDtoTask.Result?.ToGraph();
                });

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class UpdatePaymentInfoGraphType : ObjectGraphType<UpdatePaymentInfoCommand>
    {
        public UpdatePaymentInfoGraphType()
        {
            Name = "updatePaymentInfo";
            Description = "the payment information to update";

            Field(p => p.Name, nullable: true);
            Field(p => p.IsNameChanged);
            Field(p => p.StartDate, type: typeof(DateTimeGraphType), nullable: true);
            Field(p => p.IsStartDateChanged);
            Field(p => p.EndDate, type: typeof(DateTimeGraphType), nullable: true);
            Field(p => p.IsEndDateChanged);
            Field(p => p.Amount, nullable: true);
            Field(p => p.IsAmountChanged);
            Field(p => p.Method, type: typeof(PaymentMethodEnumerationGraphType), nullable: true);
            Field(p => p.IsMethodChanged);
            Field(p => p.Frequency, type: typeof(PaymentFrequencyEnumerationGraphType), nullable: true);
            Field(p => p.IsFrequencyChanged);
            Field(p => p.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType), nullable: true);
            Field(p => p.IsCurrencyCodeChanged);
            Field(p => p.Comment, nullable: true);
            Field(p => p.IsCommentChanged);
            Field(p => p.PayorId, nullable: true);
            Field(p => p.IsPayorIdChanged);
        }
    }

    public class PaymentInfoToUpdateGraphType : InputObjectGraphType<UpdatePaymentInfoCommand>
    {
        public PaymentInfoToUpdateGraphType()
        {
            Name = "paymentInfosToUpdate";
            Description = "the payment information to update";

            Field(p => p.Name, nullable: true);
            Field(p => p.StartDate, type: typeof(DateTimeGraphType), nullable: true);
            Field(p => p.EndDate, type: typeof(DateTimeGraphType), nullable: true);
            Field(p => p.Amount, nullable: true);
            Field(p => p.Method, type: typeof(PaymentMethodEnumerationGraphType), nullable: true);
            Field(p => p.Frequency, type: typeof(PaymentFrequencyEnumerationGraphType), nullable: true);
            Field(p => p.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType), nullable: true);
            Field(p => p.Comment, nullable: true);
            Field(p => p.PayorId, nullable: true);
        }
    }
}
