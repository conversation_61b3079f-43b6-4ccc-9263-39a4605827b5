﻿using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Interfaces.Products;

using GraphQL.DataLoader;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Policies
{
    public class ExclusionInputGraphType : InputObjectGraphType<AddExclusionCommand>
    {
        public ExclusionInputGraphType()
        {
            Name = "exclusionInput";
            Description = "The exclusion input";

            Field(e => e.OfferId, nullable: true);
            Field(e => e.BenefitOptionKey, nullable: true);
            Field(e => e.BenefitParentTypeId, nullable: true);
            Field(e => e.BenefitTypeId, nullable: true);

            Field(e => e.Code, nullable: true);
            Field(e => e.Remark, nullable: true);
        }
    }

    public class ExclusionGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public CodeNameGraph CodeName { get; set; }
        public string BenefitParentTypeId { get; set; }
        public string BenefitTypeId { get; set; }
        public string BenefitOptionKey { get; set; }
        public string Remark { get; set; }
    }

    public class ExclusionGraphType : ObjectGraphType<ExclusionGraph>
    {
        public ExclusionGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "exclusion";

            Field(e => e.Id);
            Field(e => e.CodeName, type: typeof(ExclusionCodeNameGraphType), nullable: true);
            Field(e => e.BenefitOptionKey, nullable: true);
            Field(e => e.BenefitParentTypeId, nullable: true);
            Field(e => e.BenefitTypeId, nullable: true);
            Field(e => e.Remark, nullable: true);

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }
}
