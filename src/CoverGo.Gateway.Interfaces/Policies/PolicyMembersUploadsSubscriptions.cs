using System.Threading;
using System.Threading.Tasks;
using CoverGo.Gateway.Domain.Policies.MemberManagement.MemberUpload;
using HotChocolate;
using HotChocolate.Execution;
using HotChocolate.Subscriptions;
using HotChocolate.Types;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Interfaces.Policies;

[ExtendObjectType("Subscription")]
public class PolicyMembersUploads
{
    public async ValueTask<ISourceStream<JToken>> policies_policyMemberUploadRegisteredResolver(
        string tenantId,
        string policyId,
        [Service] ITopicEventReceiver receiver,
        CancellationToken cancellationToken)
        => await receiver.SubscribeAsync<string, JToken>(new PolicyMemberUploadTopic(tenantId, policyId, SubscribePolicyMemberUploadEvent.Registered).ToString(), cancellationToken);

    [Subscribe(With = nameof(policies_policyMemberUploadRegisteredResolver))]
    public PolicyMemberUploadPayload policies_policyMemberUploadRegistered(
        string tenantId,
        string policyId,
        [EventMessage] JToken message
    )
        => message.ToObject<PolicyMemberUploadRegisteredPayload>();

    public async ValueTask<ISourceStream<JToken>> policies_policyMemberUploadStartedValidatingResolver(
        string tenantId,
        string policyId,
        [Service] ITopicEventReceiver receiver,
        CancellationToken cancellationToken)
        => await receiver.SubscribeAsync<string, JToken>(new PolicyMemberUploadTopic(tenantId, policyId, SubscribePolicyMemberUploadEvent.StartedValidating).ToString(), cancellationToken);

    [Subscribe(With = nameof(policies_policyMemberUploadStartedValidatingResolver))]
    public PolicyMemberUploadPayload policies_policyMemberUploadStartedValidating(
        string tenantId,
        string policyId,
        [EventMessage] JToken message
    )
        => message.ToObject<PolicyMemberUploadStartedValidatingPayload>();

    public async ValueTask<ISourceStream<JToken>> policies_policyMemberUploadValidatedResolver(
        string tenantId,
        string policyId,
        [Service] ITopicEventReceiver receiver,
        CancellationToken cancellationToken)
        => await receiver.SubscribeAsync<string, JToken>(new PolicyMemberUploadTopic(tenantId, policyId, SubscribePolicyMemberUploadEvent.Validated).ToString(), cancellationToken);

    [Subscribe(With = nameof(policies_policyMemberUploadValidatedResolver))]
    public PolicyMemberUploadPayload policies_policyMemberUploadValidated(
        string tenantId,
        string policyId,
        [EventMessage] JToken message
    )
        => message.ToObject<PolicyMemberUploadValidatedPayload>();

    public async ValueTask<ISourceStream<JToken>> policies_policyMemberUploadStartedImportingResolver(
        string tenantId,
        string policyId,
        [Service] ITopicEventReceiver receiver,
        CancellationToken cancellationToken)
        => await receiver.SubscribeAsync<string, JToken>(new PolicyMemberUploadTopic(tenantId, policyId, SubscribePolicyMemberUploadEvent.StartedImporting).ToString(), cancellationToken);

    [Subscribe(With = nameof(policies_policyMemberUploadStartedImportingResolver))]
    public PolicyMemberUploadPayload policies_policyMemberUploadStartedImporting(
        string tenantId,
        string policyId,
        [EventMessage] JToken message
    )
        => message.ToObject<PolicyMemberUploadStartedImportingPayload>();

    public async ValueTask<ISourceStream<JToken>> policies_policyMemberUploadImportedResolver(
        string tenantId,
        string policyId,
        [Service] ITopicEventReceiver receiver,
        CancellationToken cancellationToken)
        => await receiver.SubscribeAsync<string, JToken>(new PolicyMemberUploadTopic(tenantId, policyId, SubscribePolicyMemberUploadEvent.Imported).ToString(), cancellationToken);

    [Subscribe(With = nameof(policies_policyMemberUploadImportedResolver))]
    public PolicyMemberUploadPayload policies_policyMemberUploadImported(
        string tenantId,
        string policyId,
        [EventMessage] JToken message
    )
        => message.ToObject<PolicyMemberUploadImportedPayload>();

    public async ValueTask<ISourceStream<JToken>> policies_policyMemberUploadStartedErrorRemovingResolver(
        string tenantId,
        string policyId,
        [Service] ITopicEventReceiver receiver,
        CancellationToken cancellationToken)
        => await receiver.SubscribeAsync<string, JToken>(new PolicyMemberUploadTopic(tenantId, policyId, SubscribePolicyMemberUploadEvent.StartedErrorRemoving).ToString(), cancellationToken);

    [Subscribe(With = nameof(policies_policyMemberUploadStartedErrorRemovingResolver))]
    public PolicyMemberUploadPayload policies_policyMemberUploadStartedErrorRemoving(
        string tenantId,
        string policyId,
        [EventMessage] JToken message
    )
        => message.ToObject<PolicyMemberUploadStartedErrorRemovingPayload>();

    public async ValueTask<ISourceStream<JToken>> policies_policyMemberUploadErrorRemovedResolver(
        string tenantId,
        string policyId,
        [Service] ITopicEventReceiver receiver,
        CancellationToken cancellationToken)
        => await receiver.SubscribeAsync<string, JToken>(new PolicyMemberUploadTopic(tenantId, policyId, SubscribePolicyMemberUploadEvent.ErrorRemoved).ToString(), cancellationToken);

    [Subscribe(With = nameof(policies_policyMemberUploadErrorRemovedResolver))]
    public PolicyMemberUploadPayload policies_policyMemberUploadErrorRemoved(
        string tenantId,
        string policyId,
        [EventMessage] JToken message
    )
        => message.ToObject<PolicyMemberUploadErrorRemovedPayload>();

    public async ValueTask<ISourceStream<JToken>> policies_policyMemberUploadStartedReversingResolver(
        string tenantId,
        string policyId,
        [Service] ITopicEventReceiver receiver,
        CancellationToken cancellationToken)
        => await receiver.SubscribeAsync<string, JToken>(new PolicyMemberUploadTopic(tenantId, policyId, SubscribePolicyMemberUploadEvent.StartedReversing).ToString(), cancellationToken);

    [Subscribe(With = nameof(policies_policyMemberUploadStartedReversingResolver))]
    public PolicyMemberUploadPayload policies_policyMemberUploadStartedReversing(
        string tenantId,
        string policyId,
        [EventMessage] JToken message
    )
        => message.ToObject<PolicyMemberUploadStartedReversingPayload>();

    public async ValueTask<ISourceStream<JToken>> policies_policyMemberUploadReversedResolver(
        string tenantId,
        string policyId,
        [Service] ITopicEventReceiver receiver,
        CancellationToken cancellationToken)
        => await receiver.SubscribeAsync<string, JToken>(new PolicyMemberUploadTopic(tenantId, policyId, SubscribePolicyMemberUploadEvent.Reversed).ToString(), cancellationToken);

    [Subscribe(With = nameof(policies_policyMemberUploadReversedResolver))]
    public PolicyMemberUploadPayload policies_policyMemberUploadReversed(
        string tenantId,
        string policyId,
        [EventMessage] JToken message
    )
        => message.ToObject<PolicyMemberUploadReversedPayload>();

    public async ValueTask<ISourceStream<JToken>> policies_policyMemberUploadStartedCancelingResolver(
        string tenantId,
        string policyId,
        [Service] ITopicEventReceiver receiver,
        CancellationToken cancellationToken)
        => await receiver.SubscribeAsync<string, JToken>(new PolicyMemberUploadTopic(tenantId, policyId, SubscribePolicyMemberUploadEvent.StartedCanceling).ToString(), cancellationToken);

    [Subscribe(With = nameof(policies_policyMemberUploadStartedCancelingResolver))]
    public PolicyMemberUploadPayload policies_policyMemberUploadStartedCanceling(
        string tenantId,
        string policyId,
        [EventMessage] JToken message
    )
        => message.ToObject<PolicyMemberUploadStartedCancelingPayload>();

    public async ValueTask<ISourceStream<JToken>> policies_policyMemberUploadCanceledResolver(
        string tenantId,
        string policyId,
        [Service] ITopicEventReceiver receiver,
        CancellationToken cancellationToken)
        => await receiver.SubscribeAsync<string, JToken>(new PolicyMemberUploadTopic(tenantId, policyId, SubscribePolicyMemberUploadEvent.Canceled).ToString(), cancellationToken);

    [Subscribe(With = nameof(policies_policyMemberUploadCanceledResolver))]
    public PolicyMemberUploadPayload policies_policyMemberUploadCanceled(
        string tenantId,
        string policyId,
        [EventMessage] JToken message
    )
        => message.ToObject<PolicyMemberUploadCanceledPayload>();

    public async ValueTask<ISourceStream<JToken>> policies_policyMemberUploadFailedResolver(
        string tenantId,
        string policyId,
        [Service] ITopicEventReceiver receiver,
        CancellationToken cancellationToken)
        => await receiver.SubscribeAsync<string, JToken>(new PolicyMemberUploadTopic(tenantId, policyId, SubscribePolicyMemberUploadEvent.Failed).ToString(), cancellationToken);

    [Subscribe(With = nameof(policies_policyMemberUploadFailedResolver))]
    public PolicyMemberUploadPayload policies_policyMemberUploadFailed(
        string tenantId,
        string policyId,
        [EventMessage] JToken message
    )
        => message.ToObject<PolicyMemberUploadFailedPayload>();
}