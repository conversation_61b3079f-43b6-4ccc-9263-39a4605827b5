﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.FileSystem;

using GraphQL.Types;
using System.Linq;

namespace CoverGo.Gateway.Interfaces.Files
{
    public class FileListingWithResultGraphType : ObjectGraphType<Result<ObjectListing>>
    {
        public FileListingWithResultGraphType()
        {
            Name = "fileListingWithResult";
            Description = "Lists files and files summaries with result";

            Field(f => f.Status, nullable: true);
            Field(f => f.Errors, type: typeof(ListGraphType<NonNullGraphType<StringGraphType>>));
            Field(f => f.Value, type: typeof(FileListingGraphType));
        }
    }

    public class FileListingGraphType : ObjectGraphType<ObjectListing>
    {
        public FileListingGraphType()
        {
            Name = "fileListing";
            Description = "Lists files and files summaries";

            Field(f => f.CommonPrefixes, nullable: true);
            Field(f => f.ObjectSummaries, type: typeof(ListGraphType<FileSummaryGraphType>));
            Field(f => f.ContinuationToken, nullable: true);
            Field(f => f.TotalCount, nullable: true);
        }
    }

    public class FileSummaryGraphType : ObjectGraphType<ObjectSummary>
    {
        public FileSummaryGraphType()
        {
            Name = "fileSummary";

            Field(f => f.Key);
            Field(f => f.FileName).Resolve(context => context.Source.Key.Split('/').Last());
            Field(f => f.Size);
            Field(f => f.IsPublic);
            Field(f => f.LastModifiedAt, type: typeof(DateTimeOffsetGraphType));
            Field(f => f.Metadata, type: typeof(ListGraphType<KeyValueGraphType>));
        }
    }

    public class FileWhereInputGraphType : InputObjectGraphType<FileWhere>
    {
        public FileWhereInputGraphType()
        {
            this.PopulateSystemWhereFields();

            Name = "fileWhere";
            Description = "a file filter";

            Field(w => w.KeyPattern, nullable: true);
        }
    }

    public class BucketGraphType : ObjectGraphType<FileSystemConfig>
    {
        public BucketGraphType()
        {
            Name = "buckets";

            Field(b => b.BucketName);
            Field(b => b.ProviderId, nullable: true);
            //Field(b => b.Endpoint, nullable: true);
            //Field(b => b.AccessKeyId, nullable: true);
            //Field(b => b.AccessKeySecret, nullable: true);
        }
    }

    public class BucketWhereInputGraphType : InputObjectGraphType<FileSystemConfigWhere>
    {
        public BucketWhereInputGraphType()
        {
            Name = "bucketWhereInput";

            Field(b => b.BucketName, nullable: true);
        }
    }

    public class CopyFileInputGraphType : InputObjectGraphType<CopyFileCommand>
    {
        public CopyFileInputGraphType()
        {
            Name = "copyFileInput";

            Field(f => f.Key);
            Field(f => f.NewKey);
        }
    }
}
