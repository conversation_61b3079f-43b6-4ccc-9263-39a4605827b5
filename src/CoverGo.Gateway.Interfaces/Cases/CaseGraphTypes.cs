using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Cms;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Auth;
using CoverGo.Gateway.Interfaces.Cms;
using CoverGo.Gateway.Interfaces.Notifications;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Templates;
using CoverGo.Gateway.Interfaces.Transactions;
using CoverGo.Gateway.Interfaces.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;
using GraphQL.DataLoader;
using GraphQL.Types;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Interfaces.Cases
{

    public class CasesGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<CaseGraph> List { get; set; }
        public CaseWhere Where { get; set; }
        public DateTime? AsOf { get; set; }
        public int? Skip { get; set; }
        public int? First { get; set; }
        public SortGraph Sort { get; set; }
    }

    public class CasesGraphType : ObjectGraphType<CasesGraph>
    {
        public CasesGraphType(
            ICaseService caseService,
            IAuthService authService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            PermissionValidator permissionValidator
            )
        {
            Name = "cases";
            Description = "Gets all cases";

            async Task<CaseWhere> AddInsuredNomadsProductTypeFilter(ResolveFieldContext<CasesGraph> context, CaseWhere where)
            {
                string tenantId = context.GetTenantIdFromToken();

                if (!tenantId.Contains("insuredNomads")) return where;

                var allowedProductTypes = await context.GetAllowedProductTypes(authService);

                if (allowedProductTypes == null || !allowedProductTypes.Any()) return where;

                return new CaseWhere
                {
                    And = new List<CaseWhere>
                           {
                               where,
                               new()
                               {
                                   FieldsWhere = new FieldsWhere
                                   {
                                       Path = "fields.productTypeId",
                                       Condition = FieldsWhereCondition.In,
                                       Value  = new ScalarValue{
                                           ArrayValue = allowedProductTypes.Select(x => new ScalarValue{
                                               StringValue = x
                                           }).ToList()
                                       }
                                   }
                               }
                           }
                };
            }

            Field(c => c.TotalCount)
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();

                   IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readCases");
                   CaseWhere where = allowedIds.Contains("all")
                       ? context.Source.Where ?? new CaseWhere()
                       : new CaseWhere
                       {
                           And = new List<CaseWhere>
                           {
                                context.Source.Where ?? new CaseWhere(),
                                new() { Id_in = allowedIds.ToList() }
                           }
                       };

                   await ReplaceHolderWhereWithHolderIdFilterAsync(tenantId, where, individualService, companyService);

                   where = await AddInsuredNomadsProductTypeFilter(context, where);

                   return await caseService.GetTotalCountAsync(tenantId, where);
               });

            Field(c => c.List, type: typeof(ListGraphType<CaseGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readCases");
                    CaseWhere where = allowedIds.Contains("all")
                        ? context.Source.Where ?? new CaseWhere()
                        : new CaseWhere
                        {
                            And = new List<CaseWhere>
                            {
                                context.Source.Where ?? new CaseWhere(),
                                new() { Id_in = allowedIds.ToList() }
                            }
                        };

                    int? skip = context.Source.Skip;
                    int? first = context.Source.First;
                    SortGraph sort = context.Source.Sort;
                    DateTime? asOf = context.Source.AsOf;

                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }
                    await ReplaceHolderWhereWithHolderIdFilterAsync(tenantId, where, individualService, companyService);

                    where = await AddInsuredNomadsProductTypeFilter(context, where);

                    var queryArguments = new QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy, AsOf = asOf };
                    IEnumerable<Case> cases = await caseService.GetAsync(tenantId, queryArguments);
                    return cases.Select(c => CaseGraph.ToGraph(c));
                });
        }

        private async Task ReplaceHolderWhereWithHolderIdFilterAsync(string tenantId,
            CaseWhere where,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService)
        {
            if (where.And != null)
                foreach (CaseWhere wh in where.And)
                    await ReplaceHolderWhereWithHolderIdFilterAsync(tenantId, wh, individualService, companyService);

            if (where.Or != null)
                foreach (CaseWhere wh in where.Or)
                    await ReplaceHolderWhereWithHolderIdFilterAsync(tenantId, wh, individualService, companyService);

            if (where.HolderIndividual != null)
            {
                IEnumerable<Entity> individuals = await individualService.GetAsync(tenantId, new QueryArguments { Where = where.HolderIndividual });
                where.HolderIndividual = null;
                where.HolderId_in = individuals?.Select(e => e.Id).ToList() ?? new List<string>();
            }

            else if (where.HolderCompany != null)
            {
                IEnumerable<Entity> companies = await companyService.GetAsync(tenantId, new QueryArguments { Where = where.HolderCompany });
                where.HolderCompany = null;
                where.HolderId_in = companies?.Select(e => e.Id).ToList() ?? new List<string>();
            }
        }
    }

    public class CaseGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string CaseNumber { get; set; }
        public string Source { get; set; }
        public string Status { get; set; }
        public EntityGraph Holder { get; set; }
        public IEnumerable<EntityGraph> OtherHolders { get; set; }
        public IEnumerable<EntityGraph> Insureds { get; set; }
        public IEnumerable<FactGraph> Facts { get; set; }
        public IEnumerable<NoteGraph> Notes { get; set; }
        public IEnumerable<ProposalGraph> Proposals { get; set; }
        public IEnumerable<StakeholderGraph> Stakeholders { get; set; }
        public List<CaseAgent> Agents { get; set; } = new List<CaseAgent>();
        public List<CaseHandler> Handlers { get; internal set; } = new List<CaseHandler>();
        public IEnumerable<BeneficiaryEligibilityGraph> BeneficiaryEligibilities { get; set; }
        public IEnumerable<PaymentInfoGraph> PaymentInfos { get; set; }
        public ComponentGraph Component { get; set; }
        public IEnumerable<EventLogGraph> Events { get; set; }
        public string Fields { get; set; }
        public DataSchemaGraph FieldsSchema { get; set; }
        public DataSchemaGraph WorkflowSchema { get; set; }
        public AccessPolicy? AccessPolicy { get; set; }
        public string ChannelId { get; set; }

        public static CaseGraph ToGraph(Case domain)
        {
            if (domain == null)
                return null;

            var graph = new CaseGraph
            {
                Id = domain.Id,
                Name = domain.Name,
                Description = domain.Description,
                Source = domain.Source,
                CaseNumber = domain.CaseNumber,
                Facts = domain.Facts?.Select(f => FactGraph.ToGraph(f)),
                Notes = domain.Notes?.Select(n => NoteGraph.ToGraph(n)),
                Proposals = domain.Proposals?.Select(p => ProposalGraph.ToGraph(p)),
                Holder = domain.HolderId != null ? new EntityGraph { Id = domain.HolderId } : null,
                OtherHolders = domain.OtherHolderIds?.Select(i => new EntityGraph { Id = i }) ?? new List<EntityGraph> { },
                Insureds = domain.InsuredIds?.Select(i => new EntityGraph { Id = i }) ?? new List<EntityGraph> { },
                Status = domain.Status,
                Stakeholders = domain.Stakeholders?.Select(i => StakeholderGraph.ToGraph(i)) ?? new List<StakeholderGraph> { },
                Agents = domain.Agents,
                Handlers = domain.Handlers,
                BeneficiaryEligibilities = domain.BeneficiaryEligibilities?.Select(b => BeneficiaryEligibilityGraph.ToGraph(b)),
                PaymentInfos = domain.PaymentInfos?.Select(p => PaymentInfoGraph.ToGraph(p)),

                Component = domain.ComponentId != null ? new ComponentGraph { Id = domain.ComponentId } : null,

                Fields = domain.Fields?.ToString(Formatting.None),

                FieldsSchema = domain.FieldsSchemaId != null ? new DataSchemaGraph { Id = domain.FieldsSchemaId } : null,
                WorkflowSchema = domain.WorkflowSchemaId != null ? new DataSchemaGraph { Id = domain.WorkflowSchemaId } : null,
                AccessPolicy = domain.AccessPolicy,
                ChannelId = domain.ChannelId
            };

            graph.PopulateSystemGraphFields(domain);

            return graph;
        }
    }

    public class CaseGraphType : ObjectGraphType<CaseGraph>
    {
        public CaseGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<CoverGo.Users.Domain.Objects.Object, CreateObjectCommand, UpdateObjectCommand> objectService,
            ICmsService cmsService,
            ICaseService caseService,
            PermissionValidator permissionValidator
            )
        {
            Name = "case";
            Description = "a case";

            Field(c => c.Id);
            Field(c => c.Name, nullable: true);
            Field(c => c.Description, nullable: true);
            Field(c => c.CaseNumber, nullable: true);
            Field(c => c.Source, nullable: true);
            Field(c => c.Status, nullable: true);
            Field(c => c.Facts, type: typeof(ListGraphType<FactGraphType>));
            Field(c => c.Notes, type: typeof(ListGraphType<NoteGraphType>));
            Field(c => c.Proposals, type: typeof(ListGraphType<ProposalGraphType>))
                .Argument<ProposalWhereInputGraphType>("where", "the proposal where input")
                .Resolve(context =>
                {
                    IEnumerable<ProposalGraph> proposals = context.Source.Proposals;
                    if (!proposals?.Any() ?? true)
                        return Enumerable.Empty<ProposalGraph>();

                    ProposalWhere where = context.GetArgument<ProposalWhere>("where");

                    if (where?.Id != null)
                    {
                        ProposalGraph proposal = proposals.FirstOrDefault(p => p.Id == where.Id);
                        return proposal != null ? new List<ProposalGraph> { proposal } : Enumerable.Empty<ProposalGraph>();
                    }

                    else if (where?.Id_in != null)
                        return proposals.Where(p => where.Id_in.Contains(p.Id)) ?? Enumerable.Empty<ProposalGraph>();

                    else if (where?.PolicyId_contains != null)
                        return proposals.Where(p => p.Policies?.Select(c => c.Id)?.Contains(where.PolicyId_contains) ?? false) ?? Enumerable.Empty<ProposalGraph>();

                    else if (where?.Name_contains != null)
                        return proposals.Where(p => p.Name.Contains(where.Name_contains));

                    else if (where?.Offers_contains?.Status != null)
                        return proposals.Where(p => p.Basket?.Any(o => o.Status == where?.Offers_contains.Status) == true);

                    return proposals;
                });
            Field(p => p.Holder, type: typeof(EntityInterfaceGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.Holder?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    IEnumerable<string> allowedIndividualIds = await permissionValidator.GetTargetIdsFromClaim(context, "readIndividuals");
                    var individualLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                        ids => individualService.GetDictionaryAsync(tenantId, allowedIndividualIds.Contains("all")
                            ? new IndividualWhere { Id_in = ids?.ToList() }
                            : new IndividualWhere
                            {
                                And = new List<IndividualWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedIndividualIds.ToList() }
                                }
                            }));

                    IEnumerable<string> allowedCompanyIds = await permissionValidator.GetTargetIdsFromClaim(context, "readCompanies");
                    var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>("GetCompanies",
                        ids => companyService.GetDictionaryAsync(tenantId, allowedCompanyIds.Contains("all")
                            ? new CompanyWhere { Id_in = ids?.ToList() }
                            : new CompanyWhere
                            {
                                And = new List<CompanyWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedCompanyIds.ToList() }
                                }
                            }));

                    Task<Individual> customerDtoTask = individualLoader.LoadAsync(context.Source.Holder.Id);
                    Task<Company> companyDtoTask = companyLoader.LoadAsync(context.Source.Holder.Id);
                    await Task.WhenAll(customerDtoTask, companyDtoTask);

                    return customerDtoTask.Result?.ToGraph() ?? companyDtoTask.Result?.ToGraph();
                });

            Field(p => p.OtherHolders, type: typeof(ListGraphType<EntityInterfaceGraphType>))
                .ResolveAsync(async context =>
                   {
                       if (!context.Source.OtherHolders?.Any() ?? true)
                           return new List<EntityGraph> { };

                       string tenantId = context.GetTenantIdFromToken();

                       IEnumerable<string> allowedIndividualIds = await permissionValidator.GetTargetIdsFromClaim(context, "readIndividuals");
                       var individualLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                           ids => individualService.GetDictionaryAsync(tenantId, allowedIndividualIds.Contains("all")
                            ? new IndividualWhere { Id_in = ids?.ToList() }
                            : new IndividualWhere
                            {
                                And = new List<IndividualWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedIndividualIds.ToList() }
                                }
                            }));

                       IEnumerable<string> allowedCompanyIds = await permissionValidator.GetTargetIdsFromClaim(context, "readCompanies");
                       var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>("GetCompanies",
                           ids => companyService.GetDictionaryAsync(tenantId, allowedCompanyIds.Contains("all")
                            ? new CompanyWhere { Id_in = ids?.ToList() }
                            : new CompanyWhere
                            {
                                And = new List<CompanyWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedCompanyIds.ToList() }
                                }
                            }));

                       IEnumerable<Task<Individual>> individualDtosTasks = context.Source.OtherHolders?.Select(i => individualLoader.LoadAsync(i.Id));
                       IEnumerable<Task<Company>> companyDtosTasks = context.Source.OtherHolders?.Select(i => companyLoader.LoadAsync(i.Id));

                       var individualCollectionTasks = Task.WhenAll(individualDtosTasks);
                       var companyCollectionTasks = Task.WhenAll(companyDtosTasks);

                       await Task.WhenAll(individualCollectionTasks, companyCollectionTasks);

                       var otherHolders = new List<Entity>();
                       otherHolders.AddRange(individualCollectionTasks?.Result?.Select(t => t)?.Where(t => t != null));
                       otherHolders.AddRange(companyCollectionTasks?.Result?.Select(t => t)?.Where(t => t != null));

                       return context.Source.OtherHolders?.Select(g => otherHolders?.FirstOrDefault(o => o?.Id == g?.Id)?.ToGraph())?.Where(e => e != null); //to preserve the order
                   });

            Field(p => p.Insureds, type: typeof(ListGraphType<EntityInterfaceGraphType>))
                .ResolveAsync(async context =>
                    {
                        if (!context.Source.Insureds?.Any() ?? true)
                            return new List<EntityGraph> { };

                        string tenantId = context.GetTenantIdFromToken();

                        IEnumerable<string> allowedIndividualIds = await permissionValidator.GetTargetIdsFromClaim(context, "readIndividuals");
                        var individualLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                            ids => individualService.GetDictionaryAsync(tenantId, allowedIndividualIds.Contains("all")
                            ? new IndividualWhere { Id_in = ids?.ToList() }
                            : new IndividualWhere
                            {
                                And = new List<IndividualWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedIndividualIds.ToList() }
                                }
                            }));

                        IEnumerable<string> allowedCompanyIds = await permissionValidator.GetTargetIdsFromClaim(context, "readCompanies");
                        var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>("GetCompanies",
                            ids => companyService.GetDictionaryAsync(tenantId, allowedCompanyIds.Contains("all")
                            ? new CompanyWhere { Id_in = ids?.ToList() }
                            : new CompanyWhere
                            {
                                And = new List<CompanyWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedCompanyIds.ToList() }
                                }
                            }));

                        IEnumerable<string> allowedObjectIds = await permissionValidator.GetTargetIdsFromClaim(context, "readObjects");
                        var objectLoader = accessor.Context.GetOrAddBatchLoader<string, CoverGo.Users.Domain.Objects.Object>("GetObjects",
                            ids => objectService.GetDictionaryAsync(tenantId, allowedObjectIds.Contains("all")
                            ? new ObjectWhere { Id_in = ids?.ToList() }
                            : new ObjectWhere
                            {
                                And = new List<ObjectWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedObjectIds.ToList() }
                                }
                            }));

                        IEnumerable<Task<Individual>> individualDtosTasks = context.Source.Insureds?.Where(i => i.Id != null)?.Select(i => individualLoader.LoadAsync(i.Id));
                        IEnumerable<Task<Company>> companyDtosTasks = context.Source.Insureds?.Where(i => i.Id != null)?.Select(i => companyLoader.LoadAsync(i.Id));
                        IEnumerable<Task<CoverGo.Users.Domain.Objects.Object>> objectDtosTasks = context.Source.Insureds?.Where(i => i.Id != null)?.Select(i => objectLoader.LoadAsync(i.Id));

                        var individualCollectionTasks = Task.WhenAll(individualDtosTasks);
                        var companyCollectionTasks = Task.WhenAll(companyDtosTasks);
                        var objectCollectionTasks = Task.WhenAll(objectDtosTasks);

                        await Task.WhenAll(individualCollectionTasks, companyCollectionTasks, objectCollectionTasks);

                        var insured = new List<Entity>();
                        insured.AddRange(individualCollectionTasks.Result?.Select(t => t)?.Where(t => t != null));
                        insured.AddRange(companyCollectionTasks.Result?.Select(t => t)?.Where(t => t != null));
                        insured.AddRange(objectCollectionTasks.Result?.Select(t => t)?.Where(t => t != null));

                        return context.Source.Insureds.Select(g => insured?.FirstOrDefault(i => i?.Id == g?.Id)?.ToGraph())?.Where(e => e != null); //to preserve the order
                    });

            Field(p => p.Stakeholders, type: typeof(ListGraphType<StakeholderGraphType>));
            Field(p => p.Agents, type: typeof(ListGraphType<CaseAgentGraphType>));
            Field(p => p.Handlers, type: typeof(ListGraphType<CaseHandlerGraphType>));


            Field(c => c.BeneficiaryEligibilities, type: typeof(ListGraphType<BeneficiaryEligibilityGraphType>))
                 .ResolveAsync(async context =>
                 {
                     if (context.Source.BeneficiaryEligibilities == null || context.Source.BeneficiaryEligibilities.Count() == 0)
                         return null;

                     string tenantId = context.GetTenantIdFromToken();

                     var individualLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                      i => individualService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                     var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>("GetCompanies",
                     i => companyService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                     IEnumerable<string> ids = context.Source.BeneficiaryEligibilities.Where(i => i.Entity != null).Select(i => i.Entity.Id);

                     IEnumerable<Task<Individual>> individualDtosTasks = ids.Select(i => individualLoader.LoadAsync(i));
                     IEnumerable<Task<Company>> companyDtosTasks = ids.Select(i => companyLoader.LoadAsync(i));

                     var individualCollectionTasks = Task.WhenAll(individualDtosTasks);
                     var companyCollectionTasks = Task.WhenAll(companyDtosTasks);
                     await Task.WhenAll(individualCollectionTasks, companyCollectionTasks);

                     var beneficiaries = new List<Entity>();
                     beneficiaries.AddRange(individualCollectionTasks.Result.Select(t => t).Where(t => t != null));
                     beneficiaries.AddRange(companyCollectionTasks.Result.Select(t => t).Where(t => t != null));

                     return context.Source.BeneficiaryEligibilities.Select(ben => new BeneficiaryEligibilityGraph
                     {
                         Id = ben.Id,
                         Entity = beneficiaries.FirstOrDefault(b => b.Id == ben?.Entity?.Id).ToGraph(),
                         Benefit = ben.Benefit,
                         IsRevocable = ben.IsRevocable,
                         Notes = ben.Notes,
                         Ratio = ben.Ratio
                     });
                 });

            Field(c => c.PaymentInfos, type: typeof(ListGraphType<PaymentInfoGraphType>));

            Field(p => p.Component, type: typeof(ComponentGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.Component?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    Component component = (await cmsService.GetComponentsAsync(tenantId, new QueryArguments { Where = new ComponentWhere { Id = context.Source.Component.Id } })).FirstOrDefault();

                    return ComponentGraph.ToGraph(component);
                });

            Field(c => c.Events, type: typeof(ListGraphType<DetailedEventLogGraphType>))
                  .ResolveAsync(async context =>
                  {
                      string tenantId = context.GetTenantIdFromToken();

                      var dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, DetailedEventLog>(
                          "GetCaseEvents",
                          async i => (await caseService.GetEventsAsync(tenantId, new EventQuery { Ids = i?.ToList() })).ToLookup(e => e.RelatedId)
                      );

                      IEnumerable<DetailedEventLog> logs = await dataLoader.LoadAsync(context.Source.Id);

                      return logs.Select(DetailedEventLogGraph.ToGraph);
                  });

            Field(c => c.Fields, nullable: true)
                .ResolveAsync(context => context.GetPermittedProjection(authService, accessor, context.Source.Fields, context.Source.Id, "case"));

            Field(c => c.FieldsSchema, type: typeof(DataSchemaGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.FieldsSchema?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var batchLoader = accessor.Context.GetOrAddBatchLoader<string, DataSchema>("GetDataSchemas",
                        async ids =>
                        {
                            IReadOnlyCollection<DataSchema> dataSchemas
                                = await caseService.GetDataSchemasAsync(tenantId, new DataSchemaWhere { Id_in = ids?.ToList() });
                            return ids?.Join(dataSchemas, i => i, ds => ds.Id, (i, ds) => (i, ds))
                                .ToDictionary(p => p.i, p => p.ds);
                        });

                    return DataSchemaGraph.ToGraph(await batchLoader.LoadAsync(context.Source.FieldsSchema.Id));
                });
            Field(c => c.WorkflowSchema, type: typeof(DataSchemaGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.WorkflowSchema?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var batchLoader = accessor.Context.GetOrAddBatchLoader<string, DataSchema>("GetDataSchemas",
                        async ids =>
                        {
                            IReadOnlyCollection<DataSchema> dataSchemas
                                = await caseService.GetDataSchemasAsync(tenantId, new DataSchemaWhere { Id_in = ids?.ToList() });
                            return ids?.Join(dataSchemas, i => i, ds => ds.Id, (i, ds) => (i, ds))
                                .ToDictionary(p => p.i, p => p.ds);
                        });

                    return DataSchemaGraph.ToGraph(await batchLoader.LoadAsync(context.Source.WorkflowSchema.Id));
                });
            Field(c => c.AccessPolicy, type: typeof(AccessPolicyTypeEnumGraphType), nullable: true);
            Field(c => c.ChannelId, nullable: true);

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class ProposalGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string ProposalNumber { get; set; }
        public string ReferralCode { get; set; }
        public bool IsIssued { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public LoginGraph IssuedBy { get; set; }
        public DateTime? IssuedAt { get; set; }
        public CaseGraph Case { get; set; }
        public IEnumerable<NoteGraph> Notes { get; set; }
        public IEnumerable<OfferGraph> Basket { get; set; }
        public PricingGraph TotalPrice { get; set; } // will override total price
        public IEnumerable<PolicyGraph> Policies { get; set; }
        public IEnumerable<TransactionGraph> Transactions { get; set; }
        public RenewalHistoryGraph RenewalHistory { get; set; }
        public IEnumerable<StakeholderGraph> Stakeholders { get; set; }
        public bool IsRejected { get; set; }
        public IEnumerable<CodeNameGraph> RejectionCodes { get; set; }
        public string RejectionRemarks { get; set; }
        public LoginGraph RejectedBy { get; set; }
        public bool IsAccepted { get; set; }
        public LoginGraph AcceptedBy { get; set; }
        public bool IsApprovalNeeded { get; set; }

        public static ProposalGraph ToGraph(Proposal domain)
        {
            return domain == null
                ? null
                : new ProposalGraph
                {
                    Id = domain.Id,

                    Name = domain.Name,
                    Description = domain.Description,
                    Status = domain.Status,
                    ProposalNumber = domain.ProposalNumber,
                    ReferralCode = domain.ReferralCode,
                    IsIssued = domain.IsIssued,
                    ExpiryDate = domain.ExpiryDate,
                    IssuedBy = GetIssuedBy(domain),
                    IssuedAt = domain.IssuedAt,
                    Case = GetCaseId(domain),
                    Basket = domain.Basket?.Select(o => OfferGraph.ToGraph(o)),
                    TotalPrice = PricingGraph.ToGraph(domain.TotalPrice, null),
                    Policies = domain.PolicyIds?.Select(p => new PolicyGraph { Id = p }),
                    RenewalHistory = RenewalHistoryGraph.ToGraph(domain.RenewalHistory),
                    Stakeholders = domain.Stakeholders?.Select(s => StakeholderGraph.ToGraph(s)),
                    IsRejected = domain.IsRejected,
                    RejectionCodes = domain.RejectionCodes?.Select(c => new CodeNameGraph { Code = c }),
                    RejectionRemarks = domain.RejectionRemarks,
                    RejectedBy = GetRejectedBy(domain),
                    IsAccepted = domain.IsAccepted,
                    AcceptedBy = GetAcceptedBy(domain),
                    IsApprovalNeeded = domain.IsApprovalNeeded
                }.PopulateSystemGraphFields(domain);

            static CaseGraph GetCaseId(Proposal domain)
            {
                return domain.CaseId != null ? new CaseGraph { Id = domain.CaseId } : null;
            }

            static LoginGraph GetIssuedBy(Proposal domain)
            {
                return domain.IssuedById != null ? new LoginGraph { Id = domain.IssuedById } : null;
            }

            static LoginGraph GetRejectedBy(Proposal domain)
            {
                return domain.RejectedById != null ? new LoginGraph { Id = domain.RejectedById } : null;
            }

            static LoginGraph GetAcceptedBy(Proposal domain)
            {
                return domain.AcceptedById != null ? new LoginGraph { Id = domain.AcceptedById } : null;
            }
        }
    }

    public class RenewalHistoryGraph
    {
        public ProposalGraph RenewedFrom { get; set; }
        public ProposalGraph RenewedTo { get; set; }
        public int RenewalCount { get; set; }

        public static RenewalHistoryGraph ToGraph(RenewalHistory rh) =>
            rh == null
                ? null
                : new RenewalHistoryGraph
                {
                    RenewedTo = rh.RenewedToId != null ? new ProposalGraph { Id = rh.RenewedToId } : null,
                    RenewedFrom = rh.RenewedFromId != null ? new ProposalGraph { Id = rh.RenewedFromId } : null,
                    RenewalCount = rh.RenewalCount
                };
    }

    public class ProposalGraphType : ObjectGraphType<ProposalGraph>
    {
        public ProposalGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            ICaseService caseService,
            ITransactionService transactionService,
            IPolicyService policyService,
            PermissionValidator permissionValidator)
        {
            Name = "proposal";
            Description = "a proposal";

            Field(p => p.Id);
            Field(p => p.Name, nullable: true);
            Field(p => p.ProposalNumber, nullable: true);
            Field(p => p.ReferralCode, nullable: true);
            Field(p => p.IsIssued);
            Field(p => p.ExpiryDate, nullable: true);
            Field(p => p.IssuedBy, type: typeof(LoginGraphType), nullable: true)
                 .ResolveAsync(async context =>
                 {
                     if (context.Source.IssuedBy?.Id == null)
                         return null;

                     IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readLogins", new List<string> { context.Source.IssuedBy.Id });
                     if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.IssuedBy.Id))
                         return null;

                     string tenantId = context.GetTenantIdFromToken();

                     var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                     i => authService.GetLoginsDictionaryAsync(tenantId, i));

                     Login loginDao = await loginLoader.LoadAsync(context.Source.IssuedBy.Id);

                     return LoginGraph.ToGraph(loginDao);
                 });
            Field(p => p.IssuedAt, type: typeof(DateTimeGraphType), nullable: true);
            Field(p => p.Status, nullable: true);
            Field(p => p.Description, nullable: true);
            Field(c => c.Notes, type: typeof(ListGraphType<NoteGraphType>));
            Field(p => p.Basket, type: typeof(ListGraphType<OfferGraphType>))
                .Argument<OfferWhereInputGraphType>("where", "the offer where input")
                .ResolveAsync(async context =>
                {
                    IEnumerable<OfferGraph> offers = context.Source.Basket;

                    if (!offers?.Any() ?? true)
                        return Enumerable.Empty<OfferGraph>();

                    OfferWhere where = context.GetArgument<OfferWhere>("where");
                    if (where != null)
                    {
                        if (where.Id != null)
                            offers = new List<OfferGraph> { offers.FirstOrDefault(o => o.Id == where.Id) };

                        if (where.Id_in != null)
                            offers = offers.Where(o => where.Id_in.Contains(o.Id));

                        if (where.OfferNumber != null)
                            offers = new List<OfferGraph> { offers.FirstOrDefault(o => o.OfferNumber == where.OfferNumber) };

                        if (where.OfferNumber_in != null)
                            offers = offers.Where(o => where.OfferNumber_in.Contains(o.OfferNumber));

                        if (where.Status != null)
                            offers = new List<OfferGraph> { offers.FirstOrDefault(o => o.Status == where.Status) };
                    }

                    List<string> allowedProductTypes = await context.GetAllowedProductTypes(authService);
                    if (allowedProductTypes?.Any() ?? false)
                    {
                        offers = offers.Where(x => allowedProductTypes.Contains(x?.Product?.ProductId?.Type)).ToArray();
                    }

                    return offers;
                });

            Field(p => p.TotalPrice, type: typeof(PricingGraphType));

            Field(p => p.Transactions, type: typeof(ListGraphType<TransactionGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readTransactions");

                    var loader = accessor.Context.GetOrAddCollectionBatchLoader<string, Transaction>(
                        "GetTransactionsFromProposalIds",
                        proposalIds => transactionService.GetByProposalIdsLookupAsync(tenantId, allowedIds.Contains("all")
                        ? new TransactionWhere { ProposalId_in = proposalIds?.ToList() }
                        : new TransactionWhere
                        {
                            And = new List<TransactionWhere>
                            {
                                new() { ProposalId_in = proposalIds?.ToList() },
                                new() { Id_in = allowedIds.ToList() }
                            }
                        }));

                    IEnumerable<Transaction> transactions = await loader.LoadAsync(context.Source.Id);

                    return transactions?.Select(l => TransactionGraph.ToGraph(l));
                });

            Field(p => p.Policies, type: typeof(ListGraphType<PolicyGraphType>))
                .Argument<PolicyWhereInputGraphType>("where", "the policy where input")
                .ResolveAsync(async context =>
                {
                    if (!context.Source.Policies?.Any() ?? true)
                        return Enumerable.Empty<PolicyGraph>();

                    string tenantId = context.GetTenantIdFromToken();

                    IEnumerable<string> allowedPolicyIds = await permissionValidator.GetTargetIdsFromClaim(context, "readPolicies");

                    PolicyWhere argWhere = context.GetArgument<PolicyWhere>("where");

                    var policyLoader = accessor.Context.GetOrAddBatchLoader<string, Policy>("GetPolicies",
                     i =>
                     {
                         var whereList = new List<PolicyWhere> { };

                         if (argWhere != null)
                             whereList.Add(argWhere);

                         if (!allowedPolicyIds.Contains("all"))
                             whereList.Add(new PolicyWhere { Id_in = allowedPolicyIds.ToList() });

                         return policyService.GetDictionaryAsync(tenantId, whereList.Any()
                             ? new PolicyWhere { And = new List<PolicyWhere> { new() { Id_in = i?.ToList() } }.Concat(whereList).ToList() }
                             : new PolicyWhere { Id_in = i?.ToList() });
                     });

                    IEnumerable<Task<Policy>> tasks = context.Source.Policies.Where(s => s != null).Select(s => policyLoader.LoadAsync(s.Id));
                    Policy[] results = await Task.WhenAll(tasks);

                    return results.Where(r => r != null).Select(p => PolicyGraph.ToGraph(p));
                });

            Field(p => p.Case, type: typeof(CaseGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.Case?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readCases");

                    var dataLoader = accessor.Context.GetOrAddBatchLoader<string, Case>("GetCases",
                        async i =>
                        {
                            IEnumerable<Case> cases = await caseService.GetAsync(tenantId, new QueryArguments { Where = new CaseWhere { Id_in = i } });
                            return cases.ToDictionary(c => c.Id, c => c);
                        });

                    Case @case = await dataLoader.LoadAsync(context.Source.Case.Id);
                    return CaseGraph.ToGraph(@case);
                });

            Field(p => p.RenewalHistory, type: typeof(RenewalHistoryGraphType));

            Field(p => p.Stakeholders, type: typeof(ListGraphType<StakeholderGraphType>));

            Field(p => p.IsRejected);

            Field(p => p.RejectionCodes, type: typeof(ListGraphType<RejectionCodeNameGraphType>));

            Field(p => p.RejectionRemarks, nullable: true);

            Field(p => p.RejectedBy, type: typeof(LoginGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.RejectedBy?.Id == null)
                        return null;

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readLogins", new List<string> { context.Source.RejectedBy.Id });
                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.RejectedBy.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                    i => authService.GetLoginsDictionaryAsync(tenantId, i));

                    Login loginDao = await loginLoader.LoadAsync(context.Source.RejectedBy.Id);

                    return LoginGraph.ToGraph(loginDao);
                });

            Field(p => p.IsAccepted);

            Field(p => p.AcceptedBy, type: typeof(LoginGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.AcceptedBy?.Id == null)
                        return null;

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readLogins", new List<string> { context.Source.AcceptedBy.Id });
                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.AcceptedBy.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                    i => authService.GetLoginsDictionaryAsync(tenantId, i));

                    Login loginDao = await loginLoader.LoadAsync(context.Source.AcceptedBy.Id);

                    return LoginGraph.ToGraph(loginDao);
                });

            Field(p => p.IsApprovalNeeded);

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class CaseAgentGraphType : ObjectGraphType<CaseAgent>
    {
        public CaseAgentGraphType()
        {
            Name = "caseAgent";
            Description = "Assigned agent to a case.";

            Field(c => c.Id);
            Field(c => c.PortalUserId);
            Field(c => c.EntityId);
            Field(c => c.StakeholderId);
            Field(c => c.RoleType, type: typeof(RoleTypeEnumGraphType));
        }
    }

    public class CaseHandlerGraphType : ObjectGraphType<CaseHandler>
    {
        public CaseHandlerGraphType()
        {
            Name = "caseHandler";
            Description = "Assigned handler to a case.";

            Field(c => c.PortalUserId);
            Field(c => c.EntityId);
            Field(c => c.StakeholderId);
        }
    }

    public class RoleTypeEnumGraphType : EnumerationGraphType<CaseAgentRoleType>
    {
        public RoleTypeEnumGraphType()
        {
            Name = "caseAgentRoleType";
            Description = "Role of an assigned agent to a case.";
        }
    }

    public class RenewalHistoryGraphType : ObjectGraphType<RenewalHistoryGraph>
    {
        public RenewalHistoryGraphType(
            IDataLoaderContextAccessor accessor,
            ICaseService caseService,
            PermissionValidator permissionValidator)
        {
            Name = "renewalHistory";

            Field(rh => rh.RenewalCount);

            Field(rh => rh.RenewedTo, type: typeof(ProposalGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.RenewedTo?.Id == null)
                        return null;

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readProposals", new List<string> { context.Source.RenewedTo?.Id });
                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.RenewedTo?.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loader = accessor.Context.GetOrAddBatchLoader<string, Proposal>("GetProposalByRenewedId",
                        async i => (await caseService.GetAsync(tenantId, new QueryArguments { Where = new CaseWhere { Proposal = new ProposalWhere { Id_in = i } } }))
                            .SelectMany(c => c.Proposals)
                            .ToDictionary(pair => pair.Id, pair => pair));

                    Proposal domain = await loader.LoadAsync(context.Source.RenewedTo.Id);

                    return ProposalGraph.ToGraph(domain);
                });

            Field(rh => rh.RenewedFrom, type: typeof(ProposalGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.RenewedFrom?.Id == null)
                        return null;

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readProposals", new List<string> { context.Source.RenewedFrom?.Id });
                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.RenewedFrom?.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loader = accessor.Context.GetOrAddBatchLoader<string, Proposal>("GetProposalByRenewedId",
                        async i => (await caseService.GetAsync(tenantId, new QueryArguments { Where = new CaseWhere { Proposal = new ProposalWhere { Id_in = i } } }))
                            .SelectMany(c => c.Proposals)
                            .ToDictionary(pair => pair.Id, pair => pair));

                    Proposal domain = await loader.LoadAsync(context.Source.RenewedFrom.Id);

                    return ProposalGraph.ToGraph(domain);
                });
        }
    }

    public class CaseWhereInputGraphType : InputObjectGraphType<CaseWhereGraph>
    {
        public CaseWhereInputGraphType()
        {
            Name = "caseWhere";
            Description = "a case filter";

            Field(c => c.And, type: typeof(ListGraphType<CaseWhereInputGraphType>));
            Field(c => c.Or, type: typeof(ListGraphType<CaseWhereInputGraphType>));

            Field(f => f.Id, nullable: true);
            Field(f => f.Name_contains, nullable: true);
            Field(f => f.Id_in, nullable: true);
            Field(f => f.CaseNumber, nullable: true);
            Field(f => f.Status, nullable: true);
            Field(f => f.Source, nullable: true);
            Field(f => f.Source_contains, nullable: true);
            Field(f => f.Proposal, type: typeof(ProposalWhereInputGraphType));
            Field(f => f.Proposals_contains, type: typeof(ProposalWhereInputGraphType));
            Field(f => f.HolderId, nullable: true);
            Field(f => f.HolderId_in, nullable: true);
            Field(f => f.HolderIndividual, type: typeof(IndividualWhereInputGraphType));
            Field(f => f.HolderCompany, type: typeof(IndividualWhereInputGraphType));
            Field(f => f.Stakeholders_contains, type: typeof(StakeholderWhereInputGraphType));
            Field(f => f.Facts_contains, type: typeof(FactWhereInputGraphType));
            Field(f => f.Proposals_every, type: typeof(ProposalWhereInputGraphType), nullable: true);
            Field(f => f.Proposals_exist, type: typeof(BooleanGraphType), nullable: true);
            Field(f => f.CaseNumber_contains, nullable: true);
            Field(f => f.FieldsWhere, type: typeof(FieldsWhereInputGraphType), nullable: true);
            Field(f => f.HavingIssuedPolicies, nullable: true);
            Field(f => f.AccessPolicy, type: typeof(AccessPolicyTypeEnumGraphType), nullable: true);
            Field(f => f.ChannelId, nullable: true);

            this.PopulateSystemWhereFields();
        }
    }

    public class CaseWhereGraph : Where
    {
        public List<CaseWhereGraph> Or { get; set; }
        public List<CaseWhereGraph> And { get; set; }

        public string Id { get; set; }
        public string Name_contains { get; set; }
        public IEnumerable<string> Id_in { get; set; }
        public string CaseNumber { get; set; }
        public string Status { get; set; }
        public string Source { get; set; }
        public string Source_contains { get; set; }
        public ProposalWhere Proposal { get; set; }
        public ProposalWhere Proposals_contains { get; set; }
        public string HolderId { get; set; }
        public IEnumerable<string> HolderId_in { get; set; }
        public IndividualWhere HolderIndividual { get; set; }
        public CompanyWhere HolderCompany { get; set; }
        public StakeholderWhere Stakeholders_contains { get; set; }
        public FactWhereGraph Facts_contains { get; set; }
        public ProposalWhere Proposals_every { get; set; }
        public bool? Proposals_exist { get; set; }
        public string CaseNumber_contains { get; set; }
        public FieldsWhere FieldsWhere { get; set; }
        public bool? HavingIssuedPolicies { get; set; }
        public AccessPolicy? AccessPolicy { get; set; }
        public string ChannelId { get; set; }

        public static CaseWhere ToDomain(CaseWhereGraph graph) =>
        graph == null
        ? null :
        new CaseWhere
        {
            Or = graph.Or?.Select(o => ToDomain(o))?.ToList(),
            And = graph.And?.Select(a => ToDomain(a))?.ToList(),
            Id = graph.Id,
            Name_contains = graph.Name_contains,
            Id_in = graph.Id_in,
            CaseNumber = graph.CaseNumber,
            Status = graph.Status,
            Source = graph.Source,
            Source_contains = graph.Source_contains,
            Proposal = graph.Proposal,
            Proposals_contains = graph.Proposals_contains,
            HolderId = graph.HolderId,
            HolderId_in = graph.HolderId_in,
            HolderIndividual = graph.HolderIndividual,
            HolderCompany = graph.HolderCompany,
            Stakeholders_contains = graph.Stakeholders_contains,
            Facts_contains = FactWhereGraph.ToDomain(graph.Facts_contains),
            Proposals_every = graph.Proposals_every,
            Proposals_exist = graph.Proposals_exist,
            CaseNumber_contains = graph.CaseNumber_contains,
            CreatedAt_gt = graph.CreatedAt_gt,
            CreatedAt_lt = graph.CreatedAt_lt,
            LastModifiedAt_gt = graph.LastModifiedAt_gt,
            LastModifiedAt_lt = graph.LastModifiedAt_lt,
            CreatedById = graph.CreatedById,
            CreatedById_contains = graph.CreatedById_contains,
            CreatedById_in = graph.CreatedById_in,
            LastModifiedById = graph.LastModifiedById,
            LastModifiedById_contains = graph.LastModifiedById_contains,
            LastModifiedById_in = graph.LastModifiedById_in,
            FieldsWhere = graph.FieldsWhere,
            HavingIssuedPolicies = graph.HavingIssuedPolicies,
            AccessPolicy = graph.AccessPolicy,
            ChannelId = graph.ChannelId
        };

    }

    public class StakeholderWhereInputGraphType : InputObjectGraphType<StakeholderWhere>
    {
        public StakeholderWhereInputGraphType()
        {
            Name = "stakeholderWhere";
            Description = "Stakeholder filter";

            Field(s => s.Id, nullable: true);
            Field(s => s.Id_in, nullable: true);
            Field(s => s.Type, nullable: true);
            Field(s => s.Type_in, nullable: true);
            Field(s => s.EntityId, nullable: true);
            Field(s => s.EntityId_in, nullable: true);
        }
    }

    public class FactWhereInputGraphType : InputObjectGraphType<FactWhereGraph>
    {
        public FactWhereInputGraphType()
        {
            Name = "factWhereInput";
            Description = "Fact filter";

            Field(s => s.Id, nullable: true);
            Field(s => s.Type, nullable: true);
            Field(s => s.ValueJsonString, nullable: true);
        }
    }

    public class RelationshipListWhereInputGraphType : InputObjectGraphType<RelationshipListWhere>
    {
        public RelationshipListWhereInputGraphType()
        {
            Name = "relationshipListWhereInput";
            Description = "Relationship list filter";

            Field(s => s.Type, nullable: true);
            Field(s => s.Type_in, nullable: true);
            Field(s => s.TargetId, nullable: true);
            Field(s => s.TargetId_in, nullable: true);
        }
    }

    public class FactWhereGraph : Where
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public string ValueJsonString { get; set; }

        public static FactWhere ToDomain(FactWhereGraph graph)
        {
            if (graph == null) return null;

            JToken value = null;

            if (graph.ValueJsonString != null)
            {
                var parseResult = Tools.TryParseJsonStringToJToken(graph.ValueJsonString);
                value = (parseResult.Status == "success") ? parseResult.Value : JToken.Parse("{}");
            }

            return new FactWhere
            {
                Id = graph.Id,
                Type = graph.Type,
                Value = value,
            };
        }
    }

    public class ProposalWhereInputGraphType : InputObjectGraphType<ProposalWhere>
    {
        public ProposalWhereInputGraphType()
        {
            Name = "proposalWhere";
            Description = "a proposal filter";

            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);
            Field(f => f.ProposalNumber, nullable: true);
            Field(f => f.ProposalNumber_contains, nullable: true);
            Field(f => f.Name, nullable: true);
            Field(f => f.Name_contains, nullable: true);
            Field(f => f.PolicyId_contains, nullable: true);
            Field(f => f.PolicyId_contains_every, nullable: true);
            Field(f => f.PolicyId_contains_some, nullable: true);
            Field(f => f.Offers_contains, type: typeof(OfferWhereInputGraphType));
            Field(f => f.RenewalHistory, type: typeof(RenewalHistoryWhereInputGraphType));
            Field(f => f.Status, nullable: true);
            Field(f => f.IsIssued, nullable: true);
            Field(f => f.IssuedAt_gt, nullable: true);
            Field(f => f.IssuedAt_lt, nullable: true);
            Field(f => f.Offers_every, type: typeof(OfferWhereInputGraphType), nullable: true);
            Field(f => f.Offers_exist, nullable: true);
            Field(f => f.ReferralCode, nullable: true);
            Field(f => f.IsApprovalNeeded, nullable: true);
            Field(f => f.ChannelId, nullable: true);
        }
    }

    public class RenewalHistoryWhereInputGraphType : InputObjectGraphType<RenewalHistoryWhere>
    {
        public RenewalHistoryWhereInputGraphType()
        {
            Name = "renewalHistoryWhereInput";

            Field(rh => rh.IsRenewed, nullable: true);
            Field(rh => rh.RenewedById, nullable: true);
            Field(rh => rh.RenewedFromId, nullable: true);
        }
    }

    public class CreateCaseInputGraphType : InputObjectGraphType<CreateCaseCommand>
    {
        public CreateCaseInputGraphType()
        {
            Name = "createCaseInput";
            Description = "Create case input";

            Field(c => c.Name, nullable: true);
            Field(c => c.Description, nullable: true);
            Field(c => c.CaseNumber, nullable: true);
            Field(c => c.Status, nullable: true);
            Field(c => c.Source, nullable: true);
            Field(c => c.HolderId, nullable: true);
            Field(c => c.InsuredIds, nullable: true);
            Field(c => c.OtherHolderIds, nullable: true);
            Field(c => c.ComponentId, nullable: true);
            Field(c => c.Fields, nullable: true);
            Field(c => c.FieldsSchemaId, nullable: true);
            Field(c => c.WorkflowSchemaId, nullable: true);
            Field(c => c.ChannelId, nullable: true);
        }
    }

    public class UpdateCaseInputGraphType : InputObjectGraphType<UpdateCaseCommand>
    {
        public UpdateCaseInputGraphType()
        {
            Name = "updateCaseInput";
            Description = "update case input";

            Field(c => c.Name, nullable: true);
            Field(c => c.Description, nullable: true);

            Field(c => c.CaseNumber, nullable: true);
            Field(c => c.Status, nullable: true);
            Field(c => c.Source, nullable: true);
            Field(c => c.HolderId, nullable: true);
            Field(c => c.InsuredIds, nullable: true);
            Field(c => c.OtherHolderIds, nullable: true);
            Field(c => c.ComponentId, nullable: true);
            Field(c => c.Fields, nullable: true);
            Field(c => c.FieldsPatch, nullable: true);
            Field(c => c.FieldsSchemaId, nullable: true);
            Field(c => c.WorkflowSchemaId, nullable: true);
            Field(c => c.ChannelId, nullable: true);
        }
    }

    public class AddProposalInputGraphType : InputObjectGraphType<AddProposalCommand>
    {
        public AddProposalInputGraphType()
        {
            Name = "addProposalInput";
            Description = "Add proposal input";

            Field(c => c.Name, nullable: true);
            Field(c => c.Description, nullable: true);
            Field(c => c.Status, nullable: true);
            Field(c => c.ProposalNumber, nullable: true);
            Field(c => c.ProposalNumberType, nullable: true);
            Field(c => c.ReferralCode, nullable: true);
            Field(c => c.ExpiryDate, nullable: true);
            Field(c => c.TotalPrice, type: typeof(PremiumInputGraphType));
            Field(c => c.RenewalHistory, type: typeof(RenewalHistoryInputGraphType));
        }
    }

    public class CopyProposalInputGraphType : InputObjectGraphType<CopyProposalCommand>
    {
        public CopyProposalInputGraphType()
        {
            Name = "copyProposalInput";
            Description = "Copies a proposal";

            Field(p => p.CopiedFromId);
        }
    }

    public class RenewProposalInputGraphType : InputObjectGraphType<RenewProposalCommand>
    {
        public RenewProposalInputGraphType()
        {
            Name = "renewProposalInput";
            Description = "Renews a proposal";

            Field(p => p.ProposalNumber, nullable: true);
            Field(p => p.OverrideStartDateAndEndDateAutomatically, nullable: true);
            Field(p => p.RenewedFromId);
        }
    }

    public class RenewalHistoryInputGraphType : InputObjectGraphType<RenewalHistory>
    {
        public RenewalHistoryInputGraphType()
        {
            Name = "renewalHistoryInput";
            Description = "Renewal history input";

            Field(c => c.RenewedToId, nullable: true);
            Field(c => c.RenewedFromId, nullable: true);
        }
    }

    public class UpdateProposalInputGraphType : InputObjectGraphType<UpdateProposalCommand>
    {
        public UpdateProposalInputGraphType()
        {
            Name = "updateProposalInput";
            Description = "update proposal input";

            Field(c => c.Name, nullable: true);
            Field(c => c.Description, nullable: true);
            Field(c => c.Status, nullable: true);
            Field(c => c.ProposalNumber, nullable: true);
            Field(c => c.ReferralCode, nullable: true);
            Field(c => c.ExpiryDate, nullable: true);
            Field(c => c.TotalPrice, type: typeof(PremiumInputGraphType));
        }
    }

    public class ClauseGraph
    {
        public string Id { get; set; }
        public int Order { get; set; }
        public bool StoreTemplateByValue { get; set; }
        public TemplateGraph Template { get; set; }
        public Template TemplateInstance { get; set; }
        public RenderParameters RenderParameters { get; set; }
        public Result<string> RenderedHtmlResult { get; set; }
        public string HtmlOverride { get; set; }
        public string Type { get; set; }

        public static ClauseGraph ToGraph(Clause domain) =>
            domain != null
                ? new ClauseGraph
                {
                    Id = domain.Id,
                    Order = domain.Order,
                    Template = new TemplateGraph { Id = domain.TemplateId },
                    TemplateInstance = domain.Template,
                    RenderParameters = domain.RenderParameters,
                    HtmlOverride = domain.HtmlOverride,
                    StoreTemplateByValue = domain.Template != null,
                    Type = domain.Type,
                }
                : null;
    }

    public class ClauseGraphType : ObjectGraphType<ClauseGraph>
    {
        public ClauseGraphType(
            ITemplateService templateService
            )
        {
            Name = "clause";

            Field(c => c.Id, nullable: true);
            Field(c => c.Order, nullable: true);
            Field(c => c.RenderParameters, type: typeof(RenderParametersGraphType));
            Field(c => c.StoreTemplateByValue, nullable: false);
            Field(c => c.Template, type: typeof(TemplateInterfaceGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.StoreTemplateByValue)
                        return TemplateGraph.ToGraph(context.Source.TemplateInstance);

                    if (context.Source.Template?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    Template template = (await templateService.GetAsync(tenantId, new QueryArguments { Where = new TemplateWhere { Id = context.Source.Template.Id } })).FirstOrDefault();

                    return TemplateGraph.ToGraph(template);
                });

            Field(c => c.RenderedHtmlResult, type: typeof(StringResultGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.RenderParameters == null) return null;

                    string tenantId = context.GetTenantIdFromToken();

                    if (context.Source.StoreTemplateByValue)
                        return await templateService.RenderHtmlTemplateFromInstanceAsync(tenantId, new HtmlTemplateRenderFromInstanceCommand { Template = context.Source.TemplateInstance, RenderParameters = context.Source.RenderParameters });

                    if (context.Source.Template?.Id == null) return null;

                    return await templateService.RenderHtmlTemplateAsync(tenantId, context.Source.Template?.Id, context.Source.RenderParameters);
                });

            Field(c => c.HtmlOverride, nullable: true);
            Field(c => c.Type, nullable: true);
        }
    }

    public class AddClauseToOfferInputGraphType : InputObjectGraphType<AddClauseCommand>
    {
        public AddClauseToOfferInputGraphType()
        {
            Name = "addClauseInput";
            Description = "add clause input";

            Field(c => c.Order, nullable: true);
            Field(c => c.TemplateId, nullable: true);
            Field(c => c.StoreTemplateByValue, nullable: true);
            Field(c => c.HtmlOverride, nullable: true);
            Field(c => c.RenderParameters, type: typeof(RenderParametersInputGraphType));
        }
    }

    public class AddClauseCommandGraph
    {
        public string CommandId { get; set; } //for endorsements
        public DateTime Timestamp { get; set; } //for endorsements
        public string EndorsementId { get; set; }
        public string ClauseId { get; set; }
        public int Order { get; set; }
        public string TemplateId { get; set; }
        public RenderParameters RenderParameters { get; set; }
        public Result<string> RenderedHtmlResult { get; set; }
        public Template Template { get; set; }
        public string HtmlOverride { get; set; }
        public LoginGraph AddedBy { get; set; }

        public static AddClauseCommandGraph ToGraph(AddClauseCommand command) =>
            command == null
            ? null
            : new AddClauseCommandGraph
            {
                CommandId = command.CommandId,
                Timestamp = command.Timestamp,
                ClauseId = command.ClauseId,
                Order = command.Order,
                RenderParameters = command.RenderParameters,
                TemplateId = command.TemplateId,
                Template = command.Template,
                HtmlOverride = command.HtmlOverride,
                AddedBy = new LoginGraph { Id = command.AddedById }
            };
    }

    public class AddClauseCommandGraphType : ObjectGraphType<AddClauseCommandGraph>
    {
        public AddClauseCommandGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            ITemplateService templateService,
            PermissionValidator permissionValidator)
        {
            Name = "addClauseCommand";

            Field(c => c.CommandId, nullable: true);
            Field(c => c.Timestamp, type: typeof(DateTimeGraphType));
            Field(c => c.ClauseId, nullable: true);
            Field(c => c.Order, nullable: true);
            Field(c => c.RenderParameters, type: typeof(RenderParametersGraphType));
            Field(c => c.TemplateId, nullable: true);
            Field(c => c.AddedBy, type: typeof(LoginGraphType))
                 .ResolveAsync(async context =>
                 {
                     if (context.Source.AddedBy?.Id == null)
                         return null;

                     IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readLogins", new List<string> { context.Source.AddedBy.Id });
                     if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.AddedBy.Id))
                         return null;

                     string tenantId = context.GetTenantIdFromToken();

                     var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                     i => authService.GetLoginsDictionaryAsync(tenantId, i));

                     Login loginDao = await loginLoader.LoadAsync(context.Source.AddedBy.Id);

                     return LoginGraph.ToGraph(loginDao);
                 });

            Field(c => c.RenderedHtmlResult, type: typeof(StringResultGraphType))
              .ResolveAsync(async context =>
              {
                  if (context.Source.RenderParameters == null || context.Source.TemplateId == null)
                      return null;

                  string tenantId = context.GetTenantIdFromToken();

                  return context.Source.Template != null ?
                    await templateService.RenderHtmlTemplateFromInstanceAsync(tenantId, new HtmlTemplateRenderFromInstanceCommand { Template = context.Source.Template, RenderParameters = context.Source.RenderParameters }) :
                    await templateService.RenderHtmlTemplateAsync(tenantId, context.Source.TemplateId, context.Source.RenderParameters);
              });

            Field(c => c.HtmlOverride, nullable: true);
        }
    }

    public class UpdateClauseOfOfferInputGraphType : InputObjectGraphType<UpdateClauseCommand>
    {
        public UpdateClauseOfOfferInputGraphType()
        {
            Name = "updateClauseInput";
            Description = "update clause input";

            Field(c => c.TemplateId, nullable: true);
            Field(c => c.IsTemplateIdChanged, nullable: true);
            Field(c => c.Order, nullable: true);
            Field(c => c.IsOrderChanged);
            Field(c => c.Type, nullable: true);
            Field(c => c.HtmlOverride, nullable: true);
            Field(c => c.StoreTemplateByValue, nullable: true);
            Field(c => c.RenderParameters, type: typeof(RenderParametersInputGraphType));
        }
    }

    public class ClauseCommandBatchInputGraph
    {
        public List<AddClauseCommand> AddClauseInputs { get; set; }
        public List<UpdateClauseCommand> UpdateClauseInputs { get; set; }
    }

    public class ClauseCommandBatchInputGraphType : InputObjectGraphType<ClauseCommandBatchInputGraph>
    {
        public ClauseCommandBatchInputGraphType()
        {
            Name = "clauseBatchInput";

            Field(b => b.AddClauseInputs, type: typeof(ListGraphType<ClauseBatchAddInputGraphType>));
            Field(b => b.UpdateClauseInputs, type: typeof(ListGraphType<ClauseBatchUpdateInputGraphType>));
        }
    }

    public class ClauseBatchAddInputGraphType : InputObjectGraphType<AddClauseCommand>
    {
        public ClauseBatchAddInputGraphType()
        {
            Name = "clauseBatchAddInput";
            Description = "clause batch add input";

            Field(c => c.Order, nullable: true);
            Field(c => c.TemplateId, nullable: true);
            Field(c => c.StoreTemplateByValue, nullable: true);
            Field(c => c.HtmlOverride, nullable: true);
            Field(c => c.RenderParameters, type: typeof(RenderParametersInputGraphType));
            Field(c => c.Type, nullable: true);
        }
    }

    public class ClauseBatchUpdateInputGraphType : InputObjectGraphType<UpdateClauseCommand>
    {
        public ClauseBatchUpdateInputGraphType()
        {
            Name = "clauseBatchUpdateInput";
            Description = "clause batch update input";

            Field(c => c.ClauseId);
            Field(c => c.Order, nullable: true);
            Field(c => c.IsOrderChanged);
            Field(c => c.TemplateId, nullable: true);
            Field(c => c.IsTemplateIdChanged, nullable: true);
            Field(c => c.StoreTemplateByValue, nullable: true);
            Field(c => c.HtmlOverride, nullable: true);
            Field(c => c.RenderParameters, type: typeof(RenderParametersInputGraphType));
            Field(c => c.Type, nullable: true);
        }
    }

    public class JacketInstanceCommandBatchInputGraph
    {
        public List<AddJacketInstanceCommand> AddJacketInstanceInputs { get; set; }
        public List<UpdateJacketInstanceCommand> UpdateJacketInstanceInputs { get; set; }
    }

    public class JacketInstanceCommandBatchInputGraphType : InputObjectGraphType<JacketInstanceCommandBatchInputGraph>
    {
        public JacketInstanceCommandBatchInputGraphType()
        {
            Name = "jacketInstanceBatchInput";

            Field(b => b.AddJacketInstanceInputs, type: typeof(ListGraphType<AddJacketInstanceInputGraphType>));
            Field(b => b.UpdateJacketInstanceInputs, type: typeof(ListGraphType<UpdateJacketInstanceInputGraphType>));
        }
    }

    public class AddJacketInstanceInputGraphType : InputObjectGraphType<AddJacketInstanceCommand>
    {
        public AddJacketInstanceInputGraphType()
        {
            Name = "addJacketInstanceInput";
            Description = "instance of jacket to add into offer";

            Field(c => c.JacketId);
            Field(c => c.StoreJacketByValue, true);
            Field(c => c.Order, nullable: true);
        }
    }

    public class UpdateJacketInstanceInputGraphType : InputObjectGraphType<UpdateJacketInstanceCommand>
    {
        public UpdateJacketInstanceInputGraphType()
        {
            Name = "updateJacketInstanceInput";
            Description = "instance of jacket to update from offer";

            Field(c => c.InstanceId);
            Field(c => c.Order, nullable: true);
        }
    }

    public class OfferIdInputGraph
    {
        public string CaseId { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
    }

    public class OfferIdInputGraphType : InputObjectGraphType<OfferIdInputGraph>
    {
        public OfferIdInputGraphType()
        {
            Name = "offerIdInput";

            Field(x => x.CaseId, nullable: false);
            Field(x => x.ProposalId, nullable: false);
            Field(x => x.OfferId, nullable: false);
        }
    }
}
