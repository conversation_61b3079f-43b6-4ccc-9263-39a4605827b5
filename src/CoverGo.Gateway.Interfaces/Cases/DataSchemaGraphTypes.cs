using System.Collections.Generic;
using System.Linq;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.GraphQLGenerators;
using GraphQL.DataLoader;
using GraphQL.Types;
using Newtonsoft.Json;

namespace CoverGo.Gateway.Interfaces.Cases
{
    public sealed class DataSchemaGraphType : ObjectGraphType<DataSchemaGraph>
    {
        public DataSchemaGraphType(
            IDataLoaderContextAccessor accessor,
            IProductService productService,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "dataSchema";
            Field(f => f.Id, nullable: false);
            Field(f => f.Name, nullable: true);
            Field(f => f.Description, nullable: true);
            Field(f => f.Schema, nullable: true);
            Field(f => f.Standard, type: typeof(DataSchemaStandardGraphType), nullable: true);
            Field(f => f.Type, nullable: true);
            Field(f => f.Tags, nullable: true);
            Field(f => f.UiSchemas, type: typeof(ListGraphType<NonNullGraphType<UiSchemaGraphType>>), nullable: true)
                .Argument<UiSchemaWhereInputGraphType>("where", "")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    UiSchemaWhere where = context.GetArgument<UiSchemaWhere>("where");

                    var uiSchemaLoader = accessor.Context
                        .GetOrAddBatchLoader<DataSchemaGraph, IEnumerable<UiSchema>>("GetUiSchemas",
                            async p =>
                            {
                                List<string> allUiSchemaIds = p
                                    .Where(i => i.UiSchemas != null)
                                    .SelectMany(i => i.UiSchemas)
                                    .Where(i => i != null)
                                    .Select(i => i.Id)
                                    .ToList();
                                UiSchemaWhere filter = new()
                                {
                                    And = new[] { where, new() { Id_in = allUiSchemaIds } }
                                };
                                IReadOnlyCollection<UiSchema> allUiSchemas = (await productService.GetUiSchemasAsync(tenantId, filter)).ToArray();
                                return p.ToDictionary(dataSchema => dataSchema,
                                                      dataSchema => allUiSchemas.Where(scr => dataSchema.UiSchemas?.Any(s => scr.Id == s.Id) == true));
                            });

                    IEnumerable<UiSchema> uiSchemas = await uiSchemaLoader.LoadAsync(context.Source);

                    return uiSchemas.Select(UiSchemaGraph.ToGraph).ToArray();
                });
            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }
    public sealed class DataSchemasGraphType : ObjectGraphType<DataSchemasGraph>
    {
        public DataSchemasGraphType()
        {
            Name = "dataSchemas";
            Field(f => f.TotalCount, nullable: true);
            Field(f => f.List, type: typeof(ListGraphType<DataSchemaGraphType>), nullable: true);
        }
    }

    public sealed class CreateDataSchemaInputGraphType : InputObjectGraphType<CreateDataSchemaInput>
    {
        public CreateDataSchemaInputGraphType()
        {
            Name = "createDataSchemaInput";
            Field(f => f.Name);
            Field(f => f.Description);
            Field(f => f.Schema);
            Field(f => f.Standard, type: typeof(DataSchemaStandardInputGraphType), nullable: true);
            Field(f => f.Type, nullable: true);
            Field(f => f.Tags, nullable: true);
        }
    }

    public sealed class DataSchemaWhereInputGraphType : AutoInputObjectGraphType<DataSchemaWhere>
    {
        public DataSchemaWhereInputGraphType() =>
            Name = "dataSchemaWhereInput";
    }

    public sealed class UpdateDataSchemaInputGraphType : InputObjectGraphType<UpdateDataSchemaInput>
    {
        public UpdateDataSchemaInputGraphType()
        {
            Name = "updateDataSchemaInput";
            Field(f => f.Name);
            Field(f => f.Description);
            Field(f => f.Schema);
            Field(f => f.Standard, type: typeof(DataSchemaStandardInputGraphType), nullable: true);
            Field(f => f.DataSchemaId);
            Field(f => f.Type, nullable: true);
            Field(f => f.Tags, nullable: true);
        }
    }

    public sealed class DeleteDataSchemaInputGraphType : AutoInputObjectGraphType<DeleteDataSchemaInput>
    {
        public DeleteDataSchemaInputGraphType() =>
            Name = "deleteDataSchemaInput";
    }

    public class DataSchemaGraph : SystemObjectGraph
    {
        [JsonRequired]
        public string Id { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }
        public string Schema { get; set; }
        public DataSchemaStandard Standard { get; set; }
        public string Type { get; set; }
        public string[] Tags { get; set; }
        public IReadOnlyCollection<UiSchemaGraph> UiSchemas { get; set; }

        public static DataSchemaGraph ToGraph(DataSchema domain)
        {
            if (domain == null)
                return null;

            var graph = new DataSchemaGraph
            {
                Id = domain.Id,
                Name = domain.Name,
                Description = domain.Description,
                Schema = domain.Schema?.ToString(),
                Standard = domain.Standard,
                Type = domain.Type,
                Tags = domain.Tags,
                UiSchemas = domain.UiSchemaIds?.Select(i => new UiSchemaGraph { Id = i }).ToArray(),
            };

            graph.PopulateSystemGraphFields(domain);

            return graph;
        }
    }

    public class DataSchemasGraph
    {
        public int TotalCount { get; set; }
        public IEnumerable<DataSchemaGraph> List { get; set; }
    }

    public class CreateDataSchemaInput
    {
        [JsonRequired]
        public string Name { get; set; }

        [JsonRequired]
        public string Description { get; set; }

        [JsonRequired]
        public string Schema { get; set; }

        [JsonRequired]
        public DataSchemaStandard Standard { get; set; }

        public string Type { get; set; }
        public string[] Tags { get; set; }
        public UiSchema[] UiSchemas { get; set; }
    }

    public class UpdateDataSchemaInput
    {
        [JsonRequired]
        public string DataSchemaId { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string Schema { get; set; }

        public DataSchemaStandard Standard { get; set; }

        public string Type { get; set; }
        public string[] Tags { get; set; }
        public UiSchema[] UiSchemas { get; set; }
    }

    public class DeleteDataSchemaInput
    {
        [JsonRequired]
        public string DataSchemaId { get; set; }
    }

    public class DataSchemaStandardInputGraphType : InputObjectGraphType<DataSchemaStandard>
    {
        public DataSchemaStandardInputGraphType()
        {
            Name = "dataSchemaStandardInput";
            Field(f => f.Type, type: typeof(DataSchemaStandardTypeEnumGraphType), nullable: false);
            Field(f => f.Version, nullable: true);
        }
    }

    public class DataSchemaStandardGraphType : ObjectGraphType<DataSchemaStandard>
    {
        public DataSchemaStandardGraphType()
        {
            Name = "dataSchemaStandard";
            Field(f => f.Type, type: typeof(DataSchemaStandardTypeEnumGraphType), nullable: false);
            Field(f => f.Version, nullable: true);
        }
    }

    public class DataSchemaStandardTypeEnumGraphType : EnumerationGraphType<DataSchemaStandardTypeEnum>
    {
        public DataSchemaStandardTypeEnumGraphType()
        {
            Name = "dataSchemaStandardTypeEnum";
            Description = "A list of all possible DataSchema standards";
        }
    }


    public class AddUiSchemaToDataSchemaInput
    {
        [JsonRequired]
        public string DataSchemaId { get; set; }

        [JsonRequired]
        public string UiSchemaId { get; set; }
    }

    public class RemoveUiSchemaFromDataSchemaInput
    {
        [JsonRequired]
        public string DataSchemaId { get; set; }

        [JsonRequired]
        public string UiSchemaId { get; set; }
    }


    public class AddUiSchemaToDataSchemaInputGraphType : AutoInputObjectGraphType<AddUiSchemaToDataSchemaInput> { }

    public class RemoveUiSchemaFromDataSchemaInputGraphType : AutoInputObjectGraphType<RemoveUiSchemaFromDataSchemaInput> { }
}