using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using AutoMapper;

using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Binder;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Cases;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;

using GraphQL.Authorization;
using GraphQL.Types;
using GraphQL.Validation;

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

using IPoliciesClient = CoverGo.Policies.Client.IPoliciesClient;
using Object = CoverGo.Users.Domain.Objects.Object;
using QueryArguments = GraphQL.Types.QueryArguments;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializePoliciesMutations(
            IAuthService authService,
            IPolicyService policyService,
            CoverGoPolicyMembersService policyMembersService,
            CoverGoPolicyCountersService policyCountersService,
            ITransactionService transactionService,
            IEntityService entityService,
            ITemplateService templateService,
            ICaseService caseService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
            IEntityService<Object, CreateObjectCommand, UpdateObjectCommand> objectService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            PermissionValidator permissionValidator,
            IOptions<MvcNewtonsoftJsonOptions> mvcJsonOptions,
            IPoliciesClient policiesClient,
            IMapper mapper)
        {
            Field<PolicyResultGraphType>()
                .Name("initializePolicy")
                .Description("Initializes a policy")
                .AuthorizeWith("any")
                .Argument<InitializePolicyInputGraphType>("policy", "the policy input")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("createPolicies", "writePolicies"));

                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    InitializePolicyInputGraph input = context.GetArgument<InitializePolicyInputGraph>("policy");

                    if (input.ReferralCode != null)
                    {
                        Internal intern = (await internalService.GetAsync(tenantId, new EntityWhere { InternalCode = input.ReferralCode })).FirstOrDefault();
                        if (intern == null || intern.IsActive == false)
                            input.ReferralCode = null;
                    }

                    var command = new CreatePolicyCommand
                    {
                        CreatedById = loginId,
                        Description = input.Description,
                        EndDate = input.EndDate,
                        ContractHolder = new Entity { Id = input.HolderId },
                        OtherContractHolders = input.OtherHolderIds?.Select(i => new Entity { Id = i })?.ToList(),
                        ContractInsured = input.InsuredIds?.Select(i => new Entity { Id = i })?.ToList(),
                        IssueDate = input.IssueDate,
                        IssuerNumber = input.IssuerNumber,
                        Values = input.Values != null ? JToken.FromObject(input.Values?.ToDictionary(x => x.Key, x => x.Value.GetValue())) : null,
                        ReferralCode = input.ReferralCode,
                        Source = input.Source,
                        StartDate = input.StartDate,
                        Premium = input.Premium,
                        ProductId = input.ProductId,
                        Fields = input.Fields,
                        FieldsSchemaId = input.FieldsSchemaId,
                        Status = input.Status ?? "QUOTING", // THIS IS WRONGGGGGG,  we should not have quoting in there
                        ClientId = context.GetClientIdFromToken(),
                        IsRenewal = input.IsRenewal,
                        RenewalNumber = input.RenewalNumber,
                        PreviousPolicyIds = input.PreviousPolicyIds,
                        RenewalVersion = input.RenewalVersion,
                        OriginalIssuerNumber = input.OriginalIssuerNumber
                    };

                    PolicyGeneratedFromInputGraph generatedFrom = input.GeneratedFrom;
                    if (generatedFrom != null)
                    {
                        command.GeneratedFrom = new GeneratedFrom
                        {
                            CaseId = generatedFrom.CaseId,
                            ProposalId = generatedFrom.ProposalId,
                            OfferId = generatedFrom.OfferId
                        };
                    }

                    if (command.Premium?.Amount != null || command.Premium?.GrossAmount != null || command.Premium?.CurrencyCode != null)
                    {
                        await permissionValidator.Authorize(context, "overrideOffers");
                        command.IsPremiumOverridden = true;
                    }

                    if (input.HolderId != null)
                        if (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = input.HolderId }) == null)
                            return new Result<PolicyStatus> { Status = "failure", Errors = new List<string> { $"The holder '{input?.HolderId}' doesn't exist." } };

                    IEnumerable<string> neededEntityIds = Enumerable.Empty<string>();
                    if (input?.InsuredIds != null)
                        neededEntityIds = input?.InsuredIds?.Distinct();
                    if (input?.OtherHolderIds != null)
                        neededEntityIds = neededEntityIds.Concat(input?.OtherHolderIds)?.Distinct();

                    if (neededEntityIds != Enumerable.Empty<string>())
                    {
                        IEnumerable<string> entityIds = (await Tools.GetEntitiesAsync(individualService, internalService, companyService, objectService, organizationService, tenantId, new EntityWhere { Id_in = neededEntityIds.ToList() })).Select(e => e.Id);

                        if (input?.InsuredIds != null)
                            foreach (string insuredId in input?.InsuredIds)
                                if (!entityIds.Any(c => c == insuredId))
                                    return new Result<PolicyStatus> { Status = "failure", Errors = new List<string> { $"The insured '{insuredId}' doesn't exist." } };
                        if (input.OtherHolderIds != null)
                            foreach (string otherHolderId in input.OtherHolderIds)
                                if (!entityIds.Any(c => c == otherHolderId))
                                    return new Result<PolicyStatus> { Status = "failure", Errors = new List<string> { $"The other holder '{otherHolderId}' doesn't exist." } };
                    }

                    var userContext = (GraphQLUserContext)context.UserContext;
                    Result<PolicyStatus> policyResult = await policyService.CreatePolicyAsync(tenantId, command);

                    if (input.Facts?.Any() ?? false)
                        await policyService.PolicyFactBatch(tenantId, policyResult.Value.Id, new FactCommandBatch { AddFactCommands = input.Facts.Select(f => f.ToCommand(loginId))?.ToList() });

                    if (await permissionValidator.HasCreatorRights(context))
                        await authService.AddTargettedPermissionsAsync(tenantId, loginId, new List<AddTargettedPermissionCommand> { new() { AddedById = loginId, Type = "readPolicies", Value = policyResult.Value.Id }, new() { AddedById = loginId, Type = "writePolicies", Value = policyResult.Value.Id } });

                    return policyResult;
                });

            Field<CreatedStatusResultGraphType>()
               .Name("addOffer")
               .Description("adds an offer to a policy")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the quote")
               .Argument<NonNullGraphType<AddOfferInputGraphType>>("offerInput", "the information of the offer")
               .ResolveAsync(async context =>
               {
                   await permissionValidator.Authorize(context, new PermissionRequest("createOffers", "writeOffers"));
                   string tenantId = context.GetTenantIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");

                   AddOfferInputGraph input = context.GetArgument<AddOfferInputGraph>("offerInput");

                   var command = new AddOfferCommand
                   {
                       ProductId = input.ProductId,
                       Premium = input.Premium,
                       BenefitOptions = input.BenefitOptions?.Select(b =>
                        new Domain.Pricing.BenefitOption
                        {
                            TypeId = b.TypeId,
                            Key = b.Key,
                            Value = b.Value != null ? JToken.FromObject(b.Value?.GetValue()) : null,
                            InsuredId = b.InsuredId,
                        })?.ToList()
                   };

                   if (command.Premium?.Amount != null || command.Premium?.GrossAmount != null || command.Premium?.CurrencyCode != null)
                   {
                       await permissionValidator.Authorize(context, "overrideOffers");
                       command.IsPremiumOverridden = true;
                   }

                   string loginId = context.GetLoginIdFromToken();
                   command.AddedById = loginId;

                   Result<CreatedStatus> policyResult = await policyService.AddOfferAsync(tenantId, policyId, command);

                   return policyResult;
               });

            Field<ResultGraphType>()
               .Name("updateOffer")
               .Description("updates an offer of a quote")
               .AuthorizeWith("any")
               .Argument<StringGraphType>("policyId", "the identifier of the policy")
               .Argument<NonNullGraphType<StringGraphType>>("offerId", "the identifier of the offer")
               .Argument<NonNullGraphType<UpdateOfferGraphType>>("updateOfferInput", "the information of the offer to update")
               .ResolveAsync(async context =>
               {
                   string offerId = context.GetArgument<string>("offerId");

                   string tenantId = context.GetTenantIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");

                   await permissionValidator.Authorize(context, new PermissionRequest("updateOffers", "writeOffers").WithTargetIds(offerId));

                   UpdateOfferCommand command = context.GetArgument<Dictionary<string, object>>("updateOfferInput").ToUpdateCommand<UpdateOfferCommand>();
                   if (command.Premium?.Amount != null || command.Premium?.GrossAmount != null || command.Premium?.CurrencyCode != null)
                   {
                       await permissionValidator.Authorize(context, "overrideOffers", offerId);
                       command.IsPremiumOverridden = true;
                   }

                   command.ModifiedById = context.GetLoginIdFromToken();
                   command.OfferId = offerId;

                   Result policyResult = await policyService.UpdateOfferAsync(tenantId, policyId, command);

                   return policyResult;
               });


            Field<ResultGraphType>()
               .Name("removeOffer")
               .Description("removes an offer from a quote")
               .AuthorizeWith("any")
               .Argument<StringGraphType>("policyId", "the identifier of the policy")
               .Argument<NonNullGraphType<StringGraphType>>("offerId", "the identifier of the offer")
               .ResolveAsync(async context =>
               {
                   string offerId = context.GetArgument<string>("offerId");

                   await permissionValidator.Authorize(context, new PermissionRequest("deleteOffers", "writeOffers").WithTargetIds(offerId));

                   string tenantId = context.GetTenantIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");

                   Result policyResult = await policyService.RemoveOfferAsync(tenantId, policyId, new RemoveCommand
                   {
                       Id = offerId,
                       RemovedById = context.GetLoginIdFromToken()
                   });

                   return policyResult;
               });

            Field<ResultGraphType>()
               .Name("convertOffer")
               .Description("Converts an offer")
               .AuthorizeWith("any")
               .Argument<StringGraphType>("policyId", "the identifier of the policy")
               .Argument<NonNullGraphType<StringGraphType>>("offerId", "the identifier of the offer")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();

                   string policyId = context.GetArgument<string>("policyId");
                   string offerId = context.GetArgument<string>("offerId");

                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));

                   Result policyResult = await policyService.ConvertOfferAsync(tenantId, policyId, new ConvertOfferCommand { OfferId = offerId, ConvertedById = loginId });

                   return policyResult;
               });

            FieldAsync<ResultGraphType>(
                "connectWithStripe",
                description: "Creates a bridge between CoverGo's stripe account and the client's stripe account",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "tenantId", Description = "The tenant identifier" },
                    new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "authorizationCode", Description = "The stripe authorization code of the tenant" }
                ),
                resolve: async context =>
                {
                    string tenantId = context.GetArgument<string>("tenantId");
                    string authorizationCode = context.GetArgument<string>("authorizationCode");

                    Result result = await transactionService.SetStripeDestinationUserIdAsync(
                        tenantId,
                        authorizationCode);

                    return result;
                });

            Field<PolicyResultGraphType>()
                .Name("issuePolicy")
                .Description("issues a policy")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("issuerNumber", "Will set this policy number if the issuer allows it.")
                .Argument<StringGraphType>("originalIssuerNumber", "Will set this policy original issuer number if policy is renewal, will be ignored in other case")
                .Argument<StringGraphType>("status", "Value to override status of issued policy. Default is 'ISSUED'")
                .Argument<BooleanGraphType>("isManual", "If set to true, this will just change the status to 'ISSUED'")
                .Argument<BooleanGraphType>("ignoreIssuanceValidation", "If set to true, policy validation will be ignored")
                .Argument<BooleanGraphType>("ignoreCollateralValidation", "If set to true, validation of collateral from transactions will be ignored")
                .Argument<BooleanGraphType>("skipSendNotification", "If set to true, no notifications will be sent to the end customer of the policy")
                .Argument<ListGraphType<StringGraphType>>("externalPolicyNumberIssuerIds", "List of external stakeholders to generate external policy numbers")
                .ResolveAsync(async context =>
                {
                    string policyId = context.GetArgument<string>("policyId");

                    await permissionValidator.Authorize(context, "issuePolicies", policyId);

                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    string issuerNumber = context.GetArgument<string>("issuerNumber");
                    string originalIssuerNumber = context.GetArgument<string>("originalIssuerNumber");

                    //ToDo: this is obsolete, clean when possible
                    bool isManual = context.GetArgument<bool>("isManual");
                    string status = context.GetArgument<string>("status");

                    bool ignoreIssuanceValidation = context.GetArgument<bool>("ignoreIssuanceValidation");
                    bool ignoreCollateralValidation = context.GetArgument<bool>("ignoreCollateralValidation");
                    bool skipSendNotification = context.GetArgument<bool>("skipSendNotification");
                    List<string> externalPolicyNumberIssuerIds = context.GetArgument<List<string>>("externalPolicyNumberIssuerIds");

                    Result<PolicyStatus> issuanceResult = isManual
                        ? await policyService.IssuePolicyAsync(tenantId, new IssuePolicyCommand {
                            PolicyId = policyId,
                            Status = status,
                            IssuedById = loginId,
                            IgnoreCollateralValidation = true,
                            IgnoreIssuanceValidation = true,
                            SkipSendNotification = true,
                            IssuerNumber = issuerNumber,
                            OriginalIssuerNumber = originalIssuerNumber,
                            ExternalPolicyNumberIssuerIds = externalPolicyNumberIssuerIds
                        })
                        : await policyService.IssuePolicyAsync(tenantId, new IssuePolicyCommand {
                            PolicyId = policyId,
                            Status = status,
                            IssuedById = loginId,
                            IgnoreCollateralValidation = ignoreCollateralValidation,
                            IgnoreIssuanceValidation = ignoreIssuanceValidation,
                            SkipSendNotification = skipSendNotification,
                            IssuerNumber = issuerNumber,
                            OriginalIssuerNumber = originalIssuerNumber,
                            ExternalPolicyNumberIssuerIds = externalPolicyNumberIssuerIds
                        });

                    //To remove?
                    if (issuanceResult?.Status != "success")
                    {
                        IEnumerable<Transaction> transactions = await transactionService.GetAsync(tenantId, new Domain.QueryArguments { Where = new TransactionWhere { PolicyId_in = new List<string> { policyId } } });
                        await Task.WhenAll(transactions.Select(t => transactionService.RefundAsync(tenantId, new RefundCommand
                        {
                            TransactionIdToRefund = t.Id,
                            PolicyId = t.PolicyId
                        })));
                    }

                    return issuanceResult ?? new Result<PolicyStatus> { Status = "IssuePolicy failed." };
                });

            Field<ResultGraphType>()
                .Name("batchIntegrate")
                .Description("Integrate with an issuer a batch of policies")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("issuerId", "The issuer id to integrate with")
                .Argument<NonNullGraphType<DateTimeOffsetGraphType>>("from", "The date from which to integrate to.")
                .Argument<NonNullGraphType<DateTimeOffsetGraphType>>("to", "The date to which to integrate to.")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    string issuerId = context.GetArgument<string>("issuerId");

                    await permissionValidator.Authorize(context, "batchIntegrate", issuerId);

                    DateTime from = context.GetArgument<DateTime>("from");
                    DateTime to = context.GetArgument<DateTime>("to");

                    Result result = await policyService.BatchIntegrateAsync(tenantId, new BatchIntegrateCommand
                    {
                        IssuerId = issuerId,
                        From = from,
                        To = to
                    });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("triggerIssuerFunction")
                .Description("trigger a function for the issuer of this policy")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy identifier")
                .Argument<NonNullGraphType<StringGraphType>>("purpose", "The policy identifier")
                .Argument<StringGraphType>("inputJsonString", "The input for the function in json format")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    string policyId = context.GetArgument<string>("policyId");
                    string purpose = context.GetArgument<string>("purpose");

                    string input = context.GetArgument<string>("inputJsonString");

                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Result result = await policyService.TriggerIssuerFunctionAsync(tenantId, policyId, new TriggerIssuerFunctionCommand
                    {
                        Purpose = purpose,
                        Input = input != null ? JToken.Parse(input) : null
                    });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updatePolicy")
                .Description("updates a policy")
                .DeprecationReason("use `updatePolicy2` instead")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<UpdatePolicyInputGraphType>>("updatedPolicy", "the update policy input")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identitifier of the policy to update")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                    return await context.ResolveUpdatePolicyAsync(policyService, authService, individualService, internalService, companyService, objectService, organizationService, entityService);
                });

            Field<ResultGraphType>()
                .Name("addOtherHolder")
                .Description("adds a holder to other holders")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identitifier of the policy to update")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the identitifier of the entity to add")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string policyId = context.GetArgument<string>("policyId");
                    string loginId = context.GetLoginIdFromToken();

                    if (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }) == null)
                        return new Result { Status = "failure", Errors = new List<string> { $"The entity '{entityId}' doesn't exist." } };

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result { Status = "failure", Errors = new List<string> { $"Policy '{policyId}' not found." } };
                    if (policy.Status == "ISSUED")
                        return new Result { Status = "failure", Errors = new List<string> { $"Policy has already been issued." } };
                    if (policy.ContractHolder?.Id == entityId)
                        return new Result { Status = "failure", Errors = new List<string> { $"The entity '{entityId}' is already the main holder of this policy." } };
                    if (policy.OtherContractHolders?.Any(h => h.Id == entityId) ?? false)
                        return new Result { Status = "failure", Errors = new List<string> { $"The entity '{entityId}' is already a holder of this policy" } };

                    return await policyService.AddOtherHolderAsync(tenantId, policyId, new AddEntityCommand { EntityId = entityId, AddedById = loginId });
                });

            Field<ResultGraphType>()
                .Name("removeOtherHolder")
                .Description("removes a holder from other holders")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identitifier of the policy to update")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the identitifier of the entity to add")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string policyId = context.GetArgument<string>("policyId");
                    string loginId = context.GetLoginIdFromToken();

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result { Status = "failure", Errors = new List<string> { $"Policy '{policyId}' not found." } };
                    if (policy.Status == "ISSUED")
                        return new Result<PolicyStatus> { Status = "failure", Errors = new List<string> { $"Policy has already been issued." } };

                    return await policyService.RemoveOtherHolderAsync(tenantId, policyId, new RemoveCommand
                    {
                        Id = entityId,
                        RemovedById = loginId
                    });
                });

            Field<ResultGraphType>()
                .Name("addInsured")
                .Description("adds an insured to a policy")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identitifier of the policy to update")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the identitifier of the entity to add")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string policyId = context.GetArgument<string>("policyId");
                    string loginId = context.GetLoginIdFromToken();

                    if (await entityService.GenericQueryIdsAndTypesAsync(tenantId, new EntityWhere { Id = entityId }) == null)
                        return new Result { Status = "failure", Errors = new List<string> { $"The entity '{entityId}' doesn't exist." } };

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result { Status = "failure", Errors = new List<string> { $"Policy '{policyId}' not found." } };
                    if (policy.Status == "ISSUED")
                        return new Result<PolicyStatus> { Status = "failure", Errors = new List<string> { $"Policy has already been issued." } };
                    if (policy.ContractInsured?.Any(h => h.Id == entityId) ?? false)
                        return new Result<PolicyStatus> { Status = "failure", Errors = new List<string> { $"The entity '{entityId}' is already an insured of this policy" } };

                    return await policyService.AddInsuredAsync(tenantId, policyId, new AddEntityCommand { EntityId = entityId, AddedById = loginId });
                });

            Field<ResultGraphType>()
                .Name("removeInsured")
                .Description("removes an insured from a policy")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identitifier of the policy to update")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the identitifier of the entity to add")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                    string tenantId = context.GetTenantIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string policyId = context.GetArgument<string>("policyId");
                    string loginId = context.GetLoginIdFromToken();

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result { Status = "failure", Errors = new List<string> { $"Policy '{policyId}' not found." } };
                    if (policy.Status == "ISSUED")
                        return new Result<PolicyStatus> { Status = "failure", Errors = new List<string> { $"Policy has already been issued." } };

                    return await policyService.RemoveInsuredAsync(tenantId, policyId, new RemoveCommand
                    {
                        Id = entityId,
                        RemovedById = loginId
                    });
                });


            Field<ResultGraphType>()
               .Name("updateContractHolderIndividual")
               .Description("updates a contract holder that is an individual")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy to update")
               .Argument<NonNullGraphType<UpdateIndividualInputGraphType>>("input", "the individual input")
               .ResolveAsync(async context =>
               {
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   UpdateIndividualCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateIndividualCommand>();
                   command.ModifiedById = loginId;
                   return await policyService.UpdateContractHolderIndividualAsync(tenantId, policyId, command);

               });

            Field<ResultGraphType>()
                .Name("updateContractHolderCompany")
                .Description("updates a contract holder that is a company")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy to update")
                .Argument<NonNullGraphType<UpdateCompanyInputGraphType>>("input", "the company input")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    UpdateCompanyCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateCompanyCommand>();
                    command.ModifiedById = loginId;
                    return await policyService.UpdateContractHolderCompanyAsync(tenantId, policyId, command);

                });

            Field<ResultGraphType>()
                .Name("updateContractInsuredIndividual")
                .Description("updates a contractInsured of an issued policy who is an individual")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<UpdateIndividualInputGraphType>>("input", "the update contract insured input")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the identitifer of the contractInsured")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identitifier of the policy to update")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                    return await context.ResolveUpdateContractInsuredIndividualAsync(policyService, individualService);
                });

            Field<ResultGraphType>()
                .Name("updateContractInsuredCompany")
                .Description("updates a contractInsured of an issued policy who is a company")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<UpdateCompanyInputGraphType>>("input", "the update contract insured input")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the identitifer of the contractInsured")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identitifier of the policy to update")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");
                    string policyId = context.GetArgument<string>("policyId");

                    UpdateCompanyCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateCompanyCommand>();
                    command.EntityId = entityId;
                    command.ModifiedById = loginId;

                    Result result = await policyService.UpdateContractInsuredCompanyAsync(tenantId, policyId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateContractInsuredObject")
                .Description("updates a contractInsured of an issued policy that is an object")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<UpdateObjectInputGraphType>>("input", "the update contract insured object input")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the identitifer of the contractInsured")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identitifier of the policy to update")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                    return await context.ResolveUpdateContractInsuredObjectAsync(policyService, objectService);
                });

            Field<ResultGraphType>()
                .Name("addPaymentInfo")
                .AuthorizeWith("any")
                .Description("Adds payment info to a policy")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy id")
                .Argument<NonNullGraphType<AddPaymentInfoInputGraphType>>("input", "The payment info input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    AddPaymentInfoCommand command = context.GetArgument<AddPaymentInfoCommand>("input");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result result = await policyService.AddPaymentInfoAsync(tenantId, policyId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updatePaymentInfo")
                .Description("updates a payment info of a policy")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The policy identifier")
                .Argument<NonNullGraphType<StringGraphType>>("paymentId", "The payment identifier")
                .Argument<NonNullGraphType<PaymentInfoToUpdateGraphType>>("updateIssuedPaymentInput", "The payment info input")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                    return await context.ResolveUpdatePaymentInfoAsync(policyService, permissionValidator);
                });

            Field<ResultGraphType>()
              .Name("removePaymentInfo")
              .Description("removes a payment info of a policy")
              .AuthorizeWith("any")
              .Argument<NonNullGraphType<StringGraphType>>("id", "The policy identifier")
              .Argument<NonNullGraphType<StringGraphType>>("paymentId", "The payment identifier")
              .ResolveAsync(async context =>
              {
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                  return await context.ResolveRemovePaymentInfoAsync(policyService, permissionValidator);
              });

            Field<ResultGraphType>()
                .Name("deletePolicy")
                .Description("deletes a policy")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The policy identifier")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("deletePolicies", "writePolicies"));
                    return await context.ResolveDeletePolicyAsync(policyService, permissionValidator);
                });

            Field<ResultGraphType>()
             .Name("addBeneficiaryEligibility")
             .Description("Adds a beneficiary eligibility")
             .AuthorizeWith("any")
             .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
             .Argument<NonNullGraphType<BeneficiaryEligibilityInputGraphType>>("beneficiaryEligibilityInput", "the information of the  beneficiary eligibility to update")
             .ResolveAsync(async context =>
             {
                 await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                 return await context.ResolveAddBeneficiaryEligibilityAsync(policyService, permissionValidator);
             });

            Field<ResultGraphType>()
                .Name("updateBeneficiaryEligibility")
                .Description("Updates a beneficiary eligibility")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
                .Argument<NonNullGraphType<StringGraphType>>("beneficiaryEligibilityId", "the identifier of the beneficiaryEligibility")
                .Argument<NonNullGraphType<BeneficiaryEligibilityInputGraphType>>("beneficiaryEligibilityInput", "the information of the  beneficiary eligibility to update")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                    return await context.ResolveUpdateBeneficiaryEligibilityAsync(policyService, permissionValidator);
                });

            Field<ResultGraphType>()
              .Name("removeBeneficiaryEligibility")
              .Description("removes a beneficiaryEligibility")
              .AuthorizeWith("any")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
              .Argument<NonNullGraphType<StringGraphType>>("beneficiaryEligibilityId", "the beneficiary eligibility id")
              .ResolveAsync(async context =>
              {
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                  return await context.ResolveRemoveBeneficiaryEligibilityAsync(policyService, permissionValidator);
              });

            Field<ResultGraphType>()
               .Name("upsertBenefitOption")
               .Description("updates a benefitOption or inserts it if it does not exist")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
               .Argument<StringGraphType>("offerId", "the identifier of the offer") //should be a NonNullGraphType but it is not right now as hiveup is using it
               .Argument<NonNullGraphType<BenefitOptionInputGraphType>>("input", "the information of the  benefit option to update")
               .ResolveAsync(async context =>
               {
                   string policyId = context.GetArgument<string>("policyId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId)); //ToDo: change permission to writeOffers after aag transitions to offers

                   string tenantId = context.GetTenantIdFromToken();
                   string offerId = context.GetArgument<string>("offerId");
                   string loginId = context.GetLoginIdFromToken();
                   BenefitOptionInputGraph input = context.GetArgument<BenefitOptionInputGraph>("input");

                   Result result = await policyService.UpsertBenefitOptionAsync(tenantId, policyId, BenefitOptionInputGraph.ToCommand(input, null, offerId, loginId));

                   return result;
               });

            Field<ResultGraphType>()
               .Name("upsertBenefitOptionBatch")
               .Description("batch update benefitOptions or insert them if it does not exist")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
               .Argument<StringGraphType>("offerId", "the identifier of the offer") //should be a NonNullGraphType but it is not right now as hiveup is using it
               .Argument<NonNullGraphType<ListGraphType<BenefitOptionInputGraphType>>>("input", "the information of the benefit options to upsert")
               .ResolveAsync(async context =>
               {
                   string policyId = context.GetArgument<string>("policyId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId)); //ToDo: change permission to writeOffers after aag transitions to offers

                   string tenantId = context.GetTenantIdFromToken();
                   string offerId = context.GetArgument<string>("offerId");
                   string loginId = context.GetLoginIdFromToken();
                   List<BenefitOptionInputGraph> input = context.GetArgument<List<BenefitOptionInputGraph>>("input");

                   IEnumerable<UpsertBenefitOptionCommand> commands = input.Select(bo => BenefitOptionInputGraph.ToCommand(bo, null, offerId, loginId));
                   Result result = await policyService.UpsertBenefitOptionBatchAsync(tenantId, policyId, new UpsertBenefitOptionCommandBatch { UpsertBenefitOptionCommands = commands.ToList() });

                   return result;
               });

            Field<ResultGraphType>()
               .Name("removeBenefitOption")
               .Description("removes a benefitOption")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
                .Argument<StringGraphType>("offerId", "the identifier of the offer") //should be a NonNullGraphType but it is not right now as hiveup is using it
               .Argument<NonNullGraphType<StringGraphType>>("typeId", "the identifier of the benefitOption")
               .ResolveAsync(async context =>
               {
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));
                   return await context.ResolveRemoveBenefitOptionAsync(policyService, permissionValidator);
               });

            Field<CreatedStatusResultGraphType>()
                .Name("createPolicyUpdateRequest")
                .DeprecationReason("Use `addEndorsement` instead.")
                .AuthorizeWith("any")
                .Description("Request an policy update")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy id to update")
                .ResolveAsync(async ctx =>
                {
                    string tenantId = ctx.GetTenantIdFromToken();
                    string policyId = ctx.GetArgument<string>("policyId");
                    await permissionValidator.Authorize(ctx, "readPolicies", policyId);

                    Result<CreatedStatus> result = await policyService.CreateUpdateRequestAsync(tenantId, policyId, new CreatePolicyUpdateRequestCommand
                    {
                        CreatedById = ctx.GetLoginIdFromToken()
                    });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("cancelPolicy")
                .Description("Cancels an issued policy")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
                .Argument<NonNullGraphType<StringGraphType>>("reason", "the reason why the policy was cancelled")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string reason = context.GetArgument<string>("reason");
                    await permissionValidator.Authorize(context, "cancelPolicies", policyId);

                    Result result = await policyService.CancelPolicyAsync(tenantId, policyId, new CancelPolicyCommand
                    {
                        Reason = reason,
                        CancelledById = context.GetLoginIdFromToken()
                    });

                    return result;
                });

            //Field<ResultGraphType>()
            //    .Name("rejectPolicy")
            //    .Description("Rejects the policy")
            //    .AuthorizeWith("any")
            //    .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
            //    .Argument<NonNullGraphType<KeyValueInputGraphType>>("", "")
            //    .ResolveAsync(async context =>
            //    {
            //        await context.PopulateClaimsAsync(authService);
            //        string tenantId = context.GetTenantIdFromToken();
            //        string policyId = context.GetArgument<string>("policyId");
            //        string reason = context.GetArgument<string>("reason");
            //        await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

            //        Result result = await policyService.CancelPolicyAsync(tenantId, policyId, new CancelPolicyCommand
            //        {
            //            Reason = reason,
            //            CancelledById = context.GetLoginIdFromToken()
            //        });
            //    });


            Field<ResultGraphType>()
                .Name("addUpdatePolicyToRequest")
                .DeprecationReason("Use `updatePolicy2` instead.")
                .AuthorizeWith("any")
                .Description("Add an update policy to a request")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy id to update")
                .Argument<NonNullGraphType<StringGraphType>>("requestId", "The request id to update")
                .Argument<NonNullGraphType<UpdatePolicyInputGraphType>>("input", "the update policy input")
                .ResolveAsync(async ctx =>
                {
                    string tenantId = ctx.GetTenantIdFromToken();
                    string policyId = ctx.GetArgument<string>("policyId");
                    string requestId = ctx.GetArgument<string>("requestId");
                    await permissionValidator.Authorize(ctx, "readPolicies", policyId);

                    UpdatePolicyCommand command = ctx.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdatePolicyCommand>();
                    command.ModifiedById = ctx.GetLoginIdFromToken();
                    var requestCommand = new AddUpdatePolicyCommandToRequestCommand
                    {
                        RequestId = requestId,
                        Command = command
                    };

                    Result result = await policyService.UpdatePolicyAsync(tenantId, policyId, requestCommand);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("addUpdatePaymentInfoToRequest")
                .DeprecationReason("Use `updatePaymentInfo` instead.")
                .AuthorizeWith("any")
                .Description("Add an update payment info to a request")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy id to update")
                .Argument<NonNullGraphType<StringGraphType>>("requestId", "The request id to update")
                .Argument<NonNullGraphType<StringGraphType>>("paymentInfoId", "The identifier of the payment info to update")
                .Argument<NonNullGraphType<PaymentInfoToUpdateGraphType>>("input", "the update payment info input")
                .ResolveAsync(async ctx =>
                {
                    string tenantId = ctx.GetTenantIdFromToken();
                    string policyId = ctx.GetArgument<string>("policyId");
                    string requestId = ctx.GetArgument<string>("requestId");
                    string paymentInfoId = ctx.GetArgument<string>("paymentInfoId");
                    await permissionValidator.Authorize(ctx, "readPolicies", policyId);

                    UpdatePaymentInfoCommand command = ctx.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdatePaymentInfoCommand>();
                    command.ModifiedById = ctx.GetLoginIdFromToken();
                    command.Id = paymentInfoId;
                    var requestCommand = new AddUpdatePaymentInfoCommandToRequestCommand
                    {
                        RequestId = requestId,
                        Command = command
                    };

                    Result result = await policyService.UpdatePaymentInfoAsync(tenantId, policyId, requestCommand);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("addUpsertBenefitOptionToRequest")
                .DeprecationReason("Use `upsertBenefitOption` instead.")
                .AuthorizeWith("any")
                .Description("Add an upsert benefit option to a request")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy id to update")
                .Argument<StringGraphType>("offerId", "the identifier of the offer") //should be a NonNullGraphType but it is not right now as hiveup is using it
                .Argument<NonNullGraphType<StringGraphType>>("requestId", "The request id to update")
                .Argument<NonNullGraphType<BenefitOptionInputGraphType>>("input", "the benefit option input")
                .ResolveAsync(async ctx =>
                {
                    string tenantId = ctx.GetTenantIdFromToken();
                    string policyId = ctx.GetArgument<string>("policyId");
                    string offerId = ctx.GetArgument<string>("offerId");
                    string requestId = ctx.GetArgument<string>("requestId");
                    await permissionValidator.Authorize(ctx, new PermissionRequest("updateOffers", "writeOffers").WithTargetIds(policyId));

                    BenefitOptionInputGraph input = ctx.GetArgument<BenefitOptionInputGraph>("input");
                    var command = new UpsertBenefitOptionCommand
                    {
                        OfferId = offerId,
                        Key = input.Key,
                        TypeId = input.TypeId,

                        Value = input.Value != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                        InsuredId = input.InsuredId,
                        UpsertedById = ctx.GetLoginIdFromToken()
                    };

                    var requestCommand = new AddUpsertBenefitOptionCommandToRequestCommand
                    {
                        RequestId = requestId,
                        Command = command
                    };

                    Result result = await policyService.UpsertBenefitOptionAsync(tenantId, policyId, requestCommand);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("addUpdateBeneficiaryEligibilitiesToRequest")
                .DeprecationReason("Use `updateBeneficiaryEligibility` instead.")
                .AuthorizeWith("any")
                .Description("Add an update policy to a request")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy id to update")
                .Argument<NonNullGraphType<StringGraphType>>("requestId", "The request id to update")
                .Argument<NonNullGraphType<StringGraphType>>("beneficiaryEligibilityId", "The identifier of the beneficiaryEligibility to update")
                .Argument<NonNullGraphType<BeneficiaryEligibilityInputGraphType>>("input", "the update policy input")
                .ResolveAsync(async ctx =>
                {
                    string tenantId = ctx.GetTenantIdFromToken();
                    string policyId = ctx.GetArgument<string>("policyId");
                    string requestId = ctx.GetArgument<string>("requestId");
                    string beneficiaryEligibilityId = ctx.GetArgument<string>("beneficiaryEligibilityId");
                    await permissionValidator.Authorize(ctx, "readPolicies", policyId);

                    UpdateBeneficiaryEligibilityCommand command = ctx.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateBeneficiaryEligibilityCommand>();
                    command.Id = beneficiaryEligibilityId;
                    command.ModifiedById = ctx.GetLoginIdFromToken();

                    var requestCommand = new AddUpdateBeneficiaryEligibilityCommandToRequestCommand
                    {
                        RequestId = requestId,
                        Command = command
                    };

                    Result result = await policyService.UpdateBeneficiaryEligibilityAsync(tenantId, policyId, requestCommand);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("reviewPolicyUpdateRequest")
                .DeprecationReason("Use `acceptEndorsement` / `rejectEndorsement` instead.")
                .AuthorizeWith("any")
                .Description("Review a policy request")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy id update request to review")
                .Argument<NonNullGraphType<StringGraphType>>("requestId", "The request id to review")
                .Argument<NonNullGraphType<RequestPolicyReviewInputGraphType>>("input", "The input to review the request")
                .ResolveAsync(async ctx =>
                {
                    string tenantId = ctx.GetTenantIdFromToken();
                    string loginId = ctx.GetLoginIdFromToken();
                    string policyId = ctx.GetArgument<string>("policyId");
                    string requestId = ctx.GetArgument<string>("requestId");
                    PolicyRequestReview command = ctx.GetArgument<PolicyRequestReview>("input");
                    command.RequestId = requestId;
                    command.ReviewedById = loginId;

                    await permissionValidator.Authorize(ctx, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Result result = await policyService.ReviewUpdateRequestAsync(tenantId, policyId, requestId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("deletePolicyUpdateRequest")
                .DeprecationReason("Use `removeEndorsement` instead.")
                .AuthorizeWith("any")
                .Description("Delete policy update request")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy id")
                .Argument<NonNullGraphType<StringGraphType>>("requestId", "The request id to delete")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string requestId = context.GetArgument<string>("requestId");
                    string deletedById = context.GetLoginIdFromToken();

                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Result result = await policyService.RemoveUpdateRequestAsync(tenantId, policyId, requestId, deletedById);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("addLoadingToOffer")
                .Description("adds a loading to an offer")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
                .Argument<NonNullGraphType<LoadingInputGraphType>>("input", "the loading to be added")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));
                    string loginId = context.GetLoginIdFromToken();
                    LoadingInputGraph input = context.GetArgument<LoadingInputGraph>("input");

                    var command = new AddLoadingCommand
                    {
                        CalculationJsonLogic = input.CalculationJsonLogic != null ? JObject.Parse(input.CalculationJsonLogic) : null,
                        Code = input.Code,
                        Flat = input.Flat,
                        Ratio = input.Ratio,
                        OfferId = input.OfferId,
                        Order = input.Order
                    };

                    command.AddedById = loginId;

                    Result result = await policyService.AddLoadingToOfferAsync(tenantId, policyId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateLoadingOfOffer")
                .Description("updates a loading of an offer")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
                .Argument<StringGraphType>("offerId", "the identifier of the policy")
                .Argument<NonNullGraphType<StringGraphType>>("loadingId", "the identifier of the loading")
                .Argument<NonNullGraphType<LoadingInputGraphType>>("input", "the loading to be added")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));
                    string offerId = context.GetArgument<string>("offerId");
                    string loadingId = context.GetArgument<string>("loadingId");
                    string loginId = context.GetLoginIdFromToken();

                    LoadingInputGraph input = context.GetArgument<LoadingInputGraph>("input");
                    IDictionary<string, object> dict = Tools.ToDictionary<object>(input);
                    if (input.CalculationJsonLogic != null)
                    {
                        dict["CalculationJsonLogic"] = JObject.Parse(input.CalculationJsonLogic);
                    }

                    UpdateLoadingCommand command = dict.ToUpdateCommand<UpdateLoadingCommand>();
                    command.OfferId = offerId;
                    command.Id = loadingId;

                    Result result = await policyService.UpdateLoadingOfOfferAsync(tenantId, policyId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeLoadingFromOffer")
                .Description("removes a loading from an offer")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
                .Argument<StringGraphType>("offerId", "the identifier of the policy")
                .Argument<NonNullGraphType<StringGraphType>>("loadingId", "the identifier of the loading")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));
                    string offerId = context.GetArgument<string>("offerId");
                    string loadingId = context.GetArgument<string>("loadingId");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await policyService.RemoveLoadingFromOfferAsync(tenantId, policyId, new RemoveLoadingCommand { Id = loadingId, OfferId = offerId, RemovedById = removedById });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("addDiscountToOffer")
                .Description("adds a discount to an offer")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "The identfier of the offer")
                .Argument<NonNullGraphType<AddDiscountToOfferInputGraphType>>("input", "the discount to be added")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string offerId = context.GetArgument<string>("offerId");

                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));
                    string loginId = context.GetLoginIdFromToken();
                    DiscountInputGraph input = context.GetArgument<DiscountInputGraph>("input");
                    var command = new AddDiscountCommand
                    {
                        Name = input.Name,
                        CalculationJsonLogic = input.CalculationJsonLogic != null ? JObject.Parse(input.CalculationJsonLogic) : null,
                        Order = input.Order
                    };
                    command.OfferId = offerId;
                    command.AddedById = loginId;

                    Result result = await policyService.AddDiscountToOfferAsync(tenantId, policyId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateDiscountOfOffer")
                .Description("updates a discount of an offer")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
                .Argument<StringGraphType>("offerId", "the identifier of the policy")
                .Argument<NonNullGraphType<StringGraphType>>("discountId", "the identifier of the discount")
                .Argument<NonNullGraphType<UpdateDiscountInputGraphType>>("input", "the discount to be added")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));
                    string offerId = context.GetArgument<string>("offerId");
                    string discountId = context.GetArgument<string>("discountId");

                    DiscountInputGraph input = context.GetArgument<DiscountInputGraph>("input");
                    IDictionary<string, object> dict = Tools.ToDictionary<object>(input);
                    if (input.CalculationJsonLogic != null)
                    {
                        dict["CalculationJsonLogic"] = JObject.Parse(input.CalculationJsonLogic);
                    }

                    UpdateDiscountCommand command = dict.ToUpdateCommand<UpdateDiscountCommand>();
                    command.DiscountId = discountId;
                    command.OfferId = offerId;
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await policyService.UpdateDiscountOfOfferAsync(tenantId, policyId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeDiscountFromOffer")
                .Description("removes a discount from an offer")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
                .Argument<StringGraphType>("offerId", "the identifier of the policy")
                .Argument<NonNullGraphType<StringGraphType>>("discountId", "the identifier of the discount")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));
                    string offerId = context.GetArgument<string>("offerId");
                    string discountId = context.GetArgument<string>("discountId");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await policyService.RemoveDiscountFromOfferAsync(tenantId, policyId, new RemoveDiscountFromOfferCommand { DiscountId = discountId, OfferId = offerId, RemovedById = removedById });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("addExclusionToOffer")
                .Description("adds an exclusion to a policy")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
                .Argument<NonNullGraphType<ExclusionInputGraphType>>("input", "the exclusionInput")
                .ResolveAsync(async context =>
                {
                    string policyId = context.GetArgument<string>("policyId");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));
                    AddExclusionCommand command = context.GetArgument<AddExclusionCommand>("input");
                    command.AddedById = loginId;

                    Result result = await policyService.AddExclusionToOfferAsync(tenantId, policyId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeExclusionFromOffer")
                .Description("remove exclusion from policy")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
                .Argument<StringGraphType>("offerId", "the identifier of the policy")
                .Argument<NonNullGraphType<StringGraphType>>("exclusionId", "the identifier of the exclusion")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));
                    string offerId = context.GetArgument<string>("offerId");
                    string exclusionId = context.GetArgument<string>("exclusionId");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await policyService.RemoveExclusionFromOfferAsync(tenantId, policyId, new RemoveExclusionCommand
                    {
                        Id = exclusionId,
                        OfferId = offerId,
                        RemovedById = removedById
                    });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("rejectQuote")
                .Description("Rejects the quote")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the identifier of the policy")
                .Argument<ListGraphType<StringGraphType>>("codes", "the rejection codes")
                .Argument<StringGraphType>("remarks", "the remarks for the rejection")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    List<string> codes = context.GetArgument<List<string>>("codes");
                    string remarks = context.GetArgument<string>("remarks");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Result result = await policyService.RejectPolicyAsync(tenantId, policyId, new RejectQuoteCommand
                    {
                        Codes = codes,
                        Remarks = remarks,
                        RejectedById = context.GetLoginIdFromToken()
                    });

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
           .Name("addStakeholderToPolicy")
           .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
           .Argument<NonNullGraphType<AddStakeholderInputGraphType>>("input", "the stakeholder input")
           .AuthorizeWith("any")
           .ResolveAsync(async context =>
           {
               string tenantId = context.GetTenantIdFromToken();
               string loginId = context.GetLoginIdFromToken();
               string policyId = context.GetArgument<string>("policyId");
               await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

               Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
               if (policy == null)
                   return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
               else if (policy.IsIssued)
                   await permissionValidator.Authorize(context, "updateIssuedPolicies");

               AddStakeholderCommand command = context.GetArgument<AddStakeholderCommand>("input");
               command.AddedById = loginId;

               Result<string> result = await policyService.AddStakeholderToPolicyAsync(tenantId, policyId, command);
               return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
           });

            Field<ResultGraphType>()
               .Name("updateStakeholderOfPolicy")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<NonNullGraphType<StringGraphType>>("stakeholderId", "the stakeholder identifier")
               .Argument<NonNullGraphType<UpdateStakeholderInputGraphType>>("input", "the stakeholder input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string stakeholderId = context.GetArgument<string>("stakeholderId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                   else if (policy.IsIssued)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   UpdateStakeholderCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateStakeholderCommand>();
                   command.Id = stakeholderId;
                   command.ModifiedById = loginId;

                   return await policyService.UpdateStakeholderOfPolicyAsync(tenantId, policyId, command);
               });

            Field<ResultGraphType>()
               .Name("removeStakeholderFromPolicy")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<NonNullGraphType<StringGraphType>>("stakeholderId", "the stakeholder identifier")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string stakeholderId = context.GetArgument<string>("stakeholderId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                   else if (policy.IsIssued)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   var command = new RemoveStakeholderCommand
                   {
                       Id = stakeholderId,
                       RemovedById = loginId
                   };

                   return await policyService.RemoveStakeholderFromPolicyAsync(tenantId, policyId, command);
               });

            Field<CreatedStatusResultGraphType>()
            .Name("addClauseToPolicy")
            .Argument<NonNullGraphType<StringGraphType>>("policyId", "the case identifier")
            .Argument<NonNullGraphType<AddClauseToOfferInputGraphType>>("input", "the clause input")
            .AuthorizeWith("any")
            .ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();
                string loginId = context.GetLoginIdFromToken();
                string policyId = context.GetArgument<string>("policyId");
                await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                if (policy == null)
                    return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                else if (policy.IsIssued)
                    await permissionValidator.Authorize(context, "updateIssuedPolicies");

                AddClauseCommand command = context.GetArgument<AddClauseCommand>("input");
                command.AddedById = loginId;

                if (command.StoreTemplateByValue)
                {
                    IEnumerable<Template> templates = await templateService.GetAsync(tenantId, new Domain.QueryArguments { Where = new TemplateWhere { Id = command.TemplateId } });
                    command.Template = templates.FirstOrDefault();
                }

                Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, command.RenderParameters, out List<string> errors);
                if (errors.Any())
                    return new Result<CreatedStatus> { Status = "failure", Errors = errors };

                Result<string> result = await policyService.AddClauseToPolicyAsync(tenantId, policyId, command);

                return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
            });

            Field<ResultGraphType>()
                .Name("updateClauseOfPolicy")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("clauseId", "the clause identifier")
                .Argument<NonNullGraphType<UpdateClauseOfOfferInputGraphType>>("input", "the clause input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string clauseId = context.GetArgument<string>("clauseId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                    else if (policy.IsIssued)
                        await permissionValidator.Authorize(context, "updateIssuedPolicies");

                    UpdateClauseCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateClauseCommand>();
                    command.ClauseId = clauseId;
                    command.ModifiedById = loginId;

                    if (command.IsTemplateIdChanged && command.StoreTemplateByValue)
                    {
                        IEnumerable<Template> templates = await templateService.GetAsync(tenantId, new Domain.QueryArguments { Where = new TemplateWhere { Id = command.TemplateId } });
                        command.Template = templates.FirstOrDefault();
                    }

                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, command.RenderParameters, out List<string> errors);
                    if (errors.Any())
                        return new Result { Status = "failure", Errors = errors };

                    return await policyService.UpdateClauseOfPolicyAsync(tenantId, policyId, command);
                });

            Field<ResultGraphType>()
                .Name("removeClauseFromPolicy")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("clauseId", "the clause identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string clauseId = context.GetArgument<string>("clauseId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                    else if (policy.IsIssued)
                        await permissionValidator.Authorize(context, "updateIssuedPolicies");

                    var command = new RemoveClauseCommand
                    {
                        ClauseId = clauseId,
                        RemovedById = loginId
                    };

                    return await policyService.RemoveClauseFromPolicyAsync(tenantId, policyId, command);
                });

            Field<CreatedStatusResultGraphType>()
               .Name("policyClauseBatch")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "endorsement id")
               .Argument<NonNullGraphType<PolicyClauseCommandBatchInputGraphType>>("input", "the clause input")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));

                   PolicyClauseCommandBatchInput input = context.GetArgument<PolicyClauseCommandBatchInput>("input");

                   string[] templatesToStoreByValueIds = (input.AddClauseInputs ?? Enumerable.Empty<AddClauseCommand>())
                       .Where(x => x.StoreTemplateByValue)
                       .Select(x => x.TemplateId)
                       .Union((input.UpdateClauseInputs ?? Enumerable.Empty<UpdateClauseCommand>())
                           .Where(x => x.IsTemplateIdChanged && x.StoreTemplateByValue)
                           .Select(x => x.TemplateId))
                       .Where(x => x != null)
                       .Distinct()
                       .ToArray();

                   Dictionary<string, Template> templatesMap = new();
                   if (templatesToStoreByValueIds.Any())
                   {
                       IEnumerable<Template> templates = await templateService.GetAsync(tenantId,
                           new Domain.QueryArguments { Where = new TemplateWhere { Id_in = templatesToStoreByValueIds } });
                       templatesMap = templates.ToDictionary(x => x.Id, x => x);
                   }

                   input.AddClauseInputs?.ForEach(x =>
                   {
                       x.AddedById = loginId;
                       if (x.StoreTemplateByValue && x.TemplateId != null && templatesMap.ContainsKey(x.TemplateId))
                       {
                           x.Template = templatesMap[x.TemplateId];
                       }
                   });
                   input.UpdateClauseInputs?.ForEach(x =>
                   {
                       x.ModifiedById = loginId;
                       if (x.IsTemplateIdChanged && x.StoreTemplateByValue && x.TemplateId != null && templatesMap.ContainsKey(x.TemplateId))
                       {
                           x.Template = templatesMap[x.TemplateId];
                           x.IsTemplateChanged = true;
                       }
                   });
                   input.RemoveClauseInputs?.ForEach(x => x.RemovedById = loginId);

                   ClauseBatchCommand batch = new()
                   {
                       EndorsementId = endorsementId,
                       ById = loginId,
                       Timestamp = DateTime.UtcNow,
                       AddClauseCommands = input.AddClauseInputs,
                       UpdateClauseCommands = input.UpdateClauseInputs,
                       RemoveClauseCommands = input.RemoveClauseInputs,
                   };

                   return await policyService.PolicyClauseBatchAsync(tenantId, policyId, batch);
               });

            Field<ResultGraphType>()
                .Name("policyAddJacketsFromProposal")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<StringGraphType>("endorsementId", "endorsement id if needed")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string caseId = context.GetArgument<string>("caseId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));

                    IEnumerable<Case> proposals = await caseService.GetAsync(tenantId, new Domain.QueryArguments { Where = new CaseWhere { Id = caseId } });
                    Case proposal = proposals.FirstOrDefault();
                    JacketInstance[] jackets = proposal.Proposals?
                        .SelectMany(x => x.Basket?
                            .Where(x => x.Status == "Accepted")
                            .SelectMany(x => x.Jackets))
                            .Where(x => x != null)
                        .ToArray();

                    var command = new PolicyJacketInstanceBatchCommand
                    {
                        EndorsementId = endorsementId,
                        ById = loginId,
                        Timestamp = DateTime.UtcNow,

                        AddJacketInstanceCommands = jackets?.Select(j => new AddJacketInstanceCommand
                        {
                            AddedById = loginId,
                            StoreJacketByValue = j.Jacket != null,
                            Jacket = j.Jacket,
                            JacketId = j.Jacket.Id,
                            Order = j.Order
                        }).ToList()
                    };

                    Result result = await policyService.PolicyJacketBatchAsync(tenantId, policyId, command);
                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("policyAddClausesFromProposal")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<StringGraphType>("endorsementId", "endorsement id if needed")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string caseId = context.GetArgument<string>("caseId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));

                    IEnumerable<Case> proposals = await caseService.GetAsync(tenantId, new Domain.QueryArguments { Where = new CaseWhere { Id = caseId } });
                    Case proposal = proposals.FirstOrDefault();

                    Clause[] clauses = proposal?.Proposals?
                        .SelectMany(x => x.Basket?
                            .Where(x => x.Status == "Accepted")?
                            .SelectMany(x => x.Clauses))
                        .Where(x => x != null)
                        .ToArray();

                    var now = DateTime.UtcNow;
                    var command = new ClauseBatchCommand
                    {
                        EndorsementId = endorsementId,
                        ById = loginId,
                        Timestamp = now,
                        AddClauseCommands = clauses?.Select(c => new AddClauseCommand
                        {
                            AddedById = loginId,
                            Timestamp = now,
                            Order = c.Order,
                            TemplateId = c.TemplateId,
                            Template = c.Template,
                            HtmlOverride = c.HtmlOverride,
                            RenderParameters = c.RenderParameters,
                            Type = c.Type
                        }).ToList()
                    };

                    Result<CreatedStatus> result = await policyService.PolicyClauseBatchAsync(tenantId, policyId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("policyMembersBatch")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("endorsementId", "endorsement id")
                .Argument<NonNullGraphType<PolicyMembersBatchInputGraph>>("input", "the batch input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies", "writePolicyMembers"));

                    // GraphQL 2.0 doesn't support neither System.Text.Json nor Newtonsoft. Supported only in 3.0+
                    // So we have to manually do this
                    var batchObject = context.GetArgument<object>("input");
                    var batchObjectString = JsonConvert.SerializeObject(batchObject, mvcJsonOptions.Value.SerializerSettings);
                    var batch = JsonConvert.DeserializeObject<PolicyMembersBatchCommand>(batchObjectString, mvcJsonOptions.Value.SerializerSettings);

                    batch.Create?.ForEach(x =>
                    {
                        x.CreatedById = loginId;
                        x.PolicyId = policyId;
                        x.EndorsementId = endorsementId;
                    });
                    batch.Update?.ForEach(x =>
                    {
                        x.CreatedById = loginId;
                        x.PolicyId = policyId;
                        x.EndorsementId = endorsementId;
                    });
                    batch.Delete?.ForEach(x =>
                    {
                        x.CreatedById = loginId;
                        x.PolicyId = policyId;
                        x.EndorsementId = endorsementId;
                    });

                    var batchCommand = mapper.Map<CoverGo.Policies.Client.PolicyMembersBatchCommand>(batch);

                    var result = await policiesClient.PolicyMembers_BatchAsync(
                        tenantId,
                        batchCommand,
                        context.CancellationToken);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("policyMembersMovementLogItemMarkPrinted")
                .Description("Sets memebr movement log item marked as already printed")
                .Argument<NonNullGraphType<StringGraphType>>("itemId", "itemId to be marked as already printed")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string itemId = context.GetArgument<string>("itemId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));

                    return await policyMembersService.MovementLogItemMarkAsPrintedAsync(tenantId, itemId,
                        context.CancellationToken);
                });

            Field<ResultGraphType>()
                .Name("policyMembersAssignInternalCodes")
                .Description("Assigns internal codes for active policy members and it's dependents, supposed to be invoked during policy issuance process")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "Policy id")
                .Argument<ListGraphType<StringGraphType>>("policyMembersIds", "Use this parameter to regenerate internal codes for some specific policy members, e.g. if we have duplications")
                .Argument<StringGraphType>("endorsementId", "endorsement id")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    string[] memberIds = context.GetArgument<string[]>("policyMembersIds");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies", "writePolicyMembers"));

                    return await policyMembersService.AssignInternalCodesAsync(tenantId, new PolicyMembersAssignInternalCodesCommand
                    {
                        PolicyId = policyId,
                        MemberIds = memberIds?.ToList(),
                        EndorsementId = endorsementId
                    }, context.CancellationToken);
                });


            Field<ResultGraphType>()
             .Name("addAssociatedContractToPolicy")
             .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
             .Argument<NonNullGraphType<StringGraphType>>("associatedContractId", "the associated contract identifier")
             .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
             .AuthorizeWith("any")
             .ResolveAsync(async context =>
             {
                 string tenantId = context.GetTenantIdFromToken();
                 string loginId = context.GetLoginIdFromToken();
                 string policyId = context.GetArgument<string>("policyId");
                 string associatedContractId = context.GetArgument<string>("associatedContractId");
                 string endorsementId = context.GetArgument<string>("endorsementId");
                 await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                 Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                 if (policy == null)
                     return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy?.Id}' was not found." } };
                 else if (policy.IsIssued)
                     await permissionValidator.Authorize(context, "updateIssuedPolicies");

                 var command = new AddAssociatedContractCommand
                 {
                     ContractId = associatedContractId,
                     EndorsementId = endorsementId,
                     AddedById = loginId,
                 };

                 return await policyService.AddAssociatedContractToPolicyAsync(tenantId, policyId, command);
             });
            Field<ResultGraphType>()
             .Name("removeAssociatedContractFromPolicy")
             .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
             .Argument<NonNullGraphType<StringGraphType>>("associatedContractId", "the associated contract identifier")
             .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
             .AuthorizeWith("any")
             .ResolveAsync(async context =>
             {
                 string tenantId = context.GetTenantIdFromToken();
                 string loginId = context.GetLoginIdFromToken();
                 string policyId = context.GetArgument<string>("policyId");
                 string associatedContractId = context.GetArgument<string>("associatedContractId");
                 string endorsementId = context.GetArgument<string>("endorsementId");
                 await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                 Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                 if (policy == null)
                     return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                 else if (policy.IsIssued)
                     await permissionValidator.Authorize(context, "updateIssuedPolicies");

                 var command = new RemoveAssociatedContractCommand
                 {
                     Id = associatedContractId,
                     EndorsementId = endorsementId,
                     RemovedById = loginId,
                 };

                 return await policyService.RemoveAssociatedContractFromPolicyAsync(tenantId, policyId, command);
             });

            Field<CreatedStatusResultGraphType>()
                .Name("addEndorsement")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("type", "endorsement type")
                .Argument<StringGraphType>("reasonOfChange", "reason of change")
                .Argument<StringGraphType>("fields", "fields")
                .Argument<DateTimeOffsetGraphType>("effectiveDate", "reason of change")
                .Argument<StringGraphType>("cancellationMotive", "cancellation motive")
                .Argument<EndorsementSourceGraphType>("source", "ui source")
                .Argument<BooleanGraphType>("isDraft", "determines whether this is draft")
                .Argument<EndorsementMemberMovementVersionGraphType>("memberMovementVersion", "member movement version")
                .Argument<EndorsementUnderwritingStatusGraphType>("underwritingStatus", "underwriting status")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string type = context.GetArgument<string>("type");
                    string reasonOfChange = context.GetArgument<string>("reasonOfChange");
                    string fields = context.GetArgument<string>("fields");
                    DateTime? effectiveDate = context.GetArgument<DateTime?>("effectiveDate");
                    string cancellationMotive = context.GetArgument<string>("cancellationMotive");
                    EndorsementSource? source = context.GetArgument<EndorsementSource?>("source");
                    bool? isDraft = context.GetArgument<bool?>("isDraft");
                    MemberMovementVersion? memberMovementVersion =
                        context.GetArgument<MemberMovementVersion?>("memberMovementVersion");
                    EndorsementUnderwritingStatus? underwritingStatus =
                        context.GetArgument<EndorsementUnderwritingStatus?>("underwritingStatus");

                    if (type == EndorsementType.MemberMovement && isDraft == true)
                    {
                        await permissionValidator.Authorize(context, "writeMemberMovements:draft", policyId);

                        var userClaims = (context.UserContext as GraphQLUserContext).User.Claims;
                        var appIds = userClaims.Where(x => x.Type == "appId").Select(x => x.Value).ToArray();

                        if (appIds.Any(appId => appId.Contains("hr_portal", StringComparison.OrdinalIgnoreCase)) && source != Domain.Policies.EndorsementSource.HrPortal)
                            throw new ValidationError("", "authorization", "You are not authorized to run this.");

                        if (appIds.Any(appId => appId.Contains("admin_portal", StringComparison.OrdinalIgnoreCase)) && source != Domain.Policies.EndorsementSource.AdminPortal && source != null)
                            throw new ValidationError("", "authorization", "You are not authorized to run this.");
                    }
                    else
                    {
                        await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));
                    }

                    var result = await policyService.CheckForAddEndorsement(tenantId, type, new PolicyWhere { Id = policyId });
                    if (!result.IsSuccess) {
                        return Result<CreatedStatus>.Failure(result.Errors);
                    }

                    return await policyService.AddEndorsementAsync(tenantId, policyId,
                        new AddEndorsementCommand
                        {
                            AddedById = loginId,
                            Type = type,
                            ReasonOfChange = reasonOfChange,
                            Fields = fields,
                            EffectiveDate = effectiveDate,
                            CancellationMotive = cancellationMotive,
                            Source = source,
                            IsDraft = isDraft,
                            MemberMovementVersion = memberMovementVersion,
                            UnderwritingStatus = underwritingStatus
                        });
                });
            Field<ResultGraphType>()
                .Name("removeEndorsement")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<NonNullGraphType<StringGraphType>>("endorsementId", "the endorsement identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    var command = new RemoveCommand
                    {
                        Id = endorsementId,
                        RemovedById = loginId
                    };

                    return await policyService.RemoveEndorsementAsync(tenantId, policyId, command);
                });
            Field<ResultGraphType>()
                .Name("acceptEndorsement")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<NonNullGraphType<StringGraphType>>("endorsementId", "the endorsement identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    var command = new AcceptEndorsementCommand
                    {
                        Id = endorsementId,
                        ApprovedById = loginId
                    };

                    return await policyService.AcceptEndorsementAsync(tenantId, policyId, command);
                });

            Field<ResultGraphType>()
                .Name("updateEndorsement")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<NonNullGraphType<StringGraphType>>("endorsementId", "the endorsement identifier")
                .Argument<NonNullGraphType<UpdateEndorsementInputGraphType>>("input", "Update endorsement input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");

                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    UpdateEndorsementInputGraph input = context.GetArgument<UpdateEndorsementInputGraph>("input");

                    var command = new UpdateEndorsementCommand
                    {
                        Id = endorsementId,

                        IsReasonOfChangeChanged = input.ReasonOfChange != null || (input.IsReasonOfChangeChanged ?? false),
                        ReasonOfChange = input.ReasonOfChange,
                        ReasonRemarks = input.ReasonRemarks,

                        IsFieldsChanged = input.IsFieldsChanged ?? false,
                        Fields = input.IsFieldsChanged != null && input.IsFieldsChanged == true ? input.Fields : null,

                        IsEffectiveDateChanged = input.EffectiveDate != null || (input.IsEffectiveDateChanged ?? false),
                        EffectiveDate = input.EffectiveDate,

                        IsStatusChanged = input.Status != null || (input.IsStatusChanged ?? false),
                        Status = input.Status,

                        IsCancellationMotiveChanged = input.CancellationMotive != null || (input.IsCancellationMotiveChanged ?? false),
                        CancellationMotive = input.CancellationMotive,

                        IsUnderwritingStatusChanged = input.UnderwritingStatus != null || (input.IsUnderwritingStatusChanged ?? false),
                        UnderwritingStatus = input.UnderwritingStatus,

                        IsPaymentProcessStatusChanged = input.PaymentProcessStatus != null || (input.IsPaymentProcessStatusChanged ?? false),
                        PaymentProcessStatus = input.PaymentProcessStatus,

                        UpdatedById = loginId
                    };

                    return await policyService.UpdateEndorsementAsync(tenantId, policyId, command);
                });

            Field<ResultGraphType>()
                .Name("rejectEndorsement")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<NonNullGraphType<StringGraphType>>("endorsementId", "the endorsement identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    var command = new RejectEndorsementCommand
                    {
                        Id = endorsementId,
                        RejectedById = loginId
                    };

                    return await policyService.RejectEndorsementAsync(tenantId, policyId, command);
                });

            Field<CreatedStatusResultGraphType>()
                .Name("updatePolicy2")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                .Argument<NonNullGraphType<UpdatePolicyInputGraphType>>("input", "the policy input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                    else if (policy.IsIssued && endorsementId == null)
                        await permissionValidator.Authorize(context, "updateIssuedPolicies");

                    UpdatePolicyInputGraph updateInput = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdatePolicyInputGraph>();

                    await context.PatchOperationPermittedCheckAsync(authService, tenantId, updateInput.FieldsPatch,
                        policyId, "policy", () => Task.FromResult<JToken>(policy.Fields));

                    var updateCommand = new UpdatePolicyCommand
                    {
                        IssuerNumber = updateInput.IssuerNumber,
                        IsIssuerNumberChanged = updateInput.IsIssuerNumberChanged,
                        Status = updateInput.Status,
                        IsStatusChanged = updateInput.IsStatusChanged,
                        HolderId = updateInput.HolderId,
                        IsHolderIdChanged = updateInput.IsHolderIdChanged,
                        InsuredIds = updateInput.InsuredIds,
                        IsInsuredIdsChanged = updateInput.IsInsuredIdsChanged,
                        ContractHolderIndividual = updateInput.ContractHolderIndividual,
                        IsContractHolderIndividualChanged = updateInput.IsContractHolderIndividualChanged,
                        ContractHolderCompany = updateInput.ContractHolderCompany,
                        IsContractHolderCompanyChanged = updateInput.IsContractHolderCompanyChanged,
                        IssueDate = updateInput.IssueDate,
                        IsIssueDateChanged = updateInput.IsIssueDateChanged,
                        StartDate = updateInput.StartDate,
                        IsStartDateChanged = updateInput.IsStartDateChanged,
                        EndDate = updateInput.EndDate,
                        IsEndDateChanged = updateInput.IsEndDateChanged,
                        Description = updateInput.Description,
                        IsDescriptionChanged = updateInput.IsDescriptionChanged,
                        ReferralCode = updateInput.ReferralCode,
                        IsReferralCodeChanged = updateInput.IsReferralCodeChanged,
                        Values = updateInput.Values != null ? JToken.FromObject(updateInput.Values?.ToDictionary(x => x.Key, x => x.Value.GetValue())) : null,
                        IsValuesChanged = updateInput.IsValuesChanged,
                        Source = updateInput.Source,
                        IsSourceChanged = updateInput.IsSourceChanged,
                        Premium = updateInput.Premium,
                        IsPremiumChanged = updateInput.IsPremiumChanged,
                        ProductId = updateInput.ProductId,
                        IsProductIdChanged = updateInput.IsProductIdChanged,
                        Fields = updateInput.Fields,
                        FieldsPatch = updateInput.FieldsPatch,
                        IsFieldsChanged = updateInput.IsFieldsChanged,
                        FieldsSchemaId = updateInput.FieldsSchemaId,
                        IsFieldsSchemaIdChanged = updateInput.IsFieldsSchemaIdChanged,
                        IsLapseReasonChanged = updateInput.IsLapseReasonChanged,
                        ExtraFields = updateInput.ExtraFields,
                        IsExtraFieldsChanged = updateInput.IsExtraFieldsChanged,
                        LapseReason = updateInput.LapseReason
                    };

                    updateCommand.ModifiedById = loginId;
                    updateCommand.EndorsementId = endorsementId;

                    if (updateCommand.IsPremiumChanged)
                        updateCommand.IsPremiumOverridden = true;

                    return await policyService.UpdatePolicy2Async(tenantId, policyId, updateCommand);
                });

            Field<CreatedStatusResultGraphType>()
                .Name("updatePolicyProduct")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                .Argument<NonNullGraphType<ProductIdInputGraphType>>("input", "a productId input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policyId}' was not found." } };
                    else if (policy.IsIssued && endorsementId == null)
                        await permissionValidator.Authorize(context, "updateIssuedPolicies");

                    var updateInput = context.GetArgument<ProductId>("input");

                    UpdatePolicyProductCommand updateCommand = new()
                    {
                        ProductId = updateInput
                    };

                    updateCommand.ModifiedById = loginId;
                    updateCommand.EndorsementId = endorsementId;

                    return await policyService.UpdatePolicyProductAsync(tenantId, policyId, updateCommand);
                });

            Field<CreatedStatusResultGraphType>()
              .Name("addDiscount")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<AddDiscountToOfferInputGraphType>>("input", "the discount input")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  DiscountInputGraph input = context.GetArgument<DiscountInputGraph>("input");
                  var command = new AddDiscountCommand
                  {
                      Name = input.Name,
                      CalculationJsonLogic = input.CalculationJsonLogic != null ? JObject.Parse(input.CalculationJsonLogic) : null,
                      Order = input.Order
                  };
                  command.AddedById = loginId;
                  command.EndorsementId = endorsementId;

                  return await policyService.AddDiscountAsync(tenantId, policyId, command);
              });
            Field<CreatedStatusResultGraphType>()
               .Name("updateDiscount")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
               .Argument<NonNullGraphType<StringGraphType>>("discountId", "the discount identifier")
               .Argument<NonNullGraphType<UpdateDiscountInputGraphType>>("input", "the discount input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   string discountId = context.GetArgument<string>("discountId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' not found." } };
                   else if (policy.IsIssued && endorsementId == null)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   DiscountInputGraph input = context.GetArgument<DiscountInputGraph>("input");
                   IDictionary<string, object> dict = Tools.ToDictionary<object>(input);
                   if (input.CalculationJsonLogic != null)
                   {
                       dict["CalculationJsonLogic"] = JObject.Parse(input.CalculationJsonLogic);
                   }

                   UpdateDiscountCommand command = dict.ToUpdateCommand<UpdateDiscountCommand>();
                   command.ModifiedById = loginId;
                   command.DiscountId = discountId;
                   command.EndorsementId = endorsementId;

                   return await policyService.UpdateDiscountAsync(tenantId, policyId, command);
               });
            Field<CreatedStatusResultGraphType>()
               .Name("removeDiscount")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
               .Argument<NonNullGraphType<StringGraphType>>("discountId", "the discount identifier")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   string discountId = context.GetArgument<string>("discountId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                   else if (policy.IsIssued && endorsementId == null)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   var command = new RemoveCommand
                   {
                       EndorsementId = endorsementId,
                       Id = discountId,
                       RemovedById = loginId
                   };

                   return await policyService.RemoveDiscountAsync(tenantId, policyId, command);
               });

            Field<CreatedStatusResultGraphType>()
              .Name("addLoading")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<LoadingInputGraphType>>("input", "the loading input")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  LoadingInputGraph input = context.GetArgument<LoadingInputGraph>("input");
                  var command = new AddLoadingCommand
                  {
                      CalculationJsonLogic = input.CalculationJsonLogic != null ? JObject.Parse(input.CalculationJsonLogic) : null,
                      Code = input.Code,
                      Flat = input.Flat,
                      Ratio = input.Ratio,
                      OfferId = input.OfferId,
                      Order = input.Order
                  };
                  command.AddedById = loginId;
                  command.EndorsementId = endorsementId;

                  return await policyService.AddLoadingAsync(tenantId, policyId, command);
              });
            Field<CreatedStatusResultGraphType>()
               .Name("updateLoading")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
               .Argument<NonNullGraphType<StringGraphType>>("loadingId", "the loading identifier")
               .Argument<NonNullGraphType<LoadingInputGraphType>>("input", "the loading input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   string loadingId = context.GetArgument<string>("loadingId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' not found." } };
                   else if (policy.IsIssued && endorsementId == null)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   LoadingInputGraph input = context.GetArgument<LoadingInputGraph>("input");
                   IDictionary<string, object> dict = Tools.ToDictionary<object>(input);
                   if (input.CalculationJsonLogic != null)
                   {
                       dict["CalculationJsonLogic"] = JObject.Parse(input.CalculationJsonLogic);
                   }

                   UpdateLoadingCommand command = dict.ToUpdateCommand<UpdateLoadingCommand>();
                   command.ModifiedById = loginId;
                   command.Id = loadingId;
                   command.EndorsementId = endorsementId;

                   return await policyService.UpdateLoadingAsync(tenantId, policyId, command);
               });
            Field<CreatedStatusResultGraphType>()
               .Name("removeLoading")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
               .Argument<NonNullGraphType<StringGraphType>>("loadingId", "the loading identifier")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   string loadingId = context.GetArgument<string>("loadingId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                   else if (policy.IsIssued && endorsementId == null)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   var command = new RemoveCommand
                   {
                       EndorsementId = endorsementId,
                       Id = loadingId,
                       RemovedById = loginId
                   };

                   return await policyService.RemoveLoadingAsync(tenantId, policyId, command);
               });

            Field<CreatedStatusResultGraphType>()
             .Name("addExclusionToPolicy")
             .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
             .Argument<NonNullGraphType<ExclusionInputGraphType>>("input", "the exclusion input")
             .AuthorizeWith("any")
             .ResolveAsync(async context =>
             {
                 string tenantId = context.GetTenantIdFromToken();
                 string loginId = context.GetLoginIdFromToken();
                 string policyId = context.GetArgument<string>("policyId");
                 await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                 AddExclusionCommand command = context.GetArgument<AddExclusionCommand>("input");
                 command.AddedById = loginId;

                 return await policyService.AddExclusionAsync(tenantId, policyId, command);
             });

            Field<ResultGraphType>()
                .Name("removeExclusionFromPolicy")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<NonNullGraphType<StringGraphType>>("exclusionId", "the loading identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string exclusionId = context.GetArgument<string>("exclusionId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    var command = new RemoveCommand
                    {
                        Id = exclusionId,
                        RemovedById = loginId
                    };

                    return await policyService.RemoveExclusionAsync(tenantId, policyId, command);
                });

            Field<CreatedStatusResultGraphType>()
                .Name("addPolicyFact")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                .Argument<NonNullGraphType<AddFactInputGraphType>>("input", "the fact input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                    else if (policy.IsIssued && endorsementId == null)
                        await permissionValidator.Authorize(context, "updateIssuedPolicies");

                    AddFactInputGraph input = context.GetArgument<AddFactInputGraph>("input");

                    var command = new AddFactCommand
                    {
                        EndorsementId = endorsementId,
                        Type = input.Type,
                        Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                        AddedById = loginId
                    };

                    return await policyService.AddPolicyFactAsync(tenantId, policyId, command);
                });
            Field<CreatedStatusResultGraphType>()
                .Name("updatePolicyFact")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                .Argument<NonNullGraphType<StringGraphType>>("factId", "the fact identifier")
                .Argument<NonNullGraphType<UpdateFactInputGraphType>>("input", "the fact input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    string factId = context.GetArgument<string>("factId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                    else if (policy.IsIssued && endorsementId == null)
                        await permissionValidator.Authorize(context, "updateIssuedPolicies");

                    UpdateFactInputGraph input = context.GetArgument<UpdateFactInputGraph>("input");

                    var updateFactCommand = new UpdateFactCommand
                    {
                        EndorsementId = endorsementId,
                        Id = factId,
                        Type = input.Type,
                        Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                        ModifiedById = loginId
                    };

                    return await policyService.UpdatePolicyFactAsync(tenantId, policyId, updateFactCommand);
                });
            Field<CreatedStatusResultGraphType>()
                 .Name("removePolicyFact")
                 .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                 .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                 .Argument<NonNullGraphType<StringGraphType>>("factId", "the fact identifier")
                 .AuthorizeWith("any")
                 .ResolveAsync(async context =>
                 {
                     string tenantId = context.GetTenantIdFromToken();
                     string loginId = context.GetLoginIdFromToken();
                     string policyId = context.GetArgument<string>("policyId");
                     string endorsementId = context.GetArgument<string>("endorsementId");
                     string factId = context.GetArgument<string>("factId");
                     await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                     Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                     if (policy == null)
                         return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                     else if (policy.IsIssued && endorsementId == null)
                         await permissionValidator.Authorize(context, "updateIssuedPolicies");

                     var command = new RemoveCommand
                     {
                         EndorsementId = endorsementId,
                         Id = factId,
                         RemovedById = loginId
                     };

                     return await policyService.RemovePolicyFactAsync(tenantId, policyId, command);
                 });
            Field<ResultGraphType>()
               .Name("policyFactBatch")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<NonNullGraphType<FactCommandBatchInputGraphType>>("input", "the fact input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' not found." } };
                   else if (policy.IsIssued)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   FactCommandBatchInputGraph input = context.GetArgument<FactCommandBatchInputGraph>("input");

                   var addFactCommands = input.AddFactInputs?.Select(i => i.ToCommand(loginId))?.ToList();

                   var updateFactCommands = input.UpdateFactInputs?.Select(i => i.ToCommand(loginId))?.ToList();

                   var factCommandBatch = new FactCommandBatch
                   {
                       AddFactCommands = addFactCommands,
                       UpdateFactCommands = updateFactCommands
                   };

                   return await policyService.PolicyFactBatch(tenantId, policyId, factCommandBatch);
               });

            Field<CreatedStatusResultGraphType>()
              .Name("upsertBenefitOptionOfPolicy")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<BenefitOptionInputGraphType>>("input", "the information of the  benefit option to update")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  BenefitOptionInputGraph input = context.GetArgument<BenefitOptionInputGraph>("input");

                  UpsertBenefitOptionCommand command = BenefitOptionInputGraph.ToCommand(input, null, null, loginId);
                  command.EndorsementId = endorsementId;

                  return await policyService.UpsertBenefitOptionOfPolicyAsync(tenantId, policyId, command);
              });
            Field<CreatedStatusResultGraphType>()
                 .Name("removeBenefitOptionFromPolicy")
                 .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                 .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                 .Argument<NonNullGraphType<StringGraphType>>("typeId", "the benefitOption identifier")
                 .AuthorizeWith("any")
                 .ResolveAsync(async context =>
                 {
                     string tenantId = context.GetTenantIdFromToken();
                     string loginId = context.GetLoginIdFromToken();
                     string policyId = context.GetArgument<string>("policyId");
                     string endorsementId = context.GetArgument<string>("endorsementId");
                     string typeId = context.GetArgument<string>("typeId");
                     await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                     Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                     if (policy == null)
                         return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                     else if (policy.IsIssued && endorsementId == null)
                         await permissionValidator.Authorize(context, "updateIssuedPolicies");

                     var command = new RemoveBenefitOptionCommand
                     {
                         EndorsementId = endorsementId,
                         TypeId = typeId,
                         RemovedById = loginId
                     };

                     return await policyService.RemoveBenefitOptionFromPolicyAsync(tenantId, policyId, command);
                 });

            Field<CreatedStatusResultGraphType>()
              .Name("addOtherContractHolder")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<StringGraphType>>("entityId", "the entityId identifier")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  string entityId = context.GetArgument<string>("entityId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  var command = new AddEntityCommand
                  {
                      EndorsementId = endorsementId,
                      EntityId = entityId,
                      AddedById = loginId
                  };

                  return await policyService.AddOtherContractHolderAsync(tenantId, policyId, command);
              });
            Field<CreatedStatusResultGraphType>()
              .Name("addContractInsured")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<StringGraphType>>("entityId", "the entityId identifier")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  string entityId = context.GetArgument<string>("entityId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  var result = await policyService.CheckIssuedStatusAsync(tenantId, new PolicyWhere { Id = policyId });
                    if (!result.IsSuccess)
                        return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policyId}' was not found." } };
                    else if (result.Value && endorsementId == null)
                        await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  var command = new AddEntityCommand
                  {
                      EndorsementId = endorsementId,
                      EntityId = entityId,
                      AddedById = loginId
                  };

                  return await policyService.AddContractInsuredAsync(tenantId, policyId, command);
              });
            Field<CreatedStatusResultGraphType>()
             .Name("removeOtherContractHolder")
             .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
             .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
             .Argument<NonNullGraphType<StringGraphType>>("entityId", "the entityId identifier")
             .AuthorizeWith("any")
             .ResolveAsync(async context =>
             {
                 string tenantId = context.GetTenantIdFromToken();
                 string loginId = context.GetLoginIdFromToken();
                 string policyId = context.GetArgument<string>("policyId");
                 string endorsementId = context.GetArgument<string>("endorsementId");
                 string entityId = context.GetArgument<string>("entityId");
                 await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                 Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                 if (policy == null)
                     return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                 else if (policy.IsIssued && endorsementId == null)
                     await permissionValidator.Authorize(context, "updateIssuedPolicies");

                 var command = new RemoveCommand
                 {
                     EndorsementId = endorsementId,
                     Id = entityId,
                     RemovedById = loginId
                 };

                 return await policyService.RemoveOtherContractHolderAsync(tenantId, policyId, command);
             });
            Field<CreatedStatusResultGraphType>()
            .Name("removeContractInsured")
            .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
            .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
            .Argument<NonNullGraphType<StringGraphType>>("entityId", "the entityId identifier")
            .AuthorizeWith("any")
            .ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();
                string loginId = context.GetLoginIdFromToken();
                string policyId = context.GetArgument<string>("policyId");
                string endorsementId = context.GetArgument<string>("endorsementId");
                string entityId = context.GetArgument<string>("entityId");
                await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                if (policy == null)
                    return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                else if (policy.IsIssued && endorsementId == null)
                    await permissionValidator.Authorize(context, "updateIssuedPolicies");

                var command = new RemoveCommand
                {
                    EndorsementId = endorsementId,
                    Id = entityId,
                    RemovedById = loginId
                };

                return await policyService.RemoveContractInsuredAsync(tenantId, policyId, command);
            });

            Field<CreatedStatusResultGraphType>()
               .Name("updateOtherContractHolder")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
               .Argument<NonNullGraphType<StringGraphType>>("entityId", "the entity identifier")
               .Argument<UpdateContractIndividualInputGraphType>("individualInput", "the individual input")
               .Argument<UpdateContractCompanyInputGraphType>("companyInput", "the company input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string entityId = context.GetArgument<string>("entityId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                   else if (policy.IsIssued && endorsementId == null)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   Dictionary<string, object> individualInput = context.GetArgument<Dictionary<string, object>>("individualInput");
                   if (individualInput != null)
                   {
                       UpdateIndividualCommand command = individualInput.ToUpdateCommand<UpdateIndividualCommand>();
                       command.EntityId = entityId;
                       command.EndorsementId = endorsementId;
                       command.ModifiedById = loginId;

                       return await policyService.UpdateOtherContractHolderIndividualAsync(tenantId, policyId, command);
                   }

                   Dictionary<string, object> companyInput = context.GetArgument<Dictionary<string, object>>("companyInput");
                   if (companyInput != null)
                   {
                       UpdateCompanyCommand command = companyInput.ToUpdateCommand<UpdateCompanyCommand>();
                       command.EntityId = entityId;
                       command.EndorsementId = endorsementId;
                       command.ModifiedById = loginId;

                       return await policyService.UpdateOtherContractHolderCompanyAsync(tenantId, policyId, command);
                   }

                   return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { "No inputs found." } };
               });
            Field<CreatedStatusResultGraphType>()
               .Name("updateContractInsured")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
               .Argument<NonNullGraphType<StringGraphType>>("entityId", "the entity identifier")
               .Argument<UpdateContractIndividualInputGraphType>("individualInput", "the individual input")
               .Argument<UpdateContractCompanyInputGraphType>("companyInput", "the company input")
               .Argument<UpdateContractObjectInputGraphType>("objectInput", "the object input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string entityId = context.GetArgument<string>("entityId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                   else if (policy.IsIssued && endorsementId == null)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   Dictionary<string, object> individualInput = context.GetArgument<Dictionary<string, object>>("individualInput");
                   if (individualInput != null)
                   {
                       UpdateIndividualCommand command = individualInput.ToUpdateCommand<UpdateIndividualCommand>();
                       command.EntityId = entityId;
                       command.EndorsementId = endorsementId;
                       command.ModifiedById = loginId;

                       return await policyService.UpdateContractInsuredIndividual2Async(tenantId, policyId, command);
                   }

                   Dictionary<string, object> companyInput = context.GetArgument<Dictionary<string, object>>("companyInput");
                   if (companyInput != null)
                   {
                       UpdateCompanyCommand command = companyInput.ToUpdateCommand<UpdateCompanyCommand>();
                       command.EntityId = entityId;
                       command.EndorsementId = endorsementId;
                       command.ModifiedById = loginId;

                       return await policyService.UpdateContractInsuredCompany2Async(tenantId, policyId, command);
                   }

                   Dictionary<string, object> objectInput = context.GetArgument<Dictionary<string, object>>("objectInput");
                   if (objectInput != null)
                   {
                       UpdateObjectCommand command = objectInput.ToUpdateCommand<UpdateObjectCommand>();
                       command.EntityId = entityId;
                       command.EndorsementId = endorsementId;
                       command.ModifiedById = loginId;

                       return await policyService.UpdateContractInsuredObject2Async(tenantId, policyId, command);
                   }

                   return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { "No inputs found." } };
               });

            Field<ResultGraphType>()
              .Name("contractHolderFactBatch")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<NonNullGraphType<FactCommandBatchInputGraphType>>("input", "the fact input")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' not found." } };
                  else if (policy.IsIssued)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  FactCommandBatchInputGraph input = context.GetArgument<FactCommandBatchInputGraph>("input");

                  var addFactCommands = input.AddFactInputs?.Select(i => i.ToCommand(loginId))?.ToList();

                  var updateFactCommands = input.UpdateFactInputs?.Select(i => i.ToCommand(loginId))?.ToList();

                  var factCommandBatch = new FactCommandBatch
                  {
                      AddFactCommands = addFactCommands,
                      UpdateFactCommands = updateFactCommands
                  };

                  return await policyService.AddContractHolderFactBatch(tenantId, policyId, factCommandBatch);
              });
            Field<CreatedStatusResultGraphType>()
                .Name("addContractHolderFact")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                .Argument<NonNullGraphType<AddFactInputGraphType>>("input", "the fact input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                    else if (policy.IsIssued && endorsementId == null)
                        await permissionValidator.Authorize(context, "updateIssuedPolicies");

                    AddFactInputGraph input = context.GetArgument<AddFactInputGraph>("input");

                    var command = new AddFactCommand
                    {
                        EndorsementId = endorsementId,
                        Type = input.Type,
                        Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                        AddedById = loginId
                    };

                    return await policyService.AddContractHolderFactAsync(tenantId, policyId, command);
                });
            Field<CreatedStatusResultGraphType>()
                .Name("addOtherContractHolderFact")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
                .Argument<NonNullGraphType<AddFactInputGraphType>>("input", "the fact input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();
                string loginId = context.GetLoginIdFromToken();
                string policyId = context.GetArgument<string>("policyId");
                string endorsementId = context.GetArgument<string>("endorsementId");
                string entityId = context.GetArgument<string>("entityId");
                await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                if (policy == null)
                    return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                else if (policy.IsIssued && endorsementId == null)
                    await permissionValidator.Authorize(context, "updateIssuedPolicies");

                AddFactInputGraph input = context.GetArgument<AddFactInputGraph>("input");

                var command = new AddFactCommand
                {
                    EndorsementId = endorsementId,
                    EntityId = entityId,
                    Type = input.Type,
                    Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                    AddedById = loginId
                };

                return await policyService.AddOtherContractHolderFactAsync(tenantId, policyId, command);
            });
            Field<CreatedStatusResultGraphType>()
              .Name("addContractInsuredFact")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
              .Argument<NonNullGraphType<AddFactInputGraphType>>("input", "the fact input")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  string entityId = context.GetArgument<string>("entityId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  AddFactInputGraph input = context.GetArgument<AddFactInputGraph>("input");

                  var command = new AddFactCommand
                  {
                      EndorsementId = endorsementId,
                      EntityId = entityId,
                      Type = input.Type,
                      Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                      AddedById = loginId
                  };

                  return await policyService.AddContractInsuredFactAsync(tenantId, policyId, command);
              });
            Field<CreatedStatusResultGraphType>()
                .Name("updateContractHolderFact")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                .Argument<NonNullGraphType<StringGraphType>>("factId", "the fact identifier")
                .Argument<NonNullGraphType<UpdateFactInputGraphType>>("input", "the fact input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    string factId = context.GetArgument<string>("factId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                    else if (policy.IsIssued && endorsementId == null)
                        await permissionValidator.Authorize(context, "updateIssuedPolicies");

                    UpdateFactInputGraph input = context.GetArgument<UpdateFactInputGraph>("input");

                    var updateFactCommand = new UpdateFactCommand
                    {
                        EndorsementId = endorsementId,
                        Id = factId,
                        Type = input.Type,
                        Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                        ModifiedById = loginId
                    };

                    return await policyService.UpdateContractHolderFactAsync(tenantId, policyId, updateFactCommand);
                });
            Field<CreatedStatusResultGraphType>()
                .Name("updateOtherContractHolderFact")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                .Argument<NonNullGraphType<StringGraphType>>("factId", "the fact identifier")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
                .Argument<NonNullGraphType<UpdateFactInputGraphType>>("input", "the fact input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();
                string loginId = context.GetLoginIdFromToken();
                string policyId = context.GetArgument<string>("policyId");
                string endorsementId = context.GetArgument<string>("endorsementId");
                string entityId = context.GetArgument<string>("entityId");
                string factId = context.GetArgument<string>("factId");
                await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                if (policy == null)
                    return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                else if (policy.IsIssued && endorsementId == null)
                    await permissionValidator.Authorize(context, "updateIssuedPolicies");

                UpdateFactInputGraph input = context.GetArgument<UpdateFactInputGraph>("input");

                var updateFactCommand = new UpdateFactCommand
                {
                    EndorsementId = endorsementId,
                    EntityId = entityId,
                    Id = factId,
                    Type = input.Type,
                    Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                    ModifiedById = loginId
                };

                return await policyService.UpdateOtherContractHolderFactAsync(tenantId, policyId, updateFactCommand);
            });
            Field<CreatedStatusResultGraphType>()
              .Name("updateContractInsuredFact")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<StringGraphType>>("factId", "the fact identifier")
              .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
              .Argument<NonNullGraphType<UpdateFactInputGraphType>>("input", "the fact input")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  string entityId = context.GetArgument<string>("entityId");
                  string factId = context.GetArgument<string>("factId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  UpdateFactInputGraph input = context.GetArgument<UpdateFactInputGraph>("input");

                  var updateFactCommand = new UpdateFactCommand
                  {
                      EndorsementId = endorsementId,
                      EntityId = entityId,
                      Id = factId,
                      Type = input.Type,
                      Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                      ModifiedById = loginId
                  };

                  return await policyService.UpdateContractInsuredFact2Async(tenantId, policyId, updateFactCommand);
              });
            Field<CreatedStatusResultGraphType>()
            .Name("removeContractHolderFact")
            .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
            .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
            .Argument<NonNullGraphType<StringGraphType>>("factId", "the fact identifier")
            .AuthorizeWith("any")
            .ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();
                string loginId = context.GetLoginIdFromToken();
                string policyId = context.GetArgument<string>("policyId");
                string endorsementId = context.GetArgument<string>("endorsementId");
                string factId = context.GetArgument<string>("factId");
                await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                if (policy == null)
                    return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                else if (policy.IsIssued && endorsementId == null)
                    await permissionValidator.Authorize(context, "updateIssuedPolicies");

                var command = new RemoveCommand
                {
                    EndorsementId = endorsementId,
                    Id = factId,
                    RemovedById = loginId
                };

                return await policyService.RemoveContractHolderFactAsync(tenantId, policyId, command);
            });
            Field<CreatedStatusResultGraphType>()
             .Name("removeOtherContractHolderFact")
             .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
             .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
             .Argument<NonNullGraphType<StringGraphType>>("entityId", "the entity identifier")
             .Argument<NonNullGraphType<StringGraphType>>("factId", "the fact identifier")
             .AuthorizeWith("any")
             .ResolveAsync(async context =>
             {
                 string tenantId = context.GetTenantIdFromToken();
                 string loginId = context.GetLoginIdFromToken();
                 string policyId = context.GetArgument<string>("policyId");
                 string endorsementId = context.GetArgument<string>("endorsementId");
                 string entityId = context.GetArgument<string>("entityId");
                 string factId = context.GetArgument<string>("factId");
                 await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                 Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                 if (policy == null)
                     return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                 else if (policy.IsIssued && endorsementId == null)
                     await permissionValidator.Authorize(context, "updateIssuedPolicies");

                 var command = new RemoveEntityPrimitiveCommand
                 {
                     EndorsementId = endorsementId,
                     EntityId = entityId,
                     PrimitiveId = factId,
                     RemovedById = loginId
                 };

                 return await policyService.RemoveOtherContractHolderFactAsync(tenantId, policyId, command);
             });
            Field<CreatedStatusResultGraphType>()
              .Name("removeContractInsuredFact")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<StringGraphType>>("entityId", "the entity identifier")
              .Argument<NonNullGraphType<StringGraphType>>("factId", "the fact identifier")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  string entityId = context.GetArgument<string>("entityId");
                  string factId = context.GetArgument<string>("factId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  var command = new RemoveEntityPrimitiveCommand
                  {
                      EndorsementId = endorsementId,
                      EntityId = entityId,
                      PrimitiveId = factId,
                      RemovedById = loginId
                  };

                  return await policyService.RemoveContractInsuredFactAsync(tenantId, policyId, command);
              });

            Field<CreatedStatusResultGraphType>()
               .Name("addContractHolderIdentity")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
               .Argument<NonNullGraphType<AddIdentityInputGraphType>>("input", "the clause input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                   else if (policy.IsIssued && endorsementId == null)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   AddIdentityCommand command = context.GetArgument<AddIdentityCommand>("input");
                   command.EndorsementId = endorsementId;
                   command.AddedById = loginId;

                   return await policyService.AddContractHolderIdentityAsync(tenantId, policyId, command);
               });
            Field<CreatedStatusResultGraphType>()
                .Name("addOtherContractHolderIdentity")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
                .Argument<NonNullGraphType<AddIdentityInputGraphType>>("input", "the clause input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    string entityId = context.GetArgument<string>("entityId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                    else if (policy.IsIssued && endorsementId == null)
                        await permissionValidator.Authorize(context, "updateIssuedPolicies");

                    AddIdentityCommand command = context.GetArgument<AddIdentityCommand>("input");
                    command.EntityId = entityId;
                    command.EndorsementId = endorsementId;
                    command.AddedById = loginId;

                    return await policyService.AddOtherContractHolderIdentityAsync(tenantId, policyId, command);
                });
            Field<CreatedStatusResultGraphType>()
              .Name("addContractInsuredIdentity")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
              .Argument<NonNullGraphType<AddIdentityInputGraphType>>("input", "the clause input")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  string entityId = context.GetArgument<string>("entityId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  AddIdentityCommand command = context.GetArgument<AddIdentityCommand>("input");
                  command.EntityId = entityId;
                  command.EndorsementId = endorsementId;
                  command.AddedById = loginId;

                  return await policyService.AddContractInsuredIdentityAsync(tenantId, policyId, command);
              });
            Field<CreatedStatusResultGraphType>()
               .Name("updateContractHolderIdentity")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
               .Argument<NonNullGraphType<StringGraphType>>("identityId", "the identity identifier")
               .Argument<NonNullGraphType<UpdateIdentityInputGraphType>>("input", "the clause input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   string identityId = context.GetArgument<string>("identityId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                   else if (policy.IsIssued && endorsementId == null)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   UpdateIdentityCommand command = context.GetArgument<UpdateIdentityCommand>("input");
                   command.Id = identityId;
                   command.EndorsementId = endorsementId;
                   command.ModifiedById = loginId;

                   return await policyService.UpdateContractHolderIdentityAsync(tenantId, policyId, command);
               });
            Field<CreatedStatusResultGraphType>()
                .Name("updateOtherContractHolderIdentity")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                .Argument<NonNullGraphType<StringGraphType>>("identityId", "the identity identifier")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
                .Argument<NonNullGraphType<UpdateIdentityInputGraphType>>("input", "the clause input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    string entityId = context.GetArgument<string>("entityId");
                    string identityId = context.GetArgument<string>("identityId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                    else if (policy.IsIssued && endorsementId == null)
                        await permissionValidator.Authorize(context, "updateIssuedPolicies");

                    UpdateIdentityCommand command = context.GetArgument<UpdateIdentityCommand>("input");
                    command.Id = identityId;
                    command.EntityId = entityId;
                    command.EndorsementId = endorsementId;
                    command.ModifiedById = loginId;

                    return await policyService.UpdateOtherContractHolderIdentityAsync(tenantId, policyId, command);
                });
            Field<CreatedStatusResultGraphType>()
              .Name("updateContractInsuredIdentity")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<StringGraphType>>("identityId", "the identity identifier")
              .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
              .Argument<NonNullGraphType<UpdateIdentityInputGraphType>>("input", "the clause input")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  string entityId = context.GetArgument<string>("entityId");
                  string identityId = context.GetArgument<string>("identityId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  UpdateIdentityCommand command = context.GetArgument<UpdateIdentityCommand>("input");
                  command.Id = identityId;
                  command.EntityId = entityId;
                  command.EndorsementId = endorsementId;
                  command.ModifiedById = loginId;

                  return await policyService.UpdateContractInsuredIdentityAsync(tenantId, policyId, command);
              });
            Field<CreatedStatusResultGraphType>()
               .Name("removeContractHolderIdentity")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
               .Argument<NonNullGraphType<StringGraphType>>("identityId", "the identity identifier")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   string identityId = context.GetArgument<string>("identityId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                   else if (policy.IsIssued && endorsementId == null)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   var command = new RemoveCommand
                   {
                       EndorsementId = endorsementId,
                       Id = identityId,
                       RemovedById = loginId
                   };

                   return await policyService.RemoveContractHolderIdentityAsync(tenantId, policyId, command);
               });
            Field<CreatedStatusResultGraphType>()
                .Name("removeOtherContractHolderIdentity")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the entity identifier")
                .Argument<NonNullGraphType<StringGraphType>>("identityId", "the identity identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    string entityId = context.GetArgument<string>("entityId");
                    string identityId = context.GetArgument<string>("identityId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                    else if (policy.IsIssued && endorsementId == null)
                        await permissionValidator.Authorize(context, "updateIssuedPolicies");

                    var command = new RemoveEntityPrimitiveCommand
                    {
                        EndorsementId = endorsementId,
                        EntityId = entityId,
                        PrimitiveId = identityId,
                        RemovedById = loginId
                    };

                    return await policyService.RemoveOtherContractHolderIdentityAsync(tenantId, policyId, command);
                });
            Field<CreatedStatusResultGraphType>()
                .Name("removeContractInsuredIdentity")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the entity identifier")
                .Argument<NonNullGraphType<StringGraphType>>("identityId", "the identity identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    string entityId = context.GetArgument<string>("entityId");
                    string identityId = context.GetArgument<string>("identityId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                    else if (policy.IsIssued && endorsementId == null)
                        await permissionValidator.Authorize(context, "updateIssuedPolicies");

                    var command = new RemoveEntityPrimitiveCommand
                    {
                        EndorsementId = endorsementId,
                        EntityId = entityId,
                        PrimitiveId = identityId,
                        RemovedById = loginId
                    };

                    return await policyService.RemoveContractInsuredIdentityAsync(tenantId, policyId, command);
                });

            Field<CreatedStatusResultGraphType>()
             .Name("addContractHolderContact")
             .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
             .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
             .Argument<NonNullGraphType<AddContactInputGraphType>>("input", "the clause input")
             .AuthorizeWith("any")
             .ResolveAsync(async context =>
             {
                 string tenantId = context.GetTenantIdFromToken();
                 string loginId = context.GetLoginIdFromToken();
                 string policyId = context.GetArgument<string>("policyId");
                 string endorsementId = context.GetArgument<string>("endorsementId");
                 await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                 Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                 if (policy == null)
                     return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                 else if (policy.IsIssued && endorsementId == null)
                     await permissionValidator.Authorize(context, "updateIssuedPolicies");

                 AddContactCommand command = context.GetArgument<AddContactCommand>("input");
                 command.EndorsementId = endorsementId;
                 command.AddedById = loginId;

                 return await policyService.AddContractHolderContactAsync(tenantId, policyId, command);
             });
            Field<CreatedStatusResultGraphType>()
                .Name("addOtherContractHolderContact")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
                .Argument<NonNullGraphType<AddContactInputGraphType>>("input", "the clause input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    string entityId = context.GetArgument<string>("entityId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                    else if (policy.IsIssued && endorsementId == null)
                        await permissionValidator.Authorize(context, "updateIssuedPolicies");

                    AddContactCommand command = context.GetArgument<AddContactCommand>("input");
                    command.EntityId = entityId;
                    command.EndorsementId = endorsementId;
                    command.AddedById = loginId;

                    return await policyService.AddOtherContractHolderContactAsync(tenantId, policyId, command);
                });
            Field<CreatedStatusResultGraphType>()
              .Name("addContractInsuredContact")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
              .Argument<NonNullGraphType<AddContactInputGraphType>>("input", "the clause input")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  string entityId = context.GetArgument<string>("entityId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  AddContactCommand command = context.GetArgument<AddContactCommand>("input");
                  command.EntityId = entityId;
                  command.EndorsementId = endorsementId;
                  command.AddedById = loginId;

                  return await policyService.AddContractInsuredContactAsync(tenantId, policyId, command);
              });
            Field<CreatedStatusResultGraphType>()
               .Name("updateContractHolderContact")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
               .Argument<NonNullGraphType<StringGraphType>>("contactId", "the contact identifier")
               .Argument<NonNullGraphType<UpdateContactInputGraphType>>("input", "the clause input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   string contactId = context.GetArgument<string>("contactId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                   else if (policy.IsIssued && endorsementId == null)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   UpdateContactCommand command = context.GetArgument<UpdateContactCommand>("input");
                   command.Id = contactId;
                   command.EndorsementId = endorsementId;
                   command.ModifiedById = loginId;

                   return await policyService.UpdateContractHolderContactAsync(tenantId, policyId, command);
               });
            Field<CreatedStatusResultGraphType>()
              .Name("updateOtherContractHolderContact")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<StringGraphType>>("contactId", "the contact identifier")
              .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
              .Argument<NonNullGraphType<UpdateContactInputGraphType>>("input", "the clause input")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  string entityId = context.GetArgument<string>("entityId");
                  string contactId = context.GetArgument<string>("contactId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  UpdateContactCommand command = context.GetArgument<UpdateContactCommand>("input");
                  command.Id = contactId;
                  command.EntityId = entityId;
                  command.EndorsementId = endorsementId;
                  command.ModifiedById = loginId;

                  return await policyService.UpdateOtherContractHolderContactAsync(tenantId, policyId, command);
              });
            Field<CreatedStatusResultGraphType>()
              .Name("updateContractInsuredContact")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<StringGraphType>>("contactId", "the contact identifier")
              .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
              .Argument<NonNullGraphType<UpdateContactInputGraphType>>("input", "the clause input")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  string entityId = context.GetArgument<string>("entityId");
                  string contactId = context.GetArgument<string>("contactId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  UpdateContactCommand command = context.GetArgument<UpdateContactCommand>("input");
                  command.Id = contactId;
                  command.EntityId = entityId;
                  command.EndorsementId = endorsementId;
                  command.ModifiedById = loginId;

                  return await policyService.UpdateContractInsuredContactAsync(tenantId, policyId, command);
              });
            Field<CreatedStatusResultGraphType>()
               .Name("removeContractHolderContact")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
               .Argument<NonNullGraphType<StringGraphType>>("contactId", "the contact identifier")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   string contactId = context.GetArgument<string>("contactId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                   else if (policy.IsIssued && endorsementId == null)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   var command = new RemoveCommand
                   {
                       EndorsementId = endorsementId,
                       Id = contactId,
                       RemovedById = loginId
                   };

                   return await policyService.RemoveContractHolderContactAsync(tenantId, policyId, command);
               });
            Field<CreatedStatusResultGraphType>()
             .Name("removeOtherContractHolderContact")
             .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
             .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
             .Argument<NonNullGraphType<StringGraphType>>("entityId", "the entity identifier")
             .Argument<NonNullGraphType<StringGraphType>>("contactId", "the contact identifier")
             .AuthorizeWith("any")
             .ResolveAsync(async context =>
             {
                 string tenantId = context.GetTenantIdFromToken();
                 string loginId = context.GetLoginIdFromToken();
                 string policyId = context.GetArgument<string>("policyId");
                 string endorsementId = context.GetArgument<string>("endorsementId");
                 string entityId = context.GetArgument<string>("entityId");
                 string contactId = context.GetArgument<string>("contactId");
                 await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                 Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                 if (policy == null)
                     return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                 else if (policy.IsIssued && endorsementId == null)
                     await permissionValidator.Authorize(context, "updateIssuedPolicies");

                 var command = new RemoveEntityPrimitiveCommand
                 {
                     EndorsementId = endorsementId,
                     EntityId = entityId,
                     PrimitiveId = contactId,
                     RemovedById = loginId
                 };

                 return await policyService.RemoveOtherContractHolderContactAsync(tenantId, policyId, command);
             });
            Field<CreatedStatusResultGraphType>()
              .Name("removeContractInsuredContact")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<StringGraphType>>("entityId", "the entity identifier")
              .Argument<NonNullGraphType<StringGraphType>>("contactId", "the contact identifier")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  string entityId = context.GetArgument<string>("entityId");
                  string contactId = context.GetArgument<string>("contactId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  var command = new RemoveEntityPrimitiveCommand
                  {
                      EndorsementId = endorsementId,
                      EntityId = entityId,
                      PrimitiveId = contactId,
                      RemovedById = loginId
                  };

                  return await policyService.RemoveContractInsuredContactAsync(tenantId, policyId, command);
              });

            Field<CreatedStatusResultGraphType>()
              .Name("addContractHolderAddress")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<AddAddressInputGraphType>>("input", "the clause input")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  AddressInputGraph input = context.GetArgument<AddressInputGraph>("input");
                  var command = new AddAddressCommand
                  {
                      Type = input.Type,
                      Fields = input.Fields.ToDictionary(k => k.Type, v => v.Value),
                      AddedById = loginId
                  };
                  command.EndorsementId = endorsementId;

                  return await policyService.AddContractHolderAddressAsync(tenantId, policyId, command);
              });
            Field<CreatedStatusResultGraphType>()
                .Name("addOtherContractHolderAddress")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
                .Argument<NonNullGraphType<AddAddressInputGraphType>>("input", "the clause input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    string entityId = context.GetArgument<string>("entityId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                    if (policy == null)
                        return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                    else if (policy.IsIssued && endorsementId == null)
                        await permissionValidator.Authorize(context, "updateIssuedPolicies");

                    AddressInputGraph input = context.GetArgument<AddressInputGraph>("input");
                    var command = new AddAddressCommand
                    {
                        Type = input.Type,
                        Fields = input.Fields.ToDictionary(k => k.Type, v => v.Value),
                        AddedById = loginId
                    };
                    command.EntityId = entityId;
                    command.EndorsementId = endorsementId;

                    return await policyService.AddOtherContractHolderAddressAsync(tenantId, policyId, command);
                });
            Field<CreatedStatusResultGraphType>()
              .Name("addContractInsuredAddress")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
              .Argument<NonNullGraphType<AddAddressInputGraphType>>("input", "the clause input")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  string entityId = context.GetArgument<string>("entityId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  AddressInputGraph input = context.GetArgument<AddressInputGraph>("input");
                  var command = new AddAddressCommand
                  {
                      Type = input.Type,
                      Fields = input.Fields.ToDictionary(k => k.Type, v => v.Value),
                      AddedById = loginId
                  };
                  command.EntityId = entityId;
                  command.EndorsementId = endorsementId;

                  return await policyService.AddContractInsuredAddressAsync(tenantId, policyId, command);
              });
            Field<CreatedStatusResultGraphType>()
               .Name("updateContractHolderAddress")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
               .Argument<NonNullGraphType<StringGraphType>>("addressId", "the address identifier")
               .Argument<NonNullGraphType<UpdateAddressInputGraphType>>("input", "the clause input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   string addressId = context.GetArgument<string>("addressId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                   else if (policy.IsIssued && endorsementId == null)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   UpdateAddressInputGraph input = context.GetArgument<UpdateAddressInputGraph>("input");
                   var command = new UpdateAddressCommand
                   {
                       EndorsementId = endorsementId,
                       Id = addressId,
                       Type = input.Type,
                       Fields = input.Fields?.ToDictionary(k => k.Type, v => v.Value),
                       ModifiedById = context.GetLoginIdFromToken()
                   };

                   return await policyService.UpdateContractHolderAddressAsync(tenantId, policyId, command);
               });
            Field<CreatedStatusResultGraphType>()
             .Name("updateOtherContractHolderAddress")
             .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
             .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
             .Argument<NonNullGraphType<StringGraphType>>("addressId", "the address identifier")
             .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
             .Argument<NonNullGraphType<UpdateAddressInputGraphType>>("input", "the clause input")
             .AuthorizeWith("any")
             .ResolveAsync(async context =>
             {
                 string tenantId = context.GetTenantIdFromToken();
                 string loginId = context.GetLoginIdFromToken();
                 string policyId = context.GetArgument<string>("policyId");
                 string endorsementId = context.GetArgument<string>("endorsementId");
                 string entityId = context.GetArgument<string>("entityId");
                 string addressId = context.GetArgument<string>("addressId");
                 await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                 Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                 if (policy == null)
                     return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                 else if (policy.IsIssued && endorsementId == null)
                     await permissionValidator.Authorize(context, "updateIssuedPolicies");

                 UpdateAddressInputGraph input = context.GetArgument<UpdateAddressInputGraph>("input");

                 var command = new UpdateAddressCommand
                 {
                     EndorsementId = endorsementId,
                     EntityId = entityId,
                     Id = addressId,
                     Type = input.Type,
                     Fields = input.Fields?.ToDictionary(k => k.Type, v => v.Value),
                     ModifiedById = context.GetLoginIdFromToken()
                 };

                 return await policyService.UpdateOtherContractHolderAddressAsync(tenantId, policyId, command);
             });
            Field<CreatedStatusResultGraphType>()
              .Name("updateContractInsuredAddress")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<StringGraphType>>("addressId", "the address identifier")
              .Argument<NonNullGraphType<StringGraphType>>("entityId", "the insured identifier")
              .Argument<NonNullGraphType<UpdateAddressInputGraphType>>("input", "the clause input")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  string entityId = context.GetArgument<string>("entityId");
                  string addressId = context.GetArgument<string>("addressId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  UpdateAddressInputGraph input = context.GetArgument<UpdateAddressInputGraph>("input");

                  var command = new UpdateAddressCommand
                  {
                      EndorsementId = endorsementId,
                      EntityId = entityId,
                      Id = addressId,
                      Type = input.Type,
                      Fields = input.Fields?.ToDictionary(k => k.Type, v => v.Value),
                      ModifiedById = context.GetLoginIdFromToken()
                  };

                  return await policyService.UpdateContractInsuredAddressAsync(tenantId, policyId, command);
              });
            Field<CreatedStatusResultGraphType>()
               .Name("removeContractHolderAddress")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
               .Argument<NonNullGraphType<StringGraphType>>("addressId", "the address identifier")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   string addressId = context.GetArgument<string>("addressId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                   if (policy == null)
                       return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                   else if (policy.IsIssued && endorsementId == null)
                       await permissionValidator.Authorize(context, "updateIssuedPolicies");

                   var command = new RemoveCommand
                   {
                       EndorsementId = endorsementId,
                       Id = addressId,
                       RemovedById = loginId
                   };

                   return await policyService.RemoveContractHolderAddressAsync(tenantId, policyId, command);
               });
            Field<CreatedStatusResultGraphType>()
             .Name("removeOtherContractHolderAddress")
             .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
             .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
             .Argument<NonNullGraphType<StringGraphType>>("entityId", "the entity identifier")
             .Argument<NonNullGraphType<StringGraphType>>("addressId", "the address identifier")
             .AuthorizeWith("any")
             .ResolveAsync(async context =>
             {
                 string tenantId = context.GetTenantIdFromToken();
                 string loginId = context.GetLoginIdFromToken();
                 string policyId = context.GetArgument<string>("policyId");
                 string endorsementId = context.GetArgument<string>("endorsementId");
                 string entityId = context.GetArgument<string>("entityId");
                 string addressId = context.GetArgument<string>("addressId");
                 await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                 Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                 if (policy == null)
                     return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                 else if (policy.IsIssued && endorsementId == null)
                     await permissionValidator.Authorize(context, "updateIssuedPolicies");

                 var command = new RemoveEntityPrimitiveCommand
                 {
                     EndorsementId = endorsementId,
                     EntityId = entityId,
                     PrimitiveId = addressId,
                     RemovedById = loginId
                 };

                 return await policyService.RemoveOtherContractHolderAddressAsync(tenantId, policyId, command);
             });
            Field<CreatedStatusResultGraphType>()
              .Name("removeContractInsuredAddress")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
              .Argument<NonNullGraphType<StringGraphType>>("entityId", "the entity identifier")
              .Argument<NonNullGraphType<StringGraphType>>("addressId", "the address identifier")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  string endorsementId = context.GetArgument<string>("endorsementId");
                  string entityId = context.GetArgument<string>("entityId");
                  string addressId = context.GetArgument<string>("addressId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };
                  else if (policy.IsIssued && endorsementId == null)
                      await permissionValidator.Authorize(context, "updateIssuedPolicies");

                  var command = new RemoveEntityPrimitiveCommand
                  {
                      EndorsementId = endorsementId,
                      EntityId = entityId,
                      PrimitiveId = addressId,
                      RemovedById = loginId
                  };

                  return await policyService.RemoveContractInsuredAddressAsync(tenantId, policyId, command);
              });

            Field<CreatedStatusResultGraphType>()
               .Name("addAddClauseToEndorsement")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "the endorsement identifier")
               .Argument<NonNullGraphType<AddClauseToOfferInputGraphType>>("input", "the clause input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   AddClauseCommand command = context.GetArgument<AddClauseCommand>("input");
                   command.AddedById = loginId;
                   command.EndorsementId = endorsementId;

                   Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, command.RenderParameters, out List<string> errors);
                   return errors.Any()
                       ? new Result<CreatedStatus> { Status = "failure", Errors = errors }
                       : await policyService.AddAddClauseToEndorsementAsync(tenantId, policyId, command);
               });

            Field<ResultGraphType>()
               .Name("removeCommandFromEndorsement")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<NonNullGraphType<StringGraphType>>("endorsementId", "the endorsement identifier")
               .Argument<NonNullGraphType<StringGraphType>>("commandId", "the command identifier")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   string commandId = context.GetArgument<string>("commandId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   var command = new RemoveCommand
                   {
                       EndorsementId = endorsementId,
                       Id = commandId,
                       RemovedById = loginId
                   };

                   return await policyService.RemoveCommandFromEndorsementAsync(tenantId, policyId, command);
               });

            Field<CreatedStatusResultGraphType>()
              .Name("addTagToPolicy")
              .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
              .Argument<NonNullGraphType<TagInputGraphType>>("input", "the tag input")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string policyId = context.GetArgument<string>("policyId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                  Policy policy = (await policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                  if (policy == null)
                      return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The policy '{policy.Id}' was not found." } };

                  AddTagCommand command = context.GetArgument<AddTagCommand>("input");
                  command.AddedById = loginId;

                  return await policyService.AddTagToPolicyAsync(tenantId, policyId, command);
              });

            Field<ResultGraphType>()
               .Name("removeTagFromPolicy")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<NonNullGraphType<StringGraphType>>("tagId", "the tag identifier")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string tagId = context.GetArgument<string>("tagId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                   var command = new RemoveCommand
                   {
                       Id = tagId,
                       RemovedById = loginId
                   };

                   return await policyService.RemoveTagFromPolicyAsync(tenantId, policyId, command);
               });

            Field<PolicyDebitNoteNumberGraphType>()
                .Name("policyNextDebitNoteNumber")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .Argument<StringGraphType>("transactionId", "Transaction we issue the debit note number for")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string transactionId = context.GetArgument<string>("transactionId");


                    if (!string.IsNullOrEmpty(transactionId))
                    {
                        long transactionsCount =
                            await transactionService.GetTotalCountAsync(tenantId,
                                new TransactionWhere
                                {
                                    And = new List<TransactionWhere>
                                    {
                                        new() { Id = transactionId },
                                        new() { HasDebitNoteNumber = true },
                                        new() { PolicyId = policyId }
                                    }
                                });

                        if (transactionsCount > 0)
                            throw new Exception($"Transaction {transactionId} already has debit note issued");
                    }

                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));


                    DateTime now = DateTime.UtcNow;
                    var command = new PolicyCounterCommand
                    {
                        CounterKey = PolicyCounter.DebitNoteNumber,
                        CreatedById = loginId,
                        LastModifiedById = loginId,
                        CreatedAt = now,
                        LastModifiedAt = now,
                        PolicyId = policyId
                    };

                    long counter =
                        await policyCountersService.GetNextAsync(tenantId, command, context.CancellationToken);

                    List<Policy> policies = await policyService.GetAsync(tenantId, new PolicyWhere { Id = policyId });
                    Policy policy = policies.FirstOrDefault()!;

                    string p400 = policy?.Fields["p400PolicyNumber"]?.ToString() ?? "00000000";

                    return new PolicyDebitNoteNumberGraph
                    {
                        PolicyId = policyId, DebitNoteNumber = $"GMD-{p400}-{counter:00000}"
                    };
                });

            Field<PolicyMemberMovementNumberGraphType>()
                .Name("policyNextMemberMovementNumber")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");

                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    DateTime now = DateTime.UtcNow;
                    var command = new PolicyCounterCommand
                    {
                        CounterKey = PolicyCounter.MemberMovementNumber,
                        CreatedById = loginId,
                        LastModifiedById = loginId,
                        CreatedAt = now,
                        LastModifiedAt = now,
                        PolicyId = policyId
                    };

                    long counter = await policyCountersService.GetNextAsync(tenantId, command, context.CancellationToken);

                    List<Policy> policies = await policyService.GetAsync(tenantId, new PolicyWhere { Id = policyId });
                    Policy policy = policies.FirstOrDefault()!;

                    string p400 = policy?.Fields["p400PolicyNumber"]?.ToString() ?? "00000000";
                    string inceptionYear = policy?.StartDate?.ToString("yyyy") ?? "0000";

                    return new PolicyMemberMovementNumberGraph
                    {
                        PolicyId = policyId,
                        MemberMovementNumber = $"{p400}-{inceptionYear}/V{counter:000}"
                    };
                });
        }
    }
}