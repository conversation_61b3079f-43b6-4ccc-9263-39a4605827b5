using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Users;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using CoverGo.Users.Domain.Individuals;
using Pipedrive;

namespace CoverGo.Gateway.Interfaces
{
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("dlvnWebhook/{tenantId}")]
    public class DLVNWebhookController
    {
        private readonly IPolicyService _policyService;
        private readonly IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> _entityService;
        private readonly IAuthService _authService;
        private readonly ILogger _logger;

        public DLVNWebhookController(
            IPolicyService policyService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> entityService,
            IAuthService authService,
            ILogger<DLVNWebhookController> logger)
        {
            _policyService = policyService;
            _entityService = entityService;
            _authService = authService;
            _logger = logger;

        }

        [HttpPost("Encrypt")]
        public ActionResult<Result<string>> Encrypt(string tenantId, [FromBody] DLVNUpdatePaymentRequest request)
        {
            return Result<string>.Success(Encrypt(tenantId, request.Content));
        }

        [HttpPost("Decrypt")]
        public ActionResult<Result<string>> Decrypt(string tenantId, [FromBody] DLVNDecryptRequest request)
        {
            return Result<string>.Success(Decrypt(request.Key, request.Content));
        }

        [HttpPost("UpdatePayment")]
        [AllowAnonymous]
        public async Task<ActionResult<Result<DLVNUpdatePaymentDetail>>> UpdatePaymentAsync(string tenantId, [FromBody] DLVNUpdatePaymentRequest request)
        {
            _logger.LogInformation("[UpdatePaymentAsync] starting for tenant {Tenant}", tenantId);

            if (request == null || string.IsNullOrEmpty(request.Content))
            {
                _logger.LogError("[UpdatePaymentAsync] Invalid request data");
                return Result<DLVNUpdatePaymentDetail>.Failure("Invalid request data");
            }

            DLVNUpdatePaymentDetail command;
            try
            {
                string decrypted = Decrypt(GetEncryptionKey(tenantId), request.Content);

                if (string.IsNullOrEmpty(decrypted))
                {
                    _logger.LogError("[UpdatePaymentAsync] Empty decrypted content");
                    return Result<DLVNUpdatePaymentDetail>.Failure("Cannot decrypt data, empty decrypted content");
                }

                command = JsonConvert.DeserializeObject<DLVNUpdatePaymentDetail>(decrypted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[UpdatePaymentAsync] Exception");
                return Result<DLVNUpdatePaymentDetail>.Failure("Cannot decrypt data");
            }

            Policy policy = (await _policyService.GetAsync(tenantId, new PolicyWhere
            {
                IssuerNumber_contains = command.policy?.policyNumber
            })).FirstOrDefault();

            if (policy == null)
            {
                _logger.LogError($"[UpdatePaymentAsync] Cannot find policy number {command.policy?.policyNumber}");
                return Result<DLVNUpdatePaymentDetail>.Failure($"Cannot find policy number {command.policy?.policyNumber}");
            }

            string paymentStatus = command.policy?.paymentStatus
                    switch
            {
                "1" => "PAID",
                "2" => "FAILED",
                "3" => "PROCESSING",
                _ => "PENDING"
            };

            var ret = await _policyService.UpdatePolicy2Async(tenantId, policy.Id, new UpdatePolicyCommand
            {
                FieldsPatch = $"[{{\"op\":\"add\",\"path\":\"/paymentStatus\",\"value\":\"{paymentStatus}\"}}]"
            });

            if (ret.IsSuccess)
            {
                return Result<DLVNUpdatePaymentDetail>.Success(command);
            }

            _logger.LogError($"[UpdatePaymentAsync] Cannot update policy | {string.Join(", ", ret.Errors)}");
            return Result<DLVNUpdatePaymentDetail>.Failure(ret.Errors);
        }

        [HttpPost("Login")]
        [AllowAnonymous]
        public async Task<ActionResult<Result<DLVNLoginResponse>>> LoginAsync(string tenantId, [FromBody] DLVNLoginRequest request)
        {
            _logger.LogInformation("[LoginAsync] starting for tenant {Tenant} and client {Client}", tenantId, request.ClientId);

            if (string.IsNullOrEmpty(request.ClientId))
            {
                return Result<DLVNLoginResponse>.Failure("Missing ClientId");
            }

            if (string.IsNullOrEmpty(request.Content))
            {
                return Result<DLVNLoginResponse>.Failure("Missing Content");
            }

            DLVNLoginDetailRequest loginRequest;

            try
            {
                var decrypted = Decrypt(GetEncryptionKey(tenantId), request.Content);
                loginRequest = JsonConvert.DeserializeObject<DLVNLoginDetailRequest>(decrypted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error decrypting data");
                return Result<DLVNLoginResponse>.Failure(ex.Message);
            }

            if (loginRequest.CustomerInfo == null)
            {
                return Result<DLVNLoginResponse>.Failure("Missing CustomerInfo");
            }

            if (string.IsNullOrEmpty(loginRequest.CustomerInfo.Id))
            {
                return Result<DLVNLoginResponse>.Failure("Missing nationalId");
            }

            Login nationalIdLogin = await _authService.GetLoginByNameAsync(tenantId, loginRequest.CustomerInfo.Id);

            if (nationalIdLogin == null)
            {
                if (string.IsNullOrEmpty(loginRequest.CustomerInfo.Email))
                {
                    loginRequest.CustomerInfo.Email = $"dlvn{loginRequest.CustomerInfo.Id}@dlvn.vn";
                }

                Result<CreatedStatus> createLoginResult = await _authService.CreateLoginAsync(tenantId, new CreateLoginCommand
                {
                    ClientId = request.ClientId,
                    Username = loginRequest.CustomerInfo.Id,
                    Password = loginRequest.CustomerInfo.Id,
                    IsEmailConfirmed = true
                });

                if (!createLoginResult.IsSuccess)
                    return Result<DLVNLoginResponse>.Failure(createLoginResult.Errors);

                await _authService.AddTargettedPermissionsAsync(tenantId, createLoginResult.Value.Id, new List<AddTargettedPermissionCommand>
                {
                    new() { AddedById = createLoginResult.Value.Id, Type = "clientId", Value = request.ClientId }
                });

                if (!createLoginResult.IsSuccess)
                {
                    _logger.LogError($"[DLVNCreateCustomerCommand] Create login failed | {string.Join(", ", createLoginResult.Errors)}");
                    return Result<DLVNLoginResponse>.Failure("Create login failed");
                }

                nationalIdLogin = await _authService.GetLoginByNameAsync(tenantId, loginRequest.CustomerInfo.Id);
            }

            if (nationalIdLogin.PermissionGroups?.FirstOrDefault(x => x.Name.ToUpper() == "CUSTOMER") == null)
            {
                var permissionGroup = (await _authService.GetPermissionGroupsAsync(tenantId, new PermissionGroupWhere
                {
                    Name = "CUSTOMER"
                }))?.FirstOrDefault();

                if (permissionGroup == null)
                {
                    _logger.LogError($"[DLVNCreateCustomerCommand] No customer group found");
                    return Result<DLVNLoginResponse>.Failure("No customer group found");
                }

                var addPermissionGroupResult = await _authService.AddLoginIdToPermissionGroupAsync(tenantId, permissionGroup.Id, new AddLoginPermissionsToPermissionGroupCommand
                {
                    LoginId = nationalIdLogin.Id
                });

                if (!addPermissionGroupResult.IsSuccess)
                {
                    _logger.LogError($"[DLVNCreateCustomerCommand] Update customer group failed | {string.Join(", ", addPermissionGroupResult.Errors)}");
                    return Result<DLVNLoginResponse>.Failure("Update customer group failed");
                }

                var addTargetPermissionResult = await _authService.AddTargettedPermissionsAsync(tenantId, nationalIdLogin.Id, new List<AddTargettedPermissionCommand>
                {
                    new()
                    {
                        Type = "groups",
                        Value = permissionGroup.Id
                    }
                });

                if (!addTargetPermissionResult.IsSuccess)
                {
                    _logger.LogError($"[DLVNCreateCustomerCommand] Update target failed | {string.Join(", ", addTargetPermissionResult.Errors)}");
                    return Result<DLVNLoginResponse>.Failure("Update target failed");
                }
            }

            Individual individual = (await _entityService.GetAsync(tenantId, new IndividualWhere
            {
                CreatedById = nationalIdLogin.Id,
                Type_in = new List<IndividualTypes> { IndividualTypes.Pipeline, IndividualTypes.Customer }
,
            }))?.FirstOrDefault();

            string individualId = string.Empty;
            if (individual == null)
            {
                Result<CreatedStatus> createdIndividualResult = await _entityService.CreateAsync(tenantId, new CreateIndividualCommand
                {
                    Type = IndividualTypes.Customer,
                    CreatedById = nationalIdLogin.Id,
                    EnglishFirstName = loginRequest.CustomerInfo.GivenName,
                    EnglishLastName = loginRequest.CustomerInfo.FamilyName,
                    NameFormat = "{englishLastName} {englishFirstName}",
                    Fields = $"{{\"nationalId\": {{\"number\": \"{loginRequest.CustomerInfo.Id}\", \"type\": \"{loginRequest.CustomerInfo.IdType}\", \"issueDate\": \"\", \"expiryDate\": \"\" }} }}"
                });

                if (!createdIndividualResult.IsSuccess)
                {
                    _logger.LogError($"[DLVNCreateCustomerCommand] Create individual failed | {string.Join(", ", createdIndividualResult.Errors)}");
                    return Result<DLVNLoginResponse>.Failure("Create individual failed");
                }
                individualId = createdIndividualResult.Value.Id;
            }
            else
            {
                individualId = individual.Id;
            }

            Token token = await _authService.GetWholeAccessTokenAsync(tenantId, request.ClientId, "", loginRequest.CustomerInfo.Id, loginRequest.CustomerInfo.Id);

            if (token?.ErrorDescription == "invalid_username_or_password")
            {
                var updateLoginResult = await _authService.UpdateLoginAsync(tenantId, nationalIdLogin.Id, new UpdateLoginCommand
                {
                    IsUserNameChanged = true,
                    UserName = loginRequest.CustomerInfo.Id,
                    ModifiedById = nationalIdLogin.Id
                });

                if (!updateLoginResult.IsSuccess)
                {
                    _logger.LogError($"[DLVNCreateCustomerCommand] UpdateLogin failed || {string.Join(", ", updateLoginResult.Errors)}");
                    return Result<DLVNLoginResponse>.Failure("Update login failed");
                }

                token = await _authService.GetWholeAccessTokenAsync(tenantId, request.ClientId, "", loginRequest.CustomerInfo.Id, loginRequest.CustomerInfo.Id);
            }

            loginRequest.CustomerInfo.Id = string.Empty;

            return !string.IsNullOrEmpty(token?.AccessToken)
                                ? Result<DLVNLoginResponse>.Success(new DLVNLoginResponse
                                {
                                    AccessToken = token.AccessToken,
                                    RefreshToken = token.RefreshToken,
                                    IndividualId = individualId,
                                    LoginId = nationalIdLogin.Id,
                                    UserData = loginRequest.CustomerInfo,
                                    Source = loginRequest.Source
                                })
                                : Result<DLVNLoginResponse>.Failure(token?.Error);
        }

        private string Encrypt(string tenantId, string plainText)
        {
            try
            {
                string encryptionKey = GetEncryptionKey(tenantId);
                byte[] cipherData;

                Aes aes = Aes.Create();
                aes.Key = Convert.FromBase64String(encryptionKey);
                aes.GenerateIV();
                aes.Mode = CipherMode.CBC;
                ICryptoTransform cipher = aes.CreateEncryptor(aes.Key, aes.IV);
                using (MemoryStream ms = new())
                {
                    using (CryptoStream cs = new(ms, cipher, CryptoStreamMode.Write))
                    {
                        using StreamWriter sw = new(cs);
                        sw.Write(plainText);
                    }
                    cipherData = ms.ToArray();
                }
                byte[] combinedData = new byte[aes.IV.Length + cipherData.Length];
                Array.Copy(aes.IV, 0, combinedData, 0, aes.IV.Length);
                Array.Copy(cipherData, 0, combinedData, aes.IV.Length, cipherData.Length);
                return Convert.ToBase64String(combinedData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[UpdatePaymentAsync] Encrypt Exception");
                return "";
            }
        }

        private string Decrypt(string decryptionKey, string combinedString)
        {
            try
            {
                _logger.LogInformation("DLVN Decryption Key:{decryptionKey}", decryptionKey);
                string decrypted;
                byte[] combinedData;
                try
                {
                    combinedData = Convert.FromBase64String(combinedString);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error occurred while decrypting");
                    combinedData = Encoding.ASCII.GetBytes(combinedString);
                }

                Aes aes = Aes.Create();
                aes.Key = Convert.FromBase64String(decryptionKey);
                byte[] iv = new byte[aes.BlockSize / 8];
                byte[] cipherText = new byte[combinedData.Length - iv.Length];
                Array.Copy(combinedData, iv, iv.Length);
                Array.Copy(combinedData, iv.Length, cipherText, 0, cipherText.Length);
                aes.IV = iv;
                aes.Mode = CipherMode.CBC;
                ICryptoTransform decipher = aes.CreateDecryptor(aes.Key, aes.IV);
                using MemoryStream ms = new(cipherText);
                using (CryptoStream cs = new(ms, decipher, CryptoStreamMode.Read))
                {
                    using StreamReader sr = new(cs);
                    decrypted = sr.ReadToEnd();
                }
                return decrypted;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[UpdatePaymentAsync] Decrypt Exception on {combinedString}");
                return "";
            }
        }

        public class DLVNDecryptRequest
        {
            public string Key { get; set; }
            public string Content { get; set; }
        }

        public class DLVNUpdatePaymentRequest
        {
            public string Content { get; set; }
        }

        public class DLVNUpdatePaymentDetail
        {
            public DLVNUpdatePaymentPolicyDetail policy { get; set; }
        }

        public class DLVNUpdatePaymentPolicyDetail
        {
            public string policyNumber { get; set; }
            public string paymentStatus { get; set; }
        }

        public class DLVNLoginRequest
        {
            public string Content { get; set; }
            public string ClientId { get; set; }
        }

        public class DLVNLoginDetailRequest
        {
            public string ProductId { get; set; }
            public DLVNUserData CustomerInfo { get; set; }
            public string Source { get; set; }
        }

        public class DLVNLoginResponse
        {
            public string AccessToken { get; set; }
            public string RefreshToken { get; set; }
            public string IndividualId { get; set; }
            public string LoginId { get; set; }
            public DLVNUserData UserData { get; set; }
            public string Source { get; set; }
        }

        public class DLVNUserData
        {
            public string FamilyName { get; set; }
            public string GivenName { get; set; }
            public string DOB { get; set; }
            public string Gender { get; set; }
            public string Id { get; set; }
            public string IdType { get; set; }
            public string Nationality { get; set; }
            public string Phone { get; set; }
            public string Email { get; set; }
            public string TransactionId { get; set; }
            public string OrderId { get; set; }
            public string ProposalId { get; set; }
            public string CustomerId { get; set; }
            public string PartnerIdType { get; set; }
        }

        private string GetEncryptionKey(string tenant)
            => tenant switch
            {
                "dlvn" => System.Environment.GetEnvironmentVariable("DLVN_API_ENCRYPTION_KEY"),
                _ => System.Environment.GetEnvironmentVariable("DLVN_UAT_API_ENCRYPTION_KEY") ?? "YWVzU09QZGx2bk9ubGluZVBheW1lbnRQcml2YXRlMDE="
            };
    }
}