using System.Collections.Generic;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Advisor;
using CoverGo.Gateway.Interfaces.Advisor;
using GraphQL.Authorization;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeAdvisorMutations(IAdvisorService advisorService, PermissionValidator permissionValidator)
        {
                Field<CreatedStatusResultGraphType>()
                    .Name("createReview")
                    .Description("creates a review")
                    .AuthorizeWith("any")
                    .Argument<NonNullGraphType<CreateReviewInputGraphType>>("input", "the input")
                    .ResolveAsync(async context =>
                    {
                        string tenantId = context.GetTenantIdFromToken();
                        string loginId = context.GetLoginIdFromToken();
    
                        CreateReviewCommand command = context.GetArgument<CreateReviewCommand>("input");
                        command.CreatedById = loginId;
    
                        await permissionValidator.Authorize(context, "writeReviews");
    
                        Result<CreatedStatus> result = await advisorService.CreateReviewAsync(tenantId, command);
    
                        return result;
                    });
    
                Field<ResultGraphType>()
                    .Name("updateReview")
                    .Description("updates an review")
                    .AuthorizeWith("any")
                    .Argument<NonNullGraphType<StringGraphType>>("id", "the identifier of the review")
                    .Argument<NonNullGraphType<UpdateReviewInputGraphType>>("input", "The input")
                    .ResolveAsync(async context =>
                    {
                        string tenantId = context.GetTenantIdFromToken();
                        string loginId = context.GetLoginIdFromToken();
                        string id = context.GetArgument<string>("id");
    
                        await permissionValidator.Authorize(context, "writeReviews", id);
    
                        UpdateReviewCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateReviewCommand>();
                        command.ModifiedById = loginId;
    
                        return await advisorService.UpdateReviewAsync(tenantId, id, command);
                    });
    
                Field<ResultGraphType>()
                    .Name("deleteReview")
                    .Description("deletes a review")
                    .AuthorizeWith("any")
                    .Argument<NonNullGraphType<StringGraphType>>("id", "the identifier of the review")
                    .ResolveAsync(async context =>
                    {
                        string tenantId = context.GetTenantIdFromToken();
                        string loginId = context.GetLoginIdFromToken();
                        string id = context.GetArgument<string>("id");
    
                        await permissionValidator.Authorize(context, "writeReviews", id);
    
                        var command = new DeleteCommand
                        {
                            DeletedById = loginId
                        };
    
                        return await advisorService.DeleteReviewAsync(tenantId, id, command);
                    });
    
                Field<CreatedStatusResultGraphType>()
                .Name("addScore")
                .Description("Adds a score")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("reviewId", "The identifier of the review")
                .Argument<NonNullGraphType<AddScoreInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string id = context.GetArgument<string>("reviewId");
    
                    await permissionValidator.Authorize(context, "writeReviews", id);
    
                    AddScoreCommand command = context.GetArgument<AddScoreCommand>("input");
                    command.AddedById = loginId;
    
                    return await advisorService.AddScoreAsync(tenantId, id, command);
                });
    
                Field<ResultGraphType>()
                .Name("updateScore")
                .Description("Updates a score")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("reviewId", "The identifier of the review")
                .Argument<NonNullGraphType<StringGraphType>>("scoreId", "The identifier of the score")
                .Argument<NonNullGraphType<UpdateScoreInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string id = context.GetArgument<string>("reviewId");
                    string scoreId = context.GetArgument<string>("scoreId");
    
    
                    UpdateScoreCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateScoreCommand>();
                    command.ModifiedById = loginId;
                    command.Id = scoreId;
    
                    return await advisorService.UpdateScoreAsync(tenantId, id, command);
                });
    
                Field<ResultGraphType>()
                .Name("removeScore")
                .Description("Removes a score")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("reviewId", "the identifier of the review")
                .Argument<NonNullGraphType<StringGraphType>>("scoreId", "The identifier of the score")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string id = context.GetArgument<string>("reviewId");
                    string scoreId = context.GetArgument<string>("scoreId");
    
                    var command = new RemoveCommand
                    {
                        Id = scoreId,
                        RemovedById = loginId
                    };
    
                    return await advisorService.RemoveScoreAsync(tenantId, id, command);
                });
        }
    }
}