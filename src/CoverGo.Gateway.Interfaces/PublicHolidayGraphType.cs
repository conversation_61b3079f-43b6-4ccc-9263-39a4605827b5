using System;
using CoverGo.DateUtils;
using CoverGo.DateUtils.Models;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces
{
    public class PublicHolidayGraphType : ObjectGraphType<PublicHolidayGraph>
    {
        public PublicHolidayGraphType()
        {
            Field(x => x.Date, nullable: true, type: typeof(DateTimeGraphType));
            Field(x => x.Name, nullable: true);
            Field(x => x.CountryCode, nullable: true, type: typeof(CountryCodeEnumGraphType));
            Field(x => x.Type, nullable: true, type: typeof(PublicHolidayTypeEnumGraphType));
            Field(x => x.FixedHoliday, nullable: true);
        }
    }

    public class PublicHolidayGraph
    {
        public PublicHolidayGraph(PublicHoliday publicHoliday)
        {
            Date = publicHoliday.Date;
            Name = publicHoliday.Name;
            CountryCode = publicHoliday.CountryCode;
            Type = publicHoliday.Type;
            FixedHoliday = publicHoliday.Fixed;
        }
        
        public DateTime Date { get; private set; }
        public string Name { get; private set; }
        public CountryCode CountryCode { get; private set; }
        public PublicHolidayType Type { get; private set; }
        public bool FixedHoliday { get; private set; }
    }
    
    public class PublicHolidayTypeEnumGraphType : EnumerationGraphType<PublicHolidayType>
    {
        public PublicHolidayTypeEnumGraphType()
        {
            Name = "publicHolidayTypeEnumGraphTypeEnum";
            Description = "A list of all public holiday types";
        }
    }
    
    public class CountryCodeEnumGraphType : EnumerationGraphType<CountryCode>
    {
        public CountryCodeEnumGraphType()
        {
            Name = "countryCodeEnumGraphTypeEnum";
            Description = "A list of all country code types";
        }
    }
}