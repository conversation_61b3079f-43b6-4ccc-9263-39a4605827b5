using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using GraphQL.Types;
using Newtonsoft.Json.Linq;
using CoverGo.Gateway.Domain.Claims;
using GraphQL.Validation;

namespace CoverGo.Gateway.Interfaces;

public static class PermissionValidatorExtensions
{
    public static ValueTask Authorize<T>(this PermissionValidator validator, ResolveFieldContext<T> context, string claimType)
        => validator.Authorize(context, new PermissionRequest(claimType));
    public static ValueTask Authorize<T>(this PermissionValidator validator, ResolveFieldContext<T> context, string claimType, string targetId)
        => validator.Authorize(context, new PermissionRequest(claimType).WithTargetIds(targetId));

    public static ValueTask<bool> HasTarget<T>(this PermissionValidator validator, ResolveFieldContext<T> context, string claimType, string claimValue)
        => validator.VerifyHasPermission(context, new PermissionRequest(claimType).WithTargetIds(claimValue));
    
    public static Task<IEnumerable<string>> GetTargetIdsFromClaim<T>(this PermissionValidator validator, ResolveFieldContext<T> context, string claimType, IEnumerable<string>? ids)
        => validator.GetPermittedTargetIds(context, new PermissionRequest(claimType).WithTargetIds(ids?.ToArray()));

    public static Task<IEnumerable<string>> GetTargetIdsFromClaim<T>(this PermissionValidator validator, ResolveFieldContext<T> context, string claimType)
        => validator.GetPermittedTargetIds(context, new PermissionRequest(claimType));

    public static Task<IEnumerable<string>> GetTargetIdsFromClaim(this PermissionValidator validator, ClaimsPrincipal user, bool permissionLazyLoadingRequired, PermissionRequest permissionRequest)
    {
        var context = new ResolveFieldContext<object>
        {
            UserContext = new GraphQLUserContext
            {
                User = user,
                PermissionLazyLoadingRequired = permissionLazyLoadingRequired
            }
        };

        return validator.GetPermittedTargetIds(context, permissionRequest);
    }

    public static async Task AuthorizeClaimAmountAsync<T>(
        this PermissionValidator validator,
        ResolveFieldContext<T> context,
        Domain.Claims.Claim claim,
        ITenantSpecificClaimService tenantSpecificClaimService)
    {
        string? claimType = claim.Fields["claimType"]?.Value<string>();
        IEnumerable<string> limits = [];
        if (claimType == "in_patient")
        {
            limits = (await validator.GetTargetIdsFromClaim(context, "ipClaimApprovalLimit")).Select(x => x.Replace("_", ""));
        }
        else if (claimType == "out_patient")
        {
            limits = (await validator.GetTargetIdsFromClaim(context, "opClaimApprovalLimit")).Select(x => x.Replace("_", ""));
        }

        decimal? maxLimit = null;
        foreach (string limitItem in limits)
        {
            if (decimal.TryParse(limitItem, out decimal value) && value >= (maxLimit ?? 0))
            {
                maxLimit = value;
            }
        }

        if (maxLimit != null)
        {
            decimal claimAmount = tenantSpecificClaimService.GetClaimApprovalAmount(claim);
            if (claimAmount > maxLimit.Value)
            {
                throw new ValidationError("", "claim_approval_limit", $"You are not authorized to approve {claimType} claims larger than {maxLimit.Value:N2}.");
            }
        }
    }
}