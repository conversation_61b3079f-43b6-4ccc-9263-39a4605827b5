using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Interfaces.Files;
using GraphQL.Authorization;
using GraphQL.Types;
using GraphQL.Validation;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        private readonly ParallelOptions DefaultParallelOptions = new() { MaxDegreeOfParallelism = 20 };

        protected void InitializeFilesMutations(IFileSystemService fileSystemService, PermissionValidator permissionValidator)
        {
            //Field<ResultGraphType>()
            //    .Name("uploadFile")
            //    .Description("uploads a file.")
            //    .Argument<NonNullGraphType<StringGraphType>>("key", "The file key")
            //    .Argument<NonNullGraphType<BooleanGraphType>>("isPublic", "specifies whether the file is publicly readable")
            //    .Argument<NonNullGraphType<ListGraphType<ByteGraphType>>>("bytes", "the file in bytes")
            //    .ResolveAsync(async context =>
            //    {
            //        string key = context.GetArgument<string>("key");
            //        byte[] bytes = context.GetArgument<byte[]>("bytes");
            //        bool isPublic = context.GetArgument<bool>("isPublic");
            //        await permissionValidator.Authorize(context, "writeFiles");
            //        string tenantId = context.GetTenantIdFromToken();
            //        string removedById = context.GetLoginIdFromToken();

            //        Result result = await fileSystemService.UploadFileAsync(tenantId, new UploadFileCommand
            //        {
            //            Content = bytes,
            //            IsPublic = isPublic,
            //            Key = key
            //        });

            //        return result;
            //    });

            Field<ResultGraphType>()
                .Name("setFileAcl")
                .Description("Set the ACL of a file")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("bucketName", "The bucket identifier")
                .Argument<NonNullGraphType<StringGraphType>>("key", "The folder key")
                .Argument<NonNullGraphType<BooleanGraphType>>("isPublic", "Is the file going to be public")
                .ResolveAsync(async context =>
                {
                    string key = context.GetArgument<string>("key");
                    bool isPublic = context.GetArgument<bool>("isPublic");

                    await permissionValidator.Authorize(context, "writeFiles", key);
                    string tenantId = context.GetTenantIdFromToken();
                    string createdById = context.GetLoginIdFromToken();

                    string bucketName = context.GetArgument<string>("bucketName");

                    Result result = await fileSystemService.SetAclAsync(tenantId, bucketName, new SetAclCommand { Key = key, IsPublic = isPublic });

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("generateSharableUrl")
                .Description("Generate a Sharable Url")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("bucketName", "The bucket identifier")
                .Argument<NonNullGraphType<StringGraphType>>("key", "The folder key")
                .Argument<StringGraphType>("expiresIn", "The amount of time until the file expires in hours")
                .ResolveAsync(async context =>
                {
                    string key = context.GetArgument<string>("key");
                    double? expiresIn = context.GetArgument<double>("expiresIn");

                    await permissionValidator.Authorize(context, "writeFiles", key);
                    string tenantId = context.GetTenantIdFromToken();

                    string bucketName = context.GetArgument<string>("bucketName");

                    Result<string> result = await fileSystemService.GenerateSharableUrlAsync(tenantId, bucketName, new GenerateSharableUrlCommand { Key = key, ExpiresIn = expiresIn });

                    return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
                });

            Field<ResultGraphType>()
                .Name("createFolder")
                .Description("creates a folder.")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("bucketName", "The bucket identifier")
                .Argument<NonNullGraphType<StringGraphType>>("key", "The folder key")
                .ResolveAsync(async context =>
                {
                    string key = context.GetArgument<string>("key");
                    await permissionValidator.Authorize(context, "writeFiles", key);
                    string tenantId = context.GetTenantIdFromToken();
                    string createdById = context.GetLoginIdFromToken();
                    string bucketName = context.GetArgument<string>("bucketName");

                    Result result = await fileSystemService.CreateFolderAsync(tenantId, bucketName, new CreateFolderCommand { Key = key });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("deleteFolder")
                .Description("deletes a folder.")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("bucketName", "The bucket identifier")
                .Argument<NonNullGraphType<StringGraphType>>("key", "The folder key")
                .ResolveAsync(async context =>
                {
                    string key = context.GetArgument<string>("key");
                    await permissionValidator.Authorize(context, "writeFiles", key);
                    string tenantId = context.GetTenantIdFromToken();
                    string deletedById = context.GetLoginIdFromToken();

                    string bucketName = context.GetArgument<string>("bucketName");

                    Result result = await fileSystemService.DeleteFolderAsync(tenantId, bucketName, new DeleteFolderCommand { Key = key });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("deleteFile")
                .Description("deletes a file.")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("bucketName", "The bucket identifier")
                .Argument<NonNullGraphType<StringGraphType>>("key", "The file key")
                .ResolveAsync(async context =>
                {
                    string key = context.GetArgument<string>("key");

                    bool authorized = await permissionValidator.AuthorizeWithTargetPrefix(context, "writeFiles", key);
                    if (!authorized)
                        throw new ValidationError("", "authorization", $"You are not authorized to run this. You are missing 'writeFiles:{key}' permission.\n");

                    string tenantId = context.GetTenantIdFromToken();

                    string bucketName = context.GetArgument<string>("bucketName");

                    Result result = await fileSystemService.DeleteFileAsync(tenantId, bucketName, new DeleteFileCommand { Key = key });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("deleteFiles")
                .Description("deletes multiple files.")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("bucketName", "The bucket identifier")
                .Argument<NonNullGraphType<ListGraphType<StringGraphType>>>("keys", "The file keys")
                .ResolveAsync(async context =>
                {
                    IEnumerable<string> keys = context.GetArgument<object[]>("keys")?.Select(o => o.ToString());

                    List<string> unauthorizedKeys = new();
                    foreach (string key in keys)
                    {
                        bool authorized = await permissionValidator.AuthorizeWithTargetPrefix(context, "writeFiles", key);
                        if(!authorized) unauthorizedKeys.Add(key);
                    }
                   
                    if (unauthorizedKeys.Any())
                        throw new ValidationError("", "authorization", $"You are not authorized to run this. You are missing 'writeFiles:{string.Join(", ", unauthorizedKeys)}' permission.\n");

                    string tenantId = context.GetTenantIdFromToken();
                    string removedById = context.GetLoginIdFromToken();

                    string bucketName = context.GetArgument<string>("bucketName");

                    Result result = await fileSystemService.DeleteFileAsync(tenantId, bucketName, new DeleteFileCommand { Keys = keys });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("moveFile")
                .Description("Moves a file")
                .Argument<StringGraphType>("bucketName", "The bucket identifier")
                .Argument<NonNullGraphType<StringGraphType>>("key", "The old file key")
                .Argument<NonNullGraphType<StringGraphType>>("newKey", "The new file key")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string key = context.GetArgument<string>("key");

                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"writeFiles");
                    bool authorized = allowedIds.Contains("all") ? true : allowedIds.Any(aids => key.StartsWith(aids)) ? true : false;
                    if (!authorized)
                        throw new ValidationError("", "authorization", $"You are not authorized to run this. You are missing 'writeFiles:{key}' permission.\n");

                    string tenantId = context.GetTenantIdFromToken();
                    string movedBy = context.GetLoginIdFromToken();
                    string newKey = context.GetArgument<string>("newKey");

                    string bucketName = context.GetArgument<string>("bucketName");

                    bool validateExtension = Tools.ValidateExtension(Path.GetExtension(newKey.ToLowerInvariant()));
                    if (!validateExtension)
                        return new Result { Status = "failure", Errors = new List<string> { "This extension is not allowed" } };

                    Result copyResult = await fileSystemService.CopyFileAsync(tenantId, bucketName, new CopyFileCommand { Key = key, NewKey = newKey });
                    if (copyResult.Status != "success")
                        return new Result { Status = copyResult.Status, Errors = copyResult.Errors };

                    Result deleteResult = await fileSystemService.DeleteFileAsync(tenantId, bucketName, new DeleteFileCommand { Key = key });
                    if (deleteResult.Status != "success")
                        return new Result { Status = deleteResult.Status, Errors = deleteResult.Errors };

                    return new Result { Status = "success" };
                });

            Field<CreatedStatusResultGraphType>()
             .Name("createFileSystemConfig")
             .AuthorizeWith("any")
             .Description("creates a fileSystemConfig")
             .Argument<NonNullGraphType<FileSystemConfigInputGraphType>>("input", "the config input")
             .ResolveAsync(async ctx =>
             {
                 string tenantId = ctx.GetTenantIdFromToken();
                 string loginId = ctx.GetLoginIdFromToken();

                 FileSystemConfigGraph graph = ctx.GetArgument<FileSystemConfigGraph>("input");
                 CreateFileSystemConfigCommand command = FileSystemConfigGraph.ToCreateCommand(graph);

                 await permissionValidator.Authorize(ctx, new PermissionRequest("createFileSystemConfigs", "writeFileSystemConfigs"));
                 command.CreatedById = loginId;

                 return await fileSystemService.CreateConfigAsync(tenantId, command);
             });

            Field<ResultGraphType>()
             .Name("updateFileSystemConfig")
             .AuthorizeWith("any")
             .Description("updates a fileSystemConfig")
             .Argument<NonNullGraphType<StringGraphType>>("id", "the identifier of the config")
             .Argument<NonNullGraphType<UpdateFileSystemConfigInputGraphType>>("input", "the config input")
             .ResolveAsync(async ctx =>
             {
                 string tenantId = ctx.GetTenantIdFromToken();
                 string loginId = ctx.GetLoginIdFromToken();

                 string id = ctx.GetArgument<string>("id");
                 UpdateFileSystemConfigCommand command = ctx.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateFileSystemConfigCommand>();

                 await permissionValidator.Authorize(ctx, new PermissionRequest("updateFileSystemConfigs", "writeFileSystemConfigs").WithTargetIds(id));
                 command.ModifiedById = loginId;

                 return await fileSystemService.UpdateConfigAsync(tenantId, id, command);
             });

            Field<ResultGraphType>()
             .Name("deleteFileSystemConfig")
             .AuthorizeWith("any")
             .Description("deletes a fileSystemConfig")
             .Argument<NonNullGraphType<StringGraphType>>("id", "the identifier of the config")
             .ResolveAsync(async ctx =>
             {
                 string tenantId = ctx.GetTenantIdFromToken();
                 string loginId = ctx.GetLoginIdFromToken();

                 string id = ctx.GetArgument<string>("id");

                 await permissionValidator.Authorize(ctx, new PermissionRequest("deleteFileSystemConfigs", "writeFileSystemConfigs").WithTargetIds(id));
                 var command = new DeleteCommand
                 {
                     DeletedById = loginId
                 };

                 return await fileSystemService.DeleteConfigAsync(tenantId, id, command);
             });

            Field<ResultGraphType>()
                .Name("copyFiles")
                .Description("Copies multiple files from one location to another")
                .Argument<StringGraphType>("bucketName", "The bucket identifier")
                .Argument<NonNullGraphType<ListGraphType<CopyFileInputGraphType>>>("copyFileInputs", "File to be copied")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "writeFiles");
                    List<CopyFileCommand> copyFileCommands = context.GetArgument<List<CopyFileCommand>>("copyFileInputs");
                    string tenantId = context.GetTenantIdFromToken();
                    string bucketName = context.GetArgument<string>("bucketName");
                    string allAccess = "all";
                    bool hasAccessToAllDirectories = allowedIds.Contains(allAccess);
                    string subdirectoryAllAccess = $"/{allAccess}";

                    foreach(CopyFileCommand command in copyFileCommands)
                    {
                        bool authorized = hasAccessToAllDirectories || allowedIds.Any(id => command.Key.StartsWith(id)
                            || (id.EndsWith(subdirectoryAllAccess) && command.Key.AsSpan().StartsWith(id.AsSpan().Slice(0, id.Length - subdirectoryAllAccess.Length))));
                        if (!authorized)
                            throw new ValidationError("", "authorization", $"You are not authorized to run this. You are missing 'writeFiles:{command.Key}' permission.\n");
                        
                        var validateExtension = Tools.ValidateExtension(Path.GetExtension(command.NewKey.ToLowerInvariant()));
                        if (!validateExtension)
                            return new Result { Status = "failure", Errors = new List<string> { $"This extension is not allowed: {command.NewKey}" } };
                    }

                    foreach (CopyFileCommand command in copyFileCommands)
                    {
                        Result copyResult = await fileSystemService.CopyFileAsync(tenantId, bucketName, command);

                        if (copyResult.Status != "success")
                            return new Result { Status = copyResult.Status, Errors = copyResult.Errors };
                    }

                    return new Result { Status = "success" };
                });

            Field<ResultGraphType>()
                .Name("addMetadata")
                .Description("Adds metadata to file")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<AddMetadataGraphType>>("input", "File and metadata")
                .ResolveAsync(async context =>
                {
                    var input = context.GetArgument<AddMetadataGraph>("input");
                    await permissionValidator.Authorize(context, "lockFiles", input.Key);
                    var tenantId = context.GetTenantIdFromToken();

                    var command = new AddMetadataToFileCommand(input.Key, input.Metadata.ToDictionary(kv => kv.Key, kv => kv.Value.StringValue));

                    return await fileSystemService.AddMetadataToFileAsync(tenantId, input.BucketName, command);
                });

            Field<ResultGraphType>()
                .Name("removeMetadata")
                .Description("Removes metadata to file")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("bucketName", "The bucket identifier")
                .Argument<NonNullGraphType<StringGraphType>>("key", "The file key")
                .Argument<NonNullGraphType<ListGraphType<StringGraphType>>>("metadataKeys", "Metadata Keys")
                .ResolveAsync(async context =>
                {
                    var key = context.GetArgument<string>("key");
                    await permissionValidator.Authorize(context, "lockFiles", key);
                    var tenantId = context.GetTenantIdFromToken();
                    var bucketName = context.GetArgument<string>("bucketName");
                    var metadataKeys = context.GetArgument<IEnumerable<string>>("metadataKeys");
                    var command = new RemoveMetadataFromFileCommand(key, metadataKeys);

                    return await fileSystemService.RemoveMetaDataFromFileAsync(tenantId, bucketName, command);
                });

            Field<ResultGraphType>()
                .Name("lockFile")
                .Description("Locks file")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("bucketName", "The bucket identifier")
                .Argument<NonNullGraphType<ListGraphType<StringGraphType>>>("keys", "File keys to be locked")
                .ResolveAsync(async context =>
                {
                    var keys = context.GetArgument<object[]>("keys")?.Select(o => o.ToString());
                    await Parallel.ForEachAsync(keys, DefaultParallelOptions, async (key, _) =>
                    {
                        await permissionValidator.Authorize(context, "lockFiles", key);
                    });
                    var tenantId = context.GetTenantIdFromToken();
                    var bucketName = context.GetArgument<string>("bucketName");
                    var command = new LockFileCommand(keys);

                    return await fileSystemService.LockFileAsync(tenantId, bucketName, command);
                });

            Field<ResultGraphType>()
                .Name("unlockFile")
                .Description("Unlocks file")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("bucketName", "The bucket identifier")
                .Argument<NonNullGraphType<ListGraphType<StringGraphType>>>("keys", "File keys to be unlocked")
                .ResolveAsync(async context =>
                {
                    var keys = context.GetArgument<object[]>("keys")?.Select(o => o.ToString());
                    await Parallel.ForEachAsync(keys, DefaultParallelOptions, async (key, _) =>
                    {
                        await permissionValidator.Authorize(context, "lockFiles", key);
                    });
                    var tenantId = context.GetTenantIdFromToken();
                    var bucketName = context.GetArgument<string>("bucketName");
                    var command = new UnlockFileCommand(keys);

                    return await fileSystemService.UnlockFileAsync(tenantId, bucketName, command);
                });
        }
    }
}