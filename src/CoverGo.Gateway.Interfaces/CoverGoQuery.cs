using System.Collections.Generic;
using System.Globalization;
using CoverGo.DateUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Products.Motor;
using CoverGo.Gateway.Domain.Scheduler;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Infrastructure.DBSServices;
using CoverGo.Gateway.Infrastructure.Versions;
using CoverGo.Gateway.Interfaces.Auth;
using CoverGo.Gateway.Interfaces.Motor;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Scheduler;
using CoverGo.Gateway.Interfaces.Users;
using CoverGo.Gateway.Interfaces.Versions;
using GraphQL;
using GraphQL.Authorization;
using GraphQL.DataLoader;
using GraphQL.Language.AST;
using GraphQL.Types;
using GraphQL.Validation;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Script = CoverGo.Gateway.Domain.Products.Script;
using ValidateResult = CoverGo.Gateway.Domain.Products.ValidateResult;

namespace CoverGo.Gateway.Interfaces
{
    using System;
    using System.Linq;
    using System.Security.Claims;
    using System.Security.Cryptography;
    using System.Text;
    using System.Threading.Tasks;
    using CoverGo.DomainUtils;
    using CoverGo.FeatureManagement;
    using CoverGo.Gateway.Common;
    using CoverGo.Gateway.Domain.Claims;
    using CoverGo.Gateway.Domain.Cms;
    using CoverGo.Gateway.Domain.Commissions;
    using CoverGo.Gateway.Domain.Encryptions;
    using CoverGo.Gateway.Domain.FileSystem;
    using CoverGo.Gateway.Domain.Notifications;
    using CoverGo.Gateway.Domain.Pdf;
    using CoverGo.Gateway.Domain.Templates;
    using CoverGo.Gateway.Infrastructure.AxaThServices;
    using CoverGo.Gateway.Infrastructure.BOCServices;
    using CoverGo.Gateway.Infrastructure.DLVNServices;
    using CoverGo.Gateway.Infrastructure.FubonServices;
    using CoverGo.Gateway.Infrastructure.InsuredNomadsServices;
    using CoverGo.Gateway.Infrastructure.TahoeServices;
    using CoverGo.Gateway.Infrastructure.TcbServices;
    using CoverGo.Gateway.Infrastructure.WfpServices;
    using CoverGo.Gateway.Interfaces.Achievements.AchievementTypes;
    using CoverGo.Gateway.Interfaces.Advisor;
    using CoverGo.Gateway.Interfaces.Binders;
    using CoverGo.Gateway.Interfaces.Cases;
    using CoverGo.Gateway.Interfaces.Claims;
    using CoverGo.Gateway.Interfaces.Cms;
    using CoverGo.Gateway.Interfaces.Commissions;
    using CoverGo.Gateway.Interfaces.Education.Courses;
    using CoverGo.Gateway.Interfaces.Files;
    using CoverGo.Gateway.Interfaces.NegotiatedRate;
    using CoverGo.Gateway.Interfaces.Notifications;
    using CoverGo.Gateway.Interfaces.Pdf;
    using CoverGo.Gateway.Interfaces.Templates;
    using CoverGo.Gateway.Interfaces.Transactions;
    using CoverGo.Users.Domain.Companies;
    using CoverGo.Users.Domain.Individuals;
    using CoverGo.Users.Domain.NegotiatedRate;
    using CoverGo.Users.Domain.Objects;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;
    using Object = CoverGo.Users.Domain.Objects.Object;
    using QueryArguments = global::GraphQL.Types.QueryArguments;

    public class CoverGoQuery : ObjectGraphType<object>
    {
        public CoverGoQuery(
            IDataLoaderContextAccessor accessor,
            IProductService productService,
            IPricingService pricingService,

            IMotorVenue motorVenue,

            IL10nService l10nService,
            IInsurerService insurerService,

            IPolicyService policyService,
            CoverGoPolicyMembersService policyMembersService,

            IAuthService authService,

            IEntityService userService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
            IEntityService<Object, CreateObjectCommand, UpdateObjectCommand> objectService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,

            IClaimService claimService,

            INotificationService notificationService,

            IFileSystemService fileSystemService,
            ICmsService cmsService,

            ITemplateService templateService,

            IEncryptionAlgorithm encryptionAlgorithm,

            IOptions<ServiceUrls> configs,
            ILogger<CoverGoQuery> logger,
            ICaseService caseService,

            TcbIntegrationService tcbService,
            ISchedulerService schedulerService,
            FubonIntegrationService fubonService,
            BocIntegrationService bocService,
            InsuredNomadsIntegrationService insuredNomadsService,
            DLVNIntegrationService dlvnService,
            TahoeIntegrationService tahoeService,
            WfpIntegrationService wfpService,
            IEntityService entityService,
            VersionsService versionsService,
            PermissionValidator permissionValidator,
            IHttpContextAccessor httpContextAccessor,
            IFeatureManager featureManager,
            PermittedIpList permittedIpList,
            IMultiTenantFeatureManager multiTenantFeatureManager)
        {

            Name = "covergoQuery";

            #region error tests

            Field<ResultGraphType>()
                 .Name("testerrors")
                 .Description("Api to test errors")
                 .Resolve(context =>
                 {
                     context.Errors.Add(new ExecutionError("test A"));
                     return Result.Failure("test A");
                 });

            Field<ResultGraphType>()
                .Name("testexception")
                .Description("Api to exception errors")
                .Resolve(_ =>
                {
                    throw new NullReferenceException();
                });

            Field<ResultGraphType>()
                .Name("testlogerror")
                .Description("Api to exception errors")
                .Resolve(_ =>
                {
                    logger.LogError("testing log error");
                    return Result.Failure("testing log error");
                });

            #endregion

            #region DBS GOOD Token for auth
            Field<ListGraphType<TokenGraphType>>()
                .Name("verifyGOODTokenAndGetCoverGoToken")
                .Description("verifies good token and does not make access token if fails")
                .Argument<StringGraphType>("token", "GOOD Token generated from GOOD SDK")
                .Argument<StringGraphType>("tenantId", "The tenant identifier")
                .Argument<StringGraphType>("clientId", "The client identifier")

                .Argument<StringGraphType>("username", "The username to login as")
                .Argument<StringGraphType>("password", "The password for this login")
                .ResolveAsync(async context =>
                {
                    string GOODToken = context.GetArgument<string>("token");

                    Result verifyGOODTokenResult = await GOODToken.VerifyAsync();
                    if (verifyGOODTokenResult.Status != "success")
                        throw new ValidationError("", "authorization", verifyGOODTokenResult?.Errors?.FirstOrDefault());

                    string tenantId = context.GetArgument<string>("tenantId");
                    string clientId = context.GetArgument<string>("clientId");
                    string username = context.GetArgument<string>("username");
                    string password = context.GetArgument<string>("password");

                    Token token = await authService.GetWholeAccessTokenAsync(tenantId, clientId, "", username, password);

                    return token;
                });
            #endregion

            #region auth

            FieldAsync<TokenGraphType>(
                           "token_2",
                           description: "Refresh the token",
                           arguments: new QueryArguments(
                               new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "tenantId", Description = "The tenant identifier" },
                               new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "clientId", Description = "The client identifier" },
                               new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "username" },
                               new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "password" },
                               new QueryArgument<StringGraphType> { Name = "twoFactorToken" },
                               new QueryArgument<StringGraphType> { Name = "twoFactorTemplateId" }
                           ),
                           resolve: async context =>
                           {
                               string tenantId = context.GetArgument<string>("tenantId");
                               string clientId = context.GetArgument<string>("clientId");

                               HttpContext? httpContext = httpContextAccessor.HttpContext;
                               if (httpContext != null)
                               {
                                   logger.LogInformation("IP Address Info - X-Forwarded-For: {XForwardedFor}, RemoteIpAddress: {RemoteIpAddress}, ClientIpAddress: {ClientIpAddress}",
                                       httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault(),
                                       httpContext.Connection.RemoteIpAddress,
                                       httpContext.GetClientIpAddress());

                                   List<string> ipAddresses = httpContext.GetClientIpAddresses();
                                   logger.LogInformation("IP Addresses: {IpAddresses}", string.Join(", ", ipAddresses));

                                   if (!permittedIpList.IsIncluded(clientId, tenantId, ipAddresses))
                                       throw new ValidationError("", "authorization", "Login is not allowed from your location.\n");
                               }

                               string username = context.GetArgument<string>("username");
                               string password = context.GetArgument<string>("password");

                               string twoFactorToken = context.GetArgument<string>("twoFactorToken");
                               string twoFactorTemplateId = context.GetArgument<string>("twoFactorTemplateId");

                               Dictionary<string, string> parameters = new()
                               {
                                   {"twoFactorToken", twoFactorToken},
                                   {"twoFactorTemplateId", twoFactorTemplateId},
                               };

                               Token token = await authService.GetWholeAccessTokenAsync(tenantId, clientId, "", username, password, parameters);

                               return token;
                           });

            FieldAsync<TokenGraphType>(
                "refreshToken",
                description: "Refresh the token",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "tenantId", Description = "The tenant identifier" },
                    new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "clientId", Description = "The client identifier" },
                    new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "refreshToken" }
                ),
                resolve: async context =>
                {
                    string tenantId = context.GetArgument<string>("tenantId");
                    string clientId = context.GetArgument<string>("clientId");
                    string refreshToken = context.GetArgument<string>("refreshToken");

                    Token token = await authService.RefreshTokenAsync(tenantId, clientId, "", refreshToken);

                    return token;
                });

            Field<NonNullGraphType<StringGraphType>>()
                .Name("permissioningToken")
                .Argument<ListGraphType<NonNullGraphType<AddTargettedPermissionInputGraphType>>>("targettedPermissionsToDelegate", "")
                .Argument<ListGraphType<NonNullGraphType<AddTargettedPermissionInputGraphType>>>("targettedPermissionsToRequest", "")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    // check that the requested the permissions to delegate are valid
                    List<AddTargettedPermissionCommand> targettedPermissionsToDelegate = context.GetArgument<List<AddTargettedPermissionCommand>>("targettedPermissionsToDelegate");
                    foreach (AddTargettedPermissionCommand addTargettedPermissionCommand in targettedPermissionsToDelegate)
                        await permissionValidator.Authorize(context, addTargettedPermissionCommand.Type, addTargettedPermissionCommand.Value);

                    List<AddTargettedPermissionCommand> targettedPermissionsToRequest = context.GetArgument<List<AddTargettedPermissionCommand>>("targettedPermissionsToRequest");

                    // get encrypt config
                    var encryptionParameters = new Dictionary<string, object>
                    {
                        { "keyString", "E98912CB4BA6FFAB2EC0E24ED92B0D8B" }
                    };

                    // transform commands to a json string
                    var permissioningObject = new PermissioningObject
                    {
                        DelegatinLoginId = loginId,
                        ToRequest = targettedPermissionsToRequest,
                        ToDelegate = targettedPermissionsToDelegate
                    };
                    string targettedPermissionCommandsJsonString = JsonConvert.SerializeObject(permissioningObject);

                    // encrypt all targetted permissions
                    (string encryptedValue, Dictionary<string, object>) result = await encryptionAlgorithm.EncryptAsync(tenantId, targettedPermissionCommandsJsonString, encryptionParameters);

                    return result.encryptedValue;
                });

            Field<PasswordValidatorsGraphType>()
                .Name("passwordValidators")
                .Description("Get the password validators")
                .Argument<NonNullGraphType<StringGraphType>>("tenantId", "The tenant identifier")
                .Argument<StringGraphType>("clientId", "The client identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetArgument<string>("tenantId");
                    string clientId = context.GetArgument<string>("clientId");
                    PasswordValidators passwordValidators = await authService.GetPasswordValidators(tenantId, clientId);
                    return passwordValidators;
                });

            Field<ResultGraphType>()
                .Name("validatePassword")
                .Description("Gets a result of a password validation")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("password", "The password to validate")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();
                    string password = context.GetArgument<string>("password");
                    Result result = await authService.ValidatePasswordAsync(tenantId, password);
                    return result;
                });

            FieldAsync<NonNullGraphType<LoginsGraphType>>("logins", "Get a list of logins",
                arguments: new QueryArguments(GraphQLToolsExtensions.GetGenericQueryArguments) {
                    new QueryArgument<ListGraphType<StringGraphType>> { Name = "ids", Description = "The ids of the logins" },
                    new QueryArgument<ListGraphType<StringGraphType>> { Name = "usernames", Description = "The usernames of the logins" },
                    new QueryArgument<BooleanGraphType> {Name = "isEmailConfirmed", Description ="checks whether the condition matches the given input"},
                    new QueryArgument<BooleanGraphType> {Name = "isTelephoneNumberConfirmed", Description ="checks whether the condition matches the given input"},
                    new QueryArgument<BooleanGraphType> {Name = "excludePermissions", Description ="excludes permissions in logins"},
                    new QueryArgument<ListGraphType<UserEnumerationGraphType>> { Name = "userTypes", Description = "Filter by user type. All if null." },
                    new QueryArgument<LoginWhereGraphType> { Name = "where", Description = "A login search filter" }
                },
                resolve: async context =>
                {
                    context.PassArgumentsToChildren();

                    return new LoginsGraph
                    {
                        Ids = context.GetArgument<List<string>>("ids"),
                        Usernames = context.GetArgument<List<string>>("usernames"),
                        UserTypes = context.GetArgument<EntityTypes[]>("userTypes"),
                        IsEmailConfirmed = context.GetArgument<bool?>("isEmailConfirmed"),
                        IsTelephoneNumberConfirmed = context.GetArgument<bool?>("isTelephoneNumberConfirmed"),
                        ExcludePermissions = context.GetArgument<bool?>("excludePermissions"),
                        Skip = context.GetArgument<int?>("skip"),
                        Limit = context.GetArgument<int?>("limit"),
                        Sort = context.GetArgument<SortGraph>("sort"),
                        Where = context.GetArgument<LoginWhere>("where"),
                    };
                }).AuthorizeWith("any");

            Field<LoginGraphType>()
                .Name("login")
                .AuthorizeWith("any")
                .Description("Get a single login from username")
                .Argument<NonNullGraphType<StringGraphType>>("username", "The username")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    if (tenantId == "tcb" || tenantId == "tcb_uat")
                        await permissionValidator.Authorize(context, "readLogins", "all"); // remove vulnerability, NEED for tcb

                    Login login = await authService.GetLoginByNameAsync(context.GetTenantIdFromToken(), context.GetArgument<string>("username"));
                    return LoginGraph.ToGraph(login);
                }).AuthorizeWith("any");

            FieldAsync<ListGraphType<PermissionGraphType>>("permissions", "gets all permissions",
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, "readTargettedPermissions");

                    IEnumerable<string> permissions = await authService.GetAllPermissionsAsync(tenantId);

                    return permissions.Select(p => new PermissionGraph
                    {
                        Id = p
                    });
                }).AuthorizeWith("any");

            Field<NonNullGraphType<ListGraphType<NonNullGraphType<PermissionGroupGraphType>>>>()
                .AuthorizeWith("any")
                .Name("permissionGroups")
                .Description("get all permission groups")
                .Argument<PermissionGroupWhereInputGraphType>("where", "A permission group search filter")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, "readPermissionGroups");

                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readPermissionGroups");

                    PermissionGroupWhere whereInput = context.GetArgument<PermissionGroupWhere>("where");
                    List<PermissionGroupWhere> andFilter = allowedIds.Contains("all")
                        ? whereInput != null ? new List<PermissionGroupWhere> { whereInput } : new List<PermissionGroupWhere>()
                        : new List<PermissionGroupWhere>
                            {
                                whereInput ?? new PermissionGroupWhere(),
                                new() { Id_in = allowedIds.ToList() }
                            };

                    IEnumerable<PermissionGroup> permissionGroupDaos = await authService.GetPermissionGroupsAsync(tenantId, new PermissionGroupWhere { And = andFilter });

                    return permissionGroupDaos.Select(p => PermissionGroupGraph.ToGraph(p, permissionGroupDaos));
                });

            Field<NonNullGraphType<TenantSettingsGraphType>>()
                .Name("tenantSettings")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, "readApps");
                    return await authService.GetTenantSettingsAsync(tenantId);
                });

            Field<NonNullGraphType<AppsGraphType>>()
                .Name("apps")
                .Description("Gets a list of apps")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<AppWhereInputGraphType>("where", "A company search filter")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();

                    return new AppsGraph();
                });

            Field<NonNullGraphType<PermissionSchemasGraphType>>()
                .Name("permissionSchemas")
                .Description("Gets a list of permissionSchemas")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<PermissionSchemaWhereInputGraphType>("where", "A permission schema search filter")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    PermissionSchemaWhere where = context.GetArgument<PermissionSchemaWhere>("where") ?? new PermissionSchemaWhere();

                    var allowedIds = (await permissionValidator.GetTargetIdsFromClaim(context, "readPermissionSchemas")).ToList();
                    if (!allowedIds.Contains("all"))
                        where = new PermissionSchemaWhere
                        {
                            And = new List<PermissionSchemaWhere> {
                                where,
                                new() { Id_in = allowedIds }
                            }
                        };

                    long totalCount = await authService.GetTotalCount(tenantId, where);

                    int? skip = context.GetArgument<int?>("skip");
                    int? first = context.GetArgument<int?>("limit");
                    SortGraph sort = context.GetArgument<SortGraph>("sort");

                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    Domain.QueryArguments queryArguments = new() { Where = where, Skip = skip, First = first, OrderBy = orderBy };
                    IEnumerable<PermissionSchema> permissionSchemas = await authService.GetPermissionSchemas(tenantId, queryArguments);

                    return new PermissionSchemas
                    {
                        TotalCount = totalCount,
                        List = permissionSchemas
                    };
                });

            #endregion

            FieldAsync<LoginGraphType>("me", "Gets the user related to the passed token.",
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string appId = context.GetClientIdFromToken();

                    Login loginDao = await authService.GetLoginById(tenantId, loginId, appId);

                    var login = LoginGraph.ToGraph(loginDao);

                    return login;
                }).AuthorizeWith("any");

            FieldAsync<EntityInterfaceGraphType>("searchUserByCode", "Search a user by code",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "tenantId", Description = "The tenant identifier" },
                    new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "code", Description = "The code of the internal" }
                ),
                resolve: async context =>
                {
                    string tenantId = context.GetArgument<string>("tenantId");
                    string code = context.GetArgument<string>("code");

                    var searchFilter = new EntityWhere { InternalCode = code };
                    Task<IEnumerable<Internal>> internalDtosTask = internalService.GetAsync(tenantId, searchFilter);
                    Task<IEnumerable<Company>> companyDtosTask = companyService.GetAsync(tenantId, searchFilter);
                    Task<IEnumerable<Individual>> customerDtosTask = individualService.GetAsync(tenantId, searchFilter);

                    await Task.WhenAll(internalDtosTask, companyDtosTask, customerDtosTask);

                    return internalDtosTask.Result.FirstOrDefault()?.ToGraph()
                        ?? companyDtosTask.Result.FirstOrDefault()?.ToGraph()
                        ?? customerDtosTask.Result.FirstOrDefault()?.ToGraph();
                });

            FieldAsync<StringGraphType>("termsAndConditions", "Get the terms and conditions for a specific tenant and client",
                arguments: new QueryArguments(
                    new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "tenantId", Description = "The tenant identifier" },
                    new QueryArgument<NonNullGraphType<StringGraphType>> { Name = "clientId", Description = "The client identifier" }),
                resolve: async context =>
                {
                    string tenantId = context.GetArgument<string>("tenantId");
                    string clientId = context.GetArgument<string>("clientId");

                    var l10ns = await l10nService.GetL10nsAsync(tenantId, CultureInfo.CurrentCulture.Name, new List<string> { $"{clientId}-termsAndConditions" }) as Dictionary<string, string>;

                    return l10ns.GetValueOrDefault($"{clientId}-termsAndConditions");
                });

            #region products

            Field<ListGraphType<BenefitCategoriesPerTypeGraphType>>()
                .Name("benefitCategories")
                .AuthorizeWith("any")
                .Description("Gets the benefits categories of the tenants")
                .Argument<ListGraphType<StringGraphType>>("productTypes", "The product types")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();
                    IEnumerable<string> productTypes = context.GetArgument<string[]>("productTypes");

                    Dictionary<string, Dictionary<string, List<string>>> benefitCategories = await productService.GetBenefitInfosAsync(tenantId, clientId);

                    List<BenefitCategoriesPerTypeGraph> result = benefitCategories == null
                      ? new List<BenefitCategoriesPerTypeGraph>()
                      : productTypes?
                          .Select(pt => benefitCategories.FirstOrDefault(bc => bc.Key.Equals(pt, StringComparison.InvariantCultureIgnoreCase)))?
                          .Select(a => new BenefitCategoriesPerTypeGraph
                          {
                              ProductType = a.Key,
                              Categories = a.Value?.Select(c => new BenefitCategoryGraph
                              {
                                  Id = c.Key,
                                  BenefitTypeIds = c.Value  //to be changed to output a graph instead of just benefitTypeIds
                              }).ToList()
                          }).ToList();

                    return result;
                });

            Field<ListGraphType<BenefitInfoGraphType>>()
                .Name("benefitInfos")
                .Description("Gets the benefits infos of the tenants")
                .Argument<NonNullGraphType<StringGraphType>>("tenantId", "The tenant identifier")
                .Argument<StringGraphType>("clientId", "The client identifier")
                .Argument<ListGraphType<StringGraphType>>("productTypes", "The product types")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetArgument<string>("tenantId");
                    string clientId = context.GetArgument<string>("clientId");
                    (context.UserContext as GraphQLUserContext)?.User.AddIdentity(new ClaimsIdentity(new[] { new System.Security.Claims.Claim("tenantId", tenantId) }));
                    IEnumerable<string> productTypes = context.GetArgument<string[]>("productTypes");

                    Dictionary<string, Dictionary<string, List<string>>> benefitInfos = await productService.GetBenefitInfosAsync(tenantId, clientId);

                    return benefitInfos == null
                        ? new List<BenefitInfoGraph>()
                        : productTypes?
                        .SelectMany(pt => benefitInfos.FirstOrDefault(db => db.Key.Equals(pt, StringComparison.InvariantCultureIgnoreCase)).Value?
                        .SelectMany(a => a.Value.Select(bid => (typeId: bid, category: a.Key)))
                        .GroupBy(big => big.typeId).Select(g => new BenefitInfoGraph
                        {
                            TypeId = g.Key,
                            Type = g.First().category,
                            Categories = g.Select(v => v.category).ToArray()
                        }));
                });

            Field<ListGraphType<ProductTypeGraphType>>()
              .Name("productTypes")
              .AuthorizeWith("any")
              .Description("Gets a list of productTypes")
              .Argument<ProductTypeWhereInputGraphType>("where", "A product type search filter")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string clientId = context.GetClientIdFromToken();

                  IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readProductTypes");
                  ProductTypeWhere where = allowedIds.Contains("all")
                    ? context.GetArgument<ProductTypeWhere>("where") ?? new ProductTypeWhere()
                    : new ProductTypeWhere
                    {
                        And = new List<ProductTypeWhere>
                        {
                            context.GetArgument<ProductTypeWhere>("where") ?? new ProductTypeWhere(),
                            new() { TypeId_in = allowedIds.ToList() }
                        }
                    };

                  IEnumerable<ProductType> productTypes = await productService.GetTypesAsync(tenantId, clientId, where);

                  return productTypes?.Select(t => ProductTypeGraph.ToGraph(t));
              });


            FieldAsync<ListGraphType<InsurerGraphType>>(
                "insurers",
                description: "Gets the list of insurers for a specific client",
                arguments: new QueryArguments(
                    new QueryArgument<StringGraphType> { Name = "tenantId", Description = "The tenant identifier" },
                    new QueryArgument<ListGraphType<StringGraphType>> { Name = "ids", Description = "The insurerIds" },
                    new QueryArgument<ListGraphType<StringGraphType>> { Name = "productTypes", Description = "product types" }
                ),
                resolve: async context =>
                {
                    string tenantId = context.GetTenantIdFromToken(false) ?? context.GetArgument<string>("tenantId");
                    string clientId = context.GetClientIdFromToken(false);
                    (context.UserContext as GraphQLUserContext)?.User.AddIdentity(new ClaimsIdentity(new[] { new System.Security.Claims.Claim("tenantId", tenantId) }));
                    IEnumerable<string> ids = context.GetArgument<object[]>("ids")?.Select(o => o.ToString());
                    IEnumerable<string> types = context.GetArgument<IEnumerable<string>>("productTypes");

                    IEnumerable<Insurer> insurers = await insurerService.GetInsurersAsync(tenantId, clientId, new InsurerFilter { Ids = ids, ProductTypes = types });

                    return insurers.Select(i => InsurerGraph.ToGraph(i));
                });

            Field<ProductsGraphType>()
                .Name("products_2")
                .AuthorizeWith("any")
                .Description("Get products")
                .GetPaginationArguments()
                .Argument<ProductWhereInputGraphType>("where", "A product search filter")
                .Argument<ListGraphType<KeyValueInputGraphType>>("values", "A list of dynamic values to check underwriting rules")
                .Argument<StringGraphType>("valuesJsonString", "A list of dynamic values to check underwriting rules")
                .Argument<ListGraphType<BenefitOptionInputGraphType>>("benefitOptions", "a list of benefitOptions selected")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();
                    int? skip = context.GetArgument<int?>("skip");
                    int? limit = context.GetArgument<int?>("limit");
                    bool totalCountRequested = context.SubFields.TryGetValue("totalCount", out _);
                    SortGraph sort = context.GetArgument<SortGraph>("sort");

                    bool loadRepresentation = context.ProductRepresentationRequested();
                    ProductWhere where = await CombineWithAllowedIds(
                        context.GetArgument<ProductWhere>("where") ?? new ProductWhere(),
                        authService,
                        permissionValidator,
                        context);

                    await ReplaceNameContainsWithProductIdFilterAsync(tenantId, where, l10nService);

                    IEnumerable<KeyScalarValue> values = context.GetArgument<IEnumerable<KeyScalarValue>>("values");
                    string valuesJsonString = context.GetArgument<string>("valuesJsonString");

                    JToken factorsAsToken = values != null
                        ? JToken.FromObject(values.ToDictionary(x => x.Key, x => x.Value.GetValue()))
                        : valuesJsonString != null
                            ? JToken.Parse(valuesJsonString)
                            : null;

                    var query = new ProductQuery { Where = where, Factors = factorsAsToken, LoadRepresentation = loadRepresentation, OrderBy = sort?.ToOrderBy(), Skip = skip, Limit = limit };
                    List<Product2> products = (await productService.GetAsync(tenantId, clientId, query)).ToList();

                    IEnumerable<BenefitOptionInputGraph> benefitOptionInputs = context.GetArgument<IEnumerable<BenefitOptionInputGraph>>("benefitOptions");

                    if (benefitOptionInputs != null)
                    {
                        IEnumerable<Benefit> benefitOptions = benefitOptionInputs.Select(s => new Benefit
                        {
                            TypeId = s.TypeId,
                            OptionKey = s.Key,
                            Value = JToken.FromObject(s.Value?.GetValue()),
                        });

                        foreach (Product2 product in products)
                        {
                            product.Benefits = ProductExtensions.SummarizeBenefits(benefitOptions, product.Benefits);
                        }
                    }

                    List<ProductGraph> productGraphs = products.Select(p => p.ToGraph()).ToList();

                    bool isOptimizedQueryEnabled = await featureManager.IsEnabledAsync("OptimizedProductsQueryEnabled");
                    if (!isOptimizedQueryEnabled)
                    {
                        return new ProductsGraph
                        {
                            TotalCount = productGraphs.Count,
                            List = productGraphs.Sort(context)
                        };
                    }

                    ProductsGraph graph = new() { List = productGraphs };

                    if (!totalCountRequested) return graph;

                    if (skip is null or <= 0 && (limit is null or <= 0 || limit > products.Count))
                    {
                        graph.TotalCount = products.Count;
                    }
                    else if (factorsAsToken == null)
                    {
                        graph.TotalCount = (int)await productService.GetTotalCountAsync(tenantId, clientId, where);
                    }
                    else
                    {
                        ProductQuery countQuery = new() { Where = query.Where, Factors = query.Factors, LoadRepresentation = false };
                        IEnumerable<Product2> totalProducts = await productService.GetAsync(tenantId, clientId, countQuery);
                        graph.TotalCount = totalProducts.Count();
                    }

                    return graph;
                });

            FieldAsync<ListGraphType<MotorMakeGraphType>>(
               "makes",
               description: "Gets a list of motor manufacturers",
               arguments: new QueryArguments
               {
                    new QueryArgument<StringGraphType> { Name = "id", Description = "The id of makes" }
               },
               resolve: async context =>
               {
                   string id = context.GetArgument<string>("id");
                   return id != null
                       ? await motorVenue.GetMakesAsync(new List<string> { id })
                       : await motorVenue.GetAllMakesAsync();
               });

            Field<ListGraphType<ValidateResultGraphType>>()
              .Name("validateProducts")
              .AuthorizeWith("any")
              .Description("Get validation of products")
              .GetPaginationArguments()
              .Argument<ProductWhereInputGraphType>("where", "A product search filter")
              .Argument<NonNullGraphType<ListGraphType<KeyValueInputGraphType>>>("values", "A list of dynamic values to check underwriting rules")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string clientId = context.GetClientIdFromToken();

                  IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readProducts");
                  ProductWhere where = allowedIds.Contains("all")
                      ? context.GetArgument<ProductWhere>("where") ?? new ProductWhere()
                      : new ProductWhere
                      {
                          And = new List<ProductWhere>
                          {
                                context.GetArgument<ProductWhere>("where") ?? new ProductWhere(),
                                new() { Id_in = allowedIds.Select(aId => ProductId.FromString(aId)).ToList() }
                          }
                      };

                  await ReplaceNameContainsWithProductIdFilterAsync(tenantId, where, l10nService);

                  var factors = context.GetArgument<IEnumerable<KeyScalarValue>>("values")?.ToDictionary(x => x.Key, x => x.Value.GetValue());
                  JToken factorsAsToken = factors != null ? JToken.FromObject(factors) : null;

                  var query = new ProductQuery { Where = where, Factors = factorsAsToken };
                  IEnumerable<ValidateResult> validations = await productService.ValidateProductsAsync(tenantId, clientId, query);

                  return validations;
              });

            Field<ListGraphType<PlanGraphType>>()
             .Name("plans")
             .AuthorizeWith("any")
             .Description("Get plans")
             .GetPaginationArguments()
             .Argument<PlanWhereInputGraphType>("where", "A product search filter")
             .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string clientId = context.GetClientIdFromToken();

                  IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readPlans");
                  ProductWhere where = allowedIds.Contains("all")
                      ? context.GetArgument<ProductWhere>("where") ?? new ProductWhere()
                      : new ProductWhere
                      {
                          And = new List<ProductWhere>
                          {
                                context.GetArgument<ProductWhere>("where") ?? new ProductWhere(),
                                new() { Id_in = allowedIds?.Select(i => ProductId.FromString(i))?.ToList() }
                          }
                      };

                  int? skip = context.GetArgument<int?>("skip");
                  int? first = context.GetArgument<int?>("limit");
                  SortGraph sort = context.GetArgument<SortGraph>("sort");

                  OrderBy orderBy = null;
                  if (sort != null)
                  {
                      orderBy = sort.ToOrderBy();
                  }

                  IEnumerable<Product2> plans = await productService.GetAsync(tenantId, clientId, new ProductQuery { Where = where });

                  return plans?.Select(p => PlanGraph.ToGraph(p));
              });

            Field<ListGraphType<ProductConfigGraphType>>()
              .Name("productConfigs")
              .AuthorizeWith("any")
              .Description("Get plans")
              .GetPaginationArguments()
              .Argument<ProductConfigWhereInputGraphType>("where", "A product search filter")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string clientId = context.GetClientIdFromToken();

                  var allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,
                      "readProductConfigs");
                  ProductConfigWhere where = allowedIds.Contains("all")
                         ? context.GetArgument<ProductConfigWhere>("where") ?? new ProductConfigWhere()
                         : new ProductConfigWhere
                         {
                             And = new List<ProductConfigWhere>
                               {
                                    context.GetArgument<ProductConfigWhere>("where") ?? new ProductConfigWhere(),
                                    new() { Id_in = allowedIds?.Select(i => i)?.ToList() }
                               }
                         };

                  int? skip = context.GetArgument<int?>("skip");
                  int? first = context.GetArgument<int?>("limit");
                  SortGraph sort = context.GetArgument<SortGraph>("sort");

                  OrderBy orderBy = null;
                  if (sort != null)
                  {
                      orderBy = sort.ToOrderBy();
                  }

                  IEnumerable<ProductConfig> plans = await productService.GetProductConfigsAsync(tenantId, new Domain.QueryArguments { Where = where, OrderBy = orderBy, Skip = skip, First = first });

                  return plans?.Select(p => ProductConfigGraph.ToGraph(p));
              });

            Field<UiSchemasGraphType>()
                .Name("uiSchemas")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<UiSchemaWhereInputGraphType>("where", "A uiSchema search filter")
                .Description("Get uiSchemas")
                .ResolveAsync(async context =>
                  {
                      await permissionValidator.Authorize(context, "readProducts", "all");
                      string tenantId = context.GetTenantIdFromToken();
                      UiSchemaWhere where = context.GetArgument<UiSchemaWhere>("where") ?? new UiSchemaWhere();

                      IEnumerable<UiSchema> uiSchemas = await productService.GetUiSchemasAsync(tenantId, where);
                      IReadOnlyCollection<UiSchemaGraph> uiSchemaGraphs = uiSchemas.Select(s => new UiSchemaGraph
                      {
                          Id = s.Id,
                          Name = s.Name,
                          Schema = s.Schema?.ToString() ?? string.Empty,
                          Standard = s.Standard,
                          CreatedAt = s.CreatedAt,
                          LastModifiedAt = s.LastModifiedAt
                      }).ToArray();

                      return new UiSchemasGraph
                      {
                          TotalCount = uiSchemaGraphs.Count,
                          List = uiSchemaGraphs.Sort(context)
                      };
                  });

            #endregion

            #region users

            Field<NonNullGraphType<CompaniesGraphType>>()
                .Name("companies")
                .Description("Gets a list of companies")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<CompanyWhereInputGraphType>("where", "A company search filter")
                .Argument<DateTimeOffsetGraphType>("asOf", "a time travel filter")
                .Argument<StringGraphType>("specialty", "a doctor specialty filter")
                .ResolveAsync(async context =>
                {
                    List<string> allowedIds = (await permissionValidator.GetTargetIdsFromClaim(context, "readCompanies")).ToList();
                    string specialty = context.GetArgument<string>("specialty");
                    string tenantId = context.GetTenantIdFromToken();
                    CompanyWhere where = context.GetArgument<CompanyWhere>("where") ?? new CompanyWhere();

                    if (!(allowedIds.Contains("all") && string.IsNullOrWhiteSpace(specialty)))
                    {
                        where = new CompanyWhere { And = new List<CompanyWhere> { where } };

                        if (!allowedIds.Contains("all"))
                            where.And.Add(new CompanyWhere { Id_in = allowedIds });

                        if (!string.IsNullOrWhiteSpace(specialty))
                        {
                            List<string> ids = await GetCompanyIdsBySpecialty();
                            where.And.Add(new CompanyWhere { Id_in = ids });
                        }
                    }

                    GraphQLToolsExtensions.GetQueryArguments(context, out int? skip, out int? first, out OrderBy orderBy);
                    DateTime? asOf = context.GetArgument<DateTime?>("asOf");
                    Domain.QueryArguments queryArguments = new() { Where = where, Skip = skip, First = first, OrderBy = orderBy, AsOf = asOf };

                    TotalCountResult<CompanyGraph> totalCountResult = await context.ResolveEntitiesAsync<Company, CreateCompanyCommand, UpdateCompanyCommand, CompanyGraph>(
                         companyService, queryArguments);

                    return new CompaniesGraph
                    {
                        TotalCount = totalCountResult.TotalCount,
                        List = totalCountResult.Items
                    };

                    async Task<List<string>> GetCompanyIdsBySpecialty()
                    {
                        IEnumerable<Internal> internals = await internalService.GetAsync(tenantId,
                            new InternalWhere
                            {
                                And = new List<InternalWhere>
                                {
                                    new()
                                    {
                                        Tags_contains = "doctor",
                                    },
                                    new()
                                    {
                                        Status_in = new List<string> { null, "ACTIVE" },
                                    },
                                    new()
                                    {
                                        Fields = new FieldsWhere
                                        {
                                            Path = "fields.specialty",
                                            Condition = FieldsWhereCondition.Equals,
                                            Value = new ScalarValue { StringValue = specialty }
                                        }
                                    }
                                }
                            });

                        const string doctorOfLinkType = "doctorOf";

                        IEnumerable<Relationships> relationships = await userService.GetRelationshipsAsync(tenantId, new Domain.QueryArguments
                        {
                            Where = new RelationshipWhere
                            {
                                Link = new LinkFilter
                                {
                                    SourceId_in = internals.Select(i => i.Id).ToList(),

                                    Type = doctorOfLinkType
                                }
                            }
                        });

                        return relationships
                            .SelectMany(i => i.Links)
                            .Where(i => i.Type == doctorOfLinkType)
                            .Select(i => i.TargetId)
                            .Distinct()
                            .ToList();
                    }
                });

            Field<NonNullGraphType<IndividualsGraphType>>()
                .Name("individuals")
                .Description("Gets a list of external individuals")
                .AuthorizeWith("any")
                .GetPaginationArguments(defaultPageLimit: 10000) // Setting a default limit of 10000 to avoid memory issues
                .Argument<IndividualWhereInputGraphType>("where", "An individual search filter")
                .Argument<DateTimeOffsetGraphType>("asOf", "a time travel filter")
                .Argument<BooleanGraphType>("checkActivity", "Check individuals activity based on policies membership")
                .Argument<StringGraphType>("policyId", "filter by policyId")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken(isRequired:false);
                    string role = context.GetRoleFromToken(isRequired: false);
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readIndividuals");
                    IndividualWhere inputWhere = context.GetArgument<IndividualWhere>("where") ?? new IndividualWhere();
                    string policyId = context.GetArgument<string>("policyId");
                    if (policyId != null)
                    {
                        Policy policy = (await policyService.GetAsync(tenantId,
                            new PolicyWhere { Id = policyId })).FirstOrDefault();
                        List<string> insuredIds = policy?.ContractInsured?.Select(x => x.Id).ToList() ?? new List<string>();

                        inputWhere = new IndividualWhere
                        {
                            And = new List<IndividualWhere>
                            {
                                inputWhere,
                                new() { Id_in = insuredIds }
                            }
                        };
                    }

                    if (inputWhere.Product != null)
                    {
                        ProductWhere productWhere = await CombineWithAllowedIds(
                            inputWhere.Product,
                            authService,
                            permissionValidator,
                            context);

                        inputWhere.Product = null;

                        await ReplaceNameContainsWithProductIdFilterAsync(tenantId, productWhere, l10nService);

                        ProductQuery query = new() { Where = productWhere };
                        IEnumerable<Product2> products = await productService.GetAsync(
                            tenantId,
                            clientId,
                            query);

                        List<Policy> policies = await FetchPolicies(
                            tenantId,
                            policyService,
                            new PolicyWhere
                            {
                                ProductId_in = products.Select(a => a.Id).ToList(),
                            }
                        );

                        List<string> insuredIds = policies?
                            .Where(p => p.ContractInsured is not null)
                            .SelectMany(p => p.ContractInsured)
                            .Where(i => i is not null)
                            .Select(i => i.Id)
                            .ToList();

                        inputWhere = new IndividualWhere
                        {
                            And = new List<IndividualWhere>
                            {
                                inputWhere,
                                new() { Id_in = insuredIds }
                            }
                        };
                    }

                    IndividualWhere where = allowedIds.Contains("all")
                        ? inputWhere
                        : new IndividualWhere
                        {
                            And = new List<IndividualWhere>
                            {
                                inputWhere,
                                new() { Id_in = allowedIds.ToList() },
                            }
                        };

                    GraphQLToolsExtensions.GetQueryArguments(context, out int? skip, out int? first,
                        out OrderBy orderBy);
                    DateTime? asOf = context.GetArgument<DateTime?>("asOf");

                    var queryArguments = new Domain.QueryArguments
                    {
                        Where = where,
                        Skip = skip,
                        First = first,
                        OrderBy = orderBy,
                        AsOf = asOf
                    };

                    TotalCountResult<IndividualGraph> totalCountResult = await context.ResolveEntitiesAsync
                        <Individual, CreateIndividualCommand, UpdateIndividualCommand, IndividualGraph>(
                            individualService, queryArguments);

                    IndividualGraph[] individuals =
                        totalCountResult?.Items?.ToArray() ?? Array.Empty<IndividualGraph>();

                    bool checkActivity = context.GetArgument<bool?>("checkActivity") ?? false;

                    if (checkActivity)
                    {
                        List<PoliciesPerIndividual> policesPerIndividual =
                            await policyMembersService.PoliciesPerIndividualsQueryAsync(tenantId,
                                new PoliciesPerIndividualFilter
                                {
                                    IndividualIds = individuals.Select(x => x.Id).ToArray(),
                                    AsOf = asOf
                                }, context.CancellationToken);

                        foreach (IndividualGraph individual in individuals)
                        {
                            individual.HasActivePolicy =
                                policesPerIndividual.Any(i => i.IndividualId == individual.Id && i.PolicyIds.Any());
                        }
                    }

                    return new IndividualsGraph
                    {
                        TotalCount = totalCountResult?.TotalCount ?? default,
                        List = individuals
                    };
                });

            Field<NonNullGraphType<IndividualsAgentCodeGraphType>>()
               .Name("individualAgentCode")
               .Description("Gets a list of agent code, name, and source for each individual")
               .AuthorizeWith("any")
               .GetPaginationArguments()
               .Argument<IndividualWhereInputGraphType>("where", "An individual search filter")
               .Argument<DateTimeOffsetGraphType>("asOf", "a time travel filter")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();

                   // for security requirements this api must be disabled.
                   if (tenantId != "tcb" && tenantId != "tcb_uat")
                       return new Result<byte[]> { Status = "failure", Errors = new List<string> { $"You are not authorized to run this." } };


                   IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readIndividuals");
                   IndividualWhere where = context.GetArgument<IndividualWhere>("where");

                   GraphQLToolsExtensions.GetQueryArguments(context, out int? skip, out int? first, out OrderBy orderBy);
                   DateTime? asOf = context.GetArgument<DateTime?>("asOf");

                   var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy, AsOf = asOf };

                   TotalCountResult<IndividualGraph> totalCountResult = await context.ResolveEntitiesAsync
                   <Individual, CreateIndividualCommand, UpdateIndividualCommand, IndividualGraph>(
                       individualService, queryArguments);

                   return new IndividualsGraph
                   {
                       TotalCount = totalCountResult.TotalCount,
                       List = totalCountResult.Items
                   };
               });


            Field<NonNullGraphType<InternalsGraphType>>()
                .Name("internals")
                .Description("Gets a list of internal users")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<InternalWhereInputGraphType>("where", "An internal search filter")
                .Argument<DateTimeOffsetGraphType>("asOf", "a time travel filter")
                .ResolveAsync(async context =>
                {
                    InternalWhere where = await BuildFilterByTargetIds();
                    GraphQLToolsExtensions.GetQueryArguments(context, out int? skip, out int? first, out OrderBy orderBy);
                    DateTime? asOf = context.GetArgument<DateTime?>("asOf");

                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy, AsOf = asOf };

                    TotalCountResult<InternalGraph> totalCountResult = await context.ResolveEntitiesAsync
                    <Internal, CreateInternalCommand, UpdateInternalCommand, InternalGraph>(
                        internalService, queryArguments);

                    return new InternalsGraph
                    {
                        TotalCount = totalCountResult.TotalCount,
                        List = totalCountResult.Items
                    };

                    async Task<InternalWhere> BuildFilterByTargetIds()
                    {
                        InternalWhere internalWhere = new()
                        {
                            And = new List<InternalWhere>
                            {
                                context.GetArgument<InternalWhere>("where") ?? new InternalWhere()
                            }
                        };

                        List<string> targetIds = (await permissionValidator.GetTargetIdsFromClaim(context, "readInternals")).ToList();
                        if (targetIds.Contains("all"))
                        {
                            return internalWhere;
                        }

                        List<string> tags = targetIds.Where(t => t.StartsWith("tag:")).Select(t => t.Replace("tag:", "")).ToList();
                        if (tags.Count > 0)
                        {
                            internalWhere.And.Add(new InternalWhere { Tags_in = tags });
                            targetIds.RemoveAll(t => t.StartsWith("tag:"));
                        }

                        if (targetIds.Count > 0)
                        {
                            internalWhere.And.Add(new InternalWhere { Id_in = targetIds });
                        }

                        return internalWhere;
                    }
                });

            Field<NonNullGraphType<ObjectsGraphType>>()
                .Name("objects")
                .Description("Get a list of objects")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<ObjectWhereInputGraphType>("where", "An object search filter")
                .Argument<DateTimeOffsetGraphType>("asOf", "a time travel filter")
                .ResolveAsync(async context =>
                {
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readObjects");
                    ObjectWhere where = allowedIds.Contains("all")
                        ? context.GetArgument<ObjectWhere>("where") ?? new ObjectWhere()
                        : new ObjectWhere
                        {
                            And = new List<ObjectWhere>
                            {
                                context.GetArgument<ObjectWhere>("where") ?? new ObjectWhere(),
                                new() { Id_in = allowedIds.ToList() }
                            }
                        };

                    INode eventNode = context.SubFields.FirstOrDefault(sf => sf.Key == "list").Value?.Children.FirstOrDefault(ch => ch is SelectionSet)?.Children.FirstOrDefault(ch => (ch as Field)?.Name == "events");
                    GraphQLToolsExtensions.GetQueryArguments(context, out int? skip, out int? first, out OrderBy orderBy);
                    DateTime? asOf = context.GetArgument<DateTime?>("asOf");

                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy, AsOf = asOf };

                    TotalCountResult<ObjectGraph> totalCountResult = await context.ResolveEntitiesAsync
                    <Object, CreateObjectCommand, UpdateObjectCommand, ObjectGraph>(
                        objectService, queryArguments);

                    return new ObjectsGraph
                    {
                        TotalCount = totalCountResult.TotalCount,
                        List = totalCountResult.Items
                    };
                });

            Field<NonNullGraphType<OrganizationsGraphType>>()
                .Name("organizations")
                .Description("Get a list of organizations")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<OrganizationWhereInputGraphType>("where", "An organization search filter")
                .Argument<DateTimeOffsetGraphType>("asOf", "a time travel filter")
                .ResolveAsync(async context =>
                {
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readOrganizations");
                    OrganizationWhere where = allowedIds.Contains("all")
                        ? context.GetArgument<OrganizationWhere>("where") ?? new OrganizationWhere()
                        : new OrganizationWhere
                        {
                            And = new List<OrganizationWhere>
                            {
                                context.GetArgument<OrganizationWhere>("where") ?? new OrganizationWhere(),
                                new() { Id_in = allowedIds.ToList() }
                            }
                        };

                    INode eventNode = context.SubFields.FirstOrDefault(sf => sf.Key == "list").Value?.Children.FirstOrDefault(ch => ch is SelectionSet)?.Children.FirstOrDefault(ch => (ch as Field)?.Name == "events");
                    GraphQLToolsExtensions.GetQueryArguments(context, out int? skip, out int? first, out OrderBy orderBy);
                    DateTime? asOf = context.GetArgument<DateTime?>("asOf");

                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy, AsOf = asOf };

                    TotalCountResult<OrganizationGraph> totalCountResult = await context.ResolveEntitiesAsync
                    <Organization, CreateOrganizationCommand, UpdateOrganizationCommand, OrganizationGraph>(
                        organizationService, queryArguments);

                    return new OrganizationsGraph
                    {
                        TotalCount = totalCountResult.TotalCount,
                        List = totalCountResult.Items
                    };
                });
            Field<ServiceItemsGraphType>()
                .Name("serviceItems")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<ServiceItemWhereInputGraphType>("where", "A serviceItem search filter")
                .Description("Get serviceItem")
                .ResolveAsync(async context =>
                  {
                      string tenantId = context.GetTenantIdFromToken();
                      ServiceItemWhere where = context.GetArgument<ServiceItemWhere>("where") ?? new ServiceItemWhere();

                      IEnumerable<ServiceItem> items = await entityService.GetServiceItemsAsync(tenantId, where);

                      return new ServiceItemsGraph
                      {
                          TotalCount = items.Count(),
                          List = items.Sort(context)
                      };
                  });

            Field<ServiceItemAgreedFeesGraphType>()
                .Name("serviceItemAgreedFee")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<ServiceItemAgreedFeeWhereInputGraphType>("where", "A serviceItemAgreedFee search filter")
                .Description("Get serviceItemAgreedFee")
                .ResolveAsync(async context =>
                  {
                      string tenantId = context.GetTenantIdFromToken();
                      ServiceItemAgreedFeeWhere where = context.GetArgument<ServiceItemAgreedFeeWhere>("where") ?? new ServiceItemAgreedFeeWhere();

                      IEnumerable<ServiceItemAgreedFee> fees = await entityService.GetServiceItemAgreedFeesAsync(tenantId, where);
                      IReadOnlyCollection<ServiceItemAgreedFeeGraph> feeGraph = fees.Select(fee => ServiceItemAgreedFeeGraph.ToGraph(fee)).ToArray();

                      return new ServiceItemAgreedFeesGraph
                      {
                          TotalCount = feeGraph.Count(),
                          List = feeGraph.Sort(context)
                      };
                  });

            Field<PanelProviderTiersGraphType>()
                .Name("panelProviderTiers")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<PanelProviderTierWhereInputGraphType>("where", "A panelProviderTier search filter")
                .Description("Get panelProviderTiers")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();
                    return new PanelProviderTiersGraph();
                });

            Field<DisabilitiesGraphType>()
                .Name("disabilities")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<DisabilityFilterInputGraph>("where", "A disability search filter")
                .Description("Get disabilities")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "readDisabilities");
                    context.PassArgumentsToChildren();
                    return new DisabilitiesGraph();
                });

            Field<DiagnosesGraphType>()
                .Name("diagnoses")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<DiagnosisFilterInputGraph>("where", "A diagnosis search filter")
                .Description("Get diagnoses")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "readDiagnoses");
                    context.PassArgumentsToChildren();
                    return new DiagnosesGraph();
                });
            #endregion

            #region policies

            Field<PoliciesGraphType>()
                .Name("policies")
                .AuthorizeWith("any")
                .Description("Gets a list of policies")
                .GetPaginationArguments(defaultPageLimit: 101) // Setting unusual 101 limit so that queries with default limits are more easily recognizable in logs
                .Argument<PolicyWhereInputGraphType>("where", "A policy search filter")
                .Argument<DateTimeOffsetGraphType>("asOf", "a time travel filter")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();

                    return new PoliciesGraph();
                });

            Field<PolicyGraphType>()
                .Name("policy")
                .AuthorizeWith("any")
                .Description("Gets single policy by id")
                .Argument<StringGraphType>("policyId", "Policy Id")
                .Argument<DateTimeOffsetGraphType>("asOf", "a time travel filter")
                .ResolveAsync(async context =>
                {
                    context.PassArgumentsToChildren();
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    DateTimeOffset? asOf = context.GetArgument<DateTimeOffset?>("asOf");

                    var policy = await policyService.GetAsync(tenantId, policyId, new Domain.AsOf { DateTime = asOf?.DateTime });
                    return PolicyGraph.ToGraph(policy);
                });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("validatePolicy_2")
                .DeprecationReason("use the new validatePolicy query")
                .AuthorizeWith("any")
                .Description("Validates the policy")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy id")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    Result result = await policyService.ValidatePolicy_2Async(tenantId, policyId);

                    return result;
                });

            Field<NonNullGraphType<ValidatePolicyResultGraphType>>()
                .Name("validatePolicy")
                .AuthorizeWith("any")
                .Description("Validates the policy")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The policy id")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");

                    ValidatePolicyResult result = await policyService.ValidatePolicyAsync(tenantId, policyId);

                    return result;
                });

            #endregion

            #region transactions

            Field<TransactionsGraphType>()
                .Name("transactions")
                .AuthorizeWith("any")
                .Description("Gets a list of transactions")
                .GetPaginationArguments()
                .Argument<TransactionWhereInputGraphType>("where", "A transaction search filter")
                .Argument<DateTimeOffsetGraphType>("asOf", "a time travel filter")
                .Resolve(context =>
                {
                    TransactionWhere where = context.GetArgument<TransactionWhere>("where");
                    DateTime? asOf = context.GetArgument<DateTime?>("asOf");
                    int? skip = context.GetArgument<int?>("skip");
                    int? first = context.GetArgument<int?>("limit");
                    SortGraph sort = context.GetArgument<SortGraph>("sort");

                    return new TransactionsGraph { Where = where, AsOf = asOf, Skip = skip, First = first, Sort = sort };
                });

            #endregion

            #region claims

            Field<ClaimsGraphType>()
                .Name("claims")
                .AuthorizeWith("any")
                .Description("Gets a list of claims")
                .GetPaginationArguments()
                .Argument<ClaimWhereInputGraphType>("where", "A claim search filter")
                .Argument<DateTimeOffsetGraphType>("asOf", "a time travel filter")
                .Argument<GroupByGraphType>("groupBy", "Group by filter")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();

                    return new ClaimsGraph();
                });

            Field<ListGraphType<FactTemplateGraphType>>()
                .Name("claimFactTemplates")
                .AuthorizeWith("any")
                .Description("Gets a list of templates for claim facts")
                .Argument<ListGraphType<StringGraphType>>("ids", "A list of template ids")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> ids = context.GetArgument<List<string>>("ids");
                    IEnumerable<FactTemplate> templates = await claimService.GetFactTemplatesAsync(tenantId, ids);

                    return templates.Select(t => FactTemplateGraph.ToGraph(t));
                });

            Field<GuaranteeOfPaymentsGraphType>()
                .Name("gops")
                .AuthorizeWith("any")
                .Description("Gets a list of GOPs")
                .GetPaginationArguments()
                .Argument<GuaranteeOfPaymentWhereInputGraphType>("where", "A GOP search filter")
                .Argument<DateTimeOffsetGraphType>("asOf", "a time travel filter")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();

                    return new GuaranteeOfPaymentsGraph();
                });


            Field<JacketsGraphType>()
                .Name("jackets")
                .AuthorizeWith("any")
                .Description("Gets a list of jackets")
                .GetPaginationArguments()
                .Argument<JacketWhereInputGraphType>("where", "A jacket search filter")
                .Argument<DateTimeOffsetGraphType>("asOf", "a time travel filter")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();
                    return new JacketsGraph();
                });

            Field<TreatmentsGraphType>()
                .Name("treatments")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<TreatmentFilterInputGraph>("where", "A treatment search filter")
                .Description("Get treatments")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "readTreatments");
                    context.PassArgumentsToChildren();
                    return new TreatmentsGraph();
                });

            Field<ClaimReportsGraphType>()
                .Name("claimReports")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<ClaimReportFilterInputGraph>("where", "A claim report search filter")
                .Description("Get claim reports")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "readClaimReports");
                    context.PassArgumentsToChildren();
                    return new ClaimReportsGraph();
                });
            #endregion

            #region binders

            Field<BindersGraphType>()
                .Name("binders")
                .AuthorizeWith("any")
                .Description("Gets a list of binders")
                .GetPaginationArguments()
                .Argument<BinderWhereInputGraphType>("where", "A binder search filter")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();

                    return new BindersGraph();
                });

            #endregion

            #region notifications

            Field<NonNullGraphType<NotificationsGraphType>>()
                .Name("notifications")
                .AuthorizeWith("any")
                .Description("Get the list of notifications")
                .GetPaginationArguments()
                .Argument<NotificationWhereInputGraphType>("where", "Notification filter")
                .Resolve(context =>
                {
                    NotificationWhere where = context.GetArgument<NotificationWhere>("where");
                    int? skip = context.GetArgument<int?>("skip");
                    int? first = context.GetArgument<int?>("limit");
                    SortGraph sort = context.GetArgument<SortGraph>("sort");

                    return new NotificationsGraph { Where = where, Skip = skip, First = first, Sort = sort };
                });

            Field<ListGraphType<NotificationTriggerGraphType>>()
                .Name("notificationTriggers")
                .AuthorizeWith("any")
                .Description("Get the list of notification triggers")
                .Argument<NotificationTriggerWhereInputGraphType>("where", "Notification trigger filter")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> entityIds = context.GetArgument<object[]>("entityIds")?.Select(o => o.ToString());

                    NotificationTriggerWhere where = context.GetArgument<NotificationTriggerWhere>("where") ?? new NotificationTriggerWhere();
                    IEnumerable<NotificationTrigger> triggers = await notificationService.GetNotificationTriggersAsync(tenantId, where);

                    return triggers.Select(t => NotificationTriggerGraph.ToGraph(t));
                });

            Field<NotificationSubscriptionsGraphType>()
               .Name("notificationSubscriptions")
               .AuthorizeWith("any")
               .Description("notification subscriptions")
               .Argument<NotificationSubscriptionWhereInputGraphType>("where", "a notification subscription search filter")
               .GetPaginationArguments()
               .Resolve(context =>
               {
                   context.PassArgumentsToChildren();

                   return new NotificationSubscriptionsGraph();
               });

            Field<ConnectedChatUsersGraphType>()
                .Name("getUsersInRoom")
                .Description("connected chat room users")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("id", "the name of the chat room")
                .Argument<StringGraphType>("topicName", "the name of the chat room")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string id = context.GetArgument<string>("id");
                    string topic = context.GetArgument<string>("topicName");

                    return await notificationService.GetUsersInRoomAsync(tenantId, id ?? topic);
                });

            Field<ListGraphType<NotificationConfigGraphType>>()
                .Name("notificationConfigs")
                .AuthorizeWith("any")
                .Description("Get the notification configs")
                .Argument<NotificationConfigWhereInputGraphType>("where", "Notification filter")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, "readNotificationConfigs");

                    NotificationConfigWhere where = context.GetArgument<NotificationConfigWhere>("where");

                    return await notificationService.GetNotificationConfigsAsync(tenantId, where);
                });


            #endregion

            #region payments


            #endregion

            #region files

            Field<ListGraphType<BucketGraphType>>()
                .Name("fileBuckets")
                .AuthorizeWith("any")
                .Description("Get a list of file bucket")
                .Argument<BucketWhereInputGraphType>("where", "The bucket filter")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    FileSystemConfigWhere where = context.GetArgument<FileSystemConfigWhere>("where") ?? new FileSystemConfigWhere { };

                    IEnumerable<FileSystemConfig> fsConfigs = await fileSystemService.GetConfigsAsync(tenantId, where);
                    return fsConfigs;
                });

            Field<ListGraphType<FileSystemConfigGraphType>>()
                .Name("fileSystemConfigs")
                .AuthorizeWith("any")
                .Description("Get a list of file system configs")
                .GetPaginationArguments()
                .Argument<FileSystemConfigWhereInputGraphType>("where", "A file system config search filter")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readFileSystemConfigs");

                    if (allowedIds == null || !allowedIds.Any())
                    {
                        return Enumerable.Empty<FileSystemConfigGraph>();
                    }

                    FileSystemConfigWhere where = allowedIds.Contains("all")
                        ? context.GetArgument<FileSystemConfigWhere>("where") ?? new FileSystemConfigWhere()
                        : new FileSystemConfigWhere
                        {
                            And = new List<FileSystemConfigWhere>
                            {
                            context.GetArgument<FileSystemConfigWhere>("where") ?? new FileSystemConfigWhere(),
                            new() { Id_in = allowedIds.Select(i => i)?.ToList() }
                            }
                        };

                    int? skip = context.GetArgument<int?>("skip");
                    int? first = context.GetArgument<int?>("limit");
                    SortGraph sort = context.GetArgument<SortGraph>("sort");

                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    IEnumerable<FileSystemConfig> fsConfigs = await fileSystemService.GetConfigsAsync(tenantId, new Domain.QueryArguments { Where = where, OrderBy = orderBy, Skip = skip, First = first });

                    return fsConfigs?.Select(p => FileSystemConfigGraph.ToGraph(p));
                });

            Field<NonNullGraphType<ListGraphType<NonNullGraphType<FileSummaryGraphType>>>>()
                .Name("searchFiles")
                .AuthorizeWith("any")
                .Description("Get a list of files")
                .Argument<StringGraphType>("bucketName", "The targetted bucket name")
                .Argument<FileWhereInputGraphType>("where", "The filter")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    FileWhere where = context.GetArgument<FileWhere>("where") ?? new FileWhere();

                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readFiles");

                    string bucketName = context.GetArgument<string>("bucketName");

                    Result<IEnumerable<ObjectSummary>> result = await fileSystemService.QueryAsync(tenantId, bucketName, new FileWhere
                    {
                        KeyPattern = where.KeyPattern,
                        AllowedPrefixes = allowedIds.Contains("all") ? null : allowedIds
                    });

                    if (result.Value?.Any() == true)
                        foreach (ObjectSummary summary in result.Value)
                            summary.BucketName = bucketName;

                    return result.Value;
                });

            Field<FileListingGraphType>()
                .Name("fileListing")
                .AuthorizeWith("any")
                .Description("Get a list of files")
                .Argument<StringGraphType>("bucketName", "The targetted bucket name")
                .Argument<StringGraphType>("prefix", "The path prefix")
                .Argument<StringGraphType>("tenantId", "if present, will be used to get public files of the specified tenant")
                .Argument<StringGraphType>("continuationToken", "used for continuation to get next the batch of records")
                .Argument<IntGraphType>("limit", "limit the number of files")
                .Argument<IntGraphType>("skip", "skips the N number of files")
                .Argument<StringGraphType>("sort", "sort by created_date or filename")
                .Argument<StringGraphType>("filename_contains", "filter by file name")
                .ResolveAsync(async context =>
                {
                    return (await FetchFileListing(context, fileSystemService, permissionValidator, logger))?.Value;
                });

            Field<FileListingWithResultGraphType>()
                .Name("fileListingWithResult")
                .AuthorizeWith("any")
                .Description("Get a list of files")
                .Argument<StringGraphType>("bucketName", "The targetted bucket name")
                .Argument<StringGraphType>("prefix", "The path prefix")
                .Argument<StringGraphType>("tenantId", "if present, will be used to get public files of the specified tenant")
                .Argument<StringGraphType>("continuationToken", "used for continuation to get next the batch of records")
                .Argument<IntGraphType>("limit", "limit the number of files")
                .Argument<IntGraphType>("skip", "skips the N number of files")
                .Argument<StringGraphType>("sort", "sort by created_date or filename")
                .Argument<StringGraphType>("filename_contains", "filter by file name")
                .ResolveAsync(async context =>
                {
                    return await FetchFileListing(context, fileSystemService, permissionValidator, logger);
                });

            #endregion

            #region cms

            Field<ComponentsGraphType>()
                .Name("components")
                .Description("Get components")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<ComponentWhereInputGraphType>("where", "A component search filter")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();

                    return new ComponentsGraph();
                });

            Field<ListGraphType<CmsConfigGraphType>>()
                .Name("cmsConfigs")
                .Description("Get all the cms configs")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<CmsConfigWhereInputGraphType>("where", "A component search filter")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "readCmsConfigs");
                    string tenantId = context.GetTenantIdFromToken();

                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readCms");
                    CmsConfigWhere where = allowedIds.Contains("all")
                        ? context.GetArgument<CmsConfigWhere>("where") ?? new CmsConfigWhere() ?? new CmsConfigWhere()
                        : new CmsConfigWhere
                        {
                            And = new List<CmsConfigWhere>
                            {
                                context.GetArgument<CmsConfigWhere>("where")  ?? new CmsConfigWhere() ?? new CmsConfigWhere(),
                                new() { Id_in = allowedIds.ToList() }
                            }
                        };

                    int? skip = context.GetArgument<int?>("skip");
                    int? first = context.GetArgument<int?>("limit");
                    SortGraph sort = context.GetArgument<SortGraph>("sort");
                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy };

                    IEnumerable<CmsConfig> cmsConfigs = await cmsService.GetConfigsAsync(tenantId, queryArguments);
                    return cmsConfigs?.Select(c => CmsConfigGraph.ToGraph(c, tenantId));
                });

            Field<CmsConfigResultGraphType>()
                .Name("cmsConfig")
                .Description("Get the cms for a particular url")
                .Argument<NonNullGraphType<StringGraphType>>("url", "The app url")
                .ResolveAsync(async context =>
                {
                    string url = context.GetArgument<string>("url");

                    Result<TenantIdAndAppId> tenantIdAndAppIdResult = await authService.GetTenantIdAndAppIdByUrlAsync(url);
                    if (tenantIdAndAppIdResult.Status != "success")
                        return new Result<CmsConfigGraph>
                        {
                            Status = tenantIdAndAppIdResult.Status,
                            Errors = tenantIdAndAppIdResult.Errors,
                            Errors_2 = tenantIdAndAppIdResult.Errors_2
                        };

                    CmsConfig config = await cmsService.GetConfigAsync(tenantIdAndAppIdResult.Value.TenantId, tenantIdAndAppIdResult.Value.AppId);

                    return config != null ?
                        new Result<CmsConfigGraph> { Status = "success", Value = CmsConfigGraph.ToGraph(config, tenantIdAndAppIdResult.Value.TenantId) }
                        : new Result<CmsConfigGraph> { Status = "failure", Errors = new List<string> { $"There are was no config found for url {url}." } };
                });

            #endregion

            #region pricing

            Field<ListGraphType<PriceGraphType>>()
                .Name("exchangeRateConvert")
                .Description("Converts rates")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<ListGraphType<PriceInputGraphType>>>("prices", "The prices to convert")
                .Argument<NonNullGraphType<ExchangeRateConversionOptionsInputGraphType>>("options", "Conversion options")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    List<Price> pricesToConvert = context.GetArgument<List<Price>>("prices");
                    ExchangeRateConversionOptions options = context.GetArgument<ExchangeRateConversionOptions>("options");

                    var priceInput = new ExchangeRateConversionInput
                    {
                        Prices = pricesToConvert.Select(p => new PriceDto
                        {
                            Amount = p.Amount,
                            CurrencyCode = p.CurrencyCode
                        }),
                        Options = options
                    };

                    IEnumerable<PriceDto> convertedPriceDtos = await pricingService.GetConvertedPricingsAsync(tenantId, priceInput);
                    IEnumerable<PriceGraph> convertedPrice = convertedPriceDtos.Select(p => new PriceGraph
                    {
                        Amount = p.Amount.Value,
                        CurrencyCode = p.CurrencyCode
                    });

                    return convertedPrice;
                });

            #endregion

            #region templates

            Field<TemplatesGraphType>()
                .Name("templates")
                .AuthorizeWith("any")
                .Description("Gets a list of templates")
                .GetPaginationArguments()
                .Argument<TemplateWhereInputGraphType>("where", "A policy search filter")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();

                    return new TemplatesGraph();
                });

            Field<EmailRenderedResultGraphType>()
                .Name("renderEmailMjmlTemplate")
                .AuthorizeWith("any")
                .Description("Renders the email mjml template")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template identifier")
                .Argument<RenderParametersInputGraphType>("input", "Parameters in order to render the template")
                .Argument<BooleanGraphType>("includeAttachments", "If true, will add attachments")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    RenderParameters renderParameters = context.GetArgument<RenderParameters>("input");
                    bool includeAttachments = context.GetArgument<bool>("includeAttachments");

                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, renderParameters, out List<string> errors);
                    if (errors.Any())
                        return new Result<EmailRendered> { Status = "failure", Errors = errors };

                    Result<EmailRendered> emailRenderedResult = await templateService.RenderEmailAsync(tenantId, templateId, includeAttachments, renderParameters);

                    return emailRenderedResult;
                });

            Field<SmsRenderedResultGraphType>()
                .Name("renderSmsTemplate")
                .AuthorizeWith("any")
                .Description("Renders the sms template")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template identifier")
                .Argument<RenderParametersInputGraphType>("input", "Parameters in order to render the template")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    RenderParameters renderParameters = context.GetArgument<RenderParameters>("input");

                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, renderParameters, out List<string> errors);
                    if (errors.Any())
                        return new Result<SmsRendered> { Status = "failure", Errors = errors };

                    Result<SmsRendered> smsRenderedResult = await templateService.RenderSmsAsync(tenantId, templateId, renderParameters);

                    return smsRenderedResult;
                });

            Field<StringResultGraphType>()
               .Name("renderHtmlTemplate")
               .AuthorizeWith("any")
               .Description("Renders the html template")
               .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template identifier")
               .Argument<RenderParametersInputGraphType>("input", "Parameters in order to render the template")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string templateId = context.GetArgument<string>("templateId");
                   RenderParameters renderParameters = context.GetArgument<RenderParameters>("input");

                   Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, renderParameters, out List<string> errors);
                   if (errors.Any())
                       return new Result<string> { Status = "failure", Errors = errors };

                   Result<string> renderedResult = await templateService.RenderHtmlTemplateAsync(tenantId, templateId, renderParameters);

                   return renderedResult;
               });

            Field<StringResultGraphType>()
                .Name("renderHtml")
                .AuthorizeWith("any")
                .Description("Renders the html")
                .Argument<NonNullGraphType<StringGraphType>>("html", "The html to render")
                .Argument<RenderParametersInputGraphType>("input", "Parameters in order to render the template")
                .ResolveAsync(async context =>
                {
                    string html = context.GetArgument<string>("html");
                    string tenantId = context.GetTenantIdFromToken();
                    RenderParameters renderParameters = context.GetArgument<RenderParameters>("input");

                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, renderParameters, out List<string> errors);
                    if (errors.Any())
                        return new Result<string> { Status = "failure", Errors = errors };

                    RenderHtmlCommand command = new()
                    {
                        Html = html,
                        RenderParameters = renderParameters
                    };

                    Result<string> renderedResult = await templateService.RenderHtmlAsync(tenantId, command);

                    return renderedResult;
                });

            Field<StringResultGraphType>()
                .Name("renderEmailMjml")
                .AuthorizeWith("any")
                .Description("Renders the email Mjml")
                .Argument<NonNullGraphType<StringGraphType>>("emailMjml", "The emailMjml to render")
                .Argument<RenderParametersInputGraphType>("input", "Parameters in order to render the template")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string emailMjml = context.GetArgument<string>("emailMjml");
                    RenderParameters renderParameters = context.GetArgument<RenderParameters>("input");

                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, renderParameters, out List<string> errors);
                    if (errors.Any())
                        return new Result<string> { Status = "failure", Errors = errors };

                    RenderEmailMjmlCommand command = new()
                    {
                        EmailMjml = emailMjml,
                        RenderParameters = renderParameters
                    };

                    Result<string> renderedResult = await templateService.RenderEmailMjmlAsync(tenantId, command);

                    return renderedResult;
                });

            Field<SmsRenderedResultGraphType>()
                .Name("renderSms")
                .AuthorizeWith("any")
                .Description("Renders the sms")
                .Argument<NonNullGraphType<StringGraphType>>("bodyLiquid", "The bodyLiquid to render")
                .Argument<RenderParametersInputGraphType>("input", "Parameters in order to render the template")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string bodyLiquid = context.GetArgument<string>("bodyLiquid");
                    RenderParameters renderParameters = context.GetArgument<RenderParameters>("input");

                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, renderParameters, out List<string> errors);
                    if (errors.Any())
                        return new Result<string> { Status = "failure", Errors = errors };

                    var command = new RenderSmsCommand
                    {
                        BodyLiquid = bodyLiquid,
                        RenderParameters = renderParameters
                    };

                    Result<SmsRendered> renderedResult = await templateService.RenderSmsAsync(tenantId, command);

                    return renderedResult;
                });


            Field<PdfRenderedResultGraphType>()
                .Name("renderPdfDrawingTemplate")
                .AuthorizeWith("any")
                .Description("Renders the pdf drawing template")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template identifier")
                .Argument<RenderParametersInputGraphType>("input", "Parameters in order to render the template")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    RenderParameters renderParameters = context.GetArgument<RenderParameters>("input");

                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, renderParameters, out List<string> errors);
                    if (errors.Any())
                        return new Result<byte[]> { Status = "failure", Errors = errors };

                    Result<byte[]> emailRenderedResult = await templateService.RenderPdfDrawingAsync(tenantId, templateId, renderParameters);

                    return emailRenderedResult;
                });

            Field<StringResultGraphType>()
                .Name("renderPdfDrawingTemplateV2")
                .AuthorizeWith("any")
                .Description("Renders the pdf drawing template")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template identifier")
                .Argument<RenderParametersInputGraphType>("input", "Parameters in order to render the template")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    RenderParameters renderParameters = context.GetArgument<RenderParameters>("input");

                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, renderParameters, out List<string> errors);
                    if (errors.Any())
                        return new Result<string> { Status = "failure", Errors = errors };

                    Result<string> renderedResult = await templateService.RenderPdfDrawingV2Async(tenantId, templateId, renderParameters);

                    return renderedResult;
                });

            Field<PdfRenderedResultGraphType>()
                .Name("renderWkhtmltopdfTemplate")
                .AuthorizeWith("any")
                .Description("Renders the wkhtmltopdf template")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template identifier")
                .Argument<RenderParametersInputGraphType>("input", "Parameters in order to render the template")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    RenderParameters renderParameters = context.GetArgument<RenderParameters>("input");

                    if (await multiTenantFeatureManager.IsEnabled("DisallowHtmlJavascriptForWkhtmlToPdfTemplate",tenantId) && AxaThHelper.IsWkhtmlToPdfInputValid(renderParameters?.ContentJsonString))
                        return Result.Failure("contentJsonString field in the query is Invalid.");

                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, renderParameters, out List<string> errors);
                    if (errors.Any())
                        return new Result<byte[]> { Status = "failure", Errors = errors };

                    Result<byte[]> emailRenderedResult = await templateService.RenderWkhtmltopdfAsync(tenantId, templateId, renderParameters);

                    return emailRenderedResult;
                });

            Field<StringResultGraphType>()
                .Name("renderWkhtmltopdfTemplateV2")
                .AuthorizeWith("any")
                .Description("Renders the wkhtmltopdf template in a background job, returns the jobId immediately and the file will be uploaded to a provided path when done.")
                .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template identifier")
                .Argument<NonNullGraphType<StringGraphType>>("outputFilePath", "Path to upload the rendered file in CoverGo Studio.")
                .Argument<RenderParametersInputGraphType>("input", "Parameters in order to render the template")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string templateId = context.GetArgument<string>("templateId");
                    string outputFilePath = context.GetArgument<string>("outputFilePath");
                    RenderParameters renderParameters = context.GetArgument<RenderParameters>("input");

                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, renderParameters, out List<string> errors);
                    if (errors.Any())
                        return new Result<string> { Status = "failure", Errors = errors };

                    return await templateService.RenderWkhtmltopdfAsync(tenantId, templateId, outputFilePath, renderParameters);
                });

            Field<PdfRenderedResultGraphType>()
            .Name("renderWkhtmltopdf")
            .AuthorizeWith("any")
            .Description("Renders the wkhtmltopdf")
            .Argument<NonNullGraphType<StringGraphType>>("html", "The html to render")
            .Argument<MarginSettingsInputGraphType>("marginSettings", "The margin settings")
            .Argument<HeaderFooterSettingsInputGraphType>("headerSettings", "The header settings")
            .Argument<HeaderFooterSettingsInputGraphType>("footerSettings", "The footer settings")
            .Argument<OrientationEnumerationGraphType>("orientation", "The orientation")
            .Argument<RenderParametersInputGraphType>("input", "Parameters in order to render the pdf")
            .Argument<StringGraphType>("password", "Password for the pdf")
            .ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();

                // for tcb security requirements this api must be disabled.
                if (tenantId == "tcb" || tenantId == "tcb_uat")
                    return new Result<byte[]> { Status = "failure", Errors = new List<string> { $"You are not authorized to run this." } };

                string html = context.GetArgument<string>("html");
                MarginSettings marginSettings = context.GetArgument<MarginSettings>("marginSettings");
                Orientation? orientation = context.GetArgument<Orientation?>("orientation");
                HeaderFooterSettings headerSettings = context.GetArgument<HeaderFooterSettings>("headerSettings");
                HeaderFooterSettings footerSettings = context.GetArgument<HeaderFooterSettings>("footerSettings");
                RenderParameters renderParameters = context.GetArgument<RenderParameters>("input");
                string password = context.GetArgument<string>("password");

                Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, renderParameters, out List<string> errors);
                if (errors.Any())
                    return new Result<byte[]> { Status = "failure", Errors = errors };

                RenderWkhtmltopdfCommand command = new()
                {
                    Html = html,
                    MarginSettings = marginSettings,
                    HeaderSettings = headerSettings,
                    FooterSettings = footerSettings,
                    RenderParameters = renderParameters,
                    Orientation = orientation,
                    Password = password
                };

                Result<byte[]> emailRenderedResult = await templateService.RenderWkhtmltopdfAsync(tenantId, command);

                return emailRenderedResult;
            });

            Field<StringResultGraphType>()
            .Name("renderWkhtmltopdfV2")
            .AuthorizeWith("any")
            .Description("Renders the wkhtmltopdf")
            .Argument<NonNullGraphType<StringGraphType>>("html", "The html to render")
            .Argument<MarginSettingsInputGraphType>("marginSettings", "The margin settings")
            .Argument<HeaderFooterSettingsInputGraphType>("headerSettings", "The header settings")
            .Argument<HeaderFooterSettingsInputGraphType>("footerSettings", "The footer settings")
            .Argument<OrientationEnumerationGraphType>("orientation", "The orientation")
            .Argument<RenderParametersInputGraphType>("input", "Parameters in order to render the pdf")
            .ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();

                // for tcb security requirements this api must be disabled.
                if (tenantId == "tcb" || tenantId == "tcb_uat")
                    return new Result<string> { Status = "failure", Errors = new List<string> { $"You are not authorized to run this." } };

                string html = context.GetArgument<string>("html");
                MarginSettings marginSettings = context.GetArgument<MarginSettings>("marginSettings");
                Orientation? orientation = context.GetArgument<Orientation?>("orientation");
                HeaderFooterSettings headerSettings = context.GetArgument<HeaderFooterSettings>("headerSettings");
                HeaderFooterSettings footerSettings = context.GetArgument<HeaderFooterSettings>("footerSettings");
                RenderParameters renderParameters = context.GetArgument<RenderParameters>("input");

                Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, renderParameters, out List<string> errors);
                if (errors.Any())
                    return new Result<string> { Status = "failure", Errors = errors };

                RenderWkhtmltopdfCommand command = new()
                {
                    Html = html,
                    MarginSettings = marginSettings,
                    HeaderSettings = headerSettings,
                    FooterSettings = footerSettings,
                    RenderParameters = renderParameters,
                    Orientation = orientation
                };

                return await templateService.RenderWkhtmltopdfV2Async(tenantId, command);
            });

            Field<PdfRenderedResultGraphType>()
            .Name("renderWkhtmltopdf2")
            .AuthorizeWith("any")
            .Description("Renders the wkhtmltopdf with multiple page objects")
            .Argument<NonNullGraphType<ListGraphType<PageObjectInputGraphType>>>("pageObjects", "The list of page object settings")
            .Argument<MarginSettingsInputGraphType>("marginSettings", "The margin settings")
            .Argument<OrientationEnumerationGraphType>("orientation", "The orientation")
            .Argument<RenderParametersInputGraphType>("input", "Parameters in order to render the pdf")
            .ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();

                // for tcb security requirements this api must be disabled.
                if (tenantId == "tcb" || tenantId == "tcb_uat")
                    return new Result<byte[]> { Status = "failure", Errors = new List<string> { $"You are not authorized to run this." } };

                Orientation? orientation = context.GetArgument<Orientation?>("orientation");
                MarginSettings marginSettings = context.GetArgument<MarginSettings>("marginSettings");
                List<PageObject> pageObjects = context.GetArgument<List<PageObject>>("pageObjects");
                RenderParameters renderParameters = context.GetArgument<RenderParameters>("input");

                Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, renderParameters, out List<string> errors);
                if (errors.Any())
                    return new Result<byte[]> { Status = "failure", Errors = errors };

                RenderWkhtmltopdfCommand command = new()
                {
                    MarginSettings = marginSettings,
                    Orientation = orientation,
                    PageObjects = pageObjects,
                    RenderParameters = renderParameters
                };

                Result<byte[]> renderedResult = await templateService.RenderWkhtmltopdfAsync(tenantId, command);

                return renderedResult;
            });

            Field<StringResultGraphType>()
            .Name("renderWkhtmltopdf2V2")
            .AuthorizeWith("any")
            .Description("Renders the wkhtmltopdf with multiple page objects")
            .Argument<NonNullGraphType<ListGraphType<PageObjectInputGraphType>>>("pageObjects", "The list of page object settings")
            .Argument<MarginSettingsInputGraphType>("marginSettings", "The margin settings")
            .Argument<OrientationEnumerationGraphType>("orientation", "The orientation")
            .Argument<RenderParametersInputGraphType>("input", "Parameters in order to render the pdf")
            .ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();

                // for tcb security requirements this api must be disabled.
                if (tenantId == "tcb" || tenantId == "tcb_uat")
                    return new Result<string> { Status = "failure", Errors = new List<string> { $"You are not authorized to run this." } };

                Orientation? orientation = context.GetArgument<Orientation?>("orientation");
                MarginSettings marginSettings = context.GetArgument<MarginSettings>("marginSettings");
                List<PageObject> pageObjects = context.GetArgument<List<PageObject>>("pageObjects");
                RenderParameters renderParameters = context.GetArgument<RenderParameters>("input");

                Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, renderParameters, out List<string> errors);
                if (errors.Any())
                    return new Result<string> { Status = "failure", Errors = errors };

                RenderWkhtmltopdfCommand command = new()
                {
                    MarginSettings = marginSettings,
                    Orientation = orientation,
                    PageObjects = pageObjects,
                    RenderParameters = renderParameters
                };

                return await templateService.RenderWkhtmltopdfV2Async(tenantId, command);
            });

            Field<FunctionRenderOutputGraphType>()
              .Name("renderFunctionTemplate")
              .AuthorizeWith("any")
              .Description("Renders the function template")
              .Argument<NonNullGraphType<StringGraphType>>("templateId", "The template identifier")
              .Argument<RenderParametersInputGraphType>("input", "Parameters in order to render the template")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string templateId = context.GetArgument<string>("templateId");
                  RenderParameters renderParameters = context.GetArgument<RenderParameters>("input");

                  Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, renderParameters, out List<string> errors);
                  if (errors.Any())
                      return new Result<IEnumerable<Output>> { Status = "failure", Errors = errors };

                  Result<IEnumerable<Output>> renderResult = await templateService.RenderFunctionTemplateAsync(tenantId, templateId, renderParameters);

                  return new Result<IEnumerable<OutputGraph>> { Status = renderResult.Status, Errors = renderResult.Errors, Value = renderResult.Value?.Select(v => OutputGraph.ToGraph(v)) };
              });

            #endregion

            #region cases

            Field<CasesGraphType>()
                .Name("cases")
                .AuthorizeWith("any")
                .Description("cases")
                .Argument<CaseWhereInputGraphType>("where", "a case search filter")
                .Argument<DateTimeOffsetGraphType>("asOf", "a time travel filter")
                .GetPaginationArguments()
                .Resolve(context =>
                {
                    CaseWhereGraph where = context.GetArgument<CaseWhereGraph>("where");
                    DateTime? asOf = context.GetArgument<DateTime?>("asOf");
                    int? skip = context.GetArgument<int?>("skip");
                    int? first = context.GetArgument<int?>("limit");
                    SortGraph sort = context.GetArgument<SortGraph>("sort");

                    return new CasesGraph { Where = CaseWhereGraph.ToDomain(where), AsOf = asOf, Skip = skip, First = first, Sort = sort };
                });

            Field<CasesReportGraphType>()
                .Name("casesReport")
                .AuthorizeWith("any")
                .Description("casesReport")
                .Argument<CaseWhereInputGraphType>("where", "a case search filter")
                .Argument<DateTimeOffsetGraphType>("asOf", "a time travel filter")
                .GetPaginationArguments()
                .Resolve(context =>
                {
                    CaseWhereGraph where = context.GetArgument<CaseWhereGraph>("where");
                    DateTime? asOf = context.GetArgument<DateTime?>("asOf");
                    int? skip = context.GetArgument<int?>("skip");
                    int? first = context.GetArgument<int?>("limit");
                    SortGraph sort = context.GetArgument<SortGraph>("sort");

                    return new CasesReportGraph { Where = CaseWhereGraph.ToDomain(where), AsOf = asOf, Skip = skip, First = first, Sort = sort };
                });

            Field<DataSchemasGraphType>()
                .Name("dataSchemas")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<DataSchemaWhereInputGraphType>("where", "A dataSchema search filter")
                .Description("Get dataSchemas")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "readDataSchemas");

                    string tenantId = context.GetTenantIdFromToken();
                    var allowedIds = (await permissionValidator.GetTargetIdsFromClaim(context, "readDataSchemas")).ToList();

                    DataSchemaWhere where = context.GetArgument<DataSchemaWhere>("where") ?? new DataSchemaWhere();

                    if (!allowedIds.Contains("all"))
                    {
                        where = new DataSchemaWhere
                        {
                            And = new List<DataSchemaWhere>
                            {
                                where,
                                new() { Id_in = allowedIds }
                            }
                        };
                    }

                    IEnumerable<DataSchema> dataSchemas = await caseService.GetDataSchemasAsync(tenantId, where);
                    IReadOnlyCollection<DataSchemaGraph> dataSchemaGraphs = dataSchemas.Select(s => new DataSchemaGraph
                    {
                        Id = s.Id,
                        Name = s.Name,
                        Description = s.Description,
                        Schema = s.Schema?.ToString(),
                        Standard = s.Standard,
                        Type = s.Type,
                        Tags = s.Tags,
                        UiSchemas = s.UiSchemaIds?.Select(i => new UiSchemaGraph() { Id = i }).ToList()
                    }).ToArray();

                    return new DataSchemasGraph
                    {
                        TotalCount = dataSchemaGraphs.Count,
                        List = dataSchemaGraphs.Sort(context)
                    };
                });

            #endregion

            #region advisor

            Field<AdvicesGraphType>()
                .Name("advices")
                .AuthorizeWith("any")
                .Description("Gets a list of advices")
                .GetPaginationArguments()
                .Argument<NonNullGraphType<StringGraphType>>("advisorId", "The advisor id")
                .Argument<NonNullGraphType<ListGraphType<ProductIdInputGraphType>>>("productIds", "")
                .Argument<ListGraphType<KeyValueInputGraphType>>("values", "A list of dynamic values as input of the advisor")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();

                    return new AdvicesGraph();
                });

            #endregion

            #region education
            Field<CoursesGraphType>()
               .Name("courses")
               .AuthorizeWith("any")
               .Description("courses")
               .Argument<CourseWhereInputGraphType>("where", "a course search filter")
               .GetPaginationArguments()
               .Resolve(context =>
               {
                   context.PassArgumentsToChildren();

                   return new CoursesGraph();
               });
            #endregion

            #region achievements
            Field<AchievementTypesGraphType>()
               .Name("achievementTypes")
               .AuthorizeWith("any")
               .Description("Achievement configurations")
               .Argument<AchievementTypeWhereInputGraphType>("where", "an achievementType search filter")
               .GetPaginationArguments()
               .Resolve(context =>
               {
                   context.PassArgumentsToChildren();

                   return new AchievementTypesGraph();
               });
            #endregion

            #region scripts
            Field<ScriptsGraphType>()
                .Name("scripts")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<ScriptWhereInputGraphType>("where", "A script search filter")
                .Description("Get scripts")
                .ResolveAsync(async context =>
                {
                    ScriptWhere where;
                    try
                    {
                        await permissionValidator.Authorize(context, "readScripts");
                        var allowedIds = (await permissionValidator.GetTargetIdsFromClaim(context, "readScripts")).ToList();

                        where = context.GetArgument<ScriptWhere>("where") ?? new ScriptWhere();

                        if (!allowedIds.Contains("all"))
                        {
                            where = new ScriptWhere
                            {
                                And = new List<ScriptWhere>
                               {
                                   where,
                                   new() { Id_in = allowedIds }
                               }
                            };
                        }
                    }
                    catch
                    {
                        // Keep the authorization to avoid breaking after deployment
                        // It will be removed after all permissions are added correctly in a separated ticket
                        await permissionValidator.Authorize(context, new PermissionRequest("updateProducts", "writeProducts").WithTargetIds("all"));
                        where = context.GetArgument<ScriptWhere>("where") ?? new ScriptWhere();
                    }
                    string tenantId = context.GetTenantIdFromToken();

                    IEnumerable<Script> scripts = await productService.GetScriptsAsync(tenantId, where);
                    IEnumerable<ScriptGraph> scriptGraphs = scripts?.Select(s => new ScriptGraph
                    {
                        Id = s.Id,
                        Name = s.Name,
                        InputSchema = s.InputSchema,
                        OutputSchema = s.OutputSchema,
                        SourceCode = s.SourceCode,
                        ReferenceSourceCodeUrl = s.ReferenceSourceCodeUrl,
                        ReferenceSourceCode = s.ReferenceSourceCode,
                        ExternalTableDataUrl = s.ExternalTableDataUrl,
                        ExternalTableDataUrls = s.ExternalTableDataUrls,
                        Type = s.Type,
                    });

                    return new ScriptsGraph
                    {
                        TotalCount = scriptGraphs.Count(),
                        List = scriptGraphs.Sort(context)
                    };
                });
            #endregion

            #region benefitdefinitions

            Field<BenefitDefinitionsGraphType>()
                .Name("benefitDefinitions")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<BenefitDefinitionWhereGraphType>("where", "A benefitDefinition search filter")
                .Description("Get benefitDefinitions")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "readBenefitDefinitions", "all");
                    string tenantId = context.GetTenantIdFromToken();
                    BenefitDefinitionWhere where = context.GetArgument<BenefitDefinitionWhere>("where") ??
                                                   new BenefitDefinitionWhere();

                    GraphQLToolsExtensions.GetQueryArguments(context, out int? skip, out int? first,
                        out OrderBy orderBy);

                    var queryArgument = new Domain.QueryArguments
                    {
                        Where = where,
                        Skip = skip,
                        OrderBy = orderBy,
                        First = first
                    };

                    Task<long> benefitDefinitionsCountTask =
                        productService.CountBenefitDefinitionsAsync(tenantId, queryArgument);
                    Task<IEnumerable<BenefitDefinition>> benefitDefinitionsGetTask =
                        productService.GetBenefitDefinitionsAsync(tenantId, queryArgument);

                    await Task.WhenAll(benefitDefinitionsCountTask, benefitDefinitionsGetTask);

                    IReadOnlyCollection<BenefitDefinition> benefitDefinitions =
                        benefitDefinitionsGetTask.Result.ToArray();
                    IEnumerable<BenefitDefinitionGraph> benefitDefinitionGraphs = benefitDefinitions.Select(s =>
                        new BenefitDefinitionGraph
                        {
                            Id = s.Id,
                            Name = s.Name,
                            Description = s.Description,
                            BusinessId = s.BusinessId,
                            Status = s.Status,
                            Fields = s.Fields?.ToString(Formatting.None),
                            BenefitDefinitionTypes = s.BenefitDefinitionTypeIds
                                ?.Select(b => new BenefitDefinitionTypeGraph { Id = b }).ToArray()
                        });

                    return new BenefitDefinitions
                    {
                        TotalCount = benefitDefinitionsCountTask.Result,
                        List = benefitDefinitionGraphs
                    };
                });

            Field<BenefitDefinitionTypesGraphType>()
                .Name("benefitDefinitionTypes")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<BenefitDefinitionTypeWhereGraphType>("where", "A benefitDefinitionType search filter")
                .Description("Get benefitDefinitionTypes")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "readBenefitDefinitionTypes", "all");
                    string tenantId = context.GetTenantIdFromToken();
                    BenefitDefinitionTypeWhere where = context.GetArgument<BenefitDefinitionTypeWhere>("where") ?? new BenefitDefinitionTypeWhere();

                    GraphQLToolsExtensions.GetQueryArguments(context, out int? skip, out int? first, out OrderBy orderBy);

                    Domain.QueryArguments queryArgument = new() { Where = where, Skip = skip, OrderBy = orderBy, First = first };

                    IReadOnlyCollection<BenefitDefinitionType> benefitDefinitionTypes = (await productService.GetBenefitDefinitionTypesAsync(tenantId, queryArgument)).ToArray();
                    var convertedBenefitDefinitionTypes = benefitDefinitionTypes.Select(type =>
                        new BenefitDefinitionType
                        {
                            Id = type.Id,
                            Name = type.Name,
                            Description = type.Description,
                            BusinessId = type.BusinessId,
                            Status = type.Status,
                            Fields = type.Fields?.ToString(Formatting.None) // Assuming Fields is a JSON-serializable type
                            // Copy other properties if necessary
                        }).ToArray();
                    return new BenefitDefinitionTypes
                    {
                        TotalCount = benefitDefinitionTypes.Count,
                        List = convertedBenefitDefinitionTypes
                    };
                });
            #endregion

            #region custom API integration code
            Field<NonNullGraphType<StringResultGraphType>>()
                .Name("integrate")
                .Description("Used for custom integrations with third parties.")
                .Argument<NonNullGraphType<StringGraphType>>("commandType", "the command type to execute")
                .Argument<StringGraphType>("inputJson", "the input for the integration")
                .ResolveAsync(async context =>
                {

                    string inputJson = context.GetArgument<string>("inputJson");
                    Result<JObject> parseInputResult = Tools.TryParseJsonStringToJObject(inputJson);
                    string tenantId = parseInputResult?.Value?.Property("tenantId")?.Value?.ToString() ?? context.GetTenantIdFromToken();
                    string bucketName = context.GetArgument<string>("bucketName");
                    string commandType = context.GetArgument<string>("commandType");
                    string loginIdFromToken = !IsTcbTenantWithItcbLifeCommand() ? context.GetLoginIdFromToken() : null;

                    if (parseInputResult?.Status != "success") Result<string>.Failure(parseInputResult?.Errors ?? Enumerable.Empty<string>());

                    parseInputResult?.Value?.Add(new JProperty("loginIdFromToken", loginIdFromToken));

                    var userContext = (GraphQLUserContext)context.UserContext;

                    return tenantId switch
                    {
                        "tcb_uat" => await tcbService.ExecuteAsync(tenantId, commandType, parseInputResult?.Value, bucketName, !IsTcbTenantWithItcbLifeCommand() ? Tools.GetAccessToken(userContext) : null),
                        "tcb" => await tcbService.ExecuteAsync(tenantId, commandType, parseInputResult?.Value, bucketName, !IsTcbTenantWithItcbLifeCommand() ? Tools.GetAccessToken(userContext) : null),
                        "fubon_uat" => await fubonService.ExecuteAsync(tenantId, commandType, parseInputResult?.Value, (context.UserContext as GraphQLUserContext)?.User.Claims),
                        "fubon" => await fubonService.ExecuteAsync(tenantId, commandType, parseInputResult?.Value, (context.UserContext as GraphQLUserContext)?.User.Claims),
                        "boc_uat" => await bocService.ExecuteAsync(tenantId, commandType, parseInputResult?.Value),
                        "boc" => await bocService.ExecuteAsync(tenantId, commandType, parseInputResult?.Value),
                        "insuredNomads_uat" => await insuredNomadsService.ExecuteAsync(tenantId, commandType, parseInputResult?.Value),
                        "insuredNomads" => await insuredNomadsService.ExecuteAsync(tenantId, commandType, parseInputResult?.Value),
                        "tahoe_uat" => await tahoeService.ExecuteAsync(tenantId, commandType, parseInputResult?.Value),
                        "tahoe" => await tahoeService.ExecuteAsync(tenantId, commandType, parseInputResult?.Value),
                        "dlvn_dev" => await dlvnService.ExecuteAsync(tenantId, commandType, parseInputResult?.Value),
                        "dlvn_uat" => await dlvnService.ExecuteAsync(tenantId, commandType, parseInputResult?.Value),
                        "dlvn" => await dlvnService.ExecuteAsync(tenantId, commandType, parseInputResult?.Value),
                        "wfp_dev" => await wfpService.ExecuteAsync(tenantId, commandType, parseInputResult?.Value),
                        "wfp" => await wfpService.ExecuteAsync(tenantId, commandType, parseInputResult?.Value),
                        _ => Result<string>.Failure($"No integration specified for {tenantId}"),
                    };

                    bool IsTcbTenantWithItcbLifeCommand()
                    {
                        return !String.IsNullOrEmpty(tenantId) && tenantId.Contains("tcb") &&
                        (commandType == "itcbLifeToken" || commandType == "tcbDeploymentToken");
                    }
                });
            #endregion

            Field<PolicyMembersMovementsReportGraph>()
                .Name("policyMembersMovementsReport")
                .Description("Calculates policy members movements report")
                .Argument<PolicyMembersWhereInputGraph>("where", "query arguments")
                .AuthorizeWith("any")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();
                    return new PolicyMembersMovementsReport();
                });

            Field<NonNullGraphType<UserStorageItemsGraphType>>()
                .Name("userStorage")
                .Description("Current user items storage")
                .Resolve(_ => new UserStorageItems());

            Field<RemarksGraphType>()
                .Name("claimRemarks")
                .AuthorizeWith("any")
                .Description("Gets claim remarks")
                .GetPaginationArguments()
                .Argument<RemarkWhereGraphType>("where", "A Remark search filter")
                .Resolve(context =>
                {
                    context.PassArgumentsToChildren();
                    return new Remarks();
                });


            #region scheduler
            Field<JobSchedulesGraphType>()
                .Name("jobSchedules")
                .AuthorizeWith("any")
                .GetPaginationArguments()
                .Argument<JobScheduleWhereInputGraphType>("where", "A job schedule search filter")
                .Description("Get job schedules")
                .ResolveAsync(async context =>
               {
                   await permissionValidator.Authorize(context, "readJobSchedules", "all");
                   string tenantId = context.GetTenantIdFromToken();
                   JobScheduleWhere where = context.GetArgument<JobScheduleWhere>("where") ?? new JobScheduleWhere();
                   int? skip = context.GetArgument<int?>("skip");
                   int? first = context.GetArgument<int?>("limit");
                   SortGraph sort = context.GetArgument<SortGraph>("sort");

                   OrderBy orderBy = null;
                   if (sort != null)
                   {
                       orderBy = sort.ToOrderBy();
                   }
                   QueryArguments<JobScheduleWhere> queryArguments = new()
                   {
                       First = first,
                       Skip = skip,
                       OrderBy = orderBy,
                       Where = where
                   };

                   IReadOnlyCollection<JobSchedule> schedules = await schedulerService.GetJobSchedules(tenantId, queryArguments);

                   return new JobSchedules
                   {
                       TotalCount = schedules.Count(),
                       List = schedules.Sort(context)
                   };
               });
            #endregion

            Field<ListGraphType<PublicHolidayGraphType>>()
                .Name("publicHolidays")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IntGraphType>>("year", "The year that we want to get public holidays")
                .Argument<NonNullGraphType<CountryCodeEnumGraphType>>("countryCode", "ISO 3166-1 alpha-2")
                .Description("Get public holidays of a particular year")
                .Resolve(context =>
                {
                    int year = context.GetArgument<int>("year");
                    CountryCode countryCode = context.GetArgument<CountryCode>("countryCode");

                    return PublicHolidaySystem.GetPublicHolidayProvider(countryCode).Get(year)
                        .Select(x => new PublicHolidayGraph(x));
                });

            Field<DateResultGraphType>()
                .Name("nextWorkingDate")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<DateGraphType>>("date", "The date that you want to get next working date")
                .Argument<NonNullGraphType<CountryCodeEnumGraphType>>("countryCode", "ISO 3166-1 alpha-2")
                .Description("Get next working date of a particular date")
                .Resolve(context =>
                {
                    DateTime date = context.GetArgument<DateTime>("date");
                    CountryCode countryCode = context.GetArgument<CountryCode>("countryCode");

                    return Result<DateTime>.Success(date.GetNextWorkingDate(countryCode));
                });

            Field<ApplicationVersionInfoListGraphType>()
                .Name("versions")
                .AuthorizeWith("any")
                .Description("Versions of the services")
                .Resolve(_ =>
                {
                    return new ApplicationVersionInfoListGraph();
                });
            //TODO: move to bupa separate service
            Field<StringGraphType>()
                .Name("hmacSha256")
                .Argument<StringGraphType>("message", "Message to hash")
                .Description("Temporary solution for hash")
                .Resolve(context =>
                {
                    string message = context.GetArgument<string>("message");
                    const string key = "6ca5231b676a48359247ccd931d47211ee29cba4202647469f2feb7400c5e2460721f523d81641ff9f4deed5ed1f8c5370fef6927b1b499a89789f8bf3ee78b466ee175a8a2b4cc59c50c205c540e9365433ee4bfb8e419e8df0ad678b6969ddfecbe114daaa498381a99478518ec9c20de8fa80d51b44c29b6125e51e23f143";

                    UTF8Encoding encoding = new();
                    byte[] keyByte = encoding.GetBytes(key);

                    HMACSHA256 hmacsha256 = new(keyByte);
                    byte[] messageBytes = encoding.GetBytes(message);
                    return Convert.ToBase64String(hmacsha256.ComputeHash(messageBytes));
                });

            Field<StringResultGraphType>()
                .Name("jobStatus")
                .AuthorizeWith("any")
                .Description("Get the status of a background job")
                .Argument<NonNullGraphType<StringGraphType>>("jobId", "The job identifier")
                .ResolveAsync(async context =>
                {
                    string jobId = context.GetArgument<string>("jobId");
                    return await templateService.GetJobStatusAsync(jobId);
                });
        }

        private static async Task<ProductWhere> CombineWithAllowedIds(
            ProductWhere where,
            IAuthService authService,
            PermissionValidator permissionValidator,
            ResolveFieldContext<object> context)
        {
            List<string> allowedIds = (await permissionValidator.GetTargetIdsFromClaim(context, "readProducts")).ToList();

            where = allowedIds.Contains("all")
                ? where
                : new ProductWhere
                {
                    And = new List<ProductWhere>
                    {
                        where,
                        new() { Id_in = allowedIds.Select(aId => ProductId.FromString(aId)).ToList() }
                    }
                };

            List<string> allowedProductTypes = await context.GetAllowedProductTypes(authService);

            if (allowedProductTypes?.Any() ?? false)
            {
                where = new ProductWhere
                {
                    And = new List<ProductWhere>
                    {
                        where,
                        new()
                        {
                            ProductId = new ProductIdWhere{ Type_in = allowedProductTypes }
                        }
                    }
                };
            }

            return where;
        }

        private async Task ReplaceNameContainsWithProductIdFilterAsync(string tenantId, ProductWhere where, IL10nService l10nService)
        {
            if (where.And != null)
                foreach (ProductWhere wh in where.And)
                    await ReplaceNameContainsWithProductIdFilterAsync(tenantId, wh, l10nService);

            if (where.Or != null)
                foreach (ProductWhere wh in where.Or)
                    await ReplaceNameContainsWithProductIdFilterAsync(tenantId, wh, l10nService);

            if (where.Name_contains != null)
            {
                IEnumerable<string> keys = await l10nService.GetKeysFromValueContainsAsync(tenantId, CultureInfo.CurrentCulture.Name, where.Name_contains, "products-", "-name");
                where.Name_contains = null;
                IEnumerable<ProductId> productIds = keys.Select(k => ProductId.FromString(k.Replace("products-", "").Replace("-name", ""))).Where(pid => pid != null);

                ProductWhere whereDeepCopy = JsonConvert.DeserializeObject<ProductWhere>(JsonConvert.SerializeObject(where));
                where.And = new List<ProductWhere> { whereDeepCopy, new() { Id_in = productIds?.ToList() } };
            }
        }

        private async Task<List<Policy>> FetchPolicies(string tenantId, IPolicyService policyService, PolicyWhere where)
        {
            const int BatchSize = 1000;
            List<Policy> policies = new();

            while (true)
            {
                List<Policy> batch = await policyService.GetAsync(
                    tenantId,
                    new Domain.QueryArguments
                    {
                        Where = where,
                        Skip = policies.Count,
                        First = BatchSize,
                    }
                );

                if (batch == null) break;

                policies.AddRange(batch);

                if (batch.Count < BatchSize) break;
            }

            return policies;
        }

        private async Task<Result<ObjectListing>> FetchFileListing(ResolveFieldContext<object> context, IFileSystemService fileSystemService, PermissionValidator permissionValidator, ILogger logger)
        {
            string tenantIdFromArgument = context.GetArgument<string>("tenantId");
            string tenantId = tenantIdFromArgument ?? context.GetTenantIdFromToken();

            string prefix = context.GetArgument<string>("prefix");
            string bucketName = context.GetArgument<string>("bucketName");
            string continuationToken = context.GetArgument<string>("continuationToken");

            int limit = context.GetArgument<int>("limit");
            int skip = context.GetArgument<int>("skip");
            string sort = context.GetArgument<string>("sort");
            string fileNameContains = context.GetArgument<string>("filename_contains");

            bool authorized = true;
            if (tenantIdFromArgument == null)
                authorized = await permissionValidator.AuthorizeWithTargetPrefix(context, "readFiles", prefix);

            if (!authorized)
            {
                logger.LogInformation(
                    "Unauthorized request to {query}. Message: {message}",
                    "fileListing", $"Missing 'readFiles:{prefix}' permission");
                return null;
            }

            Result<ObjectListing> result = await fileSystemService.ListAsync(tenantId, bucketName, new ListFilesCommand
            {
                Path = prefix,
                AllowedPrefixes = null,
                IsPublic = tenantIdFromArgument != null,
                ContinuationToken = continuationToken,
                Limit = limit,
                Skip = skip,
                SortBy = sort,
                FilterByFileName = fileNameContains
            });

            foreach (ObjectSummary summary in result.Value?.ObjectSummaries ?? new List<ObjectSummary> { })
                summary.BucketName = bucketName;

            return result;
        }
    }

    public class PermissioningObject
    {
        public string DelegatinLoginId { get; set; }
        public List<AddTargettedPermissionCommand> ToDelegate { get; set; }
        public List<AddTargettedPermissionCommand> ToRequest { get; set; }
    }
}