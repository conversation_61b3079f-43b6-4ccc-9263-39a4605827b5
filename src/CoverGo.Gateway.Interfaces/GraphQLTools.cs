using AngleSharp.Text;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Claims;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Users;
using CoverGo.JsonUtils;
using CoverGo.JsonUtils.Extensions;
using GraphQL;
using GraphQL.Builders;
using GraphQL.DataLoader;
using GraphQL.Language.AST;
using GraphQL.Types;
using GraphQL.Validation;
using Microsoft.AspNetCore.JsonPatch;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using CurrencyCode = CoverGo.Gateway.Domain.CurrencyCode;
using JSchema = Newtonsoft.Json.Schema.JSchema;
using JSchemaPreloadedResolver = Newtonsoft.Json.Schema.JSchemaPreloadedResolver;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Interfaces
{
    public static class GraphQLToolsExtensions
    {
        public static T ToUpdateCommand<T>(this IDictionary<string, object> updateCommandDictionary) where T : class
        {
            IDictionary<string, object> updateDictionary = updateCommandDictionary.PopulateIsChanged();
            return Tools.ToObjectOrDefault<T>(updateDictionary);
        }

        public static T ComputeArgAndVar<T, Y>(this ResolveFieldContext<Y> context, string name)
        {
            IValue reference = context.FieldAst.Arguments.FirstOrDefault(a => a.Name == name)?.Value;
            object computed = reference is VariableReference ? context.Variables.ValueFor(((VariableReference)reference).Name) : reference?.Value;
            return Tools.ToObjectOrDefault<T>(computed);
        }
        
        public static T ComputeArgAndVar<T>(this ResolveFieldContext context, string name)
        {
            IValue reference = context.FieldAst.Arguments.FirstOrDefault(a => a.Name == name)?.Value;
            object computed = reference is VariableReference ? context.Variables.ValueFor(((VariableReference)reference).Name) : reference?.Value;
            return Tools.ToObjectOrDefault<T>(computed);
        }

        public static T ComputeArgAndVar<T, Y>(this ResolveFieldContext<Y> context, string name, JsonSerializer jsonSerializer)
        {
            IValue reference = context.FieldAst.Arguments.FirstOrDefault(a => a.Name == name)?.Value;
            object computed = reference is VariableReference ? context.Variables.ValueFor(((VariableReference)reference).Name) : reference?.Value;
            return Tools.ToObjectOrDefault<T>(computed, jsonSerializer);
        }

        private static IDictionary<string, object> PopulateIsChanged(this IDictionary<string, object> updateCommandDictionary)
        {
            for (int i = updateCommandDictionary.Count - 1; i >= 0; i--)
            {
                KeyValuePair<string, object> keyValue = updateCommandDictionary.ElementAt(i);
                updateCommandDictionary.Add($"is{keyValue.Key}Changed", true);

                if (keyValue.Value is IDictionary<string, object>)
                {
                    updateCommandDictionary.Remove(keyValue.Key);
                    updateCommandDictionary.Add(keyValue.Key, PopulateIsChanged(keyValue.Value as IDictionary<string, object>));
                }

                if (keyValue.Value is IList<object> objects)
                {
                    for (var j = 0; j < objects.Count; j++)
                    {
                        if (objects[j] is IDictionary<string, object> objDict)
                        {
                            objects.RemoveAt(j);
                            objects.Insert(j, PopulateIsChanged(objDict));
                        }
                    }
                    updateCommandDictionary.Remove(keyValue.Key);
                    updateCommandDictionary.Add(keyValue.Key, objects);
                }
            }

            return updateCommandDictionary;
        }

        public static void PassArgumentsToChildren<T>(this ResolveFieldContext<T> context)
        {
            foreach (KeyValuePair<string, Field> subFieldKv in context.SubFields)
                foreach (Argument arg in context.FieldAst.Arguments)
                    subFieldKv.Value.Arguments.Add(arg);
        }

        public static void PassArgumentsToChildren<T>(this ResolveFieldContext<T> context, params string[] names)
        {
            foreach (KeyValuePair<string, Field> subFieldKv in context.SubFields)
                foreach (Argument arg in context.FieldAst.Arguments)
                {
                    if (!names.Contains(arg.Name) || subFieldKv.Value.Arguments.Any(a => a.Name == arg.Name)) continue;
                    subFieldKv.Value.Arguments.Add(arg);
                }
        }

        public static string GetTenantIdFromToken<T>(this ResolveFieldContext<T> context, bool isRequired = true)
        {
            string tenantId = (context.UserContext as GraphQLUserContext)?.User.Claims.FirstOrDefault(c => c.Type == "tenantId")?.Value;
            if (isRequired && tenantId == null)
                throw new ExecutionError("No tenantId detected from that user.");

            return tenantId;
        }

        public static string GetRoleFromToken<T>(this ResolveFieldContext<T> context, bool isRequired = true)
        {
            string role = (context.UserContext as GraphQLUserContext)?.User.Claims.FirstOrDefault(c => c.Type == "role")?.Value;
            if (isRequired && role == null)
                throw new ExecutionError("No role detected from that user.");

            return role;
        }

        public static EntityTypes GetEntityTypeFromToken<T>(this ResolveFieldContext<T> context) =>
            ToDomain((context.UserContext as GraphQLUserContext)?.User.Claims.FirstOrDefault(p => p.Type == "entityType")?.Value);

        private static EntityTypes ToDomain(string userType)
        {
            switch (userType)
            {
                case "individual": return EntityTypes.Individual;
                case "internal": return EntityTypes.Internal;
                case "company": return EntityTypes.Company;
                default: return EntityTypes.Undefined;
            }
        }

        public static string GetEntityIdFromToken<T>(this ResolveFieldContext<T> context, bool isRequired = true)
        {
            string userId = (context.UserContext as GraphQLUserContext)?.User.Claims.FirstOrDefault(c => c.Type == "entityId")?.Value;
            if (isRequired && userId == null)
                throw new ExecutionError("No entityId detected from that user.");

            return userId;
        }

        public static string GetLoginIdFromToken<T>(this ResolveFieldContext<T> context, bool isRequired = true)
        {
            string subType = "sub";
            if (LoginConfig.IsAzureLogin)
                subType = "loginId";

            string loginId = (context.UserContext as GraphQLUserContext)?.User.Claims.FirstOrDefault(c => c.Type == subType)?.Value;

            if(LoginConfig.IsAzureLogin && loginId == null)
                loginId = (context.UserContext as GraphQLUserContext)?.User.Claims.FirstOrDefault(c => c.Type == "sub")?.Value;

            if (isRequired && loginId == null)
                throw new ExecutionError("No login detected.");

            return loginId;
        }

        public static string GetUserNameFromToken<T>(this ResolveFieldContext<T> context, bool isRequired = true)
        {
            string userName = (context.UserContext as GraphQLUserContext)?.User.Claims.FirstOrDefault(c => c.Type == "userId")?.Value;
            if (isRequired && userName == null)
                throw new ExecutionError("No user name detected.");

            return userName;
        }

        public static async Task<IEnumerable<TargetedPermissionSchemaGraph>> GetTargetedPermissionSchemas<T>(this ResolveFieldContext<T> context, IAuthService authService, IDataLoaderContextAccessor accessor)
        {
            string loginId = (context.UserContext as GraphQLUserContext)?.User.Claims.FirstOrDefault(c => c.Type == "sub")?.Value;
            if (loginId == null)
                throw new ExecutionError("No login detected.");

            string tenantId = context.GetTenantIdFromToken();

            var dataLoader = accessor.Context.GetOrAddBatchLoader<string, IEnumerable<TargetedPermissionSchemaGraph>>(
                "GetPermissionSchemasForLoginId",
                async loginIds =>
                {
                    IEnumerable<Login> logins = await authService.GetLoginsAsync(tenantId, new LoginWhere { Ids = loginIds , ExcludePermissions = true });
                    IReadOnlyCollection<string> targetedPermissionSchemaIds = logins
                        .Where(l => l.TargetedPermissionSchemaIds != null)
                        .SelectMany(l => l.TargetedPermissionSchemaIds.Where(t => t != null))
                        .Where(i => i != null)
                        .ToList();

                    IReadOnlyCollection<TargetedPermissionSchema> targetedPermissionSchemas =
                        await authService.GetTargetedPermissionSchemas(tenantId, targetedPermissionSchemaIds);

                    IEnumerable<PermissionSchema> permissionSchemas = await authService
                        .GetPermissionSchemas(tenantId, new QueryArguments { Where = new PermissionSchemaWhere { Id_in = targetedPermissionSchemas.Select(t => t.PermissionSchemaId) } });

                    IReadOnlyCollection<TargetedPermissionSchemaGraph> targetedPermissionSchemaGraphs
                        = targetedPermissionSchemas.Join(permissionSchemas, t => t.PermissionSchemaId, p => p.Id, (t, p) => new TargetedPermissionSchemaGraph
                        {
                            Id = t.Id,
                            TargetIds = t.TargetIds,
                            PermissionSchema = p
                        }).ToArray();

                    return logins
                        .ToDictionary(
                            i => i.Id,
                            i => i.TargetedPermissionSchemaIds?.Join(
                                targetedPermissionSchemaGraphs,
                                ltps => ltps,
                                tps => tps.Id,
                                (ltps, tps) => tps));
                });

            return await dataLoader.LoadAsync(loginId);
        }

        public static async Task<List<string>> GetAllowedProductTypes<T>(this ResolveFieldContext<T> context, IAuthService authService)
        {
            string loginId = context.GetLoginIdFromToken();
            string tenantId = context.GetTenantIdFromToken();

            if (string.IsNullOrEmpty(loginId) || string.IsNullOrEmpty(tenantId)) throw new ValidationError("", "authorization", $"{tenantId}: Authorization failed");

            Login login = await authService.GetLoginById(tenantId, loginId);

            if (login == null)
                throw new ExecutionError("Login not found.");

            if (login.TargettedPermissions.TryGetValue("role", out IEnumerable<string> roles)
                && roles.Any(r => r == "admin"))
                return null; // admin has access to all the product types anyway

            return login.PermissionGroups?.SelectMany(x => x.ProductTypes ?? new string[0])?.Where(x => !string.IsNullOrEmpty(x))?.Distinct()?.ToList();
        }

        public static async Task<string> GetPermittedProjection<T>(this ResolveFieldContext<T> context, IAuthService authService, IDataLoaderContextAccessor accessor, string json, string objectId, string objectType)
        {
            if (json == null)
                return null;

            IEnumerable<TargetedPermissionSchemaGraph> targetedPermissionSchemas = await context.GetTargetedPermissionSchemas(authService, accessor);

            TargetedPermissionSchemaGraph schema = targetedPermissionSchemas?.FirstOrDefault(t =>
                t.PermissionSchema.ActionType == PermissionSchemaActionType.Read
                && t.PermissionSchema.ObjectType.Equals(objectType, StringComparison.InvariantCultureIgnoreCase)
                && t.TargetIds.Contains(objectId));

            if (schema == null) // Temporary. Once FE is ready to work with permission schemas, this should be removed for security
                return json;


            JsonProjector jsonProjector = new();

            string schemaJson = schema.PermissionSchema.Schema.ToString();
            if (schema.PermissionSchema.StateCondition == null)
                return jsonProjector.Read(json, schemaJson);

            try
            {
                var jToken = JToken.Parse(json);
                JToken value = jToken.SelectToken(schema.PermissionSchema.StateCondition.Path);
                bool conditionMet = ApplyCondition(value, schema.PermissionSchema.StateCondition);
                return conditionMet ? jsonProjector.Read(jToken, schemaJson).ToString() : null;
            }
            catch
            {
                return null;
            }
        }

        public static async Task PatchOperationPermittedCheckAsync<T>(this ResolveFieldContext<T> context,
            IAuthService authService, string tenantId, string jsonPatch, string objectId, string objectType,
            Func<Task<JToken>> currentFieldsGetter)
        {
            void ThrowAuthException() =>
                throw new ValidationError("", "authorization",
                    $"{tenantId}: You are not authorized to update these fields" + jsonPatch);

            if (string.IsNullOrEmpty(jsonPatch)) return;

            (bool permitted, PermissionSchema schema) =
                await context.IsJsonPatchPermittedAndGetSchema(authService, jsonPatch, objectId, objectType);

            if (!permitted)
                ThrowAuthException();

            if (schema == null || (schema.StateCondition == null && schema.UpdateCondition == null)) return;

            JToken fields = await currentFieldsGetter();
            if (fields == null) return;

            if (schema.StateCondition != null)
            {
                JToken value = fields.SelectToken(schema.StateCondition.Path ?? "");
                if (!ApplyCondition(value, schema.StateCondition))
                    ThrowAuthException();
            }

            if (schema.UpdateCondition != null)
            {
                fields =
                    new JsonProjector().Write(fields, jsonPatch);
                JToken value = fields.SelectToken(schema.UpdateCondition.Path ?? "");
                if (!ApplyCondition(value, schema.UpdateCondition))
                    ThrowAuthException();
            }
        }

        public static bool ApplyCondition(JToken actualValue, FieldsWhere @where)
        {

            bool CheckCondition(ScalarValue valueToCompareWith, FieldsWhereCondition condition)
            {
                object valueToCompareWith2 = valueToCompareWith.GetValue();
                object actualValue2 = actualValue.ToScalarValue().GetValue();
                return condition switch
                {
                    FieldsWhereCondition.Equals =>
                        valueToCompareWith2.Equals(actualValue2),
                    FieldsWhereCondition.StringContains =>
                        valueToCompareWith2 is string substring
                        && actualValue2 is string actualString
                        && actualString.Contains(substring, StringComparison.InvariantCultureIgnoreCase),
                    FieldsWhereCondition.ArrayContains =>
                        valueToCompareWith2 is string stringToCompareWith
                        && actualValue2 is IList<object> actualArray
                        && actualArray.Any(i => i is string str && str == stringToCompareWith),
                    FieldsWhereCondition.In =>
                        valueToCompareWith2 is IList<object> arrayToCompareWith
                        && actualValue2 is string actualString
                        && arrayToCompareWith.Any(i => i is string str && str == actualString),
                    FieldsWhereCondition.LessThan when valueToCompareWith2 is decimal decimalToCompareWith &&
                                                       actualValue2 is decimal actualDouble =>
                        actualDouble < decimalToCompareWith,
                    FieldsWhereCondition.GreaterThan when valueToCompareWith2 is decimal decimalToCompareWith &&
                                                          actualValue2 is decimal actualDouble =>
                        actualDouble > decimalToCompareWith,
                    FieldsWhereCondition.LessThan when valueToCompareWith2 is DateTime timeToCompareWith &&
                                                       actualValue2 is DateTime actualTime =>
                        actualTime < timeToCompareWith,
                    FieldsWhereCondition.GreaterThan when valueToCompareWith2 is DateTime timeToCompareWith &&
                                                          actualValue2 is DateTime actualTime =>
                        actualTime > timeToCompareWith,
                    _ => false
                };
            }

            if (@where.Or?.Any() ?? false)
            {
                bool result = false;
                foreach (FieldsWhere orWhere in @where.Or)
                {
                    JToken orValue = actualValue.SelectToken(orWhere.Path);
                    result = result || ApplyCondition(orValue, orWhere);
                }

                return result;
            }

            if (@where.And?.Any() ?? false)
            {
                bool result = true;
                foreach (var andWhere in @where.And)
                {
                    JToken andValue = actualValue.SelectToken(andWhere.Path);
                    result = result && ApplyCondition(andValue, andWhere);
                }

                return result;
            }

            return CheckCondition(where.Value, where.Condition);
        }

        public static async Task<(bool, PermissionSchema)> IsJsonPatchPermittedAndGetSchema<T>(this ResolveFieldContext<T> context, IAuthService authService, string jsonPatch, string objectId, string objectType)
        {
            if (jsonPatch == null)
                return (true, null);

            string loginId = (context.UserContext as GraphQLUserContext)?.User.Claims.FirstOrDefault(c => c.Type == "sub")?.Value;
            if (loginId == null)
                throw new ExecutionError("No login detected.");

            string tenantId = context.GetTenantIdFromToken();

            Login login = await authService.GetLoginById(tenantId, loginId);

            if (login == null)
                throw new ExecutionError("Login not found.");

            IReadOnlyCollection<string> targetedPermissionSchemaIds = login.TargetedPermissionSchemaIds?.ToList();

            if (targetedPermissionSchemaIds == null)
                return (true, null); // Temporary. Once FE is ready to work with permission schemas, this should be removed for security

            IReadOnlyCollection<TargetedPermissionSchema> targetedPermissionSchemas = await authService.GetTargetedPermissionSchemas(tenantId, targetedPermissionSchemaIds);

            IEnumerable<PermissionSchema> permissionSchemas = await authService
                .GetPermissionSchemas(tenantId, new QueryArguments { Where = new PermissionSchemaWhere { Id_in = targetedPermissionSchemas.Select(t => t.PermissionSchemaId) } });

            PermissionSchema schema = permissionSchemas?.FirstOrDefault(p =>
                p.ActionType == PermissionSchemaActionType.Write
                && p.ObjectType.Equals(objectType, StringComparison.InvariantCultureIgnoreCase)
                && targetedPermissionSchemas.Any(t => t.PermissionSchemaId == p.Id && t.TargetIds.Contains(objectId)));

            if (schema == null)
                return (true, null); // Temporary. Once FE is ready to work with permission schemas, this should be removed for security

            var jsonSchema = JSchema.Parse(schema.Schema?.ToString(), new JSchemaPreloadedResolver());
            JsonPatchDocument jsonPatchDocument = JsonConvert.DeserializeObject<JsonPatchDocument>(jsonPatch);

            return (jsonPatchDocument.IsValid(jsonSchema), schema);
        }

        public static async Task<bool> IsJsonPatchPermitted<T>(this ResolveFieldContext<T> context, IAuthService authService, string jsonPatch, string objectId, string objectType) =>
            (await IsJsonPatchPermittedAndGetSchema(context, authService, jsonPatch, objectId, objectType)).Item1;

        public static string GetClientIdFromToken<T>(this ResolveFieldContext<T> context, bool isRequired = true)
        {
            const string appIdClaim = "appId";
            const string clientIdClaim = "client_id";

            string clientId = (context.UserContext as GraphQLUserContext)?.User.Claims.FirstOrDefault(c => c.Type == appIdClaim)?.Value ?? (context.UserContext as GraphQLUserContext)?.User.Claims.FirstOrDefault(c => c.Type == clientIdClaim)?.Value;

            if (LoginConfig.IsAzureLogin)
                clientId = "covergo_crm";
           
            if (isRequired && clientId == null)
                throw new ExecutionError("No client_id detected from that user.");

            return clientId;
        }
        
        public static bool IsAllowed(IEnumerable<string> allowedIds, string id) =>
            allowedIds.Contains(id) || allowedIds.Contains("all");

        public static string FormatPluralEntityType(EntityTypes entityType) =>
            entityType switch
            {
                EntityTypes.Individual => "Individuals",
                EntityTypes.Company => "Companies",
                EntityTypes.Internal => "Internals",
                EntityTypes.Object => "Objects",
                EntityTypes.Organization => "Organizations",
                _ => null
            };

        public static IEnumerable<QueryArgument> GetGenericQueryArguments => new List<QueryArgument>
        {
            new QueryArgument<IntGraphType> { Name = "limit", Description = "Limit of items to query" },
            new QueryArgument<IntGraphType> { Name = "skip", Description = "Skip an amount of items" },
            new QueryArgument<SortGraphType> { Name = "sort", Description = "sort items" },
        };

        private static FieldBuilder<T, U> GetLimitSkipArguments<T, U>(this FieldBuilder<T, U> fieldBuilder, int? defaultLimit = null)
        {
            if (defaultLimit == null)
                fieldBuilder.Argument<IntGraphType>("limit", "Limit of items to query");
            else
                fieldBuilder.Argument<IntGraphType, int>("limit", "Limit of items to query", defaultLimit.Value);

            fieldBuilder.Argument<IntGraphType>("skip", "Skip an amount of items");
            return fieldBuilder;
        }

        public static FieldBuilder<T, U> GetPaginationArguments<T, U>(this FieldBuilder<T, U> fieldBuilder, int? defaultPageLimit = null)
        {
            fieldBuilder.GetLimitSkipArguments(defaultPageLimit);
            fieldBuilder.Argument<SortGraphType>("sort", "sort items");
            return fieldBuilder;
        }
        
        public static FieldBuilder<T, U> RequirePageLimit<T, U>(this FieldBuilder<T, U> fieldBuilder)
        {
            fieldBuilder.Configure(c => c.Metadata["requirePageLimit"] = "true");
            return fieldBuilder;
        }

        public static FieldBuilder<T, U> GetPaginationArguments2<T, U>(this FieldBuilder<T, U> fieldBuilder)
        {
            fieldBuilder.GetPaginationArguments();
            fieldBuilder.Argument<ListGraphType<SortGraphType>>("sort2", "sort items");
            return fieldBuilder;
        }

        public static async Task<string> GetL10nAsync<T>(this ResolveFieldContext<T> context, IDataLoaderContextAccessor accessor, IL10nService l10nService, params string[] orderedL10nIds)
        {
            IEnumerable<string> newOrderedL10nsIds = orderedL10nIds.Where(s => s != null);
            if (!newOrderedL10nsIds.Any())
                return null;

            string tenantId = context.GetTenantIdFromToken();
            var l10nsLoader = accessor.Context.GetOrAddBatchLoader<string, string>("GetL10ns",
                i => l10nService.GetL10nsAsync(tenantId, CultureInfo.CurrentCulture.Name, i));

            IEnumerable<(string l10nId, Task<string> task)> tasks = newOrderedL10nsIds.Where(s => s != null).Select(s => (s, l10nsLoader.LoadAsync(s)));
            await Task.WhenAll(tasks.Select(t => t.task));

            (string l10nId, Task<string> task) result = tasks.FirstOrDefault(t => t.task.Result != null);
            return result != default
                ? result.task.Result
                : null;
        }

        public static void GetQueryArguments<T>(ResolveFieldContext<T> context, out int? skip, out int? first, out OrderBy orderBy)
        {
            skip = context.GetArgument<int?>("skip");
            first = context.GetArgument<int?>("first") ?? context.GetArgument<int?>("limit");
            SortGraph sort = context.GetArgument<SortGraph>("sort");
            orderBy = null;
            if (sort != null)
            {
                orderBy = sort.ToOrderBy();
            }
        }

        public static async Task<string> GetFormattedPriceAsync<T>(
            this ResolveFieldContext<T> context,
            IDataLoaderContextAccessor accessor,
            IL10nService l10nService,
            decimal? amount,
            CurrencyCode? currencyCode)
        {
            if (!amount.HasValue)
                return null;

            if (currencyCode == CurrencyCode.Undefined)
                return amount?.ToString("n2");

            string tenantId = context.GetTenantIdFromToken();
            var l10nsLoader = accessor.Context.GetOrAddBatchLoader<string, string>("GetL10ns",
                i => l10nService.GetL10nsAsync(tenantId, CultureInfo.CurrentCulture.Name, i));

            string currencyName = await l10nsLoader.LoadAsync($"currency-{currencyCode}-name");

            return currencyName + amount?.ToString("n2");
        }

        public static void RecursiveSubstract(List<Benefit> summarizedBenefits, BenefitClaim benefitClaim, string typeId)
        {
            Benefit concernedsummarizedBenefit = summarizedBenefits.FirstOrDefault(sb => sb.TypeId == typeId);
            if (concernedsummarizedBenefit != null)
            {
                JToken builtJToken = null;

                if (benefitClaim.Values?.Any() ?? false)
                    builtJToken = RecursiveBuildJToken(concernedsummarizedBenefit.Value, benefitClaim.Values);

                concernedsummarizedBenefit.Value = Subtract(concernedsummarizedBenefit.Value, builtJToken); //: Build the benefit claim Jtoken Value from another complex algorithm

                if (concernedsummarizedBenefit.ParentTypeId != null)
                    RecursiveSubstract(summarizedBenefits, benefitClaim, concernedsummarizedBenefit.ParentTypeId);
            }
        }

        private static JToken Subtract(JToken first, JToken second)
        {
            if (first == null || second == null)
                return first;

            JToken clone = first.DeepClone();
            switch (clone.Type)
            {
                case JTokenType.Null: break;
                case JTokenType.String:
                    clone = second.Type == JTokenType.String
                        ? new JValue($"{first.Value<string>()} - {second.Value<string>()}")
                        : clone;
                    break;
                case JTokenType.Integer:
                case JTokenType.Float:
                    clone = (second.Type == JTokenType.Integer || second.Type == JTokenType.Float)
                        ? new JValue(first.Value<decimal>() - second.Value<decimal>())
                        : clone;
                    break;

                case JTokenType.Object:
                    {
                        if (second.Type != JTokenType.Object)
                            return clone;

                        IEnumerable<JProperty> newJproperties = first.Children<JProperty>().Select(prop =>
                        {
                            JProperty secondPropMatch = second.Children<JProperty>().FirstOrDefault(sp => sp.Name == prop.Name);
                            if (secondPropMatch == null)
                                return prop;

                            JToken newValue = Subtract(prop.Value, secondPropMatch.Value);
                            return new JProperty(prop.Name, newValue);
                        });

                        return new JObject(newJproperties);
                    }

                default: break;
            }

            return clone;
        }

        public static void ThrowIfNotSuccess(this Result result)
        {
            if (result.Status == "success") return;

            if (result.Errors?.Any() ?? false)
            {
                throw new AggregateException(result.Errors.Select(e => new Exception(e)));
            }

            if (result.Errors_2?.Any() ?? false)
            {
                throw new AggregateException(result.Errors_2.Select(e => new Exception($"Code: {e.Code}, Message: {e.Message}")));
            }

            throw new Exception("An unknown error occured on the Gateway level");
        }

        public static void ThrowIfNotSuccess<T>(this Result<T> result)
        {
            if (result.Status == "success") return;

            if (result.Errors?.Any() ?? false)
            {
                throw new AggregateException(result.Errors.Select(e => new Exception(e)));
            }

            if (result.Errors_2?.Any() ?? false)
            {
                throw new AggregateException(result.Errors_2.Select(e => new Exception($"Code: {e.Code}, Message: {e.Message}")));
            }

            throw new Exception("An unknown error occured on the Gateway level");
        }

        //very rough, to do: expand to more types than just object, able to infer unit from key
        private static JToken RecursiveBuildJToken(JToken benefitValue, List<ClaimValue> claimValues)
        {
            var jObject = new JObject
            {
                { "perPolicyYear", claimValues.Sum(c => c.ApprovedAmount)}
            };
            if (benefitValue.Type == JTokenType.Object)
                foreach (KeyValuePair<string, JToken> sub_obj in (JObject)benefitValue)
                {
                    if (sub_obj.Value.Type == JTokenType.Object)
                        jObject.Add(sub_obj.Key, RecursiveBuildJToken(benefitValue, claimValues));
                    else if (sub_obj.Key.Contains("maxDay"))
                    {
                        jObject.Add(sub_obj.Key, JToken.FromObject(claimValues.Count(c => c.Unit == "day")));
                    }
                }

            return jObject;
        }

        //TODO: implement better check
        public static bool ProductRepresentationRequested<T>(this ResolveFieldContext<T> context) => context.Document.OriginalQuery.Contains("representation", StringComparison.Ordinal);

    }
}
