﻿using CoverGo.DomainUtils;
using GraphQL.Types;
using System.Collections.Generic;

namespace CoverGo.Gateway.Interfaces.Encryptions
{
    public class InitializeTenantEncryptionsInputGraph
    {
        public IEnumerable<EncryptionConfigInputGraph> EncryptionConfigs { get; set; }
    }

    public class InitializeTenantEncryptionsInputGraphType : InputObjectGraphType<InitializeTenantEncryptionsInputGraph>
    {
        public InitializeTenantEncryptionsInputGraphType()
        {
            Name = "initializeTenantEncryptionsInput";

            Field(c => c.EncryptionConfigs, type: typeof(ListGraphType<EncryptionConfigInputGraphType>));
        }
    }

    public class EncryptionConfigInputGraph
    {
        public string Type { get; set; } //e.g. users/ policies, etc.
        public IEnumerable<KeyScalarValue> Parameters { get; set; }
        public IEnumerable<EventEncryptionInputGraph> EventEncryptions { get; set; }
        public IEnumerable<DomainEncryptionInputGraph> DomainEncryptions { get; set; }
    }

    public class EncryptionConfigInputGraphType : InputObjectGraphType<EncryptionConfigInputGraph>
    {
        public EncryptionConfigInputGraphType()
        {
            Name = "encryptionConfig";

            Field(c => c.Type);
            Field(c => c.DomainEncryptions, type: typeof(ListGraphType<DomainEncryptionInputGraphType>));
            Field(c => c.EventEncryptions, type: typeof(ListGraphType<EventEncryptionInputGraphType>));
            Field(d => d.Parameters, type: typeof(ListGraphType<KeyValueInputGraphType>));
        }
    }

    public class EventEncryptionInputGraph
    {
        public string EventType { get; set; }
        public string JsonPath { get; set; }
        public string AlgorithmId { get; set; }
        public string KeyString { get; set; }
        public IEnumerable<KeyScalarValue> Parameters { get; set; }
    }

    public class EventEncryptionInputGraphType : InputObjectGraphType<EventEncryptionInputGraph>
    {
        public EventEncryptionInputGraphType()
        {
            Name = "eventEncryptionInput";

            Field(e => e.AlgorithmId);
            Field(e => e.EventType);
            Field(e => e.JsonPath);
            Field(d => d.KeyString, nullable: true);
            Field(d => d.Parameters, type: typeof(ListGraphType<KeyValueInputGraphType>));
        }
    }

    public class DomainEncryptionInputGraph
    {
        public string JsonPath { get; set; }
        public string AlgorithmId { get; set; }
        public string KeyString { get; set; }
        public IEnumerable<KeyScalarValue> Parameters { get; set; }
    }

    public class DomainEncryptionInputGraphType : InputObjectGraphType<DomainEncryptionInputGraph>
    {
        public DomainEncryptionInputGraphType()
        {
            Name = "domainEncryptionInput";

            Field(d => d.AlgorithmId);
            Field(d => d.JsonPath);
            Field(d => d.KeyString, nullable: true);
            Field(d => d.Parameters, type: typeof(ListGraphType<KeyValueInputGraphType>));
        }
    }
}
