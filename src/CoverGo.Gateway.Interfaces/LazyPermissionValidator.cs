﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Domain.Auth;

namespace CoverGo.Gateway.Interfaces;

public class LazyPermissionValidator
{
    private readonly IAuthService _authService;
    private readonly PermissionCache _permissionCache;

    public LazyPermissionValidator(
        IAuthService authService,
        PermissionCache permissionCache)
    {
        _authService = authService;
        _permissionCache = permissionCache;
    }

    public async ValueTask<bool> HasAnyClaim(GraphQLUserContext userContext, params string[] claimTypes)
    {
        var targetIds = await GetPermissionTargetIdsAsync(userContext, claimTypes);

        return targetIds.Count > 0;
    }

    public async ValueTask<IEnumerable<string>> GetFilteredTargetIdsFromClaims(
        GraphQLUserContext userContext,
        IEnumerable<string> claimTypes,
        IEnumerable<string> targetIds)
    {
        var claimTypesAndTargetIds = await GetPermissionTargetIdsAsync(userContext, claimTypes);

        var result = Enumerable.Empty<string>();
        
        foreach (var claimType in claimTypes)
        {
            if (claimTypesAndTargetIds.TryGetValue(claimType, out var claimTargetIds))
            {
                if (claimTargetIds.Contains("all"))
                    return targetIds;

                if (targetIds == null)
                    result = result.Union(claimTargetIds);
                else
                {
                    var addedIds = targetIds.Intersect(claimTargetIds.Except(new List<string> { "all" }));
                    result = result.Union(addedIds);
                }
            }
        }

        return result;
    }

    public async ValueTask<IEnumerable<string>> GetTargetIdsFromClaims(
        GraphQLUserContext userContext,
        IEnumerable<string> claimTypes)
    {
        var targetIds = await GetPermissionTargetIdsAsync(userContext, claimTypes);
        var allowedIds = new List<string>();

        foreach (var claimType in claimTypes)
        {
            if (targetIds.TryGetValue(claimType, out var ids))
            {
                allowedIds.AddRange(ids ?? Enumerable.Empty<string>());
            }
        }

        return allowedIds;
    }

    private ValueTask<Dictionary<string, List<string>>> GetPermissionTargetIdsAsync(
        GraphQLUserContext userContext,
        IEnumerable<string> claimTypes)
    {
        var query = BuildQuery(userContext, claimTypes);

        //TODO: Use Async API once the dataloader deadlock issue is fixed
        return ValueTask.FromResult(GetPermissionTargetIds(query));
    }

    private Dictionary<string, List<string>> GetPermissionTargetIds(PermissionTargetIdQuery query)
    {
        var cacheKey = $"{query.TenantId}_{query.LoginId}_" +
                       $"{string.Join('_', query.PermissionNames.OrderBy(_ => _))}";
        return _permissionCache.GetOrAdd(
            cacheKey,
            _ => _authService.GetPermissionTargetIds(query));
    }

    private static PermissionTargetIdQuery BuildQuery(GraphQLUserContext userContext, IEnumerable<string> claimTypes)
    {
        return new PermissionTargetIdQuery(
            GetTenantId(userContext),
            GetLoginId(userContext),
            claimTypes.ToArray());
    }

    private static string GetLoginId(GraphQLUserContext graphQLUserContext)
    {
        return GetClaim(graphQLUserContext, "sub");
    }

    private static string GetTenantId(GraphQLUserContext graphQLUserContext)
    {
        return GetClaim(graphQLUserContext, "tenantId");
    }

    private static string GetClaim(GraphQLUserContext graphQLUserContext, string type)
    {
        return graphQLUserContext?.User?.Claims.FirstOrDefault(c => c.Type == type)?.Value;
    }
}