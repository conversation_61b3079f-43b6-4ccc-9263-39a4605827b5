using System.Collections.Generic;

namespace CoverGo.Gateway.Interfaces
{
    public class FileExtensionFilter
    {
        public Dictionary<string, string> AllowedExtensions { get; set; }
        public Dictionary<string, string> RestrictedExtensions { get; set; }

        public bool? IsExtensionAllowed(string extension) => AllowedExtensions?.ContainsKey(extension);
        
        public bool? IsExtensionRestricted(string extension) => RestrictedExtensions?.ContainsKey(extension);
    }
}