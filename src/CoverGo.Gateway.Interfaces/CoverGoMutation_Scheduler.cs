using CoverGo.Gateway.Domain.Scheduler;
using CoverGo.Gateway.Interfaces.Scheduler;
using GraphQL.Types;
using System;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeSchedulerMutations(ISchedulerService schedulerService, PermissionValidator permissionValidator)
        {

            Field<ResultGraphType>()
                .Name("createJobSchedule")
                .Argument<NonNullGraphType<JobScheduleInputGraphType>>("input", "")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeJobSchedules", "all");
                    string tenantId = context.GetTenantIdFromToken();
                    string appId = context.GetClientIdFromToken();
                    JobSchedule input = context.GetArgument<JobSchedule>("input");
                    string loginId = context.GetLoginIdFromToken();
                    input.IsActive = true;
                    input.CreatedAt = DateTime.Now;
                    input.LastModifiedAt = DateTime.Now;
                    input.CreatedById = loginId;
                    input.LastModifiedById = loginId;
                    input.TenantId = tenantId;
                    input.JobDetail.AsLoginId = loginId;
                    input.JobDetail.AsAppId = appId;

                    return await schedulerService.CreateJobSchedule(tenantId, input);
                });

            Field<ResultGraphType>()
               .Name("deleteJobSchedule")
               .Argument<NonNullGraphType<StringGraphType>>("jobScheduleName", "Name of job schedule to remove")
               .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeJobSchedules", "all");
                    string tenantId = context.GetTenantIdFromToken();
                    string jobScheduleName = context.GetArgument<string>("jobScheduleName");

                    return await schedulerService.DeleteJobSchedule(tenantId, jobScheduleName);
                });

            Field<ResultGraphType>()
                .Name("triggerJobSchedule")
                .Argument<NonNullGraphType<StringGraphType>>("jobScheduleName", "Name of job schedule to trigger")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeJobSchedules", "all");
                    string tenantId = context.GetTenantIdFromToken();
                    string jobScheduleName = context.GetArgument<string>("jobScheduleName");

                    return await schedulerService.TriggerJobScheduleNow(tenantId, jobScheduleName);
                });
        }
    }
}