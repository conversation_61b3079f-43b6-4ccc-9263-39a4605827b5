using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Domain.Policies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using CoverGo.Gateway.Domain.Transactions;

namespace CoverGo.Gateway.Interfaces
{
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("filifewebhook/{tenantId}")]
    public class FiLifeWebhookController
    {
        private readonly ITransactionService _transactionService;
        private readonly ILogger _logger;

        public FiLifeWebhookController(
            ITransactionService transactionService,
            ILogger<FiLifeWebhookController> logger)
        {
            _transactionService = transactionService;
            _logger = logger;

        }

        [HttpPost("transaction/{referenceId}")]
        [AllowAnonymous]
        public async Task<ActionResult<string>> GetTransaction(string tenantId, string referenceId)
        {
            _logger.LogInformation("[GetTransaction] starting for tenant {Tenant}", tenantId);

            if (string.IsNullOrEmpty(tenantId) || string.IsNullOrEmpty(referenceId))
            {
                _logger.LogWarning("[GetTransaction] tenantId or referenceId is null");
                return new BadRequestResult();
            }

            var txns = await _transactionService.GetAsync(tenantId, new Domain.QueryArguments
            {
                Where = new TransactionWhere
                {
                    Id = referenceId.ToUpper()
                }
            });

            if (!txns.Any())
            {
                _logger.LogWarning("[GetTransaction] transaction not found");
                return new NotFoundResult();
            }

            Transaction txn = txns.FirstOrDefault();

            switch (txn.Status)
            {
                case TransactionStatus.Failed:
                case TransactionStatus.Rejected:
                    return new BadRequestResult();
                case TransactionStatus.Succeed:
                case TransactionStatus.Accepted:
                    return "RECEIVEOK";
                default:
                    return "RECEIVEOK";
            }
        }
    }
}