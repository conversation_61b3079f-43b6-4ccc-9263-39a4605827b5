using System.Collections.Generic;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Education;
using CoverGo.Gateway.Domain.Education.CourseProgressions;
using CoverGo.Gateway.Domain.Education.Courses;
using CoverGo.Gateway.Interfaces.Education.CourseProgressions;
using CoverGo.Gateway.Interfaces.Education.Courses;
using GraphQL.Authorization;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeEducationMutations(IEducationService educationService, PermissionValidator permissionValidator)
        {
            Field<CreatedStatusResultGraphType>()
                .Name("createCourse")
                .Description("creates a course")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateCourseInputGraphType>>("input", "the input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    CreateCourseCommand command = context.GetArgument<CreateCourseCommand>("input");
                    command.CreatedById = loginId;

                    await permissionValidator.Authorize(context, "writeCourses");

                    Result<CreatedStatus> result = await educationService.CreateCourseAsync(tenantId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateCourse")
                .Description("updates a course")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the course identifier")
                .Argument<NonNullGraphType<UpdateCourseInputGraphType>>("input", "the updated course")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string id = context.GetArgument<string>("id");

                    await permissionValidator.Authorize(context, "writeCourses", id);

                    UpdateCourseCommand command = context.GetArgument<Dictionary<string, object>>("input")
                        .ToUpdateCommand<UpdateCourseCommand>();
                    command.ModifiedById = loginId;

                    return await educationService.UpdateCourseAsync(tenantId, id, command);
                });

            Field<ResultGraphType>()
                .Name("deleteCourse")
                .Description("deletes a course")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the identifier of the course")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string id = context.GetArgument<string>("id");

                    await permissionValidator.Authorize(context, "writeCourses", id);

                    var command = new DeleteCommand {DeletedById = loginId};

                    return await educationService.DeleteCourseAsync(tenantId, id, command);
                });

            Field<CreatedStatusResultGraphType>()
                .Name("addSectionToCourse")
                .Description("Adds a section to a course")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("courseId", "the course identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string courseId = context.GetArgument<string>("courseId");
                    await permissionValidator.Authorize(context, "writeCourses", courseId);

                    var command = new AddSectionToCourseCommand {AddedById = context.GetLoginIdFromToken()};

                    Result<CreatedStatus> result =
                        await educationService.AddSectionToCourseAsync(tenantId, courseId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeSectionFromCourse")
                .Description("Removes a section from a course")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("courseId", "the course identifier")
                .Argument<NonNullGraphType<StringGraphType>>("sectionId", "The lesson id")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string courseId = context.GetArgument<string>("courseId");
                    string sectionId = context.GetArgument<string>("sectionId");
                    await permissionValidator.Authorize(context, "writeCourses", courseId);

                    Result result = await educationService.RemoveSectionFromCourseAsync(tenantId, courseId,
                        new RemoveSectionFromCourseCommand
                        {
                            SectionId = sectionId, RemovedById = context.GetLoginIdFromToken(),
                        });

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("addSectionToSection")
                .Description("Adds a section to an other section")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("courseId", "the course identifier")
                .Argument<NonNullGraphType<StringGraphType>>("sectionId", "the section identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string courseId = context.GetArgument<string>("courseId");
                    string sectionId = context.GetArgument<string>("sectionId");
                    await permissionValidator.Authorize(context, "writeCourses", courseId);

                    var command = new AddSectionToSectionCommand
                    {
                        SectionId = sectionId, AddedById = context.GetLoginIdFromToken()
                    };

                    Result<CreatedStatus> result =
                        await educationService.AddSectionToSectionAsync(tenantId, courseId, command);
                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("addLessonToSection")
                .Description("Adds a lesson to a section of a course")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("courseId", "the course identifier")
                .Argument<NonNullGraphType<StringGraphType>>("sectionId", "the section identifier")
                .Argument<AddLessonToSectionInputGraphType>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string courseId = context.GetArgument<string>("courseId");
                    string sectionId = context.GetArgument<string>("sectionId");
                    await permissionValidator.Authorize(context, "writeCourses", courseId);

                    AddLessonToSectionCommand command = context.GetArgument<AddLessonToSectionCommand>("input");
                    command.SectionId = sectionId;
                    command.AddedById = context.GetLoginIdFromToken();

                    Result<CreatedStatus> result =
                        await educationService.AddLessonToSectionAsync(tenantId, courseId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateLessonOfSection")
                .Description("updates a lesson")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("courseId", "the course identifier")
                .Argument<NonNullGraphType<StringGraphType>>("sectionId", "the section identifier")
                .Argument<NonNullGraphType<StringGraphType>>("lessonId", "the lesson identifier")
                .Argument<NonNullGraphType<UpdateLessonOfSectionInputGraphType>>("input", "the updated lesson")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string courseId = context.GetArgument<string>("courseId");
                    string sectionId = context.GetArgument<string>("sectionId");
                    string lessonId = context.GetArgument<string>("lessonId");

                    await permissionValidator.Authorize(context, "writeCourses", courseId);

                    UpdateLessonOfSectionCommand command = context.GetArgument<Dictionary<string, object>>("input")
                        .ToUpdateCommand<UpdateLessonOfSectionCommand>();
                    command.SectionId = sectionId;
                    command.LessonId = lessonId;
                    command.ModifiedById = loginId;

                    return await educationService.UpdateLessonOfSectionAsync(tenantId, courseId, command);
                });

            Field<ResultGraphType>()
                .Name("removeLessonFromSection")
                .Description("Removes a lesson from a course")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("courseId", "the course identifier")
                .Argument<NonNullGraphType<StringGraphType>>("sectionId", "the section identifier")
                .Argument<NonNullGraphType<StringGraphType>>("lessonId", "The lesson id")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string courseId = context.GetArgument<string>("courseId");
                    string sectionId = context.GetArgument<string>("sectionId");
                    string lessonId = context.GetArgument<string>("lessonId");
                    await permissionValidator.Authorize(context, "writeCourses", courseId);

                    Result result = await educationService.RemoveLessonFromSectionAsync(tenantId, courseId,
                        new RemoveLessonFromSectionCommand
                        {
                            LessonId = lessonId, SectionId = sectionId, RemovedById = context.GetLoginIdFromToken(),
                        });

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("addLessonItem")
                .Description("Adds an item to a lesson")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("courseId", "the course identifier")
                .Argument<NonNullGraphType<StringGraphType>>("lessonId", "the lesson identifier")
                .Argument<AddLessonItemInputGraphType>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string courseId = context.GetArgument<string>("courseId");
                    string lessonId = context.GetArgument<string>("lessonId");
                    await permissionValidator.Authorize(context, "writeCourses", courseId);

                    AddLessonItemCommand command = context.GetArgument<AddLessonItemCommand>("input");
                    command.LessonId = lessonId;
                    command.AddedById = context.GetLoginIdFromToken();

                    Result<CreatedStatus> result =
                        await educationService.AddLessonItemAsync(tenantId, courseId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateLessonItem")
                .Description("updates an item of a lesson")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("courseId", "the course identifier")
                .Argument<NonNullGraphType<StringGraphType>>("lessonId", "the lesson identifier")
                .Argument<NonNullGraphType<StringGraphType>>("itemId", "the item identifier")
                .Argument<NonNullGraphType<UpdateLessonItemInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string courseId = context.GetArgument<string>("courseId");
                    string lessonId = context.GetArgument<string>("lessonId");
                    string itemId = context.GetArgument<string>("itemId");

                    await permissionValidator.Authorize(context, "writeCourses", courseId);

                    UpdateLessonItemCommand command = context.GetArgument<Dictionary<string, object>>("input")
                        .ToUpdateCommand<UpdateLessonItemCommand>();
                    command.LessonId = lessonId;
                    command.ItemId = itemId;
                    command.ModifiedById = loginId;

                    return await educationService.UpdateLessonItemAsync(tenantId, courseId, command);
                });

            Field<ResultGraphType>()
                .Name("removeLessonItem")
                .Description("Removes an item from a lesson")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("courseId", "the course identifier")
                .Argument<NonNullGraphType<StringGraphType>>("lessonId", "the lesson id")
                .Argument<NonNullGraphType<StringGraphType>>("itemId", "the item identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string courseId = context.GetArgument<string>("courseId");
                    string lessonId = context.GetArgument<string>("lessonId");
                    string itemId = context.GetArgument<string>("itemId");
                    await permissionValidator.Authorize(context, "writeCourses", courseId);

                    Result result = await educationService.RemoveLessonItemAsync(tenantId, courseId,
                        new RemoveLessonItemCommand
                        {
                            LessonId = lessonId, ItemId = itemId, RemovedById = context.GetLoginIdFromToken(),
                        });

                    return result;
                });
            Field<CreatedStatusResultGraphType>()
                .Name("addOptionToLessonItem")
                .Description("Adds an option to a lesson item")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("courseId", "the course identifier")
                .Argument<NonNullGraphType<StringGraphType>>("lessonId", "the lesson identifier")
                .Argument<NonNullGraphType<StringGraphType>>("itemId", "the lesson identifier")
                .Argument<AddOptionToLessonItemInputGraphType>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string courseId = context.GetArgument<string>("courseId");
                    string lessonId = context.GetArgument<string>("lessonId");
                    string itemId = context.GetArgument<string>("itemId");
                    await permissionValidator.Authorize(context, "writeCourses", courseId);

                    AddOptionToLessonItemCommand command = context.GetArgument<AddOptionToLessonItemCommand>("input");
                    command.LessonId = lessonId;
                    command.ItemId = itemId;
                    command.AddedById = context.GetLoginIdFromToken();

                    Result<CreatedStatus> result =
                        await educationService.AddOptionToLessonItemAsync(tenantId, courseId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateOptionOfLessonItem")
                .Description("updates an optino of a lesson item")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("courseId", "the course identifier")
                .Argument<NonNullGraphType<StringGraphType>>("lessonId", "the lesson identifier")
                .Argument<NonNullGraphType<StringGraphType>>("itemId", "the item identifier")
                .Argument<NonNullGraphType<StringGraphType>>("optionId", "the option identifier")
                .Argument<NonNullGraphType<UpdateOptionOfLessonItemInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string courseId = context.GetArgument<string>("courseId");
                    string lessonId = context.GetArgument<string>("lessonId");
                    string itemId = context.GetArgument<string>("itemId");
                    string optionId = context.GetArgument<string>("optionId");

                    await permissionValidator.Authorize(context, "writeCourses", courseId);

                    UpdateOptionOfLessonItemCommand command = context.GetArgument<Dictionary<string, object>>("input")
                        .ToUpdateCommand<UpdateOptionOfLessonItemCommand>();
                    command.LessonId = lessonId;
                    command.ItemId = itemId;
                    command.OptionId = optionId;
                    command.ModifiedById = loginId;

                    return await educationService.UpdateOptionOfLessonItemAsync(tenantId, courseId, command);
                });

            Field<ResultGraphType>()
                .Name("removeOptionFromLessonItem")
                .Description("Removes an option from a lesson item")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("courseId", "the course identifier")
                .Argument<NonNullGraphType<StringGraphType>>("lessonId", "the lesson id")
                .Argument<NonNullGraphType<StringGraphType>>("itemId", "the item identifier")
                .Argument<NonNullGraphType<StringGraphType>>("optionId", "the option identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string courseId = context.GetArgument<string>("courseId");
                    string lessonId = context.GetArgument<string>("lessonId");
                    string itemId = context.GetArgument<string>("itemId");
                    string optionId = context.GetArgument<string>("optionId");
                    await permissionValidator.Authorize(context, "writeCourses", courseId);

                    Result result = await educationService.RemoveOptionFromLessonItemAsync(tenantId, courseId,
                        new RemoveOptionFromLessonItemCommand
                        {
                            LessonId = lessonId,
                            ItemId = itemId,
                            OptionId = optionId,
                            RemovedById = context.GetLoginIdFromToken(),
                        });

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createCourseProgression")
                .Description("creates a Course progression")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateCourseProgressionInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    CreateCourseProgressionCommand command =
                        context.GetArgument<CreateCourseProgressionCommand>("input");
                    command.CreatedById = loginId;

                    await permissionValidator.Authorize(context, "writeCourseProgressions");

                    Result<CreatedStatus> result =
                        await educationService.CreateCourseProgressionAsync(tenantId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateCourseProgression")
                .Description("updates a progression")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the course progression identifier")
                .Argument<NonNullGraphType<UpdateCourseProgressionInputGraphType>>("input", "the updated progression")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string id = context.GetArgument<string>("id");

                    await permissionValidator.Authorize(context, "writeCourseProgressions", id);

                    UpdateCourseProgressionCommand command = context.GetArgument<Dictionary<string, object>>("input")
                        .ToUpdateCommand<UpdateCourseProgressionCommand>();
                    command.ModifiedById = loginId;

                    return await educationService.UpdateCourseProgressionAsync(tenantId, id, command);
                });

            Field<ResultGraphType>()
                .Name("deleteCourseProgression")
                .Description("deletes a Course progression")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "The course progression identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string id = context.GetArgument<string>("id");

                    await permissionValidator.Authorize(context, "writeCourseProgressions", id);

                    var command = new DeleteCommand {DeletedById = loginId};

                    return await educationService.DeleteCourseProgressionAsync(tenantId, id, command);
                });

            Field<CreatedStatusResultGraphType>()
                .Name("addLessonCompletionToCourseProgression")
                .Description("Adds a lesson completion to a course progression")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("courseProgressionId", "The course progression identifier")
                .Argument<NonNullGraphType<AddLessonCompletionInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string courseProgressionId = context.GetArgument<string>("courseProgressionId");
                    await permissionValidator.Authorize(context, "writeCourseProgressions", courseProgressionId);
                    string accessToken = Tools.GetAccessToken((GraphQLUserContext)context.UserContext);
                    string lessonId = context.GetArgument<string>("lessonId");

                    AddLessonCompletionToCourseProgressionCommand command =
                        context.GetArgument<AddLessonCompletionToCourseProgressionCommand>("input");
                    command.AddedById = loginId;
                    command.AccessToken = accessToken;

                    Result<CreatedStatus> result =
                        await educationService.AddLessonCompletionToCourseProgressionAsync(tenantId,
                            courseProgressionId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateLessonCompletionOfCourseProgression")
                .Description("updates a lesson completion of a course progression")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("courseProgressionId", "The course progression identifier")
                .Argument<NonNullGraphType<StringGraphType>>("lessonCompletionId", "The lesson completion identifier")
                .Argument<NonNullGraphType<UpdateLessonCompletionInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string courseProgressionId = context.GetArgument<string>("courseProgressionId");
                    string lessonCompletionId = context.GetArgument<string>("lessonCompletionId");
                    string accessToken = Tools.GetAccessToken((GraphQLUserContext)context.UserContext);

                    await permissionValidator.Authorize(context, "writeCourseProgressions", courseProgressionId);

                    UpdateLessonCompletionOfCourseProgressionCommand command = context
                        .GetArgument<Dictionary<string, object>>("input")
                        .ToUpdateCommand<UpdateLessonCompletionOfCourseProgressionCommand>();
                    command.Id = lessonCompletionId;
                    command.AccessToken = accessToken;
                    command.ModifiedById = loginId;

                    return await educationService.UpdateLessonCompletionOfCourseProgressionAsync(tenantId,
                        courseProgressionId, command);
                });

            Field<ResultGraphType>()
                .Name("removeLessonCompletionFromCourseProgression")
                .Description("Removes a lesson completion from a course progression")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("courseProgressionId", "The course progression identifier")
                .Argument<NonNullGraphType<StringGraphType>>("lessonCompletionId", "The lesson completion id")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string courseProgressionId = context.GetArgument<string>("courseProgressionId");
                    string lessonCompletionId = context.GetArgument<string>("lessonCompletionId");
                    await permissionValidator.Authorize(context, "writeCourseProgressions", courseProgressionId);

                    Result result = await educationService.RemoveLessonCompletionFromCourseProgressionAsync(tenantId,
                        courseProgressionId,
                        new RemoveLessonCompletionFromCourseProgressionCommand
                        {
                            Id = lessonCompletionId, RemovedById = context.GetLoginIdFromToken(),
                        });

                    return result;
                });
        }
    }
}