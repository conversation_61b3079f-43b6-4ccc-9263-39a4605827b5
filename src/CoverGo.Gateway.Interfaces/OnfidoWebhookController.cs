﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Domain.Users.Kyc;
using CoverGo.Users.Domain.Individuals;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Interfaces
{
    namespace workspace.Controllers
    {
        [ApiExplorerSettings(IgnoreApi = true)]
        [Route("api/v1/kyc/onfido")]
        public class OnfidoWebhookController : ControllerBase
        {
            private readonly IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> _individualService;

            private readonly ILogger<OnfidoWebhookController> _logger;

            public OnfidoWebhookController(
                IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
                ILogger<OnfidoWebhookController> logger)
            {
                _individualService = individualService;
                _logger = logger;
            }

            [HttpPost("{tenantId}/webhookEvent")]
            public async Task<IActionResult> HandleEvents(string tenantId, [FromBody] object requestBody)
            {
                _logger.LogInformation("Handling Onfido webhook event for tenant {Tenant}", tenantId);
                HttpContext.Request.Headers.TryGetValue("X-SHA2-Signature", out StringValues signatureStringValues);
                string signature = signatureStringValues.ToString();

                var command = new OnKycWebhookEventReceivedCommand
                {
                    KycProvider = KycProviderEnum.Onfido,
                    Signature = signature,
                    RequestBody = requestBody
                };
                Result result = await _individualService.OnKycWebhookEventReceivedAsync(tenantId, command);
                if (result.Status != "success")
                    return BadRequest(result.Errors);

                return Ok();
            }
        }
    }
}