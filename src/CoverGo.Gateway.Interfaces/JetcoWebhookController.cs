﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Domain.Users;

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Interfaces
{
    [ApiExplorerSettings(IgnoreApi = true)]
    public class JetcoWebhookController : ControllerBase
    {
        private readonly IPolicyService _policyService;
        private readonly ITransactionService _transactionService;
        private readonly ILogger _logger;
        private readonly IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> _internalService;
        private readonly IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> _organizationService;
        private readonly IAuthService _authService;
        private readonly ICaseService _caseService;

        private readonly Dictionary<string, string> _JetcoRedirectUrlDictionary = new()
        {
            { "l", "http://localhost:3000/{locale}/thanks?" },
            { "p", "https://payments.covergo.com/thanks?" },
            { "ab", "https://bct-asia.quote.hk/{locale}/thanks?" },
            { "am", "https://metlife-asia.quote.hk/{locale}/thanks?" },
            { "aud", "https://metlife-asia-uat2.quote.hk/{locale}/thanks?" },
            { "ad", "https://imedical.asiainsurance.hk/thanks?" },
            { "amh", "https://moneyhero-asia-uat.quote.hk/{locale}/thanks?" },
            { "boc", "https://boc-distribution-uat.quote.hk/motor/payment-result?" },
            { "boc_uat", "https://wwwuat.bocgins.com/motor/payment-result?"},
            { "boc_prod", "https://www.bocgins.com/motor/payment-result?"},
            { "boc_aliyun_uat","http://*************:32107/motor/payment-result?"},
            { "boc_aliyun_prod","http://**************:32107/motor/payment-result?"},
        };

        private readonly List<string> _channelCodesToSkipSMS = new() { "HLDCV" };

        public JetcoWebhookController(
                IPolicyService policyService,
                ITransactionService transactionService,
                ILogger<JetcoWebhookController> logger,
                IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
                IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
                IAuthService authService,
                ICaseService caseService)
        {
            _policyService = policyService;
            _logger = logger;
            _internalService = internalService;
            _organizationService = organizationService;
            _transactionService = transactionService;
            _authService = authService;
            _caseService = caseService;
        }

        [HttpPost("j/{tenantId}/{clientId}/{transactionId}/{redirectUrlKey}/{tryIssue}/{locale}")]
        [Consumes("application/x-www-form-urlencoded")]
        public async Task<IActionResult> JetcoRedirection(string tenantId, string clientId, string transactionId, string redirectUrlKey, string tryIssue, string locale, [FromForm] JetcoRedirectCommand command, string rc = null, string ac = null, string amt = null)
        {
            try
            {
                _logger.LogInformation("Handling Jetco redirection for tenant {Tenant}, client {Client}, and transaction {Transaction}", tenantId, clientId, transactionId);
                Policy policy = null;
                Transaction transactionToProcess = (await _transactionService.GetAsync(tenantId,
                       new QueryArguments
                       {
                           Where = new TransactionWhere
                           {
                               Id = transactionId
                           }
                       }
                    )).FirstOrDefault();
                if (transactionToProcess == null)
                {
                    _logger.LogError($"The transaction {transactionId} was not found.");
                    return await HandleRedirect(false);
                }

                policy = (await _policyService.GetAsync(tenantId, new QueryArguments { Where = new PolicyWhere { Id = transactionToProcess.PolicyId } })).FirstOrDefault();
                if (policy == null || policy.IsIssued)
                {
                    _logger.LogError($"Policy {transactionToProcess.PolicyId} not found or already issued");
                    return await HandleRedirect(false);
                }

                if (tenantId.Contains("boc"))
                {
                    if (rc != "0")
                        return await HandleRedirect(false);
                    if (decimal.Parse(amt).ToString("0.00") != transactionToProcess.Amount?.ToString("0.00"))
                        return await HandleRedirect(false);
                }

                string providerToken = tenantId.Contains("boc") ? ac : command.String1;
                Result result = await _transactionService.ProcessAsync(tenantId, transactionId, new ProcessTransactionCommand
                {
                    ProviderToken = providerToken
                });
                if (result.Status != "success")
                {
                    _logger.LogError($"processTransaction '{transactionId}' failed. Errors: {string.Join(',', result.Errors ?? new List<string> { })}");
                    return await HandleRedirect(false, error:string.Join(',', result.Errors ?? new List<string> { }));
                }

                if (tryIssue == "1")
                {
                    string loginId = User.GetLoginIdFromToken(false);

                    Result<PolicyStatus> issuanceResult = await _policyService.IssuePolicyAsync(tenantId, new IssuePolicyCommand { PolicyId = transactionToProcess.PolicyId, IssuedById = loginId });
                    if (issuanceResult.Status != "success")
                    {
                        _logger.LogError($"Policy {transactionToProcess.PolicyId} issuance failed,  Errors: {string.Join(',', issuanceResult.Errors ?? new List<string> { })}");
                        return await HandleRedirect(false,error: issuanceResult.Errors?.LastOrDefault());
                    }

                    if (tenantId.Contains("asia"))
                        await ResolveAsiaPostIssuanceLogic(tenantId, clientId, transactionToProcess, policy, loginId);
                    else if (tenantId.Contains("boc"))
                        await ResolveBocPostIssuanceLogic(tenantId, policy);
                }

                return await HandleRedirect(result.Status == "success", error:string.Join(',', result.Errors ?? new List<string> { }));

                async Task ResolveAsiaPostIssuanceLogic(string tenantId, string clientId, Transaction transactionToProcess, Policy policy, string loginId)
                {
                    Fact childLinkFact = policy.Facts?.FirstOrDefault(f => f.Type == "familyDiscountChildPolicyId");
                    if (childLinkFact != null)
                    {
                        string childPolicyId = childLinkFact.Value.Value<string>();
                        Result<PolicyStatus> childPolicyIssuanceResult = await _policyService.IssuePolicyAsync(tenantId, new IssuePolicyCommand { PolicyId = childPolicyId, IssuedById = loginId });
                    }

                    Internal intern = null;
                    if (policy.ReferralCode != null)
                        intern = (await _internalService.GetAsync(tenantId, new EntityWhere { InternalCode = policy.ReferralCode })).FirstOrDefault();

                    if (clientId == "coverQuote_metlife") // HACKKKKK
                    {
                        Entity comOrg = (await _organizationService.GetAsync(tenantId, new EntityWhere { Name = "MetLife" })).First();
                        await _policyService.AddCommissionAsync(tenantId, transactionToProcess.PolicyId, new AddCommissionCommand { EntityId = comOrg.Id, JsonRule = "{ \"*\": [ {\"var\" : \"grossPremium\"}, 0.05 ]}" });

                        if (intern != null)
                            await _policyService.AddCommissionAsync(tenantId, transactionToProcess.PolicyId, new AddCommissionCommand { EntityId = intern.Id, JsonRule = "{ \"*\": [ {\"var\" : \"grossPremium\"}, 0.15 ]}" });
                    }
                }

                async Task ResolveBocPostIssuanceLogic(string tenantId, Policy policy)
                {
                    Login existingLoginWithUsername = await _authService.GetLoginByNameAsync(tenantId, policy.ContractHolder.Contacts.First(c => c.Type == "email").Value);
                    if (existingLoginWithUsername != null)
                    {
                        await AddExistingAccountTargettedPermissions(existingLoginWithUsername.Id, policy.CreatedById);
                        return;
                    }

                    Result<CreatedStatus> createLoginResult = await CreateLogin();
                    if (createLoginResult.Status != "success")
                        _logger.LogError($"BOC createLogin failed: {string.Join(',', createLoginResult.Errors)}");

                    Result addTargettedPermissionsResult = await AddNewAccountTargettedPermissions(createLoginResult.Value.Id, policy.CreatedById);
                    if (addTargettedPermissionsResult.Status != "success")
                        _logger.LogError($"BOC addTargettedPermissions failed: {string.Join(',', addTargettedPermissionsResult.Errors)}");

                    async Task<Result<CreatedStatus>> CreateLogin()
                    {
                        string email = policy.ContractHolder.Contacts.First(c => c.Type == "email").Value;
                        string id = policy.ContractHolder.Identities.First().Value;
                        var createLoginCommand = new CreateLoginCommand()
                        {
                            ClientId = "clientPortal",

                            Email = email,
                            Username = email,
                            Password = id,
                            IsEmailConfirmed = true,
                            IgnorePasswordValidation = true,
                            EntityId = policy.ContractHolder.Id,
                            EntityType = policy.ContractHolder.GetEntityType()
                        };

                        return await _authService.CreateLoginAsync(tenantId, createLoginCommand);
                    }

                    async Task<Result> AddNewAccountTargettedPermissions(string loginId, string policyCreatedById)
                    {
                        string permissionGroupId = (await _authService.GetPermissionGroupsAsync(tenantId, new PermissionGroupWhere { Name = "client" })).FirstOrDefault()?.Id;
                        if (permissionGroupId == null)
                            return Result.Failure("Permission Group Id for 'client' group not found");

                        var addTargettedPermissionCommands = new List<AddTargettedPermissionCommand> {
                        new(){
                            AddedById = loginId,
                            Type = "groups",
                            Value = permissionGroupId
                        },
                        new ()
                        {
                            AddedById = loginId,
                            Type ="readLogins",
                            Value = policyCreatedById
                        }
                    };
                        return await _authService.AddTargettedPermissionsAsync(tenantId, loginId, addTargettedPermissionCommands);
                    }

                    async Task<Result> AddExistingAccountTargettedPermissions(string loginId, string policyCreatedById)
                    {
                        var addTargettedPermissionCommands = new List<AddTargettedPermissionCommand> {
                        new ()
                        {
                            AddedById = loginId,
                            Type ="readLogins",
                            Value = policyCreatedById
                        }
                    };
                        return await _authService.AddTargettedPermissionsAsync(tenantId, loginId, addTargettedPermissionCommands);
                    }
                }

                async Task<RedirectResult> HandleRedirect(bool isSuccess,string error=null)
                {
                    _logger.LogInformation("JETCO Redirect Url Key: {redirectUrlKey}", redirectUrlKey);
                    string redirectUrl = _JetcoRedirectUrlDictionary.GetValueOrDefault(redirectUrlKey);
                    _logger.LogInformation("JETCO Redirect Url: {redirectUrl}", redirectUrl);
                    if (redirectUrl == null)
                    {
                        _logger.LogError($"The key '{redirectUrlKey}' doesn't exist.");
                        throw new Exception($"The key '{redirectUrlKey}' doesn't exist.");
                    }

                    redirectUrl = redirectUrl.Replace("{locale}", locale);

                    if (tenantId.Contains("boc"))
                        return await HandleBocRedirect();

                    return isSuccess
                        ? RedirectTo($"{redirectUrl}version=4")
                        : RedirectTo($"{redirectUrl}version=6");

                    async Task<RedirectResult> HandleBocRedirect()
                    {
                        string issuerNumber = null;
                        if (isSuccess)
                        {
                            issuerNumber = await QueryIssuedPolicyIssuerNumber();
                            await SendSMS(true);
                            return RedirectTo(AppendUrlParameters($"{redirectUrl}version=4"));
                        }

                        await SendSMS(false);
                        return RedirectTo($"{redirectUrl}version=6&error={WebUtility.UrlEncode(error)}");

                        async Task<string> QueryIssuedPolicyIssuerNumber() => 
                            (await _policyService.GetAsync(tenantId, new QueryArguments { Where = new PolicyWhere { Id = transactionToProcess.PolicyId } })).FirstOrDefault().IssuerNumber;

                        async Task SendSMS(bool isIssuanceSuccess)
                        {
                            string channelCode = policy.Facts?.FirstOrDefault(f => f.Type == "channelCode")?.Value.Value<string>();
                            if (_channelCodesToSkipSMS.Contains(channelCode))
                                return;

                            try
                            {
                                using HttpClient client = new();

                                Uri uri = new("http://***********/cmd/system/api/sendsms.cgi");

                                uri = AddParameter(uri, "label", "ad");
                                uri = AddParameter(uri, "tar_mode", "utf8");
                                string mobileNumber = policy.ContractHolder.Contacts.FirstOrDefault(c => c.Type == "mobile").Value;
                                uri = AddParameter(uri, "tar_num", $"852{mobileNumber}");

                                if (issuerNumber == null)
                                {
                                    Proposal proposal = (await _caseService.GetAsync(tenantId, new QueryArguments
                                    { 
                                        Where = new CaseWhere { 
                                            Proposals_contains = new ProposalWhere { 
                                                PolicyId_contains = policy.Id 
                                            } 
                                        } })).FirstOrDefault().Proposals.FirstOrDefault();
                                    issuerNumber = proposal.ProposalNumber;
                                }

                                string msgString = isIssuanceSuccess
                                    ? $"Thank you very much for applying BOCGI Motor insurance, Cert. No.: {issuerNumber}. For any enquiries, please contact BOCG Insurance Hotline (852) 3187 5100.\n\n感謝您投保「中銀集團保險」汽車保險。您的電子暫保單號碼： {issuerNumber}。如有查詢，請電：3187 5100。"
                                    : $"BOCG Insurance: Thank you for applying our Private Car Insurance on {DateTime.UtcNow.AddHours(8):yyyy-MM-dd H:m}. Transaction failed, application failure. Reference No: {issuerNumber}.\n\n中銀集團保險: 感謝您於 {DateTime.UtcNow.AddHours(8):yyyy-MM-dd H:m} 投保本家汽車保險計劃，付款不成功，投保失敗。參考號為{issuerNumber}.";
                                uri = AddParameter(uri, "tar_msg", msgString);


                                HttpResponseMessage response = await client.PostAsync(uri.ToString(), null);
                                string responseString = await response.Content.ReadAsStringAsync();
                                _logger.LogInformation("BOC SMS Result ||| Request Url:" + uri.ToString() + " ||| Result Status Code:" + response?.StatusCode + " ||| Result Content:" + responseString);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "BOC failure sending SMS for issuerNumber `{issuerNumber}`||| Exception: {message}", issuerNumber, ex.Message);
                            }
                        }

                        string AppendUrlParameters(string url)
                        {
                            Uri uri = new(url);
                            uri = AddParameter(uri, "policyNumber", issuerNumber);
                            uri = AddParameter(uri, "premium", policy.Premium.Amount.ToString());
                            uri = AddParameter(uri, "productId", policy.ProductId.ToString());
                            uri = AddParameter(uri, "channelCode", policy.Facts.FirstOrDefault(t => t.Type == "channelCode")?.Value?.Value<string>());

                            JToken insuredValue = policy.Values?.Value<JArray>("insureds")?.Children().FirstOrDefault();
                            string clientGroup = insuredValue.Value<string>("clientGroup") ?? "Normal";

                            uri = AddParameter(uri, "clientGroup", clientGroup);

                            string promotionCode = policy.Facts.FirstOrDefault(t => t.Type == "promotionCode")?.Value?.Value<string>();
                            if (promotionCode != null)
                                uri = AddParameter(uri, "promotionCode", promotionCode);

                            string vehicleRegistrationNumber = policy.Facts.FirstOrDefault(t => t.Type == "registrationNumber")?.Value?.Value<string>();
                            if (vehicleRegistrationNumber != null)
                                uri = AddParameter(uri, "vehicleRegistrationNumber", vehicleRegistrationNumber);

                            if (policy.IsRenewal)
                            {
                                uri = AddParameter(uri, "isRenewal", "true");
                                uri = AddParameter(uri, "endDate", policy.EndDate?.ToString("yyyy-MM-dd"));
                            }

                            _logger.LogInformation("BOC JETCO Redirect Url: {redirectUrl}", uri.ToString());
                            return uri.ToString();
                        }

                        Uri AddParameter(Uri url, string paramName, string paramValue)
                        {
                            var uriBuilder = new UriBuilder(url);
                            NameValueCollection query = HttpUtility.ParseQueryString(uriBuilder.Query);
                            query[paramName] = paramValue;
                            uriBuilder.Query = query.ToString();

                            return uriBuilder.Uri;
                        }
                    }

                    
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "jetco");
                throw;
            }
        }

        RedirectResult RedirectTo(string url)
        {
            _logger.LogInformation("JETCO RedirectTo Url: {redirectUrl}", url);
            return Redirect(url);
        }

        public class JetcoRedirectCommand
        {
            public string String1 { get; set; }
        }
    }
}
