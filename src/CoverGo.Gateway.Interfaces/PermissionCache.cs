﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using Microsoft.AspNetCore.Http;

namespace CoverGo.Gateway.Interfaces;

public class PermissionCache
{
    private const string ContextKey = "permissionCache";
    private readonly IHttpContextAccessor _contextAccessor;

    public PermissionCache(IHttpContextAccessor contextAccessor)
    {
        _contextAccessor = contextAccessor;
    }

    public Dictionary<string, List<string>> GetOrAdd(
        string key, Func<string, Dictionary<string, List<string>>> valueFactory)
    {
        EnsureCacheIsInitialized();
        var cache = _contextAccessor.HttpContext.Items[ContextKey]
            as LazyConcurrentDictionary<string, Dictionary<string, List<string>>>;
        return cache.GetOrAdd(key, valueFactory);
    }

    private void EnsureCacheIsInitialized()
    {
        if (_contextAccessor.HttpContext.Items[ContextKey] == null)
        {
            lock (_contextAccessor.HttpContext.Items)
            {
                _contextAccessor.HttpContext.Items[ContextKey] ??=
                    new LazyConcurrentDictionary<string, Dictionary<string, List<string>>>();
            }
        }
    }

    private class LazyConcurrentDictionary<TKey, TValue>
    {
        private readonly ConcurrentDictionary<TKey, Lazy<TValue>> _concurrentDictionary;

        public LazyConcurrentDictionary()
        {
            _concurrentDictionary = new ConcurrentDictionary<TKey, Lazy<TValue>>();
        }

        public TValue GetOrAdd(TKey key, Func<TKey, TValue> valueFactory)
        {
            var lazyResult = _concurrentDictionary.GetOrAdd(key, k => new Lazy<TValue>(() => valueFactory(k), LazyThreadSafetyMode.ExecutionAndPublication));

            return lazyResult.Value;
        }
    }
}