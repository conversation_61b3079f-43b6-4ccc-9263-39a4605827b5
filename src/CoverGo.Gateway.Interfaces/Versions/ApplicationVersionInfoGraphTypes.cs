﻿using CoverGo.Gateway.Infrastructure.Versions;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Versions;

public class ApplicationVersionInfoListGraphType : ObjectGraphType<ApplicationVersionInfoListGraph>
{
    public ApplicationVersionInfoListGraphType(VersionsService versionsService)
    {
        Field(x => x.List, type: typeof(ListGraphType<ApplicationVersionInfoGraphType>))
            .Description("Application Version Info List")
            .ResolveAsync(async _ =>
            {
                return await versionsService.GetVersionsAsync();
            });
    }
}

public class ApplicationVersionInfoListGraph
{
    public ApplicationVersionInfo[] List { get; set; }
}

public class ApplicationVersionInfoGraphType : ObjectGraphType<ApplicationVersionInfo>
{
    public ApplicationVersionInfoGraphType()
    {
        Name = "applicationVersionInfo";
        Description = "Application version details( version, revision, build date ..etc)";

        Field(x => x.Application);
        Field(x => x.Version);
        Field(x => x.Revision);
        Field(x => x.BuildDateTime,  type: typeof(DateTimeOffsetGraphType));
    }
}