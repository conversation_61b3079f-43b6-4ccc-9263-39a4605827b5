﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Binder;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Auth;
using CoverGo.Gateway.Interfaces.Commissions;
using CoverGo.Gateway.Interfaces.Users;
using GraphQL.DataLoader;
using GraphQL.Types;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Gateway.Interfaces.Binders
{
    public class BinderGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Status { get; set; }
        public IEnumerable<OtherId> OtherIds { get; set; }
        public DateTime? BindingDate { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public OrganizationGraph FirstParty { get; set; }
        public OrganizationGraph SecondParty { get; set; }
        public CommissionRuleGraph CommissionRule { get; set; }
        public IEnumerable<FactGraph> Facts { get; set; }

        public static BinderGraph ToGraph(Binder b)
        {
            if (b == null)
                return null;

            BinderGraph graph = new BinderGraph
            {
                Id = b.Id,
                BindingDate = b.BindingDate,
                EndDate = b.EndDate,
                FirstParty = b.FirstPartyEntityId != null ? new OrganizationGraph { Id = b.FirstPartyEntityId } : null,
                SecondParty = b.SecondPartyEntityId != null ? new OrganizationGraph { Id = b.SecondPartyEntityId } : null,
                Name = b.Name,
                OtherIds = b.OtherIds,
                CommissionRule = CommissionRuleGraph.ToGraph(b.CommissionRule),
                StartDate = b.StartDate,
                Status = b.Status,
                Facts = b.Facts.Select(FactGraph.ToGraph),
            }.PopulateSystemGraphFields(b);

            return graph;
        }
    }

    public class OtherIdGraphType : ObjectGraphType<OtherId>
    {
        public OtherIdGraphType()
        {
            Name = "otherId";

            Field(i => i.Key);
            Field(i => i.Id);
        }
    }

    public class BinderGraphType : ObjectGraphType<BinderGraph>
    {
        public BinderGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            PermissionValidator permissionValidator)
        {
            Name = "binder";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(b => b.Id, type: typeof(NonNullGraphType<IdGraphType>));
            Field(b => b.Name, nullable: true);
            Field(b => b.OtherIds, type: typeof(NonNullGraphType<ListGraphType<OtherIdGraphType>>));
            Field(b => b.Status, nullable: true);

            Field(b => b.BindingDate, nullable: true);
            Field(b => b.StartDate, nullable: true);
            Field(b => b.EndDate, nullable: true);

            Field(b => b.FirstParty, type: typeof(OrganizationGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.FirstParty?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    IEnumerable<string> allowedOrganizationIds = await permissionValidator.GetTargetIdsFromClaim(context,"readOrganizations");
                    var orgLoader = accessor.Context.GetOrAddBatchLoader<string, Organization>("GetOrganizations",
                        ids => organizationService.GetDictionaryAsync(tenantId, allowedOrganizationIds.Contains("all")
                            ? new OrganizationWhere { Id_in = ids?.ToList() }
                            : new OrganizationWhere
                            {
                                And = new List<OrganizationWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedOrganizationIds.ToList() }
                                }
                            }));

                    Organization org = await orgLoader.LoadAsync(context.Source.FirstParty.Id);
                    return org?.ToGraph() as OrganizationGraph;
                });

            Field(b => b.SecondParty, type: typeof(OrganizationGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.SecondParty?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    IEnumerable<string> allowedOrganizationIds = await permissionValidator.GetTargetIdsFromClaim(context,"readOrganizations");
                    var orgLoader = accessor.Context.GetOrAddBatchLoader<string, Organization>("GetOrganizations",
                        ids => organizationService.GetDictionaryAsync(tenantId, allowedOrganizationIds.Contains("all")
                            ? new OrganizationWhere { Id_in = ids?.ToList() }
                            : new OrganizationWhere
                            {
                                And = new List<OrganizationWhere>
                                {
                                        new() { Id_in = ids?.ToList() },
                                        new() { Id_in = allowedOrganizationIds.ToList() }
                                }
                            }));

                    Organization org = await orgLoader.LoadAsync(context.Source.SecondParty.Id);
                    return org?.ToGraph() as OrganizationGraph;
                });

            Field(b => b.CommissionRule, type: typeof(CommissionRuleGraphType));

            Field(b => b.Facts, type: typeof(NonNullGraphType<ListGraphType<NonNullGraphType<FactGraphType>>>));
        }
    }

    public class BindersGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<BinderGraph> List { get; set; }
    }

    public class BindersGraphType : ObjectGraphType<BindersGraph>
    {
        public BindersGraphType(IBinderService binderService, PermissionValidator permissionValidator)
        {
            Name = "binders";
            Description = "Gets all binders";

            Field(c => c.TotalCount)
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();

                   IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readBinders");
                   BinderWhere where = allowedIds.Contains("all")
                       ? context.ComputeArgAndVar<BinderWhere, BindersGraph>("where") ?? new BinderWhere()
                       : new BinderWhere
                       {
                           And = new List<BinderWhere>
                           {
                                context.ComputeArgAndVar<BinderWhere, BindersGraph>("where") ?? new BinderWhere(),
                                new() { Id_in = allowedIds.ToList() }
                           }
                       };

                   return await binderService.GetTotalCountAsync(tenantId, where);
               });

            Field(c => c.List, type: typeof(ListGraphType<BinderGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readBinders");
                    BinderWhere where = allowedIds.Contains("all")
                        ? context.ComputeArgAndVar<BinderWhere, BindersGraph>("where") ?? new BinderWhere()
                        : new BinderWhere
                        {
                            And = new List<BinderWhere>
                            {
                                context.ComputeArgAndVar<BinderWhere, BindersGraph>("where") ?? new BinderWhere(),
                                new() { Id_in = allowedIds.ToList() }
                            }
                        };

                    int? skip = context.ComputeArgAndVar<int?, BindersGraph>("skip");
                    int? first = context.ComputeArgAndVar<int?, BindersGraph>("limit");
                    SortGraph sort = context.ComputeArgAndVar<SortGraph, BindersGraph>("sort");
                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var queryArguments = new BinderQueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy };
                    IEnumerable<Binder> binders = await binderService.GetAsync(tenantId, queryArguments);
                    return binders.Select(c => BinderGraph.ToGraph(c));
                });
        }
    }

    public class BinderWhereInputGraphType : InputObjectGraphType<BinderWhere>
    {
        public BinderWhereInputGraphType()
        {
            Name = "binderWhereInput";
            Description = "A binder search filter";

            Field(f => f.Or, type: typeof(ListGraphType<BinderWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<BinderWhereInputGraphType>));

            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);
            Field(f => f.CommissionRuleWhere, type: typeof(CommissionRuleWhereInputGraphType));
            Field(f => f.Status, nullable: true);

            this.PopulateSystemWhereFields();
        }
    }

    public class BinderInputGraphType : InputObjectGraphType<CreateBinderCommand>
    {
        public BinderInputGraphType()
        {
            Name = "binderInput";

            Field(a => a.Name, nullable: true);
            Field(a => a.OtherIds, type: typeof(ListGraphType<NonNullGraphType<OtherIdInputGraphType>>));
            Field(a => a.FirstPartyEntityId, nullable: true);
            Field(a => a.SecondPartyEntityId, nullable: true);
            Field(a => a.EndDate, nullable: true);
            Field(a => a.StartDate, nullable: true);
            Field(a => a.BindingDate, nullable: true);
            Field(a => a.CommissionRule, type: typeof(CommissionRuleInputGraphType));
        }
    }

    public class OtherIdInputGraphType : InputObjectGraphType<OtherId>
    {
        public OtherIdInputGraphType()
        {
            Name = "otherIdInput";

            Field(i => i.Key);
            Field(i => i.Id);
        }
    }

    public class AssociatedContractGraph
    {
        public string Id { get; set; }
        public BinderGraph Contract { get; set; }
    }

    public class AssociatedContractGraphType : ObjectGraphType<AssociatedContractGraph>
    {
        public AssociatedContractGraphType(
            IDataLoaderContextAccessor accessor,
            IBinderService binderService,
            PermissionValidator permissionValidator)
        {
            Name = "associatedContract";

            Field(c => c.Id, nullable: true);
            Field(c => c.Contract, type: typeof(BinderGraphType)).ResolveAsync(async context =>
            {
                if (context.Source.Contract?.Id == null)
                    return null;

                string tenantId = context.GetTenantIdFromToken();

                IEnumerable<string> allowedBinderIds = await permissionValidator.GetTargetIdsFromClaim(context,"readBinders");

                var binderLoader = accessor.Context.GetOrAddBatchLoader<string, Binder>("GetBinders",
                 i =>
                 {
                     var whereList = new List<BinderWhere> { };

                     if (!allowedBinderIds.Contains("all"))
                         whereList.Add(new BinderWhere { Id_in = allowedBinderIds.ToList() });

                     return binderService.GetDictionaryAsync(tenantId, whereList.Any()
                         ? new BinderWhere { And = new List<BinderWhere> { new() { Id_in = i?.ToList() } }.Concat(whereList).ToList() }
                         : new BinderWhere { Id_in = i?.ToList() });
                 });

                Binder binder = await binderLoader.LoadAsync(context.Source.Contract.Id);

                return BinderGraph.ToGraph(binder);
            });
        }
    }

    public class AddAssociatedContractCommandGraph 
    {
        public string CommandId { get; set; }
        public BinderGraph Contract { get; set; }
        public DateTime Timestamp { get; set; }
        public LoginGraph AddedBy { get; set; }

        public static AddAssociatedContractCommandGraph ToGraph (AddAssociatedContractCommand command)
         => command == null
                ? null
                : new AddAssociatedContractCommandGraph
                {
                    CommandId = command.CommandId,
                    Timestamp = command.Timestamp,
                    Contract = new BinderGraph { Id = command.ContractId },
                    AddedBy = new LoginGraph { Id = command.AddedById }
                };
    }

    public class AddAssociatedContractCommandGraphType : ObjectGraphType<AddAssociatedContractCommandGraph>
    {
        public AddAssociatedContractCommandGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IBinderService binderService,
            PermissionValidator permissionValidator)
        {
            Name = "addAssociatedContractCommandGraph";

            Field(c => c.CommandId, nullable: true);
            Field(c => c.Timestamp, type: typeof(DateTimeGraphType));
            Field(c => c.Contract, type: typeof(BinderGraphType))
                .ResolveAsync(async context =>
            {
                if (context.Source.Contract?.Id == null)
                    return null;

                string tenantId = context.GetTenantIdFromToken();

                IEnumerable<string> allowedBinderIds = await permissionValidator.GetTargetIdsFromClaim(context,"readBinders");

                var binderLoader = accessor.Context.GetOrAddBatchLoader<string, Binder>("GetBinders",
                 i =>
                 {
                     var whereList = new List<BinderWhere> { };

                     if (!allowedBinderIds.Contains("all"))
                         whereList.Add(new BinderWhere { Id_in = allowedBinderIds.ToList() });

                     return binderService.GetDictionaryAsync(tenantId, whereList.Any()
                         ? new BinderWhere { And = new List<BinderWhere> { new() { Id_in = i?.ToList() } }.Concat(whereList).ToList() }
                         : new BinderWhere { Id_in = i?.ToList() });
                 });

                Binder binder = await binderLoader.LoadAsync(context.Source.Contract.Id);

                return BinderGraph.ToGraph(binder);
            });
            Field(a => a.AddedBy, type: typeof(LoginGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.AddedBy?.Id == null)
                        return null;

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,"readLogins", new List<string> { context.Source.AddedBy.Id });
                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.AddedBy.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                    i => authService.GetLoginsDictionaryAsync(tenantId, i));

                    Login loginDao = await loginLoader.LoadAsync(context.Source.AddedBy.Id);

                    return LoginGraph.ToGraph(loginDao);
                });
        }
    }

    public class RemoveAssociatedContractCommandGraph
    {
        public string CommandId { get; set; }
        public string Id { get; set; }
        public string EndorsementId { get; set; }
        public DateTime Timestamp { get; set; }
        public LoginGraph RemovedBy { get; set; }

        public static RemoveAssociatedContractCommandGraph ToGraph(RemoveAssociatedContractCommand command)
          => command == null
                 ? null
                 : new RemoveAssociatedContractCommandGraph
                 {
                     CommandId = command.CommandId,
                     Timestamp = command.Timestamp,
                     Id = command.Id,
                     EndorsementId = command.EndorsementId,
                     RemovedBy = new LoginGraph { Id = command.RemovedById }
                 };
    }
    public class RemoveAssociatedContractCommandGraphType : ObjectGraphType<RemoveAssociatedContractCommandGraph>
    {
        public RemoveAssociatedContractCommandGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "removeAssociatedContractCommandGraph";

            Field(c => c.CommandId, nullable: true);
            Field(c => c.EndorsementId, nullable: true);
            Field(c => c.Timestamp, type: typeof(DateTimeGraphType));
            Field(c => c.Id, nullable: true);
            Field(a => a.RemovedBy, type: typeof(LoginGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.RemovedBy?.Id == null)
                        return null;

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,"readLogins", new List<string> { context.Source.RemovedBy.Id });
                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.RemovedBy.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                    i => authService.GetLoginsDictionaryAsync(tenantId, i));

                    Login loginDao = await loginLoader.LoadAsync(context.Source.RemovedBy.Id);

                    return LoginGraph.ToGraph(loginDao);
                });
        }
    }
}
