using System;
using System.Collections.Generic;
using System.Linq;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.Notifications;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Infrastructure.AxaThServices;
using CoverGo.Gateway.Infrastructure.DLVNServices.DLVNCommands;
using CoverGo.Gateway.Interfaces.Notifications;
using CoverGo.Gateway.Interfaces.Products;
using GraphQL.Authorization;
using GraphQL.Types;
using Microsoft.Extensions.Options;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeNotificationsMutations(
            IAuthService authService,
            INotificationService notificationService,
            ICaptchaVerificationService captchaVerificationService,
            PermissionValidator permissionValidator,
            IFileSystemService fileSystemService,
            ITemplateService templateService,
            IOptions<IssueDebitNoteNotificationSetttings> issueDebitNoteNotificationSetttings)
        {
            Field<ResultGraphType>()
                .Name("sendNotification")
                .AuthorizeWith("any")
                .Description("Sends a notification on multiple channels")
                .Argument<NonNullGraphType<SendNotificationInputGraphType>>("input", "The notification input")
                .Argument<StringGraphType>("captchaResponseToken", "The user response token provided by the reCAPTCHA client-side integration on your site")
                .Argument<StringGraphType>("remoteIp", "The user's IP address.")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "sendNotifications");

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();

                    SendNotificationGraph input = context.GetArgument<SendNotificationGraph>("input");
                    SendNotificationCommand command = SendNotificationGraph.ToCommand(input);

                    // this is custom logic sepcifically for DLVN for the Pen Test reported ticket
                    (bool, string) validationResult = DLVNHelper.ValidateDlvnNotificationTemplateContentJsonString(command.EmailMessage?.TemplateRendering?.Input?.ContentJsonString, tenantId);
                    if (!validationResult.Item1)
                        return Result.Failure(validationResult.Item2);

                    Result result;

                    var fileAttachmentsResult = await command.PopulateFileAttachments(tenantId, fileSystemService);
                    if (!fileAttachmentsResult.IsSuccess) return fileAttachmentsResult;

                    string[] msigTenants = { "msig_uat", "msig_dev", "msig_qa", "msig_prod" };
                    string[] requiredCaptchaClientIds = { "hr_portal", "member_portal" };

                    if (msigTenants.Contains(tenantId)
                        && requiredCaptchaClientIds.Contains(clientId)
                        && Environment.GetEnvironmentVariable("DISABLE_CAPTCHA") != "true")
                    {
                        string token = context.GetArgument<string>("captchaResponseToken");
                        string remoteIp = context.GetArgument<string>("remoteIp");
                        result = await captchaVerificationService.IsCaptchaValid(tenantId, clientId, token, remoteIp);

                        if (result.Status != "success")
                            return result;
                    }

                    Login loginUser;
                    if (tenantId == "tcb" || tenantId == "tcb_uat")
                    {
                        command.EmailMessage.Subject = null; // For TCB we do not want the user to be able to set the subject. This will ensure it is overridden with data in the template, if present.
                        loginUser = (await authService.GetLoginsAsync(tenantId, new LoginWhere { EntityIds = new[] { command.ToEntityId }, ExcludePermissions = true })).FirstOrDefault();
                        string userEmail = loginUser?.Email;

                        if (command.ToEntityId != context.GetEntityIdFromToken() || command.EmailMessage.To != userEmail)
                            return new Result { Status = "failure", Errors = new List<string> { "Cannot send email to a different user other than yourself" } };
                    }
                    else //TODO: See if the above TCB logics can use this new permission
                    {
                        IEnumerable<string> allowedIds =
                            await permissionValidator.GetTargetIdsFromClaim(context, "sendNotifications");
                        string loginId = context.GetLoginIdFromToken();
                        loginUser = await authService.GetLoginById(tenantId, loginId);

                        List<string> errs = command.PopulatePermissionTargetIds(loginUser, allowedIds);
                        if (errs != null)
                            return new Result { Status = "failure", Errors = errs };
                    }

                    // template's logicalId support
                    result = await command.PopulateTemplateIdParameters(tenantId, templateService);
                    if (result.Status != "success")
                        return result;

                    // Dirty cheat code for binding the member data of current login into the MSIG Contact Us template to pass Penetration Test.
                    if (msigTenants.Contains(tenantId))
                    {
                        // dddeef6d-3932-4199-8d38-62ddd0d0bacd: HR contact us template for coverHealth_dev
                        // d405bafa-c290-47e6-a78a-ea501c1f4347: Member contact us template for coverHealth_dev
                        // 3ee5eced-7c14-4122-af96-284f89059a5a: HR contact us template for other MSIG tenants
                        // e22c612a-8fdf-48ef-8044-7a181b90cb12: Member contact us template for other MSIG tenants
                        string[] templateIdsRequiredUserData = {
                            "dddeef6d-3932-4199-8d38-62ddd0d0bacd",
                            "d405bafa-c290-47e6-a78a-ea501c1f4347",
                            "3ee5eced-7c14-4122-af96-284f89059a5a",
                            "e22c612a-8fdf-48ef-8044-7a181b90cb12" };
                        if (templateIdsRequiredUserData.Contains(command.EmailMessage?.TemplateRendering?.TemplateId))
                        {
                            command.EmailMessage.TemplateRendering.Input ??= new RenderParameters();
                            command.EmailMessage.TemplateRendering.Input.Name ??= "data";
                            command.EmailMessage.TemplateRendering.Input.VariablesJsonString ??= $"{{ \"where\": {{ \"id\": \"{loginUser?.EntityId}\" }} }}";
                        }
                    }

                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, command.EmailMessage?.TemplateRendering?.Input, out List<string> errors);
                    if (errors.Any())
                        return new Result { Status = "failure", Errors = errors };
                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, command.InternalMessage?.TemplateRendering?.Input, out errors);
                    if (errors.Any())
                        return new Result { Status = "failure", Errors = errors };
                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, command.SmsMessage?.TemplateRendering?.Input, out errors);
                    if (errors.Any())
                        return new Result { Status = "failure", Errors = errors };

                    command.SentById = context.GetLoginIdFromToken();
                    return await notificationService.SendAsync(tenantId, command);
                });


            Field<ResultGraphType>()
                .Name("sendIssueDebitNoteNotification")
                .AuthorizeWith("any")
                .Description("Sends a notification on multiple channels for Issue Debit Note")
                .Argument<NonNullGraphType<SendIssueDebitNoteNotificationInputGraphType>>("input", "The notification input")
                .Argument<StringGraphType>("captchaResponseToken", "The user response token provided by the reCAPTCHA client-side integration on your site")
                .Argument<StringGraphType>("remoteIp", "The user's IP address.")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "sendNotifications");

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();

                    var sendIssueDebitNoteNotificationSetttings = issueDebitNoteNotificationSetttings.Value?.Values?.GetSettingsByTenantId(tenantId, clientId);

                    if (sendIssueDebitNoteNotificationSetttings == null)
                        return Result.Failure("IssueDebitNoteNotificationSetttings are not configured correctly.");

                    SendNotificationGraph input = context.GetArgument<SendNotificationGraph>("input");
                    SendNotificationCommand command = SendNotificationGraph.ToCommand(input);

                    if (AxaThHelper.IsWkhtmlToPdfInputValid(command?.EmailMessage?.TemplateRendering?.Input?.ContentJsonString))
                        return Result.Failure("contentJsonString in the emailMessage field of sendIssueDebitNoteNotificationInput contains invalid values.");

                    command.EmailMessage.From = sendIssueDebitNoteNotificationSetttings.From;
                    command.EmailMessage.FromName = sendIssueDebitNoteNotificationSetttings.FromName;
                    command.EmailMessage.Subject = sendIssueDebitNoteNotificationSetttings.Subject;
                    command.EmailMessage.TemplateRendering.TemplateId = sendIssueDebitNoteNotificationSetttings.TemplateId;
                    List<(string,string)> emailSubjectKeys = AxaThHelper.GetSubjectPlaceHolderKeys(sendIssueDebitNoteNotificationSetttings.Subject);

                    foreach ((string,string) emailSubjectKey in emailSubjectKeys)
                    {
                        command.EmailMessage.Subject = command.EmailMessage.Subject.Replace(emailSubjectKey.Item1, emailSubjectKey.Item2);
                    }

                    Result result;

                    var fileAttachmentsResult = await command.PopulateFileAttachments(tenantId, fileSystemService);
                    if (!fileAttachmentsResult.IsSuccess) return fileAttachmentsResult;


                    Login loginUser;

                    IEnumerable<string> allowedIds =
                        await permissionValidator.GetTargetIdsFromClaim(context, "sendNotifications");
                    string loginId = context.GetLoginIdFromToken();
                    loginUser = await authService.GetLoginById(tenantId, loginId);

                    List<string> errs = command.PopulatePermissionTargetIds(loginUser, allowedIds);
                    if (errs != null)
                        return new Result { Status = "failure", Errors = errs };


                    // template's logicalId support
                    result = await command.PopulateTemplateIdParameters(tenantId, templateService);
                    if (result.Status != "success")
                        return result;

                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, command.EmailMessage?.TemplateRendering?.Input, out List<string> errors);
                    if (errors.Any())
                        return new Result { Status = "failure", Errors = errors };
                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, command.InternalMessage?.TemplateRendering?.Input, out errors);
                    if (errors.Any())
                        return new Result { Status = "failure", Errors = errors };
                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, command.SmsMessage?.TemplateRendering?.Input, out errors);
                    if (errors.Any())
                        return new Result { Status = "failure", Errors = errors };

                    command.SentById = context.GetLoginIdFromToken();
                    return await notificationService.SendAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("updateNotificationStatus")
                .AuthorizeWith("any")
                .Description("Update the status of a notification")
                .Argument<NonNullGraphType<StringGraphType>>("notificationId", "The notification identifier")
                .Argument<NonNullGraphType<StringGraphType>>("status", "The new status of the notification")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string notificationId = context.GetArgument<string>("notificationId");

                    await notificationService.UpdateStatusAsync(tenantId, notificationId, new UpdateNotificationStatusCommand
                    {
                        Status = context.GetArgument<string>("status")
                    });

                    return new Result { Status = "success" };
                });

            Field<ResultGraphType>()
                .Name("deleteNotification")
                .AuthorizeWith("any")
                .Description("Delete the notification")
                .Argument<NonNullGraphType<StringGraphType>>("notificationId", "The notification identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string notificationId = context.GetArgument<string>("notificationId");

                    Result result = await notificationService.DeleteAsync(tenantId, notificationId);

                    return result;
                });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("createNotificationTrigger")
                .AuthorizeWith("any")
                .Description("Creates a notification trigger")
                .Argument<NonNullGraphType<CreateNotificationTriggerInputGraphType>>("input", "Input to create a notification trigger")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    NotificationTriggerGraph input = context.GetArgument<NotificationTriggerGraph>("input");

                    var command = new CreateTriggerCommand
                    {
                        CronExpression = input.CronExpression,
                        SendNotificationCommand = new SendNotificationCommand
                        {
                            FromEntityId = input.Notification.FromEntityId,
                            ToEntityId = input.Notification.ToEntityId,
                            PolicyId = input.Notification.PolicyId,
                            OfferId = input.Notification.OfferId,
                            Type = input.Notification.Type,
                            EmailMessage = input.Notification.EmailMessage,
                            SmsMessage = input.Notification.SmsMessage,
                            PushMessage = new PushMessage
                            {
                                Content = input.Notification.PushMessage?.Content,
                                Title = input.Notification.PushMessage?.Title,
                                Token = input.Notification.PushMessage?.Token,
                                Topic = input.Notification.PushMessage?.Topic,
                                Data = input.Notification.PushMessage?.Data?.ToDictionary(x => x.Type, x => x.Value)
                            },
                            SentById = context.GetLoginIdFromToken()
                        }
                    };

                    Result result = await notificationService.CreateTriggerAsync(tenantId, command);

                    return result;
                });

            Field<NonNullGraphType<ResultGraphType>>()
                .Name("deleteNotificationTrigger")
                .Description("Deletes a notfication trigger")
                .Argument<NonNullGraphType<StringGraphType>>("notificationTriggerId", "The notification trigger identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string notificationTriggerId = context.GetArgument<string>("notificationTriggerId");

                    Result result = await notificationService.DeleteTriggerAsync(tenantId, notificationTriggerId);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("addMyDevice")
                .AuthorizeWith("any")
                .Description("Adds a device to the authenticated user.")
                .Argument<NonNullGraphType<StringGraphType>>("deviceType", "The device type. Can be any key")
                .Argument<NonNullGraphType<StringGraphType>>("deviceId", "The id. Can be a registration token")
                .ResolveAsync(async context =>
                {
                    string entityId = context.GetEntityIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("updateIndividuals", "writeIndividuals").WithTargetIds(entityId));

                    string tenantId = context.GetTenantIdFromToken();
                    var command = new SaveDeviceCommand
                    {
                        RefType = "entityId",
                        RefId = entityId,
                        DeviceType = context.GetArgument<string>("deviceType"),
                        DeviceId = context.GetArgument<string>("deviceId")
                    };

                    await notificationService.SaveDeviceAsync(tenantId, command);
                    return new Result { Status = "success" };
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createNotificationSubscription")
                .Description("creates a notification subscription")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("topicName", "the topic name")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeNotificationSubscriptions");

                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    CreateNotificationSubscriptionCommand command = new() { TopicName = context.GetArgument<string>("topicName"), CreatedById = loginId };

                    Result<CreatedStatus> result = await notificationService.CreateNotificationSubscriptionAsync(tenantId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("deleteNotificationSubscription")
                .Description("deletes a notification subscription")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("id", "the id of the subscription")
                .Argument<StringGraphType>("topicName", "the topic name of the subscription")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string id = context.GetArgument<string>("id");
                    string topic = context.GetArgument<string>("topicName");

                    if (id != null) //check to be removed after migration
                        await permissionValidator.Authorize(context, "writeNotificationSubscriptions", id);

                    return await notificationService.DeleteNotificationSubscriptionAsync(tenantId, (id != null ? id : topic));
                });

            Field<CreatedStatusResultGraphType>()
               .Name("addTagToNotificationSubscription")
               .Description("adds a tag to a notification subscription")
               .Argument<NonNullGraphType<StringGraphType>>("id", "the notification subscription identifier")
               .Argument<NonNullGraphType<TagInputGraphType>>("input", "the tag content")
               .ResolveAsync(async ctx =>
               {
                   string tenantId = ctx.GetTenantIdFromToken();
                   string id = ctx.GetArgument<string>("id");
                   AddTagCommand command = ctx.GetArgument<AddTagCommand>("input");
                   await permissionValidator.Authorize(ctx, "writeNotificationSubscriptions", id);

                   command.AddedById = ctx.GetLoginIdFromToken();

                   return await notificationService.AddTagToNotificationSubscriptionAsync(tenantId, id, command);
               });

            Field<NonNullGraphType<ResultGraphType>>()
               .Name("removeTagFromNotificationSubscription")
               .Description("Removes a tag from a notification subscription")
               .Argument<NonNullGraphType<StringGraphType>>("id", "the notification subscription identifier")
               .Argument<NonNullGraphType<StringGraphType>>("tagId", "the tag identifier")
               .ResolveAsync(async ctx =>
               {
                   string tenantId = ctx.GetTenantIdFromToken();
                   string id = ctx.GetArgument<string>("id");
                   string tagId = ctx.GetArgument<string>("tagId");
                   string loginId = ctx.GetLoginIdFromToken();
                   await permissionValidator.Authorize(ctx, "writeNotificationSubscriptions", id);

                   var command = new RemoveCommand
                   {
                       Id = tagId,
                       RemovedById = loginId
                   };

                   return await notificationService.RemoveTagFromNotificationSubscriptionAsync(tenantId, id, command);
               });

            Field<ResultGraphType>()
                .Name("addEntityToNotificationSubscription")
                .Description("adds an entity to a notification subscription")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("id", "the id of the subscription")
                .Argument<StringGraphType>("topicName", "the topic name of the subscription")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the id of the entity to add")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string topic = context.GetArgument<string>("topicName");
                    string entityId = context.GetArgument<string>("entityId");
                    string id = context.GetArgument<string>("id");
                    string loginId = context.GetLoginIdFromToken();

                    if (id != null) //check to be removed after migration
                        await permissionValidator.Authorize(context, "writeNotificationSubscriptions", id);

                    return await notificationService.AddEntityToNotificationSubscriptionAsync(
                        tenantId,
                        (id != null ? id : topic),
                        new AddEntityToNotificationSubscriptionCommand { EntityId = entityId, AddedById = loginId }
                        );
                });

            Field<ResultGraphType>()
                .Name("removeEntityFromNotificationSubscription")
                .Description("removes an entity from a notification subscription")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("id", "the id of the subscription")
                .Argument<StringGraphType>("topicName", "the topic name of the subscription")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the id of the entity to add")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string topic = context.GetArgument<string>("topicName");
                    string entityId = context.GetArgument<string>("entityId");
                    string id = context.GetArgument<string>("id");
                    string loginId = context.GetLoginIdFromToken();

                    if (id != null) //check to be removed after migration
                        await permissionValidator.Authorize(context, "writeNotificationSubscriptions", id);

                    return await notificationService.RemoveEntityFromNotificationSubscriptionAsync(
                        tenantId,
                        (id != null ? id : topic),
                        new RemoveEntityFromNotificationCommand { EntityId = entityId, RemovedById = loginId }
                        );
                });

            Field<ResultGraphType>()
                .Name("joinChatRoom")
                .Description("adds connection to a chat room")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("id", "the id of the chat room")
                .Argument<StringGraphType>("topicName", "the name of the chat room")
                .Argument<NonNullGraphType<StringGraphType>>("userName", "the name of the user")
                .Argument<NonNullGraphType<StringGraphType>>("connectionId", "the id of the SignalR connection")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string id = context.GetArgument<string>("id");
                    string topic = context.GetArgument<string>("topicName");
                    string userName = context.GetArgument<string>("userName");
                    string loginId = context.GetLoginIdFromToken();
                    string connectionId = context.GetArgument<string>("connectionId");
                    string entityId = context.GetEntityIdFromToken();

                    var andWhere = new NotificationSubscriptionWhere
                    {
                        And = new List<NotificationSubscriptionWhere> {
                        new() { EntitiesIds_contains = entityId }
                    }
                    };

                    if (id != null)
                    {
                        await permissionValidator.Authorize(context, "readNotificationSubscriptions", id);
                        andWhere.And.Add(new NotificationSubscriptionWhere { Id = id });
                    }
                    else if (topic != null)
                        andWhere.And.Add(new NotificationSubscriptionWhere { Topic = topic });
                    else
                        return Result.Failure("please specify the id of the chat room");

                    NotificationSubscription ns = (await notificationService.GetNotificationSubscriptionsAsync(
                        tenantId,
                        new Domain.QueryArguments
                        {
                            Where = andWhere
                        }
                    )).FirstOrDefault();

                    if (ns == null)
                        return Result.Failure("no available notification subscription");

                    return await notificationService.JoinChatRoomAsync(
                        tenantId,
                        id ?? topic,
                        new JoinChatRoomCommand
                        {
                            UserName = userName,
                            LoginId = loginId,
                            ConnectionId = connectionId
                        }
                    );
                });

            Field<ResultGraphType>()
                .Name("leaveChatRoom")
                .Description("removes connection from a chat room")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("id", "the id of the chat room")
                .Argument<StringGraphType>("topicName", "the name of the chat room")
                .Argument<NonNullGraphType<StringGraphType>>("connectionId", "the id of the SignalR connection")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string id = context.GetArgument<string>("id");
                    string topic = context.GetArgument<string>("topicName");
                    string connectionId = context.GetArgument<string>("connectionId");

                    return await notificationService.LeaveChatRoomAsync(
                        tenantId,
                        id ?? topic,
                        new LeaveChatRoomCommand { ConnectionId = connectionId }
                    );
                });

            Field<ResultGraphType>()
                .Name("createNotificationConfig")
                .Description("creates a notification config")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateNotificationConfigInputGraph>>("config", "the config input")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeNotificationConfigs");

                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    CreateNotificationConfigCommand command = context.GetArgument<CreateNotificationConfigCommand>("config");
                    command.CreatedById = loginId;

                    Result result = await notificationService.CreateNotificationConfigAsync(tenantId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("deleteNotificationConfig")
                .Description("deletes a notification config")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("clientId", "the client id identifier for which to delete the config")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeNotificationConfigs");

                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    string clientId = context.GetArgument<string>("clientId");

                    var command = new DeleteNotificationConfigCommand { ClientId = clientId, DeletedById = loginId };

                    Result result = await notificationService.DeleteNotificationConfigAsync(tenantId, command);

                    return result;
                });
        }
    }
}