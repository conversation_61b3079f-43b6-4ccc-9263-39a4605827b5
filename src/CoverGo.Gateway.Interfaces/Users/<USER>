﻿using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.NegotiatedRate;
using CoverGo.GraphQLGenerators;
using CoverGo.Users.Domain.NegotiatedRate;
using GraphQL.DataLoader;
using GraphQL.Types;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Gateway.Interfaces.Users
{
    public class CreateServiceItemAgreedFeeInput
    {
        [JsonRequired]
        public decimal Rate { get; set; }
        [JsonRequired]
        public string Currency { get; set; }
        [JsonRequired]
        public string ServiceItemId { get; set; }
    }

    public sealed class CreateServiceItemAgreedFeeInputGraphType : InputObjectGraphType<CreateServiceItemAgreedFeeInput>
    {
        public CreateServiceItemAgreedFeeInputGraphType()
        {
            Name = "createServiceItemAgreedFeeInput";
            Field(f => f.Rate);
            Field(f => f.Currency);
            Field(f => f.ServiceItemId);
        }
    }

    public class DeleteServiceItemAgreedFeeInput
    {
        [JsonRequired]
        public string ServiceItemAgreedFeeId { get; set; }
    }

    public sealed class DeleteServiceItemAgreedFeeInputGraphType : AutoInputObjectGraphType<DeleteServiceItemAgreedFeeInput>
    {
    }

    public class ServiceItemAgreedFeeGraph : SystemObjectGraph
    {
        [JsonRequired]
        public string Id { get; set; }

        public decimal Rate { get; set; }
        public string Currency { get; set; }
        public ServiceItem ServiceItem { get; set; }
        public static ServiceItemAgreedFeeGraph ToGraph(ServiceItemAgreedFee domain)
        {
            if (domain == null)
                return null;

            var graph = new ServiceItemAgreedFeeGraph
            {
                Id = domain.Id,
                Currency = domain.Currency,
                Rate = domain.Rate,
                ServiceItem = new ServiceItem { Id = domain.ServiceItemId },
            };

            graph.PopulateSystemGraphFields(domain);

            return graph;
        }
    }
    public sealed class ServiceItemAgreedFeeGraphType : ObjectGraphType<ServiceItemAgreedFeeGraph>
    {
        public ServiceItemAgreedFeeGraphType(IDataLoaderContextAccessor accessor, IEntityService entityService, IAuthService authService, PermissionValidator permissionValidator)
        {
            Name = "serviceItemAgreedFee";

            Field(f => f.Id);
            Field(f => f.Rate);
            Field(f => f.Currency);
            Field(f => f.ServiceItem, type: typeof(ServiceItemGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.ServiceItem?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var dataLoader = accessor.Context.GetOrAddBatchLoader<string, ServiceItem>("GetServiceItems",
                        async i => (await entityService.GetServiceItemsAsync(tenantId, new ServiceItemWhere { Id_in = i?.ToList() })).ToDictionary(x => x.Id));

                    ServiceItem serviceItem = await dataLoader.LoadAsync(context.Source.ServiceItem.Id);

                    return serviceItem;
                });

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }


    public class ServiceItemAgreedFeesGraph
    {
        public int TotalCount { get; set; }
        public IEnumerable<ServiceItemAgreedFeeGraph> List { get; set; }
    }

    public sealed class ServiceItemAgreedFeesGraphType : ObjectGraphType<ServiceItemAgreedFeesGraph>
    {
        public ServiceItemAgreedFeesGraphType()
        {
            Name = "serviceItemAgreedFees";
            Field(f => f.TotalCount, nullable: true);
            Field(f => f.List, type: typeof(ListGraphType<ServiceItemAgreedFeeGraphType>), nullable: true);
        }
    }

    public sealed class ServiceItemAgreedFeeWhereInputGraphType : AutoInputObjectGraphType<ServiceItemAgreedFeeWhere>
    {
    }

    public class UpdateServiceItemAgreedFeeInput
    {
        [JsonRequired]
        public string Id { get; set; }
        public decimal Rate { get; set; }
        public string Currency { get; set; }
        public string ModifiedById { get; set; }
        public string ServiceItemId { get; set; }
        public bool IsRateChanged { get; set; }
        public bool IsCurrencyChanged { get; set; }
        public bool IsServiceItemIdChanged { get; set; }
    }

    public sealed class UpdateServiceItemAgreedFeeInputGraphType : InputObjectGraphType<UpdateServiceItemAgreedFeeInput>
    {
        public UpdateServiceItemAgreedFeeInputGraphType()
        {
            Name = "updateServiceItemAgreedFeeInput";
            Field(f => f.Id);
            Field(f => f.Rate, nullable: true);
            Field(f => f.Currency, nullable: true);
            Field(f => f.ServiceItemId, nullable: true);
            Field(f => f.IsRateChanged, nullable: true);
            Field(f => f.IsCurrencyChanged, nullable: true);
            Field(f => f.IsServiceItemIdChanged, nullable: true);
        }
    }
}