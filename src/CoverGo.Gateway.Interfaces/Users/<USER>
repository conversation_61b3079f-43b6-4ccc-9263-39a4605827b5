using CoverGo.Gateway.Domain.Achievements;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Education;
using CoverGo.Gateway.Domain.Notifications;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Auth;
using CoverGo.Gateway.Interfaces.Notifications;
using CoverGo.Users.Domain.Individuals;
using GraphQL.DataLoader;
using GraphQL.Types;
using System;
using System.Collections.Generic;
using System.Linq;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Common;
using CoverGo.Gateway.Domain.Users.Kyc;
using CoverGo.Gateway.Interfaces.Users.Kyc;
using Range = CoverGo.Users.Domain.Individuals.Range;
using CoverGo.FeatureManagement;

namespace CoverGo.Gateway.Interfaces.Users
{
    public class IndividualsGraphType : ObjectGraphType<IndividualsGraph>
    {
        public IndividualsGraphType()
        {
            Name = "individuals";

            Field(c => c.TotalCount);
            Field<ListGraphType<IndividualGraphType>>("list", resolve: context => context.Source.List);
        }
    }

    public class IndividualsAgentCodeGraphType : ObjectGraphType<IndividualsGraph>
    {
        public IndividualsAgentCodeGraphType()
        {
            Name = "individualsAgentCode";

            Field(c => c.TotalCount);
            Field<ListGraphType<IndividualAgentCodeGraphType>>("list", resolve: context => context.Source.List);
        }
    }

    public class IndividualsGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<IndividualGraph> List { get; set; }
    }

    public class IndividualGraph : CustomerGraph
    {
        public string Salutation { get; set; }

        public string EnglishFirstName { get; set; }
        public string EnglishLastName { get; set; }
        public string ChineseFirstName { get; set; }
        public string ChineseLastName { get; set; }
        public string OtherName { get; set; }

        public DateTime? DateOfBirth { get; set; }
        public string Gender { get; set; }
        public string CountryOfResidency { get; set; }

        public string EmploymentStatus { get; set; }
        public string Occupation { get; set; }
        public string MaritalStatus { get; set; }
        public string PreferredCommunicationChannel { get; set; }
        public string PreferredLanguage { get; set; }

        public Range IncomeRange { get; set; }
        public IndividualTypes Type { get; set; }
        public IEnumerable<DisabilityGraph> Disabilities { get; set; }
        public bool? HasActivePolicy { get; set; }
        public KycGraph Kyc { get; set; }
        public IEnumerable<IntegrationGraph> Integrations { get; set; }
    }


    public class IndividualGraphType : ObjectGraphType<IndividualGraph>
    {
        public IndividualGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IPolicyService policyService,
            ICaseService caseService,
            IEntityService userService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            INotificationService notificationService,
            ITransactionService transactionService,
            IEducationService educationService,
            IAchievementService achievementService,
            CoverGoDisabilityService disabilityService,
            PermissionValidator permissionValidator)
        {
            Name = "individual";
            Description = "An individual";

            Field(c => c.EnglishFirstName, nullable: true);
            Field(c => c.EnglishLastName, nullable: true);
            Field(c => c.ChineseFirstName, nullable: true);
            Field(c => c.ChineseLastName, nullable: true);
            Field(c => c.DateOfBirth, nullable: true);
            Field(c => c.Gender, nullable: true);
            Field(c => c.Occupation, nullable: true);
            Field(c => c.Email, nullable: true);
            Field(c => c.TelephoneNumber, nullable: true);
            Field(c => c.MaritalStatus, nullable: true);
            Field(c => c.EmploymentStatus, nullable: true);
            Field(c => c.Salutation, nullable: true);
            Field(c => c.PreferredCommunicationChannel, nullable: true);
            Field(c => c.PreferredLanguage, nullable: true);
            Field(c => c.CountryOfResidency, nullable: true);
            Field(c => c.HasActivePolicy, nullable: true);
            Field(c => c.IncomeRange, type: typeof(RangeGraphType), nullable: true);
            Field(c => c.Type, type: typeof(IndividualTypeEnumerationGraphType));
            Field(c => c.Disabilities, type: typeof(ListGraphType<DisabilityGraphType>), nullable: true)
                .GetPaginationArguments()
                .Argument<DisabilityFilterInputGraph>("filter", "A disability search filter")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "readIndividualDisabilities");

                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(
                            context, "readIndividualDisabilities");


                    Filter<DisabilityFilter> filter =  context.ComputeArgAndVar<Filter<DisabilityFilter>, IndividualGraph>("filter") ?? new Filter<DisabilityFilter>();
                    filter.And ??= new List<Filter<DisabilityFilter>>();

                    if (!allowedIds.Contains("all"))
                    {
                        filter.And.Add(new Filter<DisabilityFilter>
                        {
                            Where = new DisabilityFilter { Id_in = allowedIds.ToList() }
                        });
                    }

                    int? skip = context.ComputeArgAndVar<int?, IndividualGraph>("skip");
                    int? first = context.ComputeArgAndVar<int?, IndividualGraph>("limit");
                    SortGraph sort = context.ComputeArgAndVar<SortGraph, IndividualGraph>("sort");

                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    
                    var disabilitiesLoader = accessor.Context.GetOrAddBatchLoader<string, Disability>("GetDisabilitiesByIds",
                        async x =>
                        {
                            Filter<DisabilityFilter> clonedFilter = filter.DeepClone();
                            clonedFilter.And.Add(new Filter<DisabilityFilter>
                            {
                                Where = new DisabilityFilter {Id_in = x.ToList()}
                            });
                            
                            DisabilityWhere query = new()
                            {
                                Skip = skip ?? 0,
                                First = first ?? 25,
                                OrderBy = orderBy,
                                Where = clonedFilter
                            };

                            IReadOnlyCollection<Disability> disabilities = await disabilityService.QueryAsync(tenantId, query);

                            return disabilities.ToDictionary(d => d.Id, d => d);
                        });

                    Disability[] loadedDisabilities =  await disabilitiesLoader.LoadAsync(context.Source.Disabilities.Select(x => x.Id));

                    return loadedDisabilities.Select(DisabilityGraph.ToGraph);
                });
            Field(c => c.Kyc, type: typeof(KycGraphType));
            Field(c => c.Integrations, type: typeof(ListGraphType<IntegrationGraphType>));

            this.PopulateCustomerFields(accessor, authService, userService, null, individualService, policyService, caseService, notificationService, transactionService, educationService, achievementService , permissionValidator);

            Interface<CustomerInterfaceGraphType>();
            Interface<EntityInterfaceGraphType>();
        }
    }

    public class IndividualAgentCodeGraphType : ObjectGraphType<IndividualGraph>
    {
        public IndividualAgentCodeGraphType()
        {
            Name = "individualAgentCode";
            Description = "An individuals agent code";

            Field(c => c.EnglishFirstName, nullable: true);
            Field(c => c.EnglishLastName, nullable: true);
            Field(u => u.Source, nullable: true);
            Field(u => u.Fields, nullable: true);
        }
    }

    public class IndividualTypeEnumerationGraphType : EnumerationGraphType<IndividualTypes>
    {
        public IndividualTypeEnumerationGraphType()
        {
            Name = "individualTypeEnumeration";
        }
    }

    public class CreateIndividualInputGraph : CreateEntityInputGraph
    {
        public string Salutation { get; set; }
        public string Gender { get; set; }
        public string EnglishFirstName { get; set; }
        public string EnglishLastName { get; set; }
        public string ChineseFirstName { get; set; }
        public string ChineseLastName { get; set; }
        public DateTime? DateOfBirth { get; set; }

        public string CountryOfResidency { get; set; }
        public string EmploymentStatus { get; set; }
        public string Occupation { get; set; }
        public string MaritalStatus { get; set; }
        public string PreferredCommunicationChannel { get; set; }
        public string PreferredLanguage { get; set; }
        public Range IncomeRange { get; set; }
        public IndividualTypes Type { get; set; }

        public bool? AcceptsMarketing { get; set; }
    }

    public class CreateIndividualInputGraphType : InputObjectGraphType<CreateIndividualInputGraph>
    {
        public CreateIndividualInputGraphType()
        {
            Name = "createIndividualInput";
            Description = "A command to create an individual";

            Field(c => c.EnglishFirstName, nullable: true);
            Field(c => c.EnglishLastName, nullable: true);
            Field(c => c.ChineseFirstName, nullable: true);
            Field(c => c.ChineseLastName, nullable: true);
            Field(c => c.DateOfBirth, nullable: true);
            Field(c => c.Gender, nullable: true);
            Field(c => c.Occupation, nullable: true);
            Field(c => c.MaritalStatus, nullable: true);
            Field(c => c.EmploymentStatus, nullable: true);
            Field(c => c.Salutation, nullable: true);
            Field(c => c.PreferredCommunicationChannel, nullable: true);
            Field(c => c.PreferredLanguage, nullable: true);
            Field(c => c.CountryOfResidency, nullable: true);
            Field(c => c.IncomeRange, type: typeof(RangeInputGraphType), nullable: true);
            Field(c => c.AcceptsMarketing, nullable: true);
            Field(c => c.Type, type: typeof(IndividualTypeEnumerationGraphType), nullable: true);

            this.PopulateCreateEntityFields();
        }
    }

    public class UpdateIndividualInputGraphType : InputObjectGraphType<UpdateIndividualCommand>
    {
        public UpdateIndividualInputGraphType()
        {
            Name = "updateIndividualInput";
            Description = "A command to update an individual";

            Field(c => c.PhotoPath, nullable: true);
            Field(c => c.NameFormat, nullable: true);
            Field(c => c.EnglishFirstName, nullable: true);
            Field(c => c.EnglishLastName, nullable: true);
            Field(c => c.ChineseFirstName, nullable: true);
            Field(c => c.ChineseLastName, nullable: true);
            Field(c => c.DateOfBirth, nullable: true);
            Field(c => c.Gender, nullable: true);
            Field(c => c.Occupation, nullable: true);
            Field(c => c.MaritalStatus, nullable: true);
            Field(c => c.EmploymentStatus, nullable: true);
            Field(c => c.Salutation, nullable: true);
            Field(c => c.PreferredCommunicationChannel, nullable: true);
            Field(c => c.PreferredLanguage, nullable: true);
            Field(c => c.CountryOfResidency, nullable: true);
            Field(c => c.IncomeRange, type: typeof(RangeInputGraphType), nullable: true);
            Field(c => c.InternalCode, nullable: true);
            Field(c => c.AcceptsMarketing, nullable: true);
            Field(c => c.Source, nullable: true);
            Field(c => c.Type, type: typeof(IndividualTypeEnumerationGraphType), nullable: true);
            Field(c => c.Fields, nullable: true);
            Field(c => c.FieldsPatch, nullable: true);
            Field(c => c.Tags, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            Field(c => c.Status, nullable: true);
        }
    }

    public class UpdateContractIndividualInputGraphType : InputObjectGraphType<UpdateIndividualCommand>
    {
        public UpdateContractIndividualInputGraphType()
        {
            Name = "updateContractIndividualInput";
            Description = "A command to update a contract individual";

            Field(c => c.PhotoPath, nullable: true);
            Field(c => c.Id, nullable: true);
            Field(c => c.NameFormat, nullable: true);
            Field(c => c.EnglishFirstName, nullable: true);
            Field(c => c.EnglishLastName, nullable: true);
            Field(c => c.ChineseFirstName, nullable: true);
            Field(c => c.ChineseLastName, nullable: true);
            Field(c => c.DateOfBirth, nullable: true);
            Field(c => c.Gender, nullable: true);
            Field(c => c.Occupation, nullable: true);
            Field(c => c.MaritalStatus, nullable: true);
            Field(c => c.EmploymentStatus, nullable: true);
            Field(c => c.Salutation, nullable: true);
            Field(c => c.PreferredCommunicationChannel, nullable: true);
            Field(c => c.PreferredLanguage, nullable: true);
            Field(c => c.CountryOfResidency, nullable: true);
            Field(c => c.IncomeRange, type: typeof(RangeInputGraphType), nullable: true);
            Field(c => c.InternalCode, nullable: true);
            Field(c => c.AcceptsMarketing, nullable: true);
            Field(c => c.Source, nullable: true);
            Field(c => c.Fields, nullable: true);
            Field(c => c.Tags, type: typeof(ListGraphType<StringGraphType>), nullable: true);
        }
    }

    public class UpdateContractIndividualCommandGraph : UpdateEntityCommandGraph
    {
        public string Salutation { get; set; }
        public bool IsSalutationChanged { get; set; }
        public string Gender { get; set; }
        public bool IsGenderChanged { get; set; }
        public string EnglishFirstName { get; set; }
        public bool IsEnglishFirstNameChanged { get; set; }
        public string EnglishLastName { get; set; }
        public bool IsEnglishLastNameChanged { get; set; }
        public string ChineseFirstName { get; set; }
        public bool IsChineseFirstNameChanged { get; set; }
        public string ChineseLastName { get; set; }
        public bool IsChineseLastNameChanged { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public bool IsDateOfBirthChanged { get; set; }
        public string CountryOfResidency { get; set; }
        public bool IsCountryOfResidencyChanged { get; set; }
        public string EmploymentStatus { get; set; }
        public bool IsEmploymentStatusChanged { get; set; }
        public string Occupation { get; set; }
        public bool IsOccupationChanged { get; set; }
        public string MaritalStatus { get; set; }
        public bool IsMaritalStatusChanged { get; set; }
        public string PreferredCommunicationChannel { get; set; }
        public bool IsPreferredCommunicationChannelChanged { get; set; }
        public string PreferredLanguage { get; set; }
        public bool IsPreferredLanguageChanged { get; set; }
        public Range IncomeRange { get; set; }
        public bool IsIncomeRangeChanged { get; set; }
        public bool? AcceptsMarketing { get; set; }
        public bool IsAcceptsMarketingChanged { get; set; }
        public IndividualTypes Type { get; set; }
        public bool IsTypeChanged { get; set; }

        public static UpdateContractIndividualCommandGraph ToGraph(UpdateIndividualCommand command)
        {
            var graph = new UpdateContractIndividualCommandGraph
            {
                Salutation = command.Salutation,
                IsSalutationChanged = command.IsSalutationChanged,
                Gender = command.Gender,
                IsGenderChanged = command.IsGenderChanged,
                EnglishFirstName = command.EnglishFirstName,
                IsEnglishFirstNameChanged = command.IsEnglishFirstNameChanged,
                EnglishLastName = command.EnglishLastName,
                IsEnglishLastNameChanged = command.IsEnglishLastNameChanged,
                ChineseFirstName = command.ChineseFirstName,
                IsChineseFirstNameChanged = command.IsChineseFirstNameChanged,
                ChineseLastName = command.ChineseLastName,
                IsChineseLastNameChanged = command.IsChineseLastNameChanged,
                DateOfBirth = command.DateOfBirth,
                IsDateOfBirthChanged = command.IsDateOfBirthChanged,
                CountryOfResidency = command.CountryOfResidency,
                IsCountryOfResidencyChanged = command.IsCountryOfResidencyChanged,
                EmploymentStatus = command.EmploymentStatus,
                IsEmploymentStatusChanged = command.IsEmploymentStatusChanged,
                Occupation = command.Occupation,
                IsOccupationChanged = command.IsOccupationChanged,
                MaritalStatus = command.MaritalStatus,
                IsMaritalStatusChanged = command.IsMaritalStatusChanged,
                PreferredCommunicationChannel = command.PreferredCommunicationChannel,
                IsPreferredCommunicationChannelChanged = command.IsPreferredCommunicationChannelChanged,
                PreferredLanguage = command.PreferredLanguage,
                IsPreferredLanguageChanged = command.IsPreferredLanguageChanged,
                IncomeRange = command.IncomeRange,
                IsIncomeRangeChanged = command.IsIncomeRangeChanged,
                AcceptsMarketing = command.AcceptsMarketing,
                IsAcceptsMarketingChanged = command.IsAcceptsMarketingChanged,
                Type = command.Type,
                IsTypeChanged = command.IsTypeChanged
            };

            PopulateUpdateEntityCommandFields(graph, command);

            return graph;
        }
    }

    public class UpdateContractIndividualCommandGraphType : ObjectGraphType<UpdateContractIndividualCommandGraph>
    {
        public UpdateContractIndividualCommandGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "updateContractIndividualCommand";
            Description = "A command to update an individual";

            Field(c => c.CommandId, nullable: true);
            Field(p => p.Timestamp, type: typeof(DateTimeGraphType));
            Field(c => c.PhotoPath, nullable: true);
            Field(c => c.IsPhotoPathChanged);
            Field(c => c.Id, nullable: true);
            Field(c => c.IsIdChanged, nullable: true);
            Field(c => c.NameFormat, nullable: true);
            Field(c => c.IsNameFormatChanged);
            Field(c => c.EnglishFirstName, nullable: true);
            Field(c => c.IsEnglishFirstNameChanged);
            Field(c => c.EnglishLastName, nullable: true);
            Field(c => c.IsEnglishLastNameChanged);
            Field(c => c.ChineseFirstName, nullable: true);
            Field(c => c.IsChineseFirstNameChanged);
            Field(c => c.ChineseLastName, nullable: true);
            Field(c => c.IsChineseLastNameChanged);
            Field(c => c.DateOfBirth, nullable: true);
            Field(c => c.IsDateOfBirthChanged);
            Field(c => c.Gender, nullable: true);
            Field(c => c.IsGenderChanged);
            Field(c => c.Occupation, nullable: true);
            Field(c => c.IsOccupationChanged);
            Field(c => c.MaritalStatus, nullable: true);
            Field(c => c.IsMaritalStatusChanged);
            Field(c => c.EmploymentStatus, nullable: true);
            Field(c => c.IsEmploymentStatusChanged);
            Field(c => c.Salutation, nullable: true);
            Field(c => c.IsSalutationChanged);
            Field(c => c.PreferredCommunicationChannel, nullable: true);
            Field(c => c.IsPreferredCommunicationChannelChanged);
            Field(c => c.PreferredLanguage, nullable: true);
            Field(c => c.IsPreferredLanguageChanged);
            Field(c => c.CountryOfResidency, nullable: true);
            Field(c => c.IsCountryOfResidencyChanged);
            Field(c => c.IncomeRange, type: typeof(RangeGraphType), nullable: true);
            Field(c => c.IsIncomeRangeChanged);
            Field(c => c.InternalCode, nullable: true);
            Field(c => c.IsInternalCodeChanged);
            Field(c => c.AcceptsMarketing, nullable: true);
            Field(c => c.IsAcceptsMarketingChanged);
            Field(c => c.Source, nullable: true);
            Field(c => c.IsSourceChanged);
            Field(c => c.Type, type: typeof(IndividualTypeEnumerationGraphType), nullable: true);
            Field(c => c.IsTypeChanged);
            Field(c => c.ModifiedBy, type: typeof(LoginGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.ModifiedBy?.Id == null)
                        return null;

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readLogins", new List<string> { context.Source.ModifiedBy.Id });
                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.ModifiedBy.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                    i => authService.GetLoginsDictionaryAsync(tenantId, i));

                    Login loginDao = await loginLoader.LoadAsync(context.Source.ModifiedBy.Id);

                    return LoginGraph.ToGraph(loginDao);
                });
        }
    }

    public class HolderOrInsuredInputGraph
    {
        public string Salutation { get; set; }
        public string Gender { get; set; }
        public string Email { get; set; }
        public string TelephoneNumber { get; set; }
        public string EnglishFirstName { get; set; }
        public string EnglishLastName { get; set; }
        public string ChineseFirstName { get; set; }
        public string ChineseLastName { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string IdentityType { get; set; }
        public string IdentityNumber { get; set; }
        public string Occupation { get; set; }
        public string RelationshipToHolder { get; set; }
        public IEnumerable<AddressInputGraph> Addresses { get; set; }
        public bool? AcceptsMarketing { get; set; }
    }

    public class RegisterIndividualInputGraphType : InputObjectGraphType<RegisterIndividualInputGraph>
    {
        public RegisterIndividualInputGraphType()
        {
            Name = "registerIndividualInput";
            Description = "Register an individual which creates a login and an individual and link them with limited permissions";

            Field(i => i.Email);
            Field(i => i.Username, nullable: true).Description("If null, is equal to the email.");
            Field(i => i.Password, nullable: true).Description("If null, will let this user chose his password");
            Field(i => i.InternalCode, nullable: true).Description("If null, will generate an individual code of 'internalCodeLength'. Default length is 6.");
            Field(i => i.InternalCodeLength, nullable: true).Description("If 'internalCode' is null, will generate an internal code of 'internalCodeLength'. Default length is 6.");
            Field(i => i.Salutation, nullable: true);
            Field(i => i.EnglishFirstName, nullable: true);
            Field(i => i.EnglishLastName, nullable: true);
            Field(i => i.ChineseFirstName, nullable: true);
            Field(i => i.ChineseLastName, nullable: true);
            Field(i => i.NameFormat, nullable: true);
            Field(i => i.TelephoneNumber, nullable: true);
            Field(i => i.IsEmailConfirmed, nullable: true);
        }
    }

    public class InviteIndividualInputGraphType : InputObjectGraphType<InviteIndividualInputGraph>
    {
        public InviteIndividualInputGraphType()
        {
            Name = "inviteIndividualInput";
            Description = "Invite an individual which creates a login and an individual and link them with limited permissions";

            Field(i => i.EntityId);
            Field(i => i.Email);
            Field(i => i.Username, nullable: true).Description("If null, is equal to the email.");
            Field(i => i.Password, nullable: true).Description("If null, will let this user chose his password");
            Field(i => i.InternalCode, nullable: true).Description("If null, will generate an individual code of 'internalCodeLength'. Default length is 6.");
            Field(i => i.InternalCodeLength, nullable: true).Description("If 'internalCode' is null, will generate an internal code of 'internalCodeLength'. Default length is 6.");
            Field(i => i.Salutation, nullable: true);
            Field(i => i.EnglishFirstName, nullable: true);
            Field(i => i.EnglishLastName, nullable: true);
            Field(i => i.ChineseFirstName, nullable: true);
            Field(i => i.ChineseLastName, nullable: true);
            Field(i => i.NameFormat, nullable: true);
            Field(i => i.TelephoneNumber, nullable: true);
        }
    }

    public class InviteEntityInputGraphType : InputObjectGraphType<InviteEntityInputGraph>
    {
        public InviteEntityInputGraphType()
        {
            Name = "inviteEntityInput";
            Description = "Invite an entity by creating a user and associate it to the entity from the specified entityId";

            Field(i => i.EntityId);
            Field(i => i.Email);
            Field(i => i.Username, nullable: true).Description("If null, is equal to the email.");
            Field(i => i.Password, nullable: true).Description("If null, will let this user chose his password");
            Field(i => i.TelephoneNumber, nullable: true);
            Field(c => c.AppIdsToBeGrantedAccessTo, type: typeof(ListGraphType<NonNullGraphType<StringGraphType>>));
            Field(i => i.SendNotification, type: typeof(SendNotificationInputGraphType));
            Field(i => i.RedirectQueryString, nullable: true);
            Field(i => i.IsPasswordValidationDobRequire, nullable: true);
            Field(i => i.UseDefaultPermissions, nullable: true);
            Field(c => c.PermissionGroupIds, type: typeof(ListGraphType<NonNullGraphType<StringGraphType>>));
            Field(i => i.ActiveFrom, type: typeof(DateGraphType), nullable: true);
            Field(i => i.IsSsoUser, nullable: true);
        }
    }

    public class InviteEntityInputGraph
    {
        public string EntityId { get; set; }
        public string Email { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public string TelephoneNumber { get; set; }
        public IEnumerable<string> AppIdsToBeGrantedAccessTo { get; set; }
        public string RedirectQueryString { get; set; }
        public SendNotificationGraph SendNotification { get; set; }
        public bool IsPasswordValidationDobRequire { get; set; }
        public bool? UseDefaultPermissions { get; set; }
        public IEnumerable<string> PermissionGroupIds { get; set; }
        public DateTime? ActiveFrom { get; set; }
        public bool IsSsoUser { get; set; }
    }

    public class RegisterIndividualInputGraph
    {
        public string Username { get; set; }
        public string Password { get; set; }
        public string Email { get; set; }
        public string InternalCode { get; set; }
        public int? InternalCodeLength { get; set; }
        public string Salutation { get; set; }
        public string EnglishFirstName { get; set; }
        public string EnglishLastName { get; set; }
        public string ChineseFirstName { get; set; }
        public string ChineseLastName { get; set; }
        public string NameFormat { get; set; }
        public string TelephoneNumber { get; set; }
        public bool IsEmailConfirmed { get; set; }
    }

    public class InviteIndividualInputGraph : RegisterIndividualInputGraph
    {
        public string EntityId { get; set; }
    }

    public class IndividualDisabilityBatchInput
    {
        public List<string> DisabilityIdsToAdd { get; set; }
        public List<string> DisabilityIdsToRemove { get; set; }
    }

    public class IndividualDisabilityBatchInputGraphType : InputObjectGraphType<IndividualDisabilityBatchInput>
    {
        public IndividualDisabilityBatchInputGraphType()
        {
            Name = "individualDisabilityBatchInput";
            Description = "A batch command to batch disabilities for an individual";

            Field(c => c.DisabilityIdsToAdd, nullable: true, typeof(ListGraphType<StringGraphType>));
            Field(c => c.DisabilityIdsToRemove, nullable: true, typeof(ListGraphType<StringGraphType>));
        }
    }

    public class InternalCodeGenerationStrategyGraphType : EnumerationGraphType<InternalCodeGenerationStrategy>
    {
        public InternalCodeGenerationStrategyGraphType()
        {
            Name = "internalCodeGenerationStrategy";
            Description = "entity internal code generation strategy";
        }
    }
}
