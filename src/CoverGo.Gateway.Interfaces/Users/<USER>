﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Cases;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Auth;
using GraphQL.Types;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Gateway.Interfaces.Users
{
    public class EntityWhereInputGraphType : InputObjectGraphType<EntityWhere>
    {
        public EntityWhereInputGraphType()
        {
            Name = "entityWhereInput";
            Description = "An entity search filter";

            this.PopulateEntityWhereFields();
        }
    }

    public class CompanyWhereInputGraphType : InputObjectGraphType<CompanyWhere>
    {
        public CompanyWhereInputGraphType()
        {
            Name = "companyWhereInput";
            Description = "A company search filter";

            Field(a => a.Or, type: typeof(ListGraphType<CompanyWhereInputGraphType>));
            Field(a => a.And, type: typeof(ListGraphType<CompanyWhereInputGraphType>));
            Field(a => a.Fields_path, nullable: true);
            Field(a => a.Fields_regex, nullable: true);
            Field(a => a.Name_regex, nullable: true);
            Field(a => a.RegistrationNumber, nullable: true);
            Field(a => a.RegistrationNumber_in, nullable: true);
            Field(a => a.RegistrationNumber_contains, nullable: true);
            Field(a => a.NatureOfBusiness, nullable: true);
            Field(a => a.NatureOfBusiness_in, nullable: true);
            Field(a => a.NatureOfBusiness_contains, nullable: true);

            Field(a => a.Type, type: typeof(CompanyTypeEnumerationGraphType), nullable: true);
            Field(a => a.Type_in, type: typeof(ListGraphType<CompanyTypeEnumerationGraphType>), nullable: true);

            Field(a => a.MigrationBatchId, nullable: true);
            Field(a => a.MigrationBatchId_in, nullable: true);
            Field(a => a.HasMigrationBatchId, nullable: true);
            Field(a => a.ExternalClientProvider, nullable: true);
            this.PopulateCustomerWhereFields();
        }
    }

    public class IndividualWhereInputGraphType : InputObjectGraphType<IndividualWhere>
    {
        public IndividualWhereInputGraphType()
        {
            Name = "individualWhereInput";
            Description = "An individual search filter";

            Field(a => a.Or, type: typeof(ListGraphType<IndividualWhereInputGraphType>));
            Field(a => a.And, type: typeof(ListGraphType<IndividualWhereInputGraphType>));

            Field(a => a.Type, type: typeof(IndividualTypeEnumerationGraphType), nullable: true);
            Field(a => a.Type_in, type: typeof(ListGraphType<IndividualTypeEnumerationGraphType>), nullable: true);
            Field(a => a.HasActivePolicy, nullable: true);
            Field(a => a.IntegrationSystemId, type: typeof(StringGraphType));
            Field(a => a.IntegrationExternalEntityId_in, type: typeof(ListGraphType<StringGraphType>));
            Field(a => a.HasEnglishFirstNameNotNull, nullable: true);
            Field(a => a.HasEnglishLastNameNotNull, nullable: true);
            Field(a => a.Product, type: typeof(ProductWhereInputGraphType), nullable: true);
            Field(a => a.AccessPolicy, type: typeof(AccessPolicyTypeEnumGraphType), nullable: true);
            Field(a => a.Username_contains, nullable: true);
            this.PopulateCustomerWhereFields();
        }
    }

    public class InternalWhereInputGraphType : InputObjectGraphType<InternalWhere>
    {
        public InternalWhereInputGraphType()
        {
            Name = "internalWhereInput";
            Description = "An internal search filter";

            Field(a => a.Or, type: typeof(ListGraphType<InternalWhereInputGraphType>));
            Field(a => a.And, type: typeof(ListGraphType<InternalWhereInputGraphType>));

            Field(a => a.Fields_path, nullable: true);
            Field(a => a.Fields_regex, nullable: true);
            Field(a => a.IsActive, nullable: true);
            Field(a => a.Title, nullable: true);
            Field(a => a.Title_in, nullable: true);
            Field(a => a.Title_contains, nullable: true);
            Field(a => a.Name_regex, nullable: true);

            this.PopulateEntityWhereFields();
        }
    }

    public class OrganizationWhereInputGraphType : InputObjectGraphType<OrganizationWhere>
    {
        public OrganizationWhereInputGraphType()
        {
            Name = "organizationWhereInput";
            Description = "An organization search filter";

            Field(a => a.Or, type: typeof(ListGraphType<OrganizationWhereInputGraphType>));
            Field(a => a.And, type: typeof(ListGraphType<OrganizationWhereInputGraphType>));

            Field(a => a.Type, nullable: true);
            Field(a => a.IsActive, nullable: true);

            this.PopulateEntityWhereFields();
        }
    }

    public class ObjectWhereInputGraphType : InputObjectGraphType<ObjectWhere>
    {
        public ObjectWhereInputGraphType()
        {
            Name = "objectWhereInput";
            Description = "An object search filter";

            Field(a => a.Or, type: typeof(ListGraphType<ObjectWhereInputGraphType>));
            Field(a => a.And, type: typeof(ListGraphType<ObjectWhereInputGraphType>));

            this.PopulateEntityWhereFields();
        }
    }

    public static class EntityFilterExtensions
    {
        public static void PopulateCustomerWhereFields<T>(this ComplexGraphType<T> graphType)
            where T : CustomerWhere
        {
            graphType.Field(a => a.AcceptsMarketing, nullable: true);

            graphType.PopulateEntityWhereFields();
        }

        public static void PopulateEntityWhereFields<T>(this ComplexGraphType<T> graphType)
            where T : EntityWhere
        {
            graphType.Field(a => a.Id, nullable: true);
            graphType.Field(a => a.Id_in, nullable: true);
            graphType.Field(a => a.Id_contains, nullable: true);
            graphType.Field(a => a.Name, nullable: true);
            graphType.Field(a => a.Name_in, nullable: true);
            graphType.Field(a => a.Name_contains, nullable: true);
            graphType.Field(a => a.InternalCode, nullable: true);
            graphType.Field(a => a.InternalCode_in, nullable: true);
            graphType.Field(a => a.InternalCode_contains, nullable: true);
            graphType.Field(f => f.Facts_contains, type: typeof(FactWhereInputGraphType));
            graphType.Field(a => a.Email, nullable: true);
            graphType.Field(a => a.Email_in, nullable: true);
            graphType.Field(a => a.Email_contains, nullable: true);
            graphType.Field(a => a.TelephoneNumber, nullable: true);
            graphType.Field(a => a.TelephoneNumber_in, nullable: true);
            graphType.Field(a => a.TelephoneNumber_contains, nullable: true);
            graphType.Field(a => a.Source, nullable: true);
            graphType.Field(a => a.Source_in, nullable: true);
            graphType.Field(a => a.Source_contains, nullable: true);
            graphType.Field(a => a.Source_not_contains, nullable: true);
            graphType.Field(a => a.Identities_contains, type: typeof(IdentityWhereInputGraphType), nullable: true);
            graphType.Field(a => a.Tags_contains, nullable: true);
            graphType.Field(a => a.Tags_exists, nullable: true);
            graphType.Field(a => a.Fields, type: typeof(FieldsWhereInputGraphType), nullable: true);
            graphType.Field(a => a.RelationshipListType, nullable: true);
            graphType.Field(a => a.RelationshipListType_in, nullable: true);
            graphType.Field(a => a.RelationshipList_contains, type: typeof(RelationshipListWhereInputGraphType), nullable: true);
            graphType.Field(a => a.Status, nullable: true);
            graphType.Field(a => a.Status_in, nullable: true);
            graphType.PopulateSystemWhereFields();
        }
    }

    public class RelationshipWhereGraph : Where
    {
        public IEnumerable<RelationshipWhereGraph> Or { get; set; }
        public IEnumerable<RelationshipWhereGraph> And { get; set; }
        public IEnumerable<string> EntityId_in { get; set; } // checks both source and target
        public string Type { get; set; }
        public IEnumerable<string> Type_in { get; set; }
        public ScalarValue Value { get; set; }
        public ScalarValue Value_not { get; set; }
        public static RelationshipWhere ToDomain(RelationshipWhereGraph graph) =>
            graph == null
            ? null :
            new RelationshipWhere
            {
                Or = graph.Or?.Select(o => ToDomain(o)),
                And = graph.And?.Select(a => ToDomain(a)),
                EntityId_in = graph.EntityId_in,
                Type = graph.Type,
                Type_in = graph.Type_in,
                Value = graph.Value != null ? JToken.FromObject(graph.Value?.GetValue()) : null,
                Value_not = graph.Value_not != null ? JToken.FromObject(graph.Value_not?.GetValue()) : null,

                CreatedAt_gt = graph.CreatedAt_gt,
                CreatedAt_lt = graph.CreatedAt_lt,
                LastModifiedAt_gt = graph.LastModifiedAt_gt,
                LastModifiedAt_lt = graph.LastModifiedAt_lt,
                CreatedById = graph.CreatedById,
                CreatedById_contains = graph.CreatedById_contains,
                CreatedById_in = graph.CreatedById_in,
                LastModifiedById = graph.LastModifiedById,
                LastModifiedById_contains = graph.LastModifiedById_contains,
                LastModifiedById_in = graph.LastModifiedById_in,
            };
    }

    public class RelationshipWhereInputGraphType : InputObjectGraphType<RelationshipWhereGraph>
    {
        public RelationshipWhereInputGraphType()
        {
            Name = "relationshipWhereInput";
            Description = "An object search filter";

            Field(a => a.Or, type: typeof(ListGraphType<RelationshipWhereInputGraphType>));
            Field(a => a.And, type: typeof(ListGraphType<RelationshipWhereInputGraphType>));
            Field(a => a.EntityId_in, nullable: true);
            Field(a => a.Type, nullable: true);
            Field(a => a.Type_in, nullable: true);
            Field(a => a.Value, type: typeof(ScalarValueInputGraphType));
            Field(a => a.Value_not, type: typeof(ScalarValueInputGraphType));

            this.PopulateSystemWhereFields();
        }
    }

    public class IdentityWhereInputGraphType : InputObjectGraphType<IdentityWhere>
    {
        public IdentityWhereInputGraphType()
        {
            Name = "identityWhereInput";
            Description = "An identity search filter";

            Field(a => a.Id, nullable: true);
            Field(a => a.Id_in, nullable: true);

            Field(a => a.Type, nullable: true);
            Field(a => a.Type_in, nullable: true);
            Field(a => a.Type_contains, nullable: true);

            Field(a => a.Value, nullable: true);
            Field(a => a.Value_in, nullable: true);
            Field(a => a.Value_contains, nullable: true);
        }
    }
}
