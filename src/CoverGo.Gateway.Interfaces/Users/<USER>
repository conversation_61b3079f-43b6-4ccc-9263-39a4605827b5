using CoverGo.Gateway.Domain.Achievements;
using CoverGo.Gateway.Domain.Achievements.Achievements;
using CoverGo.Gateway.Domain.Advisor;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Education;
using CoverGo.Gateway.Domain.Education.CourseProgressions;
using CoverGo.Gateway.Domain.Notifications;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Achievements.Achievements;
using CoverGo.Gateway.Interfaces.Advisor;
using CoverGo.Gateway.Interfaces.Auth;
using CoverGo.Gateway.Interfaces.Cases;
using CoverGo.Gateway.Interfaces.Education.CourseProgressions;
using CoverGo.Gateway.Interfaces.Notifications;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Transactions;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;
using GraphQL.DataLoader;
using GraphQL.Types;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Common;
using EventLog = CoverGo.Gateway.Domain.EventLog;

namespace CoverGo.Gateway.Interfaces.Users
{
    public class UserEnumerationGraphType : EnumerationGraphType<EntityTypes>
    {
        public UserEnumerationGraphType()
        {
            Name = "userEnumeration";
            Description = "An enumeration of different types of users";
        }
    }

    public class EntityGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string EntityType { get; set; }
        public string InternalCode { get; set; }
        public string PhotoPath { get; set; }

        public IEnumerable<Identity> Identities { get; set; }
        public IEnumerable<Address> Addresses { get; set; }
        public IEnumerable<Contact> Contacts { get; set; }

        public IEnumerable<AttachmentGraph> Attachments { get; set; }
        public IEnumerable<NoteGraph> Notes { get; set; }
        public IEnumerable<RelationshipGraph> Relationships { get; set; }
        public RelationshipsGraph RelationshipList { get; set; }
        public IEnumerable<FactGraph> Facts { get; set; }
        public NotificationsGraph NotificationList { get; set; }
        public IEnumerable<NotificationGraph> Notifications { get; set; }

        public string Email { get; set; }
        public string TelephoneNumber { get; set; }

        public IEnumerable<EventLogGraph> Events { get; set; }

        public IEnumerable<PaymentMethodGraph> PaymentMethods { get; set; }
        public IEnumerable<SubscriptionGraph> Subscriptions { get; set; }
        public IEnumerable<CourseProgressionGraph> CourseProgressions { get; set; }

        public IEnumerable<AchievementGraph> Achievements { get; set; }

        public string Source { get; set; }

        public LoginGraph AssociatedLogin { get; set; }

        public ReviewsGraph Reviews { get; set; }

        public string Fields { get; set; }

        public IEnumerable<string> Tags { get; set; }
        public string Status { get; set; }
        public virtual AccessPolicy? AccessPolicy { get; set; }
    }

    public abstract class CustomerGraph : EntityGraph
    {
        public bool? AcceptsMarketing { get; set; }
        public IEnumerable<PolicyGraph> Policies { get; set; }
        public IEnumerable<PolicyGraph> HolderOf { get; set; }
        public IEnumerable<CaseGraph> CaseHolderOf { get; set; }
        public IEnumerable<PolicyGraph> BeneficiaryOf { get; set; }
        public IEnumerable<PolicyGraph> PayorOf { get; set; }
        public IEnumerable<PolicyGraph> InsuredBy { get; set; }
        public IEnumerable<PolicyGraph> UninsuredBy { get; set; }
        public IEnumerable<StakeholderGraph> Stakeholders { get; set; }
    }

    public class EntityInterfaceGraphType : InterfaceGraphType<EntityGraph>
    {
        public EntityInterfaceGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IEntityService userService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
            IEntityService<CoverGo.Users.Domain.Objects.Object, CreateObjectCommand, UpdateObjectCommand> objectService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            INotificationService notificationService,
            ITransactionService transactionService,
            IEducationService educationService,
            IAchievementService achievementService,
            PermissionValidator permissionValidator)
        {
            Name = "entityInterface";
            Description = "An entity interface. Can be an internal, an individual, a company, an object, or any custom entities";

            this.PopulateEntityFields(accessor, authService, userService, companyService, individualService, internalService, objectService, organizationService, notificationService, transactionService, educationService, achievementService, permissionValidator);
        }
    }

    public abstract class CreateEntityInputGraph
    {
        public string Email { get; set; }
        public string TelephoneNumber { get; set; }

        public string NameFormat { get; set; }
        public string InternalCode { get; set; }
        public int? InternalCodeLength { get; set; }
        public InternalCodeGenerationStrategy InternalCodeGenerationStrategy { get; set; }
        public string InternalCodeFormat { get; set; }
        public string PhotoPath { get; set; }
        public string Source { get; set; }

        public List<AddContactCommand> Contacts { get; set; }
        public List<AddFactInputGraph> Facts { get; set; }
        public List<AddIdentityCommand> Identities { get; set; }
        public List<AddressInputGraph> Addresses { get; set; }

        public string Fields { get; set; }

        public IEnumerable<string> Tags { get; set; }
        public string Status { get; set; }
        public string LinkedTo { get; set; }
    }

    public static class EntityExtensions
    {
        public static void PopulateCreateEntityFields<T>(this ComplexGraphType<T> graphType)
            where T : CreateEntityInputGraph
        {
            graphType.Field(c => c.NameFormat, nullable: true);
            graphType.Field(c => c.PhotoPath, nullable: true);

            graphType.Field(c => c.Source, nullable: true);
            graphType.Field(c => c.InternalCode, nullable: true);
            graphType.Field(c => c.InternalCodeLength, nullable: true);
            graphType.Field(c => c.InternalCodeGenerationStrategy, type: typeof(InternalCodeGenerationStrategyGraphType), nullable: true);
            graphType.Field(c => c.InternalCodeFormat, nullable: true);
            graphType.Field(c => c.LinkedTo, nullable: true);

            graphType.Field(c => c.Email, nullable: true);
            graphType.Field(c => c.TelephoneNumber, type: typeof(StringGraphType));

            graphType.Field(c => c.Addresses, type: typeof(ListGraphType<AddAddressInputGraphType>));
            graphType.Field(c => c.Facts, type: typeof(ListGraphType<AddFactInputGraphType>));
            graphType.Field(c => c.Identities, type: typeof(ListGraphType<AddIdentityInputGraphType>));
            graphType.Field(c => c.Contacts, type: typeof(ListGraphType<AddContactInputGraphType>));

            graphType.Field(c => c.Fields, nullable: true);

            graphType.Field(c => c.Tags, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            graphType.Field(c => c.Status, nullable: true);
        }

        public static void PopulateEntityFields<T>(
            this ComplexGraphType<T> graphType,
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IEntityService userService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
            IEntityService<CoverGo.Users.Domain.Objects.Object, CreateObjectCommand, UpdateObjectCommand> objectService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            INotificationService notificationService,
            ITransactionService transactionService,
            IEducationService educationService,
            IAchievementService achievementService,
            PermissionValidator permissionValidator)
            where T : EntityGraph
        {
            graphType.Field(c => c.Id);
            graphType.Field(c => c.InternalCode, nullable: true);
            graphType.Field(c => c.PhotoPath, nullable: true);
            graphType.Field(c => c.Name, nullable: true);
            graphType.Field(c => c.EntityType, nullable: true);

            graphType.Field(u => u.Identities, type: typeof(ListGraphType<IdentityGraphType>), nullable: true);
            graphType.Field(u => u.Addresses, type: typeof(ListGraphType<AddressGraphType>), nullable: true);
            graphType.Field(u => u.Contacts, type: typeof(ListGraphType<ContactGraphType>), nullable: true);
            graphType.Field(u => u.Facts, type: typeof(ListGraphType<FactGraphType>), nullable: true);
            graphType.Field(u => u.Attachments, type: typeof(ListGraphType<AttachmentGraphType>), nullable: true);
            graphType.Field(u => u.Notes, type: typeof(ListGraphType<NoteGraphType>), nullable: true);

            graphType.Field(u => u.Relationships, type: typeof(ListGraphType<RelationshipGraphType>), nullable: true)
                .Argument<ListGraphType<StringGraphType>>("types", "Filter relationships by type")
                .Argument<RelationshipWhereInputGraphType>("where", "a relationship filter")
                .GetPaginationArguments()
                .ResolveAsync(
                    async context =>
                    {
                        string tenantId = context.GetTenantIdFromToken();
                        RelationshipWhereGraph input = context.GetArgument<RelationshipWhereGraph>("where");
                        RelationshipWhere where = RelationshipWhereGraph.ToDomain(input);

                        int? skip = context.GetArgument<int?>("skip");
                        int? first = context.GetArgument<int?>("limit");
                        SortGraph sort = context.GetArgument<SortGraph>("sort");
                        OrderBy orderBy = null;
                        if (sort != null)
                        {
                            orderBy = sort.ToOrderBy();
                        }

                        var andFilter = new List<RelationshipWhere> { };
                        if (where != null)
                            andFilter.Add(where);

                        var types = context.GetArgument<IEnumerable<string>>("types")?.ToList(); //ToDo: remove once argument is cleaned
                        if (types != null)
                            andFilter.Add(new RelationshipWhere { Type_in = types });

                        var loader = accessor.Context.GetOrAddBatchLoader<string, Relationships>("GetRelationshipsByEntityId",
                        async i =>
                        {
                            List<RelationshipWhere> clonedAndFilter = andFilter.DeepClone();
                            // Tenant specific cheat to avoid breaking other tenants.
                            string[] msigTenants = { "coverHealth_dev", "msig_uat", "msig_dev", "msig_qa", "msig_prod", "covergo" };
                            if (msigTenants.Contains(tenantId))
                            {
                                List<string> allowedTargetIds = await GetAllowedToReadEntityIds(permissionValidator, context);
                                List<string> allowedAllTargetEntityTypes = await GetAllowedAllToEntityTypes(permissionValidator, context);

                                clonedAndFilter.Add(new RelationshipWhere
                                {
                                    Or = new List<RelationshipWhere>
                                    {
                                        new() { Link = new LinkFilter{ TargetId_in = allowedTargetIds, TypeIn = types } },
                                        new() { Link = new LinkFilter{ TargetEntityTypes_in = allowedAllTargetEntityTypes, TypeIn = types } },
                                    }
                                });
                                clonedAndFilter.Add(new RelationshipWhere
                                {
                                    Or = new List<RelationshipWhere>
                                    {
                                        new() { Link = new LinkFilter{ SourceId_in = allowedTargetIds, TypeIn = types } },
                                        new() { Link = new LinkFilter{ SourceEntityTypes_in = allowedAllTargetEntityTypes, TypeIn = types } },
                                    }
                                });
                            }

                            clonedAndFilter.Add(new RelationshipWhere { EntityId_in = i?.ToList() });
                            IEnumerable<Relationships> relationships = await userService.GetRelationshipsAsync(tenantId, new Domain.QueryArguments
                            {
                                Where = new RelationshipWhere { And = clonedAndFilter },
                                OrderBy = orderBy,
                                First = first,
                                Skip = skip
                            });
                            return relationships.ToDictionary(x => x.EntityId, x => x);
                        });

                        Relationships relationship = await loader.LoadAsync(context.Source.Id);

                        return relationship?.Links?.Select(l => RelationshipGraph.ToGraph(l)) ?? new List<RelationshipGraph> { };
                    });

            graphType.Field(u => u.RelationshipList, type: typeof(RelationshipsGraphType), nullable: true)
                .GetPaginationArguments()
                .Argument<ListGraphType<StringGraphType>>("types", "Filter relationships by type")
                .Argument<RelationshipWhereInputGraphType>("where", "a relationship filter")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    RelationshipWhereGraph input = context.GetArgument<RelationshipWhereGraph>("where");
                    RelationshipWhere where = RelationshipWhereGraph.ToDomain(input);

                    int? skip = context.GetArgument<int?>("skip");
                    int? first = context.GetArgument<int?>("limit");
                    SortGraph sort = context.GetArgument<SortGraph>("sort");
                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var andFilter = new List<RelationshipWhere> { };
                    if (where != null)
                        andFilter.Add(where);

                    var types = context.GetArgument<IEnumerable<string>>("types")?.ToList(); //ToDo: remove once argument is cleaned
                    if (types != null)
                        andFilter.Add(new RelationshipWhere { Type_in = types });

                    var loader = accessor.Context.GetOrAddBatchLoader<string, Relationships>("GetRelationshipsAndTotalCountByEntityId",
                        async i =>
                        {
                            // Tenant specific cheat to avoid breaking other tenants.
                            string[] msigTenants = { "coverHealth_dev", "msig_uat", "msig_dev", "msig_qa", "msig_prod", "covergo" };
                            if (msigTenants.Contains(tenantId))
                            {
                                List<string> allowedTargetIds = await GetAllowedToReadEntityIds(permissionValidator, context);
                                List<string> allowedAllTargetEntityTypes = await GetAllowedAllToEntityTypes(permissionValidator, context);

                                andFilter.Add(new RelationshipWhere
                                {
                                    Or = new List<RelationshipWhere>
                                    {
                                        new() { Link = new LinkFilter{TargetId_in = allowedTargetIds, TypeIn = types} },
                                        new() { Link = new LinkFilter{TargetEntityTypes_in = allowedAllTargetEntityTypes, TypeIn = types} },
                                    }
                                });
                                andFilter.Add(new RelationshipWhere
                                {
                                    Or = new List<RelationshipWhere>
                                    {
                                        new() { Link = new LinkFilter{SourceId_in = allowedTargetIds, TypeIn = types} },
                                        new() { Link = new LinkFilter{SourceEntityTypes_in = allowedAllTargetEntityTypes, TypeIn = types} },
                                    }
                                });
                            }

                            andFilter.Add(new RelationshipWhere { EntityId_in = i?.ToList() });
                            IEnumerable<Relationships> relationships = await userService.GetRelationshipsAsync(tenantId, new Domain.QueryArguments
                            {
                                Where = new RelationshipWhere { And = andFilter },
                                OrderBy = orderBy,
                                First = first,
                                Skip = skip
                            });
                            
                            return relationships.ToDictionary(x => x.EntityId);
                        });

                    Relationships relationships = await loader.LoadAsync(context.Source.Id);
                   
                    context.PassArgumentsToChildren();
                    return new RelationshipsGraph
                    {
                        EntityId = context.Source.Id,
                        List = relationships?.Links?.Select(RelationshipGraph.ToGraph)
                    };
                });

            graphType.Field(u => u.Notifications, type: typeof(ListGraphType<NotificationGraphType>), nullable: true)
                .GetPaginationArguments()
                .Argument<NotificationWhereInputGraphType>("where", "a notification filter")
                .ResolveAsync(async context =>
                    {
                        string tenantId = context.GetTenantIdFromToken();
                        var allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readNotifications");
                        NotificationWhere where = context.GetArgument<NotificationWhere>("where");

                        int? skip = context.GetArgument<int?>("skip");
                        int? first = context.GetArgument<int?>("limit");
                        SortGraph sort = context.GetArgument<SortGraph>("sort");
                        OrderBy orderBy = null;
                        if (sort != null)
                        {
                            orderBy = sort.ToOrderBy();
                        }

                        var andFilter = new List<NotificationWhere> { };
                        if (where != null)
                            andFilter.Add(where);
                        //TODO: add back after adding permission
                        //if (!allowedTargetIds.Contains("all")) 
                        //    andFilter.Add(new NotificationWhere { Id_in = allowedTargetIds?.ToList() });

                        var loader = accessor.Context.GetOrAddCollectionBatchLoader<string, Notification>("GetNotificationsByToEntityId",
                            async i =>
                            {
                                andFilter.Add(new NotificationWhere { ToEntityId_in = i?.ToList() });
                                IEnumerable<Notification> notifs = await notificationService.GetAsync(tenantId, new Domain.QueryArguments
                                {
                                    Where = new NotificationWhere { And = andFilter },
                                    OrderBy = orderBy,
                                    First = first,
                                    Skip = skip
                                });
                                return notifs.ToLookup(x => x.ToEntityId, x => x);
                            });

                        IEnumerable<Notification> notifications = await loader.LoadAsync(context.Source.Id);

                        return notifications?.Select(n => NotificationGraph.ToGraph(n));
                    }
                );

            graphType.Field(u => u.NotificationList, type: typeof(NotificationsGraphType), nullable: true)
                .GetPaginationArguments()
                .Argument<NotificationWhereInputGraphType>("where", "a notification filter")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(
                        context, "readNotifications");

                    NotificationWhere where = context.GetArgument<NotificationWhere>("where");

                    int? skip = context.GetArgument<int?>("skip");
                    int? first = context.GetArgument<int?>("limit");
                    SortGraph sort = context.GetArgument<SortGraph>("sort");
                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var andFilter = new List<NotificationWhere> { };
                    if (where != null)
                        andFilter.Add(where);
                    //TODO: add back after adding permission
                    //if (!allowedTargetIds.Contains("all"))
                    //    andFilter.Add(new NotificationWhere { Id_in = allowedTargetIds?.ToList() });

                    var loader = accessor.Context.GetOrAddCollectionBatchLoader<string, Notification>("GetNotificationsByToEntityId",
                        async i =>
                        {
                            andFilter.Add(new NotificationWhere { ToEntityId_in = i?.ToList() });
                            IEnumerable<Notification> notifs = await notificationService.GetAsync(tenantId, new Domain.QueryArguments
                            {
                                Where = new NotificationWhere { And = andFilter },
                                OrderBy = orderBy,
                                First = first,
                                Skip = skip
                            });
                            return notifs.ToLookup(x => x.ToEntityId, x => x);
                        });

                    IEnumerable<Notification> notifications = await loader.LoadAsync(context.Source.Id);
                    long totalCount = await notificationService.GetTotalCountAsync(tenantId, new NotificationWhere { And = andFilter });

                    return new NotificationsGraph
                    {
                        TotalCount = (int)totalCount,
                        List = notifications?.Select(n => NotificationGraph.ToGraph(n)),
                        IsResolved = true
                    };

                    //context.PassArgumentsToChildren();

                    //return new NotificationsGraph
                    //{
                    //    EntityId = context.Source.Id
                    //};
                });

            graphType.Field(u => u.Source, nullable: true);

            graphType.Field(u => u.PaymentMethods, type: typeof(ListGraphType<PaymentMethodInterfaceGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> paymentMethodIds = await permissionValidator.GetTargetIdsFromClaim(context, "readPaymentMethods");

                    var loader = accessor.Context.GetOrAddCollectionBatchLoader<string, PaymentMethod>("GetPaymentMethodsFromEntityIds",
                        policyIds => transactionService.GetPaymentMethodsByEntityIdsLookupAsync(tenantId, paymentMethodIds.Contains("all")
                            ? new PaymentMethodWhere { EntityId_in = policyIds?.ToList() }
                            : new PaymentMethodWhere
                            {
                                And = new List<PaymentMethodWhere>
                                {
                                new() { EntityId_in = policyIds?.ToList() },
                                new() { Id_in = paymentMethodIds.ToList() }
                                }
                            }));

                    IEnumerable<PaymentMethod> paymentMethods = await loader.LoadAsync(context.Source.Id);

                    return paymentMethods?.Select(p => PaymentMethodGraph.ToGraph(p));
                });

            graphType.Field(u => u.Subscriptions, type: typeof(ListGraphType<SubscriptionGraphType>))
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  IEnumerable<string> paymentMethodIds = await permissionValidator.GetTargetIdsFromClaim(context, "readSubscriptions");

                  var loader = accessor.Context.GetOrAddCollectionBatchLoader<string, Subscription>("GetSubscriptionsFromEntityIds",
                      entityIds => transactionService.GetSubscriptionsByEntityIdsLookupAsync(tenantId, paymentMethodIds.Contains("all")
                          ? new SubscriptionWhere { EntityId_in = entityIds?.ToList() }
                          : new SubscriptionWhere
                          {
                              And = new List<SubscriptionWhere>
                              {
                                new() { EntityId_in = entityIds?.ToList() },
                                new() { Id_in = paymentMethodIds.ToList() }
                              }
                          }));

                  IEnumerable<Subscription> paymentMethods = await loader.LoadAsync(context.Source.Id);

                  return paymentMethods?.Select(p => SubscriptionGraph.ToGraph(p));
              });

            graphType.Field(u => u.CourseProgressions, type: typeof(ListGraphType<CourseProgressionGraphType>))
                .Argument<CourseProgressionWhereInputGraphType>("where", "a course progression filter")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var courseProgressionsIds = await permissionValidator.GetTargetIdsFromClaim(context, "readCourseProgressions");

                    CourseProgressionWhere where = context.GetArgument<CourseProgressionWhere>("where");

                    var andFilter = new List<CourseProgressionWhere> { };
                    if (where != null)
                        andFilter.Add(where);
                    if (!courseProgressionsIds.Contains("all"))
                        andFilter.Add(new CourseProgressionWhere { Id_in = courseProgressionsIds?.ToList() });

                    var loader = accessor.Context.GetOrAddCollectionBatchLoader<string, CourseProgression>("GetCourseProgressionsAsync",
                        policyIds =>
                        {
                            andFilter.Add(new CourseProgressionWhere { EntityId_in = policyIds?.ToList() });
                            return educationService.GetCourseProgressionsByEntityIdsLookupAsync(tenantId, new CourseProgressionWhere { And = andFilter });
                        });

                    IEnumerable<CourseProgression> courseProgressions = await loader.LoadAsync(context.Source.Id);

                    return courseProgressions?.Select(p => CourseProgressionGraph.ToGraph(p));
                });

            graphType.Field(u => u.Achievements, type: typeof(ListGraphType<AchievementGraphType>))
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  var achievementIds = await permissionValidator.GetTargetIdsFromClaim(context, "readAchievements");

                  var loader = accessor.Context.GetOrAddCollectionBatchLoader<string, Achievement>("GetAchievementsFromEntityIds",
                      entityIds => achievementService.GetAchievementsByEntityIdsLookupAsync(tenantId, achievementIds.Contains("all")
                          ? new AchievementWhere { EntityId_in = entityIds?.ToList() }
                          : new AchievementWhere
                          {
                              And = new List<AchievementWhere>
                              {
                                new() { EntityId_in = entityIds?.ToList() },
                                new() { Id_in = achievementIds.ToList() }
                              }
                          }));

                  IEnumerable<Achievement> achievements = await loader.LoadAsync(context.Source.Id);

                  return achievements?.Select(p => AchievementGraph.ToGraph(p));
              });

            graphType.Field(u => u.Events, type: typeof(ListGraphType<EventLogGraphType>)).ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();

                IDataLoader<string, IEnumerable<EventLog>> dataLoader = null;
                if (typeof(T) == typeof(InternalGraph))
                {
                    dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, EventLog>(
                     "GetInternalEvents",
                        async i => (await internalService.GetEventsAsync(tenantId, new EventQuery { Ids = i?.ToList() })).ToLookup(e => e.RelatedId)
                    );
                }
                else if (typeof(T) == typeof(IndividualGraph))
                {
                    dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, EventLog>(
                      "GetIndividualEvents",
                         async i => (await individualService.GetEventsAsync(tenantId, new EventQuery { Ids = i?.ToList() })).ToLookup(e => e.RelatedId)
                     );
                }
                else if (typeof(T) == typeof(CompanyGraph))
                {
                    dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, EventLog>(
                       "GetCompanyEvents",
                          async i => (await companyService.GetEventsAsync(tenantId, new EventQuery { Ids = i?.ToList() })).ToLookup(e => e.RelatedId)
                      );
                }
                else if (typeof(T) == typeof(ObjectGraph))
                {
                    dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, EventLog>(
                     "GetObjectEvents",
                        async i => (await objectService.GetEventsAsync(tenantId, new EventQuery { Ids = i?.ToList() })).ToLookup(e => e.RelatedId)
                    );
                }
                else if (typeof(T) == typeof(OrganizationGraph))
                {
                    dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, EventLog>(
                      "GetOrganizationEvents",
                         async i => (await organizationService.GetEventsAsync(tenantId, new EventQuery { Ids = i?.ToList() })).ToLookup(e => e.RelatedId)
                     );
                }

                IEnumerable<EventLog> logs = await dataLoader.LoadAsync(context.Source.Id);

                return logs.Select(EventLogGraph.ToGraph);
            });

            graphType.Field(u => u.AssociatedLogin, type: typeof(LoginGraphType))
                .ResolveAsync(async context =>
                {
                    var allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readLogins");

                    string tenantId = context.GetTenantIdFromToken();

                    var loader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLoginsByEntityId",
                        async i =>
                        {
                            IEnumerable<Login> logins = await authService.GetLoginsAsync(tenantId, new LoginWhere { Ids = allowedTargetIds.Contains("all") ? null : allowedTargetIds, EntityIds = i, ExcludePermissions = true }); // No need to get login permissions on associated login
                            return logins.ToDictionary(x => x.EntityId, x => x);
                        });

                    Login login = await loader.LoadAsync(context.Source.Id);

                    return LoginGraph.ToGraph(login);
                });

            graphType.Field(u => u.Reviews, type: typeof(ReviewsGraphType))
                .GetPaginationArguments()
                .Argument<ReviewWhereInputGraphType>("where", "a notification filter")
                .Resolve(context =>
                {
                    ReviewWhere whereInput = context.GetArgument<ReviewWhere>("where");
                    int? skip = context.GetArgument<int?>("skip");
                    int? first = context.GetArgument<int?>("limit");
                    SortGraph sort = context.GetArgument<SortGraph>("sort");

                    return new ReviewsGraph { Where = whereInput, Skip = skip, First = first, Sort = sort, RelatedId = context.Source.Id };
                });

            graphType.Field(u => u.Fields, nullable: true)
                .ResolveAsync(context => context.GetPermittedProjection(authService, accessor, context.Source.Fields, context.Source.Id, "entity"));

            graphType.Field(u => u.Tags, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            graphType.Field(u => u.Status, nullable: true);
            graphType.Field(u => u.AccessPolicy, type: typeof(AccessPolicyTypeEnumGraphType), nullable: true);

            graphType.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }

        private static async Task<List<string>> GetAllowedToReadEntityIds<T>(
            PermissionValidator permissionValidator,
            ResolveFieldContext<T> context) where T : EntityGraph
        {
            IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, new[]
            {
                "readIndividuals",
                "readCompanies",
                "readInternals",
                "readObjects",
                "readOrganizations"
            });

            return allowedIds.Where(id => id != "all" && !(id.Contains("{") || id.Contains("}")))
                .Distinct()
                .ToList();
        }

        //TODO: Optimize this
        private static async Task<List<string>> GetAllowedAllToEntityTypes<T>(
            PermissionValidator permissionValidator,
            ResolveFieldContext<T> context) where T : EntityGraph
        {
            Dictionary<string, bool> allowedAllByEntityTypes = new();

            allowedAllByEntityTypes[EntityTypes.Individual.ToString()] =
                (await permissionValidator.GetTargetIdsFromClaim(context, "readIndividuals")).Contains("all");
            allowedAllByEntityTypes[EntityTypes.Company.ToString()] =
                (await permissionValidator.GetTargetIdsFromClaim(context, "readCompanies")).Contains("all");
            allowedAllByEntityTypes[EntityTypes.Internal.ToString()] =
                (await permissionValidator.GetTargetIdsFromClaim(context, "readInternals")).Contains("all");
            allowedAllByEntityTypes[EntityTypes.Object.ToString()] =
                (await permissionValidator.GetTargetIdsFromClaim(context, "readObjects")).Contains("all");
            allowedAllByEntityTypes[EntityTypes.Organization.ToString()] =
                (await permissionValidator.GetTargetIdsFromClaim(context, "readOrganizations")).Contains("all");

            return allowedAllByEntityTypes.Where(kv => kv.Value).Select(kv => kv.Key).ToList();
        }

        public static void PopulateCustomerFields<T>(
            this ComplexGraphType<T> graphType,
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IEntityService userService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IPolicyService policyService,
            ICaseService caseService,
            INotificationService notificationService,
            ITransactionService transactionService,
            IEducationService educationService,
            IAchievementService achievementService,
            PermissionValidator permissionValidator)
            where T : CustomerGraph
        {
            PopulateEntityFields(graphType, accessor, authService, userService, companyService, individualService, null, null, null, notificationService, transactionService, educationService, achievementService, permissionValidator);

            graphType.Field(u => u.HolderOf, type: typeof(ListGraphType<PolicyGraphType>), nullable: true)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    var policyLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, Policy>("GetHolderOf",
                        async i =>
                        {
                            var where = new PolicyWhere { ContractHolder = new EntityWhere { Id_in = i.ToList() } };
                            IEnumerable<string> allowedPolicyIds = await permissionValidator.GetTargetIdsFromClaim(context, "readPolicies");
                            if (!allowedPolicyIds.Contains("all"))
                                where = new PolicyWhere { And = new List<PolicyWhere> { where, new() { Id_in = allowedPolicyIds.ToList() } } };

                            return (await policyService.GetAsync(tenantId, where)).ToLookup(x => x.ContractHolder.Id, x => x);
                        });

                    IEnumerable<Policy> policyDtos = await policyLoader.LoadAsync(context.Source.Id);

                    return policyDtos?.Select(p => PolicyGraph.ToGraph(p));
                });


            graphType.Field(u => u.CaseHolderOf, type: typeof(ListGraphType<CaseGraphType>), nullable: true)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    var caseLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, Case>("GetCaseHolderOf",
                        async i =>
                        {
                            var where = new CaseWhere { HolderId_in = i.ToList() };
                            IEnumerable<string> allowedCaseIds = await permissionValidator.GetTargetIdsFromClaim(context, "readCases");
                            if (!allowedCaseIds.Contains("all"))
                                where = new CaseWhere { And = new List<CaseWhere> { where, new() { Id_in = allowedCaseIds.ToList() } } };

                            return (await caseService.GetAsync(tenantId, new Domain.QueryArguments { Where = where })).ToLookup(x => x.HolderId, x => x);
                        });

                    IEnumerable<Case> caseDtos = await caseLoader.LoadAsync(context.Source.Id);

                    return caseDtos?.Select(p => CaseGraph.ToGraph(p));
                });

            //: Make it work? Refactor?
            graphType.Field(u => u.InsuredBy, type: typeof(ListGraphType<PolicyGraphType>), nullable: true)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    var policyLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, Policy>("GetInsuredBy",
                        i => policyService.GetInsuredsOfLookupAsync(tenantId, i));

                    IEnumerable<Policy> policyDtos = await policyLoader.LoadAsync(context.Source.Id);

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readPolicies", policyDtos.Select(p => p.Id));

                    return policyDtos?
                        .Where(p => allowedTargetIds.Contains(p.Id))
                        .Select(p => PolicyGraph.ToGraph(p));
                });

            graphType.Field(u => u.UninsuredBy, type: typeof(ListGraphType<PolicyGraphType>), nullable: true)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    var policyLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, Policy>("GetUninsuredBy",
                        i => policyService.GetContractTerminatedsOfLookupAsync(tenantId, i));

                    IEnumerable<Policy> policyDtos = await policyLoader.LoadAsync(context.Source.Id);

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readPolicies", policyDtos.Select(p => p.Id));

                    return policyDtos?
                        .Where(p => allowedTargetIds.Contains(p.Id))
                        .Select(p => PolicyGraph.ToGraph(p));
                });

            //: Make it work? Refactor?
            graphType.Field(u => u.BeneficiaryOf, type: typeof(ListGraphType<PolicyGraphType>), nullable: true)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    var policyLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, Policy>("GetBeneficiaryOf",
                        i => policyService.GetBeneficiariesOfLookupAsync(tenantId, i));

                    IEnumerable<Policy> policyDtos = await policyLoader.LoadAsync(context.Source.Id);

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readPolicies", policyDtos.Select(p => p.Id));

                    return policyDtos?
                        .Where(p => allowedTargetIds.Contains(p.Id))
                        .Select(p => PolicyGraph.ToGraph(p));
                });

            graphType.Field(u => u.AcceptsMarketing, nullable: true);

            graphType.Field(u => u.Stakeholders, type: typeof(ListGraphType<StakeholderGraphType>));
        }
    }

    public class InsuredInputGraphType : InputObjectGraphType<HolderOrInsuredInputGraph>
    {
        public InsuredInputGraphType()
        {
            Name = "insuredInput";
            Description = "A command to create a insured person";

            Field(u => u.Salutation, nullable: true);
            Field(c => c.EnglishLastName, nullable: true);
            Field(c => c.EnglishFirstName, nullable: true);
            Field(c => c.ChineseFirstName, nullable: true);
            Field(c => c.ChineseLastName, nullable: true);
            Field(c => c.DateOfBirth, nullable: true);
            Field(c => c.Gender, nullable: true);
            Field(c => c.Email, nullable: true);
            Field(c => c.IdentityType, nullable: true);
            Field(c => c.IdentityNumber, nullable: true);
            Field(c => c.RelationshipToHolder, nullable: true);
        }
    }

    public class HolderInputGraphType : InputObjectGraphType<HolderOrInsuredInputGraph>
    {
        public HolderInputGraphType()
        {
            Name = "holderInput";
            Description = "A command to create a holder";

            Field(u => u.Salutation, nullable: true);
            Field(c => c.EnglishLastName, nullable: true);
            Field(c => c.EnglishFirstName, nullable: true);
            Field(c => c.ChineseFirstName, nullable: true);
            Field(c => c.ChineseLastName, nullable: true);
            Field(c => c.DateOfBirth, nullable: true);
            Field(c => c.Gender, nullable: true);
            Field(c => c.Email, nullable: true);
            Field(c => c.TelephoneNumber, nullable: true);
            Field(c => c.IdentityType, nullable: true);
            Field(c => c.IdentityNumber, nullable: true);
            Field(c => c.Occupation, nullable: true);
            Field(c => c.AcceptsMarketing, nullable: true);

            Field<ListGraphType<AddAddressInputGraphType>>("addresses");
        }
    }

    public class UpdateHolderInputGraphType : InputObjectGraphType<HolderOrInsuredInputGraph>
    {
        public UpdateHolderInputGraphType()
        {
            Name = "updateHolderInput";
            Description = "A command to create a holder";

            Field(u => u.Salutation, nullable: true);
            Field(c => c.EnglishLastName, nullable: true);
            Field(c => c.EnglishFirstName, nullable: true);
            Field(c => c.ChineseFirstName, nullable: true);
            Field(c => c.ChineseLastName, nullable: true);
            Field(c => c.DateOfBirth, nullable: true);
            Field(c => c.Gender, nullable: true);
            //Field(c => c.Email, nullable: true);
            //Field(c => c.TelephoneNumber, nullable: true);
            //Field(c => c.IdentityType, nullable: true); might need to have another API to handle these 2 fields
            //Field(c => c.IdentityNumber, nullable: true);
            Field(c => c.Occupation, nullable: true);
        }
    }

    public class CustomersGraphType : ObjectGraphType<CustomersGraph>
    {
        public CustomersGraphType()
        {
            Name = "customers";
            Description = "Gets all customers (individuals and companies)";

            Field(c => c.TotalCount);
            Field(c => c.List, type: typeof(ListGraphType<CustomerInterfaceGraphType>)).Name("list");
        }
    }

    public class CustomersGraph
    {
        public int TotalCount { get; set; }
        public IEnumerable<CustomerGraph> List { get; set; }
    }

    public class CustomerInterfaceGraphType : InterfaceGraphType<CustomerGraph>
    {
        public CustomerInterfaceGraphType(IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IEntityService userService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            INotificationService notificationService,
            ITransactionService transactionService,
            IEducationService educationService,
            IAchievementService achievementService,
            PermissionValidator permissionValidator)
        {
            Name = "customerInterface";
            Description = "A customer interface. Can be an individual or a company";

            this.PopulateEntityFields(accessor, authService, userService, companyService, individualService, null, null, null, notificationService, transactionService, educationService, achievementService, permissionValidator);
        }
    }

    public class StakeholderGraph
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public EntityGraph Entity { get; set; }

        public static StakeholderGraph ToGraph(Stakeholder stakeholder)
            => new() { Id = stakeholder.Id, Entity = new EntityGraph { Id = stakeholder.EntityId }, Type = stakeholder.Type };
    }

    public class StakeholderGraphType : ObjectGraphType<StakeholderGraph>
    {
        public StakeholderGraphType(
            IDataLoaderContextAccessor accessor,
            IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            PermissionValidator permissionValidator)
        {
            Name = "stakeholder";

            Field(s => s.Id, nullable: true);
            Field(s => s.Type, nullable: false);
            Field(s => s.Entity, type: typeof(EntityInterfaceGraphType))
                    .ResolveAsync(async context =>
                    {
                        if (context.Source.Entity?.Id == null)
                            return null;

                        string tenantId = context.GetTenantIdFromToken();

                        var internalLoader = accessor.Context.GetOrAddBatchLoader<string, Internal>("GetInternals",
                        i => internalService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));
                        var organizationLoader = accessor.Context.GetOrAddBatchLoader<string, Organization>("GetOrganizations",
                        i => organizationService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));
                        var individualLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                        i => individualService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                        var internalTask = Task.FromResult<Internal>(null);
                        var organizationTask = Task.FromResult<Organization>(null);
                        var individualTask = Task.FromResult<Individual>(null);

                        if (await permissionValidator.HasTarget(context, "readInternals", context.Source.Entity.Id)) internalTask = internalLoader.LoadAsync(context.Source.Entity.Id);
                        if (await permissionValidator.HasTarget(context, "readOrganizations", context.Source.Entity.Id)) organizationTask = organizationLoader.LoadAsync(context.Source.Entity.Id);
                        if (await permissionValidator.HasTarget(context, "readIndividuals", context.Source.Entity.Id)) individualTask = individualLoader.LoadAsync(context.Source.Entity.Id);

                        await Task.WhenAll(internalTask, organizationTask, individualTask);

                        return
                        internalTask.Result?.ToGraph() ??
                        organizationTask.Result?.ToGraph() ??
                        individualTask.Result?.ToGraph();
                    });
        }
    }

    public class AddStakeholderInputGraphType : InputObjectGraphType<AddStakeholderCommand>
    {
        public AddStakeholderInputGraphType()
        {
            Name = "addStakeholderInput";

            Field(s => s.EntityId);
            Field(s => s.Type);
        }
    }

    public class UpdateStakeholderInputGraphType : InputObjectGraphType<UpdateStakeholderCommand>
    {
        public UpdateStakeholderInputGraphType()
        {
            Name = "updateStakeholderInput";
            Field(s => s.EntityId);
            Field(s => s.Type);
        }
    }

    public abstract class UpdateEntityCommandGraph
    {
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public string EntityId { get; set; }

        public string Id { get; set; }
        public bool IsIdChanged { get; set; }

        public string NameFormat { get; set; }
        public bool IsNameFormatChanged { get; set; }

        public string InternalCode { get; set; }
        public bool IsInternalCodeChanged { get; set; }

        public string PhotoPath { get; set; }
        public bool IsPhotoPathChanged { get; set; }

        public string Source { get; set; }
        public bool IsSourceChanged { get; set; }

        public string Fields { get; set; }
        public bool IsFieldsChanged { get; set; }
        public string FieldsPatch { get; set; }

        public IEnumerable<string> Tags { get; set; }
        public bool IsTagsChanged { get; set; }

        public LoginGraph ModifiedBy { get; set; }

        public static void PopulateUpdateEntityCommandFields(UpdateEntityCommandGraph graph, UpdateEntityCommand command)
        {
            graph.EntityId = command.EntityId;
            graph.CommandId = command.CommandId;
            graph.Timestamp = command.Timestamp;
            graph.Id = command.Id;
            graph.IsIdChanged = command.IsIdChanged;
            graph.NameFormat = command.NameFormat;
            graph.IsNameFormatChanged = command.IsNameFormatChanged;
            graph.InternalCode = command.InternalCode;
            graph.IsInternalCodeChanged = command.IsInternalCodeChanged;
            graph.PhotoPath = command.PhotoPath;
            graph.IsPhotoPathChanged = command.IsPhotoPathChanged;
            graph.Source = command.Source;
            graph.IsSourceChanged = command.IsSourceChanged;
            graph.Fields = command.Fields;
            graph.IsFieldsChanged = command.IsFieldsChanged;
            graph.Tags = command.Tags;
            graph.IsTagsChanged = command.IsTagsChanged;

            graph.ModifiedBy = new LoginGraph { Id = command.ModifiedById };
        }
    }
}
