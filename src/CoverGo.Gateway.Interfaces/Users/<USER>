using CoverGo.Gateway.Domain.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;
using Newtonsoft.Json;
using System.Linq;
using CoverGo.Gateway.Domain.Users.Kyc;
using System.Collections.Generic;

namespace CoverGo.Gateway.Interfaces.Users
{
    public static class UserExtensions
    {
        public static EntityGraph ToGraph(this Entity dto)
        {
            if (dto == null)
                return null;

            switch (dto)
            {
                case Company compDto:
                    {
                        var comp = new CompanyGraph
                        {
                            NatureOfBusiness = compDto.NatureOfBusiness,
                            RegistrationNumber = compDto.RegistrationNumber,
                            AcceptsMarketing = compDto.AcceptsMarketing,
                            MigrationBatchId = compDto.MigrationBatchId,
                            ExternallySearched = compDto.ExternallySearched
                        };

                        comp.PopulateEntity(compDto);
                        return comp;
                    };
                case Individual custDto:
                    {
                        var cust = new IndividualGraph
                        {
                            OtherName = custDto.OtherName,
                            PreferredCommunicationChannel = custDto.PreferredCommunicationChannel,
                            Salutation = custDto.Salutation,
                            PreferredLanguage = custDto.PreferredLanguage,
                            CountryOfResidency = custDto.CountryOfResidency,
                            EmploymentStatus = custDto.EmploymentStatus,
                            MaritalStatus = custDto.MaritalStatus,
                            ChineseFirstName = custDto.ChineseFirstName,
                            ChineseLastName = custDto.ChineseLastName,
                            EnglishFirstName = custDto.EnglishFirstName,
                            EnglishLastName = custDto.EnglishLastName,
                            DateOfBirth = custDto.DateOfBirth,
                            Gender = custDto.Gender,
                            Occupation = custDto.Occupation,
                            IncomeRange = custDto.IncomeRange,
                            AcceptsMarketing = custDto.AcceptsMarketing,
                            Type = custDto.Type,
                            Disabilities = custDto.DisabilityIds.Select(id => new DisabilityGraph{ Id = id}),
                            Kyc = KycGraph.ToGraph(custDto.Kyc),
                            HasActivePolicy = custDto.HasActivePolicy,
                            Integrations = custDto.Integrations?.Select(i => IntegrationGraph.ToGraph(i)) ?? new List<IntegrationGraph>()
                        };

                        cust.PopulateEntity(custDto);
                        return cust;
                    };
                case Internal internDto:
                    {
                        var intern = new InternalGraph
                        {
                            ChineseFirstName = internDto.ChineseFirstName,
                            ChineseLastName = internDto.ChineseLastName,
                            EnglishFirstName = internDto.EnglishFirstName,
                            EnglishLastName = internDto.EnglishLastName,
                            Description = internDto.Description,
                            Title = internDto.Title,
                            Gender = internDto.Gender,
                            IsActive = internDto.IsActive
                        };

                        intern.PopulateEntity(internDto);
                        return intern;
                    };
                case Object objectDto:
                    {
                        var thing = new ObjectGraph
                        {
                            TypeId = objectDto.TypeId
                        };

                        thing.PopulateEntity(objectDto);
                        return thing;
                    };
                case Organization organizationDto:
                    {
                        var organization = new OrganizationGraph
                        {
                            Type = organizationDto.Type,
                            IsActive = organizationDto.IsActive
                        };
                        organization.PopulateEntity(organizationDto);
                        return organization;
                    }
                default:
                    {
                        var entity = new IndividualGraph { };
                        entity.PopulateEntity(dto);
                        return entity;
                    };
            }
        }

        public static void PopulateEntity(this EntityGraph entity, Entity dto)
        {
            entity.Id = dto.Id;
            entity.InternalCode = dto.InternalCode;
            entity.PhotoPath = dto.PhotoPath;
            entity.Name = dto.Name;
            entity.EntityType = dto.EntityType;
            entity.Addresses = dto.Addresses;
            entity.Contacts = dto.Contacts;
            entity.Identities = dto.Identities;
            entity.Facts = dto.Facts?.Select(f => new FactGraph
            {
                Id = f.Id,
                Type = f.Type,
                Value = f.Value != null ? f.Value?.ToScalarValue() : null,
                Values = f.Value != null ? f.Value?.ToScalarValue() : null //ToDo: remove after migration
            });
            entity.Attachments = dto.Attachments?.Select(a => new AttachmentGraph
            {
                Path = a.Path,
                CreatedAt = a.CreatedAt,
                LastModifiedAt = a.LastModifiedAt,
                LastModifiedBy = new Auth.LoginGraph { Id = a.LastModifiedById },
                CreatedBy = new Auth.LoginGraph { Id = a.CreatedById },
            });
            entity.Notes = dto.Notes?.Select(n => new NoteGraph
            {
                Id = n.Id,
                Title = n.Title,
                Content = n.Content,
                CreatedAt = n.CreatedAt,
                LastModifiedAt = n.LastModifiedAt,
                LastModifiedBy = new Auth.LoginGraph { Id = n.LastModifiedById },
                CreatedBy = new Auth.LoginGraph { Id = n.CreatedById },
            });
            entity.Email = dto.Contacts?.FirstOrDefault(i => i.Type == "email")?.Value;
            entity.TelephoneNumber = dto.Contacts?.FirstOrDefault(i => i.Type == "telephoneNumber")?.Value;
            entity.LastModifiedBy = new Auth.LoginGraph { Id = dto.LastModifiedById };
            entity.CreatedBy = new Auth.LoginGraph { Id = dto.CreatedById };
            entity.LastModifiedAt = dto.LastModifiedAt;
            entity.CreatedAt = dto.CreatedAt;

            entity.Source = dto.Source;
            entity.Fields = dto.Fields?.ToString(Formatting.None);

            entity.Tags = dto.Tags;
            entity.Status = dto.Status;
            entity.AccessPolicy = dto.AccessPolicy;
        }

        public static CreateIndividualCommand ToDomain(this CreateIndividualInputGraph graph, string loginId)
        {
            var command = new CreateIndividualCommand
            {
                AcceptsMarketing = graph.AcceptsMarketing,
                ChineseFirstName = graph.ChineseFirstName,
                ChineseLastName = graph.ChineseLastName,
                CountryOfResidency = graph.CountryOfResidency,
                DateOfBirth = graph.DateOfBirth,
                EmploymentStatus = graph.EmploymentStatus,
                EnglishFirstName = graph.EnglishFirstName,
                EnglishLastName = graph.EnglishLastName,
                Gender = graph.Gender,
                IncomeRange = graph.IncomeRange,
                MaritalStatus = graph.MaritalStatus,
                Occupation = graph.Occupation,
                PreferredCommunicationChannel = graph.PreferredCommunicationChannel,
                PreferredLanguage = graph.PreferredLanguage,
                Salutation = graph.Salutation,
                Type = graph.Type,
            };
            command.PopulateDomain(graph, loginId);

            return command;
        }

        public static CreateObjectCommand ToDomain(this CreateObjectInputGraph graph, string loginId)
        {
            var command = new CreateObjectCommand
            {
                TypeId = graph.TypeId
            };
            command.PopulateDomain(graph, loginId);

            return command;
        }

        public static CreateCompanyCommand ToDomain(this CreateCompanyInputGraph graph, string loginId)
        {
            var command = new CreateCompanyCommand
            {
                NatureOfBusiness = graph.NatureOfBusiness,
                RegistrationNumber = graph.RegistrationNumber,
                Type = graph.Type,
                AcceptsMarketing = graph.AcceptsMarketing,
                AccessPolicy = graph.AccessPolicy
            };
            command.PopulateDomain(graph, loginId);

            return command;
        }

        public static CreateInternalCommand ToDomain(this CreateInternalInputGraph graph, string loginId)
        {
            var command = new CreateInternalCommand
            {
                Title = graph.Title,
                Description = graph.Title,
                EnglishFirstName = graph.EnglishFirstName,
                EnglishLastName = graph.EnglishLastName,
                ChineseFirstName = graph.ChineseFirstName,
                ChineseLastName = graph.ChineseLastName,
                Gender = graph.Gender,
                IsActive = graph.IsActive
            };
            command.PopulateDomain(graph, loginId);

            return command;
        }

        public static void PopulateDomain(this CreateEntityCommand command, CreateEntityInputGraph graph, string loginId)
        {
            command.CreatedById = loginId;
            command.InternalCode = graph.InternalCode;
            command.InternalCodeLength = graph.InternalCodeLength;
            command.InternalCodeGenerationStrategy = graph.InternalCodeGenerationStrategy;
            command.InternalCodeFormat = graph.InternalCodeFormat;
            command.LinkedTo = graph.LinkedTo;
            command.NameFormat = graph.NameFormat;
            command.PhotoPath = graph.PhotoPath;
            command.Source = graph.Source;
            command.Fields = graph.Fields;
            command.Tags = graph.Tags;
            command.Status = graph.Status;
        }
    }
}
