using System;
using System.Collections.Generic;
using System.Linq;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Users;
using GraphQL.DataLoader;
using GraphQL.Types;
using Newtonsoft.Json;


namespace CoverGo.Gateway.Interfaces.Users
{
    public class PanelProviderTiersGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<PanelProviderTierGraph> List { get; set; }
    }

    public class PanelProviderTiersGraphType : ObjectGraphType<PanelProviderTiersGraph>
    {
        public PanelProviderTiersGraphType(IEntityService entityService, PermissionValidator permissionValidator)
        {
            Name = "panelProviderTiers";
            Description = "Gets all panel provider tiers";

            Field(c => c.TotalCount)
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();

                   var allowedIds = (await permissionValidator.GetTargetIdsFromClaim(context, "readPanelProviderTiers")).ToList();
                   PanelProviderTierWhere where = allowedIds.Contains("all")
                       ? context.ComputeArgAndVar<PanelProviderTierWhere, PanelProviderTiersGraph>("where") ?? new PanelProviderTierWhere()
                       : new PanelProviderTierWhere
                       {
                           And = new List<PanelProviderTierWhere>
                           {
                                context.ComputeArgAndVar<PanelProviderTierWhere, PanelProviderTiersGraph>("where") ?? new PanelProviderTierWhere(),
                                new() { Id_in = allowedIds }
                           }
                       };

                   return await entityService.GetTotalCountAsync(tenantId, where);
               });

            Field(c => c.List, type: typeof(ListGraphType<PanelProviderTierGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var allowedIds = (await permissionValidator.GetTargetIdsFromClaim(context, "readPanelProviderTiers")).ToList();
                    PanelProviderTierWhere where = allowedIds.Contains("all")
                        ? context.ComputeArgAndVar<PanelProviderTierWhere, PanelProviderTiersGraph>("where") ?? new PanelProviderTierWhere()
                        : new PanelProviderTierWhere
                        {
                            And = new List<PanelProviderTierWhere>
                            {
                                context.ComputeArgAndVar<PanelProviderTierWhere, PanelProviderTiersGraph>("where") ?? new PanelProviderTierWhere(),
                                new() { Id_in = allowedIds.ToList() }
                            }
                        };

                    int? skip = context.ComputeArgAndVar<int?, PanelProviderTiersGraph>("skip");
                    int? first = context.ComputeArgAndVar<int?, PanelProviderTiersGraph>("limit");
                    SortGraph sort = context.ComputeArgAndVar<SortGraph, PanelProviderTiersGraph>("sort");
                    DateTime? asOf = context.ComputeArgAndVar<DateTime?, PanelProviderTiersGraph>("asOf");

                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy, AsOf = asOf };

                    return (await entityService.GetAsync(tenantId, queryArguments)).Select(PanelProviderTierGraph.ToGraph);
                });
        }
    }

    public class PanelProviderTierGraphType : ObjectGraphType<PanelProviderTierGraph>
    {
        public PanelProviderTierGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IEntityService entityService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            PermissionValidator permissionValidator)
        {
            Name = "panelProviderTier";
            Description = "PanelProviderTier";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.Id, type: typeof(IdGraphType));

            Field(c => c.Panel, type: typeof(OrganizationGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.Panel?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    IEnumerable<string> allowedOrganizationIds = await permissionValidator.GetTargetIdsFromClaim(context, "readOrganizations");
                    var orgLoader = accessor.Context.GetOrAddBatchLoader<string, Organization>("GetOrganizations",
                        ids => organizationService.GetDictionaryAsync(tenantId, allowedOrganizationIds.Contains("all")
                            ? new OrganizationWhere { Id_in = ids?.ToList() }
                            : new OrganizationWhere
                            {
                                And = new List<OrganizationWhere>
                                {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedOrganizationIds.ToList() }
                                }
                            }));

                    Organization org = await orgLoader.LoadAsync(context.Source.Panel.Id);
                    return org?.ToGraph() as OrganizationGraph;
                });

            Field(c => c.ServiceItemAgreedFees, type: typeof(ListGraphType<ServiceItemAgreedFeeGraphType>), nullable: true)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context, "readPanelProviderTiers");

                    var dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, IEnumerable<ServiceItemAgreedFee>>(
                        "GetServiceItemAgreedFeesByTierIds",
                        async claimIds =>
                        {
                            var panelProviderTierWhere = allowedIds.Contains("all")
                                ? new PanelProviderTierWhere()
                                {
                                    Id_in = claimIds?.ToList()
                                }
                                : new PanelProviderTierWhere
                                {
                                    And = new List<PanelProviderTierWhere>
                                    {
                                        new() {Id_in = allowedIds.ToList()},
                                        new() {Id_in = claimIds.ToList()}
                                    }
                                };
                            return await entityService.GetServiceItemAgreedFeesByTierIds(tenantId, panelProviderTierWhere);
                        });

                    var serviceItemAgreedFees = await dataLoader.LoadAsync(context.Source.Id);

                    return serviceItemAgreedFees.SelectMany(x => x).Select(ServiceItemAgreedFeeGraph.ToGraph);
                });



            Field(c => c.Name, nullable: true);

            Field(c => c.Fields, nullable: true);

            Field(p => p.Attachments, type: typeof(ListGraphType<AttachmentGraphType>), nullable: true);

            Field(c => c.Description, nullable: true);

            Field(c => c.Events, type: typeof(ListGraphType<EventLogGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    var dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, EventLog>(
                        "GetPanelProviderTierEvents",
                        async i => (await entityService.GetEventsAsync(tenantId, new EventQuery { Ids = i?.ToList() })).ToLookup(e => e.RelatedId)
                    );

                    IEnumerable<EventLog> logs = await dataLoader.LoadAsync(context.Source.Id);

                    return logs.Select(EventLogGraph.ToGraph);
                });
        }
    }

    public class PanelProviderTierGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public EntityGraph Panel { get; set; }
        public IEnumerable<ServiceItemAgreedFeeGraph> ServiceItemAgreedFees { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Fields { get; set; }
        public IEnumerable<AttachmentGraph> Attachments { get; set; }
        public IEnumerable<EventLogGraph> Events { get; set; }

        public static PanelProviderTierGraph ToGraph(PanelProviderTier domain) =>
            domain != null
                ? new PanelProviderTierGraph
                {
                    Id = domain.Id,
                    Panel = domain.PanelId != null ? new EntityGraph {Id = domain.PanelId} : null,
                    Name = domain.Name,
                    Description = domain.Description,
                    ServiceItemAgreedFees = domain.ServiceItemAgreedFeeIds.Select(id => new ServiceItemAgreedFeeGraph(){Id = id}).ToList(),
                    Fields = domain.Fields?.ToString(Formatting.None),
                    Attachments = domain.Attachments?.Select(AttachmentGraph.ToGraph),
                }.PopulateSystemGraphFields(domain)
                : null;
    }

    public class CreatePanelProviderTierInputGraph
    {
        public string PanelId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Fields { get; set; }
        public List<string> ServiceItemAgreedFeeIds { get; set; }
    }

    public class CreatePanelProviderTierInputGraphType : InputObjectGraphType<CreatePanelProviderTierInputGraph>
    {
        public CreatePanelProviderTierInputGraphType()
        {
            Name = "createPanelProviderTierInput";
            Description = "Create panel provider tier input";

            Field(c => c.PanelId, nullable: true);
            Field(c => c.Name, nullable: true);
            Field(c => c.Description, nullable: true);
            Field(c => c.Fields, nullable: true);
            Field(c => c.ServiceItemAgreedFeeIds, nullable: true);
        }
    }

    public class UpdatePanelProviderTierInputGraph
    {
        public string PanelId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Fields { get; set; }
    }

    public class UpdatePanelProviderTierInputGraphType : InputObjectGraphType<UpdatePanelProviderTierInputGraph>
    {
        public UpdatePanelProviderTierInputGraphType()
        {
            Name = "updatePanelProviderTierInput";
            Description = "Update panel provider tier input";

            Field(c => c.PanelId, nullable: true);
            Field(c => c.Name, nullable: true);
            Field(c => c.Description, nullable: true);
            Field(c => c.Fields, nullable: true);
        }
    }

    public class ServiceItemAgreedFeeBatchInputGraphType : InputObjectGraphType<ServiceItemAgreedFeeBatchCommand>
    {
        public ServiceItemAgreedFeeBatchInputGraphType()
        {
            Name = "serviceItemAgreedFeeBatchInput";
            Description = "Update panel provider tier input";

            Field(c => c.AddIds, nullable: true);
            Field(c => c.RemoveIds, nullable: true);
        }
    }

    public class PanelProviderTierWhereInputGraphType : InputObjectGraphType<PanelProviderTierWhere>
    {
        public PanelProviderTierWhereInputGraphType()
        {
            Name = "panelProviderTierWhereInput";
            Description = "A panel provider tier search filter";

            Field(f => f.Or, type: typeof(ListGraphType<PanelProviderTierWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<PanelProviderTierWhereInputGraphType>));

            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);

            Field(f => f.PanelId, nullable: true);
            Field(f => f.PanelId_in, nullable: true);


            Field(f => f.ServiceItemAgreedFeeId, nullable: true, type:typeof(FieldsWhereInputGraphType));
            Field(f => f.ServiceItemAgreedFeeId_in, nullable: true);

            this.PopulateSystemWhereFields();
        }
    }
}
