﻿using System.Collections.Generic;
using CoverGo.GraphQLGenerators;
using CoverGo.Users.Domain.NegotiatedRate;
using GraphQL.Types;
using Newtonsoft.Json;

namespace CoverGo.Gateway.Interfaces.NegotiatedRate
{
    public class CreateServiceItemInput
    {
        [JsonRequired]
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public sealed class CreateServiceItemInputGraphType : InputObjectGraphType<CreateServiceItemInput>
    {
        public CreateServiceItemInputGraphType()
        {
            Name = "createServiceItemInput";
            Field(f => f.Name);
            Field(f => f.Description, nullable: true);
        }
    }

    public class DeleteServiceItemInput
    {
        [JsonRequired]
        public string ServiceItemId { get; set; }
    }

    public sealed class DeleteServiceItemInputGraphType : AutoInputObjectGraphType<DeleteServiceItemInput>
    {
    }

    public sealed class ServiceItemGraphType : ObjectGraphType<ServiceItem>
    {
        public ServiceItemGraphType()
        {
            Name = "serviceItem";
            Field(f => f.Id);
            Field(f => f.Name);
            Field(f => f.Description, nullable: true);
        }
    }

    public class ServiceItemsGraph
    {
        public int TotalCount { get; set; }
        public IEnumerable<ServiceItem> List { get; set; }
    }

    public sealed class ServiceItemsGraphType : ObjectGraphType<ServiceItemsGraph>
    {
        public ServiceItemsGraphType()
        {
            Name = "serviceItems";
            Field(f => f.TotalCount, nullable: true);
            Field(f => f.List, type: typeof(ListGraphType<ServiceItemGraphType>), nullable: true);
        }
    }

    public sealed class ServiceItemWhereInputGraphType : AutoInputObjectGraphType<ServiceItemWhere>
    {
    }

    public class UpdateServiceItemInput
    {
        [JsonRequired]
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsNameChanged { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public sealed class UpdateServiceItemInputGraphType : InputObjectGraphType<UpdateServiceItemInput>
    {
        public UpdateServiceItemInputGraphType()
        {
            Name = "updateServiceItemInput";
            Field(f => f.Id);
            Field(f => f.Name, nullable: true);
            Field(f => f.Description, nullable: true);
            Field(f => f.IsDescriptionChanged, nullable: true);
            Field(f => f.IsNameChanged, nullable: true);
        }
    }
}