﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;
using GraphQL.DataLoader;
using GraphQL.Types;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Domain.Context;
using CoverGo.Gateway.Interfaces.Context;

namespace CoverGo.Gateway.Interfaces.Users
{
    public class RelationshipGraphType : ObjectGraphType<RelationshipGraph>
    {
        public RelationshipGraphType(
            IDataLoaderContextAccessor accessor,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IAuthService authService,
            IEntityService<Object, CreateObjectCommand, UpdateObjectCommand> objectService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            PermissionValidator permissionValidator)
        {
            Name = "relationship";
            Description = "An entity's relationship with another entity";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.Id);
            Field(c => c.Type);
            Field(c => c.TargetEntityId, nullable: true);
            Field(c => c.Entity, type: typeof(EntityInterfaceGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.Entity?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var internalLoader = accessor.Context.GetOrAddBatchLoader<string, Internal>("GetInternals",
                        i => internalService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                    var customerLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                        i => individualService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                    var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>("GetCompanies",
                        i => companyService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                    var objectLoader = accessor.Context.GetOrAddBatchLoader<string, Object>("GetObjects",
                        i => objectService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                    var organizationsLoader = accessor.Context.GetOrAddBatchLoader<string, Organization>("GetOrganizations",
                        i => organizationService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                    Task<Internal> internalDtoTask = internalLoader.LoadAsync(context.Source.Entity.Id);
                    Task<Individual> customerDtoTask = customerLoader.LoadAsync(context.Source.Entity.Id);
                    Task<Company> companyDtoTask = companyLoader.LoadAsync(context.Source.Entity.Id);
                    Task<Object> objectDtoTask = objectLoader.LoadAsync(context.Source.Entity.Id);
                    Task<Organization> organizationDtoTask = organizationsLoader.LoadAsync(context.Source.Entity.Id);
                    var entityType = context.Source.Entity?.EntityType;
                    // If the entity type is already known, no need to try to fetch by all entity types
                    if (entityType == nameof(Individual)) 
                    {
                        return (await customerDtoTask)?.ToGraph();
                    }
                    if (entityType == nameof(Internal))
                    {
                        return (await internalDtoTask)?.ToGraph();
                    }
                    if (entityType == nameof(Company))
                    {
                        return (await companyDtoTask)?.ToGraph();
                    }
                    if (entityType == nameof(Object))
                    {
                        return (await objectDtoTask)?.ToGraph();
                    }
                    if (entityType == nameof(Organization))
                    {
                        return (await organizationDtoTask)?.ToGraph();
                    }

                    await Task.WhenAll(internalDtoTask, customerDtoTask, companyDtoTask, objectDtoTask, organizationDtoTask);

                    return
                        internalDtoTask.Result?.ToGraph() ??
                        customerDtoTask.Result?.ToGraph() ??
                        companyDtoTask.Result?.ToGraph() ??
                        objectDtoTask.Result?.ToGraph() ??
                        organizationDtoTask.Result?.ToGraph();
                });

            Field(r => r.Values, type: typeof(ListGraphType<RelationshipValueGraphType>), nullable: true);
            Field(r => r.Value, type: typeof(ScalarValueGraphType));
        }
    }

    public class RelationshipValueGraphType : ObjectGraphType<RelationshipValueGraph>
    {
        public RelationshipValueGraphType()
        {
            Name = "relationshipValue";
            Description = "A relationship value";

            Field(r => r.Key);
            Field(r => r.Value);
        }
    }

    public class RelationshipValueInputGraphType : InputObjectGraphType<RelationshipValueGraph>
    {
        public RelationshipValueInputGraphType()
        {
            Name = "relationshipValueInput";
            Description = "A relationship value";

            Field(r => r.Key);
            Field(r => r.Value);
        }
    }

    public class RelationshipsGraphType : ObjectGraphType<RelationshipsGraph>
    {
        public RelationshipsGraphType(IEntityService userService)
        {
            Name = "relationships";
            Description = "Gets all relationships";

            Field(c => c.TotalCount).ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();
                RelationshipWhereGraph input = context.GetArgument<RelationshipWhereGraph>("where");
                RelationshipWhere where = RelationshipWhereGraph.ToDomain(input);

                List<RelationshipWhere> andFilter = new List<RelationshipWhere> { };
                if (where != null)
                    andFilter.Add(where);

                List<string> types = context.GetArgument<IEnumerable<string>>("types")?.ToList(); //ToDo: remove once argument is cleaned
                if (types != null)
                    andFilter.Add(new RelationshipWhere { Type_in = types });

                andFilter.Add(new RelationshipWhere { EntityId_in = new[] { context.Source.EntityId } });
                
                long totalCount = await userService.GetRelationshipsTotalCountAsync(tenantId, new RelationshipWhere {And = andFilter});
                return (int)totalCount;
            });
            Field(c => c.List, type: typeof(ListGraphType<RelationshipGraphType>)).Name("list");
        }
    }

    public class RelationshipsGraph
    {
        public string EntityId { get; set; }
        public int TotalCount { get; set; }
        public IEnumerable<RelationshipGraph> List { get; set; }
    }

    public class RelationshipGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public string TargetEntityId { get; set; }
        public EntityGraph Entity { get; set; }
        public IEnumerable<RelationshipValueGraph> Values { get; set; }
        public ScalarValue Value { get; set; }

        public static RelationshipGraph ToGraph(Link l) =>
            l == null
                ? null
                : new RelationshipGraph
                {
                    Id = l.Id,
                    Type = l.Type,
                    TargetEntityId = l.TargetId,
                    Entity = l.TargetId != null ? new EntityGraph { Id = l.TargetId, EntityType = l.TargetEntityType } : null,
                    Values = l.Value?.ToObjectOrDefault<Dictionary<string, object>>()?.Select(v =>
                        new RelationshipValueGraph
                        {
                            Key = v.Key,
                            Value = v.Value.ToString()
                        }),
                    Value = l.Value?.ToScalarValue()
                }.PopulateSystemGraphFields(l);
    }

    public class RelationshipValueGraph
    {
        public string Key { get; set; }
        public string Value { get; set; }
    }

    public class AddLinkInputGraphType : InputObjectGraphType<AddLinkCommandInputGraph>
    {
        public AddLinkInputGraphType()
        {
            Name = "createLinkInput";
            Description = "A command to create a link between entities";

            Field(c => c.SourceId);
            Field(c => c.Link);
            Field(c => c.TargetId);
            Field(c => c.Values, type: typeof(ListGraphType<RelationshipValueInputGraphType>), nullable: true);
            Field(c => c.Value, type: typeof(ScalarValueInputGraphType), nullable: true);
        }
    }

    public class AddLinkCommandInputGraph
    {
        public string SourceId { get; set; }
        public string Link { get; set; }
        public string TargetId { get; set; }
        public IEnumerable<RelationshipValueGraph> Values { get; set; }
        public ScalarValue Value { get; set; }
    }

    public class RemoveLinkInputGraphType : InputObjectGraphType<RemoveLinkCommand>
    {
        public RemoveLinkInputGraphType()
        {
            Name = "removeLinkInput";
            Description = "A command to remove a link between entities";

            Field(c => c.SourceId);
            Field(c => c.Link);
            Field(c => c.TargetId);
        }
    }
}
