using System.Net.Http;
using CoverGo.Applications.Clients;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Users;

namespace CoverGo.Gateway.Interfaces.Users
{
    public class CoverGoDiagnosisService: CoverGoGenericGenericServiceRestClientBase<Diagnosis,
        CreateDiagnosisCommand, UpdateDiagnosisCommand, RemoveCommand, DiagnosisBatchCommand, DiagnosisWhere,
        DiagnosisFilter>
    {
        private readonly HttpClient _client;

        public CoverGoDiagnosisService(HttpClient client) : base(client)
        {
            _client = client;
        }

        protected override string ApiBaseUrlBuildInternal(string tenantId) => 
            $"{tenantId}/api/v1/diagnoses";
    }
}