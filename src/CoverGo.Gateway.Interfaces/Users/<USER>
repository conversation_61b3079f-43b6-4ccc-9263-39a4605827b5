using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Users;
using CoverGo.GraphQLGenerators;
using GraphQL.Types;
using Newtonsoft.Json;

namespace CoverGo.Gateway.Interfaces.Users
{
     public class DiagnosesGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<DiagnosisGraph> List { get; set; }
    }
    
    public class DiagnosesGraphType : ObjectGraphType<DiagnosesGraph>
    {
        public DiagnosesGraphType(
            CoverGoDiagnosisService diagnosisService,
            PermissionValidator permissionValidator)
        {
            Name = "diagnoses";
            
            Field(c => c.TotalCount)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var where = await FilterFromContextBuildInternal(permissionValidator, context);
                    return await diagnosisService.CountAsync(tenantId, where);
                });

            Field(c => c.List, type: typeof(ListGraphType<DiagnosisGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var where = await FilterFromContextBuildInternal(permissionValidator, context);
                    IReadOnlyCollection<Diagnosis> diagnoses = await diagnosisService.QueryAsync(tenantId, where);
                    return diagnoses.Select(DiagnosisGraph.ToGraph);
                });
        }

        private async Task<DiagnosisWhere> FilterFromContextBuildInternal(
            PermissionValidator permissionValidator,
            ResolveFieldContext<DiagnosesGraph> context)
        {
            var allowedIds = (await permissionValidator.GetTargetIdsFromClaim(context,"readDiagnoses")).ToList();
                   
            Filter<DiagnosisFilter> filter =  context.ComputeArgAndVar<Filter<DiagnosisFilter>, DiagnosesGraph>("where") ?? new Filter<DiagnosisFilter>();
            filter.And ??= new List<Filter<DiagnosisFilter>>();
                    
            if (!allowedIds.Contains("all"))
            {
                filter.And.Add(new Filter<DiagnosisFilter>
                {
                    Where = new DiagnosisFilter { Id_in = allowedIds.ToList() }
                });
            }
                   
            int? skip = context.ComputeArgAndVar<int?, DiagnosesGraph>("skip");
            int? first = context.ComputeArgAndVar<int?, DiagnosesGraph>("limit");
            SortGraph sort = context.ComputeArgAndVar<SortGraph, DiagnosesGraph>("sort");

            OrderBy orderBy = null;
            if (sort != null)
            {
                orderBy = sort.ToOrderBy();
            }

            return new DiagnosisWhere()
            {
                Skip = skip ?? 0,
                First = first ?? 25,
                OrderBy = orderBy,
                Where = filter
            };
        }
    }
    
    public class DiagnosisGraph
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string ClassKind { get; set; }
        public int? DepthInKind { get; set; }
        public string Code { get; set; }
        public string Chapter { get; set; }
        public string Fields { get; set; }
        
        public static DiagnosisGraph ToGraph(Diagnosis domain)
        {
            if (domain == null)
                return null;

            return new DiagnosisGraph
            {
                Id = domain.Id,
                Title =  domain.Title,
                ClassKind = domain.ClassKind,
                DepthInKind = domain.DepthInKind,
                Code = domain.Code,
                Chapter = domain.Chapter,
                Fields = domain.Fields?.ToString(Formatting.None)
            };
        }
    }

    public class DiagnosisGraphType : AutoObjectGraphType<DiagnosisGraph>
    {
    }
    
    
    public class DiagnosisInputGraph : InputObjectGraphType<CreateDiagnosisCommand>
    {
        public DiagnosisInputGraph()
        {
            Name = "diagnosisInput";
            Description = "diagnosis input";

            Field(x => x.Title, true);
            Field(x => x.Code, true);
            Field(x => x.ClassKind, true);
            Field(x => x.DepthInKind, true);
            Field(x => x.Chapter, true);
            Field(x => x.Fields, true);
        }
    }
    
    public class DiagnosisUpdateInputGraph : InputObjectGraphType<UpdateDiagnosisCommand>
    {
        public DiagnosisUpdateInputGraph()
        {
            Name = "diagnosisUpdateInput";
            Description = "diagnosis update input";

            Field(x => x.Id, true);
            Field(x => x.Title, true);
            Field(x => x.Code, true);
            Field(x => x.ClassKind, true);
            Field(x => x.DepthInKind, true);
            Field(x => x.Chapter, true);
            Field(x => x.Fields, true);
        }
    }
    
    public class DiagnosisRemoveInputGraph : InputObjectGraphType<RemoveCommand>
    {
        public DiagnosisRemoveInputGraph()
        {
            Name = "diagnosisRemoveInput";
            Description = "diagnosis remove input";

            Field(x => x.Id, true);
        }
    }
    
    public class DiagnosisBatchInputGraph : InputObjectGraphType<DiagnosisBatchCommand>
    {
        public DiagnosisBatchInputGraph()
        {
            Name = "diagnosisBatchInput";
            Description = "Diagnosis Batch Input";

            Field(x => x.Create, true, typeof(ListGraphType<DiagnosisInputGraph>));
            Field(x => x.Update, true, typeof(ListGraphType<DiagnosisUpdateInputGraph>));
            Field(x => x.Delete, true, typeof(ListGraphType<DiagnosisRemoveInputGraph>));
        }
    }
    
    public class DiagnosisFilterInputGraph : InputObjectGraphType<Filter<DiagnosisFilter>>
    {
        public DiagnosisFilterInputGraph()
        {
            Name = "diagnosisFilterInput";
            Description = "Diagnosis Filter Input";

            Field(x => x.And, true, typeof(ListGraphType<DiagnosisFilterInputGraph>));
            Field(x => x.Or, true, typeof(ListGraphType<DiagnosisFilterInputGraph>));
            Field(x => x.Where, true, typeof(DiagnosisFilterWhereInputGraph));
        }
    }
    
    public class DiagnosisFilterWhereInputGraph : InputObjectGraphType<DiagnosisFilter>
    {
        public DiagnosisFilterWhereInputGraph()
        {
            Name = "diagnosisFilterWhereInput";
            Description = "Diagnosis Filter Where Input";

            Field(x => x.Id, true);
            Field(x => x.Title, true);
            Field(x => x.Code, true);
            Field(x => x.Chapter, true);
            Field(x => x.ClassKind, true);
            Field(x => x.DepthInKind, true);
            
            Field(x => x.Title_contains, true);
            Field(x => x.Code_contains, true);
            Field(x => x.Chapter_contains, true);
            Field(x => x.ClassKind_contains, true);
            
            Field(x => x.Id_in, true);
            Field(x => x.Title_in, true);
            Field(x => x.Code_in, true);
            Field(x => x.Chapter_in, true);
            Field(x => x.ClassKind_in, true);
            Field(x => x.DepthInKind_in, true);
            Field(x => x.FieldsWhere, true, type: typeof(FieldsWhereInputGraphType));
        }
    }
}