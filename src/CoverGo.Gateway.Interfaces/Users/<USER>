using CoverGo.Users.Domain.Individuals;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Users;

public class IntegrationGraphType : ObjectGraphType<IntegrationGraph>
{
    public IntegrationGraphType()
    {
        Name = "integration";

        Field(i => i.SystemId, type: typeof(StringGraphType)).Description("Integration id of the system");
        Field(i => i.ExternalEntityId, type: typeof(StringGraphType)).Description("Entity id on the integrated system");
        Field(i => i.ExternalProductId, type: typeof(StringGraphType)).Description("Product id on the integrated system");
    }
}

public class IntegrationGraph
{
    public string SystemId { get; set; }
    public string ExternalEntityId { get; set; }
    public string ExternalProductId { get; set; }

    public static IntegrationGraph ToGraph(Integration integration) => new IntegrationGraph
    {
        SystemId = integration.SystemId,
        ExternalEntityId = integration.ExternalEntityId,
        ExternalProductId = integration.ExternalProductId
    };
}