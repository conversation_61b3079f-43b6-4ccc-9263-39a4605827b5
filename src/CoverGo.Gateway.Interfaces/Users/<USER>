﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Users.Domain.Individuals;
using GraphQL.Language.AST;
using GraphQL.Types;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Interfaces.Users
{
    public static class EntityResolvers
    {
        public static async Task<TotalCountResult<I>> ResolveEntitiesAsync<T, Y, U, I>(
            this ResolveFieldContext<object> context,
            IEntityService<T, Y, U> entityService,
            Domain.QueryArguments queryArguments)
                where T : Entity
                where Y : CreateEntityCommand
                where U : UpdateEntityCommand
                where I : EntityGraph
        {
            string tenantId = context.GetTenantIdFromToken();

            bool FragmentHasField(FragmentSpread fragment, string fieldName) =>
                context.Fragments?
                    .FindDefinition(fragment.Name)?
                    .SelectionSet?
                    .Selections?
                    .Any(s => (s as Field)?.Name == fieldName) == true;

            bool InlineFragmentHasField(InlineFragment fragment, string fieldName) =>
                fragment
                    .SelectionSet?
                    .Selections?
                    .Any(s => (s as Field)?.Name == fieldName) == true;

            bool AnyFragmentFieldIs(string fieldName) =>
                context.FieldAst?
                    .SelectionSet?
                    .Selections?
                    .Any(selection => selection is FragmentSpread fragment && FragmentHasField(fragment, fieldName)
                        || selection is InlineFragment inlineFragment && InlineFragmentHasField(inlineFragment, fieldName)) == true;

            IEnumerable<T> entities = null;
            if (context.SubFields.ContainsKey("list") || AnyFragmentFieldIs("list"))
                entities = await entityService.GetAsync(tenantId, queryArguments);

            long totalCount = 0;
            if (context.SubFields.ContainsKey("totalCount") || AnyFragmentFieldIs("totalCount"))
                totalCount = await entityService.GetTotalCount(tenantId, (EntityWhere)queryArguments.Where);

            IEnumerable<I> entityGraphs = entities?.Select(c => (I)c.ToGraph());
            return new TotalCountResult<I>
            {
                Items = entityGraphs,
                TotalCount = totalCount
            };
        }

        public static async Task<object> ResolveRegisterIndividualAsync(
            this ResolveFieldContext<object> context,
            IAuthService authService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> customerService,
            IEntityService userService)
        {
            string tenantId = context.GetArgument<string>("tenantId");
            string clientId = context.GetArgument<string>("clientId");

            RegisterIndividualInputGraph input = context.GetArgument<RegisterIndividualInputGraph>("registerIndividualInput");

            // create individual
            Result<CreatedStatus> createdIndividualResult = await customerService.CreateAsync(tenantId, new CreateIndividualCommand
            {
                EnglishFirstName = input.EnglishFirstName,
                EnglishLastName = input.EnglishLastName,
                ChineseFirstName = input.ChineseFirstName,
                ChineseLastName = input.ChineseLastName,
                InternalCode = input.InternalCode,
                InternalCodeLength = input.InternalCodeLength,
                NameFormat = input.NameFormat,
                Type = IndividualTypes.Customer
            });
            if (createdIndividualResult.Status == "failure")
                return createdIndividualResult;

            // add email
            if (input.Email != null)
                await userService.AddContactAsync(tenantId, createdIndividualResult.Value.Id, EntityTypes.Individual, new AddContactCommand
                {
                    Type = "email",
                    Value = input.Email
                });

            // add telephone
            if (input.TelephoneNumber != null)
                await userService.AddContactAsync(tenantId, createdIndividualResult.Value.Id, EntityTypes.Individual, new AddContactCommand
                {
                    Type = "telephoneNumber",
                    Value = input.TelephoneNumber
                });

            Result<CreatedStatus> loginCreatedResult = await authService.CreateLoginAsync(tenantId, new CreateLoginCommand
            {
                ClientId = clientId,
                Email = input.Email,
                Username = input.Username ?? input.Email,
                Password = input.Password,
                TelephoneNumber = input.TelephoneNumber,
                EntityId = createdIndividualResult.Value.Id,
                EntityType = "individual",
                IsEmailConfirmed = input.IsEmailConfirmed
            });

            if (loginCreatedResult.Status == "failure")
                return loginCreatedResult;

            await authService.AddTargettedPermissionsAsync(tenantId, loginCreatedResult.Value.Id, new List<AddTargettedPermissionCommand>
            {
                new() { AddedById = loginCreatedResult.Value.Id, Type = "clientId", Value = clientId }
            });

            return loginCreatedResult.Status == "failure" ? loginCreatedResult : createdIndividualResult;
        }
    }
}
