using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Clients;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Users;

namespace CoverGo.Gateway.Interfaces.Users
{
    public class CoverGoDisabilityService : CoverGoGenericGenericServiceRestClientBase<Disability,
        CreateDisabilityCommand, UpdateDisabilityCommand, RemoveCommand, DisabilityBatchCommand, DisabilityWhere,
        DisabilityFilter>
    {
        private readonly HttpClient _client;

        public CoverGoDisabilityService(HttpClient client) : base(client)
        {
            _client = client;
        }

        protected override string ApiBaseUrlBuildInternal(string tenantId) => 
            $"{tenantId}/api/v1/disabilities";
        
        public Task<Result> AddDiagnosisAsync(
            string tenantId,
            string disabilityId,
            string diagnosisId,
            CancellationToken cancellation = default) =>
            this._client.GenericGetAsync<Result>($"{this.ApiBaseUrlBuildInternal(tenantId)}/{disabilityId}/addDiagnoses/{diagnosisId}", cancellation);

        public Task<Result> RemoveDiagnosisAsync(
            string tenantId,
            string disabilityId,
            string diagnosisId,
            CancellationToken cancellation = default) =>
            this._client.GenericGetAsync<Result>($"{this.ApiBaseUrlBuildInternal(tenantId)}/{disabilityId}/removeDiagnoses/{diagnosisId}", cancellation);
    }
}