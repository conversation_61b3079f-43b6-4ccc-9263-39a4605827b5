using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Common;
using CoverGo.Gateway.Domain.Users;
using GraphQL.DataLoader;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Users
{
    public class DisabilitiesGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<DisabilityGraph> List { get; set; }
    }

    public class DisabilitiesGraphType : ObjectGraphType<DisabilitiesGraph>
    {
        public DisabilitiesGraphType(
            CoverGoDisabilityService disabilityService,
            PermissionValidator permissionValidator)
        {
            Name = "disabilities";

            Field(c => c.TotalCount)
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   var where = await FilterFromContextBuildInternal(permissionValidator, context);

                   return await disabilityService.CountAsync(tenantId, where);
               });

            Field(c => c.List, type: typeof(ListGraphType<DisabilityGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var where = await FilterFromContextBuildInternal(permissionValidator, context);
                    IReadOnlyCollection<Disability> disabilities = await disabilityService.QueryAsync(tenantId, where);

                    return disabilities.Select(DisabilityGraph.ToGraph);
                });
        }

        private async Task<DisabilityWhere> FilterFromContextBuildInternal(
            PermissionValidator permissionValidator,
            ResolveFieldContext<DisabilitiesGraph> context)
        {
            var allowedIds = (await permissionValidator.GetTargetIdsFromClaim(context, "readDisabilities")).ToList();

            Filter<DisabilityFilter> filter = context.ComputeArgAndVar<Filter<DisabilityFilter>, DisabilitiesGraph>("where") ?? new Filter<DisabilityFilter>();
            filter.And ??= new List<Filter<DisabilityFilter>>();

            if (!allowedIds.Contains("all"))
            {
                filter.And.Add(new Filter<DisabilityFilter>
                {
                    Where = new DisabilityFilter { Id_in = allowedIds.ToList() }
                });
            }

            int? skip = context.ComputeArgAndVar<int?, DisabilitiesGraph>("skip");
            int? first = context.ComputeArgAndVar<int?, DisabilitiesGraph>("limit");
            SortGraph sort = context.ComputeArgAndVar<SortGraph, DisabilitiesGraph>("sort");

            OrderBy orderBy = null;
            if (sort != null)
            {
                orderBy = sort.ToOrderBy();
            }

            return new DisabilityWhere
            {
                Skip = skip ?? 0,
                First = first ?? 25,
                OrderBy = orderBy,
                Where = filter
            };
        }
    }

    public class DisabilityGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public IEnumerable<DiagnosisGraph> Diagnoses { get; set; }

        public static DisabilityGraph ToGraph(Disability domain)
        {
            if (domain == null)
                return null;

            return new DisabilityGraph
            {
                Id = domain.Id,
                Name = domain.Name,
                Diagnoses = domain.DiagnosisIds.Select(id => new DiagnosisGraph { Id = id }).ToList()
            };
        }
    }

    public class DisabilityGraphType : ObjectGraphType<DisabilityGraph>
    {
        public DisabilityGraphType(IDataLoaderContextAccessor accessor,
            CoverGoDiagnosisService diagnosisService,
            PermissionValidator permissionValidator)
        {
            Name = "disability";
            Description = "Disability";

            Field(x => x.Id, nullable: true);
            Field(x => x.Name, nullable: true);
            Field(x => x.Diagnoses, nullable: true, type: typeof(ListGraphType<DiagnosisGraphType>))
                .GetPaginationArguments()
                .Argument<DiagnosisFilterInputGraph>("filter", "A disability search filter")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "readDiagnoses");

                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = (await permissionValidator.GetTargetIdsFromClaim(context, "readDiagnoses")).ToList();

                    Filter<DiagnosisFilter> filter = context.ComputeArgAndVar<Filter<DiagnosisFilter>, DisabilityGraph>("filter") ?? new Filter<DiagnosisFilter>();
                    filter.And ??= new List<Filter<DiagnosisFilter>>();


                    if (!allowedIds.Contains("all"))
                    {
                        filter.And.Add(new Filter<DiagnosisFilter>
                        {
                            Where = new DiagnosisFilter { Id_in = allowedIds.ToList() }
                        });
                    }

                    int? skip = context.ComputeArgAndVar<int?, DisabilityGraph>("skip");
                    int? first = context.ComputeArgAndVar<int?, DisabilityGraph>("limit");
                    SortGraph sort = context.ComputeArgAndVar<SortGraph, DisabilityGraph>("sort");

                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var disabilitiesLoader = accessor.Context.GetOrAddBatchLoader<string, Diagnosis>("GetDiagnosesByIds",
                        async x =>
                        {
                            Filter<DiagnosisFilter> clonedFilter = filter.DeepClone();
                            clonedFilter.And.Add(new Filter<DiagnosisFilter>
                            {
                                Where = new DiagnosisFilter { Id_in = x.ToList() }
                            });
                            var query = new DiagnosisWhere
                            {
                                Skip = skip ?? 0,
                                First = first ?? 25,
                                OrderBy = orderBy,
                                Where = clonedFilter
                            };

                            IReadOnlyCollection<Diagnosis> diagnoses = await diagnosisService.QueryAsync(tenantId, query);

                            return diagnoses.ToDictionary(d => d.Id, d => d);
                        });

                    Diagnosis[] loadedDiagnoses = await disabilitiesLoader.LoadAsync(context.Source.Diagnoses.Select(x => x.Id));

                    return loadedDiagnoses.Select(DiagnosisGraph.ToGraph);
                });
        }
    }

    public class DisabilityInputGraph : InputObjectGraphType<CreateDisabilityCommand>
    {
        public DisabilityInputGraph()
        {
            Name = "disabilityInput";
            Description = "disability input";

            Field(x => x.Name, true);
        }
    }

    public class DisabilityUpdateInputGraph : InputObjectGraphType<UpdateDisabilityCommand>
    {
        public DisabilityUpdateInputGraph()
        {
            Name = "disabilityUpdateInput";
            Description = "disability update input";

            Field(x => x.Id, true);
            Field(x => x.Name, true);
        }
    }

    public class DisabilityRemoveInputGraph : InputObjectGraphType<RemoveCommand>
    {
        public DisabilityRemoveInputGraph()
        {
            Name = "disabilityRemoveInput";
            Description = "disability remove input";

            Field(x => x.Id, true);
        }
    }

    public class DisabilityBatchInputGraph : InputObjectGraphType<DisabilityBatchCommand>
    {
        public DisabilityBatchInputGraph()
        {
            Name = "disabilityBatchInput";
            Description = "Disability Batch Input";

            Field(x => x.Create, true, typeof(ListGraphType<DisabilityInputGraph>));
            Field(x => x.Update, true, typeof(ListGraphType<DisabilityUpdateInputGraph>));
            Field(x => x.Delete, true, typeof(ListGraphType<DisabilityRemoveInputGraph>));
        }
    }


    public class DisabilityFilterInputGraph : InputObjectGraphType<Filter<DisabilityFilter>>
    {
        public DisabilityFilterInputGraph()
        {
            Name = "disabilityFilterInput";
            Description = "Disability Filter Input";

            Field(x => x.And, true, typeof(ListGraphType<DisabilityFilterInputGraph>));
            Field(x => x.Or, true, typeof(ListGraphType<DisabilityFilterInputGraph>));
            Field(x => x.Where, true, typeof(DisabilityFilterWhereInputGraph));
        }
    }

    public class DisabilityFilterWhereInputGraph : InputObjectGraphType<DisabilityFilter>
    {
        public DisabilityFilterWhereInputGraph()
        {
            Name = "disabilityFilterWhereInput";
            Description = "Disability Filter Where Input";

            Field(x => x.Id, true);
            Field(x => x.Id_in, true);
            Field(x => x.Name, true);
            Field(x => x.Name_contains, true);
            Field(x => x.Name_in, true);
        }
    }
}