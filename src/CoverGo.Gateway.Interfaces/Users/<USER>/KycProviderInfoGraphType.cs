using CoverGo.Gateway.Domain.Users.Kyc;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Users.Kyc
{
    public class KycProviderInfoGraphType : ObjectGraphType<KycProviderInfoGraph>
    {
        public KycProviderInfoGraphType()
        {
            Name = "kycProviderInfo";
            Description = "a kyc provider info representation";
            
            Field(k => k.Id);
            Field(k => k.ProviderEnum, type: typeof(KycProviderEnumGraphType));
            Field(k => k.ProviderReference, nullable: true);
            Field(k => k.ProviderStatus, nullable: true);
            Field(k => k.ProviderEvents, nullable: true);
        }
    }
}