using CoverGo.Gateway.Domain.Users.Kyc;
using CoverGo.GraphQLGenerators;
using CoverGo.Users.Domain.Individuals;

namespace CoverGo.Gateway.Interfaces.Users.Kyc
{
    public sealed class CreateKycApplicantInputGraphType : AutoInputObjectGraphType<CreateKycApplicantCommand>{}
    public sealed class GenerateKycAuthTokenInputGraphType : AutoInputObjectGraphType<GenerateKycAuthTokenCommand>{}
    public sealed class KycProviderEnumGraphType : AutoEnumGraphType<KycProviderEnum> {}
}