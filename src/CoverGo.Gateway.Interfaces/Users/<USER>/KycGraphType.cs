using CoverGo.Gateway.Domain.Users.Kyc;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Users.Kyc
{
    public class KycGraphType : ObjectGraphType<KycGraph>
    {
        public KycGraphType()
        {
            Name = "kyc";
            Description = "a kyc representation";

            Field(k => k.Status, nullable: true);
            Field(k => k.ProviderInfos, type: typeof(ListGraphType<KycProviderInfoGraphType>), nullable: true);
        }
    }
}