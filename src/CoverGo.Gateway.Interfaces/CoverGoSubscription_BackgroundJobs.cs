using System.Threading;
using System.Threading.Tasks;
using HotChocolate;
using HotChocolate.Execution;
using HotChocolate.Subscriptions;
using HotChocolate.Types;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Interfaces;

[ExtendObjectType("Subscription")]
public class CoverGoSubscription_BackgroundJobs
{
    public async ValueTask<ISourceStream<JToken>> OnJobStatusChangedResolver([Service] ITopicEventReceiver receiver, CancellationToken cancellationToken)
        => await receiver.SubscribeAsync<string, JToken>("JobStatusChanged", cancellationToken);

    [GraphQLDescription("Subscribes to background job status changes. Provide a jobId to listen to a specific job.")]
    [GraphQLName("onJobStatusChanged")]
    [Subscribe(With = nameof(OnJobStatusChangedResolver))]
    public JobPayload? OnJobStatusChanged(
        [EventMessage] JToken message,
        [GraphQLDescription("Optional. The ID of the specific job to listen for.")]
        string? jobId = null)
    {
        JobPayload? jobPayload = message.ToObject<JobPayload>();
        return string.IsNullOrWhiteSpace(jobId) || jobPayload?.JobId == jobId ? jobPayload : null;
    }
}

[GraphQLDescription("Payload for job status changes")]
[GraphQLName("jobPayload")]
public class JobPayload
{
    [GraphQLDescription("The ID of the job")]
    [GraphQLName("jobId")]
    public string? JobId { get; set; }

    [GraphQLDescription("The status of the job. Can be 'Processing', 'Succeeded', 'Failed'")]
    [GraphQLName("status")]
    public string? Status { get; set; }
}