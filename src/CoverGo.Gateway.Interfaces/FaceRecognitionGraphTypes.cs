﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces
{
    public class FaceRecognitionOutputGraph
    {
        public ScalarValue Value { get; set; }
        public string ValueJsonString { get; set; }

        public static FaceRecognitionOutputGraph ToGraph(FaceRecognitionOutput domain) => domain == null
            ? null
            : new FaceRecognitionOutputGraph
            {
                Value = domain.Value?.ToScalarValue(),
                ValueJsonString = domain.Value?.ToString()
            };
    }

    public class FaceRecognitionResultGraphType : ObjectGraphType<Result<FaceRecognitionOutputGraph>>
    {
        public FaceRecognitionResultGraphType()
        {
            Name = "faceRecognitionResult";

            Field<StringGraphType>("status");
            Field<ListGraphType<NonNullGraphType<StringGraphType>>>("errors");
            Field(c => c.Value, type: typeof(FaceRecognitionOutputGraphType));
        }
    }

    public class FaceRecognitionOutputGraphType : ObjectGraphType<FaceRecognitionOutputGraph>
    {
        public FaceRecognitionOutputGraphType()
        {
            Name = "faceRecognitionOutput";

            Field(f => f.Value, type: typeof(ScalarValueGraphType));
            Field(f => f.ValueJsonString, nullable: true);
        }
    }
}
