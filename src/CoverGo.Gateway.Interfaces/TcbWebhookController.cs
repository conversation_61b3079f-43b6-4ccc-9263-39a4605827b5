using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using ClosedXML.Excel;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Users.Domain.Individuals;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace CoverGo.Gateway.Interfaces
{
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("tcbWebhook/{tenantId}")]
    [Authorize]
    public class TcbWebhookController
    {
        private readonly IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> _entityService;
        private readonly IAuthService _authService;
        private readonly IFileSystemService _fileSystemService;
        private readonly ILogger _logger;

        public TcbWebhookController(
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> entityService,
            IAuthService authService,
            IFileSystemService fileSystemService,
            ILogger<TcbWebhookController> logger)
        {
            _entityService = entityService;
            _authService = authService;
            _fileSystemService = fileSystemService;
            _logger = logger;

        }

        [HttpPost("ListUsers")]
        public async Task<ActionResult<Result<string>>> ListUsers(string tenantId, [FromBody] TcbListUserRequest request)
        {
            int from = 0;
            var start = Stopwatch.GetTimestamp();

            var loginCount = await _authService.GetLoginTotalCountAsync(tenantId, new LoginWhere());

            if (loginCount == 0)
            {
                return Result<string>.Failure("No logins");
            }

            if (request == null)
            {
                request = new TcbListUserRequest();
            }

            if (!request.Limit.HasValue || request.Limit == 0)
            {
                request.Limit = 10;
            }

            if (!request.Skip.HasValue)
            {
                request.Skip = 0;
            }

            from = request.Skip.Value;

            List<TcbListUsersItemResponse> items = new List<TcbListUsersItemResponse>();
            try
            {
                var firstCall = await GetUsers(tenantId, request);
                items.AddRange(firstCall);
                var elapsed = GetElapsedMilliseconds(start, Stopwatch.GetTimestamp());

                while (loginCount >= request.Skip && elapsed < 30 * 1000)
                {
                    var nextCall = await GetUsers(tenantId, request);
                    items.AddRange(nextCall);
                    elapsed = GetElapsedMilliseconds(start, Stopwatch.GetTimestamp());
                }

                var content = CreateWorkbookAndFillData(items.Select(x => new ExcelRecord
                {
                    Id = x.Id,
                    Username = x.Username,
                    Email = x.Email,
                    IsActive = x.IsActive.ToString(),
                    Name = x.AssociatedUser?.Name,
                    Source = x.AssociatedUser?.Source,
                    StaffId = x.AssociatedUser?.FieldObject?.StaffId,
                    AgentCode = x.AssociatedUser?.FieldObject?.AgentCode,
                    SaleUserId = x.AssociatedUser?.FieldObject?.SaleUserId,
                    AgentCodeName = x.AssociatedUser?.FieldObject?.AgentCodeName,
                }).ToList());

                var key = await CreateExcelAndUploadToFileSystemAsync(tenantId, "", content);

                if (!key.IsSuccess) {
                    return key;
                }

                return Result<string>.Success(JsonConvert.SerializeObject(new { From = from, To = request.Skip, Key = key.Value }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "List Exception");
                return Result<string>.Failure(ex.Message);
            }
        }

        private async Task<IEnumerable<TcbListUsersItemResponse>> GetUsers(string tenantId, TcbListUserRequest request)
        {
            var queryArguments = new Domain.QueryArguments { Skip = request.Skip, First = request.Limit };
            IEnumerable<Login> logins = await _authService.GetLoginsAsync(tenantId, queryArguments);
            var entityIds = logins.Select(x => x.EntityId);

            List<Individual> entities = new List<Individual>();
            if (entityIds.Any())
            {
                entities.AddRange(await _entityService.GetAsync(tenantId, new Domain.QueryArguments
                {
                    Where = new IndividualWhere
                    {
                        Id_in = entityIds.ToList()
                    }
                }));
            }

            request.Skip += request.Limit;

            return logins.Select(l => {
                var user = entities.FirstOrDefault(x => x.Id == l.EntityId);
                return new TcbListUsersItemResponse
                {
                    Id = l.Id,
                    Username = l.Username,
                    Email = l.Email,
                    IsActive = l.IsActive,
                    AssociatedUser = user == null ? null : new AssociatedUser
                    {
                        Name = user.Name,
                        Fields = user.Fields?.ToString(),
                        Source = user.Source
                    }
                };
            });
        }

        private byte[] CreateWorkbookAndFillData(List<ExcelRecord> data)
        {
            IXLWorkbook wb = InitialiseSummaryExcelWorkBook();

            if (wb.TryGetWorksheet("Sheet 1", out IXLWorksheet ws))
            {
                ws.Cell(2, 1).InsertData(data);
            }

            var stream = new MemoryStream();
            wb.SaveAs(stream);

            return stream.ToArray();
        }

        private IXLWorkbook InitialiseSummaryExcelWorkBook()
        {
            XLWorkbook workbook = new XLWorkbook();
            IXLWorksheet worksheet = workbook.Worksheets.Add("Sheet 1");

            string[] columnHeader = new string[]
            {
            "Id",
            "Username",
            "Email",
            "IsActive",
            "Name",
            "Source",
            "StaffId",
            "AgentCode",
            "SaleUserId",
            "AgentCodeName"
            };

            for (int i = 0; i < columnHeader.Length; i++)
            {
                worksheet.Cell(1, i + 1).Value = columnHeader[i];
            }

            return workbook;
        }

        private async Task<Result<string>> CreateExcelAndUploadToFileSystemAsync(string tenantId, string bucketName, byte[] content)
        {
            DateTime today = DateTime.Now;

            string filePath = $"tcbListUsers/{today.ToString("yyyyMMdd")}/{today.ToString("HHmmss")}.xlsx";

            Result uploadResult = await UploadFileToFileSystem(
                tenantId,
                bucketName,
                filePath,
                content);

            if (!uploadResult.IsSuccess)
            {
                _logger.LogError($"Upload file failed | {string.Join(", ", uploadResult.Errors)}");
                return Result<string>.Failure(uploadResult.Errors);
                
            }

            return Result<string>.Success(filePath);
        }

        private async Task<Result> UploadFileToFileSystem(string tenantId, string bucketName, string key, byte[] content)
        {
            UploadFileCommand command = new()
            {
                Key = key,
                Content = content,
                IsPublic = false,
            };

            return await _fileSystemService.UploadFileAsync(tenantId, bucketName, command);
        }

        private double GetElapsedMilliseconds(long start, long stop)
        {
            return (stop - start) * 1000 / (double)Stopwatch.Frequency;
        }

        internal class TcbListUsersItemResponse
        {
            public string Id { get; set; }
            public string Username { get; set; }
            public string Email { get; set; }
            public bool IsActive { get; set; }
            public AssociatedUser AssociatedUser { get; set; }
        }

        internal interface IAssociatedUser
        {
            public string Name { get; set; }
            public string Source { get; set; }
        }

        internal class AssociatedUser : IAssociatedUser
        {
            public string Name { get; set; }
            public string Fields { get; set; }
            public string Source { get; set; }
            public AssociatedUserField FieldObject
            {
                get
                {
                    return string.IsNullOrEmpty(Fields) ? null : JsonConvert.DeserializeObject<AssociatedUserField>(Fields);
                }
            }
        }

        internal interface IAssociatedUserField
        {
            public string StaffId { get; set; }
            public string AgentCode { get; set; }
            public string SaleUserId { get; set; }
            public string AgentCodeName { get; set; }
        }

        internal class AssociatedUserField
        {
            public string StaffId { get; set; }
            public string AgentCode { get; set; }
            public string SaleUserId { get; set; }
            public string AgentCodeName { get; set; }
        }

        internal class ExcelRecord : IAssociatedUser, IAssociatedUserField
        {
            public string Id { get; set; }
            public string Username { get; set; }
            public string Email { get; set; }
            public string IsActive { get; set; }
            public string Name { get; set; }
            public string Source { get; set; }
            public string StaffId { get; set; }
            public string AgentCode { get; set; }
            public string SaleUserId { get; set; }
            public string AgentCodeName { get; set; }
        }
    }

    public class TcbListUserRequest
    {
        public int? Skip { get; set; }
        public int? Limit { get; set; }
    }
}