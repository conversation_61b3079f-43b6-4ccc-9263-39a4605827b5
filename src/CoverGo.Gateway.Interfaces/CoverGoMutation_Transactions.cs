using System.Collections.Generic;
using System.Linq;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Transactions;
using CoverGo.Gateway.Interfaces.Users;
using GraphQL.Authorization;
using GraphQL.Types;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeTransactionsMutations(
            IAuthService authService,
            ITransactionService transactionService,
            IPolicyService policyService,
            PermissionValidator permissionValidator)
        {
            Field<TransactionRedirectionResultGraphType>()
                .Name("initializeTransaction")
                .AuthorizeWith("any")
                .Description("Initialize a transaction")
                .Argument<NonNullGraphType<InitializeTransactionInputGraphType>>("input", "The initialize transaction input")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("createTransactions", "writeTransactions"));

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    InitializeTransactionCommand command = context.GetArgument<InitializeTransactionCommand>("input");
                    command.InitializedById = loginId;

                    Result<TransactionRedirection> result = await transactionService.InitializeAsync(tenantId, command);

                    return result;
                });

            Field<PolicyResultGraphType>()
                .Name("tryIssuePolicy")
                .AuthorizeWith("any")
                .Description("Try to issue Policy if the conditions to issue the policy are reached")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "The id of the policy to be issued")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string loginId = context.GetLoginIdFromToken();

                    return await policyService.IssuePolicyAsync(tenantId, new IssuePolicyCommand { PolicyId = policyId, IssuedById = loginId });
                });

            Field<PolicyResultGraphType>()
               .Name("processTransaction")
               .AuthorizeWith("any")
               .Description("Processing of a payment. Can be tied to a policy or be a standalone payment")
               .Argument<NonNullGraphType<StringGraphType>>("transactionId", "the identifier of the transaction to process")
               .Argument<BooleanGraphType>("tryIssue", "signifies whether to try to issue the policy attached to the transaction")
               .Argument<NonNullGraphType<ProcessTransactionInputGraphType>>("input", "the input to process the transaction")
               .ResolveAsync(async context =>
               {
                   string transactionId = context.GetArgument<string>("transactionId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateTransactions", "writeTransactions"));

                   string tenantId = context.GetTenantIdFromToken();
                   string clientId = context.GetClientIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   bool tryIssue = context.GetArgument<bool>("tryIssue");

                   ProcessTransactionCommand paymentCommand = context.GetArgument<ProcessTransactionCommand>("input");
                   paymentCommand.ProcessedById = loginId;

                   Transaction transactionToProcess = (await transactionService.GetAsync(tenantId,
                      new Domain.QueryArguments
                      {
                          Where = new TransactionWhere
                          {
                              Id = transactionId
                          }
                      }
                   )).FirstOrDefault();

                   if (transactionToProcess == null)
                       return new Result<PolicyStatus> { Status = "failure", Errors = new List<string> { $"The transaction {transactionId} was not found." } };

                   if (transactionToProcess.Status != TransactionStatus.NotStarted)
                       return new Result<PolicyStatus> { Status = "failure", Errors = new List<string> { $"The transaction {transactionId} was in the invalid state {transactionToProcess.Status}." } };

                   Result paymentResult = await transactionService.ProcessAsync(tenantId, transactionId, paymentCommand);

                   if (paymentResult.Status == "failure")
                       return new Result<PolicyStatus> { Status = paymentResult.Status, Errors = paymentResult.Errors };

                   if (paymentResult.Status == "success")
                   {
                       if (tryIssue == false)
                           return new Result<PolicyStatus> { Status = "success" };

                       if (tryIssue == true && transactionToProcess.PolicyId == null)
                           return new Result<PolicyStatus> { Status = "failure", Errors = new List<string> { $"The transaction was processed but there is no policy tied to this transaction to issue." } };
                   }

                   Result<PolicyStatus> issuanceResult = await policyService.IssuePolicyAsync(tenantId, new IssuePolicyCommand { PolicyId = transactionToProcess.PolicyId, IssuedById = loginId });

                   if (issuanceResult?.Status != "success")
                       await transactionService.RefundAsync(tenantId, new RefundCommand { TransactionIdToRefund = transactionId, RefundedById = loginId });

                   return issuanceResult ?? new Result<PolicyStatus> { Status = "IssuePolicy failed." };
               });

            Field<ResultGraphType>()
                .Name("refundTransaction")
                .AuthorizeWith("any")
                .Description("Refunds a transaction")
                .Argument<NonNullGraphType<StringGraphType>>("transactionId", "The identifier of the transaction to refund")
                .Argument<NonNullGraphType<RefundTransactionInputGraphType>>("input", "The refund transaction input")
                .ResolveAsync(async context =>
                {
                    string transactionId = context.GetArgument<string>("transactionId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTransactions", "writeTransactions").WithTargetIds(transactionId));

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    RefundCommand command = context.GetArgument<RefundCommand>("input");
                    command.TransactionIdToRefund = transactionId;
                    command.RefundedById = loginId;

                    Result result = await transactionService.RefundAsync(tenantId, command);

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createTransaction")
                .AuthorizeWith("any")
                .Description("Creates a transaction")
                .Argument<NonNullGraphType<CreateTransactionInputGraphType>>("input", "the create transaction input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    await permissionValidator.Authorize(context, new PermissionRequest("createTransactions", "writeTransactions"));

                    CreateTransactionCommand command = context.GetArgument<CreateTransactionCommand>("input");
                    command.CreatedById = loginId;

                    var userContext = (GraphQLUserContext)context.UserContext;
                    Result<CreatedStatus> result = await transactionService.CreateAsync(tenantId, command, Tools.GetAccessToken(userContext));

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateTransaction")
                .AuthorizeWith("any")
                .Description("Updates a transaction")
                .Argument<NonNullGraphType<StringGraphType>>("transactionId", "the identifier of the transaction to update")
                .Argument<NonNullGraphType<UpdateTransactionInputGraphType>>("input", "the update transaction input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string transactionId = context.GetArgument<string>("transactionId");
                    string loginId = context.GetLoginIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTransactions", "writeTransactions").WithTargetIds(transactionId));

                    UpdateTransactionCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateTransactionCommand>();
                    command.TransactionId = transactionId;
                    command.ModifiedById = loginId;
                    Result result = await transactionService.UpdateAsync(tenantId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("deleteTransaction")
                .AuthorizeWith("any")
                .Description("Deletes a transaction")
                .Argument<NonNullGraphType<StringGraphType>>("transactionId", "the identifier of the transaction to update")
                .ResolveAsync(async context =>
                 {
                     string tenantId = context.GetTenantIdFromToken();
                     string transactionId = context.GetArgument<string>("transactionId");
                     string loginId = context.GetLoginIdFromToken();
                     await permissionValidator.Authorize(context, new PermissionRequest("deleteTransactions", "writeTransactions").WithTargetIds(transactionId));

                     var command = new DeleteTransactionCommand
                     {
                         TransactionId = transactionId,
                         DeletedById = loginId
                     };

                     Result result = await transactionService.DeleteAsync(tenantId, command);

                     return result;
                 });

            Field<CreatedStatusResultGraphType>()
                .Name("addFactToTransaction")
                .Description("Adds a fact to a transaction")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("transactionId", "the identifier of the transaction")
                .Argument<NonNullGraphType<AddFactInputGraphType>>("input", "the fact to add")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string transactionId = context.GetArgument<string>("transactionId");
                    AddFactInputGraph input = context.GetArgument<AddFactInputGraph>("input");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTransactions", "writeTransactions").WithTargetIds(transactionId));

                    var command = new AddFactCommand
                    {
                        Type = input.Type,
                        Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                        AddedById = loginId
                    };

                    Result<CreatedStatus> result = await transactionService.AddFactAsync(tenantId, transactionId, command);
                    return result;
                });

            Field<ResultGraphType>()
              .Name("updateTransactionFact")
              .Description("Updates a fact of a transaction")
              .AuthorizeWith("any")
              .Argument<NonNullGraphType<StringGraphType>>("transactionId", "the identifier of the transaction")
              .Argument<NonNullGraphType<StringGraphType>>("factId", "the identifier of the fact")
              .Argument<NonNullGraphType<UpdateFactInputGraphType>>("input", "the fact to add")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string transactionId = context.GetArgument<string>("transactionId");
                  string factId = context.GetArgument<string>("factId");
                  UpdateFactInputGraph input = context.GetArgument<UpdateFactInputGraph>("input");
                  await permissionValidator.Authorize(context, new PermissionRequest("updateTransactions", "writeTransactions").WithTargetIds(transactionId));

                  var command = new UpdateFactCommand
                  {
                      Id = factId,
                      Type = input.Type,
                      Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                      ModifiedById = loginId
                  };

                  return await transactionService.UpdateFactAsync(tenantId, transactionId, command);
              });

            Field<ResultGraphType>()
             .Name("removeFactFromTransaction")
             .Description("removes a fact from a transaction")
             .AuthorizeWith("any")
             .Argument<NonNullGraphType<StringGraphType>>("transactionId", "the identifier of the transaction")
             .Argument<NonNullGraphType<StringGraphType>>("factId", "the identifier of the fact")
             .ResolveAsync(async context =>
             {
                 string tenantId = context.GetTenantIdFromToken();
                 string loginId = context.GetLoginIdFromToken();
                 string transactionId = context.GetArgument<string>("transactionId");
                 string factId = context.GetArgument<string>("factId");

                 await permissionValidator.Authorize(context, new PermissionRequest("updateTransactions", "writeTransactions").WithTargetIds(transactionId));

                 return await transactionService.RemoveFactAsync(tenantId, transactionId, new RemoveFactCommand { Id = factId, RemovedById = loginId });
             });

            Field<ResultGraphType>()
                .Name("addTransactionNote")
                .Description("Adds a note to a transaction")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("transactionId", "The transaction identifier")
                .Argument<NonNullGraphType<AddNoteInputGraphType>>("input", "The note input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string transactionId = context.GetArgument<string>("transactionId");
                    await permissionValidator.Authorize(context, "writeTransaction");
                    AddNoteCommand command = context.GetArgument<AddNoteCommand>("input");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result result = await transactionService.AddNoteAsync(tenantId, transactionId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateTransactionNote")
                .Description("Updates a note of a transaction")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("transactionId", "The transaction identifier")
                .Argument<NonNullGraphType<UpdateNoteInputGraphType>>("input", "The update note input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string transactionId = context.GetArgument<string>("transactionId");
                    await permissionValidator.Authorize(context, "writeTransaction", transactionId);
                    UpdateNoteCommand command = context.GetArgument<UpdateNoteCommand>("input");
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await transactionService.UpdateNoteAsync(tenantId, transactionId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeTransactionNote")
                .Description("removes a note from a transaction")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("transactionId", "The transaction identifier")
                .Argument<NonNullGraphType<IdGraphType>>("noteId", "The note identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string transactionId = context.GetArgument<string>("transactionId");
                    await permissionValidator.Authorize(context, "writeTransaction", transactionId);
                    string id = context.GetArgument<string>("noteId");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await transactionService.RemoveNoteAsync(tenantId, transactionId, new RemoveNoteCommand
                    {
                        Id = id,
                        RemovedById = removedById
                    });

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
              .Name("addStakeholderToTransaction")
              .Argument<NonNullGraphType<StringGraphType>>("transactionId", "the transaction identifier")
              .Argument<NonNullGraphType<AddStakeholderInputGraphType>>("input", "the stakeholder input")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string transactionId = context.GetArgument<string>("transactionId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updateTransactions", "writeTransactions").WithTargetIds(transactionId));

                  AddStakeholderCommand command = context.GetArgument<AddStakeholderCommand>("input");
                  command.AddedById = loginId;

                  Result<string> result = await transactionService.AddStakeholderToTransactionAsync(tenantId, transactionId, command);
                  return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
              });

            Field<ResultGraphType>()
               .Name("updateStakeholderOfTransaction")
               .Argument<NonNullGraphType<StringGraphType>>("transactionId", "the transaction identifier")
               .Argument<NonNullGraphType<StringGraphType>>("stakeholderId", "the stakeholder identifier")
               .Argument<NonNullGraphType<UpdateStakeholderInputGraphType>>("input", "the stakeholder input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string transactionId = context.GetArgument<string>("transactionId");
                   string stakeholderId = context.GetArgument<string>("stakeholderId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateTransactions", "writeTransactions").WithTargetIds(transactionId));

                   UpdateStakeholderCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateStakeholderCommand>();
                   command.Id = stakeholderId;
                   command.ModifiedById = loginId;

                   return await transactionService.UpdateStakeholderOfTransactionAsync(tenantId, transactionId, command);
               });

            Field<ResultGraphType>()
               .Name("removeStakeholderFromTransaction")
               .Argument<NonNullGraphType<StringGraphType>>("transactionId", "the transaction identifier")
               .Argument<NonNullGraphType<StringGraphType>>("stakeholderId", "the stakeholder identifier")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string transactionId = context.GetArgument<string>("transactionId");
                   string stakeholderId = context.GetArgument<string>("stakeholderId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateTransactions", "writeTransactions").WithTargetIds(transactionId));

                   var command = new RemoveStakeholderCommand
                   {
                       Id = stakeholderId,
                       RemovedById = loginId
                   };

                   return await transactionService.RemoveStakeholderFromTransactionAsync(tenantId, transactionId, command);
               });

            Field<CreatedStatusResultGraphType>()
                .Name("createPaymentMethod")
                .Argument<StringGraphType>("providerConfigId", "the payment config to use")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the identifier of the entity to add the payment method to")
                .Argument<StringGraphType>("providerToken", "the provider token to create the payment method from")
                .Argument<CardPaymentMethodInputGraphType>("cardInput", "the card input")
                .Argument<BankPaymentMethodInputGraphType>("bankInput", "the bank input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    await permissionValidator.Authorize(context, "writePaymentMethods");
                    string providerConfigId = context.GetArgument<string>("providerConfigId");
                    string providerToken = context.GetArgument<string>("providerToken");
                    string entityId = context.GetArgument<string>("entityId");
                    CreateCardPaymentMethodCommand cardCommand = context.GetArgument<CreateCardPaymentMethodCommand>("cardInput");
                    CreateBankPaymentMethodCommand bankCommand = context.GetArgument<CreateBankPaymentMethodCommand>("bankInput");

                    if (providerToken != null)
                    {
                        var command = new CreatePaymentMethodFromTokenCommand
                        {
                            ProviderConfigId = providerConfigId,
                            Token = providerToken,
                            EntityId = entityId,
                            CreatedById = loginId
                        };

                        return await transactionService.CreatePaymentMethodFromTokenAsync(tenantId, command);
                    }

                    if (cardCommand != null)
                    {
                        cardCommand.EntityId = entityId;
                        cardCommand.ProviderConfigId = providerConfigId;
                        cardCommand.CreatedById = loginId;

                        return await transactionService.CreateCardPaymentMethodAsync(tenantId, cardCommand);
                    }

                    if (bankCommand != null)
                    {
                        bankCommand.EntityId = entityId;
                        bankCommand.CreatedById = loginId;

                        return await transactionService.CreateBankPaymentMethodAsync(tenantId, bankCommand);
                    }

                    return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { "No payment method input found" } };
                });

            Field<ResultGraphType>()
                .Name("deletePaymentMethod")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the identifier of the payment method to delete")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    string id = context.GetArgument<string>("id");
                    await permissionValidator.Authorize(context, "writePaymentMethods", id);

                    var command = new DeleteCommand
                    {
                        DeletedById = loginId
                    };

                    return await transactionService.DeletePaymentMethodAsync(tenantId, id, command);
                });


            Field<CreatedStatusResultGraphType>()
                .Name("createSubscription")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("providerConfigId", "the payment config to use")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the identifier of the entity to add the payment method to")
                .Argument<StringGraphType>("paymentMethodId", "the identifier of the payment method to use")
                .Argument<NonNullGraphType<CreateSubscriptionInputGraphType>>("input", "the subscription input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    await permissionValidator.Authorize(context, "writeSubscriptions");
                    string providerConfigId = context.GetArgument<string>("providerConfigId");
                    string entityId = context.GetArgument<string>("entityId");
                    string paymentMethodId = context.GetArgument<string>("paymentMethodId");
                    CreateSubscriptionCommand command = context.GetArgument<CreateSubscriptionCommand>("input");

                    command.ProviderConfigId = providerConfigId;
                    command.PaymentMethodId = paymentMethodId;
                    command.EntityId = entityId;
                    command.CreatedById = loginId;

                    return await transactionService.CreateSubscriptionAsync(tenantId, command);
                });

            Field<ResultGraphType>()
               .Name("updateSubscription")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<StringGraphType>>("subscriptionId", "the payment config to use")
               .Argument<NonNullGraphType<UpdateSubscriptionInputGraphType>>("input", "the subscription input")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();

                   string subscriptionId = context.GetArgument<string>("subscriptionId");
                   await permissionValidator.Authorize(context, "writeSubscriptions", subscriptionId);
                   UpdateSubscriptionCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateSubscriptionCommand>();

                   command.ModifiedById = loginId;

                   return await transactionService.UpdateSubscriptionAsync(tenantId, subscriptionId, command);
               });

            Field<ResultGraphType>()
                .Name("addOfferToSubscription")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("subscriptionId", "the payment config to use")
                .Argument<NonNullGraphType<AddOfferInputGraphType>>("input", "the subscription input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    string subscriptionId = context.GetArgument<string>("subscriptionId");
                    await permissionValidator.Authorize(context, "writeSubscriptions", subscriptionId);
                    AddOfferCommand command = context.GetArgument<AddOfferCommand>("input");

                    command.AddedById = loginId;

                    return await transactionService.AddOfferToSubscriptionAsync(tenantId, subscriptionId, command);
                });

            Field<ResultGraphType>()
                .Name("updateOfferOfSubscription")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("subscriptionId", "the payment config to use")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "the payment config to use")
                .Argument<NonNullGraphType<UpdateOfferGraphType>>("input", "the update offer input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string offerId = context.GetArgument<string>("offerId");
                    string subscriptionId = context.GetArgument<string>("subscriptionId");
                    string loginId = context.GetLoginIdFromToken();

                    UpdateOfferCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateOfferCommand>();
                    command.ModifiedById = loginId;
                    command.OfferId = offerId;

                    return await transactionService.UpdateOfferOfSubscriptionAsync(tenantId, subscriptionId, command);
                });

            Field<ResultGraphType>()
                .Name("removeOfferFromSubscription")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("subscriptionId", "the payment config to use")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "the payment config to use")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    string subscriptionId = context.GetArgument<string>("subscriptionId");
                    await permissionValidator.Authorize(context, "writeSubscriptions", subscriptionId);
                    string offerId = context.GetArgument<string>("offerId");

                    var command = new RemoveCommand
                    {
                        Id = offerId,
                        RemovedById = loginId
                    };

                    return await transactionService.RemoveOfferFromSubscriptionAsync(tenantId, subscriptionId, command);
                });

            Field<ResultGraphType>()
               .Name("cancelSubscription")
               .Argument<NonNullGraphType<StringGraphType>>("subscriptionId", "the identifier of the subscription to cancel")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();

                   string providerConfigId = context.GetArgument<string>("providerConfigId");
                   string subscriptionId = context.GetArgument<string>("subscriptionId");
                   await permissionValidator.Authorize(context, "writeSubscriptions", subscriptionId);

                   var command = new CancelSubscriptionCommand
                   {
                       ProviderConfigId = providerConfigId,
                       CancelledById = loginId
                   };

                   return await transactionService.CancelSubscriptionAsync(tenantId, subscriptionId, command);
               });

            Field<ResultGraphType>()
              .Name("deleteSubscription")
              .Argument<NonNullGraphType<StringGraphType>>("id", "the identifier of the subscription to delete")
              .AuthorizeWith("any")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();

                  string id = context.GetArgument<string>("id");
                  await permissionValidator.Authorize(context, "writeSubscriptions", id);

                  var command = new DeleteCommand
                  {
                      DeletedById = loginId
                  };

                  return await transactionService.DeleteSubscriptionAsync(tenantId, id, command);
              });

            Field<ResultGraphType>()
               .Name("addTransactionAttachment")
               .Description("Adds an attachment to a transaction")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<StringGraphType>>("transactionId", "The transaction identifier")
               .Argument<NonNullGraphType<AttachmentInputGraphType>>("input", "The attachment input")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string transactionId = context.GetArgument<string>("transactionId");
                   await permissionValidator.Authorize(context, "writeTransactionAttachments");
                   AddAttachmentCommand command = context.GetArgument<AddAttachmentCommand>("input");
                   command.AddedById = context.GetLoginIdFromToken();

                   Result result = await transactionService.AddAttachment(tenantId, transactionId, command);
                   return result;
               });

            Field<ResultGraphType>()
                .Name("removeTransactionAttachment")
                .Description("removes an attachment from a transaction")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("transactionId", "The transaction identifier")
                .Argument<NonNullGraphType<StringGraphType>>("path", "The attachment path")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "writeTransactionAttachments");
                    string tenantId = context.GetTenantIdFromToken();
                    string transactionId = context.GetArgument<string>("transactionId");
                    string path = context.GetArgument<string>("path");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await transactionService.RemoveAttachment(tenantId, transactionId,
                        new RemoveAttachmentCommand { Path = path, RemovedById = removedById });

                    return result;
                });

            Field<ResultGraphType>()
               .Name("createPaymentProviderConfig")
               .Description("adds a paymentProviderConfig")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<InitializeTenantTransactionsInputGraphType>>("transactionsInput", "The transaction identifier")
               .ResolveAsync(async context =>
               {
                   await permissionValidator.Authorize(context, new PermissionRequest("updateTransactions", "writeTransactions"));
                   string tenantId = context.GetTenantIdFromToken();

                   InitializeTenantTransactionsCommand transactionsCommand = context.GetArgument<InitializeTenantTransactionsCommand>("transactionsInput");
                   Result transactionResult = await transactionService.InitializeTenantAsync(tenantId, transactionsCommand);

                   return transactionResult;
               });
        }
    }
}