using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Common;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Notifications;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Users;
using CoverGo.GraphQLGenerators;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using GraphQL.DataLoader;
using GraphQL.Types;
using Newtonsoft.Json;
using EventLog = CoverGo.Gateway.Domain.EventLog;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Interfaces.Auth
{
    public class TokenGraphType : ObjectGraphType<Token>
    {
        public TokenGraphType()
        {
            Name = "token";
            Description = "A securitiy token response";

            Field(t => t.AccessToken, nullable: true);
            Field(t => t.Error, nullable: true);
            Field(t => t.ErrorDescription, nullable: true);
            Field(t => t.ExpiresIn, nullable: true);
            Field(t => t.IdentityToken, nullable: true);
            Field(t => t.TokenType, nullable: true);
            Field(t => t.RefreshToken, nullable: true);
            Field(t => t.RequiresTwoFactor, nullable: true);
        }
    }

    public class TenantSettingsGraphType : ObjectGraphType<TenantSettings>
    {
        public TenantSettingsGraphType()
        {
            Name = "tenantSettings";

            Field(f => f.TenantId, nullable: true);
            Field(f => f.Hosts, nullable: true);
        }
    }

    public class PasswordValidatorsGraphType : ObjectGraphType<PasswordValidators>
    {
        public PasswordValidatorsGraphType()
        {
            Name = "passwordValidators";
            Description = "The password validators";

            Field(p => p.RequireConfirmedEmail);
            Field(p => p.RequireConfirmPhoneNumber);
            Field(p => p.RequireDigit);
            Field(p => p.RequireLength);
            Field(p => p.RequireLowercase);
            Field(p => p.RequireNonAlphanumeric);
            Field(p => p.RequireUniqueChars);
            Field(p => p.RequireUppercase);
            Field(p => p.RequireLetter);
        }
    }

    public class CreateLoginInputGraphType : InputObjectGraphType<CreateLoginCommand>
    {
        public CreateLoginInputGraphType()
        {
            Name = "createLoginInput";
            Description = "Input to create a login";

            Field(c => c.ClientId, nullable: true).Description("Used only to know which emails to be sent for now.");
            Field(c => c.AppIdsToBeGrantedAccessTo, type: typeof(ListGraphType<NonNullGraphType<StringGraphType>>));
            Field(c => c.Email, nullable: true);
            Field(c => c.Password, nullable: true);
            Field(c => c.Username);
            Field(c => c.IsEmailConfirmed, nullable: true);
            Field(c => c.IsPasswordValidationDobRequire, nullable: true);
            Field(c => c.IgnorePasswordValidation, nullable: true);
            Field(c => c.EntityId, nullable: true);
        }
    }

    public class ForgotPassordInputGraphType : InputObjectGraphType<ForgotPasswordCommand>
    {
        public ForgotPassordInputGraphType()
        {
            Name = "forgotPasswordInput";
            Description = "Input to be send a forgot password link by email";

            Field(c => c.ClientId);
            Field(c => c.Email, nullable: true);
            Field(c => c.Username, nullable: true);
            Field(c => c.DateOfBirth, type: typeof(DateTimeOffsetGraphType), nullable: true);
            Field(c => c.SendNotificationCommand, type: typeof(SendNotificationInputGraphType));
        }
    }

    public class ForgotPassord2InputGraphType : InputObjectGraphType<ForgotPassword2Command>
    {
        public ForgotPassord2InputGraphType()
        {
            Name = "forgotPassword2Input";
            Description = "Input to be send a forgot password link by email";

            Field(c => c.ClientId);
            Field(c => c.Email, nullable: true);
            Field(c => c.Username, nullable: true);
            Field(c => c.DateOfBirth, type: typeof(DateTimeOffsetGraphType), nullable: true);
        }
    }

    public class ChangePasswordInputGraphType : InputObjectGraphType<ChangePasswordCommand>
    {
        public ChangePasswordInputGraphType()
        {
            Name = "changePasswordInput";
            Description = "Input to change the password";

            Field(c => c.CurrentPassword, nullable: false);
            Field(c => c.NewPassword, nullable: false);
            Field(c => c.IgnorePasswordValidation, nullable: true);
            Field(c => c.SendNotificationCommand, type: typeof(SendNotificationInputGraphType));
        }
    }

    public class ChangeExpiredPasswordInputGraphType : InputObjectGraphType<ChangeExpiredPasswordCommand>
    {
        public ChangeExpiredPasswordInputGraphType()
        {
            Name = "changeExpiredPasswordInput";
            Description = "Input to change the expired password";

            Field(c => c.UserName, nullable: false);
            Field(c => c.CurrentPassword, nullable: false);
            Field(c => c.NewPassword, nullable: false);
        }
    }

    public class AddTargettedPermissionInputGraphType : InputObjectGraphType<AddTargettedPermissionCommand>
    {
        public AddTargettedPermissionInputGraphType()
        {
            Name = "addTargettedPermissionInput";
            Description = "Input to add a permission to a target to a login";

            Field(c => c.Type);
            Field(c => c.Value);
        }
    }

    public class RemoveTargettedPermissionInputGraphType : InputObjectGraphType<RemoveTargettedPermissionCommand>
    {
        public RemoveTargettedPermissionInputGraphType()
        {
            Name = "removeTargettedPermissionInput";
            Description = "Input to remove a permission to a target to a login";

            Field(c => c.Type);
            Field(c => c.Value);
        }
    }

    public class LoginsGraphType : ObjectGraphType<LoginsGraph>
    {
        public LoginsGraphType(
            IAuthService authService,
            IEntityService entityService,
            PermissionValidator permissionValidator)
        {
            Name = "logins";
            Description = "Gets all logins";

            Field(c => c.TotalCount)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    LoginWhere loginFilter = await BuildLoginFilterFromDeprecatedArguments(permissionValidator, context);

                    if (LoginWhereContainsEntityFilter(loginFilter))
                        await ReplaceEntityToEntityIdIn(tenantId, loginFilter, entityService);

                    return await authService.GetLoginTotalCountAsync(tenantId, loginFilter);
                });

            Field(l => l.List, type: typeof(ListGraphType<LoginGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    LoginWhere loginFilter = await BuildLoginFilterFromDeprecatedArguments(permissionValidator, context);

                    if (LoginWhereContainsEntityFilter(loginFilter))
                        await ReplaceEntityToEntityIdIn(tenantId, loginFilter, entityService);

                    int? skip = context.Source.Skip;
                    int? first = context.Source.Limit;
                    SortGraph sort = context.Source.Sort;
                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var queryArguments = new Domain.QueryArguments { Where = loginFilter, Skip = skip, First = first, OrderBy = orderBy };
                    IEnumerable<Login> logins = await authService.GetLoginsAsync(tenantId, queryArguments);

                    return logins.Select(l => LoginGraph.ToGraph(l));
                });
        }

        static bool LoginWhereContainsEntityFilter(LoginWhere where)
        {
            if (where == null) return false;
            return where.Entity != null
                || where.And?.Any(LoginWhereContainsEntityFilter) == true
                || where.Or?.Any(LoginWhereContainsEntityFilter) == true;
        }

        async Task ReplaceEntityToEntityIdIn(string tenantId, LoginWhere where, IEntityService entityService)
        {
            if (where.Entity != null && !where.Entity.AreAllPropertiesDefault())
            {
                IEnumerable<string> ids = await entityService.GenericQueryIds(tenantId, where.Entity);
                where.EntityIds = ids;
                where.Entity = null;
            }
            if (where.And != null)
                foreach (LoginWhere item in where.And)
                    await ReplaceEntityToEntityIdIn(tenantId, item, entityService);
            if (where.Or != null)
                foreach (LoginWhere item in where.Or)
                    await ReplaceEntityToEntityIdIn(tenantId, item, entityService);
        }

        private static async Task<LoginWhere> BuildLoginFilterFromDeprecatedArguments(
            PermissionValidator permissionValidator,
            ResolveFieldContext<LoginsGraph> context)
        {
            IEnumerable<string> ids = context.Source.Where == null
                ? context.Source.Ids
                : context.Source.Where.Ids;
            IEnumerable<string> usernames = context.Source.Usernames;
            IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,"readLogins", ids);
            IEnumerable<EntityTypes> types = context.Source.UserTypes;
            bool? isEmailConfirmed = context.Source.IsEmailConfirmed;
            bool? isTelephoneNumberConfirmed = context.Source.IsTelephoneNumberConfirmed;
            bool? ExcludePermissions = context.Source.ExcludePermissions;

            var loginFilter = context.Source.Where ?? new LoginWhere
            {
                Usernames = usernames,
                EntityTypes = types?.Select(t => t.ToString()?.ToLower()),
                IsEmailConfirmed = isEmailConfirmed,
                IsTelephoneNumberConfirmed = isTelephoneNumberConfirmed,
                ExcludePermissions = ExcludePermissions
            };
            loginFilter.Ids = allowedTargetIds;

            return loginFilter;
        }

        public static async Task<LoginWhere> BuildLoginFilterFromDeprecatedArguments<T>(
            PermissionValidator permissionValidator,
            ResolveFieldContext<T> context,
            IEnumerable<string> ids = null,
            IEnumerable<string> usernames = null,
            IEnumerable<EntityTypes> types = null,
            bool? isEmailConfirmed = null,
            bool? ExcludePermissions = null,
            bool? isTelephoneNumberConfirmed = null)
        {
            IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,"readLogins", ids);

            var loginFilter = new LoginWhere { Ids = allowedTargetIds, Usernames = usernames, EntityTypes = types?.Select(t => t.ToString()?.ToLower()) };
            if (isEmailConfirmed != null)
                loginFilter.IsEmailConfirmed = isEmailConfirmed;

            if (isTelephoneNumberConfirmed != null)
                loginFilter.IsTelephoneNumberConfirmed = isTelephoneNumberConfirmed;

            if (ExcludePermissions != null)
                loginFilter.ExcludePermissions = ExcludePermissions;

            return loginFilter;
        }
    }

    public class LoginsGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<LoginGraph> List { get; set; }
        public List<string> Ids { get; set; }
        public List<string> PermissionGroupIds { get; set; }
        public IEnumerable<string> Usernames { get; set; }
        public IEnumerable<EntityTypes> UserTypes { get; set; }
        public bool? IsEmailConfirmed { get; set; }
        public bool? ExcludePermissions { get; set; } = false;
        public bool? IsTelephoneNumberConfirmed { get; set; }
        public int? Skip { get; set; }
        public int? Limit { get; set; }
        public SortGraph Sort { get; set; }
        public LoginWhere Where { get; set; }
    }

    public class LoginGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Username { get; set; }
        public string Email { get; set; }
        public bool IsEmailConfirmed { get; set; }
        public string TelephoneNumber { get; set; }
        public bool IsTelephoneNumberConfirmed { get; set; }
        public DateTime? LockoutEndDateUtc { get; set; }
        public int AccessFailedCount { get; set; }
        public IEnumerable<TargettedPermissionGraph> TargettedPermissions { get; set; }
        public IEnumerable<PermissionGroupGraph> PermissionGroups { get; set; }
        public IEnumerable<LoginGraph> InheritedLogins { get; set; }
        public EntityGraph AssociatedUser { get; set; }
        public PolicyUpdateRequestsGraph PolicyUpdateRequests { get; set; }
        public IEnumerable<EventLogGraph> Events { get; set; }
        public IReadOnlyCollection<TargetedPermissionSchemaGraph> TargetedPermissionSchemas { get; set; }
        public bool PermissionLazyLoadingRequired { get; set; }
        public bool IsActive { get; set; }
        public string EntityId { get; set; }
        public DateTime? PasswordLastUpdated { get; set; }

        public static LoginGraph ToGraph(Login l) =>
            l == null
                ? null
                : new LoginGraph
                {
                    Email = l.Email,
                    Id = l.Id,
                    IsEmailConfirmed = l.IsEmailConfirmed,
                    IsTelephoneNumberConfirmed = l.IsTelephoneNumberConfirmed,
                    TelephoneNumber = l.TelephoneNumber,
                    Username = l.Username,
                    LockoutEndDateUtc = l.LockoutEndDateUtc,
                    AccessFailedCount = l.AccessFailedCount,
                    TargettedPermissions = l.TargettedPermissions?.Select(tp => new TargettedPermissionGraph { Permission = new PermissionGraph { Id = tp.Key }, TargetIds = tp.Value }),
                    PermissionGroups = l.PermissionGroups.Select(pg => PermissionGroupGraph.ToGraph(pg)),
                    InheritedLogins = l.InheritedLoginIds.Select(il => new LoginGraph { Id = il }),
                    AssociatedUser = l.EntityId != null ? new EntityGraph { Id = l.EntityId } : null,
                    TargetedPermissionSchemas = l.TargetedPermissionSchemaIds?.Select(t => new TargetedPermissionSchemaGraph { Id = t }).ToList(),
                    PermissionLazyLoadingRequired = l.PermissionLazyLoadingRequired,
                    IsActive = l.IsActive,
                    EntityId = l.EntityId,
                    PasswordLastUpdated = l.PasswordLastUpdated
                }.PopulateSystemGraphFields(l);
    }

    public class LoginGraphType : ObjectGraphType<LoginGraph>
    {
        public LoginGraphType(
            IDataLoaderContextAccessor accessor,
            IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            IPolicyService policyService,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "login";
            Description = "A login";

            Field(c => c.Id);
            Field(c => c.Username);
            Field(c => c.Email, nullable: true);
            Field(c => c.TelephoneNumber, nullable: true);
            Field(c => c.IsEmailConfirmed);
            Field(c => c.IsTelephoneNumberConfirmed);
            Field(c => c.AccessFailedCount);
            Field(c => c.LockoutEndDateUtc, type: typeof(DateTimeOffsetGraphType));
            Field(c => c.AssociatedUser, type: typeof(EntityInterfaceGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.AssociatedUser?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var individualLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                        i => individualService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));
                    var internalLoader = accessor.Context.GetOrAddBatchLoader<string, Internal>("GetInternals",
                        i => internalService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));
                    var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>("GetCompanies",
                        i => companyService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));
                    var organizationLoader = accessor.Context.GetOrAddBatchLoader<string, Organization>("GetOrganizations",
                        i => organizationService.GetDictionaryAsync(tenantId, new EntityWhere { Id_in = i.ToList() }));

                    var individualTask = Task.FromResult<Individual>(null);
                    var internalTask = Task.FromResult<Internal>(null);
                    var companyTask = Task.FromResult<Company>(null);
                    var organizationTask = Task.FromResult<Organization>(null);

                    if (await permissionValidator.HasTarget(context,"readIndividuals", context.Source.AssociatedUser.Id)) individualTask = individualLoader.LoadAsync(context.Source.AssociatedUser.Id);
                    if (await permissionValidator.HasTarget(context,"readInternals", context.Source.AssociatedUser.Id)) internalTask = internalLoader.LoadAsync(context.Source.AssociatedUser.Id);
                    if (await permissionValidator.HasTarget(context,"readCompanies", context.Source.AssociatedUser.Id)) companyTask = companyLoader.LoadAsync(context.Source.AssociatedUser.Id);
                    if (await permissionValidator.HasTarget(context,"readOrganizations", context.Source.AssociatedUser.Id)) organizationTask = organizationLoader.LoadAsync(context.Source.AssociatedUser.Id);
                    await Task.WhenAll(individualTask, internalTask, companyTask, organizationTask);



                    return
                        individualTask.Result?.ToGraph() ??
                        GetCurrentUserIndividual().Result?.ToGraph() ??
                        internalTask.Result?.ToGraph() ??
                        companyTask.Result?.ToGraph() ??
                        organizationTask.Result?.ToGraph();

                    async Task<Individual> GetCurrentUserIndividual()
                    {
                        EntityTypes entityType = context.GetEntityTypeFromToken();

                        if (entityType == EntityTypes.Individual && individualTask?.Result == null && context.Source.AssociatedUser?.Id == context.GetEntityIdFromToken(isRequired:false))
                            return await individualService.GetSingleAsync(tenantId, context.Source.AssociatedUser?.Id);

                        return null;
                    }
                });

            Field(c => c.TargettedPermissions, type: typeof(ListGraphType<TargettedPermissionGraphType>));
            Field(c => c.PermissionGroups, type: typeof(ListGraphType<PermissionGroupGraphType>));

            Field(c => c.PolicyUpdateRequests, type: typeof(PolicyUpdateRequestsGraphType), nullable: true)
                .GetPaginationArguments()
                .DeprecationReason("use `endorsements` instead")
                .Argument<ListGraphType<StringGraphType>>("statusFilter", "the list of status to be filtered on.")
                .ResolveAsync(async context =>
                {
                    IEnumerable<string> statusFilter = context.GetArgument<object[]>("statusFilter")?.Select(o => o.ToString());

                    var requests = (await context.ResolveGetPolicyUpdateRequestsAsync(accessor,
                        policyService, permissionValidator, statusFilter)).ToList();

                    return new PolicyUpdateRequestsGraph
                    {
                        TotalCount = requests.Count,
                        List = requests.Sort(context)
                    };
                });

            Field(c => c.InheritedLogins, type: typeof(NonNullGraphType<ListGraphType<NonNullGraphType<LoginGraphType>>>))
                .ResolveAsync(async context =>
                {
                    if (!context.Source.InheritedLogins.Any())
                        return Enumerable.Empty<LoginGraph>();

                    string tenantId = context.GetTenantIdFromToken();

                    //var loader = accessor.Context.GetOrAddCollectionBatchLoader<string, Login>(
                    //    "GetLoginsCollection",
                    //    async i =>
                    //    {
                    //        IEnumerable<string> ids = i.SelectMany(comSepId => comSepId.Split(","));
                    //        IEnumerable<Login> logins = await authService.GetLoginsAsync(tenantId, new LoginWhere { Ids = ids });
                    //        ILookup<string, Login> lookup = logins.ToLookup(x => x.Key, v => v.Value);
                    //        return lookup;
                    //    });

                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readLogins", context.Source.InheritedLogins.Select(il => il.Id));

                    IEnumerable<Login> logins = await authService.GetLoginsAsync(tenantId, new LoginWhere { Ids = allowedIds });
                    //IEnumerable<Login> logins = await loader.LoadAsync(string.Join(',', allowedIds));

                    return logins?.Select(l => LoginGraph.ToGraph(l));
                });

            Field(c => c.Events, type: typeof(ListGraphType<EventLogGraphType>))
                .Argument<ListGraphType<StringGraphType>>("types", "the list of login event types to be filtered on.")
                .Argument<DateTimeOffsetGraphType>("fromDate", "The date events created from.")
                .Argument<DateTimeOffsetGraphType>("toDate", "The date events created to.")
                .Argument<StringGraphType>("groupBy", "The login event field to group by")
                .Argument<SortGraphType>("sort", "Sort login events")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    List<string> types = context.GetArgument<List<string>>("types");
                    DateTime? from = context.GetArgument<DateTime>("fromDate") == DateTime.MinValue ? null : context.GetArgument<DateTime>("fromDate");
                    DateTime? to = context.GetArgument<DateTime>("toDate") == DateTime.MinValue ? null : context.GetArgument<DateTime>("toDate");
                    string groupByField = context.GetArgument<string>("groupBy");
                    GroupBy groupByArg = string.IsNullOrWhiteSpace(groupByField) ? default : new() { FieldName = groupByField };
                    OrderBy orderByArg = context.GetArgument<SortGraph>("sort")?.ToOrderBy();

                    var dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, DetailedEventLog>(
                        "GetLoginEvents",
                        async i => (await authService.GetLoginEventsV2Async(tenantId,
                            new QueryArguments<LoginEventWhere>()
                            {
                                Where = new()
                                {
                                    Types = types,
                                    LoginIds = i?.ToList(),
                                    FromDate = from,
                                    ToDate = to
                                },
                                GroupBy = groupByArg,
                                OrderBy = orderByArg
                            })).ToLookup(e => e.RelatedId)
                    );
                    IEnumerable<DetailedEventLog> logs = await dataLoader.LoadAsync(context.Source.Id);

                    return logs.Select(EventLogGraph.ToGraph);
                });

            Field(c => c.TargetedPermissionSchemas, type: typeof(ListGraphType<TargetedPermissionSchemaGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var dataLoader = accessor.Context.GetOrAddBatchLoader<LoginGraph, IEnumerable<TargetedPermissionSchemaGraph>>(
                        "GetTargetedPermissionSchemas",
                        async logins =>
                        {
                            IReadOnlyCollection<string> targetedPermissionSchemaIds = logins
                                .SelectMany(l => l.TargetedPermissionSchemas?
                                    .Where(t => t.Id != null)
                                    .Select(t => t?.Id))
                                .Where(i => i != null)
                                .ToList();

                            IReadOnlyCollection<TargetedPermissionSchema> targetedPermissionSchemas =
                                await authService.GetTargetedPermissionSchemas(tenantId, targetedPermissionSchemaIds);

                            IEnumerable<PermissionSchema> permissionSchemas = await authService
                                .GetPermissionSchemas(tenantId, new QueryArguments { Where = new PermissionSchemaWhere { Id_in = targetedPermissionSchemas.Select(t => t.PermissionSchemaId) } });

                            IReadOnlyCollection<TargetedPermissionSchemaGraph> targetedPermissionSchemaGraphs
                                = targetedPermissionSchemas.Join(permissionSchemas, t => t.PermissionSchemaId, p => p.Id, (t, p) => new TargetedPermissionSchemaGraph
                                {
                                    Id = t.Id,
                                    TargetIds = t.TargetIds,
                                    PermissionSchema = p
                                }).ToArray();

                            return logins
                                .ToDictionary(
                                    i => i,
                                    i => i.TargetedPermissionSchemas.Join(
                                        targetedPermissionSchemaGraphs,
                                        ltps => ltps.Id,
                                        tps => tps.Id,
                                        (ltps, tps) => tps));
                        }
                    );

                    IEnumerable<TargetedPermissionSchemaGraph> targetedPermissionSchemas = await dataLoader.LoadAsync(context.Source);

                    return targetedPermissionSchemas.ToList();
                });

            Field(c => c.IsActive);
            Field(c => c.EntityId);
            Field(c => c.PasswordLastUpdated, type: typeof(DateTimeOffsetGraphType));

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public sealed class LoginWhereGraphType : InputObjectGraphType<LoginWhere>
    {
        public LoginWhereGraphType()
        {
            Name = "loginWhere";
            Description = "A login search filter";

            Field(l => l.And, type: typeof(ListGraphType<LoginWhereGraphType>), nullable: true);
            Field(l => l.Or, type: typeof(ListGraphType<LoginWhereGraphType>), nullable: true);
            Field(l => l.Ids, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            Field(l => l.EntityIds, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            Field(l => l.EntityTypes, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            Field(l => l.Usernames, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            Field(l => l.PermissionGroupIds, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            Field(l => l.Entity, type: typeof(EntityWhereInputGraphType), nullable: true);
            Field(l => l.IsEmailConfirmed, nullable: true);
            Field(l => l.IsTelephoneNumberConfirmed, nullable: true);
            Field(l => l.ExcludePermissions, nullable: true);
            Field(l => l.Email_in, nullable: true);
            Field(l => l.IsActive, nullable: true);

            this.PopulateSystemWhereFields();
        }
    }

    public class TargettedPermissionGraphType : ObjectGraphType<TargettedPermissionGraph>
    {
        public TargettedPermissionGraphType()
        {
            Name = "targettedPermission";
            Description = "A permission applied to a user or a target group";

            Field(c => c.Permission, type: typeof(PermissionGraphType));
            Field(c => c.TargetIds);
        }
    }

    public class TargetGroupGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public IEnumerable<EntityGraph> Users { get; set; }
        public IEnumerable<TargetGroupGraph> TargetGroups { get; set; }
    }

    public class TargetGroupGraphType : ObjectGraphType<TargetGroupGraph>
    {
        public TargetGroupGraphType()
        {
            Name = "targetGroup";
            Description = "Group of users to be targetted by a permission or a permission group";

            Field(p => p.Id);
            Field(p => p.Name, nullable: true);
            Field(p => p.Description, nullable: true);
            Field(p => p.Users, type: typeof(ListGraphType<EntityInterfaceGraphType>), nullable: true);
            Field(p => p.TargetGroups, type: typeof(ListGraphType<TargetGroupGraphType>), nullable: true);
        }
    }

    public class PermissionGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class PermissionGraphType : ObjectGraphType<PermissionGraph>
    {
        public PermissionGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService)
        {
            Name = "permission";
            Description = "A fundamental permission";

            Field(p => p.Id);
            Field(p => p.Name, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"permissions-{context.Source.Id}-name"));
            Field(p => p.Description, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"permissions-{context.Source.Id}-description"));
        }
    }

    public class PermissionGroupGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public IEnumerable<string> ProductTypes { get; set; }
        public IEnumerable<TargettedPermissionGraph> TargettedPermissions { get; set; }
        public IEnumerable<PermissionGroupGraph> PermissionGroups { get; set; }
        public IEnumerable<LoginGraph> Logins { get; set; }
        public IEnumerable<EventLogGraph> Events { get; set; }

        public static PermissionGroupGraph ToGraph(PermissionGroup domain, IEnumerable<PermissionGroup> permissionGroups = null) =>
            domain == null
                ? null
                : new PermissionGroupGraph
                {
                    Id = domain.Id,
                    Description = domain.Description,
                    Name = domain.Name,
                    ProductTypes = domain.ProductTypes,
                    TargettedPermissions = domain.TargettedPermissions?.Select(TargettedPermissionGraph.ToGraph),
                    PermissionGroups = permissionGroups?
                        .Where(a => domain.PermissionGroupIds?.Contains(a.Id) ?? false)
                        .Select(b => ToGraph(b, permissionGroups))
                }.PopulateSystemGraphFields(domain);
    }

    public class PermissionGroupWhereInputGraphType : InputObjectGraphType<PermissionGroupWhere>
    {
        public PermissionGroupWhereInputGraphType()
        {
            Name = "permissionGroupWhereInput";
            Description = "A permission group search filter";

            Field(a => a.Id, nullable: true);
            Field(a => a.Id_in, nullable: true);
            Field(a => a.Name, nullable: true);
            Field(a => a.Name_in, nullable: true);
        }
    }

    public class TargettedPermissionGraph
    {
        public PermissionGraph Permission { get; set; }
        public IEnumerable<string> TargetIds { get; set; }

        public static TargettedPermissionGraph ToGraph(KeyValuePair<string, IEnumerable<string>> domain) =>
            new() { Permission = new PermissionGraph { Id = domain.Key }, TargetIds = domain.Value };
    }

    public class PermissionGroupGraphType : ObjectGraphType<PermissionGroupGraph>
    {
        public PermissionGroupGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "permissionGroup";
            Description = "Group of permission that can target users or target groups";

            Field(p => p.Id);
            Field(p => p.Name);
            Field(p => p.Description, nullable: true);
            Field(p => p.ProductTypes, nullable: true);
            Field(p => p.TargettedPermissions, type: typeof(ListGraphType<TargettedPermissionGraphType>), nullable: true);
            Field(p => p.PermissionGroups, type: typeof(ListGraphType<PermissionGroupGraphType>), nullable: true);

            Field(p => p.Logins, type: typeof(ListGraphType<LoginGraphType>))
                .ResolveAsync(async context =>
                {
                    if (context.Source.Logins?.Any() ?? false)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loader = accessor.Context.GetOrAddCollectionBatchLoader<string, Login>("getLoginsByPermissionGroupIds",
                        i => authService.GetLoginsByPermissionGroupIdsLookupAsync(tenantId, i));

                    IEnumerable<Login> logins = await loader.LoadAsync(context.Source.Id);

                    return logins?.Select(l => LoginGraph.ToGraph(l));
                });

            Field(c => c.Events, type: typeof(ListGraphType<EventLogGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    var dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, EventLog>(
                        "GetPermissionGroupEvents",
                        async i => (await authService.GetPermissionGroupEventsAsync(tenantId, new EventQuery { Ids = i?.ToList() })).ToLookup(e => e.RelatedId)
                    );

                    IEnumerable<EventLog> logs = await dataLoader.LoadAsync(context.Source.Id);

                    return logs.Select(EventLogGraph.ToGraph);
                });

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class CreatePermissionGroupGraphType : InputObjectGraphType<CreatePermissionGroupCommand>
    {
        public CreatePermissionGroupGraphType()
        {
            Name = "createPermissionGroupInput";
            Description = "Input to create permission groups";

            Field(p => p.Name);
            Field(p => p.Description);
            Field(p => p.ProductTypes, nullable: true);
        }
    }

    public class UpdatePermissionGroupGraphType : InputObjectGraphType<UpdatePermissionGroupCommand>
    {
        public UpdatePermissionGroupGraphType()
        {
            Name = "updatePermissionGroupInput";
            Description = "Input to update permission groups";

            Field(p => p.Name);
            Field(p => p.Description);
            Field(p => p.ProductTypes, nullable: true);
        }
    }

    public class AppWhereInputGraphType : InputObjectGraphType<AppWhere>
    {
        public AppWhereInputGraphType()
        {
            Name = "appWhereInput";

            Field(a => a.Or, type: typeof(ListGraphType<AppWhereInputGraphType>));
            Field(a => a.And, type: typeof(ListGraphType<AppWhereInputGraphType>));
            Field(a => a.AppId, nullable: true);
            Field(a => a.AppId_in, nullable: true);

            this.PopulateSystemWhereFields();
        }
    }

    public class AppGraph : SystemObjectGraph
    {
        public string AppId { get; set; }
        public string AppName { get; set; }
        public List<string> RedirectUris { get; set; }
        public string Email { get; set; }
        public string EmailSenderName { get; set; }
        public int? AccessTokenLifetime { get; set; }
        public int? AbsoluteRefreshTokenLifetime { get; set; }
        public int? SlidingRefreshTokenLifetime { get; set; }
        public TimeSpan? EmailConfirmationTokenLifespan { get; set; }
        public UrlRouting UrlRouting { get; set; }
        public bool Requires2FA { get; set; }
        public bool UseNotificationConfig { get; set; }
        public IEnumerable<EventLogGraph> Events { get; set; }

        public string DefaultTimeZone { get; set; }
        public bool ActivationTokenExpiryDisabled { get; set; }
        public bool RequiresEmail2FA { get; set; }
        public string AppConfig { get; set; }
        public static AppGraph ToGraph(App domain) =>
            domain != null
                ? new AppGraph
                {
                    AppId = domain.AppId,
                    AppName = domain.AppName,
                    RedirectUris = domain.RedirectUris,
                    Email = domain.Email,
                    EmailSenderName = domain.EmailSenderName,
                    AccessTokenLifetime = domain.AccessTokenLifetime,
                    AbsoluteRefreshTokenLifetime = domain.AbsoluteRefreshTokenLifetime,
                    SlidingRefreshTokenLifetime = domain.SlidingRefreshTokenLifetime,
                    EmailConfirmationTokenLifespan = domain.EmailConfirmationTokenLifespan,
                    UrlRouting = domain.UrlRouting,
                    Requires2FA = domain.Requires2FA,
                    UseNotificationConfig = domain.UseNotificationConfig,
                    DefaultTimeZone = domain.DefaultTimeZone,
                    ActivationTokenExpiryDisabled = domain.ActivationTokenExpiryDisabled,
                    RequiresEmail2FA = domain.RequiresEmail2FA,
                    AppConfig = domain.AppConfig
                }.PopulateSystemGraphFields(domain)
                : null;
    }

    public class AppGraphType : ObjectGraphType<AppGraph>
    {
        public AppGraphType(
           IDataLoaderContextAccessor accessor,
           IAuthService authService, PermissionValidator permissionValidator)
        {
            Name = "app";

            Field(a => a.AppId);
            Field(a => a.Requires2FA);
            Field(a => a.AppName, nullable: true);
            Field(a => a.Email, nullable: true);
            Field(a => a.EmailSenderName, nullable: true);
            Field(a => a.RedirectUris, nullable: true);
            Field(a => a.AccessTokenLifetime, nullable: true);
            Field(a => a.AbsoluteRefreshTokenLifetime, nullable: true);
            Field(a => a.SlidingRefreshTokenLifetime, nullable: true);
            Field(a => a.EmailConfirmationTokenLifespan, nullable: true);
            Field(a => a.UrlRouting, type: typeof(UrlRoutingGraphType));
            Field(a => a.UseNotificationConfig, nullable: true);
            Field(a => a.DefaultTimeZone, nullable: true);
            Field(a => a.ActivationTokenExpiryDisabled, nullable: true);
            Field(a => a.RequiresEmail2FA, nullable: true);
            Field(a => a.AppConfig, nullable: true);
            Field(a => a.Events, type: typeof(ListGraphType<EventLogGraphType>))
                  .ResolveAsync(async context =>
                  {
                      string tenantId = context.GetTenantIdFromToken();

                      var dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, EventLog>(
                          "GetAppEvents",
                          async i => (await authService.GetAppEventsAsync(tenantId, new EventQuery { Ids = i?.ToList() })).ToLookup(e => e.RelatedId)
                      );

                      IEnumerable<EventLog> logs = await dataLoader.LoadAsync(context.Source.AppId);

                      return logs.Select(EventLogGraph.ToGraph);
                  });

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class UrlRoutingGraphType : ObjectGraphType<UrlRouting>
    {
        public UrlRoutingGraphType()
        {
            Name = "urlRouting";

            Field(u => u.Url, nullable: true);
            Field(u => u.RegexPattern, nullable: true);
            Field(u => u.Order, nullable: true);
        }
    }

    public class UrlRoutingInputGraphType : InputObjectGraphType<UrlRouting>
    {
        public UrlRoutingInputGraphType()
        {
            Name = "urlRoutingInput";

            Field(u => u.Url, nullable: true);
            Field(u => u.RegexPattern, nullable: true);
            Field(u => u.Order, nullable: true);
        }
    }

    public class ForgotPasswordEmailSettingsInputGraphType : InputObjectGraphType<ForgotPasswordEmailSettings>
    {
        public ForgotPasswordEmailSettingsInputGraphType()
        {
            Name = "forgotPasswordEmailSettingsInput";

            Field(u => u.From, nullable: false);
            Field(u => u.FromName, nullable: false);
            Field(u => u.TemplateId, nullable: false);
            Field(u => u.Subject, nullable: true);
            Field(u => u.Link, nullable: false);
        }
    }

    public class AppsGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<AppGraph> List { get; set; }
    }

    public class AppsGraphType : ObjectGraphType<AppsGraph>
    {
        public AppsGraphType(
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "apps";

            Field(a => a.TotalCount)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    AppWhere where = context.ComputeArgAndVar<AppWhere, AppsGraph>("where") ?? new AppWhere();

                    IEnumerable<string> allowedAppIds = await permissionValidator.GetTargetIdsFromClaim(context,"readApps");
                    if (!allowedAppIds.Contains("all"))
                        where = new AppWhere { And = new List<AppWhere> { where, new() { AppId_in = allowedAppIds.ToList() } } };

                    long totalCount = await authService.GetTotalCountAsync(tenantId, where);
                    return totalCount;
                });

            Field(c => c.List, type: typeof(ListGraphType<AppGraphType>))
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();

                  AppWhere where = context.ComputeArgAndVar<AppWhere, AppsGraph>("where") ?? new AppWhere();

                  IEnumerable<string> allowedAppIds = await permissionValidator.GetTargetIdsFromClaim(context,"readApps");
                  if (!allowedAppIds.Contains("all"))
                      where = new AppWhere { And = new List<AppWhere> { where, new() { AppId_in = allowedAppIds.ToList() } } };

                  int? skip = context.ComputeArgAndVar<int?, AppsGraph>("skip");
                  int? first = context.ComputeArgAndVar<int?, AppsGraph>("limit");
                  SortGraph sort = context.ComputeArgAndVar<SortGraph, AppsGraph>("sort");

                  OrderBy orderBy = null;
                  if (sort != null)
                  {
                      orderBy = sort.ToOrderBy();
                  }

                  var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy };
                  IEnumerable<App> apps = await authService.GetAppsAsync(tenantId, queryArguments);
                  return apps?.Select(a => AppGraph.ToGraph(a));
              });
        }
    }

    public class CreateAdminInputGraphType : InputObjectGraphType<CreateAdminCommand>
    {
        public CreateAdminInputGraphType()
        {
            Name = "createAdminInput";

            Field(a => a.Email);
            Field(a => a.Username);
            Field(a => a.Password);
        }
    }

    public class CreateAppInputGraphType : InputObjectGraphType<CreateAppCommand>
    {
        public CreateAppInputGraphType()
        {
            Name = "createAppInput";

            Field(a => a.AppId);
            Field(a => a.AppName);
            Field(a => a.Email, nullable: true);
            Field(a => a.EmailSenderName, nullable: true);
            Field(a => a.RedirectUris, nullable: true);
            Field(a => a.AccessTokenLifetime, nullable: true);
            Field(a => a.AbsoluteRefreshTokenLifetime, nullable: true);
            Field(a => a.SlidingRefreshTokenLifetime, nullable: true);
            Field(a => a.EmailConfirmationTokenLifespan, nullable: true);
            Field(a => a.UrlRouting, type: typeof(UrlRoutingInputGraphType));
            Field(a => a.Requires2FA, nullable: true);
            Field(a => a.UseNotificationConfig, nullable: true);
            Field(a => a.DefaultTimeZone, nullable: true);
            Field(a => a.ActivationTokenExpiryDisabled, nullable: true);
            Field(a => a.RequiresEmail2FA, nullable: true);
            Field(a => a.AppConfig, nullable: true);
            Field(a => a.ForgotPasswordEmailSettings, nullable: true,  type: typeof(ForgotPasswordEmailSettingsInputGraphType));
        }
    }

    public class UpdateAppInputGraphType : InputObjectGraphType<UpdateAppCommand>
    {
        public UpdateAppInputGraphType()
        {
            Name = "updateAppInput";

            Field(a => a.AppName, nullable: true);
            Field(a => a.Email, nullable: true);
            Field(a => a.EmailSenderName, nullable: true);
            Field(a => a.RedirectUris, nullable: true);
            Field(a => a.AccessTokenLifetime, nullable: true);
            Field(a => a.AbsoluteRefreshTokenLifetime, nullable: true);
            Field(a => a.SlidingRefreshTokenLifetime, nullable: true);
            Field(a => a.EmailConfirmationTokenLifespan, nullable: true);
            Field(a => a.UrlRouting, type: typeof(UrlRoutingInputGraphType));
            Field(a => a.Requires2FA, nullable: true);
            Field(a => a.UseNotificationConfig, nullable: true);
            Field(a => a.DefaultTimeZone, nullable: true);
            Field(a => a.ActivationTokenExpiryDisabled, nullable: true);
            Field(a => a.RequiresEmail2FA, nullable: true);
            Field(a => a.AppConfig, nullable: true);
            Field(a => a.ForgotPasswordEmailSettings, nullable: true,  type: typeof(ForgotPasswordEmailSettingsInputGraphType));
        }
    }

    public class PermissionSchemasGraphType : ObjectGraphType<PermissionSchemas>
    {
        public PermissionSchemasGraphType()
        {
            Name = "permissionSchemas";
            Field(a => a.TotalCount, nullable: true);
            Field(a => a.List, type: typeof(ListGraphType<PermissionSchemaGraphType>), nullable: true);
        }
    }

    public class PermissionSchemaGraphType : ObjectGraphType<PermissionSchema>
    {
        public PermissionSchemaGraphType()
        {
            Name = "permissionSchema";
            Field(a => a.Id, nullable: true);
            Field(a => a.Name, nullable: true);
            Field(a => a.Description, nullable: true);
            Field(a => a.ObjectType, nullable: false);
            Field(a => a.Schema, nullable: false, typeof(StringGraphType)).Resolve(context => context.Source.Schema?.ToString());
            Field(a => a.ActionType, type: typeof(PermissionSchemaActionTypeGraphType), nullable: false);
            Field(a => a.StateCondition, type: typeof(FieldsWhereGraphType), nullable: true);
            Field(a => a.UpdateCondition, type: typeof(FieldsWhereGraphType), nullable: true);
            Field(a => a.CreatedAt, type: typeof(DateTimeGraphType), nullable: true);
            Field(a => a.LastModifiedAt, type: typeof(DateTimeGraphType), nullable: true);
            Field(a => a.CreatedById, nullable: true);
            Field(a => a.LastModifiedById, nullable: true);
        }
    }

    public class CreatePermissionSchemaInputGraphType : InputObjectGraphType<CreatePermissionSchemaCommand>
    {
        public CreatePermissionSchemaInputGraphType()
        {
            Name = "createPermissionSchemaInput";
            Field(a => a.Name, nullable: true);
            Field(a => a.Description, nullable: true);
            Field(a => a.ObjectType, nullable: false);
            Field(a => a.ActionType, type: typeof(PermissionSchemaActionTypeGraphType), nullable: false);
            Field(a => a.Schema, nullable: false);
            Field(a => a.StateCondition, type: typeof(FieldsWhereInputGraphType), nullable: true);
            Field(a => a.UpdateCondition, type: typeof(FieldsWhereInputGraphType), nullable: true);
        }
    }

    public class UpdatePermissionSchemaInputGraphType : InputObjectGraphType<UpdatePermissionSchemaCommand>
    {
        public UpdatePermissionSchemaInputGraphType()
        {
            Name = "updatePermissionSchemaInput";
            Field(a => a.PermissionSchemaId, nullable: true);
            Field(a => a.Name, nullable: true);
            Field(a => a.Description, nullable: true);
            Field(a => a.ObjectType, nullable: true);
            Field(a => a.ActionType, type: typeof(PermissionSchemaActionTypeGraphType), nullable: true);
            Field(a => a.Schema, nullable: true);
            Field(a => a.StateCondition, type: typeof(FieldsWhereInputGraphType), nullable: true);
            Field(a => a.UpdateCondition, type: typeof(FieldsWhereInputGraphType), nullable: true);
        }
    }

    public class PermissionSchemaWhereInputGraphType : InputObjectGraphType<PermissionSchemaWhere>
    {
        public PermissionSchemaWhereInputGraphType()
        {
            Name = "permissionSchemaWhereInput";
            Description = "A permission schemas filter";

            Field(a => a.Or, type: typeof(ListGraphType<PermissionSchemaWhereInputGraphType>), nullable: true);
            Field(a => a.And, type: typeof(ListGraphType<PermissionSchemaWhereInputGraphType>), nullable: true);

            Field(a => a.Id, nullable: true);
            Field(a => a.Id_in, nullable: true);
            Field(a => a.Name, nullable: true);
            Field(a => a.ObjectType, nullable: true);
            Field(a => a.ActionType, type: typeof(PermissionSchemaActionTypeGraphType), nullable: true);
            Field(a => a.Schema, type: typeof(FieldsWhereInputGraphType), nullable: true);
        }
    }

    public class PermissionSchemas
    {
        public long TotalCount { get; set; }
        public IEnumerable<PermissionSchema> List { get; set; }
    }

    public class AddTargetedPermissionSchemaToLoginInput
    {
        [JsonRequired]
        public string LoginId { get; set; }

        [JsonRequired]
        public string PermissionSchemaId { get; set; }

        [JsonRequired]
        public IReadOnlyCollection<string> TargetIds { get; set; }
    }

    public class RemoveTargetedPermissionSchemaFromLoginInput
    {
        [JsonRequired]
        public string LoginId { get; set; }

        [JsonRequired]
        public string PermissionSchemaId { get; set; }

        public IReadOnlyCollection<string> TargetIds { get; set; }
    }

    public class TargetedPermissionSchemaGraphType : ObjectGraphType<TargetedPermissionSchemaGraph>
    {
        public TargetedPermissionSchemaGraphType()
        {
            Name = "targetedPermissionSchema";
            Field(f => f.TargetIds, nullable: false);
            Field(f => f.PermissionSchema, type: typeof(PermissionSchemaGraphType), nullable: false);
        }
    }

    public class AddTargetedPermissionSchemaToLoginInputGraphType : AutoInputObjectGraphType<AddTargetedPermissionSchemaToLoginInput> { }

    public class RemoveTargetedPermissionSchemaFromLoginInputGraphType : AutoInputObjectGraphType<RemoveTargetedPermissionSchemaFromLoginInput> { }

    public class PermissionSchemaActionTypeGraphType : EnumerationGraphType<PermissionSchemaActionType>
    {
        public PermissionSchemaActionTypeGraphType()
        {
            Name = "permissionSchemaActionType";
            Description = "A list of all possible actions covered for permission schemas, like read and write";
        }
    }

    public class UserStorageItemsWhereInputGraphType : InputObjectGraphType<UserStorageItemWhere>
    {
        public UserStorageItemsWhereInputGraphType()
        {
            Name = "userStorageItemsWhereInput";

            Field(x => x.And, nullable: true, type: typeof(ListGraphType<UserStorageItemsWhereInputGraphType>));
            Field(x => x.Or, nullable: true, type: typeof(ListGraphType<UserStorageItemsWhereInputGraphType>));

            Field(x => x.Key_in, nullable: true, type: typeof(ListGraphType<StringGraphType>));
            Field(x => x.Key_contains, nullable: true);
        }
    }


    public class UserStorageItemsGraphType : ObjectGraphType<UserStorageItems>
    {
        private readonly IAuthService _authService;

        public UserStorageItemsGraphType(IAuthService authService)
        {
            _authService = authService;

            Name = "userStorageItems";
            Description = "Stored items for current user";

            Field(x => x.Count)
                .Argument<UserStorageItemsWhereInputGraphType>("where", "filter")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    UserStorageItemWhere where = context.GetArgument<UserStorageItemWhere>("where");
                    var query = new QueryArguments<UserStorageItemWhere>
                    {
                        Where = where
                    };

                    Result<long> result = await _authService.UserStorageCountAsync(tenantId, loginId, query);
                    result.ThrowIfNotSuccess();

                    return result.Value;
                });

            Field(x => x.List, type: typeof(ListGraphType<UserStorageItemGraphType>))
                .Argument<UserStorageItemsWhereInputGraphType>("where", "filter")
                .GetPaginationArguments()
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    UserStorageItemWhere where = context.GetArgument<UserStorageItemWhere>("where");
                    GraphQLToolsExtensions.GetQueryArguments(context, out int? skip, out int? first, out OrderBy orderBy);

                    var query = new QueryArguments<UserStorageItemWhere>
                    {
                        Where = where,
                        Skip = skip,
                        First = first,
                        OrderBy = orderBy
                    };

                    Result<IReadOnlyCollection<UserStorageItem>> result = await _authService.UserStorageQueryAsync(tenantId, loginId, query);
                    result.ThrowIfNotSuccess();

                    return result.Value.ToList();
                });
        }
    }

    public class UserStorageItemGraphType : ObjectGraphType<UserStorageItem>
    {
        public UserStorageItemGraphType()
        {
            Name = "userStorageItem";
            Description = "Stored data item containing dynamic fields set up by FE";

            Field(x => x.Key, nullable: false);
            Field(x => x.Fields, nullable: true, type: typeof(StringGraphType)).Resolve(x => x.Source.Fields.ToString());
            Field(x => x.CreatedAt, nullable: true);
            Field(x => x.CreatedById, nullable: true);
            Field(x => x.LastModifiedById, nullable: true);
            Field(x => x.LastModifiedAt, nullable: true);
        }
    }

    public class UserStorageItemCreateInputGraphType : AutoInputObjectGraphType<CreateUserStorageItemCommand> { }

    public class UserStorageItemUpdateInputGraphType : AutoInputObjectGraphType<UpdateUserStorageItemCommand> { }

    public class AccessPolicyTypeEnumGraphType : EnumerationGraphType<AccessPolicy>
    {
        public AccessPolicyTypeEnumGraphType()
        {
            Name = "accessPolicyTypeEnum";
            Description = "A list of all possible AccessPolicy";
        }
    }
}
