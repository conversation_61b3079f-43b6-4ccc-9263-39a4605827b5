﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Auth
{
    public class CreatePreOtpLoginInputGraphType : InputObjectGraphType<CreatePreOtpLoginCommand>
    {
        public CreatePreOtpLoginInputGraphType()
        {
            Name = "createPreOtpLoginInput";
            Description = "Input to request a pre otp login token";

            Field(t => t.ClientId, nullable: false);
            Field(t => t.Email, nullable: false);
            Field(t => t.PhoneNumber, nullable: false);
        }
    }

    public class PreOtpLoginResultGraphType : ObjectGraphType<Result<PreOtpLogin>>
    {
        public PreOtpLoginResultGraphType()
        {
            Name = "preOtpLoginResult";
            Description = "Result of preOtpLogin creation with a token for future generation of otpLogin";

            Field<NonNullGraphType<StringGraphType>>("status");
            Field<ListGraphType<StringGraphType>>("errors");

            Field<PreOtpLoginGraphType>("preOtpLogin", resolve: ctx => ctx.Source.Value);
        }
    }

    public class PreOtpLoginGraphType : ObjectGraphType<PreOtpLogin>
    {
        public PreOtpLoginGraphType()
        {
            Name = "preOtpLogin";
            Description = "preOtpLogin with a Token for creating an otpLogin";

            Field(s => s.Token);
        }
    }

    public class CreateOtpLoginInputGraphType : InputObjectGraphType<CreateOtpLoginCommand>
    {
        public CreateOtpLoginInputGraphType()
        {
            Name = "createOtpLoginInput";
            Description = "Input to request an otpLogin, which sends an One Time Password by sms. A smsTemplateId is needed";

            Field(t => t.Token, nullable: false);
            Field(t => t.SmsTemplateId, nullable: true);
        }
    }

    public class OtpLoginResultGraphType : ObjectGraphType<Result<OtpLogin>>
    {
        public OtpLoginResultGraphType()
        {
            Name = "otpLoginResult";
            Description = "A token for otp login verification";

            Field<NonNullGraphType<StringGraphType>>("status");
            Field<ListGraphType<StringGraphType>>("errors");

            Field<OtpLoginGraphType>("otpLogin", resolve: ctx => ctx.Source.Value);
        }
    }

    public class OtpLoginGraphType : ObjectGraphType<OtpLogin>
    {
        public OtpLoginGraphType()
        {
            Name = "otpLogin";
            Description = "otpLogin with a Token to be verifed with the One Time Password for access token creation";

            Field(s => s.Token);
        }
    }

    public class CreateAccessTokenFromOtpLoginInputGraphType : InputObjectGraphType<CreateAccessTokenFromOtpLoginCommand>
    {
        public CreateAccessTokenFromOtpLoginInputGraphType()
        {
            Name = "createAccessTokenFromOtpLoginInput";
            Description = "Input to get an accessToken using otpLogin.Token and One Time Password";

            Field(t => t.Token, nullable: false);
            Field(t => t.OneTimePassword, nullable: false);
        }
    }

    public class TokenResultGraphType : ObjectGraphType<Result<Token>>
    {
        public TokenResultGraphType()
        {
            Name = "tokenResult";
            Description = "Access tokens";

            Field<NonNullGraphType<StringGraphType>>("status");
            Field<ListGraphType<StringGraphType>>("errors");

            Field<TokenGraphType>("token", resolve: ctx => ctx.Source.Value);
        }
    }
}
