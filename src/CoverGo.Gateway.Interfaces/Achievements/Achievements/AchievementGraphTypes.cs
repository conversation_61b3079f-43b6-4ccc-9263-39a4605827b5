﻿using CoverGo.Gateway.Domain.Achievements;
using CoverGo.Gateway.Domain.Achievements.AchievementTypes;
using CoverGo.Gateway.Domain.Achievements.Achievements;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Interfaces.Achievements.AchievementTypes;
using GraphQL.DataLoader;
using GraphQL.Types;
using System;
using System.Collections.Generic;
using System.Linq;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Interfaces.Achievements.Achievements
{
    public class AchievementsGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<AchievementGraph> List { get; set; }
    }


    public class AchievementsGraphType : ObjectGraphType<AchievementsGraph>
    {
        public AchievementsGraphType(IAchievementService achievementService, PermissionValidator permissionValidator)
        {
            Name = "achievements";
            Description = "Gets all achievements";

            Field(c => c.TotalCount)
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();

                   IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readAchievements");
                   AchievementWhere where = allowedIds.Contains("all")
                       ? context.ComputeArgAndVar<AchievementWhere, AchievementsGraph>("where") ?? new AchievementWhere()
                       : new AchievementWhere
                       {
                           And = new List<AchievementWhere>
                           {
                                context.ComputeArgAndVar<AchievementWhere, AchievementsGraph>("where") ?? new AchievementWhere(),
                                new() { Id_in = allowedIds.ToList() }
                           }
                       };

                   return await achievementService.GetAchievementsTotalCountAsync(tenantId, where);
               });

            Field(c => c.List, type: typeof(ListGraphType<AchievementGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readAchievements");
                    AchievementWhere where = allowedIds.Contains("all")
                        ? context.ComputeArgAndVar<AchievementWhere, AchievementsGraph>("where") ?? new AchievementWhere()
                        : new AchievementWhere
                        {
                            And = new List<AchievementWhere>
                            {
                                context.ComputeArgAndVar<AchievementWhere, AchievementsGraph>("where") ?? new AchievementWhere(),
                                new() { Id_in = allowedIds.ToList() }
                            }
                        };

                    int? skip = context.ComputeArgAndVar<int?, AchievementsGraph>("skip");
                    int? first = context.ComputeArgAndVar<int?, AchievementsGraph>("limit");
                    SortGraph sort = context.ComputeArgAndVar<SortGraph, AchievementsGraph>("sort");
                    DateTime? asOf = context.ComputeArgAndVar<DateTime?, AchievementsGraph>("asOf");

                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy, AsOf = asOf };
                    IEnumerable<Achievement> achievements = await achievementService.GetAchievementsAsync(tenantId, queryArguments);

                    return achievements.Select(c => AchievementGraph.ToGraph(c));
                });
        }
    }

    public class AchievementGraphType : ObjectGraphType<AchievementGraph>
    {

        public AchievementGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IAchievementService achievementService,
            PermissionValidator permissionValidator)
        {
            Name = "achievement";
            Description = "Achievement";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.Id);
            Field(p => p.AchievementType, type: typeof(AchievementTypeGraphType)).ResolveAsync(async context =>
            {
                if (context.Source.AchievementType == null) return null;
                IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,"readAchievementTypes");
                var andFilter = new List<AchievementTypeWhere> { };
                if (!allowedTargetIds.Contains("all"))
                    andFilter.Add(new AchievementTypeWhere { Id_in = allowedTargetIds?.ToList() });
                string tenantId = context.GetTenantIdFromToken();
                var loader = accessor.Context.GetOrAddBatchLoader<string, AchievementType>("GetAchievementTypeById",
                                        async i =>
                                        {
                                            andFilter.Add(new AchievementTypeWhere { Id_in = i?.ToList() });
                                            IEnumerable<AchievementType> s = await achievementService.GetAchievementTypesAsync(tenantId, new Domain.QueryArguments
                                            {
                                                Where = new AchievementTypeWhere { And = andFilter }
                                            });
                                            return s.ToDictionary(x => x.Id, x => x);
                                        });
                AchievementType achievementType = await loader.LoadAsync(context.Source.AchievementType.Id);
                if (achievementType == null) return null;
                else return AchievementTypeGraph.ToGraph(achievementType);
            });
        }
    }

    public class AchievementGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public AchievementTypeGraph AchievementType { get; set; }

        public static AchievementGraph ToGraph(Achievement domain) =>
            domain != null
            ? new AchievementGraph
            {
                Id = domain.Id,
                EntityId = domain.EntityId,
                AchievementType = new AchievementTypeGraph { Id = domain.AchievementTypeId },
            }.PopulateSystemGraphFields(domain)
            : null;
    }

    public class AchievementWhereInputGraphType : InputObjectGraphType<AchievementWhere>
    {
        public AchievementWhereInputGraphType()
        {
            Name = "achievementWhereInput";
            Description = "An achievement search filter";
            Field(f => f.Or, type: typeof(ListGraphType<AchievementWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<AchievementWhereInputGraphType>));
            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);
            Field(f => f.AchievementTypeId, nullable: true);
            Field(f => f.AchievementTypeId_in, nullable: true);
            Field(f => f.EntityId, nullable: true);
            Field(f => f.EntityId_in, nullable: true);
            this.PopulateSystemWhereFields();
        }
    }

    public class CreateAchievementInputGraphType : InputObjectGraphType<CreateAchievementCommand>
    {
        public CreateAchievementInputGraphType()
        {
            Name = "createAchievementInput";
            Description = "Create achievemen  input";
            Field(c => c.EntityId);
            Field(c => c.AchievementTypeId);
        }
    }

    public class UpdateAchievementInputGraphType : InputObjectGraphType<UpdateAchievementCommand>
    {
        public UpdateAchievementInputGraphType()
        {
            Name = "updateAchievementInput";
            Description = "update achievement  input";
            Field(c => c.EntityId, nullable: true);
            Field(c => c.AchievementTypeId, nullable: true);
        }
    }
}
