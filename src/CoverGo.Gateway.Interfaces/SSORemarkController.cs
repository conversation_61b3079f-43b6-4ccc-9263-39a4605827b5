using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.DomainUtils;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using CoverGo.Gateway.Infrastructure.BoclServices;
using CoverGo.Gateway.Common;
using Microsoft.Extensions.Configuration;

namespace CoverGo.Gateway.Interfaces.workspace.Controllers;

[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/v1/{tenantId}/SSORemark")]
public class SSORemarkController : ControllerBase
{
    private readonly ILogger<SSORemarkController> _logger;
    private readonly BoclIntegrationService _boclIntegrationService;
    private readonly IConfiguration _configuration;
    private const string encryptionKey = "REMARK_SSO_API_ENCRYPTION_KEY";

    public SSORemarkController(ILogger<SSORemarkController> logger,
        BoclIntegrationService boclIntegrationService,
        IConfiguration configuration)
    {
        _logger = logger;
        _boclIntegrationService = boclIntegrationService;
        _configuration = configuration;
    }

    [HttpPost("Login")]
    [AllowAnonymous]
    public async Task<ActionResult<Result<RemarkOutputParam>>> LoginAsync(string tenantId, [FromBody] RemarkInputParam request)
    {
        try
        {
            _logger.LogInformation("Received Remark SSO request for {tenantId} with {input}", tenantId, JsonConvert.SerializeObject(request));
            
            if (request == null || string.IsNullOrEmpty(request.EncryptedKey) || !tenantId.StartsWith("bocl"))
            {
                _logger.LogError("Invalid request data");
                return Result<RemarkOutputParam>.Failure("Invalid request data");
            }

            var permittedIpAddresses = _configuration.GetValue<string>("BOCL_REMARK_SERVER_IP_ADDRESS");
            if (!PermittedIpList.IsIncluded(permittedIpAddresses, HttpContext?.GetClientIpAddress()))
            {
                _logger.LogError("IP address {ipAddress} is not in {range}", HttpContext.Connection.RemoteIpAddress, permittedIpAddresses);
                return Result<RemarkOutputParam>.Failure("Login is not allowed from your location");
            }

            var key = Decrypt(GetEncryptionKey(), request.EncryptedKey);

            if (!key.Equals(request.ClientId, StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogError("Invalid decrypted content");
                return Result<RemarkOutputParam>.Failure("Invalid decrypted content");
            }

            if (string.IsNullOrWhiteSpace(request.UserId))
                return Result<RemarkOutputParam>.Failure($"Empty {nameof(request.UserId)}");
            if (string.IsNullOrWhiteSpace(request.ProductId))
                return Result<RemarkOutputParam>.Failure($"Empty {nameof(request.ProductId)}");

            Result<string> authTokenResult = await _boclIntegrationService.GetAuthToken(tenantId, request);
            Token token = JsonConvert.DeserializeObject<Token>(authTokenResult.Value);

            var output = new RemarkOutputParam
            {
                ErrorMessage = token.ErrorDescription,
                Success = token != null,
                Token = token
            };

            return token.AccessToken != null 
                ? Result<RemarkOutputParam>.Success(output) 
                : Result<RemarkOutputParam>.Failure(token.Error);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while processing Remark SSO request");
            return Result<RemarkOutputParam>.Failure("Auth failure");
        }
    }

    private string GetEncryptionKey() =>
        Environment.GetEnvironmentVariable(encryptionKey);

    private string Encrypt(string tenantId, string plainText)
    {
        try
        {
            string encryptionKey = GetEncryptionKey();
            byte[] cipherData;

            Aes aes = Aes.Create();
            aes.Key = Convert.FromBase64String(encryptionKey);
            aes.GenerateIV();
            aes.Mode = CipherMode.CBC;
            ICryptoTransform cipher = aes.CreateEncryptor(aes.Key, aes.IV);
            using (MemoryStream ms = new())
            {
                using (CryptoStream cs = new(ms, cipher, CryptoStreamMode.Write))
                {
                    using StreamWriter sw = new(cs);
                    sw.Write(plainText);
                }
                cipherData = ms.ToArray();
            }
            byte[] combinedData = new byte[aes.IV.Length + cipherData.Length];
            Array.Copy(aes.IV, 0, combinedData, 0, aes.IV.Length);
            Array.Copy(cipherData, 0, combinedData, aes.IV.Length, cipherData.Length);
            return Convert.ToBase64String(combinedData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[SSORemarkController] Encrypt Exception");
            return "";
        }
    }

    private string Decrypt(string decryptionKey, string combinedString)
    {
        try
        {
            _logger.LogInformation("LoginAsync Decryption Key:{decryptionKey}", decryptionKey);
            string decrypted;
            byte[] combinedData;
            try
            {
                combinedData = Convert.FromBase64String(combinedString);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error occurred while decrypting");
                combinedData = Encoding.ASCII.GetBytes(combinedString);
            }

            Aes aes = Aes.Create();
            aes.Key = Convert.FromBase64String(decryptionKey);
            byte[] iv = new byte[aes.BlockSize / 8];
            byte[] cipherText = new byte[combinedData.Length - iv.Length];
            Array.Copy(combinedData, iv, iv.Length);
            Array.Copy(combinedData, iv.Length, cipherText, 0, cipherText.Length);
            aes.IV = iv;
            aes.Mode = CipherMode.CBC;
            ICryptoTransform decipher = aes.CreateDecryptor(aes.Key, aes.IV);
            using MemoryStream ms = new(cipherText);
            using (CryptoStream cs = new(ms, decipher, CryptoStreamMode.Read))
            {
                using StreamReader sr = new(cs);
                decrypted = sr.ReadToEnd();
            }
            return decrypted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"[LoginAsync] Decrypt Exception on {combinedString}");
            return "";
        }
    }
}