using System;
using CoverGo.DomainUtils;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces
{
    public class ResultGraphType : ObjectGraphType<Result>
    {
        public ResultGraphType()
        {
            Name = "result";
            Description = "A result object with potential errors";

            Field<NonNullGraphType<StringGraphType>>("status", "'success' or 'failure'");
            Field<ListGraphType<NonNullGraphType<StringGraphType>>>("errors");
            Field(r => r.Errors_2, type: typeof(ListGraphType<NonNullGraphType<ErrorGraphType>>));
        }
    }

    public class ErrorGraphType : ObjectGraphType<Error>
    {
        public ErrorGraphType()
        {
            Name = "errors";
            Description = "Error codes and images";

            Field(e => e.Code, nullable: true);
            Field(e => e.Message, nullable: true);
        }
    }

    public class CreatedStatusResultGraphType : ObjectGraphType<Result<CreatedStatus>>
    {
        public CreatedStatusResultGraphType()
        {
            Name = "createdStatusResult";
            Description = "A result object with a created status object and potential errors";

            Field<StringGraphType>("status");
            Field<ListGraphType<NonNullGraphType<StringGraphType>>>("errors");
            Field<CreatedStatusGraphType>("createdStatus", resolve: ctx => ctx.Source.Value);
        }
    }

    public class CreatedStatusGraphType : ObjectGraphType<CreatedStatus>
    {
        public CreatedStatusGraphType()
        {
            Name = "createdStatus";
            Description = "A created status";

            Field<StringGraphType>("id", "The id of the created object");
            Field<ListGraphType<StringGraphType>>("ids", "The ids of the created objects");
        }
    }
    
    public class DateResultGraphType : ObjectGraphType<Result<DateTime>>
    {
        public DateResultGraphType()
        {
            Name = "dateResult";
            Description = "A result object with a datetime and potential errors";

            Field<StringGraphType>("status");
            Field<ListGraphType<NonNullGraphType<StringGraphType>>>("errors");
            Field<DateGraphType>("date", resolve: ctx => ctx.Source.Value);
        }
    }

    public class IntResultGraphType : ObjectGraphType<Result<int>>
    {
        public IntResultGraphType()
        {
            Name = "intResult";
            Description = "A result object with a Int32 value and potential errors";

            Field<StringGraphType>("status");
            Field<ListGraphType<NonNullGraphType<StringGraphType>>>("errors");
            Field<IntGraphType>("value", resolve: ctx => ctx.Source.Value);
        }
    }
}
