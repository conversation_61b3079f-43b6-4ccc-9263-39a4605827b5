﻿using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Interfaces.Auth;

using GraphQL.DataLoader;
using GraphQL.Types;
using Newtonsoft.Json.Linq;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using CoverGo.DomainUtils;
using CurrencyCode = CoverGo.Gateway.Domain.CurrencyCode;
using Range = CoverGo.Users.Domain.Individuals.Range;
using SystemObject = CoverGo.Gateway.Domain.SystemObject;

namespace CoverGo.Gateway.Interfaces
{
    public class RangeGraphType : ObjectGraphType<Range>
    {
        public RangeGraphType()
        {
            Name = "range";
            Description = "A range";

            Field(r => r.Minimum, nullable: true);
            Field(r => r.Maximum, nullable: true);
            Field(r => r.ExclusiveMinimum, nullable: true);
            Field(r => r.ExclusiveMaximum, nullable: true);
        }
    }

    public class RangeInputGraphType : InputObjectGraphType<Range>
    {
        public RangeInputGraphType()
        {
            Name = "rangeInput";
            Description = "Input for a range";

            Field(r => r.Minimum, nullable: true);
            Field(r => r.Maximum, nullable: true);
            Field(r => r.ExclusiveMinimum, nullable: true);
            Field(r => r.ExclusiveMaximum, nullable: true);
        }
    }

    public class ScalarValueGraphType : ObjectGraphType<ScalarValue>
    {
        public ScalarValueGraphType()
        {
            Name = "scalarValue";
            Description = "A representation of a scalar value object";

            Field(d => d.StringValue, nullable: true);
            Field(d => d.BooleanValue, nullable: true);
            Field(d => d.NumberValue, nullable: true);
            Field(c => c.DateValue, type: typeof(DateTimeGraphType), nullable: true);
            Field(c => c.ArrayValue, type: typeof(ListGraphType<ScalarValueGraphType>), nullable: true);
            Field(c => c.ObjectValue, type: typeof(ListGraphType<KeyValueGraphType>));
        }
    }

    public class ScalarValueInputGraphType : InputObjectGraphType<ScalarValue>
    {
        public ScalarValueInputGraphType()
        {
            Name = "scalarValueInput";
            Description = "A representation of a sclar value object";

            Field(d => d.StringValue, nullable: true);
            Field(d => d.BooleanValue, nullable: true);
            Field(d => d.NumberValue, nullable: true);
            Field(d => d.DoubleValue, nullable: true);
            Field(c => c.DateValue, type: typeof(DateTimeGraphType), nullable: true);
            Field(c => c.ArrayValue, type: typeof(ListGraphType<ScalarValueInputGraphType>), nullable: true);
            Field(c => c.ObjectValue, type: typeof(ListGraphType<KeyValueInputGraphType>));
        }
    }

    public class KeyValueGraphType : ObjectGraphType<KeyScalarValue>
    {
        public KeyValueGraphType()
        {
            Name = "keyValue";

            Field(kv => kv.Key);
            Field(kv => kv.Value, type: typeof(ScalarValueGraphType));
        }
    }

    public class KeyValueInputGraphType : InputObjectGraphType<KeyScalarValue>
    {
        public KeyValueInputGraphType()
        {
            Name = "keyValueInput";

            Field(kv => kv.Key);
            Field(kv => kv.Value, type: typeof(ScalarValueInputGraphType));
        }
    }

    public class DatedAmountInputGraph
    {
        public int Year { get; set; }
        public decimal Amount { get; set; }
    }

    public static class KeyValueExtensions
    {
        public static ScalarValue ToScalarValue(this JToken jtoken)
        {
            var returnedValue = new ScalarValue();
            switch (jtoken.Type)
            {
                case JTokenType.Null: break;
                case JTokenType.Date: returnedValue.DateValue = jtoken.Value<DateTime>(); break;
                case JTokenType.String: returnedValue.StringValue = jtoken.Value<string>(); break;
                case JTokenType.Integer: returnedValue.NumberValue = jtoken.Value<decimal>(); break;
                case JTokenType.Float: returnedValue.NumberValue = jtoken.Value<decimal>(); break;
                case JTokenType.Boolean: returnedValue.BooleanValue = jtoken.Value<bool>(); break;
                case JTokenType.Array: returnedValue.ArrayValue = jtoken.Select(jv => jv.ToScalarValue()).ToList(); break;
                case JTokenType.Object: returnedValue.ObjectValue = jtoken.Children<JProperty>().Select(prop => new KeyScalarValue { Key = prop.Name, Value = prop.Value.ToScalarValue() }).ToList(); break;


                default: break;
            }

            return returnedValue;
        }
    }

    public class SortGraphType : InputObjectGraphType<SortGraph>
    {
        public SortGraphType()
        {
            Name = "sortInput";
            Description = "Sort items";

            Field(s => s.FieldName).Description("The field name onto which to sort");
            Field(s => s.Type).Description("'asc' or 'desc'");
        }
    }
    public class GroupByGraphType : InputObjectGraphType<GroupByGraph>
    {
        public GroupByGraphType()
        {
            Name = "groupInput";
            Description = "Group items";

            Field(s => s.FieldName).Description("The field name onto which to group");
           
        }
    }

    public class SortGraph
    {
        public string FieldName { get; set; }
        public string Type { get; set; }
    }
    public class GroupByGraph
    {
        public string FieldName { get; set; }
    }

    public static class GroupGraphExtensions
    {
        public static GroupBy ToGroupBy(this GroupByGraph? group)
        {
            if (group == null)
            {
                return null;
            }
            return new GroupBy() { FieldName = group.FieldName };
        }
    }

    public static class SortGraphExtensions
    {
        public static OrderBy ToOrderBy(this SortGraph? sort)
        {
            if (sort == null)
            {
                return null;
            }

            if (sort.Type.Equals("desc", StringComparison.OrdinalIgnoreCase))
            {
                sort.Type = "dsc";
            }

            var orderBy = new OrderBy
            {
                Type = Enum.Parse<OrderByType>(sort.Type, true),
                FieldName = sort.FieldName
            };
            return orderBy;
        }
    }

    public static class GraphQLExtensions
    {
        public static IEnumerable<T> Sort<T, U>(this IEnumerable<T> list, ResolveFieldContext<U> context, SortGraph defaultSort = null)
        {
            SortGraph sort = context.GetArgument<SortGraph>("sort") ?? defaultSort;
            if (sort != null)
                try { list = list.AsQueryable().OrderBy($"{IgnoreIfNull(sort.FieldName)} {sort.Type}"); }
                catch { }

            int? skip = context.GetArgument<int?>("skip");
            if (skip.HasValue)
                list = list.Skip(skip.Value);

            int? limit = context.GetArgument<int?>("limit") ?? context.GetArgument<int?>("first");
            if (limit.HasValue)
                list = list.Take(limit.Value);

            return list;
        }

        private static string IgnoreIfNull(string fieldNames)
        {
            string[] splits = fieldNames.Split('.');
            for (int i = 0; i < splits.Count() - 1; i++)
            {
                string toBeReplaced = string.Join('.', splits.Take(i + 1));
                fieldNames = fieldNames.Replace(toBeReplaced, $"{toBeReplaced} == null ? null : ({toBeReplaced}") + ")";
            }

            return fieldNames;
        }
    }

    public class CurrencyCodeEnumerationGraphType : EnumerationGraphType<CurrencyCode>
    {
        public CurrencyCodeEnumerationGraphType()
        {
            Name = "currencyCodeEnumeration";
            Description = "A list of all possible currency codes";
        }
    }

    public abstract class SystemObjectGraph
    {
        public DateTime? CreatedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }
        public LoginGraph CreatedBy { get; set; }
        public LoginGraph LastModifiedBy { get; set; }
    }

    public static class SystemGraphExtensions
    {
        public static T PopulateSystemGraphFields<T>(this T graph, SystemObject domain) where T : SystemObjectGraph
        {
            graph.CreatedBy = domain.CreatedById != null ? new LoginGraph { Id = domain.CreatedById } : null;
            graph.LastModifiedBy = domain.LastModifiedById != null ? new LoginGraph { Id = domain.LastModifiedById } : null;
            graph.CreatedAt = domain.CreatedAt;
            graph.LastModifiedAt = domain.LastModifiedAt;

            return graph;
        }

        public static void PopulateSystemGraphTypeFields<T>(
            this ComplexGraphType<T> graphType,
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
            where T : SystemObjectGraph
        {
            graphType.Field(c => c.CreatedBy, type: typeof(LoginGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.CreatedBy?.Id == null)
                        return null;

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,
                        "readLogins", new List<string> { context.Source.CreatedBy.Id });
                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.CreatedBy.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                        async i => (await authService.GetLoginsAsync(tenantId, new LoginWhere { Ids = i, ExcludePermissions = true })) // No case where permissions are needed in created by 
                            .ToDictionary(x => x.Id, x => x));

                    Login loginDao = await loginLoader.LoadAsync(context.Source.CreatedBy.Id);

                    return LoginGraph.ToGraph(loginDao);
                });

            graphType.Field(c => c.LastModifiedBy, type: typeof(LoginGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.LastModifiedBy?.Id == null)
                        return null;

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context,
                        "readLogins", new List<string> { context.Source.LastModifiedBy?.Id });
                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.LastModifiedBy?.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                        i => authService.GetLoginsDictionaryAsync(tenantId, i)); // No case where permissions are needed in created by

                    Login loginDao = await loginLoader.LoadAsync(context.Source.LastModifiedBy?.Id);

                    return LoginGraph.ToGraph(loginDao);
                });

            graphType.Field(p => p.CreatedAt, type: typeof(DateTimeGraphType), nullable: true);
            graphType.Field(p => p.LastModifiedAt, type: typeof(DateTimeGraphType), nullable: true);
        }

        public static void PopulateSystemWhereFields<T>(this ComplexGraphType<T> graphType)
             where T : Where
        {
            graphType.Field(a => a.LastModifiedAt_gt, type: typeof(DateTimeOffsetGraphType), nullable: true);
            graphType.Field(a => a.LastModifiedAt_lt, type: typeof(DateTimeOffsetGraphType), nullable: true);
            graphType.Field(a => a.CreatedAt_lt, type: typeof(DateTimeOffsetGraphType), nullable: true);
            graphType.Field(a => a.CreatedAt_gt, type: typeof(DateTimeOffsetGraphType), nullable: true);
            graphType.Field(a => a.LastModifiedById, nullable: true);
            graphType.Field(a => a.LastModifiedById_in, nullable: true);
            graphType.Field(a => a.LastModifiedById_contains, nullable: true);
            graphType.Field(a => a.CreatedById, nullable: true);
            graphType.Field(a => a.CreatedById_in, nullable: true);
            graphType.Field(a => a.CreatedById_contains, nullable: true);
        }
    }
}
