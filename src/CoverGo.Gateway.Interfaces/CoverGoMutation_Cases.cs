using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.FeatureManagement;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Binder;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Cases;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Users;
using GraphQL.Authorization;
using GraphQL.Types;
using GraphQL.Validation;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeCasesMutations(
            ITemplateService templateService,
            IAuthService authService,
            ICaseService caseService,
            IPolicyService policyService,
            IProductService productService,
            IEntityService<Internal, CreateInternalCommand,
            UpdateInternalCommand> internalService,
            PermissionValidator permissionValidator,
            IMultiTenantFeatureManager multiTenantFeatureManager)
        {
            Field<CreatedStatusResultGraphType>()
                .Name("createCase")
                .Description("creates a case")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateCaseInputGraphType>>("input", "the input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    CreateCaseCommand command = context.GetArgument<CreateCaseCommand>("input");
                    command.CreatedById = loginId;

                    await permissionValidator.Authorize(context, new PermissionRequest("createCases", "writeCases"));

                    Result<string> result = await caseService.CreateCaseAsync(tenantId, command);

                    if (await permissionValidator.HasCreatorRights(context))
                        await authService.AddTargettedPermissionsAsync(tenantId, loginId, new List<AddTargettedPermissionCommand>
                        {
                            new() { AddedById = loginId, Type = "writeCases", Value = result.Value },
                            new() { AddedById = loginId, Type = "readCases", Value = result.Value }
                        });

                    return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
                });

            Field<ResultGraphType>()
                .Name("updateCase")
                .Description("updates a case")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the case identifier")
                .Argument<NonNullGraphType<UpdateCaseInputGraphType>>("input", "the updated case")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    string id = context.GetArgument<string>("id");

                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(id));

                    UpdateCaseCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateCaseCommand>();

                    if (command.FieldsPatch != null && !await context.IsJsonPatchPermitted(authService, command.FieldsPatch, id, "case"))
                        throw new ValidationError("", "authorization", $"{tenantId}: You are not authorized to patch these fields" + command.FieldsPatch);

                    command.ModifiedById = loginId;

                    return await caseService.UpdateCaseAsync(tenantId, id, command);
                });

            Field<ResultGraphType>()
                .Name("deleteCase")
                .Description("deletes a case")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the identifier of the case")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string id = context.GetArgument<string>("id");

                    await permissionValidator.Authorize(context, new PermissionRequest("deleteCases", "writeCases").WithTargetIds(id));

                    var command = new DeleteCaseCommand
                    {
                        DeletedById = loginId
                    };

                    return await caseService.DeleteCaseAsync(tenantId, id, command);
                });


            Field<CreatedStatusResultGraphType>()
                .Name("addProposal")
                .Description("adds a proposal")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<AddProposalInputGraphType>>("input", "the input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    await permissionValidator.Authorize(context, "writeProposals");

                    AddProposalCommand command = context.GetArgument<AddProposalCommand>("input");
                    command.AddedById = loginId;

                    if (command.ReferralCode != null)
                    {
                        Internal referrer = (await internalService.GetAsync(tenantId, new InternalWhere { InternalCode = command.ReferralCode, IsActive = true })).FirstOrDefault();
                        if (referrer == null)
                            command.ReferralCode = null;
                    }

                    var userContext = (GraphQLUserContext)context.UserContext;
                    Result<string> result = await caseService.AddProposalAsync(tenantId, caseId, command, Tools.GetAccessToken(userContext));

                    if (await permissionValidator.HasCreatorRights(context))
                        await authService.AddTargettedPermissionsAsync(tenantId, loginId, new List<AddTargettedPermissionCommand>
                        {
                            new() { AddedById = loginId, Type = "writeProposals", Value = result.Value },
                            new() { AddedById = loginId, Type = "readProposals", Value = result.Value }
                        });

                    return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
                });

            Field<CreatedStatusResultGraphType>()
                .Name("copyProposal")
                .Description("copies a proposal")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<CopyProposalInputGraphType>>("input", "the input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    await permissionValidator.Authorize(context, "writeProposals");

                    CopyProposalCommand command = context.GetArgument<CopyProposalCommand>("input");
                    command.CopiedById = loginId;

                    Result<string> result = await caseService.CopyProposalAsync(tenantId, caseId, command);

                    return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
                });

            Field<CreatedStatusResultGraphType>()
                .Name("renewProposal")
                .Description("renews a proposal")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<RenewProposalInputGraphType>>("input", "the input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    await permissionValidator.Authorize(context, "writeProposals");

                    RenewProposalCommand command = context.GetArgument<RenewProposalCommand>("input");
                    command.RenewedById = loginId;

                    var userContext = (GraphQLUserContext)context.UserContext;
                    Result<string> result = await caseService.RenewProposalAsync(tenantId, caseId, command, Tools.GetAccessToken(userContext));

                    return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
                });

            Field<ResultGraphType>()
                .Name("issueProposal")
                .Description("issues a proposal")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the identifier of the case")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the identifier of the proposal")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    await permissionValidator.Authorize(context, "issueProposals");

                    var command = new IssueProposalCommand
                    {
                        ProposalId = proposalId,
                        IssuedById = loginId
                    };

                    return await caseService.IssueProposalAsync(tenantId, caseId, command);
                });


            Field<ResultGraphType>()
                .Name("rejectProposal")
                .Description("rejects a proposal")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the identifier of the case")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the identifier of the proposal")
                .Argument<ListGraphType<StringGraphType>>("codes", "the rejection codes for the proposal")
                .Argument<StringGraphType>("remarks", "the rejection remarks")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    IEnumerable<string> codes = context.GetArgument<IEnumerable<string>>("codes");
                    string remarks = context.GetArgument<string>("remarks");
                    await permissionValidator.Authorize(context, "writeProposals", proposalId);

                    var command = new RejectProposalCommand
                    {
                        ProposalId = proposalId,
                        Codes = codes,
                        Remarks = remarks,
                        RejectedById = loginId
                    };

                    return await caseService.RejectProposalAsync(tenantId, caseId, command);
                });

            Field<ResultGraphType>()
                .Name("updateProposal")
                .Description("updates a proposal")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the identifier of the case")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the identifier of the proposal")
                .Argument<NonNullGraphType<UpdateProposalInputGraphType>>("input", "the updated proposal")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    await permissionValidator.Authorize(context, "writeProposals", proposalId);

                    UpdateProposalCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateProposalCommand>();
                    command.ProposalId = proposalId;
                    command.ModifiedById = loginId;


                    return await caseService.UpdateProposalAsync(tenantId, caseId, command);
                });

            Field<ResultGraphType>()
                .Name("removeProposal")
                .Description("removes a proposal")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the identifier of the case")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the identifier of the proposal")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    await permissionValidator.Authorize(context, "writeProposals", proposalId);

                    var command = new RemoveCommand
                    {
                        Id = proposalId,
                        RemovedById = loginId
                    };


                    return await caseService.RemoveProposalAsync(tenantId, caseId, command);
                });

            Field<CreatedStatusResultGraphType>()
                .Name("addOfferToProposal")
                .Description("adds an offer to a proposal")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the identifier of the case")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the identifier of the case")
                .Argument<NonNullGraphType<AddOfferInputGraphType>>("input", "the offer input")
                .ResolveAsync(async context =>
                {
                    string proposalId = context.GetArgument<string>("proposalId");
                    string tenantId = context.GetTenantIdFromToken();
                    var isProposalIdIfStakeholderPermissionEnabled = await multiTenantFeatureManager.IsEnabled("ProposalIdIfStakeholderPermission", tenantId);
                    if (isProposalIdIfStakeholderPermissionEnabled)
                    {
                        await permissionValidator.Authorize(context, "writeProposals", proposalId);
                    }
                    else
                    {
                        await permissionValidator.Authorize(context, "writeProposals");
                    }
                    string caseId = context.GetArgument<string>("caseId");

                    AddOfferInputGraph input = context.GetArgument<AddOfferInputGraph>("input");

                    var command = new AddOfferCommand
                    {
                        Status = input.Status,
                        OfferNumberType = input.OfferNumberType,
                        OfferNumber = input.OfferNumber,
                        PolicyNumber = input.PolicyNumber,
                        ProductId = input.ProductId,
                        Premium = input.Premium,
                        Values = input.Values != null ? JToken.FromObject(input.Values?.ToDictionary(x => x.Key, x => x.Value.GetValue())) : null,
                        BenefitOptions = input.BenefitOptions?.Select(b =>
                         new Domain.Pricing.BenefitOption
                         {
                             TypeId = b.TypeId,
                             Key = b.Key,
                             Value = b.Value != null ? JToken.FromObject(b.Value?.GetValue()) : null,
                             InsuredId = b.InsuredId,
                         })?.ToList(),
                        StartDate = input.StartDate,
                        EndDate = input.EndDate,
                        ContractId = input.ContractId,
                        Pricing = input.Pricing,
                        Underwriting = input.Underwriting,
                        Fields = input.Fields,
                        FieldsSchemaId = input.FieldsSchemaId,
                        ProductTreeId = input.ProductTreeId,
                        ProductTreeRecords = input.ProductTreeRecords,
                        DistributorID = input.DistributorID,
                        CampaignCodes = input.CampaignCodes,
                    };

                    if (command.Premium?.Amount != null || command.Premium?.GrossAmount != null || command.Premium?.CurrencyCode != null)
                    {
                        await permissionValidator.Authorize(context, "overrideOffers");
                        command.IsPremiumOverridden = true;
                    }

                    string loginId = context.GetLoginIdFromToken();
                    command.ProposalId = proposalId;
                    command.AddedById = loginId;

                    var userContext = (GraphQLUserContext)context.UserContext;
                    Result<string> result = await caseService.AddOfferAsync(tenantId, caseId, command, Tools.GetAccessToken(userContext));

                    return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
                });

            Field<ResultGraphType>()
               .Name("updateOfferOfProposal")
               .Description("updates an offer to a proposal")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<StringGraphType>>("caseId", "the identifier of the case")
               .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the identifier of the proposal")
               .Argument<NonNullGraphType<StringGraphType>>("offerId", "the identifier of the offer")
               .Argument<NonNullGraphType<UpdateOfferGraphType>>("updateOfferInput", "the information of the offer to update")
               .ResolveAsync(async context =>
               {
                   string proposalId = context.GetArgument<string>("proposalId");
                   await permissionValidator.Authorize(context, "writeProposals", proposalId);

                   string offerId = context.GetArgument<string>("offerId");

                   string tenantId = context.GetTenantIdFromToken();
                   string caseId = context.GetArgument<string>("caseId");

                   UpdateOfferInputGraph input = context.GetArgument<Dictionary<string, object>>("updateOfferInput").ToUpdateCommand<UpdateOfferInputGraph>();

                   var command = new UpdateOfferCommand
                   {
                       PolicyNumber = input.PolicyNumber,
                       IsPolicyNumberChanged = input.IsPolicyNumberChanged,
                       Status = input.Status,
                       IsStatusChanged = input.IsStatusChanged,
                       Premium = input.Premium,
                       IsPremiumChanged = input.IsPremiumChanged,
                       ProductId = input.ProductId,
                       IsProductIdChanged = input.IsProductIdChanged,
                       IsValuesChanged = input.IsValuesChanged,
                       Values = input.Values != null ? JToken.FromObject(input.Values?.ToDictionary(x => x.Key, x => x.Value.GetValue())) : null,
                       IsStartDateChanged = input.IsStartDateChanged,
                       StartDate = input.StartDate,
                       IsEndDateChanged = input.IsEndDateChanged,
                       EndDate = input.EndDate,
                       ContractId = input.ContractId,
                       IsContractIdChanged = input.IsContractIdChanged,
                       Pricing = input.Pricing,
                       IsPricingChanged = input.IsPricingChanged,
                       Underwriting = input.Underwriting,
                       IsUnderwritingChanged = input.IsUnderwritingChanged,
                       Fields = input.Fields,
                       IsFieldsChanged = input.IsFieldsChanged,
                       FieldsSchemaId = input.FieldsSchemaId,
                       IsFieldsSchemaIdChanged = input.IsFieldsSchemaIdChanged,
                       ProductTreeId = input.ProductTreeId,
                       IsProductTreeIdChanged = input.IsProductTreeIdChanged,
                       DistributorID = input.DistributorID,
                       IsDistributorIDChanged = input.IsDistributorIDChanged,
                       CampaignCodes = input.CampaignCodes,
                       IsCampaignCodesChanged = input.IsCampaignCodesChanged,
                   };

                   command.ProposalId = proposalId;
                   command.OfferId = offerId;
                   command.ModifiedById = context.GetLoginIdFromToken();

                   if (command.IsPremiumChanged)
                   {
                       await permissionValidator.Authorize(context, "overrideOffers", caseId);
                       command.IsPremiumOverridden = true;
                   }

                   return await caseService.UpdateOfferAsync(tenantId, caseId, command);
               });


            Field<ResultGraphType>()
               .Name("removeOfferFromProposal")
               .Description("removes an offer of a proposal")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<StringGraphType>>("caseId", "the identifier of the case")
               .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the identifier of the proposal")
               .Argument<NonNullGraphType<StringGraphType>>("offerId", "the identifier of the offer")
               .ResolveAsync(async context =>
               {
                   string offerId = context.GetArgument<string>("offerId");

                   string tenantId = context.GetTenantIdFromToken();
                   string caseId = context.GetArgument<string>("caseId");
                   string proposalId = context.GetArgument<string>("proposalId");

                   await permissionValidator.Authorize(context, "writeProposals", proposalId);

                   Result policyResult = await caseService.RemoveOfferAsync(tenantId, caseId, new RemoveOfferFromProposalCommand
                   {
                       ProposalId = proposalId,
                       OfferId = offerId,
                       RemovedById = context.GetLoginIdFromToken()
                   });

                   return policyResult;
               });

            Field<CreatedStatusResultGraphType>()
                .Name("addFactToCase")
                .Description("adds a fact to a case")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<AddFactInputGraphType>>("input", "the fact to be added")
                .ResolveAsync(async context =>
                {
                    string caseId = context.GetArgument<string>("caseId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases"));
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    AddFactInputGraph input = context.GetArgument<AddFactInputGraph>("input");

                    var command = new AddFactCommand
                    {
                        Type = input.Type,
                        Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                        AddedById = loginId
                    };

                    Result<CreatedStatus> result = await caseService.AddFactToCaseAsync(tenantId, caseId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateFactOfCase")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("factId", "the fact identifier")
                .Argument<NonNullGraphType<UpdateFactInputGraphType>>("input", "the updated fact")
                .ResolveAsync(async context =>
                {
                    string caseId = context.GetArgument<string>("caseId");
                    string factId = context.GetArgument<string>("factId");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    UpdateFactInputGraph input = context.GetArgument<UpdateFactInputGraph>("input");

                    var command = new UpdateFactCommand
                    {
                        Id = factId,
                        Type = input.Type,
                        Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                        ModifiedById = loginId
                    };

                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases"));

                    return await caseService.UpdateFactOfCaseAsync(tenantId, caseId, command);
                });

            Field<ResultGraphType>()
                .Name("removeFactFromCase")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("factId", "the fact identifier")
                .ResolveAsync(async context =>
                {
                    string caseId = context.GetArgument<string>("caseId");
                    string factId = context.GetArgument<string>("factId");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    var command = new RemoveFactCommand
                    {
                        Id = factId,
                        RemovedById = loginId
                    };

                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                    return await caseService.RemoveFactFromCaseAsync(tenantId, caseId, command);
                });

            Field<ResultGraphType>()
                .Name("caseFactBatch")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<FactCommandBatchInputGraphType>>("input", "the fact input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                    string existingCaseId = (await caseService.GetIdsAsync(tenantId, new Domain.QueryArguments { Where = new CaseWhere { Id = caseId } })).FirstOrDefault();
                    if (existingCaseId == null)
                        return new Result { Status = "failure", Errors = new List<string> { $"The case '{caseId}' not found." } };

                    FactCommandBatchInputGraph input = context.GetArgument<FactCommandBatchInputGraph>("input");

                    var addFactCommands = input.AddFactInputs?.Select(i => i.ToCommand(loginId))?.ToList();

                    var updateFactCommands = input.UpdateFactInputs?.Select(i => i.ToCommand(loginId))?.ToList();

                    var factCommandBatch = new FactCommandBatch
                    {
                        AddFactCommands = addFactCommands,
                        UpdateFactCommands = updateFactCommands
                    };

                    return await caseService.CaseFactBatchAsync(tenantId, caseId, factCommandBatch);
                });


            Field<ResultGraphType>()
                .Name("addNoteToCase")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<AddNoteInputGraphType>>("input", "the note to be added")
                .ResolveAsync(async context =>
                {
                    string caseId = context.GetArgument<string>("caseId");
                    AddNoteCommand command = context.GetArgument<AddNoteCommand>("input");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    command.AddedById = loginId;
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                    return await caseService.AddNoteToCaseAsync(tenantId, caseId, command);
                });

            Field<ResultGraphType>()
                .Name("updateNoteOfCase")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("noteId", "the note identifier")
                .Argument<NonNullGraphType<UpdateNoteInputGraphType>>("input", "the note to be updated")
                .ResolveAsync(async context =>
                {
                    string caseId = context.GetArgument<string>("caseId");
                    string noteId = context.GetArgument<string>("noteId");
                    UpdateNoteCommand command = context.GetArgument<UpdateNoteCommand>("input");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    command.Id = noteId;
                    command.ModifiedById = loginId;

                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                    return await caseService.UpdateNoteOfCaseAsync(tenantId, caseId, command);
                });

            Field<ResultGraphType>()
                .Name("removeNoteFromCase")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("noteId", "the note identifier")
                .ResolveAsync(async context =>
                {
                    string caseId = context.GetArgument<string>("caseId");
                    string noteId = context.GetArgument<string>("noteId");
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    var command = new RemoveNoteCommand
                    {
                        Id = noteId,
                        RemovedById = loginId
                    };

                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                    return await caseService.RemoveNoteFromCaseAsync(tenantId, caseId, command);
                });

            Field<CreatedStatusResultGraphType>()
                .Name("generatePoliciesFromProposal")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identitifier")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identitifier")
                .Argument<BooleanGraphType>("copyCaseFieldsToExtraFields", "boolean flag to whether copy case fields to policy or not")
                .Argument<BooleanGraphType>("storeClausesByValue", "boolean flag to whether store clauses templates inside the policy or not")
                .Argument<BooleanGraphType>("storeJacketsByValue", "boolean flag to whether store jackets with clauses including templates inside policy or not")
                .Argument<StringGraphType>("policyStatus", "the status to set on the generated policies")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string clientId = context.GetClientIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    bool copyCaseFieldsToExtraFields = context.GetArgument<bool>("copyCaseFieldsToExtraFields");
                    bool storeClausesByValue = context.GetArgument<bool>("storeClausesByValue");
                    bool storeJacketsByValue = context.GetArgument<bool>("storeJacketsByValue");
                    string policyStatus = context.GetArgument<string>("policyStatus");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases"));

                    var command = new GeneratePoliciesFromProposalCommand
                    {
                        ProposalId = proposalId,
                        GeneratedById = loginId,
                        ClientId = clientId,
                        CopyCaseFieldsToExtraFields = copyCaseFieldsToExtraFields,
                        StoreClausesByValue = storeClausesByValue,
                        StoreJacketsByValue = storeJacketsByValue,
                        PolicyStatus = policyStatus
                    };

                    var userContext = (GraphQLUserContext)context.UserContext;

                    return await caseService.GeneratePoliciesFromProposalAsync(tenantId, caseId, command,
                        Tools.GetAccessToken(userContext));
                });

            Field<CreatedStatusResultGraphType>()
                .Name("addStakeholderToCase")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<AddStakeholderInputGraphType>>("input", "the stakeholder input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                    AddStakeholderCommand command = context.GetArgument<AddStakeholderCommand>("input");
                    command.AddedById = loginId;

                    Result<string> result = await caseService.AddStakeholderToCaseAsync(tenantId, caseId, command);
                    return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
                });

            Field<ResultGraphType>()
               .Name("updateStakeholderOfCase")
               .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
               .Argument<NonNullGraphType<StringGraphType>>("stakeholderId", "the stakeholder identifier")
               .Argument<NonNullGraphType<UpdateStakeholderInputGraphType>>("input", "the stakeholder input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string caseId = context.GetArgument<string>("caseId");
                   string stakeholderId = context.GetArgument<string>("stakeholderId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                   UpdateStakeholderCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateStakeholderCommand>();
                   command.Id = stakeholderId;
                   command.ModifiedById = loginId;

                   return await caseService.UpdateStakeholderOfCaseAsync(tenantId, caseId, command);
               });

            Field<ResultGraphType>()
               .Name("removeStakeholderFromCase")
               .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
               .Argument<NonNullGraphType<StringGraphType>>("stakeholderId", "the stakeholder identifier")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string caseId = context.GetArgument<string>("caseId");
                   string stakeholderId = context.GetArgument<string>("stakeholderId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                   var command = new RemoveStakeholderCommand
                   {
                       Id = stakeholderId,
                       RemovedById = loginId
                   };

                   return await caseService.RemoveStakeholderFromCaseAsync(tenantId, caseId, command);
               });

            Field<CreatedStatusResultGraphType>()
                .Name("addStakeholderToProposal")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
                .Argument<NonNullGraphType<AddStakeholderInputGraphType>>("input", "the stakeholder input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");

                    AddStakeholderCommand command = context.GetArgument<AddStakeholderCommand>("input");
                    command.AddedById = loginId;
                    command.ProposalId = proposalId;

                    Result<string> result = await caseService.AddStakeholderToProposalAsync(tenantId, caseId, command);
                    return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
                });

            Field<ResultGraphType>()
               .Name("updateStakeholderOfProposal")
               .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
               .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
               .Argument<NonNullGraphType<StringGraphType>>("stakeholderId", "the stakeholder identifier")
               .Argument<NonNullGraphType<UpdateStakeholderInputGraphType>>("input", "the stakeholder input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string caseId = context.GetArgument<string>("caseId");
                   string proposalId = context.GetArgument<string>("proposalId");
                   string stakeholderId = context.GetArgument<string>("stakeholderId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                   UpdateStakeholderCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateStakeholderCommand>();
                   command.Id = stakeholderId;
                   command.ModifiedById = loginId;
                   command.ProposalId = proposalId;

                   return await caseService.UpdateStakeholderOfProposalAsync(tenantId, caseId, command);
               });

            Field<ResultGraphType>()
               .Name("removeStakeholderFromProposal")
               .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
               .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
               .Argument<NonNullGraphType<StringGraphType>>("stakeholderId", "the stakeholder identifier")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string caseId = context.GetArgument<string>("caseId");
                   string proposalId = context.GetArgument<string>("proposalId");
                   string stakeholderId = context.GetArgument<string>("stakeholderId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                   var command = new RemoveStakeholderCommand
                   {
                       Id = stakeholderId,
                       RemovedById = loginId,
                       ProposalId = proposalId,
                   };

                   return await caseService.RemoveStakeholderFromProposalAsync(tenantId, caseId, command);
               });

            Field<CreatedStatusResultGraphType>()
                .Name("addDiscountToProposalOffer")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
                .Argument<NonNullGraphType<AddDiscountToOfferInputGraphType>>("input", "the discount input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));
                    await permissionValidator.Authorize(context, "overrideOffers");

                    DiscountInputGraph input = context.GetArgument<DiscountInputGraph>("input");
                    var command = new AddDiscountCommand
                    {
                        Name = input.Name,
                        CalculationJsonLogic = input.CalculationJsonLogic != null ? JObject.Parse(input.CalculationJsonLogic) : null,
                        Order = input.Order
                    };
                    command.ProposalId = proposalId;
                    command.OfferId = offerId;

                    Result<string> result = await caseService.AddDiscountToProposalOfferAsync(tenantId, caseId, command);

                    return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
                });

            Field<ResultGraphType>()
                .Name("updateDiscountOfProposalOffer")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the csae identifier")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
                .Argument<NonNullGraphType<StringGraphType>>("discountId", "the discount identifier")
                .Argument<NonNullGraphType<UpdateDiscountInputGraphType>>("input", "the discount")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");
                    string discountId = context.GetArgument<string>("discountId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));
                    await permissionValidator.Authorize(context, "overrideOffers");

                    DiscountInputGraph input = context.GetArgument<DiscountInputGraph>("input");
                    IDictionary<string, object> dict = Tools.ToDictionary<object>(input);
                    if (input.CalculationJsonLogic != null)
                    {
                        dict["CalculationJsonLogic"] = JObject.Parse(input.CalculationJsonLogic);
                    }

                    UpdateDiscountCommand command = dict.ToUpdateCommand<UpdateDiscountCommand>();
                    command.ProposalId = proposalId;
                    command.OfferId = offerId;
                    command.DiscountId = discountId;

                    return await caseService.UpdateDiscountOfProposalOfferAsync(tenantId, caseId, command);
                });

            Field<ResultGraphType>()
                .Name("removeDiscountFromProposalOffer")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
                .Argument<NonNullGraphType<StringGraphType>>("discountId", "the discount identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");
                    string discountId = context.GetArgument<string>("discountId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));
                    await permissionValidator.Authorize(context, "overrideOffers");

                    var command = new RemoveDiscountFromOfferCommand
                    {
                        ProposalId = proposalId,
                        OfferId = offerId,
                        DiscountId = discountId,
                    };

                    return await caseService.RemoveDiscountFromProposalOfferAsync(tenantId, caseId, command);
                });

            Field<CreatedStatusResultGraphType>()
            .Name("addLoadingToProposalOffer")
            .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
            .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
            .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
            .Argument<NonNullGraphType<LoadingInputGraphType>>("input", "the loading input")
            .AuthorizeWith("any")
            .ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();
                string loginId = context.GetLoginIdFromToken();
                string caseId = context.GetArgument<string>("caseId");
                string proposalId = context.GetArgument<string>("proposalId");
                string offerId = context.GetArgument<string>("offerId");
                await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));
                await permissionValidator.Authorize(context, "overrideOffers");

                LoadingInputGraph input = context.GetArgument<LoadingInputGraph>("input");
                var command = new AddLoadingCommand
                {
                    CalculationJsonLogic = input.CalculationJsonLogic != null ? JObject.Parse(input.CalculationJsonLogic) : null,
                    Code = input.Code,
                    Flat = input.Flat,
                    Ratio = input.Ratio,
                    Order = input.Order
                };
                command.ProposalId = proposalId;
                command.OfferId = offerId;
                command.AddedById = loginId;

                Result<string> result = await caseService.AddLoadingToProposalOfferAsync(tenantId, caseId, command);

                return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
            });

            Field<ResultGraphType>()
                .Name("updateLoadingOfProposalOffer")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the csae identifier")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
                .Argument<NonNullGraphType<StringGraphType>>("loadingId", "the loading identifier")
                .Argument<NonNullGraphType<LoadingInputGraphType>>("input", "the loading")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");
                    string loadingId = context.GetArgument<string>("loadingId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));
                    await permissionValidator.Authorize(context, "overrideOffers");

                    LoadingInputGraph input = context.GetArgument<LoadingInputGraph>("input");
                    IDictionary<string, object> dict = Tools.ToDictionary<object>(input);
                    if (input.CalculationJsonLogic != null)
                    {
                        dict["CalculationJsonLogic"] = JObject.Parse(input.CalculationJsonLogic);
                    }

                    UpdateLoadingCommand command = dict.ToUpdateCommand<UpdateLoadingCommand>();
                    command.ProposalId = proposalId;
                    command.OfferId = offerId;
                    command.Id = loadingId;

                    return await caseService.UpdateLoadingOfProposalOfferAsync(tenantId, caseId, command);
                });

            Field<ResultGraphType>()
                .Name("removeLoadingFromProposalOffer")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
                .Argument<NonNullGraphType<StringGraphType>>("loadingId", "the loading identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");
                    string loadingId = context.GetArgument<string>("loadingId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));
                    await permissionValidator.Authorize(context, "overrideOffers");

                    var command = new RemoveLoadingCommand
                    {
                        ProposalId = proposalId,
                        OfferId = offerId,
                        Id = loadingId,
                    };

                    return await caseService.RemoveLoadingFromProposalOfferAsync(tenantId, caseId, command);
                });

            Field<CreatedStatusResultGraphType>()
               .Name("addExclusionToProposalOffer")
               .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
               .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
               .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
               .Argument<NonNullGraphType<ExclusionInputGraphType>>("input", "the exclusion input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string caseId = context.GetArgument<string>("caseId");
                   string proposalId = context.GetArgument<string>("proposalId");
                   string offerId = context.GetArgument<string>("offerId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                   AddExclusionCommand command = context.GetArgument<AddExclusionCommand>("input");
                   command.ProposalId = proposalId;
                   command.OfferId = offerId;
                   command.AddedById = loginId;

                   return await caseService.AddExclusionToProposalOfferAsync(tenantId, caseId, command);
               });

            Field<ResultGraphType>()
                .Name("removeExclusionFromProposalOffer")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
                .Argument<NonNullGraphType<StringGraphType>>("exclusionId", "the loading identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");
                    string exclusionId = context.GetArgument<string>("exclusionId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                    var command = new RemoveExclusionCommand
                    {
                        ProposalId = proposalId,
                        OfferId = offerId,
                        Id = exclusionId,
                        RemovedById = loginId
                    };

                    return await caseService.RemoveExclusionFromProposalOfferAsync(tenantId, caseId, command);
                });

            Field<CreatedStatusResultGraphType>()
                .Name("addClauseToProposalOffer")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
                .Argument<NonNullGraphType<AddClauseToOfferInputGraphType>>("input", "the clause input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                    AddClauseCommand command = context.GetArgument<AddClauseCommand>("input");
                    command.ProposalId = proposalId;
                    command.OfferId = offerId;
                    command.AddedById = loginId;

                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, command.RenderParameters, out List<string> errors);
                    if (errors.Any())
                        return new Result<CreatedStatus> { Status = "failure", Errors = errors };

                    var result = await caseService.AddClauseToOfferAsync(tenantId, caseId, command);

                    return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
                });

            Field<ResultGraphType>()
                .Name("updateClauseOfProposalOffer")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
                .Argument<NonNullGraphType<StringGraphType>>("clauseId", "the clause identifier")
                .Argument<NonNullGraphType<UpdateClauseOfOfferInputGraphType>>("input", "the clause input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");
                    string clauseId = context.GetArgument<string>("clauseId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                    UpdateClauseCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateClauseCommand>();
                    command.ProposalId = proposalId;
                    command.OfferId = offerId;
                    command.ClauseId = clauseId;
                    command.ModifiedById = loginId;

                    Tools.PopulateRenderParameters((GraphQLUserContext)context.UserContext, command.RenderParameters, out List<string> errors);
                    if (errors.Any())
                        return new Result { Status = "failure", Errors = errors };

                    return await caseService.UpdateClauseOfOfferAsync(tenantId, caseId, command);
                });

            Field<ResultGraphType>()
                .Name("removeClauseFromProposalOffer")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
                .Argument<NonNullGraphType<StringGraphType>>("clauseId", "the clause identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");
                    string clauseId = context.GetArgument<string>("clauseId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                    var command = new RemoveClauseCommand
                    {
                        ProposalId = proposalId,
                        OfferId = offerId,
                        ClauseId = clauseId,
                        RemovedById = loginId
                    };

                    return await caseService.RemoveClauseFromOfferAsync(tenantId, caseId, command);
                });

            Field<ResultGraphType>()
                .Name("proposalOfferClauseBatch")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
                .Argument<NonNullGraphType<ClauseCommandBatchInputGraphType>>("input", "the clause input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                    string existingCaseId = (await caseService.GetIdsAsync(tenantId,
                        new Domain.QueryArguments
                        {
                            Where = new CaseWhere
                            {
                                And = new List<CaseWhere>
                                {
                                    new() { Id = caseId },
                                    new()
                                    {
                                        Proposals_contains = new ProposalWhere { Id = proposalId }
                                    },
                                    new()
                                    {
                                        Proposals_contains = new ProposalWhere
                                        {
                                            Offers_contains = new OfferWhere { Id = offerId }
                                        }
                                    }
                                }
                            }
                        })).FirstOrDefault();
                    if (existingCaseId == null)
                        return new Result
                        {
                            Status = "failure",
                            Errors = new List<string> { $"The case '{caseId}' not found." }
                        };

                    ClauseCommandBatchInputGraph input = context.GetArgument<ClauseCommandBatchInputGraph>("input");

                    string[] templateIds = (input.AddClauseInputs ?? Enumerable.Empty<AddClauseCommand>())
                        .Where(x => x.StoreTemplateByValue).Select(x => x.TemplateId)
                        .Union((input.UpdateClauseInputs ?? Enumerable.Empty<UpdateClauseCommand>())
                            .Where(x => x.IsTemplateIdChanged && x.StoreTemplateByValue)
                            .Select(x => x.TemplateId))
                        .Where(x => !string.IsNullOrEmpty(x))
                        .ToArray();

                    Dictionary<string, Template> templatesMap = new();
                    if (templateIds.Any())
                    {
                        IEnumerable<Template> templates = await templateService.GetAsync(tenantId,
                            new Domain.QueryArguments { Where = new TemplateWhere { Id_in = templateIds } });
                        templatesMap = templates.ToDictionary(x => x.Id, x => x);
                    }

                    input.AddClauseInputs?.ForEach(x =>
                    {
                        x.AddedById = loginId;
                        if (!string.IsNullOrEmpty(x.TemplateId) && templatesMap.ContainsKey(x.TemplateId))
                        {
                            x.StoreTemplateByValue = true;
                            x.Template = templatesMap[x.TemplateId];
                        }
                    });
                    input.UpdateClauseInputs?.ForEach(x =>
                    {
                        x.ModifiedById = loginId;
                        if (!string.IsNullOrEmpty(x.TemplateId) && templatesMap.ContainsKey(x.TemplateId))
                        {
                            x.StoreTemplateByValue = true;
                            x.Template = templatesMap[x.TemplateId];
                        }
                    });

                    var clauseBatchCommand = new ClauseBatchCommand
                    {
                        ProposalId = proposalId,
                        OfferId = offerId,
                        AddClauseCommands = input.AddClauseInputs,
                        UpdateClauseCommands = input.UpdateClauseInputs,
                    };

                    return await caseService.ProposalOfferClauseBatchAsync(tenantId, caseId, clauseBatchCommand);
                });

            Field<ResultGraphType>()
                .Name("proposalOfferClausesClone")
                .Argument<NonNullGraphType<OfferIdInputGraphType>>("from", "Offer caseTo copy clauses caseFrom")
                .Argument<NonNullGraphType<OfferIdInputGraphType>>("to", "Offer caseTo copy clauses caseTo")
                .Argument<StringGraphType>("endorsementId", "Endorsement id")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    OfferIdInputGraph caseFromId = context.GetArgument<OfferIdInputGraph>("from");
                    OfferIdInputGraph caseToId = context.GetArgument<OfferIdInputGraph>("to");
                    string endorsementId = context.GetArgument<string>("endorsementId");

                    async Task<Case> GetCase(OfferIdInputGraph offerIdInput)
                    {
                        Case @case = (await caseService.GetAsync(tenantId,
                        new Domain.QueryArguments
                        {
                            Where = new CaseWhere
                            {
                                And = new List<CaseWhere>
                                {
                                    new() { Id = offerIdInput.CaseId },
                                    new()
                                    {
                                        Proposals_contains = new ProposalWhere { Id = offerIdInput.ProposalId }
                                    },
                                    new()
                                    {
                                        Proposals_contains = new ProposalWhere
                                        {
                                            Offers_contains = new OfferWhere { Id = offerIdInput.OfferId }
                                        }
                                    }
                                }
                            }
                        })).FirstOrDefault();

                        return @case;
                    }

                    if (caseFromId == null) return Result.Failure($"Unable to find a case to copy from");
                    if (caseToId == null) return Result.Failure($"Unable to find a case to copy to");

                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseToId.CaseId));

                    Case caseFrom = await GetCase(caseFromId);

                    Offer offerFrom = caseFrom
                        .Proposals?
                        .FirstOrDefault(x => x.Id == caseFromId.ProposalId)?
                        .Basket?
                        .FirstOrDefault(x => x.Id == caseFromId.OfferId);

                    if (offerFrom == null) return Result.Failure("Unable to find an offer to copy from");

                    List<AddClauseCommand> addClauseCommands = offerFrom.Clauses?.Select(x => new AddClauseCommand
                    {
                        AddedById = loginId,
                        ClauseId = x.Id,
                        ProposalId = caseToId.ProposalId,
                        EndorsementId = endorsementId,
                        HtmlOverride = x.HtmlOverride,
                        OfferId = caseToId.OfferId,
                        Order = x.Order,
                        RenderParameters = x.RenderParameters,
                        StoreTemplateByValue = x.Template != null,
                        Template = x.Template,
                        TemplateId = x.TemplateId,
                        Timestamp = DateTime.UtcNow,
                        Type = x.Type
                    }).ToList() ?? new List<AddClauseCommand>();

                    if (!addClauseCommands.Any()) return Result.Success();

                    Result result = await caseService.ProposalOfferClauseBatchAsync(tenantId, caseToId.CaseId, new ClauseBatchCommand
                    {
                        ById = loginId,
                        OfferId = caseToId.OfferId,
                        ProposalId = caseToId.ProposalId,
                        Timestamp = DateTime.UtcNow,
                        EndorsementId = endorsementId,

                        AddClauseCommands = addClauseCommands
                    });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("proposalOfferJacketsClone")
                .Argument<NonNullGraphType<OfferIdInputGraphType>>("from", "Offer caseTo copy jackets from")
                .Argument<NonNullGraphType<OfferIdInputGraphType>>("to", "Offer caseTo copy jackets to")
                .Argument<StringGraphType>("endorsementId", "Endorsement id")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    OfferIdInputGraph caseFromId = context.GetArgument<OfferIdInputGraph>("from");
                    OfferIdInputGraph caseToId = context.GetArgument<OfferIdInputGraph>("to");
                    string endorsementId = context.GetArgument<string>("endorsementId");

                    async Task<Case> GetCase(OfferIdInputGraph offerIdInput)
                    {
                        Case @case = (await caseService.GetAsync(tenantId,
                        new Domain.QueryArguments
                        {
                            Where = new CaseWhere
                            {
                                And = new List<CaseWhere>
                                {
                                    new() { Id = offerIdInput.CaseId },
                                    new()
                                    {
                                        Proposals_contains = new ProposalWhere { Id = offerIdInput.ProposalId }
                                    },
                                    new()
                                    {
                                        Proposals_contains = new ProposalWhere
                                        {
                                            Offers_contains = new OfferWhere { Id = offerIdInput.OfferId }
                                        }
                                    }
                                }
                            }
                        })).FirstOrDefault();

                        return @case;
                    }

                    if (caseFromId == null) return Result.Failure($"Unable to find a case to copy from");
                    if (caseToId == null) return Result.Failure($"Unable to find a case to copy to");

                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseToId.CaseId));

                    Case caseFrom = await GetCase(caseFromId);

                    Offer offerFrom = caseFrom
                        .Proposals?
                        .FirstOrDefault(x => x.Id == caseFromId.ProposalId)?
                        .Basket?
                        .FirstOrDefault(x => x.Id == caseFromId.OfferId);

                    if (offerFrom == null) return Result.Failure("Unable to find an offer to copy from");

                    List<AddJacketInstanceCommand> addJacketsCommands = offerFrom.Jackets.Select(x => new AddJacketInstanceCommand
                    {
                        AddedById = loginId,
                        Jacket = x.Jacket,
                        JacketId = x.JacketId,
                        Order = x.Order,
                        StoreJacketByValue = x.Jacket != null
                    }).ToList();

                    if (!addJacketsCommands.Any()) return Result.Success();

                    Result result = await caseService.ProposalOfferJacketBatchAsync(tenantId, caseToId.CaseId, new JacketInstanceBatchCommand
                    {
                        OfferId = caseToId.OfferId,
                        ProposalId = caseToId.ProposalId,

                        AddJacketInstanceCommands = addJacketsCommands
                    });

                    return result;
                });


            Field<ResultGraphType>()
               .Name("proposalOfferJacketBatch")
               .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
               .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
               .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
               .Argument<NonNullGraphType<JacketInstanceCommandBatchInputGraphType>>("input", "the jacket input")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string caseId = context.GetArgument<string>("caseId");
                   string proposalId = context.GetArgument<string>("proposalId");
                   string offerId = context.GetArgument<string>("offerId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                   string existingCaseId = (await caseService.GetIdsAsync(tenantId,
                       new Domain.QueryArguments
                       {
                           Where = new CaseWhere
                           {
                               And = new List<CaseWhere>
                               {
                                   new() { Id = caseId },
                                   new()
                                   {
                                       Proposals_contains = new ProposalWhere { Id = proposalId }
                                   },
                                   new()
                                   {
                                       Proposals_contains = new ProposalWhere
                                       {
                                           Offers_contains = new OfferWhere { Id = offerId }
                                       }
                                   }
                               }
                           }
                       })).FirstOrDefault();
                   if (existingCaseId == null)
                       return new Result
                       {
                           Status = "failure",
                           Errors = new List<string> { $"The case '{caseId}' not found." }
                       };

                   JacketInstanceCommandBatchInputGraph input =
                       context.GetArgument<JacketInstanceCommandBatchInputGraph>("input");

                   string[] jacketIds = (input.AddJacketInstanceInputs ?? new List<AddJacketInstanceCommand>())
                       .Where(x => x.StoreJacketByValue && x.JacketId != null).Select(x => x.JacketId).ToArray();

                   var jacketsMap = new Dictionary<string, Jacket>();
                   string[] templateIds = Array.Empty<string>();
                   if (jacketIds.Any())
                   {
                       Jacket[] jackets = (await productService.GetJacketsAsync(tenantId,
                           new Domain.QueryArguments { Where = new JacketWhere { Id_in = jacketIds } })).ToArray();

                       templateIds = jackets.SelectMany(x => x.Clauses.Select(c => c.TemplateId)).ToArray();
                       jacketsMap = jackets.ToDictionary(x => x.Id, x => x);
                   }

                   Dictionary<string, Template> templatesMap = new();
                   if (templateIds.Any())
                   {
                       IEnumerable<Template> templates = await templateService.GetAsync(tenantId,
                           new Domain.QueryArguments { Where = new TemplateWhere { Id_in = templateIds } });
                       templatesMap = templates.ToDictionary(x => x.Id, x => x);
                   }

                   input.AddJacketInstanceInputs?.ForEach(j =>
                   {
                       j.AddedById = loginId;
                       if (!jacketsMap.ContainsKey(j.JacketId)) return;

                       j.Jacket = jacketsMap[j.JacketId];
                       j.Jacket.Clauses.ForEach(c =>
                       {
                           if (templatesMap.ContainsKey(c.TemplateId)) c.Template = templatesMap[c.TemplateId];
                       });
                   });

                   input.UpdateJacketInstanceInputs?.ForEach(x => x.ModifiedById = loginId);

                   var jacketBatchCommand = new JacketInstanceBatchCommand
                   {
                       ProposalId = proposalId,
                       OfferId = offerId,
                       AddJacketInstanceCommands = input.AddJacketInstanceInputs,
                       UpdateJacketInstanceCommands = input.UpdateJacketInstanceInputs
                   };

                   return await caseService.ProposalOfferJacketBatchAsync(tenantId, caseId, jacketBatchCommand);
               });

            Field<ResultGraphType>()
                .Name("removeJacketFromProposalOffer")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
                .Argument<NonNullGraphType<StringGraphType>>("jacketInstanceId", "the jacket instance identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");
                    string jacketInstanceId = context.GetArgument<string>("jacketInstanceId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                    var command = new RemoveJacketInstanceCommand
                    {
                        ProposalId = proposalId,
                        OfferId = offerId,
                        InstanceId = jacketInstanceId,
                        RemovedById = loginId
                    };

                    return await caseService.RemoveJacketFromOfferAsync(tenantId, caseId, command);
                });

            Field<ResultGraphType>()
               .Name("proposalOfferFactBatch")
               .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
               .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the case identifier")
               .Argument<NonNullGraphType<StringGraphType>>("offerId", "the case identifier")
               .Argument<NonNullGraphType<FactCommandBatchInputGraphType>>("input", "the fact input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string caseId = context.GetArgument<string>("caseId");
                   string proposalId = context.GetArgument<string>("proposalId");
                   string offerId = context.GetArgument<string>("offerId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                   string existingCaseId = (await caseService.GetIdsAsync(tenantId, new Domain.QueryArguments { Where = new CaseWhere { And = new List<CaseWhere> { new() { Id = caseId }, new() { Proposals_contains = new ProposalWhere { Id = proposalId } }, new() { Proposals_contains = new ProposalWhere { Offers_contains = new OfferWhere { Id = offerId } } } } } })).FirstOrDefault();
                   if (existingCaseId == null)
                       return new Result { Status = "failure", Errors = new List<string> { $"The case '{caseId}' not found." } };

                   FactCommandBatchInputGraph input = context.GetArgument<FactCommandBatchInputGraph>("input");

                   var addFactCommands = input.AddFactInputs?.Select(i => i.ToCommand(loginId))?.ToList();

                   var updateFactCommands = input.UpdateFactInputs?.Select(i => i.ToCommand(loginId))?.ToList();

                   var factCommandBatch = new FactCommandBatch
                   {
                       ProposalId = proposalId,
                       OfferId = offerId,
                       AddFactCommands = addFactCommands,
                       UpdateFactCommands = updateFactCommands
                   };

                   return await caseService.ProposalOfferFactBatchAsync(tenantId, caseId, factCommandBatch);
               });

            Field<CreatedStatusResultGraphType>()
               .Name("addFactToProposalOffer")
               .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
               .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
               .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
               .Argument<NonNullGraphType<AddFactInputGraphType>>("input", "the fact input")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string caseId = context.GetArgument<string>("caseId");
                   string proposalId = context.GetArgument<string>("proposalId");
                   string offerId = context.GetArgument<string>("offerId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                   AddFactInputGraph input = context.GetArgument<AddFactInputGraph>("input");

                   var command = new AddFactCommand
                   {
                       Type = input.Type,
                       Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                       AddedById = context.GetLoginIdFromToken(),
                   };

                   command.ProposalId = proposalId;
                   command.OfferId = offerId;
                   command.AddedById = loginId;


                   Result<string> result = await caseService.AddFactToProposalOfferAsync(tenantId, caseId, command);

                   return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
               });

            Field<ResultGraphType>()
               .Name("policyJacketBatch")
               .Argument<NonNullGraphType<StringGraphType>>("policyId", "the policy identifier")
               .Argument<StringGraphType>("endorsementId", "endorsement id")
               .Argument<NonNullGraphType<PolicyJacketInstanceCommandBatchInputGraphType>>("input", "the jacket input")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string policyId = context.GetArgument<string>("policyId");
                   string endorsementId = context.GetArgument<string>("endorsementId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies"));

                   var input = context.GetArgument<PolicyJacketInstanceCommandBatchInputGraph>("input");

                   string[] jacketIds = (input.AddJacketInstanceInputs ?? new List<AddJacketInstanceCommand>())
                       .Where(x => x.StoreJacketByValue && x.JacketId != null).Select(x => x.JacketId).ToArray();

                   var jacketsMap = new Dictionary<string, Jacket>();
                   string[] templateIds = Array.Empty<string>();
                   if (jacketIds.Any())
                   {
                       Jacket[] jackets = (await productService.GetJacketsAsync(tenantId,
                           new Domain.QueryArguments { Where = new JacketWhere { Id_in = jacketIds } })).ToArray();

                       templateIds = jackets.SelectMany(x => x.Clauses.Select(c => c.TemplateId)).ToArray();
                       jacketsMap = jackets.ToDictionary(x => x.Id, x => x);
                   }

                   Dictionary<string, Template> templatesMap = new();
                   if (templateIds.Any())
                   {
                       IEnumerable<Template> templates = await templateService.GetAsync(tenantId,
                           new Domain.QueryArguments { Where = new TemplateWhere { Id_in = templateIds } });
                       templatesMap = templates.ToDictionary(x => x.Id, x => x);
                   }

                   input.AddJacketInstanceInputs?.ForEach(j =>
                   {
                       j.AddedById = loginId;
                       if (!j.StoreJacketByValue || !jacketsMap.ContainsKey(j.JacketId)) return;

                       j.Jacket = jacketsMap[j.JacketId];
                       j.Jacket.Clauses.ForEach(c =>
                       {
                           if (templatesMap.ContainsKey(c.TemplateId)) c.Template = templatesMap[c.TemplateId];
                       });
                   });

                   input.AddJacketInstanceInputs?.ForEach(x => x.AddedById = loginId);
                   input.UpdateJacketInstanceInputs?.ForEach(x => x.ModifiedById = loginId);

                   var jacketBatchCommand = new PolicyJacketInstanceBatchCommand
                   {
                       EndorsementId = endorsementId,
                       ById = loginId,
                       Timestamp = DateTime.UtcNow,

                       AddJacketInstanceCommands = input.AddJacketInstanceInputs,
                       UpdateJacketInstanceCommands = input.UpdateJacketInstanceInputs
                   };

                   return await policyService.PolicyJacketBatchAsync(tenantId, policyId, jacketBatchCommand);
               });

            Field<ResultGraphType>()
                .Name("removeJacketFromPolicy")
                .Argument<NonNullGraphType<StringGraphType>>("policyId", "policy Id")
                .Argument<StringGraphType>("endorsementId", "endorsement Id")
                .Argument<NonNullGraphType<StringGraphType>>("jacketInstanceId", "the jacket instance identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string policyId = context.GetArgument<string>("policyId");
                    string endorsementId = context.GetArgument<string>("endorsementId");
                    string jacketInstanceId = context.GetArgument<string>("jacketInstanceId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updatePolicies", "writePolicies").WithTargetIds(policyId));

                    var command = new RemovePolicyJacketInstanceCommand
                    {
                        InstanceId = jacketInstanceId,
                        RemovedById = loginId,
                        EndorsementId = endorsementId,
                        Timestamp = DateTime.UtcNow,
                    };

                    return await policyService.RemoveJacketFromPolicyAsync(tenantId, policyId, command);
                });

            Field<ResultGraphType>()
               .Name("updateFactOfProposalOffer")
               .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
               .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
               .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
               .Argument<NonNullGraphType<StringGraphType>>("factId", "the fact identifier")
               .Argument<NonNullGraphType<UpdateFactInputGraphType>>("input", "the clause input")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string caseId = context.GetArgument<string>("caseId");
                   string proposalId = context.GetArgument<string>("proposalId");
                   string offerId = context.GetArgument<string>("offerId");
                   string factId = context.GetArgument<string>("factId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                   UpdateFactInputGraph graph = context.GetArgument<UpdateFactInputGraph>("input");
                   UpdateFactCommand command = Tools.ToDictionary<object>(graph).ToUpdateCommand<UpdateFactCommand>();
                   command.Id = factId;
                   command.Value = JToken.FromObject(graph.Value?.GetValue());
                   command.ModifiedById = context.GetLoginIdFromToken();

                   command.ProposalId = proposalId;
                   command.OfferId = offerId;
                   command.ModifiedById = loginId;

                   return await caseService.UpdateFactOfProposalOfferAsync(tenantId, caseId, command);
               });

            Field<ResultGraphType>()
                .Name("removeFactFromProposalOffer")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
                .Argument<NonNullGraphType<StringGraphType>>("factId", "the fact identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");
                    string factId = context.GetArgument<string>("factId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                    var command = new RemoveFactCommand
                    {
                        Id = factId,
                        ProposalId = proposalId,
                        OfferId = offerId,
                        RemovedById = loginId
                    };

                    return await caseService.RemoveFactFromProposalOfferAsync(tenantId, caseId, command);
                });

            Field<ResultGraphType>()
             .Name("upsertBenefitOptionOfProposalOffer")
             .Description("updates a benefitOption or inserts it if it does not exist")
             .AuthorizeWith("any")
             .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
             .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
             .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
             .Argument<NonNullGraphType<BenefitOptionInputGraphType>>("input", "the information of the  benefit option to update")
             .ResolveAsync(async context =>
             {
                 string tenantId = context.GetTenantIdFromToken();
                 string caseId = context.GetArgument<string>("caseId");
                 string proposalId = context.GetArgument<string>("proposalId");
                 string offerId = context.GetArgument<string>("offerId");
                 string loginId = context.GetLoginIdFromToken();
                 await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                 BenefitOptionInputGraph input = context.GetArgument<BenefitOptionInputGraph>("input");

                 UpsertBenefitOptionCommand command = BenefitOptionInputGraph.ToCommand(input, proposalId, offerId, loginId);

                 return await caseService.UpsertBenefitOptionOfProposalOfferAsync(tenantId, caseId, command);
             });

            Field<ResultGraphType>()
               .Name("removeBenefitOptionFromProposalOffer")
               .Description("removes a benefitOption")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
               .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
               .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
               .Argument<NonNullGraphType<StringGraphType>>("typeId", "the identifier of the benefitOption")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string caseId = context.GetArgument<string>("caseId");
                   string proposalId = context.GetArgument<string>("proposalId");
                   string offerId = context.GetArgument<string>("offerId");
                   string typeId = context.GetArgument<string>("typeId");
                   string loginId = context.GetLoginIdFromToken();
                   await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                   return await caseService.RemoveBenefitOptionFromProposalOfferAsync(tenantId, caseId, new RemoveBenefitOptionCommand
                   {
                       ProposalId = proposalId,
                       OfferId = offerId,
                       TypeId = typeId,
                       RemovedById = loginId
                   });
               });

            Field<CreatedStatusResultGraphType>()
                .Name("addStakeholderToProposalOffer")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
                .Argument<NonNullGraphType<AddStakeholderInputGraphType>>("input", "the stakeholder input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");

                    AddStakeholderCommand command = context.GetArgument<AddStakeholderCommand>("input");
                    command.AddedById = loginId;
                    command.ProposalId = proposalId;
                    command.OfferId = offerId;

                    Result<string> result = await caseService.AddStakeholderToProposalOfferAsync(tenantId, caseId, command);
                    return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = new CreatedStatus { Id = result.Value } };
                });

            Field<ResultGraphType>()
               .Name("updateStakeholderOfProposalOffer")
               .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
               .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
               .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
               .Argument<NonNullGraphType<StringGraphType>>("stakeholderId", "the stakeholder identifier")
               .Argument<NonNullGraphType<UpdateStakeholderInputGraphType>>("input", "the stakeholder input")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string caseId = context.GetArgument<string>("caseId");
                   string proposalId = context.GetArgument<string>("proposalId");
                   string offerId = context.GetArgument<string>("offerId");
                   string stakeholderId = context.GetArgument<string>("stakeholderId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                   UpdateStakeholderCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateStakeholderCommand>();
                   command.Id = stakeholderId;
                   command.ModifiedById = loginId;
                   command.ProposalId = proposalId;
                   command.OfferId = offerId;

                   return await caseService.UpdateStakeholderOfProposalOfferAsync(tenantId, caseId, command);
               });

            Field<ResultGraphType>()
               .Name("removeStakeholderFromProposalOffer")
               .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
               .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
               .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
               .Argument<NonNullGraphType<StringGraphType>>("stakeholderId", "the stakeholder identifier")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string caseId = context.GetArgument<string>("caseId");
                   string proposalId = context.GetArgument<string>("proposalId");
                   string offerId = context.GetArgument<string>("offerId");
                   string stakeholderId = context.GetArgument<string>("stakeholderId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                   var command = new RemoveStakeholderCommand
                   {
                       Id = stakeholderId,
                       RemovedById = loginId,
                       ProposalId = proposalId,
                       OfferId = offerId
                   };

                   return await caseService.RemoveStakeholderFromProposalOfferAsync(tenantId, caseId, command);
               });

            Field<ResultGraphType>()
               .Name("addAssociatedContractToProposalOffer")
               .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
               .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
               .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
               .Argument<NonNullGraphType<StringGraphType>>("associatedContractId", "the associated contract identifier")
               .AuthorizeWith("any")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();
                   string caseId = context.GetArgument<string>("caseId");
                   string proposalId = context.GetArgument<string>("proposalId");
                   string offerId = context.GetArgument<string>("offerId");
                   string associatedContractId = context.GetArgument<string>("associatedContractId");
                   await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                   var command = new AddAssociatedContractCommand
                   {
                       ContractId = associatedContractId,
                       ProposalId = proposalId,
                       OfferId = offerId,
                       AddedById = loginId,
                   };

                   return await caseService.AddAssociatedContractToProposalOfferAsync(tenantId, caseId, command);
               });

            Field<ResultGraphType>()
             .Name("removeAssociatedContractFromProposalOffer")
             .Argument<NonNullGraphType<StringGraphType>>("caseId", "the case identifier")
             .Argument<NonNullGraphType<StringGraphType>>("proposalId", "the proposal identifier")
             .Argument<NonNullGraphType<StringGraphType>>("offerId", "the offer identifier")
             .Argument<NonNullGraphType<StringGraphType>>("associatedContractId", "the associated contract identifier")
             .AuthorizeWith("any")
             .ResolveAsync(async context =>
             {
                 string tenantId = context.GetTenantIdFromToken();
                 string loginId = context.GetLoginIdFromToken();
                 string caseId = context.GetArgument<string>("caseId");
                 string proposalId = context.GetArgument<string>("proposalId");
                 string offerId = context.GetArgument<string>("offerId");
                 string associatedContractId = context.GetArgument<string>("associatedContractId");
                 await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                 var command = new RemoveAssociatedContractCommand
                 {
                     Id = associatedContractId,
                     ProposalId = proposalId,
                     OfferId = offerId,
                     RemovedById = loginId,
                 };

                 return await caseService.RemoveAssociatedContractFromProposalOfferAsync(tenantId, caseId, command);
             });

            Field<ResultGraphType>()
                .Name("addCommissionToOffer")
                .AuthorizeWith("any")
                .Description("Adds a commission split to an offer")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "The case id")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "The proposal id")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "The offer id")
                .Argument<NonNullGraphType<CommissionInputGraphType>>("input", "The commission input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");
                    CommissionInputGraph input = context.GetArgument<CommissionInputGraph>("input");

                    var command = new AddCommissionCommand
                    {
                        ProposalId = proposalId,
                        OfferId = offerId,
                        EntityId = input.EntityId,
                        JsonRule = input.JsonRule,
                        Amount = input.Amount,
                        CurrencyCode = input.CurrencyCode,
                        Remark = input.Remark,
                        AddedById = loginId
                    };

                    Result result = await caseService.AddCommissionToOfferAsync(tenantId, caseId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateCommissionOfOffer")
                .AuthorizeWith("any")
                .Description("Updates an offer commission split")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "The case id")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "The proposal id")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "The offer id")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the commission id")
                .Argument<NonNullGraphType<CommissionInputGraphType>>("input", "The commission input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");
                    string id = context.GetArgument<string>("id");

                    UpdateCommissionCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateCommissionCommand>();
                    command.Id = id;
                    command.ModifiedById = loginId;
                    command.ProposalId = proposalId;
                    command.OfferId = offerId;

                    Result result = await caseService.UpdateCommissionOfOfferAsync(tenantId, caseId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeCommissionFromOffer")
                .AuthorizeWith("any")
                .Description("Removes a commission split from an offer")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "The case id")
                .Argument<NonNullGraphType<StringGraphType>>("proposalId", "The proposal id")
                .Argument<NonNullGraphType<StringGraphType>>("offerId", "The offer id")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the commission id")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string proposalId = context.GetArgument<string>("proposalId");
                    string offerId = context.GetArgument<string>("offerId");
                    string id = context.GetArgument<string>("id");

                    var command = new RemoveCommissionCommand
                    {
                        ProposalId = proposalId,
                        OfferId = offerId,
                        Id = id,
                        RemovedById = loginId
                    };

                    Result result = await caseService.RemoveCommissionFromOfferAsync(tenantId, caseId, command);
                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("addBeneficiaryEligibilityToCase")
                .Description("Adds a beneficiary eligibility to a case")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "The case id")
                .Argument<NonNullGraphType<BeneficiaryEligibilityInputGraphType>>("input", "the beneficiary eligibility input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");

                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases"));

                    AddBeneficiaryEligibilityCommand command = context.GetArgument<AddBeneficiaryEligibilityCommand>("input");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result<CreatedStatus> result = await caseService.AddBeneficiaryEligibilityAsync(tenantId, caseId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateBeneficiaryEligibilityOfCase")
                .Description("Updates a beneficiary eligibility of a case")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "the identifier of the case")
                .Argument<NonNullGraphType<StringGraphType>>("beneficiaryEligibilityId", "the identifier of the beneficiaryEligibility")
                .Argument<NonNullGraphType<BeneficiaryEligibilityInputGraphType>>("input", "the information of the  beneficiary eligibility to update")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string beneficiaryEligibilityId = context.GetArgument<string>("beneficiaryEligibilityId");

                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                    UpdateBeneficiaryEligibilityCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateBeneficiaryEligibilityCommand>();
                    command.Id = beneficiaryEligibilityId;
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await caseService.UpdateBeneficiaryEligibilityAsync(tenantId, caseId, command);

                    return result;
                });

            Field<ResultGraphType>()
              .Name("removeBeneficiaryEligibilityFromCase")
              .Description("removes a beneficiaryEligibility from a case")
              .AuthorizeWith("any")
              .Argument<NonNullGraphType<StringGraphType>>("caseId", "the identifier of the case")
              .Argument<NonNullGraphType<StringGraphType>>("beneficiaryEligibilityId", "the beneficiary eligibility id")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string loginId = context.GetLoginIdFromToken();
                  string caseId = context.GetArgument<string>("caseId");
                  string beneficiaryEligibilityId = context.GetArgument<string>("beneficiaryEligibilityId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                  Result result = await caseService.RemoveBeneficiaryEligibilityAsync(tenantId, caseId, new RemoveCommand
                  {
                      Id = beneficiaryEligibilityId,
                      RemovedById = loginId
                  });

                  return result;
              });

            Field<CreatedStatusResultGraphType>()
                .Name("addPaymentInfoToCase")
                .AuthorizeWith("any")
                .Description("Adds payment info to a case")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "The case id")
                .Argument<NonNullGraphType<AddPaymentInfoInputGraphType>>("input", "The payment info input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases"));

                    AddPaymentInfoCommand command = context.GetArgument<AddPaymentInfoCommand>("input");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result<CreatedStatus> result = await caseService.AddPaymentInfoAsync(tenantId, caseId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updatePaymentInfoOfCase")
                .Description("updates a payment info of a policy")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("caseId", "The case identifier")
                .Argument<NonNullGraphType<StringGraphType>>("paymentInfoId", "The payment identifier")
                .Argument<NonNullGraphType<PaymentInfoToUpdateGraphType>>("input", "The payment info input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string caseId = context.GetArgument<string>("caseId");
                    string paymentInfoId = context.GetArgument<string>("paymentInfoId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                    UpdatePaymentInfoCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdatePaymentInfoCommand>();
                    command.Id = paymentInfoId;
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await caseService.UpdatePaymentInfoAsync(tenantId, caseId, command);
                    return result;
                });

            Field<ResultGraphType>()
              .Name("removePaymentInfoFromCase")
              .Description("removes a payment info of a policy")
              .AuthorizeWith("any")
              .Argument<NonNullGraphType<StringGraphType>>("caseId", "The case identifier")
              .Argument<NonNullGraphType<StringGraphType>>("paymentInfoId", "The payment identifier")
              .ResolveAsync(async context =>
              {
                  string tenantId = context.GetTenantIdFromToken();
                  string caseId = context.GetArgument<string>("caseId");
                  string paymentInfoId = context.GetArgument<string>("paymentInfoId");
                  await permissionValidator.Authorize(context, new PermissionRequest("updateCases", "writeCases").WithTargetIds(caseId));

                  Result result = await caseService.RemovePaymentInfoAsync(tenantId, caseId, new RemoveCommand
                  {
                      Id = paymentInfoId,
                      RemovedById = context.GetLoginIdFromToken()
                  });

                  return result;
              });

            Field<CreatedStatusResultGraphType>()
                .Name("createDataSchema")
                .Argument<NonNullGraphType<CreateDataSchemaInputGraphType>>("input", "Create a dataSchema")
                .ResolveAsync(async context =>
                {

                    string tenantId = context.GetTenantIdFromToken();
                    CreateDataSchemaInput input = context.GetArgument<CreateDataSchemaInput>("input");
                    await permissionValidator.Authorize(context, new PermissionRequest("createDataSchemas", "writeDataSchemas"));
                    string loginId = context.GetLoginIdFromToken();
                    var command = new CreateDataSchemaCommand
                    {
                        Name = input.Name,
                        Description = input.Description,
                        Schema = input.Schema,
                        Standard = input.Standard,
                        Type = input.Type,
                        CreatedById = loginId,
                        Tags = input.Tags
                    };

                    return await caseService.CreateDataSchemaAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("updateDataSchema")
                .AuthorizeWith("any")
                .Description("Modifies a dataSchema")
                .Argument<NonNullGraphType<UpdateDataSchemaInputGraphType>>("input", "the modified dataSchema")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    UpdateDataSchemaInput input = context.GetArgument<UpdateDataSchemaInput>("input");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateDataSchemas", "writeDataSchemas").WithTargetIds(input.DataSchemaId));

                    return await caseService.UpdateDataSchemaAsync(tenantId, new UpdateDataSchemaCommand
                    {
                        DataSchemaId = input.DataSchemaId,
                        Name = input.Name,
                        Description = input.Description,
                        Schema = input.Schema,
                        Standard = input.Standard,
                        Type = input.Type,
                        ModifiedById = context.GetLoginIdFromToken(),
                        Tags = input.Tags
                    });
                });

            Field<ResultGraphType>()
                .Name("deleteDataSchema")
                .Argument<NonNullGraphType<DeleteDataSchemaInputGraphType>>("input", "Delete a dataSchema")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    DeleteDataSchemaInput command = context.GetArgument<DeleteDataSchemaInput>("input");
                    await permissionValidator.Authorize(context, new PermissionRequest("deleteDataSchemas", "writeDataSchemas").WithTargetIds(command.DataSchemaId));

                    string loginId = context.GetLoginIdFromToken();

                    return await caseService.DeleteDataSchemaAsync(tenantId, command.DataSchemaId, new DeleteCommand { DeletedById = loginId });
                });

            Field<ResultGraphType>()
                .Name("addUiSchemaToDataSchema")
                .Argument<NonNullGraphType<AddUiSchemaToDataSchemaInputGraphType>>("input", "Add a uiSchema to the dataSchema")
                .ResolveAsync(async context =>
                {
                    AddUiSchemaToDataSchemaInput input = context.GetArgument<AddUiSchemaToDataSchemaInput>("input");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateDataSchemas", "writeDataSchemas").WithTargetIds(input.DataSchemaId));

                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    AddUiSchemaToDataSchemaCommand command = new()
                    {
                        DataSchemaId = input.DataSchemaId,
                        UiSchemaId = input.UiSchemaId,
                        AddedById = loginId
                    };

                    return await caseService.AddUiSchemaToDataSchemaAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("removeUiSchemaToDataSchema")
                .Argument<NonNullGraphType<RemoveUiSchemaFromDataSchemaInputGraphType>>("input", "Remove uiSchema from dataSchema")
                .ResolveAsync(async context =>
                {
                    RemoveUiSchemaFromDataSchemaInput input = context.GetArgument<RemoveUiSchemaFromDataSchemaInput>("input");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateDataSchemas", "writeDataSchemas").WithTargetIds(input.DataSchemaId));
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    RemoveUiSchemaFromDataSchemaCommand command = new()
                    {
                        UiSchemaId = input.UiSchemaId,
                        DataSchemaId = input.DataSchemaId,
                        RemovedById = loginId
                    };

                    return await caseService.RemoveUiSchemaFromDataSchemaAsync(tenantId, command);
                });
        }
    }
}
