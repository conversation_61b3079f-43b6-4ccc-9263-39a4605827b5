using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Interfaces
{
    public static class JsonExtensions
    {
        public static bool TryParseToJToken(this string strInput, out JToken jToken)
        {
            jToken = null;
            if (string.IsNullOrWhiteSpace(strInput)) return false;
            strInput = strInput.Trim();
            if (strInput.StartsWith("{") && strInput.EndsWith("}") ||
                strInput.StartsWith("[") && strInput.EndsWith("]"))
            {
                try
                {
                    jToken = JToken.Parse(strInput);
                    return true;
                }
                catch
                {
                    return false;
                }
            }

            return false;
        }
    }
}