﻿using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Interfaces.Auth;

using GraphQL.DataLoader;
using GraphQL.Types;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

using System;
using System.Collections.Generic;
using System.Linq;
using CoverGo.DomainUtils;
using CurrencyCode = CoverGo.Gateway.Domain.CurrencyCode;
using CoverGo.Gateway.Interfaces.Types;

namespace CoverGo.Gateway.Interfaces.Products
{
    public class PricingGraph : SystemObjectGraph
    {
        public decimal? Amount { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public PaymentFrequency PaymentFrequency { get; set; }
        public string FormattedPrice { get; set; }
        
        public List<DiscountGraph> AppliedDiscounts { get; set; }
        public List<string> DiscountCodes { get; set; }
        public List<TaxGraph> AppliedTaxes { get; set; }
        public List<DiscountGraph> Discounts { get; set; }
        public List<LoadingGraph> Loadings { get; set; }
        public decimal? OriginalPrice { get; set; }
        public string FormattedOriginalPrice { get; set; }
        public List<IndicativePriceGraph> IndicativePrices { get; set; }
        public List<PricingOptionGraph> Options { get; set; }
        public string AmountLogic { get; set; }
        public string Metadata { get; set; }

        public Dictionary<string,object> MetaDataJson { get; set; }
        public bool IsPricedAtStartDate { get; set; }

        public IEnumerable<PricingGraph> ConvertedPrices { get; set; }

        public static PricingGraph ToGraph(AugmentedPrice p, ProductId productId) =>
            p == null
                ? null
                : new PricingGraph
                {
                    Amount = p.Amount,
                    CurrencyCode = p.CurrencyCode,
                    PaymentFrequency = p.PaymentFrequency,
                    AppliedDiscounts = p.AppliedDiscounts?.Select(ad => DiscountGraph.ToGraph(ad)).ToList(),
                    DiscountCodes = p.DiscountCodes?.ToList(),
                    AppliedTaxes = p.AppliedTaxes?.Select(at => TaxGraph.ToGraph(at)).ToList(),
                    Discounts = p.Discounts?.Select(d => DiscountGraph.ToGraph(d)).ToList(),
                    Loadings = p.Loadings?.Select(l => LoadingGraph.ToGraph(l, productId))?.ToList(),
                    OriginalPrice = p.OriginalPrice,
                    Metadata = p.Metadata,
                    IsPricedAtStartDate = p.IsPricedAtStartDate,
                }.PopulateSystemGraphFields(p);

        public static PricingGraph ToGraph(PriceDto price, ProductId productId) =>
            price == null
             ? null
             : new PricingGraph
             {
                 Amount = price.Amount,
                 CurrencyCode = price.CurrencyCode,
                 PaymentFrequency = price.PaymentFrequency,
                 AppliedDiscounts = price.AppliedDiscounts?.Select(ad => DiscountGraph.ToGraph(ad)).ToList(),
                 AppliedTaxes = price.AppliedTaxes?.Select(at => TaxGraph.ToGraph(at)).ToList(),
                 Discounts = price.Discounts?.Select(d => DiscountGraph.ToGraph(d)).ToList(),
                 DiscountCodes = price.Discounts?.Select(d => d.Code)?.Where(c => c != null)?.ToList(),
                 Loadings = price.Loadings?.Select(l => LoadingGraph.ToGraph(l, productId))?.ToList(),
                 OriginalPrice = price.OriginalPrice,
                 IndicativePrices = price.IndicativePrices?.Select(i => new IndicativePriceGraph
                 {
                     Amount = i.Amount,
                     CurrencyCode = i.CurrencyCode,
                     Type = i.Type,
                     AppliedDiscounts = i.AppliedDiscounts?.Select(d => DiscountGraph.ToGraph(d))?.ToList(),
                     Discounts = i.Discounts?.Select(d => DiscountGraph.ToGraph(d))?.ToList(),
                     AppliedTaxes = i.AppliedTaxes?.Select(t => TaxGraph.ToGraph(t))?.ToList(),
                     OriginalPrice = i.OriginalPrice
                 }).ToList(),
                 Options = price.Options?.Select(o => new PricingOptionGraph
                 {
                     Id = o.Id,
                     Key = o.Key,
                     Value = o.Value?.ToScalarValue(),
                     Values = o.Value != null
                         ? JsonConvert.DeserializeObject<Dictionary<string, JToken>>(o.Value?.ToString()) //: need to correct this 2019/17/15
                             .Select(v => new KeyScalarValue //to remove $type and$ values from serializationBinder in policies when querying policies dto.Values?.Select(v => new KeyScalarValue
                             {
                                 Key = v.Key,
                                 Value = v.Value?.ToScalarValue()
                             }).ToList()
                         : null,
                     ValueJsonString = o.Value?.ToString()
                 }).ToList(),
                 AmountLogic = price.AmountLogic,
                 Metadata = price.Metadata,
                 MetaDataJson = GetMetaDataDictionary(price.Metadata) 
             }.PopulateSystemGraphFields(price);

        private static Dictionary<string,object> GetMetaDataDictionary(string metaData)
        {
            if (metaData == null)
                return null;

            return JsonConvert.DeserializeObject<Dictionary<string, object>>(metaData);
        }

        public static PriceDto ToDomain(PricingGraph price) =>
            price == null
                ? null
                : new PriceDto
                {
                    Amount = price.Amount,
                    CurrencyCode = price.CurrencyCode ?? Domain.CurrencyCode.Undefined,
                    AppliedDiscounts = price.AppliedDiscounts?.Select(ad => new Discount
                    {
                        Id = ad.Id,
                        Ratio = ad.Ratio,
                        AlwaysApplies = ad.AlwaysApplies,
                        Order = ad.Order,
                        Name = ad.Name,
                        Code = ad.Code,
                        CurrencyCode = ad.CurrencyCode,
                        Flat = ad.Flat,
                        NewPrice = ad.NewPrice,
                        OriginalPrice = ad.OriginalPrice
                    }),
                    AppliedTaxes = price.AppliedTaxes?.Select(at => new Tax
                    {
                        Id = at.Id,
                        Code = at.Code,
                        Order = at.Order,
                        Ratio = at.Ratio,
                        CurrencyCode = at.CurrencyCode,
                        Flat = at.Flat,
                        NewPrice = at.NewPrice,
                        OriginalPrice = at.OriginalPrice
                    }),
                    Discounts = price.Discounts?.Select(ad => new Discount
                    {
                        Id = ad.Id,
                        Ratio = ad.Ratio,
                        AlwaysApplies = ad.AlwaysApplies,
                        Order = ad.Order,
                        Name = ad.Name,
                        Code = ad.Code,
                        CurrencyCode = ad.CurrencyCode,
                        Flat = ad.Flat,
                        NewPrice = ad.NewPrice,
                        OriginalPrice = ad.OriginalPrice,
                        IsManual = ad.IsManual
                    }),
                    Loadings = price.Loadings?.Select(at => new Loading
                    {
                        Id = at.Id,
                        Ratio = at.Ratio,
                        CurrencyCode = at.CurrencyCode,
                        Flat = at.Flat,
                        OriginalPrice = at.OriginalPrice,
                        NewPrice = at.NewPrice,
                        Order = at.Order,
                        Code = at.CodeName?.Code
                    }),
                    OriginalPrice = price.OriginalPrice
                };
    }

    public class PriceInputGraphType : InputObjectGraphType<Price>
    {
        public PriceInputGraphType()
        {
            Name = "priceInput";

            Field(p => p.Amount);
            Field(p => p.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));
        }
    }

    public class PriceGraphType : ObjectGraphType<PriceGraph>
    {
        public PriceGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService)
        {
            Name = "price";

            Field(p => p.Amount);
            Field(p => p.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(i => i.FormattedPrice).ResolveAsync(context => context.GetFormattedPriceAsync(accessor, l10nService, context.Source.Amount, context.Source.CurrencyCode));
        }
    }

    public class IndicativePriceGraph
    {
        public string Type { get; set; }
        public decimal Amount { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public string FormattedPrice { get; set; }
        public decimal OriginalPrice { get; set; }
        public string FormattedOriginalPrice { get; set; }
        
        public List<DiscountGraph> AppliedDiscounts { get; set; }
        public List<DiscountGraph> Discounts { get; set; }
        public List<TaxGraph> AppliedTaxes { get; set; }
    }

    public class PricingOptionGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Key { get; set; }
        public ScalarValue Value { get; set; }
        
        public List<KeyScalarValue> Values { get; set; }
        public string ValueJsonString { get; set; }
    }

    public class PricingOptionGraphType : ObjectGraphType<PricingOptionGraph>
    {
        public PricingOptionGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService)
        {
            Name = "pricingOption";

            Field(o => o.Id);
            Field(o => o.Key, nullable: true);
            Field(o => o.Name, nullable: true)
                .ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"pricing-options-{context.Source.Key}-name"));
            Field(o => o.Description, nullable: true)
                .ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"pricing-options-{context.Source.Key}-description"));
            Field(o => o.Values, type: typeof(ListGraphType<KeyValueGraphType>)).DeprecationReason("use `value` instead");
            Field(o => o.Value, type: typeof(ScalarValueGraphType));
            Field(o => o.ValueJsonString, nullable: true);
        }
    }

    public class PricingOptionInputGraphType : InputObjectGraphType<PricingOptionGraph>
    {
        public PricingOptionInputGraphType()
        {
            Name = "pricingOptionInput";

            Field(o => o.Key);
            Field(o => o.ValueJsonString, nullable: true);
        }
    }

    public class UpdatePricingOptionInputGraphType : InputObjectGraphType<PricingOptionGraph>
    {
        public UpdatePricingOptionInputGraphType()
        {
            Name = "updatePricingOptionInput";
            
            Field(o => o.Key, nullable: true);
            Field(o => o.ValueJsonString, nullable: true);
        }
    }

    public class IndicativePriceGraphType : ObjectGraphType<IndicativePriceGraph>
    {
        public IndicativePriceGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService)
        {
            Name = "indicativePrice";

            Field(i => i.Type);
            Field(i => i.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(i => i.Amount);
            Field(i => i.FormattedPrice).ResolveAsync(context => context.GetFormattedPriceAsync(accessor, l10nService, context.Source.Amount, context.Source.CurrencyCode));
            Field(i => i.AppliedDiscounts, type: typeof(ListGraphType<DiscountGraphType>));
            Field(i => i.Discounts, type: typeof(ListGraphType<DiscountGraphType>));
            Field(i => i.AppliedTaxes, type: typeof(ListGraphType<TaxGraphType>));
            Field(i => i.OriginalPrice);
            Field(p => p.FormattedOriginalPrice, nullable: true).ResolveAsync(context => context.GetFormattedPriceAsync(accessor, l10nService, context.Source.OriginalPrice, context.Source.CurrencyCode));
        }
    }

    public class PricingGraphType : ObjectGraphType<PricingGraph>
    {
        public PricingGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IL10nService l10nService,
            IPricingService pricingService,
            PermissionValidator permissionValidator)
        {
            Name = "pricing";
            Field(p => p.Amount, nullable: true);
            Field(p => p.FormattedPrice, nullable: true).ResolveAsync(context => context.GetFormattedPriceAsync(accessor, l10nService, context.Source.Amount, context.Source.CurrencyCode));

            Field(p => p.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType), nullable: true);
            Field(p => p.PaymentFrequency, type: typeof(PaymentFrequencyEnumerationGraphType));
            Field(p => p.AppliedDiscounts, type: typeof(ListGraphType<DiscountGraphType>), nullable: true);
            Field(p => p.DiscountCodes, nullable: true);
            Field(p => p.Discounts, type: typeof(ListGraphType<DiscountGraphType>), nullable: true);
            Field(p => p.AppliedTaxes, type: typeof(ListGraphType<TaxGraphType>), nullable: true);
            Field(p => p.Loadings, type: typeof(ListGraphType<LoadingGraphType>), nullable: true);
            Field(p => p.OriginalPrice, nullable: true);
            Field(p => p.FormattedOriginalPrice, nullable: true).ResolveAsync(context => context.GetFormattedPriceAsync(accessor, l10nService, context.Source.OriginalPrice, context.Source.CurrencyCode));

            Field(p => p.IndicativePrices, type: typeof(ListGraphType<IndicativePriceGraphType>));

            Field(p => p.Options, type: typeof(ListGraphType<PricingOptionGraphType>));

            Field(p => p.AmountLogic, nullable: true);

            Field(p => p.Metadata, nullable: true);

            Field(p => p.MetaDataJson, nullable: true, type: typeof(JsonGraphType));

            Field(p => p.IsPricedAtStartDate, nullable: true);

            Field(p => p.ConvertedPrices, type: typeof(NonNullGraphType<ListGraphType<PricingGraphType>>))
                .Argument<NonNullGraphType<ExchangeRateConversionOptionsInputGraphType>>("options", "The conversion options")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    ExchangeRateConversionOptions options = context.GetArgument<ExchangeRateConversionOptions>("options");
                    var pricesLoader = accessor.Context.GetOrAddCollectionBatchLoader<PriceDto, PriceDto>("GetConvertedPrices",
                        async p => (await pricingService.GetConvertedPricingsAsync(tenantId, new ExchangeRateConversionInput
                        {
                            Options = options,
                            Prices = p
                        }))
                        .ToLookup(x => x, new PriceDtoDataLoaderEqualityComparer()));

                    PriceDto priceDto = PricingGraph.ToDomain(context.Source);
                    priceDto.DataLoaderCacheId = Guid.NewGuid().ToString();
                    IEnumerable<PriceDto> priceDtos = await pricesLoader.LoadAsync(priceDto);

                    return priceDtos.Select(p => PricingGraph.ToGraph(p, null));
                });

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    //public class Price
    //{
    //    public decimal Amount { get; set; }
    //    public CurrencyCode Currency { get; set; }
    //}

    //public class PriceGraphType

    public class PriceDtoDataLoaderEqualityComparer : IEqualityComparer<PriceDto>
    {
        public bool Equals(PriceDto x, PriceDto y) => x.DataLoaderCacheId == y.DataLoaderCacheId;
        public int GetHashCode(PriceDto obj) => HashCode.Combine(obj.DataLoaderCacheId);
    }

    public class ExchangeRateConversionOptionsInputGraphType : InputObjectGraphType<ExchangeRateConversionOptions>
    {
        public ExchangeRateConversionOptionsInputGraphType()
        {
            Name = "exchangeRateConversionOptionsInput";

            Field(e => e.CurrencyCodes, type: typeof(NonNullGraphType<ListGraphType<CurrencyCodeEnumerationGraphType>>));
        }
    }

    public class LoadingGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public CodeNameGraph CodeName { get; set; }
        public decimal? Ratio { get; set; }
        public decimal? Flat { get; set; }
        public string FormattedFlat { get; set; }
        public CurrencyCode? CurrencyCode { get; set; }
        public decimal? OriginalPrice { get; set; }
        public decimal? NewPrice { get; set; }
        public string FormattedOriginalPrice { get; set; }
        public string FormattedNewPrice { get; set; }
        public string CalculationJsonLogic { get; set; }
        public int Order { get; set; }

        public static LoadingGraph ToGraph(Loading l, ProductId productId) =>
            l == null
                ? null
                : new LoadingGraph
                {
                    Id = l.Id,
                    CodeName = new CodeNameGraph { Code = l.Code, ProductId = productId },
                    Flat = l.Flat,
                    Ratio = l.Ratio,
                    OriginalPrice = l.OriginalPrice,
                    NewPrice = l.NewPrice,
                    CurrencyCode = l.CurrencyCode,
                    Order = l.Order,
                    CalculationJsonLogic = l.CalculationJsonLogic?.ToString(),
                }.PopulateSystemGraphFields(l);
    }

    public class LoadingGraphType : ObjectGraphType<LoadingGraph>
    {
        public LoadingGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService)
        {
            Name = "loading";
            Description = "a loading";
            Field(l => l.Id);
            Field(l => l.CodeName, type: typeof(LoadingCodeNameGraphType), nullable: true);
            Field(l => l.Flat, nullable: true);
            Field(l => l.Ratio, nullable: true);
            Field(l => l.CalculationJsonLogic, nullable: true);
            Field(d => d.Order, nullable: true);
            Field(l => l.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType), nullable: true);
            Field(l => l.OriginalPrice, nullable: true);
            Field(p => p.FormattedOriginalPrice, nullable: true).ResolveAsync(context => context.GetFormattedPriceAsync(accessor, l10nService, context.Source.OriginalPrice, context.Source.CurrencyCode));
            Field(l => l.NewPrice, nullable: true);
            Field(p => p.FormattedNewPrice, nullable: true).ResolveAsync(context => context.GetFormattedPriceAsync(accessor, l10nService, context.Source.NewPrice, context.Source.CurrencyCode));
        }
    }

    public class CodeNameGraph
    {
        public ProductId ProductId { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
    }

    public class BenefitOptionInputGraphType : InputObjectGraphType<BenefitOptionInputGraph>
    {
        public BenefitOptionInputGraphType()
        {
            Name = "benefitOptionInput";
            Description = "Chosen benefit option";

            Field(o => o.TypeId);
            Field(o => o.Key, nullable: true).Description("Input the key if the benefit has a set of options");
            Field(o => o.Value, type: typeof(ScalarValueInputGraphType), nullable: true).Description("If free to input any value, fill this.");
            Field(o => o.InsuredId, nullable: true);
        }
    }

    public class BenefitOptionInputGraph
    {
        public string OfferId { get; set; }
        public string TypeId { get; set; }
        public string Key { get; set; }
        public ScalarValue Value { get; set; }
        public string InsuredId { get; set; }

        public static UpsertBenefitOptionCommand ToCommand(BenefitOptionInputGraph graph, string proposalId, string offerId, string upsertById) =>
            graph == null
            ? null
            : new UpsertBenefitOptionCommand
            {
                ProposalId = proposalId,
                OfferId = offerId,
                TypeId = graph.TypeId,
                Key = graph.Key,
                Value = graph.Value != null
                ? JToken.FromObject(graph.Value?.GetValue())
                : null,
                InsuredId = graph.InsuredId,
                UpsertedById = upsertById
            };
    }

    public class DiscountGraphType : ObjectGraphType<DiscountGraph>
    {
        public DiscountGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IL10nService l10nService,
            PermissionValidator permissionValidator)
        {
            Name = "discount";

            Field(d => d.Id, nullable: true);
            Field(d => d.Code, nullable: true);
            Field(d => d.Name, nullable: true);
            Field(d => d.Ratio, nullable: true);
            Field(d => d.Flat, nullable: true);
            Field(t => t.FormattedFlat, nullable: true).ResolveAsync(context => context.GetFormattedPriceAsync(accessor, l10nService, context.Source.Flat, context.Source.CurrencyCode));
            Field(d => d.OriginalPrice, nullable: true);
            Field(d => d.NewPrice, nullable: true);
            Field(d => d.FormattedOriginalPrice, nullable: true).ResolveAsync(context => context.GetFormattedPriceAsync(accessor, l10nService, context.Source.OriginalPrice, context.Source.CurrencyCode));
            Field(d => d.FormattedNewPrice, nullable: true).ResolveAsync(context => context.GetFormattedPriceAsync(accessor, l10nService, context.Source.NewPrice, context.Source.CurrencyCode));
            Field(d => d.Order, nullable: true);
            Field(d => d.CalculationLogic, nullable: true);
            Field(d => d.IsManual);

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class DiscountGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public decimal? Ratio { get; set; }
        public decimal? Flat { get; set; }
        public string FormattedFlat { get; set; }
        public decimal? OriginalPrice { get; set; }
        public decimal? NewPrice { get; set; }
        public string FormattedOriginalPrice { get; set; }
        public string FormattedNewPrice { get; set; }
        public bool AlwaysApplies { get; set; }
        public int Order { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public string CalculationLogic { get; set; }
        public bool IsManual { get; set; }

        public static DiscountGraph ToGraph(Discount discount) => discount == null
            ? null
            : new DiscountGraph
            {
                Id = discount.Id,
                Code = discount.Code,
                Name = discount.Name,
                CurrencyCode = discount.CurrencyCode,
                NewPrice = discount.NewPrice,
                Order = discount.Order,
                OriginalPrice = discount.OriginalPrice,
                AlwaysApplies = discount.AlwaysApplies,
                Flat = discount.Flat,
                Ratio = discount.Ratio,
                CalculationLogic = discount.CalculationJsonLogic?.ToString(),
                IsManual = discount.IsManual,
            }.PopulateSystemGraphFields(discount);
    }

    public class TaxGraphType : ObjectGraphType<TaxGraph>
    {
        public TaxGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IL10nService l10nService,
            PermissionValidator permissionValidator)
        {
            Name = "tax";

            Field(t => t.Code, nullable: true);
            Field(t => t.Name, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"taxes-{context.Source.Code}-name"));
            Field(t => t.OriginalPrice, nullable: true);
            Field(t => t.NewPrice, nullable: true);
            Field(t => t.FormattedOriginalPrice, nullable: true).ResolveAsync(context => context.GetFormattedPriceAsync(accessor, l10nService, context.Source.OriginalPrice, context.Source.CurrencyCode));
            Field(t => t.FormattedNewPrice, nullable: true).ResolveAsync(context => context.GetFormattedPriceAsync(accessor, l10nService, context.Source.NewPrice, context.Source.CurrencyCode));
            Field(t => t.Order, nullable: true);
            Field(t => t.Ratio, nullable: true);
            Field(t => t.Flat, nullable: true);
            Field(t => t.FormattedFlat, nullable: true).ResolveAsync(context => context.GetFormattedPriceAsync(accessor, l10nService, context.Source.Flat, context.Source.CurrencyCode));
            Field(d => d.CalculationLogic, nullable: true);

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class TaxGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public decimal? OriginalPrice { get; set; }
        public decimal? NewPrice { get; set; }
        public string FormattedOriginalPrice { get; set; }
        public string FormattedNewPrice { get; set; }
        public decimal? Ratio { get; set; }
        public decimal? Flat { get; set; }
        public string FormattedFlat { get; set; }
        public int Order { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public string CalculationLogic { get; set; }

        public static TaxGraph ToGraph(Tax tax) => tax == null
            ? null
            : new TaxGraph
            {
                Id = tax.Id,
                Code = tax.Code,
                CurrencyCode = tax.CurrencyCode,
                NewPrice = tax.NewPrice,
                Flat = tax.Flat,
                Ratio = tax.Ratio,
                Order = tax.Order,
                OriginalPrice = tax.OriginalPrice,
                CalculationLogic = tax.CalculationJsonLogic?.ToString(),
            }.PopulateSystemGraphFields(tax);
    }

    public class PremiumInputGraphType : InputObjectGraphType<PremiumInput>
    {
        public PremiumInputGraphType()
        {
            Name = "premiumInput";
            Description = "";

            Field(p => p.Amount, nullable: true);
            Field(p => p.GrossAmount, nullable: true);
            Field(p => p.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(p => p.DiscountCodes, nullable: true);
            Field(p => p.IsPricedAtStartDate, nullable: true);
        }
    }

    public class PremiumToUpdateGraphType : ObjectGraphType<PremiumToUpdate>
    {
        public PremiumToUpdateGraphType()
        {
            Name = "premiumToUpdate";

            Field(p => p.Amount, nullable: true);
            Field(p => p.IsAmountChanged);
            Field(p => p.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType), nullable: true);
            Field(p => p.IsCurrencyCodeChanged);
            Field(p => p.GrossAmount, nullable: true);
            Field(p => p.IsGrossAmountChanged, nullable: true);
            Field(p => p.DiscountCodes, nullable: true);
            Field(p => p.IsDiscountCodesChanged, nullable: true);
            Field(p => p.IsPricedAtStartDate, nullable: true);
        }
    }

    public class DiscountInputGraphType : InputObjectGraphType<Discount>
    {
        public DiscountInputGraphType()
        {
            Name = "discountInput";
            Description = "";

            Field(d => d.Code, nullable: true);
            Field(d => d.OriginalPrice, nullable: true);
            Field(d => d.Flat, nullable: true);
            Field(d => d.Ratio, nullable: true);

            Field(d => d.CreatedAt, nullable: true);
            Field(d => d.CreatedById, nullable: true);
            Field(d => d.LastModifiedAt, nullable: true);
            Field(d => d.LastModifiedById, nullable: true);
        }
    }

    public class AddPaymentInfoInputGraphType : InputObjectGraphType<AddPaymentInfoCommand>
    {
        public AddPaymentInfoInputGraphType()
        {
            Name = "addPaymentInfoInput";
            Description = "Input to add the payment info to a policy";

            Field(p => p.Name, nullable: true);
            Field(p => p.PayorId, nullable: true);
            Field(p => p.StartDate, type: typeof(DateTimeGraphType));
            Field(p => p.EndDate, type: typeof(DateTimeGraphType));
            Field(p => p.Comment, nullable: true);
            Field(p => p.Frequency, type: typeof(PaymentFrequencyEnumerationGraphType));
            Field(p => p.Method, type: typeof(PaymentMethodEnumerationGraphType));
            Field(p => p.Amount, nullable: true);
            Field(p => p.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));
        }
    }

    public class PaymentFrequencyEnumerationGraphType : EnumerationGraphType<PaymentFrequency>
    {
        public PaymentFrequencyEnumerationGraphType()
        {
            Name = "paymentFrequency";
            Description = "The list of possible payment frequencies";
        }
    }

    public class PaymentMethodEnumerationGraphType : EnumerationGraphType<PaymentMethodType>
    {
        public PaymentMethodEnumerationGraphType()
        {
            Name = "paymentMethod";
            Description = "The list of possible payment methods";
        }
    }

    public class LoadingInputGraphType : InputObjectGraphType<LoadingInputGraph>
    {
        public LoadingInputGraphType()
        {
            Name = "loadingInput";
            Description = "The loading input";

            Field(l => l.OfferId, nullable: true);
            Field(l => l.Code, nullable: true);
            Field(l => l.Ratio, nullable: true);
            Field(l => l.Flat, nullable: true);
            Field(l => l.CalculationJsonLogic, nullable: true);
            Field(l => l.Order, nullable: true);
        }
    }

    public class LoadingInputGraph
    {
        public string OfferId { get; set; }
        public string Code { get; set; }
        public decimal? Ratio { get; set; }
        public decimal? Flat { get; set; }
        public string CalculationJsonLogic { get; set; }
        public int Order { get; set; }
    }

    public class AddDiscountToOfferInputGraphType : InputObjectGraphType<DiscountInputGraph>
    {
        public AddDiscountToOfferInputGraphType()
        {
            Name = "addDiscountToOfferInput";
            Description = "The discount input";

            Field(l => l.CalculationJsonLogic, nullable: true);
            Field(l => l.Name, nullable: true);
            Field(l => l.Order, nullable: true);
        }
    }

    public class DiscountInputGraph
    { 
        public string Name { get; set; }
        public int Order { get; set; }
        public string CalculationJsonLogic { get; set; }
    }


    public class UpdateDiscountInputGraphType : InputObjectGraphType<DiscountInputGraph>
    {
        public UpdateDiscountInputGraphType()
        {
            Name = "updateDiscountOfOfferInput";

            Field(d => d.CalculationJsonLogic, nullable: true);
            Field(d => d.Name, nullable: true);
            Field(d => d.Order, nullable: true);
        }
    }

    public class AddDiscountCommandGraph
    {
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public int Order { get; set; }
        public string Name { get; set; }
        public string CalculationJsonLogic { get; set; }

        public LoginGraph AddedBy { get; set; }

        public static AddDiscountCommandGraph ToGraph(AddDiscountCommand command) =>
            new()
            {
                CommandId = command.CommandId,
                Timestamp = command.Timestamp,
                Name = command.Name,
                Order = command.Order,
                CalculationJsonLogic = command.CalculationJsonLogic?.ToString(),
                AddedBy = new LoginGraph { Id = command.AddedById }
            };
    }

    public class AddDiscountCommandGraphType : ObjectGraphType<AddDiscountCommandGraph>
    {
        public AddDiscountCommandGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "addDiscountCommand";

            Field(p => p.CommandId, nullable: true);
            Field(p => p.Timestamp, type: typeof(DateTimeGraphType));
            Field(d => d.Name, nullable: true);
            Field(d => d.Order, nullable: true);
            Field(d => d.CalculationJsonLogic, nullable: true);
            Field(d => d.AddedBy, type: typeof(LoginGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.AddedBy?.Id == null)
                        return null;

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readLogins", new List<string> { context.Source.AddedBy.Id });
                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.AddedBy.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                    i => authService.GetLoginsDictionaryAsync(tenantId, i));

                    Login loginDao = await loginLoader.LoadAsync(context.Source.AddedBy.Id);

                    return LoginGraph.ToGraph(loginDao);
                });
        }
    }

    public class UpdateDiscountCommandGraph
    {
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public string DiscountId { get; set; }
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public int Order { get; set; }
        public bool IsOrderChanged { get; set; }
        public string CalculationJsonLogic { get; set; }
        public bool IsCalculationJsonLogicChanged { get; set; }

        public LoginGraph ModifiedBy { get; set; }

        public static UpdateDiscountCommandGraph ToGraph(UpdateDiscountCommand command) => 
            new()
            {
                CommandId = command.CommandId,
                Timestamp = command.Timestamp,
                DiscountId = command.DiscountId,
                Name = command.Name,
                IsNameChanged = command.IsNameChanged,
                Order = command.Order,
                IsOrderChanged = command.IsOrderChanged,
                CalculationJsonLogic = command.CalculationJsonLogic?.ToString(),
                IsCalculationJsonLogicChanged = command.IsCalculationJsonLogicChanged,
                ModifiedBy = new LoginGraph { Id = command.ModifiedById }
            };
    }

    public class UpdateDiscountCommandGraphType : ObjectGraphType<UpdateDiscountCommandGraph>
    {
        public UpdateDiscountCommandGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "updateDiscountCommand";

            Field(p => p.CommandId, nullable: true);
            Field(p => p.Timestamp, type: typeof(DateTimeGraphType));
            Field(d => d.DiscountId, nullable: true);
            Field(d => d.Name, nullable: true);
            Field(d => d.IsNameChanged, nullable: true);
            Field(d => d.Order, nullable: true);
            Field(d => d.IsOrderChanged, nullable: true);
            Field(d => d.CalculationJsonLogic, nullable: true);
            Field(d => d.IsCalculationJsonLogicChanged, nullable: true);
            Field(d => d.ModifiedBy, type: typeof(LoginGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.ModifiedBy?.Id == null)
                        return null;

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readLogins", new List<string> { context.Source.ModifiedBy.Id });
                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.ModifiedBy.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                    i => authService.GetLoginsDictionaryAsync(tenantId, i));

                    Login loginDao = await loginLoader.LoadAsync(context.Source.ModifiedBy.Id);

                    return LoginGraph.ToGraph(loginDao);
                });
        }
    }

    public class AddLoadingCommandGraph
    {
        public string CommandId { get; set; }
        public DateTime Timestamp { get; set; }
        public string Id { get; set; }
        public string Code { get; set; }
        public decimal? Ratio { get; set; }
        public decimal? Flat { get; set; }
        public string CalculationJsonLogic { get; set; }
        public int Order { get; set; }

        public LoginGraph AddedBy { get; set; }

        public static AddLoadingCommandGraph ToGraph(AddLoadingCommand command) =>
            new()
            {
                CommandId = command.CommandId,
                Timestamp = command.Timestamp,
                Id = command.Id,
                Order = command.Order,
                Flat = command.Flat,
                Ratio = command.Ratio,
                Code = command.Code,
                CalculationJsonLogic = command.CalculationJsonLogic?.ToString(),
                AddedBy = new LoginGraph { Id = command.AddedById }
            };
    }

    public class AddLoadingCommandGraphType : ObjectGraphType<AddLoadingCommandGraph>
    {
        public AddLoadingCommandGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "addLoadingCommand";

            Field(p => p.CommandId, nullable: true);
            Field(p => p.Timestamp, type: typeof(DateTimeGraphType));
            Field(l => l.Id);
            Field(l => l.Code, nullable: true);
            Field(l => l.Flat, nullable: true);
            Field(l => l.Ratio, nullable: true);
            Field(l => l.CalculationJsonLogic, nullable: true);
            Field(d => d.Order, nullable: true);

            Field(d => d.AddedBy, type: typeof(LoginGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.AddedBy?.Id == null)
                        return null;

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readLogins", new List<string> { context.Source.AddedBy.Id });
                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.AddedBy.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                    i => authService.GetLoginsDictionaryAsync(tenantId, i));

                    Login loginDao = await loginLoader.LoadAsync(context.Source.AddedBy.Id);

                    return LoginGraph.ToGraph(loginDao);
                });
        }
    }

    public class UpdateLoadingCommandGraph
    {
        public string CommandId { get; set; } // for endorsements
        public DateTime Timestamp { get; set; } // for endorsements
        public int Order { get; set; }
        public bool IsOrderChanged { get; set; }
        public string CalculationJsonLogic { get; set; }
        public bool IsCalculationJsonLogicChanged { get; set; }
        public string Code { get; set; }
        public bool IsCodeChanged { get; set; }
        public decimal? Ratio { get; set; }
        public bool IsRatioChanged { get; set; }
        public decimal? Flat { get; set; }
        public bool IsFlatChanged { get; set; }

        public LoginGraph ModifiedBy { get; set; }

        public static UpdateLoadingCommandGraph ToGraph(UpdateLoadingCommand command) =>
            new()
            {
                CommandId = command.CommandId,
                Timestamp = command.Timestamp,
                Order = command.Order,
                IsOrderChanged = command.IsOrderChanged,
                CalculationJsonLogic = command.CalculationJsonLogic?.ToString(),
                IsCalculationJsonLogicChanged = command.IsCalculationJsonLogicChanged,
                Code = command.Code,
                IsCodeChanged = command.IsCodeChanged,
                Flat = command.Flat,
                IsFlatChanged = command.IsFlatChanged,
                Ratio = command.Ratio,
                IsRatioChanged = command.IsRatioChanged,
                ModifiedBy = new LoginGraph { Id = command.ModifiedById }
            };
    }

    public class UpdateLoadingCommandGraphType : ObjectGraphType<UpdateLoadingCommandGraph>
    {
        public UpdateLoadingCommandGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "updateLoadingCommand";

            Field(p => p.CommandId, nullable: true);
            Field(p => p.Timestamp, type: typeof(DateTimeGraphType));
            Field(d => d.Order, nullable: true);
            Field(d => d.IsOrderChanged, nullable: true);
            Field(d => d.CalculationJsonLogic, nullable: true);
            Field(d => d.IsCalculationJsonLogicChanged, nullable: true);
            Field(d => d.Code, nullable: true);
            Field(d => d.IsCodeChanged, nullable: true);
            Field(d => d.Flat, nullable: true);
            Field(d => d.IsFlatChanged, nullable: true);
            Field(d => d.Ratio, nullable: true);
            Field(d => d.IsRatioChanged, nullable: true);
            Field(d => d.ModifiedBy, type: typeof(LoginGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.ModifiedBy?.Id == null)
                        return null;

                    IEnumerable<string> allowedTargetIds = await permissionValidator.GetTargetIdsFromClaim(context, "readLogins", new List<string> { context.Source.ModifiedBy.Id });
                    if (!GraphQLToolsExtensions.IsAllowed(allowedTargetIds, context.Source.ModifiedBy.Id))
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var loginLoader = accessor.Context.GetOrAddBatchLoader<string, Login>("GetLogins",
                    i => authService.GetLoginsDictionaryAsync(tenantId, i));

                    Login loginDao = await loginLoader.LoadAsync(context.Source.ModifiedBy.Id);

                    return LoginGraph.ToGraph(loginDao);
                });
        }
    }

    public class PriceLogicInputGraphType : InputObjectGraphType<CreatePriceLogicCommand>
    {
        public PriceLogicInputGraphType()
        {
            Name = "priceLogicInput";
            Description = "The loading input";

            Field(p => p.ProductId, type: typeof(ProductIdInputGraphType));
            Field(p => p.AmountLogic, nullable: true);
            Field(p => p.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(p => p.Options, type: typeof(ListGraphType<PricingOptionInputGraphType>));
        }
    }

    public class MigratePricingsInputGraph
    {
        public IEnumerable<PriceLogicInputGraphType> PriceLogicInputs { get; set; }
    }

    public class MigratePricingsInputGraphType : InputObjectGraphType<MigratePricingsInputGraph>
    {
        public MigratePricingsInputGraphType()
        {
            Name = "migratePricingsInput";

            Field(p => p.PriceLogicInputs, type: typeof(ListGraphType<PriceLogicInputGraphType>));
        }
    }
}
