using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Common;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;
using FormatWith;
using GraphQL;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Object = CoverGo.Users.Domain.Objects.Object;

namespace CoverGo.Gateway.Interfaces
{
    public static class Tools
    {
        public static T ToObjectOrDefault<T>(this object obj)
        {
            try
            {
                if (obj == null)
                {
                    return default;
                }

                var jtoken = JToken.FromObject(obj);
                var jtokenObject = jtoken.ToObject<T>();
                return jtokenObject;
            }
            catch
            {
                return default;
            };
        }

        public static T ToObjectOrDefault<T>(this object obj, JsonSerializer jsonSerializer)
        {
            try
            {
                return obj != null ? JToken.FromObject(obj, jsonSerializer).ToObject<T>(jsonSerializer) : default;
            }
            catch
            {
                return default;
            };
        }

        public static IDictionary<string, T> ToDictionary<T>(this object source)
        {
            if (source == null)
                return null;

            var dictionary = new Dictionary<string, T>();
            foreach (PropertyDescriptor property in TypeDescriptor.GetProperties(source))
                AddPropertyToDictionary(property, source, dictionary);

            return dictionary;
        }

        private static void AddPropertyToDictionary<T>(PropertyDescriptor property, object source, Dictionary<string, T> dictionary)
        {
            object value = property.GetValue(source);
            if (value is T)
                dictionary.Add(property.Name, (T)value);
        }

        //ToDo: rename
        public static string GetLoginIdFromToken(this ClaimsPrincipal claimsPricinpal, bool isRequired = true)
        {
            string loginId = claimsPricinpal?.Claims?.FirstOrDefault(c => c.Type == "sub")?.Value;
            if (isRequired && loginId == null)
                throw new ExecutionError($"No login detected."); //ToDo: changes

            return loginId;
        }

        public static string BambiFormat(this object value, string translation, string currencyName = null) => translation?
            .FormatWith(
                (parameter) =>
                {
                    if (value == null)
                        return new ReplacementResult(false, null);

                    int splitIndex = parameter.LastIndexOf('|');
                    string nakedParameter = splitIndex == -1 ? parameter : parameter.Substring(0, splitIndex);
                    ReplacementResult replacementResult = GetResult(nakedParameter, value);
                    if (splitIndex < 0)
                        return replacementResult;
                    else
                    {
                        string modifier = parameter.Length > splitIndex + 1 ? parameter.Substring(splitIndex + 1) : string.Empty;
                        try
                        {
                            switch (modifier)
                            {
                                case "C0":
                                case "C1":
                                case "C2":
                                    return new ReplacementResult(true,
                                        currencyName + decimal.Parse(replacementResult.Value.ToString()).ToString($"n{modifier.Last()}"));
                                case "D0":
                                case "D1":
                                case "D2":
                                {
                                    decimal decimalValue = decimal.Parse(replacementResult.Value.ToString());
                                    bool isRelativeNumber = decimalValue % 1 == 0;
                                    return new ReplacementResult(true,
                                        currencyName + (isRelativeNumber ? decimalValue.ToString("n0") : decimalValue.ToString($"n{modifier.Last()}")));
                                }
                                case "percent": return new ReplacementResult(true, (decimal.Parse(replacementResult.Value.ToString()) * 100).ToString("0.##") + "%");
                                default:
                                    return new ReplacementResult(false, value.ToDictionary<object>());
                            }
                        }
                        catch
                        {
                            return new ReplacementResult(false, value.ToDictionary<object>());
                        }
                    }
                },
                MissingKeyBehaviour.ReplaceWithFallback,
                value);


        private static ReplacementResult GetResult(string parameter, object value)
        {
            if (value == null)
                return new ReplacementResult(false, null);

            if (parameter == "this")
                return new ReplacementResult(true, value);

            if (parameter.StartsWith("["))
            {
                string indexString = string.Concat(parameter.Skip(1).TakeWhile(c => c != ']'));
                int index = int.Parse(indexString);

                string[] nakedParameterSplits = parameter.Split('.');
                if (nakedParameterSplits.Count() > 1)
                {
                    parameter = string.Join('.', nakedParameterSplits.Skip(1));

                    value = ((value as IEnumerable<object>) ?? Enumerable.Empty<object>()).ElementAt(index);
                    return GetResult(parameter, value);
                }
            }

            if (parameter.Contains("."))
            {
                string[] nakedParameterSplits = parameter.Split('.');
                if (nakedParameterSplits.Count() > 1)
                    parameter = string.Join('.', nakedParameterSplits.Skip(1));

                value.ToDictionary<object>().TryGetValue(nakedParameterSplits[0], out object newValue);
                return GetResult(parameter, newValue);
            }

            return new ReplacementResult(
                value.ToDictionary<object>().TryGetValue(parameter, out object valueToBeFormatted),
                valueToBeFormatted);
        }

        public static T MergeObjects<T>(this T obj1, T obj2)
        {
            object objResult = Activator.CreateInstance(typeof(T));

            IEnumerable<PropertyInfo> allProperties = typeof(T).GetProperties().Where(x => x.CanRead && x.CanWrite);
            foreach (PropertyInfo pi in allProperties)
            {
                object defaultValue = pi.PropertyType.IsValueType ? Activator.CreateInstance(pi.PropertyType) : null;
                object value = pi.GetValue(obj2, null);

                if (value != defaultValue)
                    pi.SetValue(objResult, value, null);
                else
                {
                    value = pi.GetValue(obj1, null);
                    if (value != defaultValue)
                        pi.SetValue(objResult, value, null);
                }
            }

            return (T)objResult;
        }

        public static int CalculateAge(DateTime birthDate, DateTime now)
        {
            int age = now.Year - birthDate.Year;

            if (now.Month < birthDate.Month || now.Month == birthDate.Month && now.Day < birthDate.Day)
                age--;

            return age;
        }

        public static async Task<Entity> GetEntityAsync(
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<Object, CreateObjectCommand, UpdateObjectCommand> objectService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            string tenantId,
            string entityId)
        {
            IEnumerable<Entity> entities = await GetEntitiesAsync(individualService, internalService, companyService, objectService, organizationService, tenantId, new EntityWhere { Id = entityId });

            return entities?.FirstOrDefault();
        }


        public static async Task<IEnumerable<Entity>> GetEntitiesAsync(
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<Object, CreateObjectCommand, UpdateObjectCommand> objectService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            string tenantId,
            EntityWhere entitySearchFilter)
        {
            Task<IEnumerable<Individual>> individualTask = individualService.GetAsync(tenantId, entitySearchFilter);
            Task<IEnumerable<Internal>> internalTask = internalService.GetAsync(tenantId, entitySearchFilter);
            Task<IEnumerable<Company>> companyTask = companyService.GetAsync(tenantId, entitySearchFilter);
            Task<IEnumerable<Object>> objectTask = objectService.GetAsync(tenantId, entitySearchFilter);
            Task<IEnumerable<Organization>> organizationTask = organizationService.GetAsync(tenantId, entitySearchFilter);

            await Task.WhenAll(individualTask, internalTask, companyTask);

            IEnumerable<Entity> entities = Enumerable.Empty<Entity>();

            if (individualTask.Result.Any()) entities = entities.Concat(individualTask.Result);
            if (internalTask.Result.Any()) entities = entities.Concat(internalTask.Result);
            if (companyTask.Result.Any()) entities = entities.Concat(companyTask.Result);
            if (objectTask.Result.Any()) entities = entities.Concat(objectTask.Result);
            if (organizationTask.Result.Any()) entities = entities.Concat(organizationTask.Result);

            return entities;
        }

        public static string GetEntityType<T>(this T entity) where T : Entity
        {
            switch (entity)
            {
                case Individual _: return "individual";
                case Company _: return "company";
                case Internal _: return "internal";
                case Object _: return "object";
                case Organization _: return "organization";
                default: throw new Exception("Entity type not supported");
            }
        }

        public static Result<JToken> TryParseJsonStringToJToken(string json)
        {
            try
            {
                var token = JToken.Parse(json);
                return new Result<JToken> { Status = "success", Value = token };
            }
            catch (Exception e)
            {
                return new Result<JToken>
                {
                    Status = "failure",
                    Errors = new List<string> { e.Message }
                };
            }
        }

        public static Result<JObject> TryParseJsonStringToJObject(string json)
        {
            try
            {
                var token = JObject.Parse(json);
                return new Result<JObject> { Status = "success", Value = token };
            }
            catch (Exception e)
            {
                return new Result<JObject>
                {
                    Status = "failure",
                    Errors = new List<string> { e.Message }
                };
            }
        }

        public static void PopulateRenderParameters(GraphQLUserContext userContext, RenderParameters renderParameters, out List<string> errors)
        {
            errors = new List<string>();
            if (renderParameters != null)
            {
                if (userContext.Headers.ContainsKey("Authorization"))
                {
                    renderParameters.AccessToken = userContext.Headers["Authorization"].ToString().Split("Bearer ")[1];
                }
            }

            if (renderParameters?.ContentJsonString != null)
            {
                try
                {
                    renderParameters.Content = JObject.Parse(renderParameters?.ContentJsonString);
                }
                catch
                {
                    errors.Add("Cannot parse contentJsonString");
                }
            }

            if (renderParameters?.VariablesJsonString != null)
            {
                try
                {
                    renderParameters.Variables = JObject.Parse(renderParameters?.VariablesJsonString);
                }
                catch
                {
                    errors.Add("Cannot parse variableJsonString");
                }
            }

            var validateUrlErrors = ValidateUrls(
                renderParameters?.Content?.GetValue("link")?.ToString(),
                renderParameters?.Content?.GetValue("rootUrl")?.ToString()
            );
            if (validateUrlErrors != null && validateUrlErrors.Any())
                errors.AddRange(validateUrlErrors);
        }

        public static List<string> ValidateUrls(params string[] urls)
        {
            var errors = new List<string>();
            foreach (var url in urls)
            {
                if (string.IsNullOrEmpty(url) || UrlValidator.AllowedOrigins.Any(whitelist => IsValidUrl(whitelist, url)))
                    continue;

                errors.Add($"Unauthorized url {url}");
            }

            return errors;
        }

        private static bool IsValidUrl(string pattern, string url)
            => WildCardStartsWith(pattern, url) && MatchesDomain(pattern, url);

        private static bool MatchesDomain(string pattern, string url)
        {
            if (!Uri.TryCreate(url, UriKind.Absolute, out Uri uri))
                return false;

            string hostWithPort = uri.IsDefaultPort ? uri.Host : $"{uri.Host}:{uri.Port}";
            string allowedDomain = pattern.Replace("http://", "").Replace("https://", "").Replace("*.", "");

            return hostWithPort.EndsWith(allowedDomain, StringComparison.OrdinalIgnoreCase);
        }

        private static bool WildCardStartsWith(string pattern, string text)
            => System.IO.Enumeration.FileSystemName.MatchesSimpleExpression($"{pattern}*", text);

        public static string GetAccessToken(GraphQLUserContext userContext) =>
            userContext.Headers.First(h => h.Key == "Authorization").Value.ToString().Split("Bearer ")[1];

        public static bool ValidateExtension(string extension) =>
            GetWhitelistedExtensions().Any(wle => wle.Key == extension);

        public static bool DoesExtensionMatchContentType(string extension, string contentType) =>
            GetWhitelistedExtensions().FirstOrDefault(e => e.Key == extension).Value == contentType;

        private static Dictionary<string, string> GetWhitelistedExtensions() =>
            Environment.GetEnvironmentVariable("datacenterId") switch
            {
                "dbs-hk" => _dbsWhitelistedExtensions,
                _ => _whitelistedExtensions
            };


        public static string GetContentType(string path)
        {
            string ext = Path.GetExtension(path).ToLowerInvariant();
            return _whitelistedExtensions[ext];
        }

        private static readonly Dictionary<string, string> _whitelistedExtensions = new()
        {
                {".txt", "text/plain"},
                {".jpg", "image/jpeg"},
                {".jpeg", "image/jpeg"},
                {".gif", "image/gif"},
                {".png", "image/png"},
                {".svg", "image/svg+xml" },
                {".bmp", "image/bmp" },
                {".tif", "image/tiff" },
                {".tiff", "image/tiff" },
                {".heif", "image/heif" },
                {".heic", "image/heic" },
                {".pdf", "application/pdf"},
                {".doc", "application/msword"},
                {".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"},
                {".xls", "application/vnd.ms-excel"},
                {".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},
                {".csv", "text/csv"},
                {".ppt", "application/vnd.ms-powerpoint" },
                {".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation" },
                {".htm", "text/html" },
                {".html", "text/html" },
                { ".avi", "video/x-msvideo" },
                { ".weba", "audio/webm" },
                { ".webm", "video/webm" },
                { ".mpeg", "video/mpeg" },
                { ".mp4", "video/mp4" },
                { ".mov", "video/quicktime" },
                { ".wmv", "video/x-ms-wmv" },
                { ".flv", "video/x-flv" },
                {".js", "application/javascript"},
                {".msg", "application/vnd.ms-outlook"},
                {".eml", "message/rfc822"},
                {".zip", "application/zip"},
                {".css", "text/css"},
                {".ttf", "application/x-font-ttf"},
                {".ttc", "application/x-font-ttf"},
                {".otf", "font/opentype"},
                {".woff", "application/font-woff"},
                {".woff2 ", "application/font-woff2"},
                {".cgx", "application/octet-stream"}
            };

        private static readonly Dictionary<string, string> _dbsWhitelistedExtensions = new()
        {
            {".pdf", "application/pdf"},
            {".doc", "application/msword"},
            {".docx", "application/vnd.ms-word"}
        };
    }
}
