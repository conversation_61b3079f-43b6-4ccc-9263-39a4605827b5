using System;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Infrastructure.InsuredNomadsServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Interfaces;

[ApiExplorerSettings(IgnoreApi = true)]
[Authorize(AuthenticationSchemes = "Bearer")]
[Route("expaTpaWebhook/{tenantId}")]
public class ExpaTpaWebhookController : ControllerBase
{
    private readonly ILogger<ExpaTpaWebhookController> _logger;
    private readonly InsuredNomadsIntegrationService _insuredNomadsIntegrationService;

    public ExpaTpaWebhookController(ILogger<ExpaTpaWebhookController> logger, InsuredNomadsIntegrationService insuredNomadsIntegrationService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _insuredNomadsIntegrationService = insuredNomadsIntegrationService ?? throw new ArgumentNullException(nameof(insuredNomadsIntegrationService));
    }

    [HttpPost("members/changeStatus")]
    [Consumes("application/json")]
    public async Task<IActionResult> ChangeMemberStatus(string tenantId, [FromBody] MemberStatusChangedEvent @event)
    {
        try
        {
            if (!(tenantId?.ToLower().StartsWith("insurednomads") ?? false)) return BadRequest("Invalid tenant");

            _logger.LogInformation("Handling ExpaTPA webhook event for tenant {tenantId} - {event}", tenantId, JsonConvert.SerializeObject(@event));

            Result<string> result = await _insuredNomadsIntegrationService.IssuePolicyForActiveMember(tenantId, JObject.FromObject(@event));

            return !result.IsSuccess ? BadRequest(result) : NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error while handling ExpaTPA webhook event for tenant {tenantId}", tenantId);
            throw;
        }
    }

    public class MemberStatusChangedEvent
    {
        public string MainInsuredId { get; set; }
        public string Status { get; set; }

        [JsonProperty("Beneficiaries")]
        public BeneficiaryUpdate[] beneficiaryUpdates { get; set; }
    }

    public class BeneficiaryUpdate
    {
        public string BeneficiaryId { get; set; }
        public string Status { get; set; }
    }
}