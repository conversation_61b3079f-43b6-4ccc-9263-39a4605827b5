using System;
using System.Linq;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Interfaces.Products;
using GraphQL.Authorization;
using GraphQL.Types;
using GraphQL.Validation;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeBenefitDefinitionsMutations(IProductService productService, PermissionValidator permissionValidator)
        {
            Field<CreatedStatusResultGraphType>()
                .Name("createBenefitDefinition")
                .Argument<NonNullGraphType<CreateBenefitDefinitionInputGraphType>>("input", "Create a benefitDefinition")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("createBenefitDefinitions", "writeBenefitDefinitions").WithTargetIds("all"));
                    string tenantId = context.GetTenantIdFromToken();
                    CreateBenefitDefinitionInput input = context.GetArgument<CreateBenefitDefinitionInput>("input");
                    string loginId = context.GetLoginIdFromToken();
                    var command = new CreateBenefitDefinitionCommand
                    {
                        Name = input.Name,
                        Description = input.Description,
                        BusinessId = input.BusinessId,
                        Status = input.Status,
                        BenefitDefinitionTypeIds = input.BenefitDefinitionTypeIds,
                        Fields = input.Fields,
                        CreatedById = loginId
                    };

                    return await productService.CreateBenefitDefinitionAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("updateBenefitDefinition")
                .AuthorizeWith("any")
                .Description("Modifies a benefitdefinition")
                .Argument<NonNullGraphType<UpdateBenefitDefinitionInputGraphType>>("input", "the modified benefitDefinition")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updateBenefitDefinitions", "writeBenefitDefinitions").WithTargetIds("all"));
                    string tenantId = context.GetTenantIdFromToken();
                    UpdateBenefitDefinitionInput input = context.GetArgument<UpdateBenefitDefinitionInput>("input");

                    return await productService.UpdateBenefitDefinitionAsync(tenantId, new UpdateBenefitDefinitionCommand
                    {
                        BenefitDefinitionId = input.BenefitDefinitionId,
                        Name = input.Name,
                        Description = input.Description,
                        Status = input.Status,
                        BusinessId = input.BusinessId,
                        BenefitDefinitionTypeIds = input.BenefitDefinitionTypeIds,
                        Fields = input.Fields,
                        ModifiedById = context.GetLoginIdFromToken(),
                    });
                });

            Field<ResultGraphType>()
                .Name("deleteBenefitDefinition")
                .Argument<NonNullGraphType<DeleteBenefitDefinitionInputGraphType>>("input", "Delete a benefitDefinition")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("deleteBenefitDefinitions", "writeBenefitDefinitions").WithTargetIds("all"));
                    string tenantId = context.GetTenantIdFromToken();
                    DeleteBenefitDefinitionInput input = context.GetArgument<DeleteBenefitDefinitionInput>("input");

                    return await productService.DeleteBenefitDefinitionAsync(tenantId, input.BenefitDefinitionId, new DeleteCommand { DeletedById = context.GetLoginIdFromToken() });
                });
            
            Field<ResultGraphType>()
                .Name("batchBenefitDefinition")
                .Argument<NonNullGraphType<BatchBenefitDefinitionInputGraphType>>("input", "batch benefitDefinition input")
                .ResolveAsync(async context =>
                {
                    BatchBenefitDefinitionInput input = context.GetArgument<BatchBenefitDefinitionInput>("input");

                    if (!(input.CreateBenefitDefinitionInputs?.Any() ?? false) 
                        && !(input.UpdateBenefitDefinitionInputs?.Any() ?? false))
                    {
                        return Result.Success();
                    }

                    if (!await permissionValidator.HasTarget(context, "writeBenefitDefinitions", "all"))
                    {
                        if (input.CreateBenefitDefinitionInputs?.Any() == true)
                            await permissionValidator.Authorize(context, "createBenefitDefinitions", "all");
                        if (input.UpdateBenefitDefinitionInputs?.Any() == true)
                            await permissionValidator.Authorize(context, "updateBenefitDefinitions", "all");
                    }

                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    
                    var command = new BatchBenefitDefinitionCommand
                    {
                        CreateBenefitDefinitionCommands =  input.CreateBenefitDefinitionInputs.Select(createInput =>  new CreateBenefitDefinitionCommand
                        {
                            Name = createInput.Name,
                            Description = createInput.Description,
                            BusinessId = createInput.BusinessId,
                            Status = createInput.Status,
                            BenefitDefinitionTypeIds = createInput.BenefitDefinitionTypeIds,
                            Fields = createInput.Fields,
                            CreatedById = loginId
                        }).ToList(),
                        UpdateBenefitDefinitionCommands =  input.UpdateBenefitDefinitionInputs.Select(updateInput =>  new UpdateBenefitDefinitionCommand()
                        {
                            BenefitDefinitionId = updateInput.BenefitDefinitionId,
                            Name = updateInput.Name,
                            Description = updateInput.Description,
                            Status = updateInput.Status,
                            BusinessId = updateInput.BusinessId,
                            BenefitDefinitionTypeIds = updateInput.BenefitDefinitionTypeIds,
                            Fields = updateInput.Fields,
                            ModifiedById = loginId
                        }).ToList()
                    };

                   
                    return await productService.BatchBenefitDefinitionAsync(tenantId, command);
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createBenefitDefinitionType")
                .Argument<NonNullGraphType<CreateBenefitDefinitionTypeInputGraphType>>("input", "Create a benefitDefinitionType")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("createBenefitDefinitionTypes", "writeBenefitDefinitionTypes").WithTargetIds("all"));
                    string tenantId = context.GetTenantIdFromToken();
                    CreateBenefitDefinitionTypeInput input = context.GetArgument<CreateBenefitDefinitionTypeInput>("input");
                    string loginId = context.GetLoginIdFromToken();
                    var command = new CreateBenefitDefinitionTypeCommand
                    {
                        Name = input.Name,
                        Description = input.Description,
                        BusinessId = input.BusinessId,
                        Status = input.Status,
                        Fields = input.Fields,
                        CreatedById = loginId
                    };

                    return await productService.CreateBenefitDefinitionTypeAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("updateBenefitDefinitionType")
                .AuthorizeWith("any")
                .Description("Modifies a benefitdefinitionType")
                .Argument<NonNullGraphType<UpdateBenefitDefinitionTypeInputGraphType>>("input", "the modified benefitDefinitionType")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updateBenefitDefinitionTypes", "writeBenefitDefinitionTypes").WithTargetIds("all"));
                    string tenantId = context.GetTenantIdFromToken();
                    UpdateBenefitDefinitionTypeInput input = context.GetArgument<UpdateBenefitDefinitionTypeInput>("input");

                    return await productService.UpdateBenefitDefinitionTypeAsync(tenantId, new UpdateBenefitDefinitionTypeCommand
                    {
                        BenefitDefinitionTypeId = input.BenefitDefinitionTypeId,
                        Name = input.Name,
                        Description = input.Description,
                        BusinessId = input.BusinessId,
                        Status = input.Status,
                        Fields = input.Fields,
                        ModifiedById = context.GetLoginIdFromToken(),
                    });
                });

            Field<ResultGraphType>()
                .Name("deleteBenefitDefinitionType")
                .Argument<NonNullGraphType<DeleteBenefitDefinitionTypeInputGraphType>>("input", "Delete a benefitDefinitionType")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("deleteBenefitDefinitionTypes", "writeBenefitDefinitionTypes").WithTargetIds("all"));
                    string tenantId = context.GetTenantIdFromToken();
                    DeleteBenefitDefinitionTypeInput input = context.GetArgument<DeleteBenefitDefinitionTypeInput>("input");

                    return await productService.DeleteBenefitDefinitionTypeAsync(tenantId, input.BenefitDefinitionTypeId, new DeleteCommand { DeletedById = context.GetLoginIdFromToken() });
                });
            
            
            Field<ResultGraphType>()
                .Name("batchBenefitDefinitionType")
                .Argument<NonNullGraphType<BatchBenefitDefinitionTypeInputGraphType>>("input", "batch benefitDefinitionType input")
                .ResolveAsync(async context =>
                {
                    BatchBenefitDefinitionTypeInput input = context.GetArgument<BatchBenefitDefinitionTypeInput>("input");

                    if (!(input.CreateBenefitDefinitionTypeInputs?.Any() ?? false) 
                        && !(input.UpdateBenefitDefinitionTypeInputs?.Any() ?? false))
                    {
                        return Result.Success();
                    }
                    
                    if (!await permissionValidator.HasTarget(context, "writeBenefitDefinitionTypes", "all"))
                    {
                        if (input.CreateBenefitDefinitionTypeInputs?.Any() == true)
                            await permissionValidator.Authorize(context, "createBenefitDefinitionTypes", "all");
                        if (input.UpdateBenefitDefinitionTypeInputs?.Any() == true)
                            await permissionValidator.Authorize(context, "updateBenefitDefinitionTypes", "all");
                    }
                    
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    
                    var command = new BatchBenefitDefinitionTypeCommand
                    {
                        CreateBenefitDefinitionTypeCommands =  input.CreateBenefitDefinitionTypeInputs.Select(createInput =>  new CreateBenefitDefinitionTypeCommand
                        {
                            Name = createInput.Name,
                            Description = createInput.Description,
                            BusinessId = createInput.BusinessId,
                            Status = createInput.Status,
                            Fields = createInput.Fields,
                            CreatedById = loginId
                        }).ToList(),
                        UpdateBenefitDefinitionTypeCommands =  input.UpdateBenefitDefinitionTypeInputs.Select(updateInput =>  new UpdateBenefitDefinitionTypeCommand()
                        {
                            BenefitDefinitionTypeId = updateInput.BenefitDefinitionTypeId,
                            Name = updateInput.Name,
                            Description = updateInput.Description,
                            BusinessId = updateInput.BusinessId,
                            Status = updateInput.Status,
                            Fields = updateInput.Fields,
                            ModifiedById = loginId
                        }).ToList()
                    };
                   
                    return await productService.BatchBenefitDefinitionTypeAsync(tenantId, command);
                });
        }
    }
}