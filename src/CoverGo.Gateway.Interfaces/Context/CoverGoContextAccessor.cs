using System;
using System.Threading.Tasks;
using CoverGo.Gateway.Common;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Context;
using Microsoft.AspNetCore.Http;

namespace CoverGo.Gateway.Interfaces.Context
{
    public class CoverGoContextAccessor : ICoverGoContextAccessor
    {
        private const string tenantIdClaim = "tenantId";
        private const string appIdClaim = "appId";
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IAuthService _authService;

        public CoverGoContextAccessor(IHttpContextAccessor httpContextAccessor, IAuthService authService)
        {
            _authService = authService;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<CoverGoContext> CoverGoContextGetAsync()
        {
            HttpContext context = _httpContextAccessor.HttpContext;
            string tenantId = context.GetClaim(tenantIdClaim);
            string appId = context.GetClaim(appIdClaim);

            if (string.IsNullOrEmpty(tenantId))
            {
                throw new Exception("Unable to get tenant Id");
            }

            if (string.IsNullOrEmpty(appId))
            {
                throw new Exception("Unable to get app Id");
            }

            var tenantApps = await _authService.GetAppsDictionaryAsync(tenantId, new AppWhere());

            return new CoverGoContext
            {
                TenantId = tenantId,
                App = tenantApps[appId]
            };
        }
    }
}