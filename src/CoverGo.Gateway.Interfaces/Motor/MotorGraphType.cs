﻿using CoverGo.Gateway.Domain.Products.Motor;

using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Motor
{
    public class MotorMakeGraphType : ObjectGraphType<MotorMake>
    {
        public MotorMakeGraphType(IMotorVenue motorVenue)
        {
            Name = "motorMake";
            Field<StringGraphType>("id");
            Field<StringGraphType>("name");

            FieldAsync<ListGraphType<MotorModelGraphType>>(
               "models",
               arguments: new QueryArguments
               {
                    new QueryArgument<StringGraphType> { Name = "id", Description = "The id of model" }
               },
               resolve: async context =>
               {
                   string id = context.GetArgument<string>("id");
                   return id == null
                       ? await motorVenue.GetModelsAsync(context.Source.Id)
                       : await motorVenue.GetModelsAsync(context.Source.Id, new [] { id });
               });

            FieldAsync<ListGraphType<MotorGenerationGraphType>>(
                "generations",
                arguments: new QueryArguments
                {
                    new QueryArgument<StringGraphType> { Name = "modelsId", Description = "The id of model" },
                    new QueryArgument<StringGraphType> { Name = "id", Description = "The id of generation" }
                },
                resolve: async context =>
                {
                    string modelId = context.GetArgument<string>("modelId");
                    string id = context.GetArgument<string>("id");
                    return modelId != null
                        ? id != null
                            ? await motorVenue.GetGenerationsAsync(context.Source.Id, modelId, new [] { id })
                            : await motorVenue.GetGenerationsAsync(context.Source.Id, modelId)
                        : await motorVenue.GetGenerationsAsync(context.Source.Id);
                });

            FieldAsync<ListGraphType<MotorTrimGraphType>>(
                "trims",
                arguments: new QueryArguments
                {
                    new QueryArgument<StringGraphType> { Name = "modelsId", Description = "The id of model" },
                    new QueryArgument<StringGraphType> { Name= "generationId", Description = "The id of generation" },
                    new QueryArgument<StringGraphType> { Name = "id", Description = "The id of generation" }
                },
                resolve: async context =>
                {
                    string modelId = context.GetArgument<string>("modelId");
                    string generationId = context.GetArgument<string>("generationId");
                    string id = context.GetArgument<string>("id");
                    return modelId != null
                        ? generationId != null
                            ? (id != null)
                                ? await motorVenue.GetTrimsAsync(context.Source.Id, modelId, generationId, new [] { id })
                                : await motorVenue.GetTrimsAsync(context.Source.Id, modelId, generationId)
                            : await motorVenue.GetTrimsAsync(context.Source.Id, modelId)
                        : await motorVenue.GetTrimsAsync(context.Source.Id);
                });
        }
    }

    public class MotorModelGraphType : ObjectGraphType<MotorModel>
    {
        public MotorModelGraphType(IMotorVenue motorVenue)
        {
            Name = "motorModel";
            Field<StringGraphType>("id");
            Field<StringGraphType>("name");

            FieldAsync<ListGraphType<MotorGenerationGraphType>>(
                "generations",
                arguments: new QueryArguments
                {
                    new QueryArgument<StringGraphType> { Name = "id", Description = "The id of generation" }
                },
                resolve: async context =>
                {
                    string id = context.GetArgument<string>("id");
                    return id == null
                        ? await motorVenue.GetGenerationsAsync(context.Source.MakeId, context.Source.Id)
                        : await motorVenue.GetGenerationsAsync(context.Source.MakeId, context.Source.Id, new [] { id });
                });

            FieldAsync<ListGraphType<MotorTrimGraphType>>(
                "trims",
                arguments: new QueryArguments
                {
                    new QueryArgument<StringGraphType> { Name= "generationId", Description = "The id of generation" },
                    new QueryArgument<StringGraphType> { Name = "id", Description = "The id of generation" }
                },
                resolve: async context =>
                {
                    string generationId = context.GetArgument<string>("generationId");
                    string id = context.GetArgument<string>("id");
                    return generationId != null
                        ? (id != null)
                            ? await motorVenue.GetTrimsAsync(context.Source.MakeId, context.Source.Id, generationId, new [] { id })
                            : await motorVenue.GetTrimsAsync(context.Source.MakeId, context.Source.Id, generationId)
                        : await motorVenue.GetTrimsAsync(context.Source.MakeId, context.Source.Id);
                });
        }
    }

    public class MotorGenerationGraphType : ObjectGraphType<MotorGeneration>
    {
        public MotorGenerationGraphType(IMotorVenue motorVenue)
        {
            Name = "motorGeneration";
            Field<StringGraphType>("id");
            Field<StringGraphType>("name");

            FieldAsync<ListGraphType<MotorTrimGraphType>>(
                "trims",
                arguments: new QueryArguments
                {
                    new QueryArgument<StringGraphType> { Name = "id", Description = "The id of trim" }
                },
                resolve: async context =>
                {
                    string id = context.GetArgument<string>("id");

                    return id == null
                        ? await motorVenue.GetTrimsAsync(context.Source.MakeId, context.Source.ModelId, context.Source.Id)
                        : await motorVenue.GetTrimsAsync(context.Source.MakeId, context.Source.ModelId, context.Source.Id, new [] { id });
                });
        }
    }

    public class MotorTrimGraphType : ObjectGraphType<MotorTrim>
    {
        public MotorTrimGraphType(IMotorVenue motorVenue)
        {
            Name = "motorTrim";
            Field<StringGraphType>("id");
            Field<StringGraphType>("name");

            FieldAsync<MotorDetailsGraphType>(
                "details",
                //arguments: new QueryArguments(new QueryArgument<NonNullGraphType<>>
                resolve: async context =>
            {
                MotorDetails details = await motorVenue.GetMotorDetails("", "", "", context.Source.Id);
                return details;
            });
        }
    }

    public class MotorDetailsGraphType : ObjectGraphType<MotorDetails>
    {
        public MotorDetailsGraphType()
        {
            Name = "motorDetails";
            Field<StringGraphType>("body");
            Field<IntGraphType>("engineCapacity");
            Field<IntGraphType>("horsePower");
            Field<StringGraphType>("engineType");
            Field<ListGraphType<IntGraphType>>("years");
            Field<StringGraphType>("drive");
            Field<StringGraphType>("gearbox");
            Field<StringGraphType>("imageUrl");
        }
    }
}
