﻿using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Motor
{
    public class MotorModelInputGraphType : InputObjectGraphType
    {
        public MotorModelInputGraphType()
        {
            Name = "motorModelInput";
            Field<StringGraphType>("makeId");
            Field<StringGraphType>("modelId");
            Field<StringGraphType>("generationId");
            Field<StringGraphType>("trimId");
            Field<IntGraphType>("year");
        }
    }
}
