﻿using CoverGo.Gateway.Domain.Scheduler;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Scheduler
{
    public class JobDetailGraphType : ObjectGraphType<JobDetail>
    {
        public JobDetailGraphType()
        {
            Name = "jobDetail";

            Field(f => f.Type, nullable: true);
            Field(f => f.JobId, nullable: true);
            Field(f => f.AsLoginId, nullable: true);
            Field(f => f.AsAppId, nullable: true);
            Field(f => f.GraphQlOperationType, nullable: true);
            Field(f => f.GraphQlQuery, nullable: true);
            Field(f => f.GraphQlVariables, nullable: true);
            Field(f => f.CustomJobVariables, nullable: true);
        }
    }
}