﻿using CoverGo.Gateway.Domain.Scheduler;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Scheduler
{
    public class JobDetailInputGraphType : InputObjectGraphType<JobDetail>
    {
        public JobDetailInputGraphType()
        {
            Name = "jobDetailInput";

            Field(f => f.Type, nullable: false);
            Field(f => f.JobId, nullable: false);
            Field(f => f.GraphQlOperationType, nullable: true);
            Field(f => f.GraphQlQuery, nullable: true);
            Field(f => f.GraphQlVariables, nullable: true);
            Field(f => f.CustomJobVariables, nullable: true);
        }
    }
}