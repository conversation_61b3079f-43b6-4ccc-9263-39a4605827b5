﻿using CoverGo.Gateway.Domain.Scheduler;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Scheduler
{
    public class JobScheduleWhereInputGraphType : InputObjectGraphType<JobScheduleWhere>
    {
        public JobScheduleWhereInputGraphType()
        {
            Name = "jobScheduleWhereInput";

            Field(f => f.And, type: typeof(ListGraphType<JobScheduleWhereInputGraphType>), nullable: true);
            Field(f => f.Or, type: typeof(ListGraphType<JobScheduleWhereInputGraphType>), nullable: true);
            Field(f => f.CreatedAt_gt, type: typeof(DateTimeGraphType), nullable: true);
            Field(f => f.CreatedAt_lt, type: typeof(DateTimeGraphType), nullable: true);
            Field(f => f.LastCheckedAt_gt, type: typeof(DateTimeGraphType), nullable: true);
            Field(f => f.LastCheckedAt_lt, type: typeof(DateTimeGraphType), nullable: true);
            Field(f => f.LastModifiedAt_gt, type: typeof(DateTimeGraphType), nullable: true);
            Field(f => f.LastModifiedAt_lt, type: typeof(DateTimeGraphType), nullable: true);
            Field(f => f.CreatedById, nullable: true);
            Field(f => f.CreatedById_contains, nullable: true);
            Field(f => f.CreatedById_in, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            Field(f => f.LastModifiedById, nullable: true);
            Field(f => f.LastModifiedById_contains, nullable: true);
            Field(f => f.LastModifiedById_in, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            Field(f => f.Name, nullable: true);
            Field(f => f.Id, nullable: true);
            Field(f => f.IsActive, nullable: true);
        }
    }
}