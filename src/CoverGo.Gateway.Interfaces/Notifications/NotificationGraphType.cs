﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Notifications;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Pdf;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Types;
using CoverGo.Gateway.Interfaces.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;
using GraphQL.DataLoader;
using GraphQL.Language.AST;
using GraphQL.Types;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Interfaces.Notifications
{
    public class NotificationsGraphType : ObjectGraphType<NotificationsGraph>
    {
        public NotificationsGraphType(
            INotificationService notificationService,
            PermissionValidator permissionValidator
        )
        {
            Name = "notifications";
            Description = "Paginated notifications";
            Field(c => c.TotalCount)
               .ResolveAsync(async context =>
               {
                   if (context.Source.IsResolved)
                   {
                       return context.Source.TotalCount;
                   }

                   string tenantId = context.GetTenantIdFromToken();

                   IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readNotifications");
                   NotificationWhere where = allowedIds.Contains("all")
                       ? context.Source.Where ?? new NotificationWhere()
                       : new NotificationWhere
                       {
                           And = new List<NotificationWhere>
                           {
                                context.Source.Where ?? new NotificationWhere(),
                                new() { Id_in = allowedIds.ToList() }
                           }
                       };

                   return await notificationService.GetTotalCountAsync(tenantId, where);
               });

            Field(c => c.List, type: typeof(ListGraphType<NotificationGraphType>))
                .ResolveAsync(async context =>
                {
                    if (context.Source.IsResolved)
                    {
                        return context.Source.List;
                    }

                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readNotifications");
                    NotificationWhere where = allowedIds.Contains("all")
                        ? context.Source.Where ?? new NotificationWhere()
                        : new NotificationWhere
                        {
                            And = new List<NotificationWhere>
                            {
                                context.Source.Where ?? new NotificationWhere(),
                                new() { Id_in = allowedIds.ToList() }
                            }
                        };

                    SortGraph sort = context.Source.Sort;
                    OrderBy orderBy = null;

                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = context.Source.Skip, First = context.Source.First, OrderBy = orderBy, AsOf = context.Source.AsOf };
                    IEnumerable<Notification> notifications = await notificationService.GetAsync(tenantId, queryArguments);
                    return notifications.Select(c => NotificationGraph.ToGraph(c));
                });
        }
    }

    public class NotificationGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public string FromEntityId { get; set; }
        public string ToEntityId { get; set; }
        public string ToTopicId { get; set; }
        public string PolicyId { get; set; }
        public string OfferId { get; set; }
        public PushMessageGraph PushMessage { get; set; }
        public EmailMessage EmailMessage { get; set; }
        public SmsMessage SmsMessage { get; set; }
        public InternalMessageGraph InternalMessage { get; set; }
        public ChatMessageGraph ChatMessage { get; set; }
        public string Status { get; set; }
        public DateTime Timestamp { get; set; }

        public static NotificationGraph ToGraph(Notification n) =>
            n == null
                ? null
                : new NotificationGraph
                {
                    FromEntityId = n.FromEntityId,
                    ToEntityId = n.ToEntityId,
                    ToTopicId = n.ToTopicId,
                    PolicyId = n.PolicyId,
                    OfferId = n.OfferId,
                    EmailMessage = n.EmailMessage,
                    SmsMessage = n.SmsMessage,
                    Id = n.Id,
                    PushMessage = PushMessageGraph.ToGraph(n.PushMessage),
                    InternalMessage = InternalMessageGraph.ToGraph(n.InternalMessage),
                    ChatMessage = ChatMessageGraph.ToGraph(n.ChatMessage),
                    Status = n.Status,
                    Timestamp = n.TimeStamp,
                    Type = n.Type
                }.PopulateSystemGraphFields(n);
    }

    public class NotificationGraphType : ObjectGraphType<NotificationGraph>
    {
        public NotificationGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "notification";
            Description = "A notification with multiple channels";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(n => n.Id);
            Field(n => n.Type, nullable: true);
            Field(n => n.EmailMessage, type: typeof(EmailMessageGraphType), nullable: true);
            Field(n => n.SmsMessage, type: typeof(SmsMessageGraphType), nullable: true);
            Field(n => n.PushMessage, type: typeof(PushMessageGraphType), nullable: true);
            Field(n => n.InternalMessage, type: typeof(InternalMessageGraphType), nullable: true);
            Field(n => n.ChatMessage, type: typeof(ChatMessageGraphType), nullable: true);
            Field(n => n.Status, nullable: true);
            Field(n => n.Timestamp, type: typeof(DateTimeGraphType), nullable: true);
            Field(n => n.FromEntityId, nullable: true);
        }
    }

    public class NotificationWhereInputGraphType : InputObjectGraphType<NotificationWhere>
    {
        public NotificationWhereInputGraphType()
        {
            Name = "notificationWhere";
            Field(n => n.And, type: typeof(ListGraphType<NotificationWhereInputGraphType>), nullable: true);
            Field(n => n.Or, type: typeof(ListGraphType<NotificationWhereInputGraphType>), nullable: true);

            Field(n => n.Id, nullable: true);
            Field(n => n.Id_in, nullable: true);
            Field(n => n.FromEntityId, nullable: true);
            Field(n => n.FromEntityId_in, nullable: true);
            Field(n => n.FromEntityId_not, nullable: true);
            Field(n => n.ToEntityId, nullable: true);
            Field(n => n.ToEntityId_in, nullable: true);
            Field(n => n.ToTopic, nullable: true);
            Field(n => n.ToTopic_in, nullable: true);
            Field(n => n.ToTopicId, nullable: true);
            Field(n => n.ToTopicId_in, nullable: true);
            Field(n => n.PolicyId, nullable: true);
            Field(n => n.PolicyId_in, nullable: true);
            Field(n => n.OfferId, nullable: true);
            Field(n => n.OfferId_in, nullable: true);
            Field(n => n.Type, nullable: true);
            Field(n => n.Type_in, nullable: true);
            Field(n => n.Status, nullable: true);
            Field(n => n.Status_not, nullable: true);
            Field(n => n.Status_in, nullable: true);

            this.PopulateSystemWhereFields();
        }
    }

    public class SendNotificationInputGraphType : InputObjectGraphType<SendNotificationGraph>
    {
        public SendNotificationInputGraphType()
        {
            Name = "sendNotificationInput";
            Description = "The input to send notifications";

            Field(n => n.FromEntityId, nullable: true);
            Field(n => n.ToEntityId, nullable: true);
            Field(n => n.ToEntityIds, nullable: true);
            Field(n => n.ToTopic, nullable: true);
            Field(n => n.ToTopicId, nullable: true);
            Field(n => n.PolicyId, nullable: true);
            Field(n => n.OfferId, nullable: true);
            Field(n => n.Type, nullable: true);
            Field(n => n.UseConfig, nullable: true);
            Field(n => n.EmailMessage, type: typeof(EmailMessageInputGraphType), nullable: true);
            Field(n => n.SmsMessage, type: typeof(SmsMessageInputGraphType), nullable: true);
            Field(n => n.PushMessage, type: typeof(PushMessageInputGraphType), nullable: true);
            Field(n => n.InternalMessage, type: typeof(InternalMessageInputGraphType), nullable: true);
            Field(n => n.ChatMessage, type: typeof(ChatMessageInputGraphType), nullable: true);
            Field(n => n.ScheduleToSendAt, type: typeof(DateGraphType), nullable: true);
        }
    }

    public class SendIssueDebitNoteNotificationInputGraphType : InputObjectGraphType<SendNotificationGraph>
    {
        public SendIssueDebitNoteNotificationInputGraphType()
        {
            Name = "sendIssueDebitNoteNotificationInput";
            Description = "The input to send notifications";

            Field(n => n.FromEntityId, nullable: true);
            Field(n => n.ToEntityId, nullable: true);
            Field(n => n.ToEntityIds, nullable: true);
            Field(n => n.ToTopic, nullable: true);
            Field(n => n.ToTopicId, nullable: true);
            Field(n => n.PolicyId, nullable: true);
            Field(n => n.OfferId, nullable: true);
            Field(n => n.Type, nullable: true);
            Field(n => n.UseConfig, nullable: true);
            Field(n => n.EmailMessage, type: typeof(IssueDebitNoteEmailMessageInputGraphType), nullable: true);
            Field(n => n.SmsMessage, type: typeof(SmsMessageInputGraphType), nullable: true);
            Field(n => n.PushMessage, type: typeof(PushMessageInputGraphType), nullable: true);
            Field(n => n.InternalMessage, type: typeof(InternalMessageInputGraphType), nullable: true);
            Field(n => n.ChatMessage, type: typeof(ChatMessageInputGraphType), nullable: true);
        }
    }

    public class SendNotificationGraphType : ObjectGraphType<SendNotificationGraph>
    {
        public SendNotificationGraphType()
        {
            Name = "sendNotification";
            Description = "Send notification";

            Field(n => n.FromEntityId, nullable: true);
            Field(n => n.ToEntityId, nullable: true);
            Field(n => n.ToEntityIds, nullable: true);
            Field(n => n.ToTopic, nullable: true);
            Field(n => n.ToTopicId, nullable: true);
            Field(n => n.PolicyId, nullable: true);
            Field(n => n.OfferId, nullable: true);
            Field(n => n.Type, nullable: true);
            Field(n => n.EmailMessage, type: typeof(EmailMessageGraphType), nullable: true);
            Field(n => n.SmsMessage, type: typeof(SmsMessageGraphType), nullable: true);
            Field(n => n.PushMessage, type: typeof(PushMessageGraphType), nullable: true);
            Field(n => n.InternalMessage, type: typeof(InternalMessageGraphType), nullable: true);
            Field(n => n.ChatMessage, type: typeof(ChatMessageGraphType), nullable: true);
        }
    }

    public class SendNotificationGraph
    {
        public string FromEntityId { get; set; }
        public string ToEntityId { get; set; }
        public List<string> ToEntityIds { get; set; }
        public string ToTopic { get; set; }
        public string ToTopicId { get; set; }
        public string PolicyId { get; set; }
        public string OfferId { get; set; }
        public string Type { get; set; }
        public PushMessageGraph PushMessage { get; set; }
        public EmailMessage EmailMessage { get; set; }
        public SmsMessage SmsMessage { get; set; }
        public InternalMessageGraph InternalMessage { get; set; }
        public ChatMessageGraph ChatMessage { get; set; }
        public DateTime? ScheduleToSendAt { get; set; }
        public bool UseConfig { get; set; }

        public static SendNotificationGraph ToGraph(SendNotificationCommand s) =>
            s == null
                ? null
                : new SendNotificationGraph
                {
                    FromEntityId = s.FromEntityId,
                    ToEntityId = s.ToEntityId,
                    ToEntityIds = s.ToEntityIds,
                    ToTopic = s.ToTopic,
                    ToTopicId = s.ToTopicId,
                    OfferId = s.OfferId,
                    PolicyId = s.PolicyId,
                    Type = s.Type,
                    EmailMessage = s.EmailMessage,
                    SmsMessage = s.SmsMessage,
                    PushMessage = PushMessageGraph.ToGraph(s.PushMessage),
                    InternalMessage = InternalMessageGraph.ToGraph(s.InternalMessage),
                    ChatMessage = ChatMessageGraph.ToGraph(s.ChatMessage)
                };

        public static SendNotificationCommand ToCommand(SendNotificationGraph graph) =>
            graph == null
                ? null
                : new SendNotificationCommand
                {
                    FromEntityId = graph.FromEntityId,
                    ToEntityId = graph.ToEntityId,
                    ToEntityIds = graph.ToEntityIds,
                    ToTopic = graph.ToTopic,
                    ToTopicId = graph.ToTopicId,
                    PolicyId = graph.PolicyId,
                    OfferId = graph.OfferId,
                    Type = graph.Type,
                    EmailMessage = graph.EmailMessage,
                    SmsMessage = graph.SmsMessage,
                    ScheduleToSendAt = graph.ScheduleToSendAt,
                    UseConfig = graph.UseConfig,
                    PushMessage = graph.PushMessage != null ? new PushMessage
                    {
                        Content = graph.PushMessage?.Content,
                        Title = graph.PushMessage?.Title,
                        Token = graph.PushMessage?.Token,
                        Topic = graph.PushMessage?.Topic,
                        Data = graph.PushMessage?.Data?.ToDictionary(x => x.Type, x => x.Value)
                    } : null,
                    InternalMessage = graph.InternalMessage != null ? new InternalMessage
                    {
                        TemplateRendering = graph.InternalMessage?.TemplateRendering,
                        Data = graph.InternalMessage.DataJsonString != null ? JToken.Parse(graph.InternalMessage.DataJsonString) : null
                    } : null,
                    ChatMessage = graph.ChatMessage != null ? new ChatMessage
                    {
                        FromName = graph.ChatMessage?.FromName,
                        Content = graph.ChatMessage?.Content,
                        Attachements = graph.ChatMessage.Attachements
                    } : null
                };
        }

    public class CreateNotificationTriggerInputGraphType : InputObjectGraphType<NotificationTriggerGraph>
    {
        public CreateNotificationTriggerInputGraphType()
        {
            Name = "createNotificationTriggerInput";
            Description = "Create notification trigger input";

            Field(c => c.CronExpression);
            Field(c => c.Notification, type: typeof(SendNotificationInputGraphType));
        }
    }

    public class NotificationTriggerGraphType : ObjectGraphType<NotificationTriggerGraph>
    {
        public NotificationTriggerGraphType()
        {
            Name = "notificationTrigger";
            Description = "Notification trigger";

            Field(c => c.Id);
            Field(c => c.CronExpression);
            Field(c => c.Notification, type: typeof(SendNotificationGraphType));
        }
    }

    public class NotificationTriggerGraph
    {
        public string Id { get; set; }
        public string CronExpression { get; set; }
        public SendNotificationGraph Notification { get; set; }

        public static NotificationTriggerGraph ToGraph(NotificationTrigger t) =>
            t == null
                ? null
                : new NotificationTriggerGraph
                {
                    Id = t.Id,
                    CronExpression = t.CronExpression,
                    Notification = SendNotificationGraph.ToGraph(t.SendNotificationCommand)
                };
    }


    public class EmailMessageGraphType : ObjectGraphType<EmailMessage>
    {
        public EmailMessageGraphType()
        {
            Name = "emailMessage";
            Description = "An email message";

            Field(m => m.From, nullable: true);
            Field(m => m.FromName, nullable: true);
            Field(m => m.ReplyTo, nullable: true);
            Field(m => m.ReplyToName, nullable: true);
            Field(m => m.To, nullable: true);
            Field(m => m.Ccs, nullable: true);
            Field(m => m.Bccs, nullable: true);
            Field(m => m.Subject);
            Field(m => m.HtmlContent, nullable: true);
            Field(m => m.PdfAttachments, type: typeof(ListGraphType<PdfAttachmentGraphType>), nullable: true);
            Field(m => m.TemplateRendering, type: typeof(TemplateRenderingGraphType));
        }
    }

    public class TemplateRenderingGraphType : ObjectGraphType<TemplateRendering>
    {
        public TemplateRenderingGraphType()
        {
            Name = "templateRendering";

            Field(t => t.TemplateId);
            Field(t => t.Input, type: typeof(RenderParametersGraphType));
        }
    }

    public class RenderParametersGraphType : ObjectGraphType<RenderParameters>
    {
        public RenderParametersGraphType()
        {
            Name = "templateRenderParameters";

            Field(t => t.Name);
            Field(t => t.ContentJsonString, nullable: true).Resolve(context => context.Source.Content != null ? JsonConvert.SerializeObject(context.Source.Content) : null);
            Field(t => t.VariablesJsonString, nullable: true).Resolve(context => context.Source.Variables != null ? JsonConvert.SerializeObject(context.Source.Variables) : null);
        }
    }

    public class PdfAttachmentGraphType : ObjectGraphType<PdfAttachment>
    {
        public PdfAttachmentGraphType()
        {
            Name = "pdfAttachment";

            Field(a => a.FileName);
            Field(a => a.HtmlContent, nullable: true);
            Field(a => a.Password, nullable: true);
        }
    }

    public class PdfAttachmentInputGraphType : InputObjectGraphType<PdfAttachment>
    {
        public PdfAttachmentInputGraphType()
        {
            Name = "pdfAttachmentInput";

            Field(a => a.FileName);
            Field(a => a.HtmlContent, nullable: true);
            Field(a => a.Bytes, type: typeof(ListGraphType<ByteGraphType>));
            Field(a => a.Password, nullable: true);
            Field(a => a.MarginSettings, type: typeof(MarginSettingsInputGraphType));
            Field(a => a.HeaderSettings, type: typeof(HeaderFooterSettingsInputGraphType));
            Field(a => a.FooterSettings, type: typeof(HeaderFooterSettingsInputGraphType));
        }
    }

    public class EmailMessageAttachmentInputGraphType : InputObjectGraphType<EmailMessageAttachment>
    {
        public EmailMessageAttachmentInputGraphType()
        {
            Name = "emailMessageAttachmentInput";

            Field(a => a.Key);
            Field(a => a.Name);
        }
    }

    public class SmsMessageGraphType : ObjectGraphType<SmsMessage>
    {
        public SmsMessageGraphType()
        {
            Name = "smsMessage";
            Description = "A sms message";

            Field(m => m.From, nullable: true);
            Field(m => m.To, nullable: true);
            Field(m => m.Body, nullable: true);
            Field(m => m.TemplateRendering, type: typeof(TemplateRenderingGraphType));
        }
    }

    public class PushMessageGraphType : ObjectGraphType<PushMessageGraph>
    {
        public PushMessageGraphType()
        {
            Name = "pushMessage";
            Description = "A push message";

            Field(m => m.Token, nullable: true);
            Field(m => m.Topic, nullable: true);
            Field(m => m.Title, nullable: true);
            Field(m => m.Content, nullable: true);
            Field(m => m.Data, type: typeof(ListGraphType<PushMessageDataGraphType>), nullable: true);
        }
    }

    public class InternalMessageGraphType : ObjectGraphType<InternalMessageGraph>
    {
        public InternalMessageGraphType(
            IL10nService l10nService,
            IDataLoaderContextAccessor accessor,
            ITemplateService templateService)
        {
            Name = "internalMessage";
            Description = "An internal message";

            Field(m => m.RenderedHtml, nullable: true).ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();
                string localizedTemplate = await context.GetL10nAsync(accessor, l10nService, $"templates-{context.Source.TemplateRendering.TemplateId}-html");
                if (localizedTemplate == null) return null;
                RenderHtmlCommand command = new()
                {
                    Html = localizedTemplate,
                    RenderParameters = context.Source.TemplateRendering.Input
                };
                Result<string> renderedHtml = await templateService.RenderHtmlAsync(tenantId, command);
                return renderedHtml.Value;
            });
            Field(t => t.DataJsonString, nullable: true);
        }
    }

    public class EmailMessageInputGraphType : InputObjectGraphType<EmailMessage>
    {
        public EmailMessageInputGraphType()
        {
            Name = "emailMessageInput";
            Description = "An email message";

            Field(m => m.From, nullable: true);
            Field(m => m.FromName, nullable: true);
            Field(m => m.ReplyTo, nullable: true);
            Field(m => m.ReplyToName, nullable: true);
            Field(m => m.To, nullable: true);
            Field(m => m.Ccs, nullable: true);
            Field(m => m.Bccs, nullable: true);
            Field(m => m.Subject, nullable: true);
            Field(m => m.HtmlContent, nullable: true);
            Field(m => m.Attachments, type: typeof(ListGraphType<EmailMessageAttachmentInputGraphType>), nullable: true);
            Field(m => m.PdfAttachments, type: typeof(ListGraphType<PdfAttachmentInputGraphType>), nullable: true);
            Field(m => m.TemplateRendering, type: typeof(TemplateRenderingInputGraphType));
        }
    }

    public class IssueDebitNoteEmailMessageInputGraphType : InputObjectGraphType<EmailMessage>
    {
        public IssueDebitNoteEmailMessageInputGraphType()
        {
            Name = "issueDebitNoteEmailMessageInput";
            Description = "issue debit note email message";

            Field(m => m.ReplyTo, nullable: true);
            Field(m => m.To, nullable: true);
            Field(m => m.Ccs, nullable: true);
            Field(m => m.Bccs, nullable: true);
            Field(m => m.TemplateRendering, type: typeof(TemplateRenderingContentInputGraphType));
        }
    }

    public class TemplateRenderingInputGraphType : InputObjectGraphType<TemplateRendering>
    {
        public TemplateRenderingInputGraphType()
        {
            Name = "templateRenderingInput";

            Field(t => t.TemplateId, nullable: true);
            Field(t => t.LogicalId, nullable: true);
            Field(t => t.Input, type: typeof(RenderParametersInputGraphType));
        }
    }

    public class TemplateRenderingContentInputGraphType : InputObjectGraphType<TemplateRendering>
    {
        public TemplateRenderingContentInputGraphType()
        {
            Name = "templateRenderingContentInput";

            Field(t => t.Input, type: typeof(IssueDebitNoteRenderParametersInputGraphType));
        }
    }

    public class RenderParametersInputGraphType : InputObjectGraphType<RenderParameters>
    {
        public RenderParametersInputGraphType()
        {
            Name = "templateRenderParametersInput";

            Field(t => t.Name);
            Field(t => t.ContentJsonString, nullable: true);
            Field(t => t.VariablesJsonString, nullable: true);
            Field(t => t.StatisticsMetadata, type: typeof(StatisticsMetadataInputGraphType), nullable: true);
            Field(t => t.OverrideAttachmentTemplates,
                type: typeof(ListGraphType<OverrideEmailAttachmentTemplateInputGraphType>));
            Field(t => t.OverrideAttachmentReferences,
                type: typeof(ListGraphType<OverrideEmailAttachmentReferenceInputGraphType>));
        }
    }

    public class IssueDebitNoteRenderParametersInputGraphType : InputObjectGraphType<RenderParameters>
    {
        public IssueDebitNoteRenderParametersInputGraphType()
        {
            Name = "issueDebitNoteTemplateRenderParametersInput";

            Field(t => t.ContentJsonString, nullable: true);
        }
    }

    public class StatisticsMetadataInputGraphType : InputObjectGraphType<StatisticsMetadata>
    {
        public StatisticsMetadataInputGraphType()
        {
            Name = "statisticsMetadataInput";

            Field(t => t.Vendor, nullable: true);
            Field(t => t.ProductCode, nullable: true);
        }
    }

    public class OverrideEmailAttachmentTemplateInputGraphType : InputObjectGraphType<AddEmailAttachmentTemplateCommand>
    {
        public OverrideEmailAttachmentTemplateInputGraphType()
        {
            Name = "overrideEmailAttachmentTemplateInput";

            Field(f => f.FileName);
            Field(f => f.TemplateId);
        }
    }
    
    public class OverrideEmailAttachmentReferenceInputGraphType : InputObjectGraphType<AddEmailAttachmentReferenceCommand>
    {
        public OverrideEmailAttachmentReferenceInputGraphType()
        {
            Name = "overrideEmailAttachmentReferenceInput";

            Field(f => f.FileName);
            Field(f => f.FilePath);
        }
    }
    

    public class SmsMessageInputGraphType : InputObjectGraphType<SmsMessage>
    {
        public SmsMessageInputGraphType()
        {
            Name = "smsMessageInput";
            Description = "A sms message";

            Field(m => m.From, nullable: true);
            Field(m => m.To, nullable: true);
            Field(m => m.Body, nullable: true);
            Field(m => m.TemplateRendering, type: typeof(TemplateRenderingInputGraphType));
        }
    }

    public class PushMessageInputGraphType : InputObjectGraphType<PushMessageGraph>
    {
        public PushMessageInputGraphType()
        {
            Name = "pushMessageInput";
            Description = "A push message";

            Field(m => m.Token, nullable: true);
            Field(m => m.Topic, nullable: true);
            Field(m => m.Title, nullable: true);
            Field(m => m.Content, nullable: true);
            Field(m => m.Data, type: typeof(ListGraphType<PushMessageDataInputGraphType>), nullable: true);
        }
    }

    public class PushMessageGraph
    {
        public string Token { get; set; }
        public string Topic { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public IEnumerable<PushMessageDataGraph> Data { get; set; }

        public static PushMessageGraph ToGraph(PushMessage p) =>
             p == null
                ? null
                : new PushMessageGraph
                {
                    Content = p.Content,
                    Data = p.Data?.Select(d => new PushMessageDataGraph
                    {
                        Type = d.Key,
                        Value = d.Value
                    }),
                    Title = p.Title,
                    Token = p.Token,
                    Topic = p.Topic
                };
    }

    public class PushMessageDataGraph
    {
        public string Type { get; set; }
        public string Value { get; set; }
    }

    public class PushMessageDataGraphType : ObjectGraphType<PushMessageDataGraph>
    {
        public PushMessageDataGraphType()
        {
            Name = "pushMessageData";

            Field(p => p.Type);
            Field(p => p.Value);
        }
    }

    public class PushMessageDataInputGraphType : InputObjectGraphType<PushMessageDataGraph>
    {
        public PushMessageDataInputGraphType()
        {
            Name = "pushMessageDataInput";

            Field(p => p.Type);
            Field(p => p.Value);
        }
    }
    public class InternalMessageInputGraphType : InputObjectGraphType<InternalMessageGraph>
    {
        public InternalMessageInputGraphType()
        {
            Name = "internalMessageInput";
            Description = "An internal message";

            Field(m => m.TemplateRendering, type: typeof(TemplateRenderingInputGraphType));
            Field(m => m.DataJsonString, nullable: true);
        }
    }
    public class InternalMessageGraph
    {
        public TemplateRendering TemplateRendering { get; set; }
        public string DataJsonString { get; set; }
        public string RenderedHtml { get; set; }

        public static InternalMessageGraph ToGraph(InternalMessage p) =>
             p == null
                ? null
                : new InternalMessageGraph
                {
                    DataJsonString = p.Data != null ? JsonConvert.SerializeObject(p.Data) : null,
                    TemplateRendering = p.TemplateRendering,
                };
    }
    public class ChatMessageGraphType : ObjectGraphType<ChatMessageGraph>
    {
        public ChatMessageGraphType()
        {
            Name = "chatMessage";
            Description = "A chat message";

            Field(m => m.FromName, nullable: true);
            Field(m => m.Content, nullable: true);
            Field(m => m.Attachements, type: typeof(ListGraphType<StringGraphType>));
        }
    }
    public class ChatMessageInputGraphType : InputObjectGraphType<ChatMessageGraph>
    {
        public ChatMessageInputGraphType()
        {
            Name = "chatMessageInput";
            Description = "A chat message";

            Field(m => m.FromName, nullable: true);
            Field(m => m.Content, nullable: true);
            Field(m => m.Attachements, type: typeof(ListGraphType<StringGraphType>));
        }
    }
    public class ChatMessageGraph
    {
        public string FromName { get; set; }
        public string Content { get; set; }
        public IEnumerable<string> Attachements { get; set; }

        public static ChatMessageGraph ToGraph(ChatMessage p) =>
             p == null
                ? null
                : new ChatMessageGraph
                {
                    FromName = p.FromName,
                    Content = p.Content,
                    Attachements = p.Attachements
                };
    }

    public class NotificationsGraph
    {
        public string EntityId { get; set; } //Just for mapping, to resolve notifications per entity
        public long TotalCount { get; set; }
        public IEnumerable<NotificationGraph> List { get; set; }
        public NotificationWhere Where { get; set; } // populated in covergoQuery
        public DateTime AsOf { get; set; } // populated in asOf
        public int? Skip { get; set; }
        public int? First { get; set; }
        public SortGraph Sort { get; set; }
        public bool IsResolved { get; set; }
    }

    public class NotificationTriggerWhereInputGraphType : InputObjectGraphType<NotificationTriggerWhere>
    {
        public NotificationTriggerWhereInputGraphType()
        {
            Name = "notificationTriggerWhereInput";

            Field(f => f.Or, type: typeof(ListGraphType<NotificationTriggerWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<NotificationTriggerWhereInputGraphType>));

            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);

            Field(f => f.SendNotificationCommand, type: typeof(SendNotificationCommandWhereInputGraphType));

            this.PopulateSystemWhereFields();
        }
    }

    public class SendNotificationCommandWhereInputGraphType : InputObjectGraphType<SendNotificationCommandWhere>
    {
        public SendNotificationCommandWhereInputGraphType()
        {
            Name = "sendNotificationCommandWhereInput";

            Field(n => n.ToEntityId, nullable: true);
            Field(n => n.ToEntityId_in, nullable: true);
        }
    }

    public class InitializeTenantNotificationsInputGraphType : InputObjectGraphType<InitializeTenantNotificationsCommand>
    {
        public InitializeTenantNotificationsInputGraphType()
        {
            Name = "initializeTenantNotificationsInput";

            Field(i => i.Configs, type: typeof(ListGraphType<CreateNotificationConfigInputGraph>));
        }
    }

    public class NotificationConfigGraphType : ObjectGraphType<NotificationConfig>
    {
        public NotificationConfigGraphType()
        {
            Name = "notificationConfig";

            Field(c => c.ClientId);
            Field(c => c.EmailConfig, type: typeof(EmailConfigGraphType));
        }
    }

    public class NotificationConfigWhereInputGraphType : InputObjectGraphType<NotificationConfigWhere>
    {
        public NotificationConfigWhereInputGraphType()
        {
            Name = "notificationConfigWhere";

            Field(n => n.ClientId, nullable: true);
        }
    }

    public class CreateNotificationConfigInputGraph : InputObjectGraphType<CreateNotificationConfigCommand>
    {
        public CreateNotificationConfigInputGraph()
        {
            Name = "createNotificationConfigInput";

            Field(c => c.ClientId);
            Field(c => c.EmailConfig, type: typeof(EmailConfigInputGraphType));
        }
    }

    public class EmailConfigGraphType : ObjectGraphType<EmailConfig>
    {
        public EmailConfigGraphType()
        {
            Name = "emailConfig";

            Field(c => c.EmailName);
            Field(c => c.EmailSender);
            Field(c => c.Ccs, nullable: true);
            Field(c => c.Bccs, nullable: true);
        }
    }

    public class EmailConfigInputGraphType : InputObjectGraphType<EmailConfig>
    {
        public EmailConfigInputGraphType()
        {
            Name = "emailConfigInput";

            Field(c => c.EmailName);
            Field(c => c.EmailSender);
            Field(c => c.Ccs, nullable: true);
            Field(c => c.Bccs, nullable: true);
        }
    }

    public class NotificationSubscriptionGraph
    {
        public string Id { get; set; }
        public string TopicName { get; set; }
        public IEnumerable<EntityGraph> Entities { get; set; }
        public IEnumerable<TagGraph> Tags { get; set; }

        public static NotificationSubscriptionGraph ToGraph(NotificationSubscription n) =>
            n == null
                ? null
                : new NotificationSubscriptionGraph
                {
                    Entities = n?.EntitiesIds?.Select(id => new EntityGraph { Id = id }),
                    TopicName = n.TopicName,
                    Id = n.Id,
                    Tags = n?.Tags?.Select(t => TagGraph.ToGraph(t))
                };
    }

    public class NotificationSubscriptionGraphType : ObjectGraphType<NotificationSubscriptionGraph>
    {
        public NotificationSubscriptionGraphType(
            IDataLoaderContextAccessor accessor,
            IEntityService<Internal, CreateInternalCommand, UpdateInternalCommand> internalService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand> companyService,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService,
            IEntityService<CoverGo.Users.Domain.Objects.Object, CreateObjectCommand, UpdateObjectCommand> objectService,
            PermissionValidator permissionValidator
            )
        {
            Name = "notificationSubscription";
            Description = "A notification subscription with a topic and a list of entities subscribed";

            Field(n => n.Id, nullable: true);
            Field(n => n.TopicName, nullable: true);
            Field(n => n.Entities, type: typeof(ListGraphType<EntityInterfaceGraphType>)).ResolveAsync(async context =>
            {
                if (context.Source.Entities == null || context.Source.Entities.Count() == 0)
                    return new List<EntityGraph> { };

                string tenantId = context.GetTenantIdFromToken();

                IEnumerable<string> allowedIndividualIds = await permissionValidator.GetTargetIdsFromClaim(context,"readIndividuals");
                var individualLoader = accessor.Context.GetOrAddBatchLoader<string, Individual>("GetIndividuals",
                    ids => individualService.GetDictionaryAsync(tenantId, allowedIndividualIds.Contains("all")
                     ? new IndividualWhere { Id_in = ids?.ToList() }
                     : new IndividualWhere
                     {
                         And = new List<IndividualWhere>
                         {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedIndividualIds.ToList() }
                         }
                     }));

                IEnumerable<string> allowedCompanyIds = await permissionValidator.GetTargetIdsFromClaim(context,"readCompanies");
                var companyLoader = accessor.Context.GetOrAddBatchLoader<string, Company>("GetCompanies",
                    ids => companyService.GetDictionaryAsync(tenantId, allowedCompanyIds.Contains("all")
                     ? new CompanyWhere { Id_in = ids?.ToList() }
                     : new CompanyWhere
                     {
                         And = new List<CompanyWhere>
                         {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedCompanyIds.ToList() }
                         }
                     }));
                IEnumerable<string> allowedInternalsIds = await permissionValidator.GetTargetIdsFromClaim(context,"readInternals");
                var internalLoader = accessor.Context.GetOrAddBatchLoader<string, Internal>("GetInternals",
                    ids => internalService.GetDictionaryAsync(tenantId, allowedInternalsIds.Contains("all")
                     ? new InternalWhere { Id_in = ids?.ToList() }
                     : new InternalWhere
                     {
                         And = new List<InternalWhere>
                         {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedInternalsIds.ToList() }
                         }
                     }));
                IEnumerable<string> allowedOrgnizationsIds = await permissionValidator.GetTargetIdsFromClaim(context,"readInternals");
                var organizationLoader = accessor.Context.GetOrAddBatchLoader<string, Organization>("GetOrganizations",
                    ids => organizationService.GetDictionaryAsync(tenantId, allowedOrgnizationsIds.Contains("all")
                     ? new OrganizationWhere { Id_in = ids?.ToList() }
                     : new OrganizationWhere
                     {
                         And = new List<OrganizationWhere>
                         {
                                    new() { Id_in = ids?.ToList() },
                                    new() { Id_in = allowedOrgnizationsIds .ToList() }
                         }
                     }));

                IEnumerable<Task<Individual>> individualDtosTasks = context.Source.Entities?.Select(i => individualLoader.LoadAsync(i.Id));
                IEnumerable<Task<Company>> companyDtosTasks = context.Source.Entities?.Select(i => companyLoader.LoadAsync(i.Id));
                IEnumerable<Task<Internal>> internalDtosTasks = context.Source.Entities?.Select(i => internalLoader.LoadAsync(i.Id));
                IEnumerable<Task<Organization>> organizationDtosTasks = context.Source.Entities?.Select(i => organizationLoader.LoadAsync(i.Id));

                var individualCollectionTasks = Task.WhenAll(individualDtosTasks);
                var companyCollectionTasks = Task.WhenAll(companyDtosTasks);
                var internalCollectionTasks = Task.WhenAll(internalDtosTasks);
                var organizationCollectionTasks = Task.WhenAll(organizationDtosTasks);

                await Task.WhenAll(individualCollectionTasks, companyCollectionTasks, internalCollectionTasks, organizationCollectionTasks);

                var entities = new List<Entity>();
                entities.AddRange(individualCollectionTasks.Result?.Select(t => t)?.Where(t => t != null));
                entities.AddRange(companyCollectionTasks.Result?.Select(t => t)?.Where(t => t != null));
                entities.AddRange(internalCollectionTasks.Result?.Select(t => t)?.Where(t => t != null));
                entities.AddRange(organizationCollectionTasks.Result?.Select(t => t)?.Where(t => t != null));

                return context.Source.Entities.Select(i => entities?.FirstOrDefault(e => e?.Id == i.Id)?.ToGraph())?.Where(e => e != null); //to preserve the order
            });
            Field(n => n.Tags, type: typeof(ListGraphType<TagGraphType>));
        }
    }
    public class NotificationSubscriptionWhereInputGraphType : InputObjectGraphType<NotificationSubscriptionWhere>
    {
        public NotificationSubscriptionWhereInputGraphType()
        {
            Name = "notificationSubscriptionWhereInput";

            Field(f => f.Or, type: typeof(ListGraphType<NotificationSubscriptionWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<NotificationSubscriptionWhereInputGraphType>));

            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);

            Field(f => f.Topic, nullable: true);
            Field(f => f.Topic_in, nullable: true);

            Field(f => f.EntitiesIds_contains, nullable: true);
            Field(f => f.Tags_some, type: typeof(TagWhereInputGraphType));

            this.PopulateSystemWhereFields();
        }
    }
    public class NotificationSubscriptionsGraph
    {
        public long TotalCount { get; set; }
        public IEnumerable<NotificationSubscriptionGraph> List { get; set; }
    }

    public class NotificationSubscriptionsGraphType : ObjectGraphType<NotificationSubscriptionsGraph>
    {
        public NotificationSubscriptionsGraphType(
            INotificationService notificationService,
            PermissionValidator permissionValidator)
        {
            Name = "notificationSubscriptions";
            Description = "Gets all notification subscriptions";

            Field(c => c.TotalCount)
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();

                   IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readNotificationSubscriptions");
                   NotificationSubscriptionWhere where = allowedIds.Contains("all")
                       ? context.ComputeArgAndVar<NotificationSubscriptionWhere, NotificationSubscriptionsGraph>("where") ?? new NotificationSubscriptionWhere()
                       : new NotificationSubscriptionWhere
                       {
                           And = new List<NotificationSubscriptionWhere>
                           {
                                context.ComputeArgAndVar<NotificationSubscriptionWhere, NotificationSubscriptionsGraph>("where") ?? new NotificationSubscriptionWhere(),
                                new() { Topic_in = allowedIds.ToList() }
                           }
                       };

                   return await notificationService.GetNotificationSubscriptionsTotalCountAsync(tenantId, where);
               });

            Field(c => c.List, type: typeof(ListGraphType<NotificationSubscriptionGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    IEnumerable<string> allowedIds = await permissionValidator.GetTargetIdsFromClaim(context,"readNotificationSubscriptions");
                    NotificationSubscriptionWhere where = allowedIds.Contains("all")
                        ? context.ComputeArgAndVar<NotificationSubscriptionWhere, NotificationSubscriptionsGraph>("where") ?? new NotificationSubscriptionWhere()
                        : new NotificationSubscriptionWhere
                        {
                            And = new List<NotificationSubscriptionWhere>
                            {
                                context.ComputeArgAndVar<NotificationSubscriptionWhere, NotificationSubscriptionsGraph>("where") ?? new NotificationSubscriptionWhere(),
                                new() { Topic_in = allowedIds.ToList() }
                            }
                        };

                    int? skip = context.ComputeArgAndVar<int?, NotificationSubscriptionsGraph>("skip");
                    int? first = context.ComputeArgAndVar<int?, NotificationSubscriptionsGraph>("limit");
                    SortGraph sort = context.ComputeArgAndVar<SortGraph, NotificationSubscriptionsGraph>("sort");

                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy };
                    IEnumerable<NotificationSubscription> subscriptions = await notificationService.GetNotificationSubscriptionsAsync(tenantId, queryArguments);

                    return subscriptions.Select(c => NotificationSubscriptionGraph.ToGraph(c));
                });
        }
    }

    public class ConnectedChatUsersGraphType : ObjectGraphType<ChatRoomUserList>
    {
        public ConnectedChatUsersGraphType()
        {
            Name = "connectedChatUsers";

            Field(f => f.TopicName, nullable: true);
            Field(f => f.LoginIdToUserName, nullable: true);
        }
    }
}