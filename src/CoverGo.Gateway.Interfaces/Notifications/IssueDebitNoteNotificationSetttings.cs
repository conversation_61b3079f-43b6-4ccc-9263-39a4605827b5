﻿using System.Linq;

namespace CoverGo.Gateway.Interfaces.Notifications
{
    public class IssueDebitNoteNotificationSetttings
    {
        public IssueDebitNoteNotificationSettingValue[] Values { get; set; }
    }

    public class IssueDebitNoteNotificationSettingValue
    {
        public string TenantId { get; set; }
        public string ClientId { get; set; }
        public string From { get; set; }
        public string FromName { get; set; }
        public string Subject { get; set; }
        public string TemplateId { get; set; }
    }

    public static class IssueDebitNoteNotificationSettingsExtensions
    {
        public static IssueDebitNoteNotificationSettingValue GetSettingsByTenantId(this IssueDebitNoteNotificationSettingValue[] values, string tenantId, string clientId)
        {
            return values.SingleOrDefault(x => x.TenantId == tenantId && x.ClientId == clientId);
        }
    }
}
