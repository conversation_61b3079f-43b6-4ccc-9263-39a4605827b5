﻿#nullable enable

using System;

namespace CoverGo.Gateway.Interfaces;

public class PermissionRequest
{
    public PermissionRequest(params string[] allowedClaimTypes)
    {
        AllowedClaimTypes = allowedClaimTypes;
        IgnoreTargetIds = true;
    }

    public PermissionRequest WithTargetIds(params string[]? targetIds)
    {
        TargetIds = targetIds;
        IgnoreTargetIds = false;

        return this;
    }
    
    public string[] AllowedClaimTypes { get; }

    public string[]? TargetIds { get; private set; }
    
    /// <summary>
    /// Had to add specific field to ignore targetIds and just checking if IgnoreTargetIds is null in order to keep
    /// backward compatibility with older code. 
    /// </summary>
    public bool IgnoreTargetIds { get; private set; }
    
    public override string ToString()
    {
        var result = string.Join("|", AllowedClaimTypes);
        if (IgnoreTargetIds)
            return result;

        return result + ":" + string.Join(",", TargetIds ?? Array.Empty<string>());
    }
}