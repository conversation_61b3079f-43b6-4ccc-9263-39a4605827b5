﻿using GraphQL.Language.AST;

using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Types;

public class ByteValueConverter : IAstFromValueConverter
{
    public bool Matches(object value, IGraphType type)
    {
        return value is byte;
    }

    public IValue Convert(object value, IGraphType type)
    {
        return new ByteValue((byte)value);
    }
}

public class ByteValue : ValueNode<byte>
{
    public ByteValue(byte value) => Value = value;

    protected override bool Equals(ValueNode<byte> other) => Value == other.Value;
}

// TODO: Use GraphQL.Types 3.0
public class ByteGraphType : ScalarGraphType
{
    public ByteGraphType()
    {
        Name = "Byte";
        Description = "ByteGraphType";
    }

    public override object Serialize(object value) => ParseValue(value);

    public override object ParseValue(object value)
    {
        if (byte.TryParse(value?.ToString() ?? string.Empty, out byte intByte))
            return intByte;
        else if (byte.TryParse(value?.ToString() ?? string.Empty, System.Globalization.NumberStyles.HexNumber, null, out byte hexByte))
            return hexByte;

        return null;
    }

    public override object ParseLiteral(IValue value)
    {
        switch (value)
        {
            case StringValue stringValue:
                return ParseValue(stringValue.Value);

            case ByteValue byteValue:
                return byteValue.Value;

            case IntValue intValue:
                if (byte.MinValue <= intValue.Value && intValue.Value <= byte.MaxValue)
                    return (byte)intValue.Value;
                return null;

            default:
                return null;
        }
    }
}

