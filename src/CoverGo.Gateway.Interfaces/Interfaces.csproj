<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>CoverGo.Gateway.Interfaces</AssemblyName>
    <RootNamespace>CoverGo.Gateway.Interfaces</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ComponentPro.Saml" />
    <PackageReference Include="CoverGo.Applications.Http.GraphQl.Services" />
    <PackageReference Include="CoverGo.HotChocolate.TypeExtensions" />
    <PackageReference Include="CoverGo.GraphQLGenerators" />
    <PackageReference Include="CoverGo.JsonUtils" />
    <PackageReference Include="CoverGo.SettableValues" />
    <PackageReference Include="FormatWith" />
    <PackageReference Include="GraphQL" />
    <PackageReference Include="GraphQL.Authorization" />
    <PackageReference Include="GraphQL.Server.Transports.AspNetCore" />
    <PackageReference Include="HtmlSanitizer" />
    <PackageReference Include="JsonLogic.Net" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
    <PackageReference Include="morelinq" />
    <PackageReference Include="NodaTime" />
    <PackageReference Include="OpenSSL.PrivateKeyDecoder" />
    <PackageReference Include="Serilog" />
    <PackageReference Include="Stripe.net" />
    <PackageReference Include="System.Linq.Dynamic.Core" />
    <PackageReference Include="ClosedXML" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Common\Common.csproj" />
    <ProjectReference Include="..\CoverGo.Gateway.Domain\Domain.csproj" />
    <ProjectReference Include="..\CoverGo.Gateway.Infrastructure\Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

</Project>
