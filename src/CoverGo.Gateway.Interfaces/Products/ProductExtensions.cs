using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Interfaces.Templates;
using CoverGo.Gateway.Interfaces.Users;
using GraphQL.DataLoader;
using GraphQL.Types;

using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Gateway.Interfaces.Products
{
    public static class ProductExtensions
    {
        public static ProductGraph ToGraph(this Product dto)
        {
            if (dto == null)
                return null;

            var product = new ProductGraph();
            product.PopulateProduct(dto);

            return product;
        }

        public static void PopulateProduct(this ProductGraph product, Product dto)
        {
            product.ProductId = new ProductId { Plan = dto.Id, Type = dto.Type, Version = dto.Version };
            product.InsurerId = dto.InsurerId;
            product.Benefits = dto.Benefits?.Select(b => b.ToGraph(product.ProductId));
        }

        public static ProductGraph ToGraph(this Product2 dto)
        {
            if (dto == null)
                return null;

            var product = new ProductGraph();
            product.ProductId = dto.Id;
            product.InsurerId = dto.InsurerId;
            product.IssuerProductId = dto.IssuerProductId;
            product.TenantId = dto.TenantId;
            product.Benefits = dto.Benefits?.Select(b => b.ToGraph(dto.Id));
            product.BenefitGraph = ToGraph(dto.Benefits, dto.Id);
            product.ClaimFactTemplateGraph = dto.ClaimFactTemplateId != null ? new FactTemplateGraph { Id = dto.ClaimFactTemplateId } : null;
            product.RejectionSettings = dto.RejectionSettings != null ? new ProductSettingsGraph { CodeNames = dto.RejectionSettings.Codes?.Select(c => new CodeNameGraph { Code = c, ProductId = dto.Id })?.ToList() } : null;

            product.LoadingSettings = dto.LoadingSettings != null ?
                    new LoadingSettingsGraph
                    {
                        CodeNames = dto.LoadingSettings.Codes?.Select(
                            c => new CodeNameGraph
                            {
                                Code = c,
                                ProductId = dto.Id
                            })?.ToList(),
                        MaxLoadingMultiplier = dto.LoadingSettings.MaxLoadingMultiplier
                    } : null;
            product.ExclusionSettings = dto.ExclusionSettings != null ? new ProductSettingsGraph { CodeNames = dto.ExclusionSettings.Codes?.Select(c => new CodeNameGraph { Code = c, ProductId = dto.Id })?.ToList() } : null;

            product.ClaimSettings = dto.ClaimSettings != null ? new ClaimSettingsGraph
            {
                RejectionSettings = dto.ClaimSettings.RejectionSettings != null ?
                    new ProductSettingsGraph
                    {
                        CodeNames = dto.ClaimSettings.RejectionSettings.Codes?.Select(c => new CodeNameGraph { Code = c, ProductId = dto.Id })?.ToList()
                    } : null,
                DiagnosisSettings = dto.ClaimSettings.DiagnosisSettings != null ?
                    new ProductSettingsGraph
                    {
                        CodeNames = dto.ClaimSettings.DiagnosisSettings.Codes?.Select(c => new CodeNameGraph { Code = c })?.ToList()
                    } : null,
                OperationSettings = dto.ClaimSettings.OperationSettings != null ?
                    new ProductSettingsGraph
                    {
                        CodeNames = dto.ClaimSettings.OperationSettings.Codes?.Select(c => new CodeNameGraph { Code = c })?.ToList()
                    } : null,
                ProviderSettings = dto.ClaimSettings.ProviderSettings != null ?
                    new ProviderSettingsGraph
                    {
                        Providers = dto.ClaimSettings.ProviderSettings?.EntityIds.Select(i => new EntityGraph { Id = i })?.ToList()
                    } : null
            } : null;

            product.Underwriting = dto.Underwriting != null ? UnderwritingGraph.ToGraph(dto.Id, dto.Underwriting) : null;
            product.Tags = dto.Tags?.Select(t => TagGraph.ToGraph(t));
            product.Facts = dto.Facts?.Select(f => FactGraph.ToGraph(f));

            product.LaunchPeriodStartDate = dto.LaunchPeriodStartDate;
            product.LaunchPeriodEndDate = dto.LaunchPeriodEndDate;
            product.ChangeEffectiveDate = dto.ChangeEffectiveDate;
            product.Status = dto.Status;
            product.InternalReviews = dto.InternalReviews?.Select(i => InternalReviewGraph.ToGraph(i));

            product.CreatedBy = dto.CreatedById != null ? new Auth.LoginGraph { Id = dto.CreatedById } : null;
            product.LastModifiedBy = dto.LastModifiedById != null ? new Auth.LoginGraph { Id = dto.LastModifiedById } : null;
            product.CreatedAt = dto.CreatedAt;
            product.LastModifiedAt = dto.LastModifiedAt;
            product.Representation = dto.Representation;
            product.Fields = dto.Fields;

            product.LifecycleStage = dto.LifecycleStage;
            product.Scripts = dto.ScriptIds?.Select(sid => new ScriptGraph { Id = sid }).ToArray();
            product.TemplateRelationships = dto.TemplateRelationshipIds?.Select(tid => new TemplateRelationshipGraph { Id = tid }).ToArray();
            product.ProductTreeId = dto.ProductTreeId;
            product.TermsAndConditionsTemplate = dto.TermsAndConditionsTemplateId != null ? new TemplateGraph
            {
                Id = dto.TermsAndConditionsTemplateId
            } : null;
            product.TermsAndConditionsJacket = dto.TermsAndConditionsJacketId != null ? new JacketGraph
            {
                Id = dto.TermsAndConditionsJacketId
            } : null;
            product.RatingFactorsTable = dto.RatingFactorsTable;
            product.Segments = dto.Segments;
            product.PolicyIssuanceMethod = dto.PolicyIssuanceMethod;
            product.OfferValidityPeriod = dto.OfferValidityPeriod;
            product.AllowCustomProduct = dto.AllowCustomProduct;
            product.UpdateTypes = dto.UpdateTypes;
            product.AutoRenewal = dto.AutoRenewal;
            product.RenewalNotification = dto.RenewalNotification;

            return product;
        }

        public static ScriptGraph ToGraph(this Script script) =>
            new()
            {
                Id = script.Id,
                InputSchema = script.InputSchema,
                Name = script.Name,
                OutputSchema = script.OutputSchema,
                SourceCode = script.SourceCode,
                ReferenceSourceCodeUrl = script.ReferenceSourceCodeUrl,
                ReferenceSourceCode = script.ReferenceSourceCode,
                ExternalTableDataUrl = script.ExternalTableDataUrl,
                ExternalTableDataUrls = script.ExternalTableDataUrls,
                Type = script.Type,
            };

        public static IEnumerable<BenefitGraph2> ToGraph(IEnumerable<Benefit> benefits, ProductId productId)
        {
            if (benefits == null)
                return null;

            IEnumerable<Benefit> topLevelBenefits = benefits.Where(b => b.ParentTypeId == null);
            IEnumerable<BenefitGraph2> handledBenefits = HandleFields(topLevelBenefits, benefits.Except(topLevelBenefits), productId);
            return handledBenefits;
        }

        public static IEnumerable<BenefitGraph2> HandleFields(IEnumerable<Benefit> parentBenefits, IEnumerable<Benefit> benefits, ProductId productId)
        {
            IEnumerable<BenefitGraph2> benefitGraphs = Enumerable.Empty<BenefitGraph2>();
            foreach (IGrouping<string, Benefit> benefitsByTypeId in parentBenefits.GroupBy(b => b.TypeId))
            {
                var benefitGraphToAdd = new BenefitGraph2
                {
                    ProductId = productId,
                    TypeId = benefitsByTypeId.Key,
                    CurrencyCode = benefitsByTypeId.FirstOrDefault()?.CurrencyCode ?? CurrencyCode.Undefined,
                    Options = Enumerable.Empty<BenefitGraph2>(),
                    Children = Enumerable.Empty<BenefitGraph2>()
                };

                IEnumerable<Benefit> benefitOptions = benefitsByTypeId;

                if (benefitOptions.Count() > 1)
                    foreach (Benefit benefitOption in benefitOptions)
                    {
                        var option = new BenefitGraph2
                        {
                            TypeId = benefitOption.TypeId,
                            ProductId = productId,
                            OptionKey = benefitOption.OptionKey,
                            ParentOptionKeys = benefitOption.ParentOptionKeys,
                            CurrencyCode = benefitOption.CurrencyCode,
                            IsValueInput = benefitOption.IsValueInput,
                            Condition = ConditionGraph.ToGraph(benefitOption.Condition),
                            RawData = benefitOption.Value
                        };
                        option.Children = HandleFields(benefits.Where(b => b.ParentTypeId == benefitOption.TypeId && (b.ParentOptionKey == benefitOption.OptionKey || //ToDo: remove after migrating old product data for infinity
                           (b.ParentOptionKeys?.Contains(option.OptionKey) ?? false))
                        ), benefits, productId);


                        benefitGraphToAdd.Options = benefitGraphToAdd.Options.Append(option);
                    }

                else
                {
                    benefitGraphToAdd.IsValueInput = benefitsByTypeId.FirstOrDefault()?.IsValueInput ?? false;
                    benefitGraphToAdd.RawData = benefitsByTypeId.FirstOrDefault()?.Value;
                    benefitGraphToAdd.OptionKey = benefitsByTypeId.FirstOrDefault()?.OptionKey;
                    benefitGraphToAdd.ParentOptionKeys = benefitsByTypeId.FirstOrDefault()?.ParentOptionKeys;
                    benefitGraphToAdd.Condition = ConditionGraph.ToGraph(benefitsByTypeId.FirstOrDefault()?.Condition);
                }

                IEnumerable<Benefit> childBenefits = benefitGraphToAdd?.OptionKey != null
                    ? benefits.Where(b => b.ParentTypeId == benefitsByTypeId.Key
                        && (b.ParentOptionKey == benefitGraphToAdd.OptionKey //TODO: remove after migrating away from old ParentOptionKey
                        || (b.ParentOptionKeys?.Contains(benefitGraphToAdd.OptionKey) ?? true)))
                   : benefits.Where(b => b.ParentTypeId == benefitsByTypeId.Key
                        && b.ParentOptionKey == null //TODO: remove after migrating away from old ParentOptionKey
                        && (!b.ParentOptionKeys?.Any() ?? true));

                benefitGraphToAdd.Children = benefitGraphToAdd.Children.Concat(HandleFields(childBenefits, benefits.Except(benefitsByTypeId), productId));

                benefitGraphs = benefitGraphs.Append(benefitGraphToAdd);
            }

            return benefitGraphs;
        }

        public static BenefitGraph ToGraph(this Benefit dto, ProductId productId) => new()
        {
            TypeId = dto.TypeId,
            ParentTypeId = dto.ParentTypeId,
            IsOptional = dto.IsOptional,
            ProductId = productId,
            IsValueInput = dto.IsValueInput,
            RawData = dto.Value,
            ParentOptionKey = dto.ParentOptionKey,
            ParentOptionKeys = dto.ParentOptionKeys,
            OptionKey = dto.OptionKey,
            CurrencyCode = dto.CurrencyCode,
            Condition = ConditionGraph.ToGraph(dto.Condition),
            Options = dto.Options?.Select(o => o.ToGraph(productId, dto.TypeId))
        };

        private static BenefitOptionGraph ToGraph(this BenefitOption dto, ProductId productId, string benefitTypeId) => new()
        {
            ProductId = productId,
            BenefitTypeId = benefitTypeId,
            Key = dto.Key,
            RawData = dto.Value,
        };

        public static InsurerGraph ToGraph(this Insurer dto) =>
            dto == null
                ? null
                : new InsurerGraph
                {
                    Id = dto.Id,
                    TenantId = dto.TenantId,
                    LogoUrls = new InsurerLogoUrlsGraph
                    {
                        TypeA = dto.LogoUrls?.TypeA,
                        TypeB = dto.LogoUrls?.TypeB,
                        TypeC = dto.LogoUrls?.TypeC,
                        TypeD = dto.LogoUrls?.TypeD,
                        TypeE = dto.LogoUrls?.TypeE
                    },
                    CreatedAt = dto.CreatedAt,
                    LastModifiedAt = dto.LastModifiedAt,
                    CreatedBy = dto.CreatedById != null ? new Auth.LoginGraph { Id = dto.CreatedById } : null,
                    LastModifiedBy = dto.LastModifiedById != null ? new Auth.LoginGraph { Id = dto.LastModifiedById } : null
                };

        public static void PopulateBenefitGraphFields<T>(this ComplexGraphType<T> graphType,
            IDataLoaderContextAccessor accessor,
            IL10nService l10nService,
            IProductService productService)
            where T : BenefitGraph
        {
            graphType.Field(b => b.TypeId);
            graphType.Field(b => b.Name, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"products-{context.Source.ProductId}-benefits-{context.Source.TypeId}-name", $"benefits-{context.Source.TypeId}-name"));
            graphType.Field(b => b.Description, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"products-{context.Source.ProductId}-benefits-{context.Source.TypeId}-description", $"benefits-{context.Source.TypeId}-description"));
            graphType.Field(b => b.Value, nullable: true).ResolveAsync(async context =>
            {
                if (context.Source.ProductId == null)
                    return null;

                string l10nKey = context.Source.OptionKey == null ? $"products-{context.Source.ProductId}-benefits-{context.Source.TypeId}-format" : $"products-{context.Source.ProductId}-benefits-{context.Source.TypeId}-options-{context.Source.OptionKey}-format";
                string oldFormatl10nKey = context.Source.OptionKey == null ? $"products-{context.Source.ProductId.Plan}-benefits-{context.Source.TypeId}-format" : $"products-{context.Source.ProductId.Plan}-benefits-{context.Source.TypeId}-options-{context.Source.OptionKey}-format"; //ToDo: clean the old l10n db values
                return await context.GetL10nWithCurrencyNameAsync(accessor, l10nService, context.Source.CurrencyCode, context.Source.RawData, l10nKey, oldFormatl10nKey);
            });
            graphType.Field(b => b.DetailedValue, nullable: true).ResolveAsync(async context =>
            {
                string l10nKey = context.Source.OptionKey == null ? $"products-{context.Source.ProductId}-benefits-{context.Source.TypeId}-detailedFormat" : $"products-{context.Source.ProductId}-benefits-{context.Source.TypeId}-options-{context.Source.OptionKey}-detailedFormat";
                string oldFormatl10nKey = context.Source.OptionKey == null ? $"products-{context.Source.ProductId.Plan}-benefits-{context.Source.TypeId}-detailedFormat" : $"products-{context.Source.ProductId.Plan}-benefits-{context.Source.TypeId}-options-{context.Source.OptionKey}-detailedFormat";
                return await context.GetL10nWithCurrencyNameAsync(accessor, l10nService, context.Source.CurrencyCode, context.Source.RawData, l10nKey, oldFormatl10nKey);
            });
            graphType.Field(b => b.Value2, type: typeof(ScalarValueGraphType), nullable: true)
                .Resolve(context => context.Source.RawData?.ToScalarValue());
            graphType.Field(b => b.IsValueInput);
            graphType.Field(b => b.RawData, type: typeof(StringGraphType)).Resolve(context =>
                JsonConvert.SerializeObject(context.Source.RawData, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver() }));
            graphType.Field(b => b.OptionKey, nullable: true);
            graphType.Field(b => b.ParentOptionKeys, nullable: true);
            graphType.Field(b => b.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType), nullable: true);
            graphType.Field(b => b.IsOptional).DeprecationReason("not used anymore");

            graphType.Field(b => b.Categories, type: typeof(NonNullGraphType<ListGraphType<BenefitCategoryGraphType>>)).ResolveAsync(async context =>
            {
                string tenantId = context.GetTenantIdFromToken();
                string clientId = context.GetClientIdFromToken();

                Dictionary<string, Dictionary<string, List<string>>> benefitCategories = await productService.GetBenefitInfosAsync(tenantId, clientId);
                //: benefit categoryinfo should ask for productType as well
                Dictionary<string, List<string>> categoriesPerType = benefitCategories.FirstOrDefault(bc => bc.Key == context.Source.ProductId.Type).Value;
                //: Check for nulls
                IEnumerable<KeyValuePair<string, List<string>>> categories = categoriesPerType.Where(c => c.Value.Contains(context.Source.TypeId));

                IEnumerable<BenefitCategoryGraph> categoryGraphs = categories.Select(ci => new BenefitCategoryGraph
                {
                    Id = ci.Key,
                    BenefitTypeIds = ci.Value
                });

                return categoryGraphs;
            });
        }

        public static TagGraph ToGraph(this Tag tag)
           => tag != null
               ? new TagGraph
               {
                   Id = tag.Id,
                   Type = tag.Type
               } : null;

        public static IEnumerable<Benefit> SummarizeBenefits(IEnumerable<Benefit> benefitOptions, IEnumerable<Benefit> benefits)
        {
            if (benefits == null)
                return new List<Benefit>();

            IEnumerable<Benefit> summarizedBenefits = benefits.Select(b =>
            {
                var benefitSummary = new Benefit
                {
                    CurrencyCode = b.CurrencyCode,
                    IsOptional = b.IsOptional,
                    OptionKey = b.OptionKey,
                    ParentOptionKeys = b.ParentOptionKeys,
                    ParentTypeId = b.ParentTypeId,
                    TypeId = b.TypeId,
                    IsValueInput = b.IsValueInput,
                    Value = b.Value
                };

                if (b.OptionKey == null && !b.IsValueInput)
                    return benefitSummary;
                else
                {
                    Benefit benefitOption = benefitOptions?.FirstOrDefault(bo => bo.TypeId == benefitSummary.TypeId && bo.OptionKey == benefitSummary.OptionKey);
                    if (benefitOption != null)
                    {
                        benefitSummary.Value = benefitOption.Value;
                        return benefitSummary;
                    }
                    return null; // No option was selected.
                }
            }).Where(b => b != null);


            //IEnumerable<Benefit> summarizedBenefits = benefits?.Except(benefits?.Where(b => benefitOptions.Any(o => o.TypeId == b.TypeId && o.OptionKey != b.OptionKey)));
            //foreach (Benefit benefitOption in benefitOptions)
            //{
            //    Benefit benefitToUpdate = benefits.FirstOrDefault(b => b.TypeId == benefitOption.TypeId);
            //    benefitToUpdate.Value = benefitOption.Value; //for if they input their own value
            //}
            return summarizedBenefits;
        }
    }
}
