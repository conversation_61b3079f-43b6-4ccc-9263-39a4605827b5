using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Interfaces.Cases;
using CoverGo.Gateway.Interfaces.Notifications;
using GraphQL.DataLoader;
using GraphQL.Types;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Gateway.Interfaces.Products
{
    public class JacketsGraph
    {
        public long TotalCount { get; set; }

        public IEnumerable<JacketGraph> List { get; set; }
    }

    public class JacketsGraphType : ObjectGraphType<JacketsGraph>
    {
        public JacketsGraphType(IProductService productService)
        {
            Name = "jackets";
            Description = "Gets all jackets";

            Field(c => c.TotalCount)
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();

                   //TODO: Add read permission later
                   JacketWhere where = context.ComputeArgAndVar<JacketWhere, JacketsGraph>("where") ?? new JacketWhere();

                   return await productService.GetJacketsTotalCountAsync(tenantId, where);
               });

            Field(c => c.List, type: typeof(ListGraphType<JacketGraphType>))
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();

                    //TODO: Add read permission later
                    JacketWhere where = context.ComputeArgAndVar<JacketWhere, JacketsGraph>("where") ?? new JacketWhere();

                    int? skip = context.ComputeArgAndVar<int?, JacketsGraph>("skip");
                    int? first = context.ComputeArgAndVar<int?, JacketsGraph>("limit");
                    SortGraph sort = context.ComputeArgAndVar<SortGraph, JacketsGraph>("sort");
                    DateTime? asOf = context.ComputeArgAndVar<DateTime?, JacketsGraph>("asOf");

                    OrderBy orderBy = null;
                    if (sort != null)
                    {
                        orderBy = sort.ToOrderBy();
                    }

                    var queryArguments = new Domain.QueryArguments { Where = where, Skip = skip, First = first, OrderBy = orderBy, AsOf = asOf };
                    IEnumerable<Jacket> jackets = await productService.GetJacketsAsync(tenantId, queryArguments);
                    return jackets.Select(JacketGraph.ToGraph);
                });
        }
    }

    public class JacketGraphType : ObjectGraphType<JacketGraph>
    {
        public JacketGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService, PermissionValidator permissionValidator)
        {
            Name = "jacket";
            Description = "jacket";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.Id);
            Field(c => c.Title, nullable: true);
            Field(c => c.Status, nullable: true);
            Field(p => p.Clauses, type: typeof(ListGraphType<ClauseGraphType>))
                .GetPaginationArguments()
                .Resolve(context => context.Source.Clauses.Sort(context));
        }
    }

    public class JacketGraph : SystemObjectGraph
    {
        public string Id { get; set; }

        public string Title { get; set; }

        public string Status { get; set; }


        public IEnumerable<ClauseGraph> Clauses { get; set; }


        public static JacketGraph ToGraph(Jacket domain) =>
            domain != null
            ? new JacketGraph
            {
                Id = domain.Id,
                Title = domain.Title,
                Status = domain.Status,
                Clauses = domain.Clauses?.Select(ClauseGraph.ToGraph).OrderBy(c => c.Order),
            }.PopulateSystemGraphFields(domain)
            : null;
    }


    public class CreateJacketInputGraphType : InputObjectGraphType<CreateJacketCommand>
    {
        public CreateJacketInputGraphType()
        {
            Name = "createJacketInput";
            Description = "Create jacket input";

            Field(c => c.Title, nullable: true);
            Field(c => c.Status, nullable: true);
            Field(c => c.Clauses, type: typeof(ListGraphType<AddClauseToJacketInputGraphType>));
        }
    }

    public class UpdateJacketInputGraphType : InputObjectGraphType<UpdateJacketCommand>
    {
        public UpdateJacketInputGraphType()
        {
            Name = "updateJacketInput";
            Description = "Update jacket input";

            Field(c => c.Title, nullable: true);
            Field(c => c.Status, nullable: true);
            Field(c => c.Clauses, nullable: true, type: typeof(ListGraphType<AddClauseToJacketInputGraphType>));
        }
    }

    public class AddClauseToJacketInputGraphType : InputObjectGraphType<AddClauseToOfferCommand>
    {
        public AddClauseToJacketInputGraphType()
        {
            Name = "addClauseToJacketInput";
            Description = "add to clause jacket input";

            Field(c => c.Id);
            Field(c => c.TemplateId);
            Field(c => c.Order, nullable: true);
            Field(c => c.HtmlOverride, nullable: true);
            Field(c => c.RenderParameters, type: typeof(RenderParametersInputGraphType));
        }
    }

    public class JacketWhereInputGraphType : InputObjectGraphType<JacketWhere>
    {
        public JacketWhereInputGraphType()
        {
            Name = "jacketWhereInput";
            Description = "A jacket search filter";

            Field(f => f.Or, type: typeof(ListGraphType<JacketWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<JacketWhereInputGraphType>));

            Field(f => f.Id, nullable: true);
            Field(f => f.Id_in, nullable: true);

            Field(f => f.Status, nullable: true);
            Field(f => f.Status_contains, nullable: true);
            Field(f => f.Status_in, nullable: true);

            Field(f => f.Title, nullable: true);
            Field(f => f.Title_contains, nullable: true);

            this.PopulateSystemWhereFields();
        }
    }

    public class JacketInstanceGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public int? Order { get; set; }
        public JacketGraph Jacket { get; set; }
        public Jacket JacketInstance { get; set; }
        public bool StoredByValue { get; set; }

        public static JacketInstanceGraph ToGraph(JacketInstance domain) =>
            domain != null
                ? new JacketInstanceGraph
                {
                    Id = domain.Id,
                    Order = domain.Order,
                    Jacket = new JacketGraph { Id =  domain.JacketId},
                    JacketInstance = domain.Jacket,
                    StoredByValue = domain.Jacket != null
                } : null;
    }

    public class JacketInstanceGraphType : ObjectGraphType<JacketInstanceGraph>
    {
        public JacketInstanceGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            IProductService productService, PermissionValidator permissionValidator)
        {
            Name = "jacketInstance";
            Description = "The instance of jacket was added into offer";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(c => c.Id);
            Field(c => c.Order, nullable: true);
            Field(c => c.StoredByValue, nullable: true);
            Field(p => p.Jacket, type: typeof(JacketGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.JacketInstance != null)
                        return JacketGraph.ToGraph(context.Source.JacketInstance);
                    
                    if (context.Source.Jacket?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    Jacket jacket = (await productService.GetJacketsAsync(tenantId,
                        new Domain.QueryArguments
                        {
                            Where  = new JacketWhere()
                            {
                                Id = context.Source.Jacket.Id
                            }
                        })).FirstOrDefault();

                    return JacketGraph.ToGraph(jacket);
                });
        }
    }
}