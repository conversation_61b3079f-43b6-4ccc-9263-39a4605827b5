﻿
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Products;
using GraphQL.DataLoader;
using GraphQL.Types;
using System.Collections.Generic;

namespace CoverGo.Gateway.Interfaces.Products
{
    public class InsurerGraphType : ObjectGraphType<InsurerGraph>
    {
        public InsurerGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService, IAuthService authService, PermissionValidator permissionValidator)
        {
            Name = "insurer";
            Field(i => i.Id);
            Field(i => i.Name, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"insurers-{context.Source.Id}-name"));
            Field(i => i.DetailedName, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"insurers-{context.Source.Id}-detailedName"));
            Field(i => i.TenantId, nullable: true);

            Field<InsurerLogoUrlsGraphType>("logoUrls");
            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class InsurerLogoUrlsGraphType : ObjectGraphType<InsurerLogoUrlsGraph>
    {
        public InsurerLogoUrlsGraphType()
        {
            Name = "logoUrls";
            Field(l => l.TypeA, nullable: true).Description("flexible width color");
            Field(l => l.TypeB, nullable: true).Description("flexible width grey");
            Field(l => l.TypeC, nullable: true).Description("static width color");
            Field(l => l.TypeD, nullable: true).Description("static width grey");
            Field(l => l.TypeE, nullable: true).Description("static width left color");
        }
    }

    public class InsurerLogoUrlsInputGraphType : InputObjectGraphType<InsurerLogoUrlsGraph>
    {
        public InsurerLogoUrlsInputGraphType()
        {
            Name = "logoUrlsInput";
            Field(l => l.TypeA, nullable: true).Description("flexible width color");
            Field(l => l.TypeB, nullable: true).Description("flexible width grey");
            Field(l => l.TypeC, nullable: true).Description("static width color");
            Field(l => l.TypeD, nullable: true).Description("static width grey");
            Field(l => l.TypeE, nullable: true).Description("static width left color");
        }
    }

    public class InsurerInputGraphType : InputObjectGraphType<CreateInsurerCommand>
    {
        public InsurerInputGraphType()
        {
            Name = "createInsurerInput";
            Description = "The insurer to be added";

            Field(t => t.Id);
            Field(t => t.LogoUrls, type: typeof(InsurerLogoUrlsInputGraphType));
        }
    }

    public class MigrateInsurersInputGraph
    {
        public IEnumerable<CreateInsurerCommand> InsurerInputs { get; set; }
    }

    public class MigrateInsurersInputGraphType: InputObjectGraphType<MigrateInsurersInputGraph>
    {
        public MigrateInsurersInputGraphType()
        {
            Name = "migrateInsurersInput";

            Field(i => i.InsurerInputs, type: typeof(ListGraphType<InsurerInputGraphType>));
        }
    }
}
