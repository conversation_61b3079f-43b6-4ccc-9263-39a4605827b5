using System.Net.Http;
using CoverGo.Applications.Clients;
using CoverGo.Gateway.Domain.Products;

namespace CoverGo.Gateway.Interfaces.Products
{
    public class CoverGoProductDiscountCodeService : CoverGoGenericGenericServiceRestClientBase<DiscountCode, DiscountCodeUpsert, DiscountCodeFilter>
    {
        private readonly HttpClient _client;

        public CoverGoProductDiscountCodeService(HttpClient client) : base(client)
        {
            _client = client;
        }

        protected override string ApiBaseUrlBuildInternal(string tenantId) => 
            $"{tenantId}/api/v1/discountCodes";
    }
}