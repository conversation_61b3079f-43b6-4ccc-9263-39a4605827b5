﻿using CoverGo.Gateway.Domain.Products;

using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Products
{
    public class ProductWhereInputGraphType : InputObjectGraphType<ProductWhere>
    {
        public ProductWhereInputGraphType()
        {
            Name = "productWhereInput";
            Description = "A product filter";

            Field(a => a.Or, type: typeof(ListGraphType<ProductWhereInputGraphType>));
            Field(a => a.And, type: typeof(ListGraphType<ProductWhereInputGraphType>));

            Field(a => a.Id_in, type: typeof(ListGraphType<ProductIdInputGraphType>));
            Field(a => a.ProductId, type: typeof(ProductIdWhereInputGraphType));
            Field(a => a.IssuerProductId, nullable: true);
            Field(a => a.Insurer, type: typeof(InsurerWhereInputGraphType));
            Field(a => a.TenantId, nullable: true);
            Field(a => a.TenantId_in, nullable: true);
            Field(a => a.Benefits_some, type: typeof(BenefitWhereInputGraphType));
            Field(a => a.Tags_some, type: typeof(TagWhereInputGraphType));

            Field(a => a.Name_contains, nullable: true);

            Field(a => a.LaunchPeriodStartDate_lt, nullable: true);
            Field(a => a.LaunchPeriodStartDate_gt, nullable: true);
            Field(a => a.LaunchPeriodEndDate_lt, nullable: true);
            Field(a => a.LaunchPeriodEndDate_gt, nullable: true);
            Field(a => a.ChangeEffectiveDate_lt, nullable: true);
            Field(a => a.ChangeEffectiveDate_gt, nullable: true);
            Field(a => a.LifecycleStage, nullable: true);
            Field(a => a.Status, nullable: true);
            Field(f => f.Status_in, nullable: true);
            Field(f => f.Status_not_in, nullable: true);
            Field(a => a.InternalReviews_some, type: typeof(InternalReviewWhereInputGraphType));
            Field(a => a.Representation, type: typeof(FieldsWhereInputGraphType), nullable: true);
            Field(a => a.Fields, type: typeof(FieldsWhereInputGraphType), nullable: true);
            Field(a => a.ProductTreeId_exists, true);
        }
    }

    public class ProductIdWhereInputGraphType : InputObjectGraphType<ProductIdWhere>
    {
        public ProductIdWhereInputGraphType()
        {
            Name = "productIdWhereInput";
            Description = "A product id filter";

            Field(a => a.Plan, nullable: true);
            Field(a => a.Plan_in, nullable: true);
            Field(a => a.Plan_contains, nullable: true);
            Field(a => a.Version, nullable: true);
            Field(a => a.Version_in, nullable: true);
            Field(a => a.Version_contains, nullable: true);
            Field(a => a.Version_ncontains, nullable: true);
            Field(a => a.Type, nullable: true);
            Field(a => a.Type_in, nullable: true);
        }
    }

    public class InsurerWhereInputGraphType : InputObjectGraphType<InsurerWhere>
    {
        public InsurerWhereInputGraphType()
        {
            Name = "insurerWhere";
            Description = "An insurer filter";

            Field(a => a.Id, nullable: true);
            Field(a => a.Id_in, nullable: true);
            Field(a => a.Id_contains, nullable: true);
        }
    }

    public class BenefitWhereInputGraphType : InputObjectGraphType<BenefitWhere>
    {
        public BenefitWhereInputGraphType()
        {
            Name = "benefitWhere";
            Description = "A benefit filter";

            Field(a => a.Or, type: typeof(ListGraphType<BenefitWhereInputGraphType>), nullable: true);
            Field(a => a.And, type: typeof(ListGraphType<BenefitWhereInputGraphType>), nullable: true);

            Field(a => a.TypeId, nullable: true);
            Field(a => a.TypeId_in, nullable: true);
            Field(a => a.RawData_gt, nullable: true);
            Field(a => a.RawData_lt, nullable: true);
        }
    }

    public class TagWhereInputGraphType : InputObjectGraphType<TagWhere>
    {
        public TagWhereInputGraphType()
        {
            Name = "tagWhere";
            Description = "A tag filter";

            Field(a => a.Id, nullable: true);
            Field(a => a.Id_in, nullable: true);
            Field(a => a.Type, nullable: true);
            Field(a => a.Type_in, nullable: true);
        }
    }

    public class InternalReviewWhereInputGraphType : InputObjectGraphType<InternalReviewWhere>
    {
        public InternalReviewWhereInputGraphType()
        {
            Name = "internalReviewWhere";
            Description = "An internal review filter";

            Field(a => a.Status, nullable: true);
            Field(a => a.Status_in, nullable: true);
        }
    }
}
