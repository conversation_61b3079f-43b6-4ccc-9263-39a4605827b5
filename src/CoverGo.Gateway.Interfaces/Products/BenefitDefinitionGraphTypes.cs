﻿using CoverGo.Gateway.Domain.Products;
using CoverGo.GraphQLGenerators;
using GraphQL.DataLoader;
using GraphQL.Types;
using System.Collections.Generic;
using System.Linq;
using CoverGo.Gateway.Domain.L10n;

namespace CoverGo.Gateway.Interfaces.Products
{
    public sealed class BenefitDefinitionsGraphType : ObjectGraphType<BenefitDefinitions>
    {
        public BenefitDefinitionsGraphType()
        {
            Name = "benefitDefinitions";
            Field(f => f.TotalCount, nullable: true);
            Field(f => f.List, type: typeof(ListGraphType<BenefitDefinitionGraphType>), nullable: true);
        }
    }
    public sealed class BenefitDefinitionGraphType : ObjectGraphType<BenefitDefinitionGraph>
    {
        public BenefitDefinitionGraphType(
            IDataLoaderContextAccessor accessor,
            IProductService productService,
            IL10nService l10nService)
        {
            Name = "benefitDefinition";
            Field(f => f.Id, nullable: true);
            Field(f => f.BusinessId, nullable: true);
            Field(f => f.Name, nullable: true).ResolveAsync(async context => {
                string benefitName = await context.GetL10nAsync(accessor, l10nService, $"benefits-{context.Source.BusinessId}-name");
                return benefitName == null ? context.Source.Name : benefitName;
            });
            Field(f => f.Description, nullable: true).ResolveAsync(async context => {
                string benefitDescription = await context.GetL10nAsync(accessor, l10nService, $"benefits-{context.Source.BusinessId}-description");
                return benefitDescription == null ? context.Source.Description : benefitDescription;
            });
            Field(f => f.Status, nullable: true);
            Field(f => f.Fields, nullable: true);
            Field(f => f.BenefitDefinitionTypes, type: typeof(ListGraphType<BenefitDefinitionTypeGraphType>), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.BenefitDefinitionTypes == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    var dataLoader
                        = accessor.Context.GetOrAddBatchLoader<BenefitDefinitionGraph, IEnumerable<BenefitDefinitionType>>(
                            "GetBenefitDefinitionTypes",
                            async b =>
                            {
                                List<string> allBenefitDefinitionTypesIds = b.Where(i => i.BenefitDefinitionTypes != null)
                                    .SelectMany(i => i.BenefitDefinitionTypes)
                                    .Select(i => i.Id)
                                    .ToList();
                                IReadOnlyCollection<BenefitDefinitionType> allBenefitDefinitionTypes
                                    = (await productService.GetBenefitDefinitionTypesAsync(tenantId, new Domain.QueryArguments
                                    {
                                        Where = new BenefitDefinitionTypeWhere { Id_in = allBenefitDefinitionTypesIds?.ToList() }
                                    }))
                                        .ToArray();
                                return b.ToDictionary(
                                    prod => prod,
                                    prod => allBenefitDefinitionTypes.Where(bt => prod.BenefitDefinitionTypes.Any(s => bt.Id == s.Id)));

                            });

                    return (await dataLoader.LoadAsync(context.Source))
                        .Select(BenefitDefinitionTypeGraph.ToGraph).ToArray();
                });
        }
    }
    public class BenefitDefinitionWhereGraphType : InputObjectGraphType<BenefitDefinitionWhere>
    {
        public BenefitDefinitionWhereGraphType()
        {
            Name = "benefitDefinitionWhereInput";
            Field(f => f.Or, type: typeof(ListGraphType<BenefitDefinitionWhereGraphType>), nullable:true);
            Field(f => f.And, type: typeof(ListGraphType<BenefitDefinitionWhereGraphType>), nullable:true);
            Field(f => f.Id_in, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            Field(f => f.BusinessId_in, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            Field(f => f.Type, type: typeof(BenefitDefinitionTypeWhereGraphType), nullable: true);
            Field(f => f.Status, nullable: true);
            Field(f => f.Name_contains, nullable: true);
            Field(f => f.BusinessId_contains, nullable: true);
            Field(f => f.Fields, nullable: true, type:typeof(FieldsWhereInputGraphType));
        }
    }
    public sealed class CreateBenefitDefinitionInputGraphType : AutoInputObjectGraphType<CreateBenefitDefinitionInput> { }
    public sealed class UpdateBenefitDefinitionInputGraphType : AutoInputObjectGraphType<UpdateBenefitDefinitionInput> { }
    public sealed class DeleteBenefitDefinitionInputGraphType : AutoInputObjectGraphType<DeleteBenefitDefinitionInput> { }
    public sealed class BatchBenefitDefinitionInputGraphType : AutoInputObjectGraphType<BatchBenefitDefinitionInput> { }

    public sealed class BenefitDefinitionTypesGraphType : AutoObjectGraphType<BenefitDefinitionTypes> { }
    public sealed class BenefitDefinitionTypeGraphType : AutoObjectGraphType<BenefitDefinitionTypeGraph> { }
    public sealed class CreateBenefitDefinitionTypeInputGraphType : AutoInputObjectGraphType<CreateBenefitDefinitionTypeInput> { }
    public sealed class UpdateBenefitDefinitionTypeInputGraphType : AutoInputObjectGraphType<UpdateBenefitDefinitionTypeInput> { }
    public sealed class DeleteBenefitDefinitionTypeInputGraphType : AutoInputObjectGraphType<DeleteBenefitDefinitionTypeInput> { }
    public sealed class BatchBenefitDefinitionTypeInputGraphType : AutoInputObjectGraphType<BatchBenefitDefinitionTypeInput> { }

    public class BenefitDefinitionTypeWhereGraphType : InputObjectGraphType<BenefitDefinitionTypeWhere>
    {
        public BenefitDefinitionTypeWhereGraphType()
        {
            Name = "benefitDefinitionTypeWhereInput";
            Field(f => f.Or, type: typeof(ListGraphType<BenefitDefinitionTypeWhereGraphType>), nullable: true);
            Field(f => f.And, type: typeof(ListGraphType<BenefitDefinitionTypeWhereGraphType>), nullable: true);
            Field(f => f.Id_in, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            Field(f => f.BusinessId_in, type: typeof(ListGraphType<StringGraphType>), nullable: true);
            Field(f => f.Status, nullable: true);
            Field(f => f.Name_contains, nullable: true);
            Field(f => f.BusinessId_contains, nullable: true);
            Field(f => f.Fields, nullable: true, type:typeof(FieldsWhereInputGraphType));
        }
    }

    public class BenefitDefinitions
    {
        public long TotalCount { get; set; }
        public IEnumerable<BenefitDefinitionGraph> List { get; set; }
    }

    public class BenefitDefinitionTypes
    {
        public int TotalCount { get; set; }
        public IEnumerable<BenefitDefinitionType> List { get; set; }
    }

    public class BenefitDefinitionGraph
    {
        public string Id { get; set; }
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Fields { get; set; }
        public IReadOnlyCollection<BenefitDefinitionTypeGraph> BenefitDefinitionTypes { get; set; }
    }

    public class CreateBenefitDefinitionInput
    {
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Fields { get; set; }
        public IReadOnlyCollection<string> BenefitDefinitionTypeIds { get; set; }
    }

    public class UpdateBenefitDefinitionInput
    {
        public string BenefitDefinitionId { get; set; }
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Fields { get; set; }
        public IReadOnlyCollection<string> BenefitDefinitionTypeIds { get; set; }
    }
    
    public class BatchBenefitDefinitionInput
    {
        public List<CreateBenefitDefinitionInput> CreateBenefitDefinitionInputs { get; set; } = new();

        public List<UpdateBenefitDefinitionInput> UpdateBenefitDefinitionInputs { get; set; } = new();
    }

    public class DeleteBenefitDefinitionInput
    {
        public string BenefitDefinitionId { get; set; }
    }

    public class CreateBenefitDefinitionTypeInput
    {
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Fields { get; set; }
    }

    public class UpdateBenefitDefinitionTypeInput
    {
        public string BenefitDefinitionTypeId { get; set; }
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Fields { get; set; }
    }

    public class DeleteBenefitDefinitionTypeInput
    {
        public string BenefitDefinitionTypeId { get; set; }
    }
    
    public class BatchBenefitDefinitionTypeInput
    {
        public List<CreateBenefitDefinitionTypeInput> CreateBenefitDefinitionTypeInputs { get; set; } = new();

        public List<UpdateBenefitDefinitionTypeInput> UpdateBenefitDefinitionTypeInputs { get; set; } = new();
    }
}