﻿using CoverGo.GraphQLGenerators;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace CoverGo.Gateway.Interfaces.Products
{
    public sealed class Error2GraphType : AutoObjectGraphType<Error2> { }
    public sealed class CommandExecutionResultGraphType : AutoInterfaceGraphType<CommandExecutionResult> { }

    public interface CommandExecutionResult
    {
        [JsonProperty("errors")]
        List<Error2> Errors { get; set; }

        [JsonProperty("success")]
        [JsonRequired]
        bool Success { get; set; }
    }

    public class Error2
    {
        [JsonProperty("code")]
        public string Code { get; set; }

        [JsonProperty("message")]
        [JsonRequired]
        public string Message { get; set; }
    }
}