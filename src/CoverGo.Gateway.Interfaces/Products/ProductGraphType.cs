using CoverGo.DomainUtils;
using CoverGo.FeatureManagement;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Advisor;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Claims;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Advisor;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Templates;
using CoverGo.Gateway.Interfaces.Users;
using GraphQL.DataLoader;
using GraphQL.Types;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CurrencyCode = CoverGo.Gateway.Domain.CurrencyCode;
using Point = CoverGo.Gateway.Domain.Products.Point;

namespace CoverGo.Gateway.Interfaces.Products
{
    public class ProductIdInputGraphType : InputObjectGraphType<ProductId>
    {
        public ProductIdInputGraphType()
        {
            Name = "productIdInput";
            Description = "A product id input";

            Field(p => p.Plan);
            Field(p => p.Version, nullable: true);
            Field(p => p.Type);
        }
    }

    public class ProductIdInputToUpdateInputGraphType : InputObjectGraphType<ProductId>
    {
        public ProductIdInputToUpdateInputGraphType()
        {
            Name = "productIdInputToUpdateInput";
            Description = "A product id input";

            Field(p => p.Plan, nullable: true);
            Field(p => p.Version, nullable: true);
            Field(p => p.Type, nullable: true);
        }
    }

    public class ProductIdGraphType : ObjectGraphType<ProductId>
    {
        public ProductIdGraphType()
        {
            Name = "productId";
            Description = "A product id";

            Field(p => p.Plan);
            Field(p => p.Version, nullable: true);
            Field(p => p.Type);
        }
    }

    public class ProductIdToUpdateGraphType : ObjectGraphType<ProductIdToUpdate>
    {
        public ProductIdToUpdateGraphType()
        {
            Name = "productIdToUpdate";
            Description = "A product id";

            Field(p => p.Plan, nullable: true);
            Field(p => p.IsPlanChanged);
            Field(p => p.Version, nullable: true);
            Field(p => p.IsVersionChanged);
            Field(p => p.Type, nullable: true);
            Field(p => p.IsTypeChanged);
        }
    }

    public class ProductsGraphType : ObjectGraphType<ProductsGraph>
    {
        public ProductsGraphType()
        {
            Name = "products";
            Description = "Gets all products of all types";

            Field(c => c.TotalCount);
            Field(c => c.List, type: typeof(ListGraphType<ProductGraphType>));
        }
    }

    public class ProductsGraph
    {
        public int TotalCount { get; set; }
        public IEnumerable<ProductGraph> List { get; set; }
    }

    public class ProductGraphType : ObjectGraphType<ProductGraph>
    {
        public ProductGraphType(
            IDataLoaderContextAccessor accessor,
            IInsurerService insurerService,
            IProductService productService,
            IMarketingService marketingService,
            IL10nService l10nService,
            IAuthService authService,
            IPricingService pricingService,
            IAdvisorService advisorService,
            IClaimService claimService,
            ITemplateService templateService,
            IFileSystemService fileSystemService,
            PermissionValidator permissionValidator,
            IMultiTenantFeatureManager multiTenantFeatureManager,
            IOptions<HideIndividualPolicyExtraFieldsSettings> hideIndividualPolicyExtraFieldsSettings)
        {
            Name = "product";
            Description = "A product";

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);

            Field(p => p.ProductId, type: typeof(ProductIdGraphType));
            Field(p => p.Type, type: typeof(ProductTypeGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.ProductId?.Type != null)
                    {
                        string tenantId = context.GetTenantIdFromToken();
                        string clientId = context.GetClientIdFromToken(false);

                        var dataLoader = accessor.Context.GetOrAddBatchLoader<string, ProductType>("GetProductTypes",
                            i => productService.GetTypeDictionaryAsync(tenantId, clientId, new ProductTypeWhere { TypeId_in = i?.ToList() }));

                        ProductType type = await dataLoader.LoadAsync(context.Source.ProductId?.Type);

                        return ProductTypeGraph.ToGraph(type);
                    }

                    return null;
                });
            Field(p => p.TenantId, nullable: true);
            Field(p => p.Slug, nullable: true).ResolveAsync(context =>
                context.GetL10nAsync(accessor, l10nService, $"products-{context.Source.ProductId?.ToString()}-slug", $"products-{context.Source.ProductId?.Plan}-slug"));
            Field(p => p.Name, nullable: true).ResolveAsync(context =>
                context.GetL10nAsync(accessor, l10nService, $"products-{context.Source.ProductId?.ToString()}-name", $"products-{context.Source.ProductId?.Plan}-name"));
            Field(p => p.Description, nullable: true).ResolveAsync(context =>
                context.GetL10nAsync(accessor, l10nService, $"products-{context.Source.ProductId?.ToString()}-description", $"products-{context.Source.ProductId?.Plan}-description"));

            Field(p => p.LaunchPeriodStartDate, nullable: true);
            Field(p => p.LaunchPeriodEndDate, nullable: true);
            Field(p => p.ChangeEffectiveDate, nullable: true);
            Field(p => p.Status, nullable: true);

            Field(p => p.LifecycleStage, nullable: true);
            Field(p => p.Representation, nullable: true)
                .ResolveAsync(context => context.GetPermittedProjection(authService, accessor, context.Source.Representation, context.Source.ProductId.ToString(), "product"));
            Field(p => p.Fields, nullable: true)
                .ResolveAsync(context => context.GetPermittedProjection(authService, accessor, context.Source.Fields, context.Source.ProductId.ToString(), "product"));
            Field(p => p.UnderwritingRulesJson, nullable: true)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var dataLoader = accessor.Context.GetOrAddBatchLoader<ProductId, ProductUnderwritingJsonLogicRules>("GetProductUnderwritingJsonLogicRules",
                        async i => (await productService.GetUnderwrittingJsonLogicRulesAsync(tenantId, new ProductWhere { Id_in = i.ToList() })).ToDictionary(e => e.Id)
                    );

                    ProductUnderwritingJsonLogicRules jsonLogicRules = await dataLoader.LoadAsync(context.Source.ProductId);

                    return jsonLogicRules?.JsonLogicRules?.ToString(Newtonsoft.Json.Formatting.None);
                });

            Field(p => p.Insurer, type: typeof(InsurerGraphType), nullable: true)
                .ResolveAsync(async context =>
                {
                    if (context.Source.InsurerId == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken(false);

                    var insurersLoader = accessor.Context.GetOrAddBatchLoader<string, Insurer>(
                        "GetInsurers", i => insurerService.GetInsurersDictionaryAsync(tenantId, clientId, new InsurerFilter { Ids = i }));

                    Insurer insurerDto = await insurersLoader.LoadAsync(context.Source.InsurerId);

                    return insurerDto.ToGraph();
                });
            Field(p => p.IssuerProductId, nullable: true);

            Field(p => p.ImportantNotes, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"products-{context.Source.ProductId}-importantNotes", $"products-{context.Source.ProductId?.Plan}-importantNotes"));
            Field(p => p.MajorExclusions, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"products-{context.Source.ProductId}-majorExclusions", $"products-{context.Source.ProductId?.Plan}-majorExclusions"));
            Field(p => p.BrochureUrl, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"products-{context.Source.ProductId}-brochureUrl", $"products-{context.Source.ProductId?.Plan}-brochureUrl"));
            Field(p => p.PremiumTableUrl, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"products-{context.Source.ProductId}-premiumTableUrl", $"products-{context.Source.ProductId?.Plan}-premiumTableUrl"));
            Field(p => p.TermsAndConditionsUrl, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"products-{context.Source.ProductId}-termsAndConditionsUrl", $"products-{context.Source.ProductId?.Plan}-termsAndConditionsUrl"));
            Field(p => p.ApplicationFormUrl, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"products-{context.Source.ProductId}-applicationFormUrl", $"products-{context.Source.ProductId?.Plan}-applicationFormUrl"));
            Field(p => p.EnrollmentUrl, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"products-{context.Source.ProductId}-enrollmentUrl", $"products-{context.Source.ProductId?.Plan}-enrollmentUrl"));

            Field(p => p.Benefits, type: typeof(ListGraphType<BenefitGraphType>))
                .Argument<ListGraphType<StringGraphType>>("typeIds", "The benefit type ids.")
                .Argument<BooleanGraphType>("includeOptionals", "include optional benefits").DeprecationReason("not used anymore")
                .Resolve(context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken(isRequired: false);
                    string role = context.GetRoleFromToken(isRequired: false);

                    bool clientIdExists = (hideIndividualPolicyExtraFieldsSettings.Value?.ClientIds?.Any(x => x == clientId)).GetValueOrDefault();
                    var isDisallowIndividualPolicyExtraFieldsFeatureEnabled = multiTenantFeatureManager.IsEnabled("DisallowIndividualPolicyExtraFields", tenantId).GetAwaiter().GetResult();
                    if (isDisallowIndividualPolicyExtraFieldsFeatureEnabled && clientIdExists)
                        return null;

                    IEnumerable<string> typeIds = context.GetArgument<object[]>("typeIds")?.Select(o => o?.ToString());
                    bool includeOptionals = context.GetArgument<bool>("includeOptionals");

                    return context.Source.Benefits?.Where(b => (typeIds?.Contains(b.TypeId) ?? true) || includeOptionals && b.IsOptional == true);
                });

            Field(p => p.BenefitGraph, type: typeof(ListGraphType<BenefitGraph2GraphType>), nullable: true);

            Field(p => p.RejectionSettings, type: typeof(RejectionSettingsGraphType), nullable: true);

            Field(p => p.LoadingSettings, type: typeof(LoadingSettingsGraphType), nullable: true);
            Field(p => p.ExclusionSettings, type: typeof(ExclusionSettingsGraphType), nullable: true);
            Field(p => p.ClaimSettings, type: typeof(ClaimSettingsGraphType), nullable: true);

            Field(p => p.Tags, type: typeof(ListGraphType<TagGraphType>))
                .ResolveAsync(async context =>
                {
                    if (context.Source.Tags?.Any() ?? false)
                        return context.Source.Tags;

                    //ToDo: remove all this after tags are migrated away (used in vhis right now)
                    if (context.Source?.ProductId == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();

                    var tagsLoader = accessor.Context.GetOrAddBatchLoader<ProductId, IEnumerable<Tag>>("GetTags",
                        p => marketingService.GetTagsPerProductIdDictionaryAsync(tenantId, clientId, p));

                    IEnumerable<Tag> tags = await tagsLoader.LoadAsync(context.Source?.ProductId);

                    return tags?.Select(t => t.ToGraph()) ?? new List<TagGraph> { };
                });


            Field(p => p.Facts, type: typeof(ListGraphType<FactGraphType>));

            Field(p => p.InternalReviews, type: typeof(ListGraphType<InternalReviewGraphType>));

            Field(p => p.Scores, type: typeof(ListGraphType<ScoreGraphType>))
                .Argument<NonNullGraphType<StringGraphType>>("advisorId", "The id of the advisor")
                .Argument<ListGraphType<KeyValueInputGraphType>>("values", "A list of dynamic values to check underwriting rules")
                .ResolveAsync(async context =>
                {
                    string advisorId = context.GetArgument<string>("advisorId");

                    if (context.Source?.ProductId == null || advisorId == null)
                        return Enumerable.Empty<ScoreGraph>();

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken();

                    var factors = context.GetArgument<IEnumerable<KeyScalarValue>>("values")?.ToDictionary(x => x.Key, x => x.Value.GetValue());
                    JToken token = factors != null ? JToken.FromObject(factors) : null;

                    var adviceLoader = accessor.Context.GetOrAddBatchLoader<ProductId, IEnumerable<Score>>("GetScores",
                        async p =>
                        {
                            IEnumerable<Advice> advices = await advisorService.GetAdvicesAsync(tenantId, new AdviceQuery
                            {
                                ProductIds = p,
                                ClientId = clientId,
                                AdvisorId = advisorId,
                                Values = token
                            });

                            return advices.ToDictionary(i => i.Offer.ProductId, v => v.Scores.Where(s => s != null));
                        });

                    IEnumerable<Score> scores = await adviceLoader.LoadAsync(context.Source.ProductId);

                    return scores?.Select(s => new ScoreGraph
                    {
                        Id = s.Id,
                        Value = s.Value
                    });
                });

            Field(p => p.Scripts, type: typeof(ListGraphType<ScriptGraphType>), nullable: true)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken(isRequired: false);
                    string role = context.GetRoleFromToken(isRequired: false);

                    bool clientIdExists = (hideIndividualPolicyExtraFieldsSettings.Value?.ClientIds?.Any(x => x == clientId)).GetValueOrDefault();
                    var isDisallowIndividualPolicyExtraFieldsFeatureEnabled = await multiTenantFeatureManager.IsEnabled("DisallowIndividualPolicyExtraFields", tenantId);
                    if (isDisallowIndividualPolicyExtraFieldsFeatureEnabled && clientIdExists)
                        return null;

                    var scriptLoader = accessor.Context
                        .GetOrAddBatchLoader<ProductGraph, IEnumerable<Script>>("GetScripts",
                            async p =>
                            {
                                List<string> allScriptsIds = p
                                    .Where(i => i.Scripts != null)
                                    .SelectMany(i => i.Scripts)
                                    .Where(i => i != null)
                                    .Select(i => i.Id)
                                    .ToList();
                                IReadOnlyCollection<Script> allScripts = (await productService.GetScriptsAsync(tenantId, new ScriptWhere { Id_in = allScriptsIds })).ToArray();
                                return p.ToDictionary(prod => prod, prod => allScripts.Where(scr => prod.Scripts.Any(s => scr.Id == s.Id)));
                            });

                    IEnumerable<Script> scripts = await scriptLoader.LoadAsync(context.Source);

                    Dictionary<string, string> dict = new();
                    List<string> distinctUrls = scripts
                        .Where(s => !String.IsNullOrEmpty(s.ReferenceSourceCodeUrl))
                        .Select(s => s.ReferenceSourceCodeUrl)
                        .Distinct()
                        .ToList();

                    foreach (string url in distinctUrls)
                    {
                        Result<byte[]> codeInBytesResult = await fileSystemService.GetFileAsync(tenantId, null,
                                new GetFileCommand { Key = url, IsPublic = false });
                        string refCode = codeInBytesResult.Value != null ? System.Text.Encoding.UTF8.GetString(codeInBytesResult.Value) : null;
                        dict.Add(url, refCode);
                    }
                    scripts.ToList().ForEach(s =>
                    {
                        if (!String.IsNullOrEmpty(s.ReferenceSourceCodeUrl))
                        {
                            s.ReferenceSourceCode = dict.GetValueOrDefault(s.ReferenceSourceCodeUrl);
                        }
                    });

                    return scripts?.Select(s => s.ToGraph()).ToArray();
                });

            Field(p => p.ClaimFactTemplateGraph, type: typeof(FactTemplateGraphType))
                .ResolveAsync(async context =>
                {
                    if (context.Source.ClaimFactTemplateGraph?.Id == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();

                    FactTemplate factTemplate = (await claimService.GetFactTemplatesAsync(tenantId, new List<string> { context.Source.ClaimFactTemplateGraph?.Id })).FirstOrDefault();

                    return FactTemplateGraph.ToGraph(factTemplate);
                });

            Field(p => p.Pricing, type: typeof(PricingGraphType))
                .Argument<ListGraphType<StringGraphType>>("discountCodes", "A list of the discount codes input by customers.")
                .Argument<ListGraphType<KeyValueInputGraphType>>("values", "A list of dynamic values to be sent to pricing engine.")
                .Argument<StringGraphType>("valuesJsonString", "A list of dynamic values to be sent to pricing engine in json string format.")
                .Argument<DateTimeOffsetGraphType>("pricingDate", "To calculate price at a certain date")
                .ResolveAsync(async context =>
                {
                    if (context.Source?.ProductId == null)
                        return null;

                    string tenantId = context.GetTenantIdFromToken();
                    string clientId = context.GetClientIdFromToken(isRequired: false);
                    string role = context.GetRoleFromToken(isRequired: false);

                    bool clientIdExists = (hideIndividualPolicyExtraFieldsSettings.Value?.ClientIds?.Any(x => x == clientId)).GetValueOrDefault();
                    var isDisallowIndividualPolicyExtraFieldsFeatureEnabled = await multiTenantFeatureManager.IsEnabled("DisallowIndividualPolicyExtraFields", tenantId);
                    if (isDisallowIndividualPolicyExtraFieldsFeatureEnabled && clientIdExists)
                        return null;

                    IEnumerable<string> discountCodes = context.GetArgument<IEnumerable<string>>("discountCodes")?.Where(d => d != null);

                    JToken token = null;
                    var factors = context.GetArgument<IEnumerable<KeyScalarValue>>("values")?.ToDictionary(x => x.Key, x => x.Value.GetValue());
                    if (factors != null)
                        token = JToken.FromObject(factors);
                    else
                    {
                        string factorsJsonString = context.GetArgument<string>("valuesJsonString");
                        if (factorsJsonString != null)
                        {
                            try
                            {
                                token = JToken.Parse(factorsJsonString);
                            }
                            catch (Exception e)
                            {
                                context.Errors.Add(new global::GraphQL.ExecutionError(e.Message, e));
                            }
                        }
                    }

                    DateTime pricingDate = context.GetArgument<DateTime?>("pricingDate") ?? DateTime.UtcNow;

                    var pricesLoader = accessor.Context.GetOrAddBatchLoader<ProductId, PriceDto>("GetPrices",
                        async p => (await pricingService.GetPricingsAsync(tenantId, new PriceFilter { ProductIds = p, Factors = token, DiscountCodes = discountCodes, PricingDate = pricingDate }))
                        .ToDictionary(x => x.ProductId));

                    PriceDto priceDto = await pricesLoader.LoadAsync(context.Source.ProductId);
                    return PricingGraph.ToGraph(priceDto, context.Source.ProductId);
                });

            Field(p => p.Underwriting, type: typeof(UnderwritingGraphType));

            Field(p => p.Illustrations, type: typeof(ListGraphType<NonNullGraphType<IllustrationGraphType>>))
                        .Argument<ListGraphType<KeyValueInputGraphType>>("values", "A list of dynamic values to be sent to pricing engine.")
                        .Argument<StringGraphType>("valuesJsonString", "A list of dynamic values to be sent to pricing engine in json string format.")
                        .ResolveAsync(async context =>
                        {
                            if (context.Source?.ProductId == null)
                                return Enumerable.Empty<IllustrationGraph>();

                            string tenantId = context.GetTenantIdFromToken();
                            string clientId = context.GetClientIdFromToken(isRequired: false);
                            string role = context.GetRoleFromToken(isRequired: false);

                            bool clientIdExists = (hideIndividualPolicyExtraFieldsSettings.Value?.ClientIds?.Any(x => x == clientId)).GetValueOrDefault();
                            var isDisallowIndividualPolicyExtraFieldsFeatureEnabled = await multiTenantFeatureManager.IsEnabled("DisallowIndividualPolicyExtraFields", tenantId);
                            if (isDisallowIndividualPolicyExtraFieldsFeatureEnabled && clientIdExists)
                                return null;

                            JToken token = null;
                            var factors = context.GetArgument<IEnumerable<KeyScalarValue>>("values")?.ToDictionary(x => x.Key, x => x.Value.GetValue());
                            if (factors != null)
                                token = JToken.FromObject(factors);
                            else
                            {
                                string factorsJsonString = context.GetArgument<string>("valuesJsonString");
                                if (factorsJsonString != null)
                                {
                                    try
                                    {
                                        token = JToken.Parse(factorsJsonString);
                                    }
                                    catch (Exception e)
                                    {
                                        context.Errors.Add(new global::GraphQL.ExecutionError(e.Message, e));
                                    }
                                }
                            }

                            IEnumerable<Illustration> illustrations = await productService.GetIllustrationsAsync(tenantId,
                        new ProductQuery
                        {
                            Where = new ProductWhere { Id_in = new List<ProductId> { context.Source.ProductId }, },
                            Factors = token
                        });

                            return illustrations.Select(IllustrationGraph.ToGraph);
                        });
            Field(p => p.TemplateRelationships, type: typeof(ListGraphType<TemplateRelationshipGraphType>), nullable: true)
                .Argument<TemplateRelationshipWhereGraphType>("where", "")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    TemplateRelationshipWhere where = context.GetArgument<TemplateRelationshipWhere>("where");

                    var templateRelationshipsLoader = accessor.Context
                        .GetOrAddBatchLoader<ProductGraph, IEnumerable<TemplateRelationshipGraph>>("GetTemplateRelationships",
                            async p =>
                            {
                                List<string> allTemplateRelationshipsIds = p
                                    .Where(i => i.TemplateRelationships != null)
                                    .SelectMany(i => i.TemplateRelationships)
                                    .Where(i => i != null)
                                    .Select(i => i.Id)
                                    .ToList();
                                IReadOnlyCollection<TemplateRelationshipGraph> allTemplateRelationships
                                    = (await templateService.GetTemplateRelationshipsAsync(tenantId,
                                        new TemplateRelationshipWhere
                                        {
                                            And = new List<TemplateRelationshipWhere> {
                                                new() { Id_in = allTemplateRelationshipsIds },
                                                where
                                            }
                                        }))
                                        ?.Select(TemplateRelationshipGraph.ToGraph)
                                        ?.ToArray() ?? Array.Empty<TemplateRelationshipGraph>();
                                return p.ToDictionary(
                                    prod => prod,
                                    prod => allTemplateRelationships.Where(scr => prod.TemplateRelationships?.Any(s => scr.Id == s.Id) == true));
                            });


                    IEnumerable<TemplateRelationshipGraph> templateRelationships = await templateRelationshipsLoader.LoadAsync(context.Source);

                    return templateRelationships?.ToArray();
                });

            Field(p => p.Events, type: typeof(ListGraphType<DetailedEventLogGraphType>))
                  .ResolveAsync(async context =>
                  {
                      string tenantId = context.GetTenantIdFromToken();

                      var dataLoader = accessor.Context.GetOrAddCollectionBatchLoader<string, DetailedEventLog>(
                          "GetProductEvents",
                          async ids => (await productService.GetEventsAsync(tenantId, new Proxies.Product.EventQuery { ProductIds = ids.Select(id => Proxies.Product.ProductId.FromString(id)).ToList() })).ToLookup(e => e.RelatedId)
                      );

                      IEnumerable<DetailedEventLog> logs = await dataLoader.LoadAsync(context.Source.ProductId.ToString());

                      return logs.Select(DetailedEventLogGraph.ToGraph);
                  });

            Field(p => p.ProductTreeId, nullable: true);

            Field(p => p.TermsAndConditionsTemplate, nullable: true, type: typeof(TemplateInterfaceGraphType)).ResolveAsync(async context =>
            {
                if (context.Source.TermsAndConditionsTemplate?.Id == null)
                    return null;

                string tenantId = context.GetTenantIdFromToken();
                IDataLoader<string, TemplateGraph> templateLoader = accessor.Context
                    .GetOrAddBatchLoader<string, TemplateGraph>("GetTemplates",
                        async p =>
                        {
                            IReadOnlyCollection<TemplateGraph> templates
                                = (await templateService.GetAsync(tenantId,
                                    new Domain.QueryArguments
                                    {
                                        Where = new TemplateWhere { Id_in = p }
                                    }))
                                    ?.Select(c => c != null ? TemplateGraph.ToGraph(c) : null)
                                    ?.ToArray() ?? Array.Empty<TemplateGraph>();
                            return templates.ToDictionary(i => i?.Id);
                        });

                return await templateLoader.LoadAsync(context.Source.TermsAndConditionsTemplate?.Id);
            });
            Field(p => p.TermsAndConditionsJacket, nullable: true, type: typeof(JacketGraphType)).ResolveAsync(async context =>
            {
                if (context.Source.TermsAndConditionsJacket?.Id == null)
                    return null;

                string tenantId = context.GetTenantIdFromToken();
                IDataLoader<string, JacketGraph> jacketLoader = accessor.Context
                    .GetOrAddBatchLoader<string, JacketGraph>("GetJackets",
                        async p =>
                        {
                            IReadOnlyCollection<JacketGraph> jackets
                                = (await productService.GetJacketsAsync(tenantId,
                                    new Domain.QueryArguments
                                    {
                                        Where = new JacketWhere { Id_in = p }
                                    }))
                                    ?.Select(c => c != null ? JacketGraph.ToGraph(c) : null)
                                    ?.ToArray() ?? Array.Empty<JacketGraph>();
                            return jackets.ToDictionary(i => i?.Id);
                        });

                return await jacketLoader.LoadAsync(context.Source.TermsAndConditionsJacket?.Id);
            });

            Field(p => p.RatingFactorsTable, nullable: true);
            Field(p => p.Segments, nullable: true);
            Field(p => p.PolicyIssuanceMethod, type: typeof(PolicyIssuanceMethodGraphType), nullable: true);
            Field(p => p.OfferValidityPeriod, nullable: true);
            Field(p => p.AllowCustomProduct, nullable: true);
            Field(p => p.UpdateTypes, type: typeof(ProductUpdatesGraphType), nullable: true);
            Field(p => p.AutoRenewal, nullable: false);
            Field(p => p.RenewalNotification, nullable: false);
        }
    }

    public class PriceWhereInputGraphType : InputObjectGraphType<PriceWhere>
    {
        public PriceWhereInputGraphType()
        {
            Name = "priceWhereInput";
            Description = "A price search filter";

            Field(f => f.Or, type: typeof(ListGraphType<PriceWhereInputGraphType>));
            Field(f => f.And, type: typeof(ListGraphType<PriceWhereInputGraphType>));

            Field(f => f.CurrencyCode_in, type: typeof(ListGraphType<CurrencyCodeEnumerationGraphType>));

            this.PopulateSystemWhereFields();
        }
    }

    public class PriceWhere : Where
    {
        public PriceWhere Or { get; set; }
        public PriceWhere And { get; set; }

        public IEnumerable<CurrencyCode> CurrencyCode_in { get; set; }
    }

    public class CreateProductInputGraphType : InputObjectGraphType<CreateProductInputGraph>
    {
        public CreateProductInputGraphType()
        {
            Name = "createProductInput";
            Description = "Input to create a new product";

            Field(p => p.ProductId, type: typeof(NonNullGraphType<ProductIdInputGraphType>));
            Field(p => p.InsurerId, nullable: true);
            Field(p => p.IssuerProductId, nullable: true);
            Field(p => p.UnderwritingRules, nullable: true);
            Field(p => p.BenefitInputs, type: typeof(ListGraphType<BenefitInputGraphType>));
            Field(p => p.RejectionSettings, type: typeof(RejectionSettingsInputGraphType));
            Field(p => p.LoadingSettings, type: typeof(LoadingSettingsInputGraphType));
            Field(p => p.ExclusionSettings, type: typeof(ExclusionSettingsInputGraphType));
            Field(p => p.ClaimSettings, type: typeof(ClaimSettingsInputGraphType));
            Field(p => p.Underwriting, type: typeof(UnderwritingInputGraphType));
            Field(p => p.Tags, type: typeof(ListGraphType<TagInputGraphType>));
            Field(p => p.Facts, type: typeof(ListGraphType<AddFactInputGraphType>));
            Field(p => p.LaunchPeriodStartDate, nullable: true);
            Field(p => p.LaunchPeriodEndDate, nullable: true);
            Field(p => p.ChangeEffectiveDate, nullable: true);
            Field(p => p.Status, nullable: true);
            Field(p => p.Representation, nullable: true);
            Field(p => p.Fields, nullable: true);
            Field(p => p.LifecycleStage, nullable: true);
            Field(p => p.ProductTreeId, nullable: true);
            Field(p => p.PolicyIssuanceMethod, type: typeof(PolicyIssuanceMethodGraphType), nullable: true);
            Field(p => p.OfferValidityPeriod, nullable: true);
            Field(p => p.AllowCustomProduct, nullable: true);
            Field(p => p.AutoRenewal, nullable: true);
            Field(p => p.RenewalNotification, nullable: true);
        }
    }

    public class UpdateProductInputGraphType : InputObjectGraphType<UpdateProductInputGraph>
    {
        public UpdateProductInputGraphType()
        {
            Name = "updateProductInput";
            Description = "Input to update a custom product";

            Field(p => p.InsurerId, nullable: true);
            Field(p => p.UnderwritingRules, nullable: true);
            Field(p => p.RejectionSettings, type: typeof(RejectionSettingsInputGraphType));
            Field(p => p.LoadingSettings, type: typeof(LoadingSettingsInputGraphType));
            Field(p => p.ExclusionSettings, type: typeof(ExclusionSettingsInputGraphType));
            Field(p => p.ClaimSettings, type: typeof(ClaimSettingsInputGraphType));
            Field(p => p.Underwriting, type: typeof(UnderwritingInputGraphType));
            Field(p => p.Representation, nullable: true);
            Field(p => p.IsRepresentationChanged, nullable: true);
            Field(p => p.RepresentationPatch, nullable: true);
            Field(p => p.Fields, nullable: true);
            Field(p => p.FieldsPatch, nullable: true);
            Field(p => p.IsFieldsChanged, nullable: true);
            Field(p => p.LifecycleStage, nullable: true);
            Field(p => p.LaunchPeriodStartDate, nullable: true);
            Field(p => p.LaunchPeriodEndDate, nullable: true);
            Field(p => p.ChangeEffectiveDate, nullable: true);
            Field(p => p.Status, nullable: true);
            Field(p => p.ProductTreeId, nullable: true);
            Field(p => p.PolicyIssuanceMethod, type: typeof(PolicyIssuanceMethodGraphType), nullable: true);
            Field(p => p.IsPolicyIssuanceMethodChanged, nullable: true);
            Field(p => p.IsOfferValidityPeriodChanged, nullable: true);
            Field(p => p.OfferValidityPeriod, nullable: true);
            Field(p => p.AllowCustomProduct, nullable: true);
            Field(p => p.IsAllowCustomProductChanged, nullable: true);
            Field(p => p.UpdateTypes, type: typeof(ProductUpdatesInputGraphType), nullable: true);
            Field(p => p.IsUpdateTypesChanged, nullable: true);
            Field(p => p.AutoRenewal, nullable: true);
            Field(p => p.RenewalNotification, nullable: true);
        }
    }

    public class ProductUpdatesInputGraphType : InputObjectGraphType<ProductUpdateTypes>
    {
        public ProductUpdatesInputGraphType()
        {
            Name = "updateTypesInput";
            Description = "The updateTypes input";

            Field(p => p.Plans, nullable: true);
            Field(p => p.BenefitsOrLimits, nullable: true);
            Field(p => p.Pricing, nullable: true);
            Field(p => p.OtherMinorChanges, nullable: true);
        }
    }

    public class ProductUpdatesGraphType : ObjectGraphType<ProductUpdateTypes>
    {
        public ProductUpdatesGraphType()
        {
            Name = "updateTypesPayload";
            Description = "The updateTypes payload";

            Field(p => p.Plans, nullable: true);
            Field(p => p.BenefitsOrLimits, nullable: true);
            Field(p => p.Pricing, nullable: true);
            Field(p => p.OtherMinorChanges, nullable: true);
        }
    }

    public class CloneProductInputGraphType : InputObjectGraphType<CloneProductInputGraph>
    {

        public CloneProductInputGraphType()
        {
            Name = "cloneProductInput";
            Description = "Input to clone a custom product";

            Field(p => p.CloneProductId, type: typeof(NonNullGraphType<ProductIdInputGraphType>));
            Field(p => p.IssuerProductId, nullable: true);
        }
    }

    public class UpdateProductInputGraph
    {
        public string InsurerId { get; set; }
        public bool IsInsurerIdChanged { get; set; }
        public string UnderwritingRules { get; set; }
        public bool IsUnderwritingRulesChanged { get; set; }
        public ProductSettingsToUpdate RejectionSettings { get; set; }
        public bool IsRejectionSettingsChanged { get; set; }
        public LoadingSettingsToUpdate LoadingSettings { get; set; }
        public bool IsLoadingSettingsChanged { get; set; }
        public ProductSettingsToUpdate ExclusionSettings { get; set; }
        public bool IsExclusionSettingsChanged { get; set; }
        public ClaimSettingsToUpdate ClaimSettings { get; set; }
        public bool IsClaimSettingsChanged { get; set; }
        public UnderwritingToUpdateInputGraph Underwriting { get; set; }
        public bool IsUnderwritingChanged { get; set; }
        public string LifecycleStage { get; set; }
        public bool IsLifecycleStageChanged { get; set; }
        public string Representation { get; set; }
        public string RepresentationPatch { get; set; }
        public bool IsRepresentationChanged { get; set; }

        public string Fields { get; set; }
        public string FieldsPatch { get; set; }
        public bool IsFieldsChanged { get; set; }

        public DateTime? LaunchPeriodStartDate { get; set; }
        public bool IsLaunchPeriodStartDateChanged { get; set; }
        public DateTime? LaunchPeriodEndDate { get; set; }
        public bool IsLaunchPeriodEndDateChanged { get; set; }
        public DateTime? ChangeEffectiveDate { get; set; }
        public bool IsChangeEffectiveDateChanged { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public string ProductTreeId { get; set; }
        public bool IsProductTreeIdChanged { get; set; }
        public PolicyIssuanceMethod? PolicyIssuanceMethod { get; set; }
        public bool IsPolicyIssuanceMethodChanged { get; set; }
        public bool IsOfferValidityPeriodChanged { get; set; }
        public long? OfferValidityPeriod { get; set; }
        public bool? AllowCustomProduct { get; set; }
        public bool IsAllowCustomProductChanged { get; set; }
        public ProductUpdateTypes UpdateTypes { get; set; }
        public bool IsUpdateTypesChanged { get; set; }
        public bool? AutoRenewal { get; set; }
        public bool? RenewalNotification { get; set; }
    }

    public class CloneProductInputGraph
    {
        public ProductId ProductId { get; set; }
        public ProductId CloneProductId { get; set; }
        public string IssuerProductId { get; set; }
    }

    public class UnderwritingToUpdateInputGraph
    {
        public bool IsSourceTypeChanged { get; set; }
        public string SourceType { get; set; }

        public bool IsJsonLogicRulesChanged { get; set; } // only for `jsonLogic`
        public string JsonLogicRules { get; set; } // only for `jsonLogic`

        public bool IsExcelPathChanged { get; set; } // only for `excel`
        public string ExcelPath { get; set; } // only for `excel`

        public bool IsExcelRulesChanged { get; set; } // only for `excel`
        public ExcelRules ExcelRules { get; set; } // only for `excel`
    }

    public class ProductGraph : SystemObjectGraph
    {
        public ProductId ProductId { get; set; }
        public ProductTypeGraph Type { get; set; }
        public string TenantId { get; set; }
        public string Slug { get; set; }
        public InsurerGraph Insurer { get; set; }
        public string InsurerId { get; set; }
        public string IssuerProductId { get; set; }

        public string BrochureUrl { get; set; }
        public string ApplicationFormUrl { get; set; }
        public string TermsAndConditionsUrl { get; set; }

        public string Name { get; set; }
        public string Description { get; set; }
        public string ImportantNotes { get; set; }
        public string MajorExclusions { get; set; }
        public string PremiumTableUrl { get; set; }

        public string CheckoutConfigJson { get; set; }
        public string UnderwritingRulesJson { get; set; }
        public string EnrollmentUrl { get; set; }

        public FactTemplateGraph ClaimFactTemplateGraph { get; set; }
        public PricingGraph Pricing { get; set; }
        public IEnumerable<BenefitGraph> Benefits { get; set; }
        public IEnumerable<BenefitGraph2> BenefitGraph { get; set; }
        public ProductSettingsGraph RejectionSettings { get; set; }

        public LoadingSettingsGraph LoadingSettings { get; set; }
        public ProductSettingsGraph ExclusionSettings { get; set; }

        public ClaimSettingsGraph ClaimSettings { get; set; }


        public IEnumerable<TagGraph> Tags { get; set; }
        public IEnumerable<FactGraph> Facts { get; set; }
        public IEnumerable<InternalReviewGraph> InternalReviews { get; set; }
        public IEnumerable<ScoreGraph> Scores { get; set; }

        public DateTime? LaunchPeriodStartDate { get; set; }
        public DateTime? LaunchPeriodEndDate { get; set; }
        public DateTime? ChangeEffectiveDate { get; set; }
        public string Status { get; set; }

        public UnderwritingGraph Underwriting { get; set; }
        public IEnumerable<UnderwritingVariable> UnderwritingVariables { get; set; }

        public IEnumerable<IllustrationGraph> Illustrations { get; set; }

        public string LifecycleStage { get; set; }

        /// <summary>
        /// Visual tree or any other JSON-as-set-of-rules product representation
        /// </summary>
        public string Representation { get; set; }
        public string Fields { get; set; }

        public IReadOnlyCollection<ScriptGraph> Scripts { get; set; }

        public IReadOnlyCollection<TemplateRelationshipGraph> TemplateRelationships { get; set; }

        public IEnumerable<DetailedEventLogGraph> Events { get; set; }
        public string ProductTreeId { get; set; }
        public TemplateGraph TermsAndConditionsTemplate { get; set; }
        public JacketGraph TermsAndConditionsJacket { get; set; }
        public string RatingFactorsTable { get; set; }
        public string Segments { get; set; }
        public PolicyIssuanceMethod? PolicyIssuanceMethod { get; set; }
        public TimeSpan? OfferValidityPeriod { get; set; }
        public bool? AllowCustomProduct { get; set; }
        public ProductUpdateTypes UpdateTypes { get; set; }
        public bool AutoRenewal { get; set; }
        public bool RenewalNotification { get; set; }
    }

    public class IllustrationGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public IEnumerable<Point> Points { get; set; }

        public static IllustrationGraph ToGraph(Illustration domain) =>
            domain != null
            ? new IllustrationGraph
            {
                Id = domain.Id,
                Points = domain.Points
            } : null;
    }

    public class PointGraphType : ObjectGraphType<Point>
    {
        public PointGraphType()
        {
            Name = "point";

            Field(p => p.X);
            Field(p => p.Y);
        }
    }

    public class IllustrationGraphType : ObjectGraphType<IllustrationGraph>
    {
        public IllustrationGraphType()
        {
            Name = "illustration";

            Field(i => i.Id);
            Field(i => i.Name, nullable: true);
            Field(i => i.Description, nullable: true);
            Field(i => i.Points, type: typeof(ListGraphType<NonNullGraphType<PointGraphType>>));
        }
    }

    public class UnderwritingGraph
    {
        public ProductId ProductId { get; set; } //Note: only for mapping to use in dataLoader
        public string SourceType { get; set; }
        public List<UnderwritingVariable> Variables { get; set; }

        public string JsonLogicRules { get; set; } // only for `jsonLogic`

        public string ExcelPath { get; set; } // only for `excel`
        public ExcelRules ExcelRules { get; set; } // only for `excel`

        public string JsonSchema { get; set; }

        public static UnderwritingGraph ToGraph(ProductId productId, Underwriting underwriting) =>
            new()
            {
                ProductId = productId,
                SourceType = underwriting.SourceType,
                Variables = underwriting.Variables,
                ExcelPath = underwriting.ExcelPath,
                ExcelRules = underwriting.ExcelRules,
                JsonLogicRules = underwriting.JsonLogicRules?.ToString()
            };
    }

    public class UnderwritingGraphType : ObjectGraphType<UnderwritingGraph>
    {
        public UnderwritingGraphType(
            IDataLoaderContextAccessor accessor,
            IProductService productService)
        {
            Name = "underwriting";

            Field(u => u.SourceType, nullable: true);
            Field(u => u.Variables, type: typeof(ListGraphType<UnderwritingVariableGraphType>));
            Field(u => u.JsonLogicRules, nullable: true)
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var dataLoader = accessor.Context.GetOrAddBatchLoader<ProductId, ProductUnderwritingJsonLogicRules>("GetProductUnderwritingJsonLogicRules",
                        async i => (await productService.GetUnderwrittingJsonLogicRulesAsync(tenantId, new ProductWhere { Id_in = i.ToList() })).ToDictionary(e => e.Id)
                    );

                    ProductUnderwritingJsonLogicRules jsonLogicRules = await dataLoader.LoadAsync(context.Source.ProductId);

                    return jsonLogicRules?.JsonLogicRules?.ToString(Newtonsoft.Json.Formatting.None);
                });
            Field(u => u.ExcelPath, nullable: true);
            Field(u => u.ExcelRules, type: typeof(ExcelRulesGraphType));
            Field(u => u.JsonSchema, nullable: true)
                 .Argument<ListGraphType<KeyValueInputGraphType>>("values", "A list of dynamic values to be sent to the underwriting engine.")
                 .ResolveAsync(async context =>
                 {
                     string tenantId = context.GetTenantIdFromToken();

                     JToken token = null;
                     var factors = context.GetArgument<IEnumerable<KeyScalarValue>>("values")?.ToDictionary(x => x.Key, x => x.Value.GetValue());
                     if (factors != null)
                         token = JToken.FromObject(factors);

                     var dataLoader = accessor.Context.GetOrAddBatchLoader<ProductUnderwritingJsonSchemaKey, ProductUnderwritingJsonSchema>("GetProductUnderwritingJsonSchemas",
                         async i => (await productService.GetUnderwrittingJsonSchemasAsync(tenantId, new ProductUnderwritingJsonSchemaQuery { Keys = i.ToList(), Factors = token })).ToDictionary(e => new ProductUnderwritingJsonSchemaKey { UnderwritingEngineId = e.UnderwritingEngineId, ProductId = e.ProductId })
                     );

                     ProductUnderwritingJsonSchema jsonSchema = await dataLoader.LoadAsync(new ProductUnderwritingJsonSchemaKey { UnderwritingEngineId = context.Source.SourceType, ProductId = context.Source.ProductId });

                     return jsonSchema?.JsonSchema;
                 });
        }
    }

    public class UnderwritingInputGraphType : InputObjectGraphType<UnderwritingInputGraph>
    {
        public UnderwritingInputGraphType()
        {
            Name = "underwritingInput";

            Field(u => u.SourceType, nullable: true);
            Field(u => u.JsonLogicRules, nullable: true);
            Field(u => u.ExcelPath, nullable: true);
            Field(u => u.ExcelRules, type: typeof(ExcelRulesInputGraphType));
        }
    }

    public class ExcelRulesGraphType : ObjectGraphType<ExcelRules>
    {
        public ExcelRulesGraphType()
        {
            Name = "excelRule";

            Field(e => e.ResultCell, type: typeof(ResultCellGraphType));
        }
    }

    public class ExcelRulesInputGraphType : InputObjectGraphType<ExcelRules>
    {
        public ExcelRulesInputGraphType()
        {
            Name = "excelRuleInput";

            Field(e => e.ResultCell, type: typeof(ResultCellInputGraphType));
        }
    }

    public class ResultCellGraphType : ObjectGraphType<ExcelCell>
    {
        public ResultCellGraphType()
        {
            Name = "resultCell";

            Field(r => r.RowIndex, nullable: true);
            Field(r => r.ColumnIndex, nullable: true);
            Field(r => r.Type, type: typeof(CellTypeEnumerationGraphType), nullable: true);
        }
    }

    public class ResultCellInputGraphType : InputObjectGraphType<ExcelCell>
    {
        public ResultCellInputGraphType()
        {
            Name = "resultCellInput";

            Field(r => r.RowIndex, nullable: true);
            Field(r => r.ColumnIndex, nullable: true);
            Field(r => r.Type, type: typeof(CellTypeEnumerationGraphType));
        }
    }

    public class CellTypeEnumerationGraphType : EnumerationGraphType<CellType>
    {
        public CellTypeEnumerationGraphType()
        {
            Name = "cellType";
        }
    }

    public class InsurerGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string DetailedName { get; set; }
        public InsurerLogoUrlsGraph LogoUrls { get; set; }
        public string TenantId { get; set; }

        public static InsurerGraph ToGraph(Insurer i) =>
            i == null
                ? null
                : new InsurerGraph
                {
                    Id = i.Id,
                    TenantId = i.TenantId,
                    LogoUrls = new InsurerLogoUrlsGraph
                    {
                        TypeA = i.LogoUrls?.TypeA,
                        TypeB = i.LogoUrls?.TypeB,
                        TypeC = i.LogoUrls?.TypeC,
                        TypeD = i.LogoUrls?.TypeD,
                        TypeE = i.LogoUrls?.TypeE
                    }
                }.PopulateSystemGraphFields(i);
    }

    public class TagGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Type { get; set; }

        public static TagGraph ToGraph(Tag domain) =>
            domain == null
                ? null
                : new TagGraph
                {
                    Id = domain.Id,
                    Type = domain.Type
                };
    }

    public class TagGraphType : ObjectGraphType<TagGraph>
    {
        public TagGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService)
        {
            Name = "tag";

            Field(t => t.Id);
            Field(t => t.Type, nullable: true);
            Field(t => t.Name, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"tags-{context.Source.Id}-name"));
            Field(t => t.Description, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"tags-{context.Source.Id}-description"));
        }
    }

    public class TagInputGraphType : InputObjectGraphType<TagGraph>
    {
        public TagInputGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService)
        {
            Name = "tagInput";

            Field(t => t.Type);
        }
    }

    public class InternalReviewGraph : SystemObjectGraph
    {
        public string Id { get; set; }
        public string Status { get; set; }
        public string Comment { get; set; }

        public static InternalReviewGraph ToGraph(InternalReview domain) =>
            domain == null
                ? null
                : new InternalReviewGraph
                {
                    Id = domain.Id,
                    Status = domain.Status,
                    Comment = domain.Comment
                }.PopulateSystemGraphFields(domain);
    }

    public class InternalReviewGraphType : ObjectGraphType<InternalReviewGraph>
    {
        public InternalReviewGraphType(
            IDataLoaderContextAccessor accessor,
            IAuthService authService,
            PermissionValidator permissionValidator)
        {
            Name = "internalReview";
            Description = "An internal review";

            Field(t => t.Id);
            Field(t => t.Status);
            Field(t => t.Comment);

            this.PopulateSystemGraphTypeFields(accessor, authService, permissionValidator);
        }
    }

    public class InternalReviewInputGraphType : InputObjectGraphType<InternalReviewGraph>
    {
        public InternalReviewInputGraphType()
        {
            Name = "internalReviewInput";

            Field(t => t.Status);
            Field(t => t.Comment);
        }
    }

    public class UpdateInternalReviewInputGraph
    {
        public string Id { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public string Comment { get; set; }
        public bool IsCommentChanged { get; set; }
    }

    public class UpdateInternalReviewInputGraphType : InputObjectGraphType<InternalReviewGraph>
    {
        public UpdateInternalReviewInputGraphType()
        {
            Name = "updateInternalReviewInput";
            Description = "An input for updating an internal review of a product";

            Field(t => t.Id);
            Field(t => t.Status, nullable: true);
            Field(t => t.Comment, nullable: true);
        }
    }

    public class InsurerLogoUrlsGraph
    {
        public string TypeA { get; set; }
        public string TypeB { get; set; }
        public string TypeC { get; set; }
        public string TypeD { get; set; }
        public string TypeE { get; set; }
    }

    public class BenefitInputGraphType : InputObjectGraphType<BenefitInputGraph>
    {
        public BenefitInputGraphType()
        {
            Name = "benefitInput";
            Description = "an input for a benefit";

            Field(b => b.TypeId);
            Field(b => b.ParentTypeId, nullable: true);
            Field(b => b.Value, type: typeof(ScalarValueInputGraphType));
            Field(b => b.ValueJsonString, nullable: true);
            Field(b => b.Condition, type: typeof(ConditionInputGraphType));
            Field(b => b.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(b => b.ParentOptionKeys, type: typeof(ListGraphType<StringGraphType>));
            Field(b => b.OptionKey, nullable: true);
            Field(b => b.IsValueInput, nullable: true);
        }
    }

    public class UpdateBenefitInputGraphType : InputObjectGraphType<BenefitInputGraph>
    {
        public UpdateBenefitInputGraphType()
        {
            Name = "updateBenefitInput";
            Description = "an input for a benefit";

            Field(b => b.ParentTypeId, nullable: true);
            Field(b => b.ParentOptionKeys, type: typeof(ListGraphType<StringGraphType>));
            Field(b => b.Value, type: typeof(ScalarValueInputGraphType));
            Field(b => b.ValueJsonString, nullable: true);
            Field(b => b.Condition, type: typeof(ConditionInputGraphType));
            Field(b => b.CurrencyCode, type: typeof(CurrencyCodeEnumerationGraphType));
            Field(b => b.IsValueInput, nullable: true);
        }
    }

    public class BenefitCommandBatchInputGraph
    {
        public List<BenefitInputGraph> AddBenefitInputs { get; set; }
        public List<BenefitInputGraph> UpdateBenefitInputs { get; set; }
        public List<RemoveBenefitCommand> RemoveBenefitInputs { get; set; }
    }

    public class BenefitCommandBatchInputGraphType : InputObjectGraphType<BenefitCommandBatchInputGraph>
    {
        public BenefitCommandBatchInputGraphType()
        {
            Name = "benefitBatchInput";

            Field(b => b.AddBenefitInputs, type: typeof(ListGraphType<BenefitInputGraphType>));
            Field(b => b.UpdateBenefitInputs, type: typeof(ListGraphType<BenefitInputGraphType>));
            Field(b => b.RemoveBenefitInputs, type: typeof(ListGraphType<RemoveBenefitCommandInputGraphType>));
        }
    }

    public class RemoveBenefitCommandInputGraphType : InputObjectGraphType<RemoveBenefitCommand>
    {
        public RemoveBenefitCommandInputGraphType()
        {
            Name = "removeBenefitInput";

            Field(b => b.TypeId);
            Field(b => b.OptionKey, nullable: true);
        }
    }


    public class BenefitInputGraph
    {
        public string TypeId { get; set; }
        public string ParentTypeId { get; set; }
        public List<string> ParentOptionKeys { get; set; }
        public string OptionKey { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public ScalarValue Value { get; set; }
        public string ValueJsonString { get; set; }
        public ConditionGraph Condition { get; set; }
        public bool IsValueInput { get; set; }

        public AddBenefitCommand ToAddBenefitCommand(string loginId) =>
            new()
            {
                TypeId = TypeId,
                ParentTypeId = ParentTypeId,
                ParentOptionKeys = ParentOptionKeys,
                Value = Value?.GetValue() != null ? JToken.FromObject(Value?.GetValue()) : (ValueJsonString != null ? JsonConvert.DeserializeObject<JToken>(ValueJsonString) : null),
                OptionKey = OptionKey,
                CurrencyCode = CurrencyCode,
                Condition = Condition != null
                                ? new Condition
                                {
                                    Type = Condition.Type,
                                    JsonLogicRule = Condition.JsonLogicRuleString != null
                                    ? JObject.Parse(Condition.JsonLogicRuleString)
                                    : null
                                } : null,
                AddedById = loginId,
                IsValueInput = IsValueInput
            };

        public UpdateBenefitCommand ToUpdateBenefitCommand(string loginId)
        {
            IDictionary<string, object> dict = Tools.ToDictionary<object>(this);
            if (Value != null)
                dict["Value"] = Value != null ? JToken.FromObject(Value?.GetValue()) : null;
            else if (ValueJsonString != null)
                dict["Value"] = JsonConvert.DeserializeObject<JToken>(ValueJsonString);

            if (Condition?.JsonLogicRuleString != null)
            {
                IDictionary<string, object> condition = Tools.ToDictionary<object>(dict["Condition"]);
                condition["JsonLogicRule"] = JObject.Parse(Condition.JsonLogicRuleString);
                dict["Condition"] = condition;
            }

            UpdateBenefitCommand command = dict.ToUpdateCommand<UpdateBenefitCommand>();

            command.ModifiedById = loginId;

            return command;
        }
    }

    public class ConditionInputGraphType : InputObjectGraphType<ConditionGraph>
    {
        public ConditionInputGraphType()
        {
            Name = "conditionInput";

            Field(c => c.Type, nullable: true);
            Field(c => c.JsonLogicRuleString, nullable: true);
        }
    }

    public class BenefitGraph
    {
        public string TypeId { get; set; }
        public string ParentTypeId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Value { get; set; }
        public ScalarValue Value2 { get; set; }
        public bool IsValueInput { get; set; }
        public string DetailedValue { get; set; }

        public string ParentOptionKey { get; set; }
        public IEnumerable<string> ParentOptionKeys { get; set; }
        public string OptionKey { get; set; }

        public IEnumerable<BenefitOptionGraph> Options { get; set; }
        public JToken RawData { get; set; }

        public bool IsOptional { get; set; }
        public ProductId ProductId { get; set; } // used only for references
        public CurrencyCode CurrencyCode { get; set; }

        public ConditionGraph Condition { get; set; }

        public IEnumerable<BenefitCategoryGraph> Categories { get; set; }
    }

    public class ConditionGraph
    {
        public string Type { get; set; }
        public string JsonLogicRuleString { get; set; }

        public static ConditionGraph ToGraph(Condition condition) =>
            condition != null ? new ConditionGraph
            {
                Type = condition.Type,
                JsonLogicRuleString = condition.JsonLogicRule?.ToString()
            } : null;
    }

    public class ConditionGraphType : ObjectGraphType<ConditionGraph>
    {
        public ConditionGraphType()
        {
            Name = "condition";

            Field(c => c.Type, nullable: true);
            Field(c => c.JsonLogicRuleString, nullable: true);
        }
    }

    public class BenefitOptionGraph : SystemObjectGraph
    {
        public string Name { get; set; }
        public string Key { get; set; }
        public ScalarValue Value { get; set; }
        public string DetailedValue { get; set; }
        public object RawData { get; set; }
        public string FormattedValue { get; set; }
        public ProductId ProductId { get; set; } // used only for references
        public string BenefitTypeId { get; set; } // used only for references
        public CurrencyCode CurrencyCode { get; set; }
    }

    public class BenefitInfoGraph
    {
        public string TypeId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }

        public string Type { get; set; }
        public string[] Categories { get; set; }
    }

    public class BenefitCategoryGraph
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public IEnumerable<string> BenefitTypeIds { get; set; }
        public string Description { get; set; }
    }

    public class BenefitCategoriesPerTypeGraph
    {
        public string ProductType { get; set; }
        public List<BenefitCategoryGraph> Categories { get; set; }
    }

    public class ClaimSettingsGraph
    {
        public ProductSettingsGraph RejectionSettings { get; set; }
        public ProductSettingsGraph DiagnosisSettings { get; set; }
        public ProductSettingsGraph OperationSettings { get; set; }
        public ProviderSettingsGraph ProviderSettings { get; set; }
    }

    public class ProductSettingsGraph
    {
        public List<CodeNameGraph> CodeNames { get; set; }
    }

    public class LoadingSettingsGraph : ProductSettingsGraph
    {
        public decimal? MaxLoadingMultiplier { get; set; }
    }

    public class ProviderSettingsGraph
    {
        public List<EntityGraph> Providers { get; set; }
    }

    public class RejectionSettingsGraphType : ObjectGraphType<ProductSettingsGraph>
    {
        public RejectionSettingsGraphType()
        {
            Name = "rejectionSettings";
            Description = "The product rejection settings";

            Field(l => l.CodeNames, type: typeof(ListGraphType<RejectionCodeNameGraphType>));
        }
    }

    public class RejectionSettingsInputGraphType : InputObjectGraphType<ProductSettings>
    {
        public RejectionSettingsInputGraphType()
        {
            Name = "rejectionSettingsInput";
            Description = "The product rejection settings input";

            Field(l => l.Codes, nullable: true);
        }
    }

    public class RejectionCodeNameGraphType : ObjectGraphType<CodeNameGraph>
    {
        public RejectionCodeNameGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService)
        {
            Name = "rejectionCodeName";

            Field(l => l.Code);
            Field(l => l.Name, nullable: true).ResolveAsync(context =>
            context.GetL10nAsync(accessor, l10nService, $"codes-rejections-{context.Source.Code}-name", $"products-{context.Source.ProductId}-rejections-codes-{context.Source.Code}-name"));
        }
    }

    public class LoadingSettingsGraphType : ObjectGraphType<LoadingSettingsGraph>
    {
        public LoadingSettingsGraphType()
        {
            Name = "loadingSettings";
            Description = "The product loading settings";

            Field(l => l.CodeNames, type: typeof(ListGraphType<LoadingCodeNameGraphType>));
            Field(l => l.MaxLoadingMultiplier, nullable: true);
        }
    }

    public class LoadingSettingsInputGraphType : InputObjectGraphType<LoadingSettings>
    {
        public LoadingSettingsInputGraphType()
        {
            Name = "loadingSettingsInput";
            Description = "The product loading settings input";

            Field(l => l.Codes, nullable: true);
            Field(l => l.MaxLoadingMultiplier, nullable: true);
        }
    }

    public class LoadingCodeNameGraphType : ObjectGraphType<CodeNameGraph>
    {
        public LoadingCodeNameGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService)
        {
            Name = "loadingCodeName";

            Field(l => l.Code, nullable: true);
            Field(l => l.Name, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"codes-loadings-{context.Source.Code}-name", $"products-{context.Source.ProductId}-loadings-codes-{context.Source.Code}-name"));
        }
    }

    public class ExclusionSettingsGraphType : ObjectGraphType<ProductSettingsGraph>
    {
        public ExclusionSettingsGraphType()
        {
            Name = "exclusionSettings";
            Description = "The product exclusion settings";

            Field(l => l.CodeNames, type: typeof(ListGraphType<ExclusionCodeNameGraphType>));
        }
    }

    public class ExclusionSettingsInputGraphType : InputObjectGraphType<ProductSettings>
    {
        public ExclusionSettingsInputGraphType()
        {
            Name = "exclusionSettingsInput";
            Description = "The product exclusion settings input";

            Field(l => l.Codes, nullable: true);
        }
    }

    public class ExclusionCodeNameGraphType : ObjectGraphType<CodeNameGraph>
    {
        public ExclusionCodeNameGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService)
        {
            Name = "exclusionCodeName";

            Field(l => l.Code);
            Field(l => l.Name, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"codes-exclusions-{context.Source.Code}-name", $"products-{context.Source.ProductId}-exclusions-codes-{context.Source.Code}-name"));
        }
    }

    public class ClaimSettingsGraphType : ObjectGraphType<ClaimSettingsGraph>
    {
        public ClaimSettingsGraphType()
        {
            Name = "claimSettings";

            Field(s => s.RejectionSettings, type: typeof(RejectionSettingsGraphType));
            Field(s => s.DiagnosisSettings, type: typeof(DiagnosisSettingsGraphType));
            Field(s => s.OperationSettings, type: typeof(OperationSettingsGraphType));
            Field(s => s.ProviderSettings, type: typeof(ProviderSettingsGraphType));
        }
    }

    public class ClaimSettingsInputGraphType : InputObjectGraphType<ClaimSettingsGraph>
    {
        public ClaimSettingsInputGraphType()
        {
            Name = "claimSettingsInput";

            Field(s => s.RejectionSettings, type: typeof(RejectionSettingsInputGraphType));
            Field(s => s.DiagnosisSettings, type: typeof(DiagnosisSettingsInputGraphType));
            Field(s => s.OperationSettings, type: typeof(OperationSettingsInputGraphType));
            Field(s => s.ProviderSettings, type: typeof(ProviderSettingsInputGraphType));
        }
    }

    public class DiagnosisSettingsGraphType : ObjectGraphType<ProductSettingsGraph>
    {
        public DiagnosisSettingsGraphType()
        {
            Name = "diagnosisSettings";
            Description = "The product diagnosis settings";

            Field(l => l.CodeNames, type: typeof(ListGraphType<DiagnosisCodeNameGraphType>));
        }
    }

    public class DiagnosisSettingsInputGraphType : InputObjectGraphType<ProductSettings>
    {
        public DiagnosisSettingsInputGraphType()
        {
            Name = "diagnosisSettingsInput";
            Description = "The product diagnosis settings input";

            Field(l => l.Codes, nullable: true);
        }
    }

    public class DiagnosisCodeNameGraphType : ObjectGraphType<CodeNameGraph>
    {
        public DiagnosisCodeNameGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService)
        {
            Name = "diagnosisCodeName";

            Field(l => l.Code);
            Field(l => l.Name, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"codes-diagnoses-{context.Source.Code}-name"));
            Field(l => l.Description, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"codes-diagnoses-{context.Source.Code}-description"));
        }
    }

    public class OperationSettingsGraphType : ObjectGraphType<ProductSettingsGraph>
    {
        public OperationSettingsGraphType()
        {
            Name = "operationSettings";
            Description = "The product operation settings";

            Field(l => l.CodeNames, type: typeof(ListGraphType<OperationCodeNameGraphType>));
        }
    }

    public class OperationSettingsInputGraphType : InputObjectGraphType<ProductSettings>
    {
        public OperationSettingsInputGraphType()
        {
            Name = "operationSettingsInput";
            Description = "The product operation settings input";

            Field(l => l.Codes, nullable: true);
        }
    }

    public class OperationCodeNameGraphType : ObjectGraphType<CodeNameGraph>
    {
        public OperationCodeNameGraphType(IDataLoaderContextAccessor accessor, IL10nService l10nService)
        {
            Name = "operationCodeName";

            Field(l => l.Code);
            Field(l => l.Name, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"codes-operations-{context.Source.Code}-name"));
            Field(l => l.Description, nullable: true).ResolveAsync(context => context.GetL10nAsync(accessor, l10nService, $"codes-operations-{context.Source.Code}-description"));
        }
    }

    public class ProviderSettingsGraphType : ObjectGraphType<ProviderSettingsGraph>
    {
        public ProviderSettingsGraphType(
            IDataLoaderContextAccessor accessor,
            IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand> organizationService)
        {
            Name = "providerSettings";

            Field(s => s.Providers, type: typeof(ListGraphType<EntityInterfaceGraphType>))
                .ResolveAsync(async context =>
                {
                    IEnumerable<string> entityIds = context.Source.Providers?.Select(e => e.Id);
                    if (!entityIds?.Any() ?? true)
                        return new List<EntityGraph> { };

                    string tenantId = context.GetTenantIdFromToken();

                    var organizationLoader = accessor.Context.GetOrAddBatchLoader<string, Organization>("GetOrganizations",
                        ids => organizationService.GetDictionaryAsync(tenantId, new OrganizationWhere { Id_in = ids?.ToList() }));

                    IEnumerable<Organization> organizationDtos = await Task.WhenAll(entityIds?.Select(i => organizationLoader.LoadAsync(i)));

                    return organizationDtos?.Select(o => o?.ToGraph())?.ToList();
                });
        }
    }

    public class ProviderSettingsInputGraphType : InputObjectGraphType<ProviderSettings>
    {
        public ProviderSettingsInputGraphType()
        {
            Name = "providerSettingsInput";

            Field(s => s.EntityIds, nullable: true);
        }
    }

    public class UnderwritingInputGraph
    {
        public string SourceType { get; set; }

        public string JsonLogicRules { get; set; } // only for `jsonLogic`

        public string ExcelPath { get; set; } // only for `excel`
        public ExcelRules ExcelRules { get; set; } // only for `excel`
    }

    public class CreateProductInputGraph
    {
        public ProductId ProductId { get; set; }
        public string InsurerId { get; set; }
        public string IssuerProductId { get; set; } //ToDo: used for chubb, maybe need to prevent others from using it
        public string UnderwritingRules { get; set; }
        public ProductSettings RejectionSettings { get; set; }
        public LoadingSettings LoadingSettings { get; set; }
        public ProductSettings ExclusionSettings { get; set; }
        public ClaimSettings ClaimSettings { get; set; }
        public IEnumerable<BenefitInputGraph> BenefitInputs { get; set; }
        public UnderwritingInputGraph Underwriting { get; set; }
        public IEnumerable<AttachmentGraph> Attachments { get; set; }
        public IEnumerable<Tag> Tags { get; set; }
        public List<AddFactInputGraph> Facts { get; set; } = new();
        public DateTime? LaunchPeriodStartDate { get; set; }
        public DateTime? LaunchPeriodEndDate { get; set; }
        public DateTime? ChangeEffectiveDate { get; set; }
        public string Status { get; set; }
        public string LifecycleStage { get; set; }
        public string Representation { get; set; }
        public string Fields { get; set; }
        public string ProductTreeId { get; set; }
        public PolicyIssuanceMethod? PolicyIssuanceMethod { get; set; }
        public long? OfferValidityPeriod { get; set; }
        public bool? AllowCustomProduct { get; set; }
        public bool? AutoRenewal { get; set; }
        public bool? RenewalNotification { get; set; }
    }

    public class MigrateProductsInputGraph
    {
        public IEnumerable<CreateProductInputGraph> ProductInputs { get; set; }
    }

    public class MigrateProductsInputGraphType : InputObjectGraphType<MigrateProductsInputGraph>
    {
        public MigrateProductsInputGraphType()
        {
            Name = "migrateProductsInput";

            Field(p => p.ProductInputs, type: typeof(ListGraphType<CreateProductInputGraphType>));
        }
    }

    public class UnderwritingVariableGraphType : ObjectGraphType<UnderwritingVariable>
    {
        public UnderwritingVariableGraphType()
        {
            Name = "underwritingVariable";

            Field(v => v.Id, nullable: true);
            Field(v => v.Description, nullable: true);
            Field(v => v.Name, nullable: true);
            Field(v => v.JsonSchemaValidation, nullable: true);
        }
    }

    public class AddUnderwritingVariableInputGraphType : InputObjectGraphType<UpdateUnderwritingVariableCommand>
    {
        public AddUnderwritingVariableInputGraphType()
        {
            Name = "addUnderwritingVariableInput";

            Field(v => v.Name, nullable: true);
            Field(v => v.Description, nullable: true);
            Field(v => v.JsonSchemaValidation, nullable: true);
        }
    }

    public class UpdateUnderwritingVariableInputGraphType : InputObjectGraphType<UpdateUnderwritingVariableCommand>
    {
        public UpdateUnderwritingVariableInputGraphType()
        {
            Name = "updateUnderwritingVariableInput";

            Field(v => v.Name, nullable: true);
            Field(v => v.Description, nullable: true);
            Field(v => v.JsonSchemaValidation, nullable: true);
        }
    }

    public class ValidateResultGraphType : ObjectGraphType<ValidateResult>
    {
        public ValidateResultGraphType()
        {
            Name = "validateProductResult";

            Field(v => v.ProductId, type: typeof(ProductIdGraphType));
            Field(v => v.Status);
            Field(v => v.Errors, type: typeof(ListGraphType<ErrorGraphType>));
        }
    }

    public class AttachmentInputGraphType : InputObjectGraphType<AddAttachmentCommand>
    {
        public AttachmentInputGraphType()
        {
            Name = "attachmentInput";

            Field(v => v.Path, nullable: true);
            Field(v => v.Type, nullable: true);
        }
    }

    public class AddScriptToProductInputGraphType : InputObjectGraphType<AddScriptToProductInput>
    {
        public AddScriptToProductInputGraphType()
        {
            Name = "addScriptToProductInput";
            Field(v => v.ProductId, type: typeof(NonNullGraphType<ProductIdInputGraphType>), nullable: false);
            Field(v => v.ScriptId, nullable: false);
        }
    }

    public class RemoveScriptFromProductInputGraphType : InputObjectGraphType<RemoveScriptFromProductInput>
    {
        public RemoveScriptFromProductInputGraphType()
        {
            Name = "removeScriptFromProductInput";
            Field(v => v.ProductId, type: typeof(NonNullGraphType<ProductIdInputGraphType>), nullable: false);
            Field(v => v.ScriptId, nullable: false);
        }
    }

    public sealed class AddTemplateRelationshipToProductInputGraphType : InputObjectGraphType<AddTemplateRelationshipToProductInput>
    {
        public AddTemplateRelationshipToProductInputGraphType()
        {
            Name = "addTemplateRelationshipToProductInput";
            Field(f => f.ProductId, type: typeof(ProductIdInputGraphType), nullable: false);
            Field(f => f.Action, nullable: false);
            Field(f => f.TemplateId, nullable: false);
        }
    }

    public sealed class RemoveTemplateRelationshipFromProductInputGraphType : InputObjectGraphType<RemoveTemplateRelationshipFromProductInput>
    {
        public RemoveTemplateRelationshipFromProductInputGraphType()
        {
            Name = "removeTemplateRelationshipFromProductInput";
            Field(f => f.ProductId, type: typeof(ProductIdInputGraphType), nullable: false);
            Field(f => f.TemplateRelationshipId, nullable: false);
        }
    }

    public class AddScriptToProductInput
    {
        [JsonRequired]
        public ProductId ProductId { get; set; }

        [JsonRequired]
        public string ScriptId { get; set; }
    }

    public class RemoveScriptFromProductInput
    {
        [JsonRequired]
        public ProductId ProductId { get; set; }

        [JsonRequired]
        public string ScriptId { get; set; }
    }

    public class AddTemplateRelationshipToProductInput
    {
        [JsonRequired]
        public ProductId ProductId { get; set; }

        [JsonRequired]
        public string Action { get; set; }

        [JsonRequired]
        public string TemplateId { get; set; }
    }

    public class RemoveTemplateRelationshipFromProductInput
    {
        [JsonRequired]
        public ProductId ProductId { get; set; }

        [JsonRequired]
        public string TemplateRelationshipId { get; set; }
    }

    public class TemplateRelationshipGraph
    {
        public string Id { get; set; }
        public TemplateGraph Template { get; set; }
        public string Action { get; set; }

        public static TemplateRelationshipGraph ToGraph(TemplateRelationship templateRelationship) =>
            new()
            {
                Id = templateRelationship.Id,
                Template = TemplateGraph.ToGraph(templateRelationship.Template),
                Action = templateRelationship.Action
            };
    }
}
