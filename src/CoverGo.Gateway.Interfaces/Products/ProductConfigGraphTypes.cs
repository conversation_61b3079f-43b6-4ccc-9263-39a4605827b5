﻿using CoverGo.Gateway.Domain.Products;
using GraphQL.Types;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Gateway.Interfaces.Products
{
    public class InitializeTenantProductsInputGraph
    {
        public IEnumerable<ProductConfigGraph> ProductConfigs { get; set; }
    }
    public class InitializeTenantProductsInputGraphType : InputObjectGraphType<InitializeTenantProductsInputGraph>
    {
        public InitializeTenantProductsInputGraphType()
        {
            Name = "initializeTenantProductConfigInput";
            Field(i => i.ProductConfigs, type: typeof(ListGraphType<ProductConfigInputGraphType>));
        }
    }

    public class ProductConfigGraph
    {
        public string Id { get; set; }
        public string ClientId { get; set; }
        public IEnumerable<DisplayedInsurersGraph> DisplayedInsurers { get; set; }
        
        public IEnumerable<DisplayedProductIdsGraph> DisplayedProductIds { get; set; }
        public IEnumerable<DisplayedProductsGraph> DisplayedProducts { get; set; }
        public IEnumerable<DisplayedBenefitsGraph> DisplayedBenefits { get; set; }
        public IEnumerable<string> DisplayedTypes { get; set; }
        public Meta Meta { get; set; } //ToDo: Not needed for chubb but add back for other tenants

        public static ProductConfigGraph ToGraph(ProductConfig domain)
            => domain == null
               ? null
               : new ProductConfigGraph
               {
                   Id = domain.Id,
                   ClientId = domain.ClientId,
                   DisplayedBenefits = domain.DisplayedBenefits?.Select(db => DisplayedBenefitsGraph.ToGraph(db)),
                   DisplayedInsurers = domain.DisplayedInsurers?.Select(di => DisplayedInsurersGraph.ToGraph(di)),
                   DisplayedProducts = domain.DisplayedProducts?.Select(dp => DisplayedProductsGraph.ToGraph(dp)),
                   DisplayedTypes = domain.DisplayedTypes
               };

        public static CreateProductConfigCommand ToCreateCommand(ProductConfigGraph graph)
          => graph == null
             ? null
             : new CreateProductConfigCommand
             {
                 ClientId = graph.ClientId,
                 DisplayedBenefits = graph.DisplayedBenefits.Select(db => DisplayedBenefitsGraph.ToDomain(db))?.ToDictionary(pair => pair.Key, pair => pair.Value),
                 DisplayedInsurers = graph.DisplayedInsurers.Select(di => DisplayedInsurersGraph.ToDomain(di))?.ToDictionary(pair => pair.Key, pair => pair.Value),
                 DisplayedProducts = graph.DisplayedProducts.Select(dp => DisplayedProductsGraph.ToDomain(dp))?.ToDictionary(pair => pair.Key, pair => pair.Value),
                 DisplayedTypes = graph.DisplayedTypes
             };
    }
    public class ProductConfigInputGraphType : InputObjectGraphType<ProductConfigGraph>
    {
        public ProductConfigInputGraphType()
        {
            Name = "productConfigInput";
            Field(p => p.ClientId);
            Field(p => p.DisplayedInsurers, type: typeof(ListGraphType<DisplayedInsurersInputGraphType>));
            Field(p => p.DisplayedProductIds, type: typeof(ListGraphType<DisplayedProductsInputGraphType>)).DeprecationReason("use displayedProducts");
            Field(p => p.DisplayedProducts, type: typeof(ListGraphType<DisplayedProductsInputGraphType>));
            Field(p => p.DisplayedBenefits, type: typeof(ListGraphType<DisplayedBenefitsInputGraphType>));
            Field(p => p.DisplayedTypes, nullable: true);
        }
    }

    public class UpdateProductConfigInputGraphType : InputObjectGraphType<ProductConfigGraph>
    {
        public UpdateProductConfigInputGraphType()
        {
            Name = "updateProductConfigInput";
            Field(p => p.ClientId, nullable: true);
            Field(p => p.DisplayedInsurers, type: typeof(ListGraphType<DisplayedInsurersInputGraphType>));
            Field(p => p.DisplayedProducts, type: typeof(ListGraphType<DisplayedProductsInputGraphType>));
            Field(p => p.DisplayedBenefits, type: typeof(ListGraphType<DisplayedBenefitsInputGraphType>));
            Field(p => p.DisplayedTypes, nullable: true);
        }
    }

    public class DisplayedInsurersGraph
    {
        public string ProductType { get; set; }
        public IEnumerable<string> InsurerIds { get; set; }

        public static DisplayedInsurersGraph ToGraph(KeyValuePair<string, IEnumerable<string>> domain)
           => new()
           {
               ProductType = domain.Key,
               InsurerIds = domain.Value
           };

        public static KeyValuePair<string, IEnumerable<string>> ToDomain(DisplayedInsurersGraph graph)
            => new(
                graph.ProductType,
                graph.InsurerIds
            );
    }
    public class DisplayedInsurersInputGraphType : InputObjectGraphType<DisplayedInsurersGraph>
    {
        public DisplayedInsurersInputGraphType()
        {
            Name = "displayedInsurersInput";

            Field(i => i.ProductType);
            Field(i => i.InsurerIds);
        }
    }
    public class DisplayedInsurersGraphType : ObjectGraphType<DisplayedInsurersGraph>
    {
        public DisplayedInsurersGraphType()
        {
            Name = "displayedInsurers";

            Field(i => i.ProductType);
            Field(i => i.InsurerIds);
        }
    }

    
    public class DisplayedProductIdsGraph
    {
        public string TenantId { get; set; }
        public IEnumerable<ProductId> ProductIds { get; set; }

        public static DisplayedProductIdsGraph ToGraph(KeyValuePair<string, IEnumerable<ProductId>> domain)
            => new()
            {
                TenantId = domain.Key,
                ProductIds = domain.Value
            };
    }
    
    public class DisplayedProductIdsInputGraphType : InputObjectGraphType<DisplayedProductIdsGraph>
    {
        public DisplayedProductIdsInputGraphType()
        {
            Name = "displayedProductIdsInput";

            Field(p => p.TenantId);
            Field(p => p.ProductIds, type: typeof(NonNullGraphType<ListGraphType<ProductIdInputGraphType>>));
        }
    }

    public class DisplayedProductsGraph
    {
        public string Type { get; set; }
        public IEnumerable<ProductId> ProductIds { get; set; }

        public static DisplayedProductsGraph ToGraph(KeyValuePair<string, IEnumerable<ProductId>> domain)
           => new()
           {
               Type = domain.Key,
               ProductIds = domain.Value
           };

        public static KeyValuePair<string, IEnumerable<ProductId>> ToDomain(DisplayedProductsGraph graph)
           => new(
               graph.Type,
               graph.ProductIds
           );
    }
    public class DisplayedProductsInputGraphType : InputObjectGraphType<DisplayedProductsGraph>
    {
        public DisplayedProductsInputGraphType()
        {
            Name = "displayedProductsInput";

            Field(p => p.Type);
            Field(p => p.ProductIds, type: typeof(NonNullGraphType<ListGraphType<ProductIdInputGraphType>>));
        }
    }
    public class DisplayedProductsGraphType : ObjectGraphType<DisplayedProductsGraph>
    {
        public DisplayedProductsGraphType()
        {
            Name = "displayedProducts";

            Field(p => p.Type);
            Field(p => p.ProductIds, type: typeof(NonNullGraphType<ListGraphType<ProductIdGraphType>>));
        }
    }

    public class DisplayedBenefitsGraph
    {
        public string ProductType { get; set; }
        public IEnumerable<DisplayedBenefitCategoryGraph> BenefitCategories { get; set; }

        public static DisplayedBenefitsGraph ToGraph(KeyValuePair<string, Dictionary<string, IEnumerable<string>>> domain)
            => new()
            {
                ProductType = domain.Key,
                BenefitCategories = domain.Value?.Select(kv => DisplayedBenefitCategoryGraph.ToGraph(kv))
            };

        public static KeyValuePair<string, Dictionary<string, IEnumerable<string>>> ToDomain(DisplayedBenefitsGraph graph)
            => new(
                graph.ProductType,
                graph.BenefitCategories?.Select(b => DisplayedBenefitCategoryGraph.ToDomain(b))?.ToDictionary(pair => pair.Key, pair => pair.Value)
            );
    }
    public class DisplayedBenefitsInputGraphType : InputObjectGraphType<DisplayedBenefitsGraph>
    {
        public DisplayedBenefitsInputGraphType()
        {
            Name = "displayedBenefitsInput";
            Field(b => b.ProductType);
            Field(b => b.BenefitCategories, type: typeof(NonNullGraphType<ListGraphType<DisplayedBenefitCategoryInputGraphType>>));
        }
    }
    public class DisplayedBenefitsGraphType : ObjectGraphType<DisplayedBenefitsGraph>
    {
        public DisplayedBenefitsGraphType()
        {
            Name = "displayedBenefits";
            Field(b => b.ProductType);
            Field(b => b.BenefitCategories, type: typeof(ListGraphType<DisplayedBenefitCategoryGraphType>));
        }
    }

    public class DisplayedBenefitCategoryGraph
    {
        public string Id { get; set; }
        public IEnumerable<string> TypeIds { get; set; }

        public static DisplayedBenefitCategoryGraph ToGraph(KeyValuePair<string, IEnumerable<string>> domain)
            => new()
            {
                Id = domain.Key,
                TypeIds = domain.Value
            };

        public static KeyValuePair<string, IEnumerable<string>> ToDomain(DisplayedBenefitCategoryGraph graph)
            => new(
                graph.Id,
                graph.TypeIds
            );
    }
    public class DisplayedBenefitCategoryInputGraphType : InputObjectGraphType<DisplayedBenefitCategoryGraph>
    {
        public DisplayedBenefitCategoryInputGraphType()
        {
            Name = "displayedBenefitCategoryInput";
            Field(c => c.Id);
            Field(c => c.TypeIds);
        }
    }
    public class DisplayedBenefitCategoryGraphType : ObjectGraphType<DisplayedBenefitCategoryGraph>
    {
        public DisplayedBenefitCategoryGraphType()
        {
            Name = "displayedBenefitCategory";
            Field(c => c.Id);
            Field(c => c.TypeIds);
        }
    }

    public class ProductConfigGraphType : ObjectGraphType<ProductConfigGraph>
    {
        public ProductConfigGraphType()
        {
            Name = "productConfig";
            Field(p => p.Id);
            Field(p => p.ClientId, nullable: true);
            Field(p => p.DisplayedInsurers, type: typeof(ListGraphType<DisplayedInsurersGraphType>));
            Field(p => p.DisplayedProducts, type: typeof(ListGraphType<DisplayedProductsGraphType>));
            Field(p => p.DisplayedBenefits, type: typeof(ListGraphType<DisplayedBenefitsGraphType>));
            Field(p => p.DisplayedTypes);
        }
    }
    public class ProductConfigWhereInputGraphType : InputObjectGraphType<ProductConfigWhere>
    {
        public ProductConfigWhereInputGraphType()
        {
            Name = "productConfigWhere";

            Field(p => p.Or, type: typeof(ListGraphType<ProductConfigWhereInputGraphType>));
            Field(p => p.And, type: typeof(ListGraphType<ProductConfigWhereInputGraphType>));
            Field(p => p.Id, nullable: true);
            Field(p => p.ClientId, nullable: true);

            this.PopulateSystemWhereFields();
        }
    }
}
