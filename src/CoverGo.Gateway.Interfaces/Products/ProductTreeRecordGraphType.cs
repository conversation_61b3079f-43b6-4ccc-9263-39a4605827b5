﻿using CoverGo.Gateway.Domain.Products;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces.Products
{
    public class ProductTreeRecordGraphType: ObjectGraphType<ProductTreeRecord>
    {
        public ProductTreeRecordGraphType()
        {
            Name = "productTreeRecord";
            Description = "A reference to record of product tree resolution";

            Field(r => r.Type);
            Field(r => r.RecordId);
        }
    }

    public class ProductTreeRecordInputGraphType : InputObjectGraphType<ProductTreeRecord>
    {
        public ProductTreeRecordInputGraphType()
        {
            Name = "productTreeRecordInput";
            Description = "A reference to record of product tree resolution";

            Field(r => r.Type);
            Field(r => r.RecordId);
        }
    }
}
