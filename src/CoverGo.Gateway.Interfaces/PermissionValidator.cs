﻿#nullable enable

using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using GraphQL.Types;
using GraphQL.Validation;
using Microsoft.Extensions.Logging;

namespace CoverGo.Gateway.Interfaces;

public class PermissionValidator
{
    private readonly LazyPermissionValidator _lazyPermissionValidator;
    private readonly ILogger<PermissionValidator> _logger;

    public PermissionValidator(LazyPermissionValidator lazyPermissionValidator,ILogger<PermissionValidator> logger)
    {
        _lazyPermissionValidator = lazyPermissionValidator;
        _logger = logger;
    }

    public async ValueTask Authorize<T>(ResolveFieldContext<T> context, PermissionRequest permissionRequest)
    {
        if (!await VerifyHasPermission(context, permissionRequest))
        {
            string tenantId = context.GetTenantIdFromToken();
            throw new ValidationError("", "authorization", $"{tenantId}: You are not authorized to run this. You are missing '{permissionRequest}' permission.\n");
        }
    }

    public async ValueTask<bool> VerifyHasPermission<T>(ResolveFieldContext<T> context, PermissionRequest permissionRequest)
    {
        if (permissionRequest.IgnoreTargetIds)
        {
            var userContext = context.UserContext as GraphQLUserContext;
            var hasClaimResult = userContext?.User.Claims
                .Any(c => permissionRequest.AllowedClaimTypes.Contains(c.Type)) ?? false;

            return hasClaimResult ? hasClaimResult :
                await _lazyPermissionValidator.HasAnyClaim(userContext, permissionRequest.AllowedClaimTypes);
        }

        var permittedTargetIds = await GetPermittedTargetIds(context, permissionRequest);

        return permissionRequest.TargetIds?.All(targetId => permittedTargetIds.Contains(targetId)) == true;
    }

    public virtual async Task<IEnumerable<string>> GetPermittedTargetIds<T>(
        ResolveFieldContext<T> context, PermissionRequest permissionRequest)
    {
        _logger.LogInformation("test log GetPermittedTargetIds");
        _logger.LogInformation("AllowedClaimType:{AllowedClaimType}", string.Join(",", (permissionRequest?.AllowedClaimTypes ?? new string[] { })));
        if (permissionRequest.IgnoreTargetIds)
        {
            _logger.LogInformation("AllowedClaimType:{AllowedClaimType}", string.Join(",", (permissionRequest?.AllowedClaimTypes ?? new string[] { })));
            return await GetPermittedTargetIdsFromClaimTypes(context, permissionRequest.AllowedClaimTypes);
        }

        var userContext = context.UserContext as GraphQLUserContext;
        var user = userContext?.User;
        _logger.LogInformation("AllowedClaimType:{AllowedClaimType}", string.Join(",", (permissionRequest?.AllowedClaimTypes ?? new string[] { })));
        _logger.LogInformation("PermissionRequest TargetIds:{TargetIds}", string.Join(",", (permissionRequest?.TargetIds ?? new string[] { })));
        var filteredIds = GetTargetIdsFromClaimTypesAndSpecifiedIds(user, permissionRequest.AllowedClaimTypes, permissionRequest.TargetIds);

        if (CheckIfLazyLoadingRequired(userContext))
        {
            var lazilyLoadedIds = await _lazyPermissionValidator.GetFilteredTargetIdsFromClaims(userContext, permissionRequest.AllowedClaimTypes, permissionRequest.TargetIds);

            if (filteredIds == null) return lazilyLoadedIds;
            if (lazilyLoadedIds == null) return filteredIds;

            _logger.LogInformation("FilteredIds loaded lazily");

            filteredIds = filteredIds.Concat(lazilyLoadedIds);
        }

        _logger.LogInformation("PermissionRequest FilteredIds:{FilteredIds}", string.Join(",", (filteredIds ?? new string[] { })));

        return filteredIds;
    }

    [Obsolete("This method is left for backward compatibility with old code, use GetPermittedTargetIds for new logic instead.")]
    public ValueTask<IEnumerable<string>> GetTargetIdsFromClaim<T>(
        ResolveFieldContext<T> context, IEnumerable<string> claimTypes)
    {
        // null reference exception
        var userContext = context.UserContext as GraphQLUserContext;
        if (CheckIfLazyLoadingRequired(userContext))
        {
            return _lazyPermissionValidator.GetTargetIdsFromClaims(userContext, claimTypes);
        }

        var targetIds = ((context.UserContext as GraphQLUserContext)!).User.Claims
            .Where(c => claimTypes.Contains(c.Type))
            .Select(c => c.Value);

        return ValueTask.FromResult(targetIds);
    }

    public async Task<bool> AuthorizeWithTargetPrefix<T>(ResolveFieldContext<T> context, string claimType, string prefix)
    {
        IEnumerable<string> allowedIds = (await GetPermittedTargetIdsFromClaimTypes(context, claimType)).ToList();
        return allowedIds.Contains("all") || allowedIds.Any(allowedId => StartWith(prefix, allowedId));
    }

    public ValueTask<bool> HasCreatorRightsTargetted<T>(ResolveFieldContext<T> context, string claimType)
    {
        var hasRights = (context.UserContext as GraphQLUserContext)?.User.Claims.Any(c =>
            c.Type == claimType && c.Value == "{creatorRightsTargetted}") ?? false;

        return ValueTask.FromResult(hasRights);
    }

    public async ValueTask AuthorizeWriteLinks<T>(ResolveFieldContext<T> context, string sourceId, string type, string targetId)
    {
        string tenantId = context.GetTenantIdFromToken();

        IEnumerable<string> allowedWriteLinksValues = await GetPermittedTargetIdsFromClaimTypes(context, "writeLinks");

        if (allowedWriteLinksValues.Contains("all"))
            return;

        if (!allowedWriteLinksValues.Any())
            throw new ValidationError("", "authorization", $"{tenantId}: You are not authorized to run this. You are missing a 'writeLinks' permission.\n");

        else if (allowedWriteLinksValues.Any(a => a == sourceId || a == targetId)) //check if they can write any links of either source or target
            return;

        else if (allowedWriteLinksValues.Any(a => a == "{allowedWriteIndividuals}" || a == "{allowedUpdateIndividuals}")) //check if they can write any links of either source or target
        {
            var allowedIds = (await GetPermittedTargetIdsFromClaimTypes(context, "writeIndividuals", "updateIndividuals"))?.ToList();
            if (allowedIds.Any(a => a == sourceId || a == targetId))
                return;
        }

        IEnumerable<string> typedPermissions = allowedWriteLinksValues.Where(a => a.Contains(":")); //get permissions with a split containing a restriction on type
        string reversedType = ReverseRelationshipType(type);

        IEnumerable<string> valuesContainingType = typedPermissions.Where(a => a.Contains(type) || a.Contains(reversedType)); //check both type and reversedType
        if (!valuesContainingType.Any())
            throw new ValidationError("", "authorization", $"{tenantId}: You are not authorized to run this. You are missing a 'writeLinks' permission that authorizes this type.\n");

        foreach (string validValue in valuesContainingType)
        {
            string valueWithoutExteriorBrackets = validValue.Substring(1, validValue.Length - 2);
            string[] idSplits = valueWithoutExteriorBrackets.Split(":"); //separate source, type and target restrictions
            var allowedSourceIds = new List<string> { };
            allowedSourceIds.Add(idSplits[0]);
            if (idSplits[0] == "{allowedWriteIndividuals}" || idSplits[0] == "{allowedUpdateIndividuals}")
                allowedSourceIds = (await GetPermittedTargetIdsFromClaimTypes(context, "writeIndividuals", "updateIndividuals"))?.ToList();
            else if (idSplits[0] == "{allowedWriteInternals}")
                allowedSourceIds = (await GetPermittedTargetIdsFromClaimTypes(context, "writeInternals"))?.ToList();
            else if (idSplits[0] == "{entityId}")
                allowedSourceIds.Add(context.GetEntityIdFromToken(false));

            if (idSplits.Length == 2)
            {
                if (allowedSourceIds.Contains(sourceId) && idSplits[1] == type || allowedSourceIds.Contains(targetId) && idSplits[1] == reversedType)
                    return;
            }
            else if (idSplits.Length == 3)
            {
                var allowedTargetIds = new List<string> { };
                allowedTargetIds.Add(idSplits[2]);
                if (idSplits[2] == "{allowedWriteIndividuals}" || idSplits[2] == "{allowedUpdateIndividuals}")
                    allowedTargetIds = (await GetPermittedTargetIdsFromClaimTypes(context, "writeIndividuals", "updateIndividuals"))?.ToList();
                else if (idSplits[2] == "{allowedWriteInternals}")
                    allowedTargetIds = (await GetPermittedTargetIdsFromClaimTypes(context, "writeInternals"))?.ToList();
                else if (idSplits[2] == "{entityId}")
                    allowedTargetIds.Add(context.GetEntityIdFromToken(false));
                if (allowedSourceIds.Contains(sourceId) && idSplits[1] == type && allowedTargetIds.Contains(targetId) || allowedSourceIds.Contains(targetId) && idSplits[1] == reversedType && allowedTargetIds.Contains(sourceId))
                    return;
            }
        }

        throw new ValidationError("", "authorization", $"{tenantId}: You are not authorized to run this. You are missing a 'writeLinks' permission that authorizes this type.\n");
    }

    public async ValueTask AuthorizeWriteLinks<T>(ResolveFieldContext<T> context, IEnumerable<(string sourceId, string type, string targetId)> links)
    {
        string tenantId = context.GetTenantIdFromToken();

        HashSet<string> allowedWriteLinksValues = (await GetPermittedTargetIdsFromClaimTypes(context, "writeLinks")).ToHashSet();

        if (allowedWriteLinksValues.Contains("all"))
            return;

        if (!allowedWriteLinksValues.Any())
            throw new ValidationError("", "authorization", $"{tenantId}: You are not authorized to run this. You are missing a 'writeLinks' permission.\n");

        if (links.All(l => allowedWriteLinksValues.Contains(l.sourceId) || allowedWriteLinksValues.Contains(l.targetId))) //check if they can write any links of either source or target
            return;

        if (allowedWriteLinksValues.Contains("{allowedWriteIndividuals}") || allowedWriteLinksValues.Contains("{allowedUpdateIndividuals}")) //check if they can write any links of either source or target
        {
            HashSet<string>? allowedIds = (await GetPermittedTargetIdsFromClaimTypes(context, "writeIndividuals", "updateIndividuals"))?.ToHashSet();
            if (allowedIds != null && links.All(l => allowedIds.Contains(l.sourceId) || allowedIds.Contains(l.targetId)))
                return;
        }

        IEnumerable<string> typedPermissions = allowedWriteLinksValues.Where(a => a.Contains(":")); //get permissions with a split containing a restriction on type

        foreach ((string sourceId, string type, string targetId) in links)
        {
            string reversedType = ReverseRelationshipType(type);
            IEnumerable<string> valuesContainingType = typedPermissions.Where(a => a.Contains(type) || a.Contains(reversedType)); //check both type and reversedType
            if (!valuesContainingType.Any())
                throw new ValidationError("", "authorization", $"{tenantId}: You are not authorized to run this. You are missing a 'writeLinks' permission that authorizes this type.\n");

            foreach (string validValue in valuesContainingType)
            {
                string valueWithoutExteriorBrackets = validValue.Substring(1, validValue.Length - 2);
                string[] idSplits = valueWithoutExteriorBrackets.Split(":"); //separate source, type and target restrictions
                List<string>? allowedSourceIds = new() { idSplits[0] };
                if (idSplits[0] == "{allowedWriteIndividuals}" || idSplits[0] == "{allowedUpdateIndividuals}")
                    allowedSourceIds = (await GetPermittedTargetIdsFromClaimTypes(context, "writeIndividuals", "updateIndividuals"))?.ToList();
                else if (idSplits[0] == "{allowedWriteInternals}")
                    allowedSourceIds = (await GetPermittedTargetIdsFromClaimTypes(context, "writeInternals"))?.ToList();
                else if (idSplits[0] == "{entityId}")
                    allowedSourceIds.Add(context.GetEntityIdFromToken(false));

                if (idSplits.Length == 2)
                {
                    if (allowedSourceIds.Contains(sourceId) && idSplits[1] == type || allowedSourceIds.Contains(targetId) && idSplits[1] == reversedType)
                        continue;
                }
                else if (idSplits.Length == 3)
                {
                    List<string>? allowedTargetIds = new() { idSplits[2] };
                    if (idSplits[2] == "{allowedWriteIndividuals}" || idSplits[2] == "{allowedUpdateIndividuals}")
                        allowedTargetIds = (await GetPermittedTargetIdsFromClaimTypes(context, "writeIndividuals", "updateIndividuals"))?.ToList();
                    else if (idSplits[2] == "{allowedWriteInternals}")
                        allowedTargetIds = (await GetPermittedTargetIdsFromClaimTypes(context, "writeInternals"))?.ToList();
                    else if (idSplits[2] == "{entityId}")
                        allowedTargetIds.Add(context.GetEntityIdFromToken(false));
                    if (allowedSourceIds.Contains(sourceId) && idSplits[1] == type && allowedTargetIds.Contains(targetId) || allowedSourceIds.Contains(targetId) && idSplits[1] == reversedType && allowedTargetIds.Contains(sourceId))
                        continue;
                }
            }

            throw new ValidationError("", "authorization", $"{tenantId}: You are not authorized to run this. You are missing a 'writeLinks' permission that authorizes this type.\n");
        }
    }

    public async ValueTask AuthorizeWriteTargettedPermissions<T>(ResolveFieldContext<T> context, string permissionId, string value)
    {
        string tenantId = context.GetTenantIdFromToken();

        IEnumerable<string> allowedWriteTargettedPermissionsValues = await GetPermittedTargetIdsFromClaimTypes(context, "writeTargettedPermissions");

        if (allowedWriteTargettedPermissionsValues.Contains("all"))
            return;

        if (!allowedWriteTargettedPermissionsValues.Any())
            throw new ValidationError("", "authorization", $"{tenantId}: You are not authorized to run this. You are missing a 'writeTargettedPermissions' permission for `{permissionId}:{value}`.\n");

        IEnumerable<string> validValues = allowedWriteTargettedPermissionsValues.Where(v => v.Contains(permissionId));
        if (!validValues.Any())
            throw new ValidationError("", "authorization", $"{tenantId}: You are not authorized to run this. You are missing a 'writeTargettedPermissions' permission for '{permissionId}:{value}'.\n");

        if (validValues.Any(v => v == $"{permissionId}|{value}"))
            return;

        foreach (string validValue in validValues)
        {
            string valueWithoutExteriorBrackets = validValue.Substring(1, validValue.Length - 2);
            string[] idSplits = valueWithoutExteriorBrackets.Split(":");
            if (idSplits[1] == "own")
            {
                IEnumerable<string> ownPermissionValues = await GetPermittedTargetIdsFromClaimTypes(context, permissionId);
                if (ownPermissionValues.Contains("all"))
                    return;
                if (ownPermissionValues.Any(v => v == value))
                    return;
            }
            if (idSplits[1] == "all")
                return;
        }

        throw new ValidationError("", "authorization", $"{tenantId}: You are not authorized to run this. You are missing a 'writeTargettedPermissions' permission for '{permissionId}:{value}'.\n");
    }

    public ValueTask<bool> HasCreatorRights<T>(ResolveFieldContext<T> context)
    {
        var hasRights = (context.UserContext as GraphQLUserContext)?.User.Claims.Any(c => c.Type == "creatorRights") ?? false;

        return ValueTask.FromResult(hasRights);
    }

    private async Task<IEnumerable<string>> GetPermittedTargetIdsFromClaimTypes<T>(ResolveFieldContext<T> context, params string[] claimTypes)
    {
        var userContext = context.UserContext as GraphQLUserContext;
        var targetIds = userContext!.User.Claims
            .Where(c => claimTypes.Contains(c.Type))
            .Select(c => c.Value);

        _logger.LogInformation("PermissionRequest 2 TargetIds:{targetIds}", string.Join(",", targetIds));
        if (CheckIfLazyLoadingRequired(userContext))
        {
            var lazilyLoadedIds = await _lazyPermissionValidator.GetTargetIdsFromClaims(userContext, claimTypes);
            targetIds = targetIds.Concat(lazilyLoadedIds);
        }
        _logger.LogInformation("PermissionRequest 3 TargetIds:{targetIds}", string.Join(",", targetIds));
        return targetIds;
    }

    private IEnumerable<string>? GetTargetIdsFromClaimTypesAndSpecifiedIds(ClaimsPrincipal claimsPrincipal, string[] claimTypes, IEnumerable<string>? ids)
    {
        IEnumerable<string>? allowedIds = claimsPrincipal?.Claims?.Where(c => claimTypes.Contains(c.Type)).Select(c => c.Value);
        if (allowedIds == null)
            return null;

        var filteredIds = allowedIds.Contains("all")
            ? ids
            : ids == null
                ? allowedIds
                : ids.Intersect(allowedIds.Except(new List<string> { "all" }));

        return filteredIds;
    }

    private static bool CheckIfLazyLoadingRequired(GraphQLUserContext userContext)
    {
        return userContext?.PermissionLazyLoadingRequired ?? false;
    }

    private static string ReverseRelationshipType(string type)
    {
        if (type?.StartsWith("opposite of ") ?? false)
            return type.Substring("opposite of ".Length);

        string[] walaaTypes = new string[] {
            "Wife",
            "Husband",
            "Daughter",
            "Son",
            "Father",
            "Mother",
            "Brother",
            "Sister",
            "Other",
            "Otherwise",
        };

        if (walaaTypes.Contains(type))
            return "Dependent";

        switch (type)
        {
            case "guardian": return "dependent";
            case "dependent": return "guardian";
            case "spouse": return "spouse";
            case "parent": return "child";
            case "child": return "parent";
            case "grandparent": return "grandchild";
            case "grandchild": return "grandparent";
            case "relative": return "relative";
            case "domesticPartner": return "domesticPartner";
            case "other": return "other";
            case "employs": return "worksFor";
            case "worksFor": return "employs";
            case "advise": return "advisedBy";
            case "advisedBy": return "advise";
            case "owns": return "ownedBy";
            case "ownedBy": return "owns";
            case "occupies": return "occupiedBy";
            case "occupiedBy": return "occupies";
            case "supervise": return "supervisedBy";
            case "supervisedBy": return "supervise";
            case "sibling": return "sibling";
            case null: return null;
            default: return $"opposite of {type}";
        };
    }

    private bool StartWith(string prefix, string allowedId)
    {
        if (prefix.StartsWith(allowedId)) return true;

        if (!allowedId.Contains("all")) return false;

        string[] prefixSegments = prefix.Split(new[] { '/' }, StringSplitOptions.RemoveEmptyEntries);
        string[] allowedIdSegments = allowedId.Split(new[] { '/' }, StringSplitOptions.RemoveEmptyEntries);
        if (prefixSegments.Length < allowedIdSegments.Length) return false;

        for (int i = 0; i < allowedIdSegments.Length; i++)
            if (prefixSegments[i] != allowedIdSegments[i] && allowedIdSegments[i] != "all")
                return false;

        return true;
    }
}