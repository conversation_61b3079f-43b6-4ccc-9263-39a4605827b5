using System.Collections.Generic;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Achievements;
using CoverGo.Gateway.Domain.Achievements.Achievements;
using CoverGo.Gateway.Domain.Achievements.AchievementTypes;
using CoverGo.Gateway.Interfaces.Achievements.Achievements;
using CoverGo.Gateway.Interfaces.Achievements.AchievementTypes;
using GraphQL.Authorization;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeAchievementsMutations(IAchievementService achievementService, PermissionValidator permissionValidator)
        {
            Field<CreatedStatusResultGraphType>()
                .Name("createAchievementType")
                .Description("creates an achievement type")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateAchievementTypeInputGraphType>>("input", "the input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string appId = context.GetClientIdFromToken();

                    CreateAchievementTypeCommand command = context.GetArgument<CreateAchievementTypeCommand>("input");
                    command.CreatedById = loginId;
                    command.AsLoginId = loginId;
                    command.AsAppId = appId;

                    await permissionValidator.Authorize(context, "writeAchievementTypes");

                    Result<CreatedStatus> result = await achievementService.CreateAchievementTypeAsync(tenantId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateAchievementType")
                .Description("updates an achievement type")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the achievement type identifier")
                .Argument<NonNullGraphType<UpdateAchievementTypeInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string id = context.GetArgument<string>("id");

                    await permissionValidator.Authorize(context, "writeAchievementTypes", id);

                    UpdateAchievementTypeCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateAchievementTypeCommand>();
                    command.ModifiedById = loginId;

                    return await achievementService.UpdateAchievementTypeAsync(tenantId, id, command);
                });

            Field<ResultGraphType>()
                .Name("deleteAchievementType")
                .Description("deletes an achievement type")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the identifier of the achievement type")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string id = context.GetArgument<string>("id");

                    await permissionValidator.Authorize(context, "writeAchievementTypes", id);

                    var command = new DeleteCommand
                    {
                        DeletedById = loginId
                    };

                    return await achievementService.DeleteAchievementTypeAsync(tenantId, id, command);
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createAchievement")
                .Description("creates an achievement")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateAchievementInputGraphType>>("input", "the input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    CreateAchievementCommand command = context.GetArgument<CreateAchievementCommand>("input");
                    command.CreatedById = loginId;

                    await permissionValidator.Authorize(context, "writeAchievements");

                    Result<CreatedStatus> result = await achievementService.CreateAchievementAsync(tenantId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateAchievement")
                .Description("updates an achievement")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the achievement identifier")
                .Argument<NonNullGraphType<UpdateAchievementInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string id = context.GetArgument<string>("id");

                    await permissionValidator.Authorize(context, "writeAchievements", id);

                    UpdateAchievementCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateAchievementCommand>();
                    command.ModifiedById = loginId;

                    return await achievementService.UpdateAchievementAsync(tenantId, id, command);
                });

            Field<ResultGraphType>()
                .Name("deleteAchievement")
                .Description("deletes an achievement")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("id", "the identifier of the achievement")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string id = context.GetArgument<string>("id");

                    await permissionValidator.Authorize(context, "writeAchievements", id);

                    var command = new DeleteCommand
                    {
                        DeletedById = loginId
                    };

                    return await achievementService.DeleteAchievementAsync(tenantId, id, command);
                });

            Field<ResultGraphType>()
                .Name("computeAchievementTypes")
                .Description("compute all achievement types for an entity")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("entityId", "the identifier of the entity")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string entityId = context.GetArgument<string>("entityId");

                    var command = new ComputeAchievementTypesCommand
                    {
                        EntityId = entityId,
                    };

                    return await achievementService.ComputeAchievementTypesAsync(tenantId, command);
                });
        }
    }
}