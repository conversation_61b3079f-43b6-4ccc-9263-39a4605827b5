using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Encryptions;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Notifications;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Interfaces.Auth;
using CoverGo.Gateway.Interfaces.Encryptions;
using CoverGo.Gateway.Interfaces.Files;
using CoverGo.Gateway.Interfaces.L10ns;
using CoverGo.Gateway.Interfaces.Notifications;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Transactions;
using GraphQL.Authorization;
using GraphQL.Types;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeMigrationsMutations(
            IAuthService authService,
            IProductService productService,
            IPolicyService policyService,
            INotificationService notificationService,
            IEntityService entityService,
            ITransactionService transactionService,
            IFileSystemService fileSystemService,
            IPricingService pricingService,
            IL10nService l10nService,
            IInsurerService insurerService,
            PermissionValidator permissionValidator)
        {
            Field<ResultGraphType>()
                .Name("initializeTenantAuth")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("tenantId", "the tenant identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetArgument<string>("tenantId");
                    if (string.IsNullOrEmpty(tenantId))
                        return Result.Failure("TenantId cannot be null or empty string");
                    if (!Regex.IsMatch(tenantId, "^[a-zA-Z_0-9]+$"))
                        return Result.Failure("TenantId can contain only characters numbers and underscore.");

                    if (context.GetTenantIdFromToken() != "covergo")
                        return new Result { Status = "failure", Errors = new List<string> { "You are not allowed to run this query as you are not a super admin." } };
                    await permissionValidator.Authorize(context, "role", "admin");

                    return await authService.InitializeTenantAsync(new InitializeTenantCommand { TenantId = tenantId });
                });

            Field<ResultGraphType>()
                .Name("initializeTenant")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("tenantId", "the tenant identifier")
                .Argument<ListGraphType<NonNullGraphType<StringGraphType>>>("hosts", "tenant hosts")
                .Argument<NonNullGraphType<CreateAdminInputGraphType>>("adminSettings", "the admin settings")
                .Argument<ListGraphType<CreateLoginInputGraphType>>("logins", "logins")
                .Argument<ListGraphType<CreateAppInputGraphType>>("apps", "apps")
                .Argument<InitializeTenantProductsInputGraphType>("productsInput", "the product config input")
                .Argument<InitializeTenantPoliciesInputGraphType>("policiesInput", "the policy config input")
                .Argument<InitializeTenantNotificationsInputGraphType>("notificationsInput", "the notification config input")
                .Argument<InitializeTenantEncryptionsInputGraphType>("encryptionsInput", "the encryption config input")
                .Argument<InitializeTenantTransactionsInputGraphType>("transactionsInput", "the transaction config input")
                .Argument<InitializeTenantFileSystemInputGraphType>("fileSystemInput", "the fileSystem config input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetArgument<string>("tenantId");

                    if (context.GetTenantIdFromToken() != "covergo")
                        return new Result { Status = "failure", Errors = new List<string> { "You are not allowed to run this query as you are not a super admin." } };
                    await permissionValidator.Authorize(context, "role", "admin");

                    List<CreateAppCommand> createAppCommands = context.GetArgument<List<CreateAppCommand>>("apps");
                    if (!createAppCommands.Any())
                        return Result.Failure("Must provide at least one App when initializing a tenant.");

                    List<string> errors = new() { };

                    CreateAdminCommand adminSettings = context.GetArgument<CreateAdminCommand>("adminSettings");

                    bool adminUserExists = await TryCheckExistingAdmin(tenantId, adminSettings.Username);
                    if (adminUserExists)
                        return Result.Failure($"Admin user '{adminSettings.Username}' exists for tenant '{tenantId}'");

                    Result createTenantResult = await authService.CreateTenantAsync(tenantId, new CreateTenantCommand { AdminSettings = adminSettings });

                    List<string> hosts = context.GetArgument<List<string>>("hosts") ?? new List<string> { };
                    foreach (string host in hosts)
                    {
                        Result addHostResult = await authService.AddHostToTenantSettingsAsync(tenantId, host);
                        errors.AddRange(addHostResult.Errors ?? new List<string> { });
                    }

                    List<CreateLoginCommand> createLoginCommands = context.GetArgument<List<CreateLoginCommand>>("logins");
                    foreach (CreateLoginCommand createLoginCommand in createLoginCommands ?? new List<CreateLoginCommand> { })
                    {
                        Result<CreatedStatus> createLoginResult = await authService.CreateLoginAsync(tenantId, createLoginCommand);
                        errors.AddRange(createLoginResult.Errors ?? new List<string> { });
                    }

                    foreach (CreateAppCommand createAppCommand in createAppCommands ?? new List<CreateAppCommand> { })
                    {
                        Result creatAppResult = await authService.CreateAppAsync(tenantId, createAppCommand);
                        errors.AddRange(creatAppResult.Errors ?? new List<string> { });
                    }

                    InitializeTenantProductsInputGraph productsInput = context.GetArgument<InitializeTenantProductsInputGraph>("productsInput");
                    if (productsInput != null)
                    {
                        var productsCommand = new InitializeTenantProductsCommand
                        {
                            ProductConfigs = productsInput?.ProductConfigs?.Select(c => new ProductConfig
                            {
                                ClientId = c.ClientId,
                                DisplayedInsurers = c.DisplayedInsurers?.ToDictionary(i => i.ProductType, i => i.InsurerIds),
                                DisplayedProducts = c.DisplayedProductIds?.ToDictionary(p => p.TenantId, i => i.ProductIds),
                                DisplayedBenefits = c.DisplayedBenefits?.ToDictionary(b => b.ProductType, b => b.BenefitCategories?.ToDictionary(bc => bc?.Id, bc => bc.TypeIds))
                            })
                        };
                        //Task<Result> productsTask = productService.InitializeTenantAsync(tenantId, productsCommand);
                        //tasks.Add(productsTask);

                        Result productsResult = await productService.InitializeTenantAsync(tenantId, productsCommand);
                        errors.AddRange(productsResult.Errors ?? new List<string> { });
                    }


                    InitializeTenantPoliciesCommand policiesCommand = context.GetArgument<InitializeTenantPoliciesCommand>("policiesInput");
                    if (policiesCommand != null)
                    {
                        //Task<Result> policiesTask = policyService.InitializeTenantAsync(tenantId, policiesCommand);
                        //tasks.Add(policiesTask);

                        Result policiesResult = await policyService.InitializeTenantAsync(tenantId, policiesCommand);
                        errors.AddRange(policiesResult.Errors ?? new List<string> { });
                    }

                    InitializeTenantNotificationsCommand notificationsCommand = context.GetArgument<InitializeTenantNotificationsCommand>("notificationsInput");
                    if (notificationsCommand != null)
                    {
                        //Task<Result> notificationsTask = notificationService.InitializeTenantAsync(tenantId, notificationsCommand);
                        //tasks.Add(notificationsTask);

                        Result notificationsResult = await notificationService.InitializeTenantAsync(tenantId, notificationsCommand);
                        errors.AddRange(notificationsResult.Errors ?? new List<string> { });
                    }

                    InitializeTenantEncryptionsInputGraph encryptionsInput = context.GetArgument<InitializeTenantEncryptionsInputGraph>("encryptionsInput");
                    if (encryptionsInput != null)
                    {
                        var encryptionsCommand = new InitializeTenantEncryptionsCommand
                        {
                            EncryptionConfigs = encryptionsInput?.EncryptionConfigs?.Select(c =>
                                new EncryptionConfig
                                {
                                    Type = c.Type,
                                    EventEncryptions = c.EventEncryptions?.Select(e => new EventEncryption
                                    {
                                        AlgorithmId = e.AlgorithmId,
                                        EventType = e.EventType,
                                        JsonPath = e.JsonPath,
                                        KeyString = e.KeyString,
                                        Parameters = e.Parameters != null ? e.Parameters?.ToDictionary(x => x.Key, x => x.Value.GetValue()) : null,
                                    }),
                                    DomainEncryptions = c.DomainEncryptions?.Select(d => new DomainEncryption
                                    {
                                        AlgorithmId = d.AlgorithmId,
                                        JsonPath = d.JsonPath,
                                        KeyString = d.KeyString,
                                        Parameters = d.Parameters != null ? d.Parameters?.ToDictionary(x => x.Key, x => x.Value.GetValue()) : null,
                                    }),
                                    Parameters = c.Parameters != null ? c.Parameters?.ToDictionary(x => x.Key, x => x.Value.GetValue()) : null,
                                })
                        };
                        //ToDo: refactor when encryptions is its own microservice
                        //Task<Result> encryptionsTask = entityService.InitializeTenantEncryptionsAsync(tenantId, encryptionsCommand);
                        //tasks.Add(encryptionsTask);

                        Result encryptionResult = await entityService.InitializeTenantEncryptionsAsync(tenantId, encryptionsCommand);
                        errors.AddRange(encryptionResult.Errors ?? new List<string> { });
                    }

                    InitializeTenantTransactionsCommand transactionsCommand = context.GetArgument<InitializeTenantTransactionsCommand>("transactionsInput");
                    if (transactionsCommand != null)
                    {
                        //Task<Result> transactionsTask = transactionService.InitializeTenantAsync(tenantId, transactionsCommand);
                        //tasks.Add(transactionsTask);
                        Result transactionResult = await transactionService.InitializeTenantAsync(tenantId, transactionsCommand);
                        errors.AddRange(transactionResult.Errors ?? new List<string> { });
                    }

                    InitializeTenantFileSystemCommand fileSystemCommand = context.GetArgument<InitializeTenantFileSystemCommand>("fileSystemInput");
                    if (fileSystemCommand != null)
                    {
                        //Task<Result> fileSystemTask = fileSystemService.InitializeTenantAsync(tenantId, fileSystemCommand);
                        //tasks.Add(fileSystem);
                        Result transactionResult = await fileSystemService.InitializeTenantAsync(tenantId, fileSystemCommand);
                        errors.AddRange(transactionResult.Errors ?? new List<string> { });
                    }

                    //Result[] results = await Task.WhenAll(tasks);
                    //IEnumerable<string> errors = results.Where(r => r.Errors?.Any() ?? false)?.SelectMany(e => e.Errors);
                    return errors?.Any() ?? false ? new Result { Status = "failure", Errors = errors?.ToList() } : new Result { Status = "success" };
                });

            Field<ResultGraphType>()
                .Name("migrateL10ns")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("tenantId", "the tenant identifier")
                .Argument<NonNullGraphType<ListGraphType<UpsertL10nInputGraphType>>>("input", "the migrate l10n input")
                .ResolveAsync(async context =>
                {
                    if (context.GetTenantIdFromToken() != "covergo")
                        return new Result { Status = "failure", Errors = new List<string> { "You are not allowed to run this query as you are not a super admin of covergo." } };
                    await permissionValidator.Authorize(context, "role", "admin");
                    string tenantId = context.GetArgument<string>("tenantId");
                    List<UpsertL10nCommand> commands = context.GetArgument<List<UpsertL10nCommand>>("input");
                    foreach (UpsertL10nCommand command in commands)
                    {
                        await l10nService.UpsertAsync(tenantId, command);
                    }
                    return new Result { Status = "success" };
                });

            Field<ResultGraphType>()
                .Name("migratePricings")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("tenantId", "the tenant identifier")
                .Argument<MigratePricingsInputGraphType>("input", "the productIds input")
                .ResolveAsync(async context =>
                {
                    if (context.GetTenantIdFromToken() != "covergo")
                        return new Result { Status = "failure", Errors = new List<string> { "You are not allowed to run this query as you are not a super admin of covergo." } };
                    await permissionValidator.Authorize(context, "role", "admin");
                    string tenantId = context.GetArgument<string>("tenantId");
                    MigratePricingsCommand command = context.GetArgument<MigratePricingsCommand>("input");

                    return await pricingService.MigratePricingsAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("migrateProducts")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("tenantId", "the tenant identifier")
                .Argument<MigrateProductsInputGraphType>("input", "the productIds input")
                .ResolveAsync(async context =>
                {
                    if (context.GetTenantIdFromToken() != "covergo")
                        return new Result { Status = "failure", Errors = new List<string> { "You are not allowed to run this query as you are not a super admin of covergo." } };
                    await permissionValidator.Authorize(context, "role", "admin");
                    string tenantId = context.GetArgument<string>("tenantId");
                    MigrateProductsInputGraph input = context.GetArgument<MigrateProductsInputGraph>("input");
                    var command = new MigrateProductsCommand
                    {
                        ProductInputs = input.ProductInputs.Select(p => new CreateProductCommand
                        {
                            ProductId = p.ProductId,
                            InsurerId = p.InsurerId,
                            IssuerProductId = p.IssuerProductId,
                            Underwriting = p.Underwriting != null
                            ? new Underwriting
                            {
                                SourceType = p.Underwriting.SourceType,
                                JsonLogicRules = p.Underwriting.JsonLogicRules != null ? JObject.Parse(p.Underwriting.JsonLogicRules) : null,
                                ExcelPath = p.Underwriting.ExcelPath,
                                ExcelRules = p.Underwriting.ExcelRules
                            } : p.UnderwritingRules != null
                                ? new Underwriting
                                {
                                    SourceType = "jsonLogic",
                                    JsonLogicRules = p.UnderwritingRules != null ? JObject.Parse(p.UnderwritingRules) : null,
                                } : null,
                            BenefitInputs = p.BenefitInputs?.Select(b => new AddBenefitCommand
                            {
                                TypeId = b.TypeId,
                                ParentTypeId = b.ParentTypeId,
                                CurrencyCode = b.CurrencyCode,
                                OptionKey = b.OptionKey,
                                ParentOptionKeys = b.ParentOptionKeys,
                                Condition = b.Condition != null
                                ? new Condition
                                {
                                    Type = b.Condition.Type,
                                    JsonLogicRule = b.Condition.JsonLogicRuleString != null
                                    ? JObject.Parse(b.Condition.JsonLogicRuleString)
                                    : null
                                } : null,
                                Value = b.Value != null ? JToken.FromObject(b.Value?.GetValue()) : null,
                                IsValueInput = b.IsValueInput
                            })
                        })
                    };

                    return await productService.MigrateProductsAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("migrateInsurers")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("tenantId", "the tenant Identitfier")
                .Argument<MigrateInsurersInputGraphType>("input", "the insurers input")
                .ResolveAsync(async context =>
                {
                    if (context.GetTenantIdFromToken() != "covergo")
                        return new Result { Status = "failure", Errors = new List<string> { "You are not allowed to run this query as you are not a super admin of covergo." } };
                    await permissionValidator.Authorize(context, "role", "admin");
                    string tenantId = context.GetArgument<string>("tenantId");
                    MigrateInsurersCommand command = context.GetArgument<MigrateInsurersCommand>("input");

                    return await insurerService.MigrateInsurersAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("initializeFileSystem")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("tenantId", "the tenant identifier")
                .Argument<InitializeTenantFileSystemInputGraphType>("fileSystemInput", "the fileSystem config input")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, "role", "admin");
                    string tenantId = context.GetArgument<string>("tenantId");

                    if (context.GetTenantIdFromToken() != "covergo")
                        return new Result { Status = "failure", Errors = new List<string> { "You are not allowed to run this query as you are not a super admin." } };

                    var errors = new List<string> { };
                    InitializeTenantFileSystemCommand fileSystemCommand = context.GetArgument<InitializeTenantFileSystemCommand>("fileSystemInput");

                    if (fileSystemCommand != null)
                    {
                        Result transactionResult = await fileSystemService.InitializeTenantAsync(tenantId, fileSystemCommand);
                        errors.AddRange(transactionResult.Errors ?? new List<string> { });
                    }

                    return errors?.Any() ?? false ? new Result { Status = "failure", Errors = errors?.ToList() } : new Result { Status = "success" };
                });

            async Task<bool> TryCheckExistingAdmin(string tenantId, string username)
            {
                try
                {
                    Login existingAdminUser = await authService.GetLoginByNameAsync(tenantId, username);
                    return existingAdminUser != null;
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Tenant `{tenantId}` auth not yet available. ||| {ex.Message}");
                }
            }
        }
    }
}