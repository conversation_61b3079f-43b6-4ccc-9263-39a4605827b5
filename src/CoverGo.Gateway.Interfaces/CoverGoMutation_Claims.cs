using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Claims;
using CoverGo.Gateway.Domain.Users;
using GraphQL.Validation;
using CoverGo.Gateway.Interfaces.Claims;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Users;
using GraphQL.Authorization;
using GraphQL.Types;
using Newtonsoft.Json.Linq;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;
using GraphQL;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeClaimsMutations(
            IClaimService claimService,
            IAuthService authService,
            IGuaranteeOfPaymentService guaranteeOfPaymentService,
            IClaimRemarksService claimRemarksService,
            ITenantSpecificClaimService tenantSpecificClaimService, 
            CoverGoTreatmentService treatmentService,
            CoverGoClaimReportService claimReportService,
            PermissionValidator permissionValidator)
        {
            Field<CreatedStatusResultGraphType>()
                .Name("createClaim")
                .Description("Creates a claim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateClaimInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("createClaims", "writeClaims"));

                    CreateClaimInputGraph input = context.GetArgument<CreateClaimInputGraph>("input");

                    var command = new CreateClaimCommand
                    {
                        Id = input.Id,
                        CreateIncident = input.CreateIncident,
                        ClaimantId = input.ClaimantId,
                        PanelId = input.PanelId,
                        CreatedById = context.GetLoginIdFromToken(),
                        IssuerNumber = input.IssuerNumber,
                        IssuerNumberType = input.IssuerNumberType,
                        PolicyId = input.PolicyId,
                        Status = input.Status,
                        DiagnosisCodes = input.DiagnosisCodes,
                        OperationCodes = input.OperationCodes,
                        ProviderId = input.ProviderId,
                        Fields = input.Fields,
                        Remark = input.Remark,
                        TempAttachmentFolderKey = input.TempAttachmentFolderKey
                    };

                    var userContext = (GraphQLUserContext)context.UserContext;
                    Result<string> result =
                        await claimService.CreateClaimAsync(tenantId, command, Tools.GetAccessToken(userContext));

                    return new Result<CreatedStatus>
                    {
                        Status = result.Status,
                        Errors = result.Errors,
                        Value = new CreatedStatus {Id = result.Value}
                    };
                });

            Field<ResultGraphType>()
                .Name("updateClaim")
                .Description("Updates a claim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("claimId", "the claim identifier")
                .Argument<NonNullGraphType<UpdateClaimInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims").WithTargetIds(claimId));

                    Claim claim = (await claimService.GetAsync(tenantId, new QueryArguments { Where = new ClaimWhere { Id = claimId } })).FirstOrDefault()
                        ?? throw new InvalidOperationException($"Claim with ID {claimId} not found.");

                    if (claim.Status == "APPROVED")
                        await permissionValidator.Authorize(context, "writeClaimWhenStatusApproved", claimId);

                    UpdateClaimCommand command = context.GetArgument<Dictionary<string, object>>("input").ToUpdateCommand<UpdateClaimCommand>();
                    if (command.IsStatusChanged && command.Status == "APPROVED")
                    {
                        await permissionValidator.Authorize(context, "approveClaim", claimId);
                        await permissionValidator.AuthorizeClaimAmountAsync(context, claim, tenantSpecificClaimService);
                    }

                    command.ModifiedById = context.GetLoginIdFromToken();

                    await context.PatchOperationPermittedCheckAsync(authService, tenantId, command.FieldsPatch, claimId,
                        "claim", () => Task.FromResult(claim.Fields));

                    Result result = await claimService.UpdateClaimAsync(tenantId, claimId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("deleteClaim")
                .Description("Deletes a claim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("claimId", "the claim identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    await permissionValidator.Authorize(context, new PermissionRequest("deleteClaims", "writeClaims").WithTargetIds(claimId));

                    var command = new DeleteClaimCommand {DeletedById = context.GetLoginIdFromToken()};
                    Result result = await claimService.DeleteAsync(tenantId, claimId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("rejectClaim")
                .Description("rejects a claim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("claimId", "the claim identifier")
                .Argument<ListGraphType<StringGraphType>>("codes", "the claim rejectionCodes")
                .Argument<StringGraphType>("remarks", "the remarks for claim rejection")
                .Argument<ListGraphType<RejectionReasonInputGraphType>>("reasons", "the reasons for claim rejection")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    List<string> codes = context.GetArgument<List<string>>("codes");
                    string remarks = context.GetArgument<string>("remarks");
                    List<RejectionReason> reasons = context.GetArgument<List<RejectionReason>>("reasons");
                    await permissionValidator.Authorize(context, new PermissionRequest("rejectClaims", "writeClaims").WithTargetIds(claimId));

                    Result result = await claimService.RejectClaimAsync(tenantId, claimId,
                        new RejectClaimCommand
                        {
                            Codes = codes, Remarks = remarks, Reasons = reasons, RejectedById = context.GetLoginIdFromToken()
                        });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("addBenefitToClaim")
                .Description("Adds a benefit to a claim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("claimId", "the claim identifier")
                .Argument<NonNullGraphType<AddBenefitToClaimInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims").WithTargetIds(claimId));

                    AddBenefitToClaimGraph input = context.GetArgument<AddBenefitToClaimGraph>("input");

                    Result result = await claimService.AddBenefitToClaimAsync(tenantId, claimId,
                        new AddBenefitToClaimCommand
                        {
                            BenefitTypeId = input.BenefitTypeId,
                            Value = input.Value?.GetValue() != null ? JToken.FromObject(input.Value?.GetValue()) : null,
                            Values = input.Values,
                            CurrencyCode = input.CurrencyCode,
                            BenefitName = input.BenefitName,
                            BenefitDescription = input.BenefitDescription,
                            Remark = input.Remark,
                            AddedById = context.GetLoginIdFromToken(),
                        });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateBenefitClaim")
                .Description("Updates a benefit of a claim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("claimId", "the claim identifier")
                .Argument<NonNullGraphType<StringGraphType>>("benefitClaimId", "The benefit claim id")
                .Argument<NonNullGraphType<UpdateBenefitClaimInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    string benefitClaimId = context.GetArgument<string>("benefitClaimId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims").WithTargetIds(claimId));

                    UpdateBenefitClaimGraph graph = context.GetArgument<UpdateBenefitClaimGraph>("input");
                    UpdateBenefitClaimCommand command =
                        Tools.ToDictionary<object>(graph).ToUpdateCommand<UpdateBenefitClaimCommand>();
                    command.Value = graph.Value != null ? JToken.FromObject(graph.Value?.GetValue()) : null;

                    command.BenefitClaimId = benefitClaimId;
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await claimService.UpdateBenefitClaimAsync(tenantId, claimId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeBenefitFromClaim")
                .Description("Removes a benefit from a claim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("claimId", "the claim identifier")
                .Argument<NonNullGraphType<StringGraphType>>("benefitClaimId", "The benefit claim id")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    string benefitClaimId = context.GetArgument<string>("benefitClaimId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims").WithTargetIds(claimId));

                    Result result = await claimService.RemoveBenefitFromClaimAsync(tenantId, claimId,
                        new RemoveBenefitFromClaimCommand
                        {
                            BenefitClaimId = benefitClaimId, RemovedById = context.GetLoginIdFromToken(),
                        });

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("addFactToClaim")
                .Description("Adds a fact to a claim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("claimId", "the claim identifier")
                .Argument<NonNullGraphType<AddFactInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims").WithTargetIds(claimId));

                    AddFactInputGraph input = context.GetArgument<AddFactInputGraph>("input");

                    Result<CreatedStatus> result = await claimService.AddFactAsync(tenantId, claimId,
                        new AddFactCommand
                        {
                            Type = input.Type,
                            Value = input.Value?.GetValue() != null
                                ? JToken.FromObject(input.Value?.GetValue())
                                : null,
                            AddedById = context.GetLoginIdFromToken(),
                        });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateClaimFact")
                .Description("update a claim fact")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("claimId", "the claim identifier")
                .Argument<NonNullGraphType<StringGraphType>>("factId", "The fact id")
                .Argument<NonNullGraphType<UpdateFactInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    string factId = context.GetArgument<string>("factId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims").WithTargetIds(claimId));

                    UpdateFactInputGraph graph = context.GetArgument<UpdateFactInputGraph>("input");
                    UpdateFactCommand command = Tools.ToDictionary<object>(graph).ToUpdateCommand<UpdateFactCommand>();
                    command.Id = factId;
                    command.Value = JToken.FromObject(graph.Value?.GetValue());
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await claimService.UpdateFactAsync(tenantId, claimId, command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeFactFromClaim")
                .Description("Removes a fact from a claim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("claimId", "the claim identifier")
                .Argument<NonNullGraphType<StringGraphType>>("factId", "The fact id")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    string factId = context.GetArgument<string>("factId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims").WithTargetIds(claimId));

                    Result result = await claimService.RemoveFactAsync(tenantId, claimId,
                        new RemoveFactCommand {Id = factId, RemovedById = context.GetLoginIdFromToken(),});

                    return result;
                });

            Field<ResultGraphType>()
                .Name("claimFactBatch")
                .Description("A batch of fact operations on a claim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("claimId", "the claim identifier")
                .Argument<NonNullGraphType<FactCommandBatchInputGraphType>>("input", "The fact batch input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetTenantIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims").WithTargetIds(claimId));

                    FactCommandBatchInputGraph input = context.GetArgument<FactCommandBatchInputGraph>("input");

                    var addFactCommands = input.AddFactInputs?.Select(i => i.ToCommand(loginId))?.ToList();

                    var updateFactCommands = input.UpdateFactInputs?.Select(i => i.ToCommand(loginId))?.ToList();

                    var factCommandBatch = new FactCommandBatch
                    {
                        AddFactCommands = addFactCommands, UpdateFactCommands = updateFactCommands
                    };

                    return await claimService.FactBatchAsync(tenantId, claimId, factCommandBatch);
                });

            Field<ResultGraphType>()
                .Name("addClaimNote")
                .Description("Adds a note to a claim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("claimId", "The claim identifier")
                .Argument<NonNullGraphType<AddNoteInputGraphType>>("input", "The note input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    await permissionValidator.Authorize(context, new PermissionRequest("createClaimNotes", "writeClaimNotes"));
                    AddNoteCommand command = context.GetArgument<AddNoteCommand>("input");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result result = await claimService.AddNoteAsync(tenantId, claimId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateClaimNote")
                .Description("Updates a note of a claim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("claimId", "The claim identifier")
                .Argument<NonNullGraphType<UpdateNoteInputGraphType>>("input", "The update note input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaimNotes", "writeClaimNotes"));
                    UpdateNoteCommand command = context.GetArgument<UpdateNoteCommand>("input");
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await claimService.UpdateNoteAsync(tenantId, claimId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeClaimNote")
                .Description("removes a note from a claim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("claimId", "The claim identifier")
                .Argument<NonNullGraphType<IdGraphType>>("noteId", "The note identifier")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("deleteClaimNotes", "writeClaimNotes"));
                    string tenantId = context.GetTenantIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    string id = context.GetArgument<string>("noteId");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await claimService.RemoveNoteAsync(tenantId, claimId,
                        new RemoveNoteCommand {Id = id, RemovedById = removedById});

                    return result;
                });

            Field<CreatedStatusResultGraphType>()
                .Name("addStakeholderToClaim")
                .Argument<NonNullGraphType<StringGraphType>>("claimId", "the claim identifier")
                .Argument<NonNullGraphType<AddStakeholderInputGraphType>>("input", "the stakeholder input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims").WithTargetIds(claimId));

                    AddStakeholderCommand command = context.GetArgument<AddStakeholderCommand>("input");
                    command.AddedById = loginId;

                    Result<string> result = await claimService.AddStakeholderToClaimAsync(tenantId, claimId, command);
                    return new Result<CreatedStatus>
                    {
                        Status = result.Status,
                        Errors = result.Errors,
                        Value = new CreatedStatus {Id = result.Value}
                    };
                });

            Field<ResultGraphType>()
                .Name("updateStakeholderOfClaim")
                .Argument<NonNullGraphType<StringGraphType>>("claimId", "the claim identifier")
                .Argument<NonNullGraphType<StringGraphType>>("stakeholderId", "the stakeholder identifier")
                .Argument<NonNullGraphType<UpdateStakeholderInputGraphType>>("input", "the stakeholder input")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    string stakeholderId = context.GetArgument<string>("stakeholderId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims").WithTargetIds(claimId));

                    UpdateStakeholderCommand command = context.GetArgument<Dictionary<string, object>>("input")
                        .ToUpdateCommand<UpdateStakeholderCommand>();
                    command.Id = stakeholderId;
                    command.ModifiedById = loginId;

                    return await claimService.UpdateStakeholderOfClaimAsync(tenantId, claimId, command);
                });

            Field<ResultGraphType>()
                .Name("removeStakeholderFromClaim")
                .Argument<NonNullGraphType<StringGraphType>>("claimId", "the claim identifier")
                .Argument<NonNullGraphType<StringGraphType>>("stakeholderId", "the stakeholder identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    string stakeholderId = context.GetArgument<string>("stakeholderId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims").WithTargetIds(claimId));

                    var command = new RemoveStakeholderCommand {Id = stakeholderId, RemovedById = loginId};

                    return await claimService.RemoveStakeholderFromClaimAsync(tenantId, claimId, command);
                });

             Field<CreatedStatusResultGraphType>()
                .Name("createGOP")
                .Description("Creates a guarantee of payment")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<CreateGuaranteeOfPaymentInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("createGOPs", "writeGOPs"));

                    CreateGuaranteeOfPaymentInputGraph input = context.GetArgument<CreateGuaranteeOfPaymentInputGraph>("input");

                    var command = new CreateGuaranteeOfPaymentCommand
                    {
                        MemberId = input.MemberId,
                        CreatedById = context.GetLoginIdFromToken(),
                        PolicyId = input.PolicyId,
                        Status = input.Status,
                        EstimatedAmount = input.EstimatedAmount,
                        Doctor = input.Doctor,
                        ProviderId = input.ProviderId,
                        Fields = input.Fields,
                        CurrencyCode = input.CurrencyCode,
                        IssuerNumber = input.IssuerNumber,
                    };

                    var userContext = (GraphQLUserContext)context.UserContext;
                    Result<string> result =
                        await guaranteeOfPaymentService.CreateGuaranteeOfPaymentAsync(tenantId, command, Tools.GetAccessToken(userContext));

                    return new Result<CreatedStatus>
                    {
                        Status = result.Status,
                        Errors = result.Errors,
                        Value = new CreatedStatus {Id = result.Value}
                    };
                });

            Field<ResultGraphType>()
                .Name("updateGOP")
                .Description("Updates a guarantee of payment")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("gopId", "the guarantee of payment identifier")
                .Argument<NonNullGraphType<UpdateGuaranteeOfPaymentInputGraphType>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string guaranteeOfPaymentId = context.GetArgument<string>("gopId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateGOPs", "writeGOPs").WithTargetIds(guaranteeOfPaymentId));

                    UpdateGuaranteeOfPaymentCommand command = context.GetArgument<Dictionary<string, object>>("input")
                        .ToUpdateCommand<UpdateGuaranteeOfPaymentCommand>();

                    command.ModifiedById = context.GetLoginIdFromToken();

                    await context.PatchOperationPermittedCheckAsync(authService, tenantId, command.FieldsPatch,
                        guaranteeOfPaymentId, "guaranteeOfPayment", async () =>
                        {
                            GuaranteeOfPayment guaranteeOfPayment = (await guaranteeOfPaymentService.GetAsync(tenantId,
                                    new QueryArguments {Where = new GuaranteeOfPaymentWhere {Id = guaranteeOfPaymentId}}))
                                .FirstOrDefault();

                            return guaranteeOfPayment?.Fields;
                        });

                    Result result =
                        await guaranteeOfPaymentService.UpdateGuaranteeOfPaymentAsync(tenantId, guaranteeOfPaymentId,
                            command);

                    return result;
                });

            Field<ResultGraphType>()
                .Name("deleteGOP")
                .Description("Deletes a guarantee of payment")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("gopId", "the guarantee of payment identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string guaranteeOfPaymentId = context.GetArgument<string>("gopId");
                    await permissionValidator.Authorize(context, new PermissionRequest("deleteGOPs", "writeGOPs").WithTargetIds(guaranteeOfPaymentId));

                    var command = new DeleteGuaranteeOfPaymentCommand {DeletedById = context.GetLoginIdFromToken()};
                    Result result = await guaranteeOfPaymentService.DeleteAsync(tenantId, guaranteeOfPaymentId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("rejectGOP")
                .Description("rejects a guarantee of payment")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("gopId", "the guarantee of payment identifier")
                .Argument<RejectGuaranteeOfPaymentInputGraphType>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string guaranteeOfPaymentId = context.GetArgument<string>("gopId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateGOPs", "writeGOPs").WithTargetIds(guaranteeOfPaymentId));

                    RejectGuaranteeOfPaymentInputGraph input = context.GetArgument<RejectGuaranteeOfPaymentInputGraph>("input");

                    Result result = await guaranteeOfPaymentService.RejectGuaranteeOfPaymentAsync(tenantId, guaranteeOfPaymentId,
                        new RejectGuaranteeOfPaymentCommand
                        {
                            Codes = input?.Codes, Remarks = input?.Remarks, RejectedById = context.GetLoginIdFromToken()
                        });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("approveGOP")
                .Description("approves a guarantee of payment")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("gopId", "the guarantee of payment identifier")
                .Argument<ApproveGuaranteeOfPaymentInputGraphType>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string guaranteeOfPaymentId = context.GetArgument<string>("gopId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateGOPs", "writeGOPs").WithTargetIds(guaranteeOfPaymentId));

                    ApproveGuaranteeOfPaymentInputGraph input = context.GetArgument<ApproveGuaranteeOfPaymentInputGraph>("input");

                    Result result = await guaranteeOfPaymentService.ApproveGuaranteeOfPaymentAsync(tenantId, guaranteeOfPaymentId,
                        new ApproveGuaranteeOfPaymentCommand
                        {
                            ApprovedAmount = input?.ApprovedAmount, ExcessAmount = input?.ExcessAmount, ApprovedById = context.GetLoginIdFromToken()
                        });

                    return result;
                });

            Field<ResultGraphType>()
                .Name("addGOPNote")
                .Description("Adds a note to a guarantee of payment")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("gopId", "The gop identifier")
                .Argument<NonNullGraphType<AddNoteInputGraphType>>("input", "The note input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string guaranteeOfPaymentId = context.GetArgument<string>("gopId");
                    await permissionValidator.Authorize(context, new PermissionRequest("createGOPNotes", "writeGOPNotes"));
                    AddNoteCommand command = context.GetArgument<AddNoteCommand>("input");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result result = await guaranteeOfPaymentService.AddNoteAsync(tenantId, guaranteeOfPaymentId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("updateGOPNote")
                .Description("Updates a note of a guarantee of payment")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("gopId", "The gop identifier")
                .Argument<NonNullGraphType<UpdateNoteInputGraphType>>("input", "The update note input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string guaranteeOfPaymentId = context.GetArgument<string>("gopId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateGOPNotes", "writeGOPNotes"));
                    UpdateNoteCommand command = context.GetArgument<UpdateNoteCommand>("input");
                    command.ModifiedById = context.GetLoginIdFromToken();

                    Result result = await guaranteeOfPaymentService.UpdateNoteAsync(tenantId, guaranteeOfPaymentId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeGOPNote")
                .Description("removes a note from a guarantee of payment")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("gopId", "The gop identifier")
                .Argument<NonNullGraphType<IdGraphType>>("noteId", "The note identifier")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("deleteGOPNotes", "writeGOPNotes"));
                    string tenantId = context.GetTenantIdFromToken();
                    string guaranteeOfPaymentId = context.GetArgument<string>("gopId");
                    string id = context.GetArgument<string>("noteId");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await guaranteeOfPaymentService.RemoveNoteAsync(tenantId, guaranteeOfPaymentId,
                        new RemoveNoteCommand {Id = id, RemovedById = removedById});

                    return result;
                });

             Field<ResultGraphType>()
                .Name("addGOPAttachment")
                .Description("Adds an attachment to a guarantee of payment")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("gopId", "The gop identifier")
                .Argument<NonNullGraphType<AttachmentInputGraphType>>("input", "The attachment input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string guaranteeOfPaymentId = context.GetArgument<string>("gopId");
                    await permissionValidator.Authorize(context, new PermissionRequest("createGOPAttachments", "writeGOPAttachments"));
                    AddAttachmentCommand command = context.GetArgument<AddAttachmentCommand>("input");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result result = await guaranteeOfPaymentService.AddAttachmentAsync(tenantId, guaranteeOfPaymentId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeGOPAttachment")
                .Description("removes an attachment from a guarantee of payment")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("gopId", "The gop identifier")
                .Argument<NonNullGraphType<StringGraphType>>("path", "The attachment path")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updateGOPAttachments", "writeGOPAttachments"));
                    string tenantId = context.GetTenantIdFromToken();
                    string guaranteeOfPaymentId = context.GetArgument<string>("gopId");
                    string path = context.GetArgument<string>("path");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await guaranteeOfPaymentService.RemoveAttachmentAsync(tenantId, guaranteeOfPaymentId,
                        new RemoveAttachmentCommand() {Path = path, RemovedById = removedById});

                    return result;
                });


            Field<ResultGraphType>()
                .Name("AddGOPToClaim")
                .Argument<NonNullGraphType<StringGraphType>>("claimId", "the claim identifier")
                .Argument<NonNullGraphType<StringGraphType>>("gopId", "the gop identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    string guaranteeOfPaymentId = context.GetArgument<string>("gopId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims").WithTargetIds(claimId));

                    var command = new AddGuaranteeOfPaymentCommand
                    {
                        GuaranteeOfPaymentId = guaranteeOfPaymentId,
                        AddedById = loginId
                    };

                    return await claimService.AddGuaranteeOfPaymentToClaimAsync(tenantId, claimId, command);
                });

            Field<ResultGraphType>()
                .Name("addClaimAttachment")
                .Description("Adds an attachment to a claim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("claimId", "The claim identifier")
                .Argument<NonNullGraphType<AttachmentInputGraphType>>("input", "The attachment input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    await permissionValidator.Authorize(context, new PermissionRequest("createClaimAttachments", "writeClaimAttachments"));
                    AddAttachmentCommand command = context.GetArgument<AddAttachmentCommand>("input");
                    command.AddedById = context.GetLoginIdFromToken();

                    Result result = await claimService.AddAttachmentAsync(tenantId, claimId, command);
                    return result;
                });

            Field<ResultGraphType>()
                .Name("removeClaimAttachment")
                .Description("removes an attachment from a claim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<IdGraphType>>("claimId", "The claim identifier")
                .Argument<NonNullGraphType<StringGraphType>>("path", "The attachment path")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaimAttachments", "writeClaimAttachments"));
                    string tenantId = context.GetTenantIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    string path = context.GetArgument<string>("path");
                    string removedById = context.GetLoginIdFromToken();

                    Result result = await claimService.RemoveAttachmentAsync(tenantId, claimId,
                        new RemoveAttachmentCommand() { Path = path, RemovedById = removedById });

                    return result;
                });


            Field<ResultGraphType>()
                .Name("RemoveGOPFromClaim")
                .Argument<NonNullGraphType<StringGraphType>>("claimId", "the claim identifier")
                .Argument<NonNullGraphType<StringGraphType>>("gopId", "the gop identifier")
                .AuthorizeWith("any")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    string guaranteeOfPaymentId = context.GetArgument<string>("gopId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims").WithTargetIds(claimId));

                    var command = new RemoveGuaranteeOfPaymentCommand
                    {
                        GuaranteeOfPaymentId = guaranteeOfPaymentId,
                        RemovedById = loginId
                    };

                    return await claimService.RemoveGuaranteeOfPaymentFromClaimAsync(tenantId, claimId, command);
                });

            Field<ResultGraphType>()
               .Name("claimBatch")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<ClaimBatchCommandInputGraphType>>("input", "the claim batch input")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();

                   ClaimBatchCommand claimBatchCommand = context.GetArgument<Dictionary<string, object>>("input")
                       .ToUpdateCommand<ClaimBatchCommand>();

                   if (!await permissionValidator.VerifyHasPermission(context, new PermissionRequest("writeClaims")))
                   {
                       if (claimBatchCommand.CreateClaimCommands?.Any() == true)
                           await permissionValidator.Authorize(context, new PermissionRequest("createClaims"));
                       if (claimBatchCommand.UpdateClaimCommands?.Any() == true)
                           await permissionValidator.Authorize(context, new PermissionRequest("updateClaims"));
                   }

                   claimBatchCommand.CreateClaimCommands?.ForEach(x => x.CreatedById = loginId);
                   claimBatchCommand.UpdateClaimCommands?.ForEach(x => x.ModifiedById = loginId);

                   if (claimBatchCommand.UpdateClaimCommands != null)
                   {
                       var claims = (await claimService.GetAsync(tenantId,
                           new Domain.QueryArguments {Where = new ClaimWhere {Id_in = claimBatchCommand.UpdateClaimCommands.Select(x => x.ClaimId)}})).ToList();

                       foreach (var claim in claims)
                       {
                           if (claim.Status == "APPROVED")
                               await permissionValidator.Authorize(context, "writeClaimWhenStatusApproved", claim.Id);
                       }

                       foreach (UpdateClaimCommand updateClaimCommand in claimBatchCommand.UpdateClaimCommands)
                       {
                           if (updateClaimCommand.IsStatusChanged && updateClaimCommand.Status == "APPROVED")
                               await permissionValidator.Authorize(context, "approveClaim", updateClaimCommand.ClaimId);
                       }
                   }

                   var userContext = (GraphQLUserContext)context.UserContext;
                   var result = await claimService.ClaimBatchAsync(tenantId, claimBatchCommand, Tools.GetAccessToken(userContext));

                   return result.IsSuccess ? Result.Success() : Result.Failure(result.Errors);
               });

             Field<CreatedStatusResultGraphType>()
               .Name("claimBatchV2")
               .AuthorizeWith("any")
               .Argument<NonNullGraphType<ClaimBatchCommandInputGraphType>>("input", "the claim batch input")
               .ResolveAsync(async context =>
               {
                   string tenantId = context.GetTenantIdFromToken();
                   string loginId = context.GetLoginIdFromToken();

                   ClaimBatchCommand claimBatchCommand = context.GetArgument<Dictionary<string, object>>("input")
                       .ToUpdateCommand<ClaimBatchCommand>();

                   if (!await permissionValidator.VerifyHasPermission(context, new PermissionRequest("writeClaims")))
                   {
                       if (claimBatchCommand.CreateClaimCommands?.Any() == true)
                           await permissionValidator.Authorize(context, new PermissionRequest("createClaims"));
                       if (claimBatchCommand.UpdateClaimCommands?.Any() == true)
                           await permissionValidator.Authorize(context, new PermissionRequest("updateClaims"));
                   }

                   claimBatchCommand.CreateClaimCommands?.ForEach(x => x.CreatedById = loginId);
                   claimBatchCommand.UpdateClaimCommands?.ForEach(x => x.ModifiedById = loginId);

                   if (claimBatchCommand.UpdateClaimCommands != null)
                   {
                       var claims = (await claimService.GetAsync(tenantId,
                           new Domain.QueryArguments {Where = new ClaimWhere {Id_in = claimBatchCommand.UpdateClaimCommands.Select(x => x.ClaimId)}})).ToList();

                       foreach (var claim in claims)
                       {
                           if (claim.Status == "APPROVED")
                               await permissionValidator.Authorize(context, "writeClaimWhenStatusApproved", claim.Id);
                       }

                       foreach (UpdateClaimCommand updateClaimCommand in claimBatchCommand.UpdateClaimCommands)
                       {
                           if (updateClaimCommand.IsStatusChanged && updateClaimCommand.Status == "APPROVED")
                               await permissionValidator.Authorize(context, "approveClaim", updateClaimCommand.ClaimId);
                       }
                   }

                   var userContext = (GraphQLUserContext)context.UserContext;
                   return await claimService.ClaimBatchAsync(tenantId, claimBatchCommand, Tools.GetAccessToken(userContext));
               });

            Field<ResultGraphType>()
                .Name("claimRemarkAddOrUpdate")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<RemarkUpsertGraphType>>("input", "")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims"));

                    string tenantId = context.GetTenantIdFromToken();
                    var input = context.GetArgument<RemarkUpsert>("input");

                    await claimRemarksService.UpsertAsync(tenantId, input);
                    return Result.Success();
                });

            Field<ResultGraphType>()
                .Name("claimRemarkDelete")
                .AuthorizeWith("any")
                .Argument<StringGraphType>("remarkId", "remark id to be deleted")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("deleteClaims", "writeClaims"));

                    string tenantId = context.GetTenantIdFromToken();
                    string remarkId = context.GetArgument<string>("remarkId");

                    await claimRemarksService.DeleteAsync(tenantId, remarkId);
                    return Result.Success();
                });

            Field<ResultGraphType>()
                .Name("reverseClaim")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("claimId", "claim to reverse")
                .Argument<NonNullGraphType<ReverseClaimInputGraphType>>("input", "reversal input")
                .ResolveAsync(async context =>
                {
                    await permissionValidator.Authorize(context, new PermissionRequest("reverseClaims", "writeClaims"));

                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string claimId = context.GetArgument<string>("claimId");
                    ReverseClaimCommand command = context.GetArgument<ReverseClaimCommand>("input");
                    command.ReversedById = loginId;

                    return await claimService.ReverseClaim(tenantId, claimId, command);
                });

            Field<CreatedStatusResultGraphType>()
                .Name("createTreatment")
                .Description("Creates a treatment")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<TreatmentInputGraph>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    await permissionValidator.Authorize(context, new PermissionRequest("createTreatments", "writeTreatments"));

                    CreateTreatmentCommand command = context.GetArgument<CreateTreatmentCommand>("input");
                    command.CreatedById = loginId;

                    return await treatmentService.CreateAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("updateTreatment")
                .Description("Updates a treatment")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("treatmentId", "The treatment identifier")
                .Argument<NonNullGraphType<TreatmentInputGraph>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string treatmentId = context.GetArgument<string>("treatmentId");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateTreatments", "writeTreatments").WithTargetIds(treatmentId));

                    UpdateTreatmentCommand command = context.GetArgument<UpdateTreatmentCommand>("input");
                    command.Id = treatmentId;
                    command.ModifiedById = loginId;

                    return await treatmentService.UpdateAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("deleteTreatment")
                .Description("Deletes a treatment")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<StringGraphType>>("treatmentId", "The treatment identifier")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();
                    string treatmentId = context.GetArgument<string>("treatmentId");
                    await permissionValidator.Authorize(context, new PermissionRequest("deleteTreatments", "writeTreatments").WithTargetIds(treatmentId));

                    var command = new RemoveCommand
                    {
                        RemovedById = loginId, Id = treatmentId
                    };

                    return await treatmentService.DeleteAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("treatmentBatch")
                .Description("treatment Batch")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<TreatmentBatchInputGraph>>("input", "The input")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    string loginId = context.GetLoginIdFromToken();

                    TreatmentBatchCommand command = context.GetArgument<TreatmentBatchCommand>("input");

                    if (command.Create?.Any() == true)
                        await permissionValidator.Authorize(context, "createTreatments", "writeTreatments");

                    command.Create?.ForEach(c => c.CreatedById = loginId);

                    if (command.Update != null)
                    {
                        foreach (var c in command.Update)
                        {
                            c.ModifiedById = loginId;
                            await permissionValidator.Authorize(context, new PermissionRequest("updateTreatments", "writeTreatments").WithTargetIds(c.Id));
                        }
                    }
                    if (command.Delete != null)
                    {
                        foreach (var c in command.Delete)
                        {
                            c.RemovedById = loginId;
                            await permissionValidator.Authorize(context, new PermissionRequest("deleteTreatments", "writeTreatments").WithTargetIds(c.Id));
                        }
                    }

                    return await treatmentService.BatchAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("removeClaimReport")
                .Description("Removes claim report")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<DateTimeGraphType>>("createdAt_gte", "CreatedAt greater than or equal")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var createdAtGte = context.GetArgument<DateTime>("createdAt_gte");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims"));

                    var command = new RemoveClaimReportCommand
                    {
                        RemovedById = context.GetLoginIdFromToken(),
                        CreatedAt_gte = createdAtGte
                    };

                    return await claimReportService.DeleteAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("generateClaimReport")
                .Description("Generate claim report")
                .AuthorizeWith("any")
                .Argument<NonNullGraphType<DateTimeGraphType>>("fromDate", "From date greater than or equal")
                .Argument<NonNullGraphType<DateTimeGraphType>>("toDate", "To date greater than or equal")
                .Argument<NonNullGraphType<StringGraphType>>("reportName", "Name of the report to be generated")
                .ResolveAsync(async context =>
                {
                    string tenantId = context.GetTenantIdFromToken();
                    var fromDate = context.GetArgument<DateTime>("fromDate");
                    var toDate = context.GetArgument<DateTime>("toDate");
                    var reportName = context.GetArgument<string>("reportName");
                    await permissionValidator.Authorize(context, new PermissionRequest("updateClaims", "writeClaims"));

                    var command = new ClaimReportOptions()
                    {
                        UtcFromDate = fromDate,
                        UtcToDate = toDate,
                        ReportName = reportName
                    };

                    return await claimService.GenerateAdhocClaimReport(tenantId, command);
                });
        }
    }
}