using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Interfaces.Products;
using GraphQL.Authorization;
using GraphQL.Types;

namespace CoverGo.Gateway.Interfaces
{
    public partial class CoverGoMutation
    {
        protected void InitializeScriptsMutations(IProductService productService, PermissionValidator permissionValidator)
        {
            Field<CreatedStatusResultGraphType>()
                .Name("createScript")
                .Argument<NonNullGraphType<CreateScriptInputGraphType>>("input", "Create a script")
                .ResolveAsync(async context =>
                {
                    try
                    {
                        await permissionValidator.Authorize(context, new PermissionRequest("createScripts", "writeScripts"));
                    }
                    catch
                    {
                        // Keep the authorization to avoid breaking after deployment
                        // It will be removed after all permissions are added correctly in a separated ticket
                        await permissionValidator.Authorize(context, new PermissionRequest("updateProducts", "writeProducts").WithTargetIds("all"));
                    }
                   
                    string tenantId = context.GetTenantIdFromToken();
                    CreateScriptInput input = context.GetArgument<CreateScriptInput>("input");
                    string loginId = context.GetLoginIdFromToken();
                    var command = new CreateScriptCommand
                    {
                        Name = input.Name,
                        InputSchema = input.InputSchema,
                        OutputSchema = input.OutputSchema,
                        SourceCode = input.SourceCode,
                        ReferenceSourceCodeUrl = input.ReferenceSourceCodeUrl,
                        ExternalTableDataUrl = input.ExternalTableDataUrl,
                        ExternalTableDataUrls = input.ExternalTableDataUrls,
                        Type = input.Type,
                        CreatedById = loginId,
                    };

                    return await productService.CreateScriptAsync(tenantId, command);
                });

            Field<ResultGraphType>()
                .Name("updateScript")
                .AuthorizeWith("any")
                .Description("Modifies a script")
                .Argument<NonNullGraphType<UpdateScriptInputGraphType>>("input", "the modified script")
                .ResolveAsync(async context =>
                {
                    UpdateScriptInput input = context.GetArgument<UpdateScriptInput>("input");
                    try
                    {
                        await permissionValidator.Authorize(context, new PermissionRequest("updateScripts", "writeScripts").WithTargetIds(input.ScriptId));
                    }
                    catch
                    {
                        // Keep the authorization to avoid breaking after deployment
                        // It will be removed after all permissions are added correctly in a separated ticket
                        await permissionValidator.Authorize(context, new PermissionRequest("updateProducts", "writeProducts").WithTargetIds("all"));
                    }
                    
                    string tenantId = context.GetTenantIdFromToken();
                    return await productService.UpdateScriptAsync(tenantId, new UpdateScriptCommand
                    {
                        ScriptId = input.ScriptId,
                        Name = input.Name,
                        InputSchema = input.InputSchema,
                        OutputSchema = input.OutputSchema,
                        SourceCode = input.SourceCode,
                        ReferenceSourceCodeUrl = input.ReferenceSourceCodeUrl,
                        ExternalTableDataUrl = input.ExternalTableDataUrl,
                        ExternalTableDataUrls = input.ExternalTableDataUrls,
                        Type = input.Type,
                        ModifiedById = context.GetLoginIdFromToken(),
                    });
                });

            Field<ResultGraphType>()
                .Name("deleteScript")
                .Argument<NonNullGraphType<RemoveScriptInputGraphType>>("input", "Delete a script")
                .ResolveAsync(async context =>
                {
                    
                    RemoveScriptInput command = context.GetArgument<RemoveScriptInput>("input");
                    try
                    {
                        await permissionValidator.Authorize(context, new PermissionRequest("deleteScripts", "writeScripts").WithTargetIds(command.ScriptId));
                    }
                    catch
                    {
                        // Keep the authorization to avoid breaking after deployment
                        // It will be removed after all permissions are added correctly in a separated ticket
                        await permissionValidator.Authorize(context, new PermissionRequest("deleteProducts", "writeProducts").WithTargetIds("all"));
                    }

                    string tenantId = context.GetTenantIdFromToken();
                    return await productService.DeleteScriptAsync(tenantId, new DeleteCommand { DeletedById = command.ScriptId });
                });
        }
    }
}