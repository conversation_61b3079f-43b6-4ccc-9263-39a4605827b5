using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace CoverGo.Gateway.Application;

public class DisableTenantMiddleware
{
    public const string TENANTS_TO_DISABLE = nameof(TENANTS_TO_DISABLE);

    readonly RequestDelegate _next;

    public DisableTenantMiddleware(RequestDelegate next)
    {
        _next = next ?? throw new ArgumentNullException(nameof(next));
    }

    public Task Invoke(HttpContext httpContext)
    {
        var tenantsToDisableValue = Environment.GetEnvironmentVariable(TENANTS_TO_DISABLE);
        if (string.IsNullOrEmpty(tenantsToDisableValue)) return _next(httpContext);

        if (!httpContext.User.Identity?.IsAuthenticated ?? true) return _next(httpContext);

        var tenant = httpContext.User.Claims.FirstOrDefault(x => x.Type == "tenantId");
        if (tenant == null) return _next(httpContext);

        var tenantsList = tenantsToDisableValue.Split(",");
        if (tenantsList.Any(x => x == tenant.Value)) throw new Exception($"Tenant {tenant.Value} is temporary disabled");

        return _next(httpContext);
    }
}