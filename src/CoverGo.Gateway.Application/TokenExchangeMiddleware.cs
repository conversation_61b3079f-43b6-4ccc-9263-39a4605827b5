﻿using IdentityModel;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Gateway.Domain;
using CoverGo.Proxies.Auth;
using Microsoft.Extensions.Options;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Application;

public abstract class TokenExchangeMiddleware
{
    private const string BearTokenPrefix = "Bearer ";

    protected readonly RequestDelegate _next;
    protected readonly IAuthService _authService;
    protected readonly IDistributedCache _cache;
    protected readonly IDateTimeProvider _dateTimeProvider;
    private readonly string _middleWareType;
    private readonly CacheSettings _cacheSettings;

    // Initializes a new instance of the TokenExchangeMiddleware class.
    //
    // <param name="next">The next middleware delegate in the pipeline.</param>
    // <param name="authService">The authentication service used to authenticate the token.</param>
    // <param name="cache">The distributed cache used to store and retrieve tokens.</param>
    protected TokenExchangeMiddleware(RequestDelegate next, IAuthService authService, IDistributedCache cache, IDateTimeProvider dateTimeProvider,  IOptions<CacheSettings> cacheSettingsOptions)
    {
        _next = next;
        _authService = authService;
        _cache = cache;
        _dateTimeProvider = dateTimeProvider;
        _middleWareType = this.GetType().Name;
        _cacheSettings = cacheSettingsOptions.Value;
    }

    public async Task Invoke(HttpContext context, ILogger<TokenExchangeMiddleware> logger)
    {
        string authorizationHeaderKey = HttpRequestHeader.Authorization.ToString();

        if (!context.Request.Headers.ContainsKey(HttpRequestHeader.Authorization.ToString()))
        {
            logger.LogWarning("{MiddlewareName} Authorization header not found", _middleWareType);
            await _next(context);
            return;
        }

        StringValues authorizationStringValues = context.Request.Headers[authorizationHeaderKey];

        int index = FindIndexOfFirstBearerToken(authorizationStringValues);

        if (index == -1)
        {
            logger.LogWarning("{MiddlewareName} Bearer token not found in Authorization", _middleWareType);
            await _next(context);
            return;
        }

        logger.LogInformation("{MiddlewareName} Bearer token found in Authorization", _middleWareType);
        if (!TryParseTokenFromAuthorizationHeader(authorizationStringValues[index], out JwtSecurityToken? accessToken)
            || accessToken == null
            || !IsValidToken(accessToken)
            || IsTokenExpired(accessToken))
        {
            logger.LogInformation("{MiddlewareName} Token is expired or invalid",_middleWareType);
            await _next(context);
            return;
        }

        logger.LogInformation("{MiddlewareName} Fetching user login information against the SSO User Token", _middleWareType);
        Login? login = await GetLoginAsync(accessToken, context.RequestAborted);
        logger.LogInformation("{MiddlewareName} SSO User Token found", _middleWareType);
        logger.LogInformation("{MiddlewareName} original SSO user Token for loginId:{loginId}", _middleWareType, login?.Id);

        string? internalAccessToken = await GenerateInternalAccessTokenAsync(accessToken, logger, context.RequestAborted);
        logger.LogInformation("{MiddlewareName} Internal access token generated from AXA HK SSO user for loginId: {loginId}", _middleWareType, login?.Id);
        logger.LogInformation("{MiddlewareName} Internal token:{token} user for loginId: {loginId}", _middleWareType, internalAccessToken, login?.Id);

        if (string.IsNullOrEmpty(internalAccessToken))
        {
            logger.LogWarning("{MiddlewareName} Empty Internal access token generated for loginId: {loginId}", _middleWareType, login?.Id);
            await _next(context);
            return;
        }

        logger.LogInformation("{MiddlewareName} Replacing SSO token with Internal access token for loginId:{loginId}",_middleWareType, login?.Id);
        ReplaceBearerToken(context, authorizationStringValues, authorizationHeaderKey,
            $"{BearTokenPrefix}{internalAccessToken}", index);
        logger.LogInformation("{MiddlewareName} Replaced SSO token with Internal access token", _middleWareType);

        await _next(context);
    }

    /// <summary>
    /// Generates an internal access token asynchronously.
    /// </summary>
    /// <param name="accessToken">The jwt access token used to generate the corresponding internal access token.</param>
    /// <returns>A string representing the generated internal access token, or null if the token could not be generated.</returns>
    private async Task<string?> GenerateInternalAccessTokenAsync(JwtSecurityToken accessToken, ILogger<TokenExchangeMiddleware> logger, CancellationToken cancellationToken)
    {
        logger.LogInformation("{MiddlewareName} GenerateInternalAccessTokenAsync SSO Cache Key: {key}",_middleWareType, accessToken.Id);
        logger.LogInformation("{MiddlewareName} GenerateInternalAccessTokenAsync SSO Subject: {subject}", _middleWareType, accessToken.Subject);
        logger.LogInformation("{MiddlewareName} GenerateInternalAccessTokenAsync SSO Token Validity: {expiry}", _middleWareType, accessToken.ValidTo);

        string cachedToken = null;
        try
        {
            using CancellationTokenSource timeoutCts = new(TimeSpan.FromSeconds(_cacheSettings.CacheTimeoutInSeconds));
            using CancellationTokenSource linkedCts = CancellationTokenSource.CreateLinkedTokenSource(timeoutCts.Token, cancellationToken);

            cachedToken = await _cache.GetStringAsync(BuildCacheKey(accessToken.Subject), linkedCts.Token);
        }
        catch (OperationCanceledException ex)
        {
            logger.LogWarning("{MiddlewareName} Cache get operation timed out: {Message}", _middleWareType, ex.Message);
        }

        logger.LogInformation("{MiddlewareName} GenerateInternalAccessTokenAsync Cached SSO User Token: {Token}", _middleWareType, cachedToken);
        if (!string.IsNullOrEmpty(cachedToken)) return cachedToken;

        logger.LogInformation("{MiddlewareName} GenerateInternalAccessTokenAsync SSO User Token not found in Cache", _middleWareType);
        string? clientId = await GetClientIdFromToken(accessToken);
        string? issuer = GetIssuerFromToken(accessToken);
        string? tenantId = await ExtractTenantIdFromIssuer(issuer);

        if (string.IsNullOrEmpty(tenantId)
            || string.IsNullOrEmpty(clientId)) return string.Empty;

        logger.LogInformation("{MiddlewareName} GenerateInternalAccessTokenAsync Fetching user login information against the SSO User Token", _middleWareType);
        Login? login = await GetLoginAsync(accessToken, cancellationToken);
        logger.LogInformation("{MiddlewareName} GenerateInternalAccessTokenAsync SSO User Token found", _middleWareType);
        logger.LogInformation("{MiddlewareName} GenerateInternalAccessTokenAsync original SSO user Token for loginId:{loginId}", _middleWareType, login?.Id);
        if (login == null) return string.Empty;

        Token token = await _authService.GetInternalAccessTokenAsync(tenantId, login.Id, clientId, cancellationToken);
        logger.LogInformation("{MiddlewareName} Internal access token generated from SSO user for loginId: {loginId}",_middleWareType, login?.Id);
        JwtSecurityToken? internalAccessToken = ConvertToJwtSecurityToken(token.AccessToken);
        if (internalAccessToken == null)
        {
            logger.LogWarning("{MiddlewareName} Empty Internal access token generated for loginId: {loginId}",_middleWareType, login?.Id);
            return string.Empty;
        }

        await _authService.AddLoginEventAsync(tenantId, new AddEventLogCommand(login.Id, LoginEventType.requestAccessTokenSuccess, _dateTimeProvider.UtcNow), cancellationToken);

        try
        {
            using CancellationTokenSource timeoutCts = new(TimeSpan.FromSeconds(_cacheSettings.CacheTimeoutInSeconds));
            using CancellationTokenSource linkedCts = CancellationTokenSource.CreateLinkedTokenSource(timeoutCts.Token, cancellationToken);
            await _cache.SetStringAsync(
                BuildCacheKey(accessToken.Subject),
                token.AccessToken,
                new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = CalculateAbsoluteExpirationRelativeToNow(accessToken, internalAccessToken, logger)
                },
                linkedCts.Token);
        }
        catch (OperationCanceledException ex)
        {
            logger.LogWarning("{MiddlewareName} Cache set operation timed out: {Message}", _middleWareType, ex.Message);
        }

        return token.AccessToken;
    }


    /// <summary>
    /// Asynchronously gets the login information for a given tenant, username, and email.
    /// </summary>
    protected async Task<Login?> GetLoginAsync(JwtSecurityToken accessToken, CancellationToken cancellationToken)
    {
        string? username = GetUsernameFromToken(accessToken);
        string? email = GetEmailFromToken(accessToken);
        string? issuer = GetIssuerFromToken(accessToken);
        string? tenantId = await ExtractTenantIdFromIssuer(issuer);

        if (string.IsNullOrEmpty(username) && string.IsNullOrEmpty(email)) return null;

        return (await _authService.GetLoginsAsync(tenantId,
            new LoginWhere
            {
                Or = new[]
                {
                    new LoginWhere { Usernames = new List<string> { username! } },
                    new LoginWhere { Email_in = new List<string> { email! } }
                },
                ExcludePermissions = true
            }, cancellationToken)).FirstOrDefault();
    }

    /// <summary>
    /// Build cache key based on the given access token
    /// </summary>
    /// <param name="tokenId">The identifier of the access token</param>
    /// <returns>
    /// The distributed cache key
    /// </returns>
    public abstract string BuildCacheKey(string tokenId);


    /// <summary>
    /// Calculates the absolute expiration relative to the current time based on the given access token and internal access token.
    /// </summary>
    /// <param name="accessToken">The access token string.</param>
    /// <param name="internalAccessToken">The internal access token string.</param>
    /// <returns>
    /// The calculated absolute expiration relative to the current time as a TimeSpan object. If the access token or internal access token is invalid, returns TimeSpan.Zero.
    /// </returns>
    private TimeSpan? CalculateAbsoluteExpirationRelativeToNow(JwtSecurityToken accessToken, JwtSecurityToken internalAccessToken,ILogger<TokenExchangeMiddleware> logger)
    {
        if (accessToken.ValidTo == DateTime.MinValue || internalAccessToken.ValidTo == DateTime.MinValue) return TimeSpan.Zero;

        TimeSpan expiry = new[] { accessToken.ValidTo, internalAccessToken.ValidTo }.Min() - _dateTimeProvider.UtcNow;
        logger.LogInformation("token cache exipration time in seconds: {timeInSeconds}", expiry.TotalSeconds);
        return expiry < TimeSpan.Zero ? TimeSpan.Zero : expiry;
    }


    /// <summary>
    /// Determines whether the given access token is expired.
    /// </summary>
    /// <param name="accessToken">The jwt access token to check.</param>
    /// <returns>True if the access token is expired; otherwise, false.</returns>
    private bool IsTokenExpired(JwtSecurityToken accessToken) => _dateTimeProvider.UtcNow > accessToken.ValidTo;

    /// <summary>
    /// Replaces the bearer token in the specified authorization header key of the given HttpContext.
    /// </summary>
    /// <param name="context">The HttpContext object.</param>
    /// <param name="stringValues">The StringValues object containing the existing bearer tokens.</param>
    /// <param name="authorizationHeaderKey">The key of the authorization header to update.</param>
    /// <param name="newToken">The new bearer token to replace the existing one.</param>
    /// <param name="indexToReplace">The index of the bearer token in the StringValues object to replace.</param>
    /// <returns>None.</returns>
    private static void ReplaceBearerToken(HttpContext context, StringValues stringValues,
        string authorizationHeaderKey, string newToken, int indexToReplace) =>
        context.Request.Headers[authorizationHeaderKey] = new StringValues(
            stringValues.Take(indexToReplace)
                .Concat(new[] { newToken })
                .Concat(stringValues.Skip(indexToReplace + 1))
                .ToArray());


    /// <summary>
    /// This function finds the index of the first bearer token in the given string values.
    /// </summary>
    /// <param name="stringValues">The string values to search through.</param>
    /// <returns>The index of the first bearer token, or -1 if no bearer token is found.</returns>
    protected static int FindIndexOfFirstBearerToken(StringValues stringValues)
    {
        int index = -1;
        for (int i = 0; i < stringValues.Count; i++)
        {
            if (!(stringValues[i]?.StartsWith(BearTokenPrefix, StringComparison.OrdinalIgnoreCase) ?? false)) continue;

            index = i;
            break;
        }

        return index;
    }

    /// <summary>
    /// Tries to parse a token from the provided authorization header.
    /// </summary>
    /// <param name="authorizationHeader">The authorization header containing the token.</param>
    /// <param name="result">The parsed token, if successful.</param>
    /// <returns>True if the token was successfully parsed, false otherwise.</returns>
    private static bool TryParseTokenFromAuthorizationHeader(string? authorizationHeader, out JwtSecurityToken? result)
    {
        result = null;
        if (string.IsNullOrEmpty(authorizationHeader)) return false;

        string accessToken = authorizationHeader.Replace(BearTokenPrefix, string.Empty);

        result = ConvertToJwtSecurityToken(accessToken);

        return result != null;

    }

    /// <summary>
    /// Convert access token string into JwtSecurityToken
    /// </summary>
    private static JwtSecurityToken? ConvertToJwtSecurityToken(string accessToken)
    {
        JwtSecurityTokenHandler tokenHandler = new();

        if (!tokenHandler.CanReadToken(accessToken)) return null;

        return tokenHandler.ReadToken(accessToken) as JwtSecurityToken;
    }

    /// <summary>
    /// Extracts the tenant ID from the issuer string.
    /// </summary>
    /// <param name="issuer">The issuer string from which to extract the tenant ID.</param>
    /// <returns>The extracted tenant ID, or null if the issuer string is null or does not contain the tenant ID.</returns>
    /// <remarks>
    /// The extracted tenant ID is obtained by finding the substring after the KeyCloakRealms
    /// segment in the issuer string. If the issuer string is null or does not contain the
    /// KeyCloakRealms segment, the function returns null.
    /// </remarks>
    public abstract Task<string?> ExtractTenantIdFromIssuer(string? issuer);


    /// <summary>
    /// Retrieves a claim from a JWT security token based on the specified claim type.
    /// </summary>
    protected static Claim? GetClaimFromJwtSecurityToken(JwtSecurityToken jwtSecurityToken, string claimType) =>
        jwtSecurityToken.Claims.FirstOrDefault(claim => claim.Type == claimType);
    protected string? GetIssuerFromToken(JwtSecurityToken accessToken) => GetClaimFromJwtSecurityToken(accessToken, JwtClaimTypes.Issuer)?.Value;


    public abstract bool IsValidToken(JwtSecurityToken? accessToken);
    public abstract Task<string?> GetClientIdFromToken(JwtSecurityToken accessToken);
    public abstract string? GetUsernameFromToken(JwtSecurityToken accessToken);
    public abstract string? GetEmailFromToken(JwtSecurityToken accessToken);


}

