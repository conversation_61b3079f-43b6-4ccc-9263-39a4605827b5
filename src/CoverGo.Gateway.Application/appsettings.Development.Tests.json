{"serviceUrls": {"gateway": "http://localhost:60060/", "logging": "http://localhost:9200/", "auth": "http://localhost:60000/", "users": "http://localhost:60010/", "products": "http://localhost:60020/", "pricing": "http://localhost:60030/", "l10n": "http://localhost:60040/", "policies": "http://localhost:60050/", "premium": "http://localhost:50020/", "notifications": "http://localhost:60070/", "claims": "http://localhost:60080/", "commissions": "http://localhost:60090/", "advisor": "http://localhost:60110/", "transactions": "http://localhost:60120/", "cms": "http://localhost:8080/", "templates": "http://localhost:63542/", "cases": "http://localhost:60600/", "filesystem": "http://localhost:61872/", "education": "http://localhost:60130", "achievements": "http://localhost:60140", "scheduler": "http://localhost:63570", "fubonfps": "http://localhost:8080", "claimInvestigation": "http://localhost:56073/", "party": "http://localhost:60910/", "reference": "http://localhost:61910/", "productbuilder": "http://localhost:60180/", "requestManager": "http://localhost:60300/", "printing": "http://localhost:60920/", "channelManagement": "http://localhost:64483/", "taskManagement": "http://localhost:51505/", "finance": "http://localhost:60930/", "etl": "http://localhost:5301/", "payments": "http://localhost:5201/", "middleEastIntegration": "http://localhost:8080/"}, "fileExtensionFilter": {"allowedExtensions": {".txt": "text/plain", ".jpg": "image/jpeg", ".jpeg": "image/jpeg", ".gif": "image/gif", ".png": "image/png", ".svg": "image/svg+xml", ".bmp": "image/bmp", ".tif": "image/tiff", ".tiff": "image/tiff", ".heif": "image/heif", ".heic": "image/heic", ".pdf": "application/pdf", ".doc": "application/msword", ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", ".xls": "application/vnd.ms-excel", ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", ".csv": "text/csv", ".ppt": "application/vnd.ms-powerpoint", ".pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation", ".htm": "text/html", ".html": "text/html", ".avi": "video/x-msvideo", ".weba": "audio/webm", ".webm": "video/webm", ".mpeg": "video/mpeg", ".mp4": "video/mp4", ".mov": "video/quicktime", ".wmv": "video/x-ms-wmv", ".flv": "video/x-flv", ".js": "application/javascript", ".msg": "application/vnd.ms-outlook", ".eml": "message/rfc822", ".cgx": "application/octet-stream"}, "restrictedExtensions": {".js": "application/javascript"}}, "FeatureManagement": {"PermissionV2": true, "Reference": true, "TaskManagement": true, "RequestManager": true, "ChannelManagement": true, "Premium": true, "Printing": true, "IntegrationGateway": true, "ProposalIdIfStakeholderPermission": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["asia_dev", "coverHealth_dev", "asia_preprod", "asia", "covergo"]}}]}}, "GraphQL": {"EnableMetrics": true}, "ReverseProxy": {"Routes": {"tameeni-route": {"ClusterId": "tameeni-cluster", "Match": {"Path": "api/v1/tameeni/{**catch-all}"}}}, "Clusters": {"tameeni-cluster": {"Destinations": {"tameeni-destination": {"Address": "http://localhost:40010"}}}}}, "Redis": {"ConnectionString": "localhost:6379"}, "AxaHkSsoSettings": {"EnableSsoTokenExchange": "false"}, "UseSentry": false}