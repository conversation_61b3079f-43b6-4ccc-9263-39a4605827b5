#This dockerfile exists for debugging purposes only
#If file is located outside of this project debugger won't be attached.
#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS base
RUN apk add --no-cache icu-libs # Without this DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=0 will fail
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=0
WORKDIR /app
EXPOSE 8080

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG GH_ACCOUNT
ARG GH_TOKEN
WORKDIR /src

COPY ["nuget.config", "."]
COPY ./Directory.Build.props .
COPY ./Directory.Packages.props .
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text

COPY ["src/CoverGo.Gateway.Application/CoverGo.Gateway.Application.csproj", "src/CoverGo.Gateway.Application/"]
COPY ["src/Common/Common.csproj", "src/Common/"]
COPY ["src/CoverGo.Gateway.Domain/Domain.csproj", "src/CoverGo.Gateway.Domain/"]
COPY ["src/CoverGo.Gateway.Infrastructure/Infrastructure.csproj", "src/CoverGo.Gateway.Infrastructure/"]
COPY ["src/CoverGo.Gateway.Interfaces/Interfaces.csproj", "src/CoverGo.Gateway.Interfaces/"]
RUN dotnet restore "src/CoverGo.Gateway.Application/CoverGo.Gateway.Application.csproj"
COPY . .
WORKDIR "/src/src/CoverGo.Gateway.Application"
RUN dotnet build "CoverGo.Gateway.Application.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "CoverGo.Gateway.Application.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS service-runtime
ENV ASPNETCORE_URLS http://*:8080
WORKDIR /app
COPY --from=publish /app/publish .
EXPOSE 8080
ENTRYPOINT ["dotnet", "CoverGo.Gateway.Application.dll"]
