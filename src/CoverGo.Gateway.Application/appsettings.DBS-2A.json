{"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Syslog", "Serilog.Expressions"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.AspNetCore": "Warning", "System": "Warning"}}, "Properties": {"type": "RMHK"}, "WriteTo:Console": {"Name": "<PERSON><PERSON>", "Args": {"configureLogger": {"Filter": [{"Name": "ByExcluding", "Args": {"expression": "StartsWith(SourceContext, 'Serilog.AspNetCore.RequestLoggingMiddleware')"}}], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}]}}}, "WriteTo:File": {"Name": "<PERSON><PERSON>", "Args": {"configureLogger": {"Filter": [{"Name": "ByIncludingOnly", "Args": {"expression": "StartsWith(SourceContext, 'CoverGo.Gateway.Application.Logging.SerilogDBSMiddleware')"}}], "WriteTo": [{"Name": "File", "Args": {"path": "./logs/myapp.json", "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"}}]}}}, "WriteTo:Performance": {"Name": "<PERSON><PERSON>", "Args": {"configureLogger": {"Filter": [{"Name": "ByIncludingOnly", "Args": {"expression": "StartsWith(SourceContext, 'CoverGo.Gateway.Application.Logging.SerilogDBSMiddleware')"}}], "WriteTo": [{"Name": "TcpSyslog", "Args": {"formatter": "CoverGo.Gateway.Application.Logging.Formatters::Output, CoverGo.Gateway.Application", "host": "*************", "port": 5066, "format": "Local", "facility": "Local7", "secureProtocols": "None", "disableDualMode": true, "checkHostIPAddress": false, "acceptAllCertifications": true}}]}}}, "WriteTo:Application": {"Name": "<PERSON><PERSON>", "Args": {"configureLogger": {"Filter": [{"Name": "ByExcluding", "Args": {"expression": "StartsWith(SourceContext, 'Serilog.AspNetCore.RequestLoggingMiddleware')"}}], "WriteTo": [{"Name": "TcpSyslog", "Args": {"host": "x01blaasaadm1a.vsi.uat.dbs.com", "port": 6031, "format": "RFC5424", "facility": "Local7", "secureProtocols": "Tls12", "disableDualMode": true, "checkHostIPAddress": false, "acceptAllCertifications": true}}]}}}, "AllowedHosts": "*"}, "serviceUrls": {"gateway": "http://gateway-2A.rmhk.cbg.prd.cloudnow.dbs.com/", "logging": "http://logging-2A.rmhk.cbg.prd.cloudnow.dbs.com/", "auth": "http://auth-2A.rmhk.cbg.prd.cloudnow.dbs.com/", "users": "http://users-2A.rmhk.cbg.prd.cloudnow.dbs.com/", "products": "http://product-2A.rmhk.cbg.prd.cloudnow.dbs.com/", "pricing": "http://localhost:60030/", "l10n": "http://localization-2A.rmhk.cbg.prd.cloudnow.dbs.com/", "policies": "http://policies-2A.rmhk.cbg.prd.cloudnow.dbs.com/", "notifications": "http://notification-2A.rmhk.cbg.prd.cloudnow.dbs.com/", "claims": "http://localhost:60080/", "commissions": "http://localhost:60090/", "advisor": "http://localhost:60110/", "transactions": "http://localhost:60120/", "cms": "http://localhost:8080/", "templates": "http://templates-2A.rmhk.cbg.prd.cloudnow.dbs.com/", "cases": "http://cases-2A.rmhk.cbg.prd.cloudnow.dbs.com/", "filesystem": "http://filesystem-2A.rmhk.cbg.prd.cloudnow.dbs.com/", "education": "http://localhost:60130", "achievements": "http://localhost:60140", "scheduler": "http://localhost:63570", "productbuilder": "http://localhost:60210"}}