using System;
using System.Security.Claims;
using CoverGo.Gateway.Domain.Claims;
using CoverGo.Gateway.Domain.Claims.Asia;
using CoverGo.HttpUtils;
using Microsoft.AspNetCore.Http;

namespace CoverGo.Gateway.Application.Services;

public class LazyResolvedTenantSpecificClaimService : ITenantSpecificClaimService
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IServiceProvider _serviceProvider;

    public LazyResolvedTenantSpecificClaimService(
        IHttpContextAccessor httpContextAccessor,
        IServiceProvider serviceProvider)
    {
        _httpContextAccessor = httpContextAccessor;
        _serviceProvider = serviceProvider;
    }

    private ITenantSpecificClaimService? ResolveService()
    {
        HttpContext? context = _httpContextAccessor.HttpContext;
        string? tenantId = context?.GetTenantId() ?? context?.User?.FindFirstValue("tenantId");

        if (tenantId?.StartsWith("asia_", StringComparison.OrdinalIgnoreCase) == true)
        {
            return _serviceProvider.GetService(typeof(AsiaClaimService)) as ITenantSpecificClaimService;
        }
        else
        {
            return _serviceProvider.GetService(typeof(DefaultTenantSpecificClaimService)) as ITenantSpecificClaimService;
        }
    }

    public decimal GetClaimApprovalAmount(Domain.Claims.Claim claim)
    {
        ITenantSpecificClaimService? resolvedService = ResolveService();
        if (resolvedService == null)
        {
            throw new InvalidOperationException($"[{nameof(LazyResolvedTenantSpecificClaimService)}] Cannot resolve tenant specific claim service.");
        }
        
        return resolvedService.GetClaimApprovalAmount(claim);
    }
}
