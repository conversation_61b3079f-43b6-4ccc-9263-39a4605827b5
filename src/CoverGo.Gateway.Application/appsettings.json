{"AllowedHosts": "*", "Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": "Serilog.Formatting.Compact.RenderedCompactJsonFormatter, Serilog.Formatting.Compact"}}], "Enrich": ["FromLogContext"]}, "Sentry": {"MinimumBreadcrumbLevel": "Warning"}, "serviceTimeouts": {"fubonfps": "00:00:10", "templates": "00:02:00", "wfp": "00:00:10"}, "FeatureManagement": {"PermissionV2": false, "RequestManager": false, "OptimizedProductsQueryEnabled": true, "Printing": false, "ProposalIdIfStakeholderPermission": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["asia_dev", "coverHealth_dev", "asia_preprod", "asia"]}}]}, "DisAllowSendNotificationParameters": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["axaTh_test", "axaTh_uat", "axaTh_preprod", "axaTh_uatx", "axaTh_prod"]}}]}, "DisallowIndividualPolicyExtraFields": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["axaTh_test", "axaTh_uat", "axaTh_preprod", "axaTh_uatx", "axaTh_prod"]}}]}, "DisallowHtmlJavascriptForWkhtmlToPdfTemplate": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["axaTh_test", "axaTh_uat", "axaTh_preprod", "axaTh_uatx", "axaTh_prod"]}}]}, "UseCustomDataRateLimit": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["axaTh_test", "axaTh_uat", "axaTh_preprod", "axaTh_uatx", "axaTh_prod"]}}]}}, "TargetUrl": "http://localhost:60060/", "BoclCertificate": "MIIEpjCCA46gAwIBAgICDdkwDQYJKoZIhvcNAQELBQAwgcgxCzAJBgNVBAYTAkhLMRIwEAYDVQQIDAlIb25nIEtvbmcxEjAQBgNVBAcMCUhvbmcgS29uZzEmMCQGA1UECgwdQmFuayBvZiBDaGluYSAoSG9uZyBLb25nKSBMdGQxDDAKBgNVBAsMA0lURDE1MDMGA1UEAwwsT3BlbiBTeXN0ZW0gTWFuYWdlbWVudCBDZXJ0aWZpY2F0ZSBBdXRob3JpdHkxJDAiBgkqhkiG9w0BCQEWFWl0X21zX2FkbWluQGJvY2hrLmNvbTAeFw0yMTA3MjYwODAyMDFaFw0yNjA0MjYwODAyMDFaMIGQMQswCQYDVQQGEwJISzESMBAGA1UECAwJSG9uZyBLb25nMRIwEAYDVQQHDAlIb25nIEtvbmcxJjAkBgNVBAoMHUJhbmsgb2YgQ2hpbmEgKEhvbmcgS29uZykgTHRkMQwwCgYDVQQLDANJVEQxIzAhBgNVBAMMGmR1bXMwMS10b2tlbnNpZ24uYm9jaGsuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAppbnGgVfZAcs0lU23f1c7mcRS3KeuOiwMp5OZTQ6sceDSheEjF9js1wJaAc/acFpy6nTAKuiTxTXznOFotZBILeUEIaTdLinovAySGRbuedNih/mp1HxfDvphkhvX1bHppFpTBC00BX6uPjBUdkERRsD4VtPpBV4wunAUk8EqWGd5tlteINtEPsONY0GFlRGhAtl0Vwj5OfmFmFMqMCzLQtEZy5AWRsi2DGMCxMBS26f2sVh/KdakTKn4vy4lsYSIv3MZ7QAkiIzb7JJmy9iawtV259ZI1ajIbk9eAQn1QBG/ItCWOBLxFs0N5oAf5+yofxVg9W102lkR2S26mpKBQIDAQABo4HPMIHMMAkGA1UdEwQCMAAwCwYDVR0PBAQDAgXgMCwGCWCGSAGG+EIBDQQfFh1PcGVuU1NMIEdlbmVyYXRlZCBDZXJ0aWZpY2F0ZTAdBgNVHQ4EFgQURgav37d1BVjzfAJ+DTa88gPf8kkwHwYDVR0jBBgwFoAU22jKhhTuSKkIAoKn/9A/dBU9vaswHQYDVR0lBBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMCMCUGA1UdEQQeMByCGmR1bXMwMS10b2tlbnNpZ24uYm9jaGsuY29tMA0GCSqGSIb3DQEBCwUAA4IBAQBKEmRQSmcrua5ebKHl510hKD/StxTaxvni1sdKb8pgh/LpQ0eV1lkRi5DfVsMGeN2GsYNME07zOSuvnLxfn0psmRLYcBYH+QponHJhJGER+efmO1sDcULtgjBTxWrEf7wzkXgF3X5E7IrX+rII/B4Yd+iebDdLmBLr5EZvfgyAh2+WUj151mWrqz/fzY2rgKhKRFdkrM6sBwuJlZ0mzU0UiKZL2J7cEFU4LfkUfju+uZvzJBxpVLS1UYH9OoYEi1K50Tzi/eGWokdvcn1mjHPo5M56qu8+lxZHjroZ5qK9gqTi0qA1b7FCbO4Dl3l8aEG3QSxvEjkthNjyByf7NHzz", "ComponentProLicKey": "4D16A969F0F8F1DBE38C0247B19E83AFEDE5B17C8C9CBDC298C2D334EF", "GraphQL": {"EnableMetrics": false}, "KeyCloak": {"Authority": "https://keycloak.dev.covergo.cloud/realms/master", "AuthEndpointTemplate": "{0}/protocol/openid-connect/auth", "MetadataEndpointTemplate": "{0}/.well-known/openid-configuration"}, "UseSentry": true, "DeadlockDetection": {"QueryTimeoutMinutes": 3, "QueryCountThreshold": 10, "AllowHealthCheckUpdate": false}, "HideIndividualPolicyExtraFieldsSettings": {"ClientIds": ["provider_portal"]}, "PubSubConfiguration": {"Environment": "", "SiloModel": {"Tenants": []}}, "AuditLogging": {"IsDisabled": true, "ExcludedOperations": []}, "CacheSettings": {"CacheTimeoutInSeconds": 5}}