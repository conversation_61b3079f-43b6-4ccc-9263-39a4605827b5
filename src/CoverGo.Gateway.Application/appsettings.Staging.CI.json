{"serviceUrls": {"gateway": "http://localhost:8080/", "products": "http://covergo-products:8080/", "pricing": "http://covergo-pricing:8080/", "l10n": "http://covergo-l10n:8080/", "logging": "http://covergo-logging:9200/", "policies": "http://covergo-policies:8080/", "premium": "http://covergo-premium:8080/", "auth": "http://covergo-auth:8080/", "users": "http://covergo-users:8080/", "notifications": "http://covergo-notifications:8080/", "claims": "http://covergo-claims:8080/", "commissions": "http://covergo-commissions:80/", "advisor": "http://covergo-advisor:8080/", "transactions": "http://covergo-transactions:8080/", "cms": "http://covergo-cms:8080/", "templates": "http://covergo-templates:8080/", "cases": "http://covergo-cases:8080/", "filesystem": "http://covergo-filesystem:8080/", "education": "http://covergo-education:8080", "achievements": "http://covergo-achievements:8080", "scheduler": "http://covergo-scheduler:8080", "fubonfps": "http://covergo-fubonfps:8080", "claimInvestigation": "http://covergo-claim-investigation/", "party": "http://covergo-party/", "reference": "http://covergo-reference/", "productbuilder": "http://covergo-product-builder:80/", "requestManager": "http://covergo-request-manager/", "printing": "http://covergo-printing/", "channelManagement": "http://covergo-channel-management/"}, "serviceTimeouts": {"fubonfps": "00:02:00"}, "FeatureManagement": {"PermissionV2": true, "ProposalIdIfStakeholderPermission": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["asia_dev", "coverHealth_dev", "asia_preprod", "asia"]}}]}}, "GraphQL": {"EnableMetrics": true}, "UseSentry": false}