﻿using System;
using System.Collections.Generic;
using System.Linq;
using GraphQL.Execution;
using GraphQL.Language.AST;
using GraphQL.Validation.Complexity;
using Microsoft.Extensions.Logging;

namespace CoverGo.Gateway.Application;

/// <summary>
/// Analyzes GraphQL requests for it's complexity.
///
/// Adopted from here:
/// https://github.com/graphql-dotnet/graphql-dotnet/blob/v2.4.0/src/GraphQL/Validation/Complexity/ComplexityAnalyzer.cs
/// </summary>
public class GraphQlComplexityAnalyzer
{
    private readonly ILogger<GraphQlComplexityAnalyzer> _logger;
    private readonly IDocumentBuilder _documentBuilder;
    
    // Max. number of times to traverse tree nodes. GraphiQL queries take ~95 iterations, adjust as needed.
    private readonly int _maxRecursionCount = 250;

    public GraphQlComplexityAnalyzer(ILogger<GraphQlComplexityAnalyzer> logger)
    {
        _logger = logger;
        _documentBuilder = new GraphQLDocumentBuilder();
    }

    public ComplexityResult TryAnalyze(string query)
    {
        try
        {
            var document = _documentBuilder.Build(query);
            return AnalyzeDocument(document);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "ComplexityAnalyzer failed");
            return null;
        }
    }
        
    private ComplexityResult AnalyzeDocument(Document doc, double avgImpact = 2.0d)
    {
        if (avgImpact <= 1) throw new ArgumentOutOfRangeException(nameof(avgImpact));

        var context = new AnalysisContext();

        foreach (var node in doc.Children.OfType<FragmentDefinition>())
        {
            var fragResult = new FragmentComplexity();
            FragmentIterator(context, node, fragResult, avgImpact, avgImpact, 1d);
            context.FragmentMap[node.Name] = fragResult;
        }

        TreeIterator(context, doc, avgImpact, avgImpact, 1d);

        return context.Result;
    }
        
    private void FragmentIterator(AnalysisContext context, INode node, FragmentComplexity qDepthComplexity, double avgImpact, double currentSubSelectionImpact, double currentEndNodeImpact)
    {
        if (context.LoopCounter++ > _maxRecursionCount)
        {
            throw new InvalidOperationException("Query is too complex to validate.");
        }

        if (node.Children != null &&
            node.Children.Any(
                n => n is Field || (n is SelectionSet set && set.Children.Any()) || n is Operation))
        {
            if (node is Field)
            {
                qDepthComplexity.Depth++;
                var impactFromArgs = GetImpactFromArgs(node);
                qDepthComplexity.Complexity += currentEndNodeImpact = impactFromArgs / avgImpact * currentSubSelectionImpact ?? currentSubSelectionImpact;
                foreach (var nodeChild in node.Children.Where(n => n is SelectionSet))
                    FragmentIterator(context, nodeChild, qDepthComplexity, avgImpact, currentSubSelectionImpact * (impactFromArgs ?? avgImpact), currentEndNodeImpact);
            }
            else
                foreach (var nodeChild in node.Children)
                    FragmentIterator(context, nodeChild, qDepthComplexity, avgImpact, currentSubSelectionImpact, currentEndNodeImpact);
        }
        else if (node is Field)
            qDepthComplexity.Complexity += currentEndNodeImpact;
    }

    private void TreeIterator(AnalysisContext context, INode node, double avgImpact, double currentSubSelectionImpact, double currentEndNodeImpact)
    {
        if (context.LoopCounter++ > _maxRecursionCount)
        {
            throw new InvalidOperationException("Query is too complex to validate.");
        }

        if (node is FragmentDefinition) return;

        if (node.Children != null &&
            node.Children.Any(n => n is Field || n is FragmentSpread || (n is SelectionSet set && set.Children.Any()) || n is Operation))
        {
            if (node is Field)
            {
                context.Result.TotalQueryDepth++;
                var impactFromArgs = GetImpactFromArgs(node);
                currentEndNodeImpact = impactFromArgs / avgImpact * currentSubSelectionImpact ?? currentSubSelectionImpact;
                RecordFieldComplexity(context, node, currentEndNodeImpact);
                foreach (var nodeChild in node.Children.Where(n => n is SelectionSet))
                    TreeIterator(context, nodeChild, avgImpact, currentSubSelectionImpact * (impactFromArgs ?? avgImpact), currentEndNodeImpact);
            }
            else
                foreach (var nodeChild in node.Children)
                    TreeIterator(context, nodeChild, avgImpact, currentSubSelectionImpact, currentEndNodeImpact);
        }
        else if (node is Field)
            RecordFieldComplexity(context, node, currentEndNodeImpact);
        else if (node is FragmentSpread spread)
        {
            var fragmentComplexity = context.FragmentMap[spread.Name];
            RecordFieldComplexity(context, spread, currentSubSelectionImpact / avgImpact * fragmentComplexity.Complexity);
            context.Result.TotalQueryDepth += fragmentComplexity.Depth;
        }
    }
        
    private static double? GetImpactFromArgs(INode node)
    {
        double? newImpact = null;
        if (!(node.Children.First(n => n is Arguments) is Arguments args)) return null;

        if (args.ValueFor("id") != null) newImpact = 1;
        else
        {
            if (args.ValueFor("first") is IntValue firstValue) newImpact = firstValue.Value;
            else
            {
                if (args.ValueFor("last") is IntValue lastValue) newImpact = lastValue.Value;
            }
        }
        return newImpact;
    }

    private static void RecordFieldComplexity(AnalysisContext context, INode node, double impact)
    {
        context.Result.Complexity += impact;
        if (context.Result.ComplexityMap.ContainsKey(node))
            context.Result.ComplexityMap[node] += impact;
        else
            context.Result.ComplexityMap.Add(node, impact);
    }
        
    private sealed class FragmentComplexity
    {
        public int Depth { get; set; }
        public double Complexity { get; set; }
    }
        
    private sealed class AnalysisContext
    {
        public ComplexityResult Result { get; } = new();
        public int LoopCounter { get; set; }
        public Dictionary<string, FragmentComplexity> FragmentMap { get; } = new();
    }
}