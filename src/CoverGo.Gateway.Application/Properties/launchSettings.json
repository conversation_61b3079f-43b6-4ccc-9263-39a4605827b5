{"profiles": {"CoverGo.Gateway.Application": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "datacenterId": "local", "DISABLE_PLAYGROUND": "false", "HIDE_GRAPHQL_SCHEMA": "false", "INTEGRATED_API_DISABLE_SSL": "true", "TCB_API_TOKEN": "0092B0D6625340FD2D3C444B7D4A82F6423D0B7CBAEA0072697133852F93F765", "TCB_API_PRIVATE_KEY": "\n        MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCJj9F3k9Z9w73R\n        PfEY0E9RM1tV8WzD/p5vtVenCs8ErIlQtkEQ59sy2L8Q+fQ4fEwRfGJ00Z+lhzr+\n        D8PcZpMLkHB4WX7tQdYnp0lSOKi+os/e0SWeIPKufUyBzvuG92KZf16yKXYrAuqJ\n        yY3V/3qnNGMBUaXLNFQXhWjIuLGsMS5YOl69Au0M0exox5MAvdwBN05z8cvVw8M5\n        Rz9PiJEm1b9Uvpo08cuUgEbIyCwdX+Wq+071h+RRjY9r+EeADV/1GTmUdOx2yvFf\n        es7ta8I7aHBaM9kj25+0Q1h+jsoyJlbR87obsCXdIkKRqW5LxzXewgzhdGhCEFcU\n        oBXDkPSNAgMBAAECggEAeQt4QkRwhRHswWZD9JW4dbc/M4zRLFWsjJeBnZdBOten\n        rMHy1zFuDHv1BLctU1bsiKZu7GRYmos4Ob8dlZ8c3RwoG36E1bkM/K+TMSpyMe2K\n        BIL9GvvbD/dpjv/XDJg6xt7xMMyTEp3qS3xztk6sWt0aEVsLDJfrNxdJcPY0QVNp\n        0w95KuvRdBwLyhBQH6l+BwJJ6dKn9yQn3A8hwepAwxihBmKDuO7QoWDeAMvDCSlq\n        3fEpXR/UE2Bj2pZwLsLyNsQF/L/DNUo3SJgY2AOr7wuMMsCbc9+o7uDHi6CNvhBE\n        nZZvqQt19Z/qUHr//VjYxmPmsIFYOAAiYdAjC8lvgQKBgQDNqv9of/PO3QE3D3AI\n        V+uT08Zo/cSWO7sEWq99EUPvOnvKWk8sB8Bhts1oxtQPyub1Q7ojKeUBA189sqVT\n        c5h848mGLe5OkT7/2zJsvWqIW00CPQ3iaDlnHCXJPeEHMRdNYN9DSYra6p0Qs9S+\n        kmb0khtvlBVqgRxetK8+MtDmGQKBgQCrOgAmeSBAZ4wYtO5gRihJ2CvfGqxXGc/J\n        QZv6EOY+P428O1syLb/+ay2oiNOUtZI5gtAiX8s535bkfRCjFwskDMqKAYRu0ZTo\n        d04l6576VFh2sqzDhSjimrxiLx3oXuB3dGeYlDIwSh/EpIGHd8dS73E7kSGDkCfc\n        4OpEqeRIlQKBgCGHSvQCSADlTplnTuBNVP5Ye/QJys6jFi3zisBc3DJX2m52oomP\n        CfHkF0CJiXC5p0U8sU+iOM8deZ1ZTlJ4edeCWwwg2MS+MQMM/P91MbThDYDb6zOJ\n        wjHpCM/x4omFm0LmE/+oMZbWwJxEdN5Mh7Q/TXG3NnaSbcmSsy70odGhAoGAIEuT\n        Ra5o59zf7rUJlP0Gkk8Ej1MPmGoU5+fJDJRtaOZBzHQBkmhQoyMB/Jy6E31ftBs5\n        OJKK2UKTykrQfYJWJpmHBhqdKO28wU/nXH2hgaOw/8pZjZMrD/qRd49de7uRaNZ6\n        x6uD/9mrPkKs76km3jytRcfs40yoSB/f781/cjUCgYA7N1rQQxvNpFrG/ZzrkVOV\n        yABUkoW7V10sauEoelcI6u4pMNOgFj0EHrcZdSb4wcecr8t3p0vjwTZXHfNkWmRP\n        B+4Ki+qAuoJr6TCba8ECje8Y8X0HHxWIVn1SrTimh6dFADHT+9M7s7eUTs8JHX8u\n        RPjQ12NqseYB0h9KjjJH3Q==", "TRACING_ENABLED": "false", "TRACING_CONNECTION_STRING": "http://localhost:4317", "TRACING_EXPORT_TIMEOUT": "1000", "BOC_API_ENDPOINT": "http://16t4ln9rru.shhttp.cn/apiServer", "DLVN_API_ENDPOINT": "http://*************:5012", "DLVN_USERNAME": "EAPP", "DLVN_PASSWORD": "2C64EEA6-BFBC-4731-8C36-1E73E9572EFE", "DLVN_API_ENCRYPTION_KEY": "iQfEzAFTqr61aJESXIi/6VZ7P5W0EtY6dib3+F02XK8=", "FeatureManagement:Reference": "true", "FeatureManagement:RequestManager": "true", "REMARK_SSO_API_ENCRYPTION_KEY": "YWVzU09QZGx2bk9ubGluZVBheW1lbnRQcml2YXRlMDE=", "BOCL_IDP_SERVICE_ENTITY_ID": "LWSHK", "BOCL_IDP_SERVICE_URL": "https://dums01.bochk.com/adfs/ls", "BOCL_IDP_ISSUER_URL": "http://dums01.bochk.com/adfs/services/trust", "BOCL_SAML_SSO_CERTIFICATE": ""}, "applicationUrl": "http://localhost:60060/"}, "Developer Machine": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_URLS": "http://localhost:60060", "ASPNETCORE_ENVIRONMENT": "Development", "datacenterId": "developer-machine", "PROXY_GRAPHQL_PRODUCTBUILDER_ENABLED": "true", "USE_HOTCHOCOLATE": "true", "DISABLE_AZURE_LOGIN": "true", "REMARK_SSO_API_ENCRYPTION_KEY": "YWVzU09QZGx2bk9ubGluZVBheW1lbnRQcml2YXRlMDE=", "BOCL_IDP_SERVICE_ENTITY_ID": "LWSHK", "BOCL_IDP_SERVICE_URL": "https://dums01.bochk.com/adfs/ls", "BOCL_IDP_ISSUER_URL": "http://dums01.bochk.com/adfs/services/trust", "BOCL_SAML_SSO_CERTIFICATE": "", "TCB_IAM_USERNAME": "someUsername", "TCB_IAM_PASSWORD": "somePassword", "TCB_DEPLOYMENT_USERNAME": "someUsername", "TCB_DEPLOYMENT_PASSWORD": "somePassword", "TCB_PERMISSIONGROUPS_TENANTID": "", "DLVN_ENABLE_VALIDATE_PAYMENT_OTP": "false", "FeatureManagement:DLVNIntegrationServices": "false"}, "applicationUrl": "http://localhost:60060/"}, "DBS": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "datacenterId": "dbs-hk", "Serilog__WriteTo__Performance__Args__configureLogger__WriteTo__0__Args__host": "localhost", "Serilog__WriteTo__Performance__Args__configureLogger__WriteTo__0__Args__port": "5066", "Serilog__WriteTo__Application__Args__configureLogger__WriteTo__0__Args__host": "localhost", "Serilog__WriteTo__Application__Args__configureLogger__WriteTo__0__Args__port": "6031"}, "applicationUrl": "http://localhost:60060/"}, "Docker": {"commandName": "<PERSON>er", "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "publishAllPorts": true}}}