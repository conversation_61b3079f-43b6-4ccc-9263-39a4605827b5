using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;

using Serilog;
using Serilog.Events;

namespace CoverGo.Gateway.Application.Logging
{
    public class SerilogDBSMiddleware
    {
        const string MessageTemplate =
            "[Timestamp: {timestamp_log} id: {msg_uid}] HTTP {request_type} {request_path} from {client_ip} responed {StatusCode} in {execution_time_milliseconds} ms {serviceID} {func_map}";

        static readonly ILogger Log = Serilog.Log.ForContext<SerilogDBSMiddleware>();

        static readonly HashSet<string> HeaderWhitelist = new() { "Content-Type", "Content-Length", "User-Agent" };

        readonly RequestDelegate _next;

        public SerilogDBSMiddleware(RequestDelegate next)
        {
            if (next == null) throw new ArgumentNullException(nameof(next));
            _next = next;
        }

        public async Task Invoke(HttpContext httpContext)
        {
            if (httpContext == null) throw new ArgumentNullException(nameof(httpContext));

            var start = Stopwatch.GetTimestamp();
            try
            {
                await _next(httpContext);
                var elapsedMs = GetElapsedMilliseconds(start, Stopwatch.GetTimestamp());

                var statusCode = httpContext.Response?.StatusCode;
                var level = statusCode > 499 ? LogEventLevel.Error : LogEventLevel.Information;

                var timeStamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff");
                var uuid = Guid.NewGuid();
                var clientIp = httpContext.Connection.RemoteIpAddress;
                var elapsed = Math.Round(elapsedMs, MidpointRounding.AwayFromZero);
                httpContext.Request.Headers.TryGetValue("serviceID", out Microsoft.Extensions.Primitives.StringValues serviceID);
                httpContext.Request.Headers.TryGetValue("func_map", out Microsoft.Extensions.Primitives.StringValues funcMap);

                var log = level == LogEventLevel.Error ? LogForErrorContext(httpContext) : Log;
                log.Write(level, MessageTemplate, timeStamp, uuid, httpContext.Request.Method, GetPath(httpContext),
                    clientIp, statusCode.ToString(), elapsed, serviceID.FirstOrDefault() ?? string.Empty, funcMap.FirstOrDefault() ?? string.Empty);

                //var logItems = new LogItems { client_ip = clientIp.ToString(), StatusCode = statusCode.ToString() };
                //Log.Information("{@LogItems}", logItems);
            }
            // Never caught, because `LogException()` returns false.
            catch (Exception ex) when (LogException(httpContext, GetElapsedMilliseconds(start, Stopwatch.GetTimestamp()), ex)) { }
        }

        static bool LogException(HttpContext httpContext, double elapsedMs, Exception ex)
        {
            LogForErrorContext(httpContext)
                .Error(ex, MessageTemplate, httpContext.Request.Method, GetPath(httpContext), 500, elapsedMs);

            return false;
        }

        static ILogger LogForErrorContext(HttpContext httpContext)
        {
            var request = httpContext.Request;

            var loggedHeaders = request.Headers
                .Where(h => HeaderWhitelist.Contains(h.Key))
                .ToDictionary(h => h.Key, h => h.Value.ToString());

            var result = Log
                .ForContext("RequestHeaders", loggedHeaders, destructureObjects: true)
                .ForContext("RequestHost", request.Host)
                .ForContext("RequestProtocol", request.Protocol);

            return result;
        }

        static double GetElapsedMilliseconds(long start, long stop)
        {
            return (stop - start) * 1000 / (double)Stopwatch.Frequency;
        }

        static string GetPath(HttpContext httpContext)
        {
            return httpContext.Features.Get<IHttpRequestFeature>()?.RawTarget ?? httpContext.Request.Path.ToString();
        }
    }
}
