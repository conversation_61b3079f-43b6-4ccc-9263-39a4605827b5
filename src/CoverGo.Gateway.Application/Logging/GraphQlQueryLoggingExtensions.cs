﻿using CoverGo.Gateway.Interfaces;
using GraphQL;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace CoverGo.Gateway.Application.Logging;

public static class GraphQlQueryLoggingExtensions
{
    static readonly string[] ChangePasswordVariables = new string[] { "changePasswordInput", "changeExpiredPasswordInput" };
    static readonly Regex PasswordRegex = new("(P|p)assword:(.*?)\"(.*?)\"");

    public static string MaskQuery(string query)
    {
        if (string.IsNullOrEmpty(query)) return query;

        MatchCollection matches = PasswordRegex.Matches(query);
        foreach (Match match in matches)
            try
            {
                string password = match.Groups[3].Value;
                query = query.Replace(password, "*****");
            }
            catch
            {
                // do nothing
            }

        return query;
    }

    public static void MaskVariables(Inputs variables)
    {
        if (variables != null)
        {
            if (variables.ContainsKey("password"))
                variables["password"] = "*****";

            foreach (string changePasswordVariableName in ChangePasswordVariables)
            {
                if (variables.ContainsKey(changePasswordVariableName))
                {
                    JObject passwordInput = JObject.FromObject(variables[changePasswordVariableName]);

                    if (passwordInput.ContainsKey("currentPassword"))
                        passwordInput["currentPassword"] = "*****";

                    if (passwordInput.ContainsKey("newPassword"))
                        passwordInput["newPassword"] = "*****";

                    variables[changePasswordVariableName] = passwordInput.ToDictionary<object>();
                }

            }
        }
    }

    public static void AddTargetIdsToLoggingProperties(Inputs variables, Dictionary<string, object> loggingProperties)
        => AddVariablesToLoggingProperties(variables, loggingProperties, new[] { "id" }, "TargetIds");
    
    public static void AddPagingVariablesToLoggingProperties(Inputs variables, Dictionary<string, object> loggingProperties)
        => AddVariablesToLoggingProperties(variables, loggingProperties, new[] { "skip", "limit", "first" }, "PagingVariables");

    private static void AddVariablesToLoggingProperties(Inputs variables, Dictionary<string, object> loggingProperties, string[] variableNames, string propertyName)
    {
        if (variables == null) return;
        
        IEnumerable<KeyValuePair<string, object>> pagingVariables = variables
            .Where(v => variableNames.Any(n => v.Key.Contains(n, StringComparison.InvariantCultureIgnoreCase)))
            .ToDictionary(v => v.Key, v => v.Value);
        
        object inputVariable = variables.FirstOrDefault(v => v.Key == "input").Value;
        if (inputVariable != null)
        {
            string json = JsonConvert.SerializeObject(inputVariable);

            IDictionary<string, object> inputVariables = null;
            try {
                inputVariables = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);
            }
            catch (Exception) {
                inputVariables = new Dictionary<string, object>
                {
                    { "input", json }
                };
            }
             

            if (inputVariables != null)
            {
                pagingVariables = pagingVariables.Concat(
                    inputVariables
                        .Where(v => variableNames.Any(n => v.Key.Contains(n, StringComparison.InvariantCultureIgnoreCase)))
                        .ToDictionary(v => $"input.{v.Key}", v => v.Value).ToList()).ToList();
            }
        }
        
        if(pagingVariables.Any())
            loggingProperties.Add(propertyName, new Dictionary<string, object>(pagingVariables));
    }
}
