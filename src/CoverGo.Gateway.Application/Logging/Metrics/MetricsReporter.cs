using CoverGo.Gateway.Common;
using GraphQLParser;
using GraphQLParser.AST;
using Microsoft.AspNetCore.Http;
using Prometheus;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using GraphQL;
using GraphQL.Instrumentation;
using GraphQL.Validation.Complexity;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using ISource = GraphQLParser.ISource;
using Metrics = Prometheus.Metrics;
using Parser = GraphQLParser.Parser;

namespace CoverGo.Gateway.Application
{
    public class MetricsReporter
    {
        public MetricsReporter(IHttpContextAccessor httpContextAccessor, ILogger<MetricsReporter> logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
            
            _graphqlResponseTimeHistogram = Metrics.CreateHistogram(
                "covergo_long_graqhql_request",
                "Long (>1s) graphQL queries in milliseconds",
                new HistogramConfiguration
                {
                    Buckets = Histogram.ExponentialBuckets(1000, 4, 3),
                    LabelNames = new[] { "query_name", "root_types", "tenantId", "appId", "query_hash" }
                });
            _graphQlFieldsMetricCounter = Metrics.CreateSummary(
                "graphql_fields_resolved",
                "Resolved GraphQL fields", new SummaryConfiguration()
                {
                    LabelNames = new [] { "parent_type_name", "field_name" }
                });
            _graphQlHeavyFieldsMetricCounter = Metrics.CreateSummary(
                "graphql_heavy_fields_resolved",
                "Resolved heavy (resolve duration > 0.5ms) GraphQL fields.", new SummaryConfiguration()
                {
                    LabelNames = new [] { "parent_type_name", "field_name" }
                });
            _graphQlQueryComplexityCounter = Metrics.CreateSummary(
                "graphql_query_complexity",
                "Complexity for GraphQL queries", new SummaryConfiguration()
                {
                    LabelNames = new [] { "query_name", "root_types", "query_hash", "tenantId" }
                });
            _graphQlQueryDepthCounter = Metrics.CreateSummary(
                "graphql_query_depth_total",
                "Total depth for GraphQL queries", new SummaryConfiguration()
                {
                    LabelNames = new [] { "query_name", "root_types", "query_hash", "tenantId" }
                });
            _graphQlQueryResponseSizeCounter = Metrics.CreateSummary(
                "graphql_query_response_size_kb",
                "Response size executed GraphQL queries", new SummaryConfiguration()
                {
                    LabelNames = new [] { "query_name", "root_types", "query_hash", "tenantId" }
                });
            _graphQlInitiatedHttpRequestCounter = Metrics.CreateCounter(
                "graphql_initiated_httpclient_requests_total",
                "Count of HTTP requests that have been caused by a GraphQl query.",
                new CounterConfiguration()
                {
                    LabelNames = new[] { "query_name", "query_hash", "tenantId", "host", "method" }
                });
        }

        readonly Histogram _graphqlResponseTimeHistogram;
        readonly Summary _graphQlFieldsMetricCounter;
        readonly Summary _graphQlHeavyFieldsMetricCounter;
        readonly Summary _graphQlQueryComplexityCounter;
        readonly Summary _graphQlQueryResponseSizeCounter;
        readonly Summary _graphQlQueryDepthCounter;
        readonly Counter _graphQlInitiatedHttpRequestCounter;
        readonly TimeSpan _longQueryDuration = TimeSpan.FromSeconds(1);
        private readonly IHttpContextAccessor _httpContextAccessor;
        readonly ILogger<MetricsReporter> _logger;

        void RegisterGraphqlResponseTime(TimeSpan elapsed, params string[] labels) =>
            _graphqlResponseTimeHistogram.Labels(labels).Observe(elapsed.TotalMilliseconds);
        
        public void RegisterGraphQlResponseSize(GraphQlBodyInfo info, ExecutionResult result)
        {
            if (!bool.TryParse(Environment.GetEnvironmentVariable("PROMETHEUS_GRAPHQL_COMPLEXITY_METRICS_ENABLED"), out bool enabled) || !enabled)
                return;
            
            if (result?.Data == null) return;
            
            string serializedResult = JsonConvert.SerializeObject(result.Data, Formatting.None);
            if (string.IsNullOrWhiteSpace(serializedResult)) return;

            int bytes = Encoding.UTF8.GetByteCount(serializedResult);
            int kb = bytes / 1000;

            if (kb == 0) return;
            
            QueryInfo queryInfo = GetQueryInfo(info);
            
            _graphQlQueryResponseSizeCounter.Labels(
                queryInfo.QueryName, 
                queryInfo.RootTypes, 
                queryInfo.QueryHash, 
                GetTeanantId()).Observe(kb);
        }

        public void RegisterGraphQlComplexity(GraphQlBodyInfo graphQlInfo, ComplexityResult complexityResult)
        {
            if (graphQlInfo == null || complexityResult == null || string.IsNullOrWhiteSpace(graphQlInfo.Query))
                return;
            
            QueryInfo queryInfo = GetQueryInfo(graphQlInfo);
            
            _graphQlQueryComplexityCounter.Labels(
                queryInfo.QueryName, 
                queryInfo.RootTypes, 
                queryInfo.QueryHash, 
                GetTeanantId()).Observe(complexityResult.Complexity);
            
            _graphQlQueryDepthCounter.Labels(
                queryInfo.QueryName, 
                queryInfo.RootTypes, 
                queryInfo.QueryHash, 
                GetTeanantId()).Observe(complexityResult.TotalQueryDepth);
        }
        
        public void RegisterGraphQlInitiatedHttpRequest(GraphQlBodyInfo graphQlInfo, string host, string method)
        {
            if (string.IsNullOrWhiteSpace(graphQlInfo?.Query))
                return;
            
            QueryInfo queryInfo = GetQueryInfo(graphQlInfo);
            
            _graphQlInitiatedHttpRequestCounter.Labels(
                queryInfo.QueryName,
                queryInfo.QueryHash,
                GetTeanantId(),
                host,
                method).Inc();
        }

        public void RegisterGraphqlResponseTime(GraphQlBodyInfo graphQlInfo, TimeSpan elapsed)
        {
            if (elapsed < _longQueryDuration) 
                return;
            
            if (graphQlInfo == null || string.IsNullOrWhiteSpace(graphQlInfo.Query)) 
                return;

            try
            {
                QueryInfo queryInfo = GetQueryInfo(graphQlInfo);
                RegisterGraphqlResponseTime(
                    elapsed,
                    queryInfo.QueryName,
                    queryInfo.RootTypes,
                    GetTeanantId(),
                    GetAppId(),
                    queryInfo.QueryHash);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cannot register metrics");
            }
        }

        private string GetTeanantId() => 
            _httpContextAccessor.HttpContext?.GetTenantId() ?? "unknown";
        
        private string GetAppId() => 
            _httpContextAccessor.HttpContext?.GetClaim("appId") ?? "unknown";

        public void RegisterGraphqlQueryPerformance(ExecutionResult executionResult)
        {
            if (executionResult.Perf == null)
                return;
            
            foreach (PerfRecord perfRecord in executionResult.Perf)
            {
                if (perfRecord.Metadata == null)
                    continue;
                    
                var fieldName = perfRecord.MetaField<string>("fieldName");
                var parentTypeName = perfRecord.MetaField<string>("typeName");
                    
                _graphQlFieldsMetricCounter.Labels(parentTypeName, fieldName).Observe(perfRecord.Duration);
                if (perfRecord.Duration > 0.5)
                    _graphQlHeavyFieldsMetricCounter.Labels(parentTypeName, fieldName).Observe(perfRecord.Duration);
            }
        }

        static QueryInfo GetQueryInfo(
            GraphQlBodyInfo graphQlInfo)
        {
            GraphQLOperationDefinition operation = GetOperationDefinition(graphQlInfo.Query);

            IEnumerable<string> graphqlTypeNames = operation
                ?.SelectionSet
                .Selections
                .Select(o => (GraphQLFieldSelection)o)
                .Select(f => f.Name.Value);

            string typeNames = graphqlTypeNames != null ? string.Join(", ", graphqlTypeNames) : "invalid query";
            string operationName = graphQlInfo.OperationName ?? operation?.Name?.Value ?? "";
            string queryHash = Encryption.GetStringHash(graphQlInfo.Query);

            return new QueryInfo(typeNames, operationName, queryHash);
        }

        static GraphQLOperationDefinition GetOperationDefinition(string query)
        {
            Parser parser = new(new Lexer());
            GraphQLDocument document = parser.Parse((ISource)new QuerySource { Body = query });

            return document.Definitions.FirstOrDefault(n => n.Kind == ASTNodeKind.OperationDefinition) as GraphQLOperationDefinition;
        }

        class QuerySource : ISource
        {
            public string Body { get; set; }
            public string Name { get; set; }
        }

        record QueryInfo(string RootTypes, string QueryName, string QueryHash);
    }
}