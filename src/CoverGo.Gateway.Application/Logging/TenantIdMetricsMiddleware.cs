using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using System;
using System.Threading.Tasks;
using CoverGo.Gateway.Common;
using Microsoft.AspNetCore.Routing;

namespace CoverGo.Gateway.Application.Logging
{
    public class TenantIdMetricsMiddleware
    {
        private readonly RequestDelegate _next;

        public TenantIdMetricsMiddleware(RequestDelegate next)
        {
            _next = next ?? throw new ArgumentNullException(nameof(next));
        }

        public async Task Invoke(HttpContext httpContext)
        {
            string tenantId = httpContext.GetClaim("tenantId");
            // tenantId stored in route data is used by Prometheus metrics exporter to collect per-tenant statistics
            if (!string.IsNullOrEmpty(tenantId))
                httpContext.GetRouteData().Values.Add("tenantId", tenantId);

            await _next(httpContext);
        }
    }
}
