using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using System;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Application.Logging
{
    /// <summary>
    /// It writes trace identifier (e.g. trace ID in Tempo) of the current request to response headers.
    /// This allows a caller of Gateway service to find logs of his request in Tempo by using
    /// trace ID from the response headers. Also this identifier can be used as a reference in
    /// support incident investigations (can ask customer to provide it).
    /// </summary>
    public class TraceIdentifierHeaderMiddleware
    {
        private readonly RequestDelegate _next;

        public TraceIdentifierHeaderMiddleware(RequestDelegate next)
        {
            _next = next ?? throw new ArgumentNullException(nameof(next));
        }

        public async Task Invoke(HttpContext httpContext)
        {
            if (httpContext.Response.HasStarted)
                return;
            
            IHttpActivityFeature activityFeature = httpContext.Features.Get<IHttpActivityFeature>();
            if (activityFeature != null)
                httpContext.Response.Headers["X-CoverGo-Ref"] = activityFeature.Activity.TraceId.ToHexString();

            await _next(httpContext);
        }
    }
}
