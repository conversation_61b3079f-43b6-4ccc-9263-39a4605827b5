using System;
using System.Threading.Tasks;
using CoverGo.Gateway.Common;
using Microsoft.AspNetCore.Http;
using Serilog;
using Serilog.Context;

namespace CoverGo.Gateway.Application.Logging
{
    /*
     * This Middleware is used to recover the TenantId and AppId from the request header
     * and push it as a property in Serilog context.
     */
    public class LoggerPropertyProviderMiddleware
    {
        readonly RequestDelegate _next;

        public LoggerPropertyProviderMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            IDisposable PushProperty(string propertyName, string alias = null)
            {
                try
                {
                    string property = context.GetClaim(propertyName);

                    if (property != null)
                        return LogContext.PushProperty(alias ?? propertyName, property);
                }
                catch
                {
                    Log.Information($"{nameof(LoggerPropertyProviderMiddleware)} couldn't extract {propertyName} information from request");
                }

                return null;
            }

            using (PushProperty("appId", "AppId"))
            using (PushProperty("tenantId", "TenantId"))
            using (PushProperty("sub", "LoginId"))
                await _next.Invoke(context);
        }
    }
}