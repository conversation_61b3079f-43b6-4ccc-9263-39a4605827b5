﻿using System;

using GraphQL;

using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace CoverGo.Gateway.Application.GraphQl.Compatibility;

/// <summary>
/// Without this we can't override attribute in JsonConverter
/// </summary>
public class SpecCompliantContractResolver : DefaultContractResolver
{
    protected override JsonConverter ResolveContractConverter(Type objectType)
    {
        if (objectType == typeof(ExecutionResult))
            return new SpecCompliantExecutionResultJsonConverter();

        return base.ResolveContractConverter(objectType);
    }
}
