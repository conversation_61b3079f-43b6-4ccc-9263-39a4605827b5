﻿using CoverGo.HotChocolate.SchemaFederations;

using HotChocolate.Execution.Configuration;

using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Gateway.Application.GraphQl.Compatibility;

public class CompatibilityRequestExecutorBuilderInterceptor : DefaultRequestExecutorBuilderInterceptor
{
    public override IRequestExecutorBuilder OnCreate(IRequestExecutorBuilder remoteSchemaExecutor)
    {
        return remoteSchemaExecutor
            .UseOrdinalIgnoreCaseEnumValueCheck();
    }
}
