﻿using System.Collections;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Gateway.Interfaces;
using GraphQL.Instrumentation;
using GraphQL.Types;
using Serilog;

namespace CoverGo.Gateway.Application.GraphQl;

public class WatchingLimitsFieldsMiddleware
{
    private static readonly ILogger Log = Serilog.Log.ForContext<WatchingLimitsFieldsMiddleware>();
    
    public async Task<object> Resolve(ResolveFieldContext context, FieldMiddlewareDelegate next)
    {
        object fieldResult = await next(context);

        if (!context.FieldDefinition.Metadata.ContainsKey("requirePageLimit"))
            return fieldResult;
        
        if (fieldResult is IEnumerable fieldResultAsEnumerable)
        {
            int count = fieldResultAsEnumerable.Cast<object>().Count();
            if (count > 10)
            {
                int? limit = context.ComputeArgAndVar<int?>("limit");
                Log.Information($"Field {context.ParentType.Name}.{context.FieldName} returned {0} elements, because limit {1} was specified", count, limit);
            }
        }
      
        return fieldResult;
    }
}