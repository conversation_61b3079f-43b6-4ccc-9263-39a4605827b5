﻿using System;
using CoverGo.Gateway.Infrastructure.Configuration;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using StackExchange.Redis;

namespace CoverGo.Gateway.Application.Extensions;

public static class DataProtectionServiceCollectionExtension
{
    public static void AddDataProtectionWithRedis(this IServiceCollection services, IConfiguration configuration, ILogger logger)
    {
        if (services == null) throw new ArgumentNullException(nameof(services));
        if (configuration == null) throw new ArgumentNullException(nameof(configuration));
        if (logger == null) throw new ArgumentNullException(nameof(logger));

        try
        {
            RedisConfiguration redisConfiguration = RedisConfigurationHelper.GetRedisConfiguration(configuration, logger);
            if (string.IsNullOrEmpty(redisConfiguration?.ConnectionString)) return;

            IDataProtectionBuilder dataProtectionBuilder = services.AddDataProtection()
                .SetApplicationName(Program.AppName);

            ConfigureDataProtectionWithRedis(dataProtectionBuilder, redisConfiguration, logger);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error configuring data protection with Redis");
        }
    }

    private static void ConfigureDataProtectionWithRedis(IDataProtectionBuilder builder, RedisConfiguration redisConfiguration, ILogger logger)
    {
        try
        {
            ConnectionMultiplexer redis = ConnectionMultiplexer.Connect(redisConfiguration.ConnectionString);
            builder.PersistKeysToStackExchangeRedis(redis, "DataProtection-Keys");
        }
        catch (RedisConnectionException ex)
        {
            logger.Error(ex, "Failed to connect to Redis");
            throw;
        }
    }
}