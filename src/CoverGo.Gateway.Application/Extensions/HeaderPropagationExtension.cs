﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Gateway.Application.Http;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.HeaderPropagation;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Primitives;
using Microsoft.Net.Http.Headers;

namespace CoverGo.Gateway.Application.Extensions;

public static class AddAuthorizationPropagationExtension
{
    // https://github.com/dotnet/aspnetcore/blob/main/src/Middleware/HeaderPropagation/src/DependencyInjection/HeaderPropagationHttpClientBuilderExtensions.cs
    public static IHttpClientBuilder AddAuthorizationPropagation(this IHttpClientBuilder builder)
    {
        ArgumentNullException.ThrowIfNull(builder);

        builder.Services.AddHeaderPropagation(); // builder.Services.TryAddSingleton<HeaderPropagationValues>();
        builder.AddHttpMessageHandler(services => new AuthorizationPropagationMessageHandler(services.GetRequiredService<HeaderPropagationValues>()));
        return builder;
    }

    private static readonly string _unableToFindServices = string.Format(
        CultureInfo.CurrentCulture,
        "Unable to find the required services. Please add all the required services by calling '{0}.{1}' inside the call to 'ConfigureServices(...)' in the application startup code.",
        nameof(IServiceCollection),
        nameof(AddAuthorizationPropagation));
    public static IApplicationBuilder UseAuthorizationPropagation(this IApplicationBuilder app)
    {
        ArgumentNullException.ThrowIfNull(app);

        if (app.ApplicationServices.GetService<HeaderPropagationValues>() == null)
        {
            throw new InvalidOperationException(_unableToFindServices);
        }

        return app.UseMiddleware<AuthorizationPropagationMiddleware>();
    }
}

// https://github.com/dotnet/aspnetcore/blob/main/src/Middleware/HeaderPropagation/src/HeaderPropagationMiddleware.cs#L14
internal class AuthorizationPropagationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly HeaderPropagationValues _values;

    public AuthorizationPropagationMiddleware(RequestDelegate next, HeaderPropagationValues values)
    {
        ArgumentNullException.ThrowIfNull(next);
        ArgumentNullException.ThrowIfNull(values);

        _next = next;
        _values = values;
    }

    public Task Invoke(HttpContext context)
    {
        // We need to intialize the headers because the message handler will use this to detect misconfiguration.
        var headers = _values.Headers ??= new Dictionary<string, StringValues>(StringComparer.OrdinalIgnoreCase);

        var authorization = GetValue(context, HeaderNames.Authorization);
        if (!StringValues.IsNullOrEmpty(authorization)) headers.Add(HeaderNames.Authorization, authorization);

        var tenant = GetValue(context, CoverGoHeaderNames.Tenant);
        if (!StringValues.IsNullOrEmpty(authorization)) headers.Add(CoverGoHeaderNames.Tenant, tenant);

        return _next.Invoke(context);
    }

    private static StringValues GetValue(HttpContext context, string header)
    {
        context.Request.Headers.TryGetValue(header, out var value);
        return value;
    }
}
// https://github.com/dotnet/aspnetcore/blob/main/src/Middleware/HeaderPropagation/src/HeaderPropagationMessageHandler.cs
internal class AuthorizationPropagationMessageHandler : DelegatingHandler
{
    private readonly HeaderPropagationValues _values;
    public AuthorizationPropagationMessageHandler(HeaderPropagationValues values)
    {
        _values = values ?? throw new ArgumentNullException(nameof(values));
    }
    protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var captured = _values.Headers;
        if (captured != null)
        {
            foreach (var pair in captured)
            {
                var (key, stringValue) = pair;

                bool hasContent = request.Content != null;
                if (request.Headers.TryGetValues(key, out var _)
                || (hasContent && request.Content.Headers.TryGetValues(key, out var _))
                || StringValues.IsNullOrEmpty(stringValue)) continue;

                if (stringValue.Count == 1)
                {
                    var value = stringValue.ToString();
                    if (!request.Headers.TryAddWithoutValidation(key, value) && hasContent) request.Content.Headers.TryAddWithoutValidation(key, value);
                }
                else
                {
                    var values = stringValue.ToArray();
                    if (!request.Headers.TryAddWithoutValidation(key, values) && hasContent) request.Content.Headers.TryAddWithoutValidation(key, values);
                }
            }
        }

        return base.SendAsync(request, cancellationToken);
    }
}


