﻿using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Http;

namespace CoverGo.Gateway.Application.Extensions;

public static class HttpContextExtension
{
    public static T GetValueFromItems<T>(this HttpContext context, object key)
    {
        context.Items.TryGetValue(key, out object value);

        if (value is T result) return result;

        return default;
    }
}