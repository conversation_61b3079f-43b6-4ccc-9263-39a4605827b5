﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Threading.Tasks;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.Gateway.Application.Configuration;
using CoverGo.Gateway.Common;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Cache;
using CoverGo.Gateway.Infrastructure.Auth;
using CoverGo.Gateway.Infrastructure.Configuration;
using IdentityModel;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;
using ILogger = Serilog.ILogger;
using SSOConfig = CoverGo.Gateway.Domain.Auth.SSOConfig;
using TenantSettings = CoverGo.Gateway.Domain.Auth.TenantSettings;

namespace CoverGo.Gateway.Application.Extensions;

public static class AuthenticationServiceCollectionExtension
{
    public static void AddKeyCloakAuthentication(this IServiceCollection services, IConfiguration configuration, ILogger logger, bool requireHttps = true)
    {
        if (services == null) throw new ArgumentNullException(nameof(services));
        if (configuration == null) throw new ArgumentNullException(nameof(configuration));
        if (logger == null) throw new ArgumentNullException(nameof(logger));

        KeyCloakDefaultConfiguration defaultConfiguration = GetDefaultConfiguration(configuration);

        if (!defaultConfiguration.IsAuthorityValid())
        {
            logger.Warning("The default Authority config doesn't exist");
            return;
        }

        services.Configure<CookiePolicyOptions>(options =>
        {
            options.Secure = CookieSecurePolicy.Always;
            options.MinimumSameSitePolicy = SameSiteMode.None;
        });

        try
        {
            RedisConfiguration redisConfiguration = RedisConfigurationHelper.GetRedisConfiguration(configuration, logger);
            services.AddStackExchangeRedisCache(action => {
                action.InstanceName = $"{Program.AppName}-";
                action.Configuration = redisConfiguration.ConnectionString;
            });
        }
        catch (Exception e)
        {
            logger.Warning(e, "KeyCloakAuthentication Failed to configure stack exchange redis cache");
            return;
        }

        services.AddOidcStateDataFormatterCache();

        services
            .AddAuthentication(options =>
            {
                options.DefaultChallengeScheme = OpenIdConnectDefaults.AuthenticationScheme;
            })
            .AddCookie()
            .AddOpenIdConnect(KeyCloakDefaults.AuthenticationScheme, KeyCloakDefaults.DisplayName, options =>
            {
                ConfigureKeyCloakOptions(options, defaultConfiguration, logger, requireHttps);
            });
    }

    public static void AddAxaHkSSOAuthentication(this IServiceCollection? services, IConfiguration? configuration, ILogger? logger)
    {
        if (services == null) throw new ArgumentNullException(nameof(services));
        if (configuration == null) throw new ArgumentNullException(nameof(configuration));
        if (logger == null) throw new ArgumentNullException(nameof(logger));

        try
        {
            RedisConfiguration redisConfiguration = RedisConfigurationHelper.GetRedisConfiguration(configuration, logger);
            services.AddStackExchangeRedisCache(action => {
                action.InstanceName = $"{Program.AppName}-";
                action.Configuration = redisConfiguration.ConnectionString;
            });
        }
        catch (Exception e)
        {
            logger.Error(e, "AddAxaHkSSOAuthentication Failed to configure stack exchange redis cache");
            return;
        }

    }

    public static IServiceCollection AddOidcStateDataFormatterCache(
        this IServiceCollection services,
        params string[] schemes)
    {
        services.AddSingleton<IPostConfigureOptions<OpenIdConnectOptions>>(
            svc => new OpenIdConnectOptionsConfigurer(
                schemes,
                svc.GetRequiredService<IHttpContextAccessor>())
        );

        return services;
    }

    private static KeyCloakDefaultConfiguration GetDefaultConfiguration(IConfiguration configuration)
    {
        KeyCloakDefaultConfiguration defaultConfiguration = new();
        configuration?.GetSection(AuthProviderIds.KeyCloak).Bind(defaultConfiguration);
        return defaultConfiguration;
    }

    private static void ConfigureKeyCloakOptions(OpenIdConnectOptions options, KeyCloakDefaultConfiguration defaultConfiguration, ILogger logger, bool requireHttps)
    {
        SetupDefaultConfiguration(options, defaultConfiguration, requireHttps);
        SetupIdpRedirectConfiguration(options, defaultConfiguration, requireHttps);
        SetupMessageReceivedConfiguration(options, defaultConfiguration, requireHttps);
        SetupTicketReceivedConfiguration(options, logger);
        HandleRemoteFailure(options, logger);
    }

    private static void SetupTicketReceivedConfiguration(OpenIdConnectOptions options, ILogger logger) =>
        options.Events.OnTicketReceived = async context =>
        {
            if (context.Properties != null)
            {
                string tenantId = context.Properties.Items["tenantId"];
                string clientId = context.Properties.Items["clientId"];

                ValidateTenantAndClient(tenantId, clientId);

                try
                {
                    Login user = await GetLoginAsync(tenantId, context);
                    if (user == null) return;

                    IReadOnlyCollection<string> authorizedClientIds = GetAuthorizedClientIdsFromLogin(user);

                    if (authorizedClientIds == null || !authorizedClientIds.Any(x => x == clientId || x == UserClaimValues.All))
                    {
                        logger.Error("User {Id} doesn't have access to client {ClientId}", user.Id, clientId);
                        await HandleFailedResult(context, tenantId, clientId, KeyCloakErrorMessages.AccessDenied);
                    }
                }
                catch (Exception e)
                {
                    logger.Error(e,"Failed to authorize with client {ClientId}",  clientId);
                    await HandleFailedResult(context, tenantId, clientId, KeyCloakErrorMessages.AccessDenied);
                }
            }
        };

    private static async Task HandleFailedResult(TicketReceivedContext context, string tenantId, string clientId, string errorMessage)
    {
        SSOConfig ssoConfig = await GetSSOConfiguration(context.HttpContext, tenantId, clientId);

        context.Fail(errorMessage);
        context.Response.Redirect($"{ssoConfig.ErrorPath}?error={Uri.EscapeDataString(errorMessage)}");
        context.HandleResponse();
    }

    private static IReadOnlyCollection<string> GetAuthorizedClientIdsFromLogin(Login user) =>
        user.PermissionGroups
            .SelectMany(g =>
            (
                g.TargettedPermissions?.Any(p => p.Key == UserClaim.ClientId.ToString()) == true
                    ? g.TargettedPermissions.Where(p => p.Key == UserClaim.ClientId.ToString())
                    : new Dictionary<string, IEnumerable<string>>()
            ).SelectMany(p => p.Value))
            .ToArray();

    private static string GetClaimValueFromTicketReceivedContext(TicketReceivedContext context, string name) =>
        context.Principal.FindFirstValue(name);

    private static async Task<Login> GetLoginAsync(string tenantId, TicketReceivedContext context)
    {
        string username = GetClaimValueFromTicketReceivedContext(context, JwtClaimTypes.PreferredUserName)
                          ?? GetClaimValueFromTicketReceivedContext(context, JwtClaimTypes.Subject);
        string email = GetClaimValueFromTicketReceivedContext(context, JwtClaimTypes.Email);

        IAuthService authService = context.HttpContext.RequestServices.GetRequiredService<IAuthService>();
        IEnumerable<Login> logins = await authService.GetLoginsAsync(tenantId, new LoginWhere()
        {
            Or = new []
            {
                new LoginWhere
                {
                    Usernames = new [] { username }
                },
                new LoginWhere
                {
                    Email_in = new [] { email }
                }
            }
        });

        return logins.FirstOrDefault();
    }

    private static void SetupMessageReceivedConfiguration(OpenIdConnectOptions options,
        KeyCloakDefaultConfiguration defaultConfiguration, bool requireHttps) =>
        options.Events.OnMessageReceived = async context =>
        {
            if (context.Properties != null)
            {
                string tenantId = context.Properties.Items["tenantId"];
                string clientId = context.Properties.Items["clientId"];

                ValidateTenantAndClient(tenantId, clientId);

                SSOConfig ssoConfig = await GetSSOConfiguration(context.HttpContext, tenantId, clientId);

                OverrideOpenIdConnectOptions(context.Options, defaultConfiguration, ssoConfig, requireHttps);
            }
        };


    private static void SetupDefaultConfiguration(OpenIdConnectOptions options, KeyCloakDefaultConfiguration defaultConfiguration,  bool requireHttps)
    {
        options.Authority = defaultConfiguration.Authority;
        options.RequireHttpsMetadata = requireHttps;
        options.SaveTokens = true;
        options.ClientId = defaultConfiguration.ClientId ?? "dummy-client-id";
        options.ClientSecret = defaultConfiguration.ClientSecret ?? "dummy-client-secret";
        options.GetClaimsFromUserInfoEndpoint = true;
        options.ResponseType = OpenIdConnectResponseType.CodeIdToken;
        options.SignInScheme = CookieAuthenticationDefaults.AuthenticationScheme;
    }

    private static void SetupIdpRedirectConfiguration(OpenIdConnectOptions options, KeyCloakDefaultConfiguration defaultConfiguration, bool requireHttps) =>
        options.Events.OnRedirectToIdentityProvider = async context =>
        {
            string tenantId = context.HttpContext.GetValueFromItems<string>("tenantId");
            string clientId = context.HttpContext.GetValueFromItems<string>("clientId");

            ValidateTenantAndClient(tenantId, clientId);

            SSOConfig ssoConfig = await GetSSOConfiguration(context.HttpContext, tenantId, clientId);

            OverrideDefaultConfiguration(context, defaultConfiguration, ssoConfig, requireHttps);
        };

    private static void HandleRemoteFailure(OpenIdConnectOptions options,  ILogger logger) =>
        options.Events.OnRemoteFailure = context =>
        {
            switch (context.Failure)
            {
                case SecurityTokenInvalidAudienceException invalidAudienceException:
                    logger.Error(invalidAudienceException, KeyCloakErrorMessages.InvalidAudience);
                    break;
                case SecurityTokenInvalidIssuerException invalidIssuerException:
                    logger.Error(invalidIssuerException, KeyCloakErrorMessages.InvalidIssuer);
                    break;
                case SecurityTokenExpiredException expiredException:
                    logger.Error(expiredException, KeyCloakErrorMessages.TokenExpired);
                    break;
                case SecurityTokenInvalidSignatureException invalidSignatureException:
                    logger.Error(invalidSignatureException, KeyCloakErrorMessages.InvalidTokenSignature);
                    break;
                case SecurityTokenNoExpirationException noExpirationException:
                    logger.Error(noExpirationException, KeyCloakErrorMessages.TokenNoExpiration);
                    break;
                case SecurityTokenReplayDetectedException replayDetectedException:
                    logger.Error(replayDetectedException, KeyCloakErrorMessages.TokenReplayDetected);
                    break;
                case SecurityTokenNotYetValidException notYetValidException:
                    logger.Error(notYetValidException, KeyCloakErrorMessages.TokenNotYetValid);
                    break;
                case SecurityTokenValidationException validationException:
                    logger.Error(validationException, KeyCloakErrorMessages.TokenValidation);
                    break;
                default:
                    logger.Error(context.Failure, KeyCloakErrorMessages.Unknown);
                    break;
            }

            return Task.CompletedTask;
        };

    private static void OverrideDefaultConfiguration(RedirectContext context, KeyCloakDefaultConfiguration defaultConfiguration, SSOConfig ssoConfig, bool requireHttps)
    {
        ConfigureIdpHint(context, ssoConfig);
        OverrideProtocolMessage(context.ProtocolMessage, defaultConfiguration, ssoConfig);
        OverrideOpenIdConnectOptions(context.Options, defaultConfiguration, ssoConfig, requireHttps);

        if(requireHttps)
            EnforceHttpsOnRedirectUri(context.ProtocolMessage);
    }

    private static void OverrideProtocolMessage(OpenIdConnectMessage protocolMessage, KeyCloakDefaultConfiguration defaultConfiguration, SSOConfig ssoConfig)
    {
        protocolMessage.State =  GenerateUniqueState();
        protocolMessage.ClientId = ssoConfig.ClientId;
        protocolMessage.ClientSecret = ssoConfig.ClientSecret;
        protocolMessage.IssuerAddress =
            string.Format(defaultConfiguration.AuthEndpointTemplate ?? KeyCloakDefaults.AuthEndpointTemplate, ssoConfig.KeyUrl);
    }

    private static void OverrideOpenIdConnectOptions(OpenIdConnectOptions options, KeyCloakDefaultConfiguration defaultConfiguration, SSOConfig ssoConfig, bool requireHttps)
    {
        options.Authority = ssoConfig.KeyUrl;
        options.ClientId = ssoConfig.ClientId;
        options.ClientSecret = ssoConfig.ClientSecret;
        options.TokenValidationParameters.ValidAudience = ssoConfig.ClientId;
        options.ConfigurationManager = new ConfigurationManager<OpenIdConnectConfiguration>(
            string.Format(defaultConfiguration.MetadataEndpointTemplate  ?? KeyCloakDefaults.MetadataEndpointTemplate, ssoConfig.KeyUrl),
            new OpenIdConnectConfigurationRetriever(),
            new HttpDocumentRetriever { RequireHttps = requireHttps });
    }

    static string GenerateUniqueState()
    {
        byte[] randomBytes = new byte[32];
        using (RandomNumberGenerator rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(randomBytes);
        }

        string uniqueState = Convert.ToBase64String(randomBytes);

        return uniqueState;
    }

    private static void EnforceHttpsOnRedirectUri(OpenIdConnectMessage protocolMessage)
    {
        if (!Uri.TryCreate(protocolMessage.RedirectUri, UriKind.Absolute, out Uri redirectUri)) return;

        if (!redirectUri.Scheme.Equals(UrlSchemes.Http, StringComparison.OrdinalIgnoreCase)) return;

        UriBuilder builder = new(redirectUri) { Scheme = UrlSchemes.Https, Port = -1 };

        protocolMessage.RedirectUri = builder.Uri.ToString();
    }

    private static void ConfigureIdpHint(RedirectContext context, SSOConfig ssoConfig)
    {
        if (string.IsNullOrEmpty(ssoConfig.IdentityProviderHint))
            return;

        context.ProtocolMessage.SetParameter(KeyCloakDefaults.IdpHintUrlParameter, ssoConfig.IdentityProviderHint);
    }

    private static async Task<SSOConfig> GetSSOConfiguration(HttpContext context, string tenantId, string clientId)
    {
        ICacheManager cacheManager = context.RequestServices.GetRequiredService<ICacheManager>();

        SSOConfig ssoConfig = await cacheManager.GetOrSet($"SSOConfig_{tenantId}_{clientId}", async () =>
        {
            IAuthService authService = context.RequestServices.GetRequiredService<IAuthService>();

            TenantSettings tenantSettings = await authService.GetTenantSettingsAsync(tenantId);
            return tenantSettings?.SSOConfigs?.FirstOrDefault(
                x => x.ProviderId == AuthProviderIds.KeyCloak && x.ClientId == clientId);
        }, Infrastructure.Constants.DefaultCacheDuration);

        if (ssoConfig == null)
            throw new InvalidOperationException("This Tenant or Client is Not Supported");

        return ssoConfig;
    }

    private static void ValidateTenantAndClient(string tenantId, string clientId)
    {
        if (string.IsNullOrEmpty(tenantId) || string.IsNullOrEmpty(clientId))
            throw new InvalidOperationException("TenantId or clientId is missing");
    }
}