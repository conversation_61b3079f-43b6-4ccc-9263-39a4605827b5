﻿using System;
using CoverGo.Gateway.Infrastructure.Configuration;
using Microsoft.Extensions.Configuration;
using Serilog;

namespace CoverGo.Gateway.Application.Extensions;

public static class RedisConfigurationHelper
{
    public static RedisConfiguration GetRedisConfiguration(IConfiguration configuration, ILogger logger)
    {
        RedisConfiguration redisConfiguration = new();
        configuration?.GetSection("Redis").Bind(redisConfiguration);

        if (!string.IsNullOrEmpty(redisConfiguration.ConnectionString)) return redisConfiguration;

        string legacyRedisConnectionString = GetLegacyRedisConnectionString(configuration);
        if (!string.IsNullOrEmpty(legacyRedisConnectionString))
        {
            redisConfiguration.ConnectionString = legacyRedisConnectionString;
            return redisConfiguration;
        }
        
        logger.Warning("Invalid or missing Redis connection string in configuration");
        throw new ArgumentException("Invalid or missing Redis connection string in configuration");
    }
    
    private static string GetLegacyRedisConnectionString(IConfiguration configuration) => configuration.GetValue<string>("REDIS_CONNECTION_STRING");
}