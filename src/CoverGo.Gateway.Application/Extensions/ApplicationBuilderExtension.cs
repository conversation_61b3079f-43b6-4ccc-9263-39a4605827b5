﻿using System;
using CoverGo.Gateway.Infrastructure.Configuration;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Serilog;

namespace CoverGo.Gateway.Application.Extensions;

public static class ApplicationBuilderExtension
{
    public static void UseTokenExchangeMiddleware<TMiddleware>(this IApplicationBuilder app, IConfiguration configuration, ILogger logger) where TMiddleware : TokenExchangeMiddleware
    {
        if (app == null) throw new ArgumentNullException(nameof(app));
        if (configuration == null) throw new ArgumentNullException(nameof(configuration));
        if (logger == null) throw new ArgumentNullException(nameof(logger));

        try
        {
            RedisConfigurationHelper.GetRedisConfiguration(configuration, logger); // Just to make sure redis is configured to use distributed cache

            app.UseMiddleware<TMiddleware>();
        }
        catch (Exception ex)
        {
            logger.Error(ex, $"Error configuring {typeof(TMiddleware).FullName}");
        }
    }
}