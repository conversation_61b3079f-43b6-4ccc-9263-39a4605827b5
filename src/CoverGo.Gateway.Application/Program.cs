using System;
using System.IO;

using CoverGo.Gateway.Application;
using CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands;
using CoverGo.Sentry;

using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

using Sentry;

using Serilog;

if (Environment.GetEnvironmentVariable("appName") == null)
    Environment.SetEnvironmentVariable("appName", AppName);

string environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? Environments.Production;

IConfigurationRoot configuration = BuildConfiguration(environment);

Serilog.Debugging.SelfLog.Enable(Console.Error);
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(configuration)
    .CreateLogger();
Log.Logger.Information($"APPNAME: {AppName}");
Log.Logger.Information($"DATACENTERID: {DataCenterId}");

var builder = Host
    .CreateDefaultBuilder(args)
    .ConfigureWebHostDefaults(webHostBuilder =>
    {
        webHostBuilder
            .ConfigureLogging(logging => logging.ClearProviders())
            .UseStartup(context =>
            {
                return new Startup(
                    context.Configuration,
                    context.HostingEnvironment);
            });

        if (Environment.GetEnvironmentVariable("datacenterId") == "dbs-hk")
        {
            webHostBuilder.ConfigureKestrel(serverOptions =>
            {
                serverOptions.AddServerHeader = false;
            }).UseSerilog((hostingContext, loggerConfiguration) =>
                loggerConfiguration
                    .ReadFrom.Configuration(hostingContext.Configuration));
        }
        else
        {
            webHostBuilder.ConfigureKestrel(serverOptions =>
            {
                serverOptions.Limits.Http2.KeepAlivePingDelay = TimeSpan.FromSeconds(30);
                serverOptions.Limits.Http2.KeepAlivePingTimeout = TimeSpan.FromMinutes(10);
            }).UseSerilog((hostingContext, loggerConfiguration) =>
                loggerConfiguration
                    .ReadFrom.Configuration(hostingContext.Configuration)
                    .Enrich.WithClientIp(), writeToProviders: true);
        }

        if (configuration.GetValue<bool>("UseSentry"))
        {
            webHostBuilder.UseCoverGoSentry(
                configuration,
                @event =>
                {
                    if (@event.Logger?.Contains("GraphQLHttpMiddleware") ?? false)
                    {
                        var message = @event.Message?.Formatted ?? "";
                        if (message.Contains("invalid or expired token") ||
                            message.Contains("invalid query") ||
                            message.Contains("Variable '$token' is invalid"))
                        {
                            return null;
                        }
                        @event.SetFingerprint(new[]
                        {
                            "{{ default }}",
                            message
                        });
                    }
                    return @event;
                });
        }

        webHostBuilder
            .ConfigureAppConfiguration((_, config) =>
            {
                config.AddYamlFile("ratelimits.yaml", optional: true, reloadOnChange: true);
                if (environment == Environments.Development)
                {
                    config.AddUserSecrets<Program>();
                }
            });
    });

var host = builder
              .Build();
// Custom for TCB tenant to create certain permission groups with specific targetted permissions on the Gateway Startup
string tenantId = Environment.GetEnvironmentVariable("TCB_PERMISSIONGROUPS_TENANTID");
if (!string.IsNullOrEmpty(tenantId))
{
    var tcbDeployOperationPermissionGroupService = host.Services.GetRequiredService<tcbDeployOperationPermissionGroups>();
    var ret = await tcbDeployOperationPermissionGroupService.ExecuteAsync(tenantId);
    if (!ret.IsSuccess)
    {
        Log.Logger.Error(string.Join(", ", ret.Errors));
    }
}
host.Run();

IConfigurationRoot BuildConfiguration(string env)
{
    var configurationBuilder = new ConfigurationBuilder()
        .SetBasePath(Directory.GetCurrentDirectory())
        // Default some values
        .AddJsonFile("appsettings.json")
        // Override by environment
        .AddJsonFile($"appsettings.{env}.json", true)
        .AddEnvironmentVariables();

    if (env == Environments.Development)
    {
        configurationBuilder.AddUserSecrets<Program>();
    }

    var configurationRoot = configurationBuilder.Build();
    return configurationRoot;
}

public partial class Program
{
    public const string AppName = "covergo-gateway";

    private static readonly string DataCenterId = Environment.GetEnvironmentVariable("datacenterId");
}
