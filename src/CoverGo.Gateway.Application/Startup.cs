using System;
using System.Collections.Generic;
using System.Globalization;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Applications.Monitoring;
using CoverGo.Applications.Startup;
using CoverGo.BuildingBlocks.Auth.Extensions;
using CoverGo.Extensions.Hosting.Abstractions;
using CoverGo.Gateway.Application.DeadlockDetection;
using CoverGo.Gateway.Application.Extensions;
using CoverGo.Gateway.Application.GraphQl;
using CoverGo.Gateway.Application.Http;
using CoverGo.Gateway.Application.Logging;
using CoverGo.Gateway.Application.Services;
using CoverGo.Gateway.Common;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Achievements;
using CoverGo.Gateway.Domain.Advisor;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Binder;
using CoverGo.Gateway.Domain.Cache;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Claims;
using CoverGo.Gateway.Domain.Claims.Asia;
using CoverGo.Gateway.Domain.Cms;
using CoverGo.Gateway.Domain.Context;
using CoverGo.Gateway.Domain.Education;
using CoverGo.Gateway.Domain.Encryptions;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Notifications;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Products.Motor;
using CoverGo.Gateway.Domain.Scheduler;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Infrastructure;
using CoverGo.Gateway.Infrastructure.Achievements;
using CoverGo.Gateway.Infrastructure.Advisor;
using CoverGo.Gateway.Infrastructure.Auth;
using CoverGo.Gateway.Infrastructure.Binders;
using CoverGo.Gateway.Infrastructure.BoclServices;
using CoverGo.Gateway.Infrastructure.BoclServices.BoclCommands;
using CoverGo.Gateway.Infrastructure.BOCServices;
using CoverGo.Gateway.Infrastructure.BOCServices.BocCommands;
using CoverGo.Gateway.Infrastructure.Cache;
using CoverGo.Gateway.Infrastructure.Cases;
using CoverGo.Gateway.Infrastructure.Claims;
using CoverGo.Gateway.Infrastructure.Cms;
using CoverGo.Gateway.Infrastructure.DLVNServices;
using CoverGo.Gateway.Infrastructure.DLVNServices.DLVNCommands;
using CoverGo.Gateway.Infrastructure.Education;
using CoverGo.Gateway.Infrastructure.FileSystem;
using CoverGo.Gateway.Infrastructure.FubonServices;
using CoverGo.Gateway.Infrastructure.FubonServices.FubonServicesCommands;
using CoverGo.Gateway.Infrastructure.InsuredNomadsServices;
using CoverGo.Gateway.Infrastructure.InsuredNomadsServices.InsuredNomadsServicesCommands;
using CoverGo.Gateway.Infrastructure.L10n;
using CoverGo.Gateway.Infrastructure.Notifications;
using CoverGo.Gateway.Infrastructure.Policies;
using CoverGo.Gateway.Infrastructure.Pricing;
using CoverGo.Gateway.Infrastructure.Products;
using CoverGo.Gateway.Infrastructure.Products.Motor;
using CoverGo.Gateway.Infrastructure.RemoteServices.InMemory;
using CoverGo.Gateway.Infrastructure.Scheduler;
using CoverGo.Gateway.Infrastructure.TahoeServices;
using CoverGo.Gateway.Infrastructure.TcbServices;
using CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands;
using CoverGo.Gateway.Infrastructure.Templates;
using CoverGo.Gateway.Infrastructure.Transactions;
using CoverGo.Gateway.Infrastructure.Users;
using CoverGo.Gateway.Infrastructure.Versions;
using CoverGo.Gateway.Infrastructure.WfpServices;
using CoverGo.Gateway.Infrastructure.WfpServices.WfpServicesCommands;
using CoverGo.Gateway.Interfaces;
using CoverGo.Gateway.Interfaces.Claims;
using CoverGo.Gateway.Interfaces.Context;
using CoverGo.Gateway.Interfaces.Notifications;
using CoverGo.Gateway.Interfaces.Policies;
using CoverGo.Gateway.Interfaces.Products;
using CoverGo.Gateway.Interfaces.Users;
using CoverGo.Gateway.RemoteSchemas.Domain;
using CoverGo.RateLimiting.DependencyInjection;
using CoverGo.SettableValues.NewtonsoftJson;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;
using GraphQL;
using GraphQL.Client.Http;
using GraphQL.Client.Serializer.Newtonsoft;
using GraphQL.Server;
using GraphQL.Server.Internal;
using GraphQL.Server.Ui.Playground;

using HotChocolate;
using HotChocolate.AspNetCore;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Routing;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Microsoft.FeatureManagement;
using Microsoft.Net.Http.Headers;
using Microsoft.OpenApi.Models;

using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

using ProxyKit;

using Sentry;

using Serilog;
using Serilog.Events;
using Object = CoverGo.Users.Domain.Objects.Object;
using PermissionValidator = CoverGo.Gateway.Interfaces.PermissionValidator;

namespace CoverGo.Gateway.Application
{
    public class Startup
    {
        private const string _mainGatewayPath = "graphql";
        private const string _oldGatewayPath = "graphql_old";

        public Startup(
            IConfiguration configuration,
            IWebHostEnvironment environment)
        {
            Configuration = configuration;
            Environment = environment;

            GraphQlDotnetPath = _oldGatewayPath;
            HotChocolatePath = _mainGatewayPath;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        public string GraphQlDotnetPath { get; }
        public string HotChocolatePath { get; }

        static readonly TimeSpan _httpClientTimeout = TimeSpan.FromMinutes(20);

        public void ConfigureServices(IServiceCollection services)
        {
            var useSentry = Configuration.GetValue<bool>("UseSentry");
            if (useSentry)
            {
                services.AddTransient<SentryHttpMessageHandler>();
            }

            string datacenterId = System.Environment.GetEnvironmentVariable("datacenterId");
            // Using a validation rule to disable introspection query for tenants in list datacenterIdsToDisableGraphQLSchema
            // or if "HIDE_GRAPHQL_SCHEMA" is true
            bool hideGraphqlSchema = Convert.ToBoolean(System.Environment.GetEnvironmentVariable("HIDE_GRAPHQL_SCHEMA") ?? "false")
                || FeatureFlags.DatacenterIdsToDisableGraphQLSchema.Contains(datacenterId);

            // check if auth is working
            while (!IsAuthWorking($"{Configuration["serviceUrls:auth"]}hc").Result)
            {
                // Avoid over-logging
                Thread.Sleep(TimeSpan.FromSeconds(1));
                Log.Error("Gateway -> Cannot connect to Auth!!!");
            }

            services.Configure<ForwardedHeadersOptions>(options =>
            {
                // This is safe as long as x-forwarded-for header is configured at the level of ingress. In that case
                // it will not be possible to spoof ip address because the spoofed x-forwarded-for header will always be
                // overwritten by ingress with real user address.
                options.KnownNetworks.Clear();
                options.KnownProxies.Clear();
                options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
            });

            services.Configure<FileExtensionFilter>(Configuration.GetSection("fileExtensionFilter"));

            services.Configure<KestrelServerOptions>(options =>
            {
                options.AllowSynchronousIO = true;
            });

            services
                .AddAuthorization()
                .AddMvcCore()
                .AddApiExplorer()
                .AddNewtonsoftJson(options =>
                {
                    options.SerializerSettings.Converters.Add(new StringEnumConverter());
                    options.SerializerSettings.Converters.Add(new SettableConverter());
                    options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                    options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;
                    options.SerializerSettings.ContractResolver = new SettableContractResolver();

                    if (Environment.IsDevelopment())
                        options.SerializerSettings.Formatting = Formatting.Indented;
                });

            if (datacenterId == "chubb-azure-sg")
            {
                services.AddApplicationInsightsTelemetry("8ac4f215-0467-49dc-8547-82dc93744552");
                services.AddApplicationInsightsKubernetesEnricher();
            }

            if (datacenterId == "chubb-azure-sg-prod")
            {
                services.AddApplicationInsightsTelemetry("ec23606f-9f97-4eea-9f6b-95499bc855dd");
                services.AddApplicationInsightsKubernetesEnricher();
            }

            services.AddSignalR();

            services.AddDataProtectionWithRedis(Configuration, Log.Logger);
            services.AddKeyCloakAuthentication(Configuration, Log.Logger, !Environment.IsDevelopment());

            // Bind the SsoSettings section to the SsoSettings class
            services.Configure<AxaHkSsoSettings>(Configuration.GetSection("AxaHkSsoSettings"));

            // Use IOptions<SsoSettings> to access the settings
            var ssoSettings = Configuration.GetSection("AxaHkSsoSettings").Get<AxaHkSsoSettings>();

            if (ssoSettings?.EnableSsoTokenExchange ?? false)
            {
                services.AddAxaHkSSOAuthentication(Configuration, Log.Logger);
            }

            services.AddCoverGoAuthorization(Configuration);

            LoginConfig.AdminLoginRestrictionKey = System.Environment.GetEnvironmentVariable("ADMIN_LOGIN_RESTRICTION_KEY");
            LoginConfig.AdminLoginRestrictionIV = System.Environment.GetEnvironmentVariable("ADMIN_LOGIN_RESTRICTION_IV");
            LoginConfig.AdminLoginRestrictionTokenLifespan = int.TryParse(System.Environment.GetEnvironmentVariable("ADMIN_LOGIN_RESTRICTION_TOKEN_LIFESPAN"), out int f) ? f : default;

            services.ConfigureApplicationCookie(options =>
            {
                // Cookie settings
                options.Cookie.HttpOnly = true;
                options.ExpireTimeSpan = TimeSpan.FromMinutes(5);

                options.LoginPath = "/Identity/Account/Login";
                options.AccessDeniedPath = "/Identity/Account/AccessDenied";
                options.SlidingExpiration = true;
            });

            services.AddSingleton<MetricsReporter>();

            services.AddSwaggerGen(c => c.SwaggerDoc("v1", new OpenApiInfo { Title = "CoverGo Gateway", Version = "v1" }));

            services.Configure<ServiceUrls>(Configuration.GetSection("serviceUrls"));

            services.Configure<ForgotPasswordSettings>(Configuration.GetSection("ForgotPasswordEmailSetttings"));

            services.Configure<IssueDebitNoteNotificationSetttings>(Configuration.GetSection("IssueDebitNoteNotificationSetttings"));

            services.Configure<HideIndividualPolicyExtraFieldsSettings>(Configuration.GetSection("HideIndividualPolicyExtraFieldsSettings"));

            services.Configure<CacheSettings>(Configuration.GetSection("CacheSettings"));

            services.AddCors();

            services.AddHttpContextAccessor();

            services.AddSingleton<IDependencyResolver, GraphQLDependencyResolver>();

            services.AddGraphQLAuth(options => options.AddPolicy("any", _ => _.AddRequirement(new ValidAuth())));

            string gubernatorUrl = Configuration["GUBERNATOR_SERVICE_URL"];
            Log.Information("Gubernator URL: {GubernatorUrl}", gubernatorUrl);
            if (string.IsNullOrEmpty(gubernatorUrl))
                services.AddStubRateLimiting();
            else
                services.AddRateLimiting(Configuration.GetSection("rateLimits"), gubernatorUrl);

            services
                .AddGraphQL(options =>
                {
                    var graphQlOptions = Configuration.GetSection(GraphQLAppOptions.SectionName)
                        .Get<GraphQLAppOptions>(c => c.BindNonPublicProperties = true);

                    options.EnableMetrics = graphQlOptions.EnableMetrics;

                    options.ExposeExceptions = Environment.IsDevelopment() || Environment.IsStagingCi();
                })
                .AddUserContextBuilder(httpContext => new GraphQLUserContext
                {
                    User = httpContext.User,
                    Headers = httpContext.Request.Headers,
                    BaseUrl = (httpContext.Request.Host.Host != "localhost" ? "https" : httpContext.Request.Scheme) + "://" + httpContext.Request.Host + "/"
                })
                .AddWebSockets()
                .AddDataLoader()
                .AddRelayGraphTypes()
                .AddGraphTypes(Assembly.GetAssembly(typeof(CoverGoSchema)))
                .FixErrorPath()
                .Services.AddSingleton(typeof(IGraphQLExecuter<>), typeof(InstrumentingGraphQLExecutor<>));

            services.AddProxy(); // used for CMS for now

            services.AddSingleton<FileUploadController>();
            services.AddSingleton<FiLifeWebhookController>();
            services.AddSingleton<CoverGoSchema>();

            services.AddScoped<GraphQlHttpClientRequestCountMetricHandler>();
            services.AddScoped<GatewayGraphQlExecutionContext>();

            void SetupService<TService, TImplementation>(string configEntryForBaseUrl = null, Uri uri = null)
                where TImplementation : class, TService
                where TService : class
            {
                services.AddHttpClient<TService, TImplementation>(c =>
                {
                    c.Timeout = _httpClientTimeout;
                    c.BaseAddress = uri ?? new Uri($"{Configuration[$"serviceUrls:{configEntryForBaseUrl}"]}");
                })
                .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
                {
                    AutomaticDecompression = System.Net.DecompressionMethods.Brotli
                        | System.Net.DecompressionMethods.GZip
                        | System.Net.DecompressionMethods.Deflate
                })
                .SetupResilienceAndTracing(useSentry)
                .SetupHttpClientMetrics();
            }

            void SetupServiceImplementation<TService>(string configEntryForBaseUrl)
                where TService : class
            {
                services.AddHttpClient<TService>(c =>
                {
                    c.Timeout = TimeSpan.TryParse(
                        Configuration[$"serviceTimeouts:{configEntryForBaseUrl}"],
                        out TimeSpan timeout) ? timeout : _httpClientTimeout;
                    c.BaseAddress = new Uri($"{Configuration[$"serviceUrls:{configEntryForBaseUrl}"]}");
                })
                .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
                {
                    AutomaticDecompression = System.Net.DecompressionMethods.Brotli
                        | System.Net.DecompressionMethods.GZip
                        | System.Net.DecompressionMethods.Deflate
                })
                .SetupResilienceAndTracing(useSentry)
                .SetupHttpClientMetrics();
            }

            SetupService<IAuthService, CoverGoAuthService>("auth");
            SetupService<ILoginProvider, LoginProvider>("auth");
            SetupService<IProductService, CoverGoProductVenue>("products");
            SetupService<IPricingService, CoverGoPricingService>("pricing");
            SetupService<IL10nService, CoverGoL10nService>("l10n");
            SetupService<IMarketingService, CoverGoMarketingService>("l10n");
            SetupService<IInsurerService, CoverGoInsurerService>("products");
            SetupServiceImplementation<CoverGoProductDiscountCodeService>("products");

            SetupService<IEntityService, CoverGoEntityService>("users");
            SetupServiceImplementation<CoverGoDisabilityService>("users");
            SetupServiceImplementation<CoverGoDiagnosisService>("users");

            SetupService<INotificationService, CoverGoNotificationService>("notifications");
            SetupService<ICmsService, CoverGoCmsService>("cms");
            SetupService<ITemplateService, CoverGoTemplateService>("templates");
            SetupService<IFileSystemService, CoverGoFileSystemService>("filesystem");

            SetupService<ICaseService, CoverGoCaseService>("cases");


            SetupService<IEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand>, CoverGoEntityService<Company, CreateCompanyCommand, UpdateCompanyCommand>>("users");
            SetupService<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>, CoverGoEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>>("users");
            SetupService<IEntityService<CoverGo.Gateway.Domain.Users.Internal, CreateInternalCommand, UpdateInternalCommand>, CoverGoEntityService<CoverGo.Gateway.Domain.Users.Internal, CreateInternalCommand, UpdateInternalCommand>>("users");
            SetupService<IEntityService<Object, CreateObjectCommand, UpdateObjectCommand>, CoverGoEntityService<Object, CreateObjectCommand, UpdateObjectCommand>>("users");
            SetupService<IEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand>, CoverGoEntityService<Organization, CreateOrganizationCommand, UpdateOrganizationCommand>>("users");
            SetupService<IPolicyService, CoverGoPolicyService>("policies");

            services.AddSingleton<GraphQlComplexityAnalyzer>();
            services.AddHttpClient<CoverGo.Policies.Client.IPoliciesClient, CoverGo.Policies.Client.PoliciesClient>((serviceProvider, client) =>
            {
                client.BaseAddress = new Uri($"{Configuration[$"serviceUrls:policies"]}");
                client.Timeout = _httpClientTimeout;

                var context = serviceProvider.GetRequiredService<IHttpContextAccessor>()?.HttpContext;

                if (context != null)
                {
                    var headerNamesToPropogate = new[] { HeaderNames.Authorization, CoverGoHeaderNames.Tenant };
                    foreach (var headerName in headerNamesToPropogate)
                    {
                        if (context.Request.Headers.TryGetValue(headerName, out var value))
                        {
                            client.DefaultRequestHeaders.TryAddWithoutValidation(headerName, value.ToArray());
                        }
                    };
                }
            })
            .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                AutomaticDecompression = System.Net.DecompressionMethods.Brotli
                    | System.Net.DecompressionMethods.GZip
                    | System.Net.DecompressionMethods.Deflate
            })
            .AddAuthorizationPropagation().SetupResilienceAndTracing(useSentry).SetupHttpClientMetrics();

            SetupService<IPolicyMemberService, CoverGoPolicyMembersService>("policies");

            SetupServiceImplementation<CoverGoPolicyMembersService>("policies");
            SetupServiceImplementation<CoverGoPolicyCountersService>("policies");
            SetupServiceImplementation<CoverGoPolicyUnderwritingsService>("policies");
            SetupServiceImplementation<CoverGoPolicyPricingCachesService>("policies");

            SetupService<IBinderService, CoverGoBinderService>("policies");
            SetupService<IClaimService, CoverGoClaimService>("claims");
            SetupService<IGuaranteeOfPaymentService, CoverGoGuaranteeOfPaymentService>("claims");
            SetupServiceImplementation<CoverGoTreatmentService>("claims");
            SetupServiceImplementation<CoverGoClaimReportService>("claims");
            SetupServiceImplementation<CoverGoClaimSnapshotService>("claims");
            SetupService<IAdvisorService, CovergoAdvisorService>("advisor");
            SetupService<ITransactionService, CoverGoTransactionService>("transactions");
            SetupService<IMotorVenue, DatabaseOneMotorVenue>(uri: new Uri("https://databases.one/api/"));
            SetupService<IEducationService, CoverGoEducationService>("education");
            SetupService<IAchievementService, CoverGoAchievementService>("achievements");
            SetupService<IClaimRemarksService, CoverGoRemarksService>("claims");
            SetupService<ISchedulerService, CoverGoSchedulerService>("scheduler");

            SetupServiceImplementation<CoverGoClaimRejectionCodeService>("claims");
            SetupServiceImplementation<CoverGoClaimRejectionReasonService>("claims");
            SetupServiceImplementation<CoverGoClaimRequestReasonService>("claims");

            var remoteSchemaNames = SetupRemoteSchemas(
                services,
                datacenterId);

            // HotChocolate graphql that supports schema federations.
            var hotChocolateGraphQl = services
                .AddGatewayGraphQLServerWithLegacyCompatibility()
                .ModifyRequestOptions(requestOptions =>
                {
                    requestOptions.IncludeExceptionDetails = Environment.IsDevelopment() || Environment.IsStagingCi();
                    requestOptions.ExecutionTimeout = _httpClientTimeout;
                })
                .AddTypeExtension<CoverGoSubscription_BackgroundJobs>()
                .AddTypeExtension<PolicyMembersUploads>()
                .AllowIntrospection(!hideGraphqlSchema)
                .AddDiagnosticEventListener<QueryDiagnosticLogger>()
                .AddDiagnosticEventListener<QueryErrorLogger>();
            // TODO: get gateway schema without an HTTP call. Otherwise during the startup we can't get legacy gateway schema.
            //.InitializeOnStartup()
            ;

            hotChocolateGraphQl.AddRemoteSchemasByPollingWithLegacyCompatibility(remoteSchemaNames);

            services.AddHotChocolateSubscriptionsTransport(System.Environment.GetEnvironmentVariable("REDIS_CONNECTION_STRING"));

            services.AddSingleton<FubonIntegrationService>();

            int fubonFpsTimeOut = (int)TimeSpan.Parse(Configuration["serviceTimeouts:fubonfps"]).TotalMilliseconds;
            services.AddSingleton<FubonCommand, FubonIp2Location>(s => new FubonIp2Location(fubonFpsTimeOut));
            services.AddSingleton<FubonCommand, FubonIpStack>(s => new FubonIpStack(fubonFpsTimeOut));
            services.AddSingleton<FubonCommand, FubonFpsQrCode>(s => new FubonFpsQrCode($"{Configuration["serviceUrls:fubonfps"]}", fubonFpsTimeOut));
            services.AddSingleton<FubonCommand, FubonDBSTransaction>(s => new FubonDBSTransaction(s.GetRequiredService<ITransactionService>(), fubonFpsTimeOut));

            InitializeTcbServices(services);

            InitializeInsuredNomadServices(services);

            InitializeBocIntegrationServices(services);

            InitializeBoclIntegrationServices(services);

            InitializeDLVNIntegrationServices(services);

            InitializeTahoeIntegrationServices(services);

            InitializeWfpServices(services);

            InitializeIntegrationGateway(services, Configuration);

            services.AddSingleton<IEncryptionAlgorithm, AesEncryptionAlgorithm>();
            services.AddSingleton<ICaptchaVerificationService, CaptchaVerificationService>();

            services.AddSingleton<ICoverGoContextAccessor, CoverGoContextAccessor>();

            services.AddCoverGoOpenTelemetryTracingIfEnabled();

            services.AddHostedService<ApplicationLifetimeService>();
            services.Configure<HostOptions>(opts => opts.ShutdownTimeout = TimeSpan.FromSeconds(45));
            services.AddGatewayApplicationMonitoring();
            services.AddMemoryCache();
            services.AddSingleton<PermissionValidator>();
            services.AddSingleton<LazyPermissionValidator>();
            services.AddSingleton<PermissionCache>();
            services.AddFeatureManagement();
            services.AddMultiTenantFeatureManagement(Configuration);
            services.AddHttpClient<VersionsService>();
            services.AddSingleton<ISamlAuthService, SamlAuthService>();
            services.AddSingleton<IDateTimeProvider, DateTimeProvider>();

            services.AddAutoMapper(typeof(PolicyMembersRestMappingProfile));

            services.Configure<PermittedIpListOptions>(Configuration.GetSection(PermittedIpListOptions.PermittedIpListKey));
            services.AddSingleton<PermittedIpList>();
            services.AddSingleton<ICacheManager, MemoryCacheManager>();
            services.AddHttpClient<TokenService>();
            services.AddSingleton<ITokenService, TokenService>();
            services.AddScoped<AsiaClaimService>();
            services.AddScoped<DefaultTenantSpecificClaimService>();
            services.AddSingleton<ITenantSpecificClaimService, LazyResolvedTenantSpecificClaimService>();

            // services.AddMessageBus(builder => builder.AddDaprMessageBus(Configuration));
            // services.AddAuditLogging(Configuration);
        }

        List<NameString> SetupRemoteSchemas(
            IServiceCollection services,
            string datacenterId)
        {
            var remoteSchemaNames = new List<NameString>();

            // TODO: implement in HotChocolate gateway
            var tenantFilters = new Dictionary<string, RemoteServiceFilterDefinition>();

            void SetupRemoteSchema(
                string schemaName,
                RemoteServiceFilterDefinition? datacenterFilter = default,
                RemoteServiceFilterDefinition? tenantFilter = default,
                string serviceName = null,
                string graphqlEndpoint = "graphql")
            {
                if (datacenterFilter?.Filter(datacenterId) == false)
                {
                    return;
                }

                if (tenantFilter is not null)
                {
                    tenantFilters[schemaName] = tenantFilter;
                }

                var httpClientBuilder = services.AddHttpClient(schemaName, (serviceProvider, client) =>
                {
                    client.Timeout = _httpClientTimeout;
                    client.BaseAddress = new($"{Configuration[$"serviceUrls:{serviceName ?? schemaName}"]}{graphqlEndpoint}");

                    var context = serviceProvider.GetRequiredService<IHttpContextAccessor>()?.HttpContext;

                    if (context is null)
                    {
                        return;
                    }

                    if (context.Connection.RemoteIpAddress != null)
                        client.DefaultRequestHeaders.Add(ForwardedHeadersDefaults.XForwardedForHeaderName,
                            context.Connection.RemoteIpAddress.ToString());

                    var headers = context.Request.Headers;
                    foreach (var header in headers)
                    {
                        switch (header.Key)
                        {
                            case CoverGoHeaderNames.Tenant:
                                continue;
                            default:
                                client.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value.ToArray());
                                break;
                        }
                    }

                    string tenantId = context.GetTenantId();
                    if (string.IsNullOrEmpty(tenantId))
                    {
                        return;
                    }

                    client.DefaultRequestHeaders.TryAddWithoutValidation(CoverGoHeaderNames.Tenant, tenantId);
                })
                .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
                {
                    AutomaticDecompression = System.Net.DecompressionMethods.Brotli
                        | System.Net.DecompressionMethods.GZip
                        | System.Net.DecompressionMethods.Deflate
                });

                var useSentry = Configuration.GetValue<bool>("UseSentry");
                if (useSentry)
                {
                    httpClientBuilder.AddHttpMessageHandler<SentryHttpMessageHandler>();
                }

                httpClientBuilder.AddPolicyHandler(CoverGoHttpClientBuilderExtensions.GetRetryPolicy(retryCount: 2));

                remoteSchemaNames.Add(schemaName);
            }

            SetupRemoteSchema(
                "claims",
                datacenterFilter: new RemoteServiceFilterDefinition(
                    FilterMode.DisableOn,
                    new HashSet<string>(FeatureFlags.DatacenterIdsWhereClaimsDisabled)));

            SetupRemoteSchema(
                "transactions",
                datacenterFilter: new RemoteServiceFilterDefinition(
                    FilterMode.DisableOn,
                    new HashSet<string>(FeatureFlags.DatacenterIdsToDisableTransactions)));

            SetupRemoteSchema("users");

            SetupRemoteSchema("auth");

            SetupRemoteSchema("cases");

            SetupRemoteSchema("products");

            SetupRemoteSchema("notifications");

            SetupRemoteSchema("templates");

            SetupRemoteSchema("filesystem");

            SetupRemoteSchema("policies",
                datacenterFilter: new RemoteServiceFilterDefinition(
                    FilterMode.DisableOn,
                    new HashSet<string>(FeatureFlags.DatacenterIdsWherePoliciesDisabled)));

            SetupRemoteSchema(
                "claimInvestigation",
                datacenterFilter: new RemoteServiceFilterDefinition(
                    FilterMode.DisableOn,
                    new HashSet<string>(FeatureFlags.DatacenterIdsWhereClaimInvestigationDisabled)));

            if (Configuration.GetSection("FeatureManagement:RequestManager").Value?.ToLower() == "true")
                SetupRemoteSchema("requestManager");

            if (Configuration.GetSection("FeatureManagement:ChannelManagement").Value?.ToLower() == "true"
                || Configuration.GetSection("FeatureManagement:TaskManagement").Value?.ToLower() == "true")
                SetupRemoteSchema("channelManagement");

            if (Configuration.GetSection("FeatureManagement:Reference").Value?.ToLower() == "true"
                || Configuration.GetSection("FeatureManagement:TaskManagement").Value?.ToLower() == "true")
                SetupRemoteSchema("reference");

            if (Configuration.GetSection("FeatureManagement:TaskManagement").Value?.ToLower() == "true")
                SetupRemoteSchema("taskManagement");

            if (Configuration.GetSection("FeatureManagement:Printing").Value?.ToLower() == "true")
                SetupRemoteSchema("printing");

            if (Configuration.GetSection("FeatureManagement:Premium").Value?.ToLower() == "true")
                SetupRemoteSchema("premium", graphqlEndpoint: "graphql_v2");

            services.AddSingleton<IRemoteServicesFilter>(new InMemoryRemoteServicesFilter(tenantFilters));

            bool proxyProductBuilder = System.Environment.GetEnvironmentVariable("PROXY_GRAPHQL_PRODUCTBUILDER_ENABLED")?.ToLower() == "true";

            if (proxyProductBuilder)
            {
                SetupRemoteSchema("productbuilder");

                SetupRemoteSchema("productBuilderAdmin",
                    serviceName: "productbuilder",
                    graphqlEndpoint: "admin/graphql");
            }

            // legacy gateway mapped to hot chocolate
            SetupRemoteSchema(
                "gateway",
                graphqlEndpoint: GraphQlDotnetPath);

            return remoteSchemaNames;
        }

        static void InitializeBocIntegrationServices(IServiceCollection services)
        {
            string bocAPIEndpoint = System.Environment.GetEnvironmentVariable("BOC_API_ENDPOINT");

            services.AddSingleton<BocIntegrationService>();

            services.AddSingleton<BocCommand, BocGetCoverNoteInfoCommand>(c => new BocGetCoverNoteInfoCommand(bocAPIEndpoint));
            services.AddSingleton<BocCommand, BocEndorCoverNoteCommand>(c =>
                new BocEndorCoverNoteCommand(bocAPIEndpoint,
                c.GetRequiredService<IFileSystemService>(),
                c.GetRequiredService<ILogger<BocEndorCoverNoteCommand>>()));
            services.AddSingleton<BocCommand, BocCreateEndorsementFileCommand>(c =>
                new BocCreateEndorsementFileCommand(
                    c.GetRequiredService<IFileSystemService>(),
                    c.GetRequiredService<ILogger<BocCreateEndorsementFileCommand>>())
            );

            services.AddSingleton<BocCommand, BocGetRenewalInfoCommand>(c =>
                new BocGetRenewalInfoCommand(bocAPIEndpoint,
                c.GetRequiredService<IPolicyService>(),
                c.GetRequiredService<ILogger<BocGetRenewalInfoCommand>>()));
            services.AddSingleton<BocCommand, BocGetRenewalInfo2Command>(c =>
                new BocGetRenewalInfo2Command(bocAPIEndpoint,
                c.GetRequiredService<IPolicyService>(),
                c.GetRequiredService<ILogger<BocGetRenewalInfo2Command>>()));
            services.AddSingleton<BocCommand, BocMakeRenewalCommand>(c => new BocMakeRenewalCommand(bocAPIEndpoint,
                c.GetRequiredService<IFileSystemService>(),
                c.GetRequiredService<IPolicyService>(),
                c.GetRequiredService<IEntityService>(),
                c.GetRequiredService<ITransactionService>(),
                c.GetRequiredService<ILogger<BocMakeRenewalCommand>>()));
            services.AddSingleton<BocCommand, BocCreateRenewalFileCommand>(c =>
                new BocCreateRenewalFileCommand(
                    c.GetRequiredService<IFileSystemService>(),
                    c.GetRequiredService<ILogger<BocCreateRenewalFileCommand>>())
            );

            services.AddSingleton<BocCommand, BocCreateTransactionFileCommand>(c =>
                new BocCreateTransactionFileCommand(
                    c.GetRequiredService<ITransactionService>(),
                    c.GetRequiredService<IPolicyService>(),
                    c.GetRequiredService<IFileSystemService>(),
                    c.GetRequiredService<ILogger<BocCreateTransactionFileCommand>>())
            );

            services.AddSingleton<BocCommand, BocCreatePriceFileCommand>(c => new BocCreatePriceFileCommand(
                c.GetRequiredService<IPolicyService>(),
                c.GetRequiredService<IPricingService>(),
                c.GetRequiredService<IFileSystemService>(),
                c.GetRequiredService<ILogger<BocCreatePriceFileCommand>>()));

            services.AddSingleton<BocCommand, BocGetCarModelsCommand>(c => new BocGetCarModelsCommand(
                c.GetRequiredService<IFileSystemService>(),
                c.GetRequiredService<ILogger<BocGetCarModelsCommand>>()));

            services.AddSingleton<BocCommand, BocComparePricingsCommand>(c => new BocComparePricingsCommand(
                c.GetRequiredService<IPricingService>(),
                c.GetRequiredService<ILogger<BocGetCarModelsCommand>>()));

            services.AddSingleton<BocCommand, BocGetRenewalPolicyCommand>(c => new BocGetRenewalPolicyCommand(
                bocAPIEndpoint,
                c.GetRequiredService<IPolicyService>(),
                c.GetRequiredService<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>>(),
                c.GetRequiredService<IEntityService<Object, CreateObjectCommand, UpdateObjectCommand>>(),
                c.GetRequiredService<IPricingService>(),
                c.GetRequiredService<ILogger<BocGetRenewalPolicyCommand>>()));

            services.AddSingleton<BocCommand, BocUpdateRenewalPolicyCommand>(c => new BocUpdateRenewalPolicyCommand(
                c.GetRequiredService<IPolicyService>(),
                c.GetRequiredService<IPricingService>(),
                c.GetRequiredService<ILogger<BocUpdateRenewalPolicyCommand>>()));
        }

        void InitializeBoclIntegrationServices(IServiceCollection services)
        {
            services.AddSingleton<BoclIntegrationService>();

            services.AddSingleton<BoclCommand, BoclGetAuthTokenCommand>(c => new BoclGetAuthTokenCommand(
                c.GetRequiredService<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>>(),
                c.GetRequiredService<IAuthService>(),
                c.GetRequiredService<ILogger<BoclGetAuthTokenCommand>>(),
                new GraphQLHttpClient(new Uri($"{Configuration["serviceUrls:users"]}graphql"), new NewtonsoftJsonSerializer())
            ));
        }

        void InitializeDLVNIntegrationServices(IServiceCollection services)
        {
            services.AddSingleton<DLVNIntegrationService>();

            if (Configuration.GetSection("FeatureManagement:DLVNIntegrationServices").Value?.ToLower() == "true")
                InitializeDLVNCommands();

            void InitializeDLVNCommands()
            {
                services.AddSingleton<DLVNCommand, DLVNRequestOCRCommand>(c => new DLVNRequestOCRCommand(
                c.GetRequiredService<IFileSystemService>(),
                c.GetRequiredService<ILogger<DLVNRequestOCRCommand>>()));

                services.AddSingleton<DLVNCommand, DLVNFetchGeographyMasterdataCommand>(c => new DLVNFetchGeographyMasterdataCommand(
                    c.GetRequiredService<IFileSystemService>(),
                    c.GetRequiredService<ILogger<DLVNFetchGeographyMasterdataCommand>>()));
                services.AddSingleton<DLVNCommand, DLVNCheckClientVerifyCommand>(c => new DLVNCheckClientVerifyCommand(
                    c.GetRequiredService<IPolicyService>(),
                    c.GetRequiredService<CoverGoPolicyPricingCachesService>(),
                    c.GetRequiredService<IProductService>(),
                    c.GetRequiredService<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>>(),
                    c.GetRequiredService<IFileSystemService>(),
                    c.GetRequiredService<ILogger<DLVNCheckClientVerifyCommand>>()));
                services.AddSingleton<DLVNCommand, DLVNSubmitApplicationCommand>(c => new DLVNSubmitApplicationCommand(
                    c.GetRequiredService<IPolicyService>(),
                    c.GetRequiredService<CoverGoPolicyPricingCachesService>(),
                    c.GetRequiredService<IProductService>(),
                    c.GetRequiredService<IFileSystemService>(),
                    c.GetRequiredService<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>>(),
                    c.GetRequiredService<ILogger<DLVNSubmitApplicationCommand>>()));
                services.AddSingleton<DLVNCommand, DLVNCreateCustomerCommand>(c => new DLVNCreateCustomerCommand(
                    c.GetRequiredService<IPolicyService>(),
                    c.GetRequiredService<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>>(),
                    c.GetRequiredService<IAuthService>(),
                    c.GetRequiredService<IFileSystemService>(),
                    c.GetRequiredService<ILogger<DLVNCreateCustomerCommand>>()));
                services.AddSingleton<DLVNCommand, DLVNTotpCommand>(c => new DLVNTotpCommand(
                    c.GetRequiredService<INotificationService>(),
                    c.GetRequiredService<IFileSystemService>(),
                    c.GetRequiredService<IDistributedCache>(),
                    c.GetRequiredService<ILogger<DLVNTotpCommand>>()));
                services.AddSingleton<DLVNCommand, DLVNBuildPaymentParamsCommand>(c => new DLVNBuildPaymentParamsCommand(
                    c.GetRequiredService<IPolicyService>(),
                    c.GetRequiredService<CoverGoPolicyPricingCachesService>(),
                    c.GetRequiredService<IProductService>(),
                    c.GetRequiredService<IFileSystemService>(),
                    c.GetRequiredService<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>>(),
                    c.GetRequiredService<ILogger<DLVNBuildPaymentParamsCommand>>()));
                services.AddSingleton<DLVNCommand, DLVNSendScbNotificationCommand>(c => new DLVNSendScbNotificationCommand(
                    c.GetRequiredService<IFileSystemService>(),
                    c.GetRequiredService<ILogger<DLVNSendScbNotificationCommand>>()));
                services.AddSingleton<DLVNCommand, DLVNInvokeStbIntegrationCommand>(c => new DLVNInvokeStbIntegrationCommand(
                    c.GetRequiredService<IFileSystemService>(),
                    c.GetRequiredService<ILogger<DLVNInvokeStbIntegrationCommand>>()));
                services.AddSingleton<DLVNCommand, DLVNRequestVoucheryCommand>(c => new DLVNRequestVoucheryCommand(
                    c.GetRequiredService<IAuthService>(),
                    c.GetRequiredService<IFileSystemService>(),
                    c.GetRequiredService<ILogger<DLVNRequestVoucheryCommand>>()));
                services.AddSingleton<DLVNCommand, DLVNGetAgentInfoCommand>();
            }
        }

        void InitializeTcbServices(IServiceCollection services)
        {
            string _tcbApiEndpoint = System.Environment.GetEnvironmentVariable("TCB_API_ENDPOINT") ?? "https://101.99.23.176:8457";
            string _tcbCrmApiEndpoint = System.Environment.GetEnvironmentVariable("TCB_CRM_API_ENDPOINT") ?? "https://101.99.23.176:9447/internal-apigw-uat-aws/itcblife/itcb-salesforce";
            string tcbManulifeApiEndpoint = System.Environment.GetEnvironmentVariable("TCB_MANULIFE_API_ENDPOINT") ?? "https://101.99.23.176:9447/manulife-api/tcb/v1/";
            string tcbBancaApiEndpoint = System.Environment.GetEnvironmentVariable("TCB_BANCA_API_ENDPOINT") ?? "https://101.99.23.176:9447/banca/service";
            string tcbMulesoftApiEndpoint = System.Environment.GetEnvironmentVariable("TCB_MULESOFT_API_ENDPOINT") ?? "https://apigw-uat.techcombank.com.vn/retail-insurance-service/v1/itcblife";

            services.AddSingleton<TcbIntegrationService>();
            services.AddSingleton<TcbCommand, GetLeads>(s => new GetLeads(_tcbApiEndpoint, s.GetService<ILogger<GetLeads>>()));
            services.AddSingleton<TcbCommand, GetLeadCrm>(s => new GetLeadCrm(_tcbCrmApiEndpoint, s.GetService<ILogger<GetLeadCrm>>()));
            services.AddSingleton<TcbCommand, ReferLead>(s => new ReferLead(_tcbApiEndpoint, s.GetService<ILogger<ReferLead>>()));
            services.AddSingleton<TcbCommand, ReferLeadCrm>(s => new ReferLeadCrm(_tcbCrmApiEndpoint, s.GetService<ILogger<ReferLeadCrm>>()));
            services.AddSingleton<TcbCommand, UpdateLead>(s => new UpdateLead(_tcbApiEndpoint, s.GetService<ILogger<UpdateLead>>()));
            services.AddSingleton<TcbCommand, UpdateLeadCrm>(s => new UpdateLeadCrm(_tcbCrmApiEndpoint, s.GetService<ILogger<UpdateLeadCrm>>()));
            services.AddSingleton<TcbCommand, CreateNewLead>(s => new CreateNewLead(_tcbApiEndpoint, s.GetService<ILogger<CreateNewLead>>()));
            services.AddSingleton<TcbCommand, CreateNewLeadCrm>(s => new CreateNewLeadCrm(_tcbCrmApiEndpoint, s.GetService<ILogger<CreateNewLeadCrm>>()));
            services.AddSingleton<TcbCommand, ReferLeadFna>();
            services.AddSingleton<TcbCommand, ReferLeadFnaCrm>();
            services.AddSingleton<TcbCommand, ReferLeadandUpdate>(s => new ReferLeadandUpdate(_tcbApiEndpoint, s.GetService<ILogger<ReferLeadandUpdate>>()));
            services.AddSingleton<TcbCommand, ReferLeadandUpdateCrm>(s => new ReferLeadandUpdateCrm(_tcbApiEndpoint, new UpdateLeadCrm(_tcbCrmApiEndpoint, s.GetService<ILogger<UpdateLeadCrm>>()), s.GetService<ILogger<ReferLeadandUpdateCrm>>()));
            services.AddSingleton<TcbCommand, GetLeadsStatus>(s => new GetLeadsStatus(tcbManulifeApiEndpoint, s.GetService<ILogger<GetLeadsStatus>>()));
            services.AddSingleton<TcbCommand, GetLeadsStatusCrm>(s => new GetLeadsStatusCrm(tcbManulifeApiEndpoint, s.GetService<ILogger<GetLeadsStatusCrm>>()));
            services.AddSingleton<TcbCommand, InquiryPolicy>(s => new InquiryPolicy(tcbBancaApiEndpoint, s.GetService<ILogger<InquiryPolicy>>(), s.GetService<IAuthService>(), s.GetService<IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand>>()));
            services.AddSingleton<TcbCommand, GetPolicyDetail>(s => new GetPolicyDetail(tcbBancaApiEndpoint, s.GetService<ILogger<GetPolicyDetail>>()));
            services.AddSingleton<TcbCommand, GetClaimInfo>(s => new GetClaimInfo(tcbBancaApiEndpoint, s.GetService<ILogger<GetClaimInfo>>()));
            services.AddSingleton<TcbCommand, GetReminder>(s => new GetReminder(tcbBancaApiEndpoint, s.GetService<ILogger<GetReminder>>()));

            services.AddSingleton<TcbCommand, GetServicingAgent>(s => new GetServicingAgent(tcbManulifeApiEndpoint, s.GetService<ILogger<GetServicingAgent>>()));
            services.AddSingleton<TcbCommand, UpdateServicingAgent>(s => new UpdateServicingAgent(tcbManulifeApiEndpoint, s.GetService<ILogger<UpdateServicingAgent>>(), new GraphQLHttpClient(new Uri($"{Configuration["serviceUrls:users"]}graphql"), new NewtonsoftJsonSerializer())));

            services.AddSingleton<TcbCommand, ItcbLifeToken>(s => new ItcbLifeToken(s.GetRequiredService<IAuthService>(), s.GetService<ILogger<ItcbLifeToken>>()));
            services.AddSingleton<TcbCommand, tcbDeploymentToken>(s => new tcbDeploymentToken(s.GetRequiredService<IAuthService>(), s.GetService<ILogger<tcbDeploymentToken>>()));
            services.AddSingleton<TcbCommand, tcbDeployOperationPermissionGroups>(s => new tcbDeployOperationPermissionGroups(s.GetRequiredService<IAuthService>(), s.GetService<ILogger<tcbDeployOperationPermissionGroups>>()));
            services.AddSingleton<tcbDeployOperationPermissionGroups>();
            services.AddSingleton<TcbCommand, CheckConsent>(s => new CheckConsent(tcbMulesoftApiEndpoint, s.GetService<ILogger<CheckConsent>>()));
            services.AddSingleton<TcbCommand, GenerateOTPConsent>(s => new GenerateOTPConsent(tcbMulesoftApiEndpoint, s.GetService<ILogger<GenerateOTPConsent>>()));
            services.AddSingleton<TcbCommand, VerifyOtpConsent>(s => new VerifyOtpConsent(tcbMulesoftApiEndpoint, s.GetService<ILogger<VerifyOtpConsent>>()));

            services.AddDeadlockDetection(Configuration);
        }

        static void InitializeInsuredNomadServices(IServiceCollection services)
        {
            services.AddSingleton<InsuredNomadsIntegrationService>();
            services.AddSingleton<InsuredNomadsCommand, PasscreatorCreate>();
            services.AddSingleton<InsuredNomadsCommand, PasscreatorGetTemplates>();
            services.AddSingleton<InsuredNomadsCommand, SafetureSubscriptionAdd>(s => new SafetureSubscriptionAdd(
                s.GetRequiredService<Policies.Client.IPoliciesClient>(),
                s.GetService<ILogger<SafetureSubscriptionAdd>>()));
            services.AddSingleton<InsuredNomadsCommand, SafetureSubscriptionUpdate>();
            services.AddSingleton<InsuredNomadsCommand, ProcessPolicyAndMembers>(s => new ProcessPolicyAndMembers(
                s.GetRequiredService<IAuthService>(),
                s.GetRequiredService<IEntityService>(),
                s.GetRequiredService<IFileSystemService>(),
                s.GetRequiredService<INotificationService>(),
                s.GetRequiredService<Policies.Client.IPoliciesClient>(),
                s.GetRequiredService<ITemplateService>(),
                s.GetRequiredService<ITransactionService>(),
                s.GetService<ILogger<ProcessPolicyAndMembers>>()));
            services.AddSingleton<InsuredNomadsCommand, IssuePolicyForActiveMember>();
        }

        static void InitializeTahoeIntegrationServices(IServiceCollection services)
        {
            services.AddSingleton<TahoeIntegrationService>();

            ServiceProvider serviceProvider = services.BuildServiceProvider();

            services.AddSingleton<ZolozCommand, ZolozIdRecognitionCheckResultCommand>(c => new ZolozIdRecognitionCheckResultCommand(serviceProvider.GetService<ILogger<ZolozIdRecognitionCheckResultCommand>>()));
            services.AddSingleton<ZolozCommand, ZolozIdRecognitionInitializeCommand>(c => new ZolozIdRecognitionInitializeCommand(serviceProvider.GetService<ILogger<ZolozIdRecognitionInitializeCommand>>()));
            services.AddSingleton<ZolozCommand, ZolozRealIdCheckResultCommand>(c => new ZolozRealIdCheckResultCommand(serviceProvider.GetService<ILogger<ZolozRealIdCheckResultCommand>>()));
            services.AddSingleton<ZolozCommand, ZolozRealIdInitializeCommand>(c => new ZolozRealIdInitializeCommand(serviceProvider.GetService<ILogger<ZolozRealIdInitializeCommand>>()));
            services.AddSingleton<ZolozCommand, ZolozDeletePrivacyInfoCommand>(c => new ZolozDeletePrivacyInfoCommand(serviceProvider.GetService<ILogger<ZolozDeletePrivacyInfoCommand>>()));
        }

        static void InitializeWfpServices(IServiceCollection services)
        {
            services.AddSingleton<WfpIntegrationService>();
            services.AddSingleton<WfpCommand, LapetusGetLifeExpectancy>();
            services.AddSingleton<WfpCommand, WealthSpanAssessment>();
            services.AddSingleton<FaceRecongitionService>();
            services.AddSingleton<WfpCommand, GeneticAnalysis>();
        }

        static bool IntegrationGatewayEnabled(IConfiguration configuration)
        {
            return configuration.GetSection("FeatureManagement:IntegrationGateway").Value?.ToLower() == "true";
        }

        static void InitializeIntegrationGateway(IServiceCollection services, IConfiguration configuration)
        {
            if (!IntegrationGatewayEnabled(configuration)) return;

            services.AddReverseProxy()
                .LoadFromConfig(configuration.GetSection("ReverseProxy"));
        }

        static void MapIntegrationGateway(IEndpointRouteBuilder endpoints, IConfiguration configuration)
        {
            if (!IntegrationGatewayEnabled(configuration)) return;

            endpoints.MapReverseProxy();
        }

        public void Configure(
            IApplicationBuilder app,
            IWebHostEnvironment env,
            IHttpContextAccessor httpContextAccessor,
            ILoggerFactory loggerFactory,
            IEnumerable<ILoggerProvider> loggerProviders)
        {
            string datacenterId = System.Environment.GetEnvironmentVariable("datacenterId");

            AddHttpContextTimestamp(app);

            if (bool.TryParse(System.Environment.GetEnvironmentVariable("ENABLE_FORWARDED_HEADERS") ?? "false",
                    out bool enableForwardedHeaders) && enableForwardedHeaders)
                app.UseForwardedHeaders();

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                foreach (ILoggerProvider loggerProvider in loggerProviders)
                    loggerFactory.AddProvider(loggerProvider);
                loggerFactory.AddSentry();
            }

            // app.UseGraphQLAuditLogging();
            app.UseDeadlockDetection();

            if (datacenterId == "dbs-hk")
                app.Use(async (context, next) =>
                {
                    context.Response.Headers.Add("X-Frame-Options", "DENY");
                    await next();
                });

            app.UseAuthorizationPropagation();
            app.UseHsts(hsts => hsts.MaxAge(365).IncludeSubdomains().Preload());
            app.UseXContentTypeOptions();
            app.UseNoCacheHttpHeaders();
            app.UseReferrerPolicy(opts => opts.NoReferrer());
            app.UseSerilogRequestLogging(o =>
            {
                o.EnrichDiagnosticContext = (diagnosticContext, httpContext) =>
                {
                    bool requestHasFile = httpContext.Request.HasFormContentType;
                    if (requestHasFile)
                    {
                        string fileName = httpContext.Request.Form?.Files?.FirstOrDefault()?.FileName;
                        diagnosticContext.Set("FileName", fileName);
                    }

                    string loginId = httpContext?.User?.FindFirstValue("sub");
                    string tenantId = httpContext?.User?.FindFirstValue("tenantId");

                    if (loginId == null || tenantId == null)
                    {
                        bool headerExists = httpContext.Request.Headers.TryGetValue("authorization", out StringValues header);
                        if (headerExists)
                        {
                            string tokenString =
                            header.ToString().Split(' ', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries) switch
                            { { Length: > 1 } parts => parts[1],
                                _ => null
                            };
                            JwtSecurityToken jwtToken = new JwtSecurityTokenHandler().ReadJwtToken(tokenString);
                            loginId ??= jwtToken.Subject;
                            tenantId ??= jwtToken.Claims?.FirstOrDefault(c => c.Type == "tenantId")?.Value; ;
                        }
                    }

                    diagnosticContext.Set("LoginId", loginId);
                    diagnosticContext.Set("TenantId", tenantId);
                };
                List<string> endpointsToFilter = new() { $"/{_mainGatewayPath}", $"/{_oldGatewayPath}", "/playground", "/metrics" };
                Func<HttpContext, double, Exception, LogEventLevel> endpointFilterLogEventLevel = LogEventLevel (HttpContext httpContext, double _, Exception ex) =>
                 ex != null
                    ? LogEventLevel.Error
                    : httpContext.Response.StatusCode > 499
                        ? LogEventLevel.Error
                        : SerilogConfigurator.IsHealthCheckEndpoint(httpContext) || endpointsToFilter.Contains(httpContext?.Request?.Path.Value, StringComparer.InvariantCultureIgnoreCase)
                            ? LogEventLevel.Verbose
                            : LogEventLevel.Information;
                o.GetLevel = endpointFilterLogEventLevel;
            });

            if (datacenterId == "dbs-hk")
                app.UseXXssProtection(options => options.Disabled());
            else
                app.UseXXssProtection(options => options.EnabledWithBlockMode());
            //app.Use(async (context, next) =>
            //{
            //    context.Response.OnStarting(() =>
            //    {
            //        try
            //        {
            //            context.Response.Headers.Append("X-Frame-Options", "ALLOW-FROM https://uapix-portal.jtetbwkl.com.hk/");
            //        }
            //        catch { }
            //        return Task.CompletedTask;
            //    });

            //    await next();
            //});
            if (datacenterId == "dbs-hk" || datacenterId == "12factor")
            {
                app.UseCsp(opts => opts
                    .DefaultSources(s => s.Self()));
            }
            else if (env.IsProduction())
                app.UseCsp(opts => opts
                    .DefaultSources(s => s.None())
                    .BaseUris(s => s.None())
                    .WorkerSources(s => s.Self()
                        .CustomSources("blob:")
                    )
                    .ConnectSources(s => s.Self())
                    .BlockAllMixedContent()
                      .StyleSources(s => s
                        .Self()
                        .CustomSources(
                            "https://cdn.jsdelivr.net/npm/graphql-playground-react/build/static/css/index.css",
                            "https://fonts.googleapis.com",
                            "sha256-47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=",
                            "sha256-TAnmuFnPR9h7/BWqle7//h2Li9AQ5DJ/NYVBeghDI8U=",
                            "sha256-O577/R2yD2COM5KrDiJSrAwRWDqsh2FVrIkpKcLt3b8=",
                            "sha256-wkAU1AW/h8YFx0XlzvpTllAKnFEO2tw8aKErs5a26LY=" //swagger
                        )
                    )
                    .FontSources(s => s.Self().CustomSources(
                        "https://fonts.gstatic.com",
                        "https://fonts.googleapis.com"
                    ))
                    .FormActions(s => s.Self())
                    .FrameAncestors(s => s.Self().CustomSources("https://uapix-portal.jtetbwkl.com.hk"))
                    .ObjectSources(s => s.None())
                    .ImageSources(s => s.Self()
                        .CustomSources(
                            "https://cdn.jsdelivr.net/npm/graphql-playground-react/build/logo.png",
                            "https://cdn.jsdelivr.net/npm/graphql-playground-react/build/favicon.png",
                            "https://www.w3.org/2000/svg",
                            "data:"
                        //"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAMAAAAM7l6QAAAAYFBMVEUAAABUfwBUfwBUfwBUfwBUfwBUfwBUfwBUfwBUfwBUfwBUfwBUfwBUfwBUfwB0lzB/n0BfhxBpjyC0x4////+qv4CJp1D09++ft3C/z5/K16/U379UfwDf58/q79+Ur2D2RCk9AAAAHXRSTlMAEEAwn9//z3Agv4/vYID/////////////////UMeji1kAAAD8SURBVHgBlZMFAoQwDATRxbXB7f+vPKnlXAZn6k2cf3A9z/PfOC8IIYni5FmmABM8FMhwT17c9hnhiZL1CwvEL1tmPD0qSKq6gaStW/kMXanVmAVRDUlH1OvuuTINo6k90Sxf8qsOtF6g4ff1osP3OnMcV7d4pzdIUtu1oA4V0DZoKmxmlEYvtDUjjS3tmKmqB+pYy8pD1VPf7jPE0I40HHcaBwnue6fGzgyS5tXIU96PV7rkDWHNLV0DK4FkoKmFpN5oUnvi8KoeA2/JXsmXQuokx0siR1G8tLkN6eB9sLwJp/yymcyaP/TrP+RPmbMMixcJVgTR1aUZ93oGXsgXQAaG6EwAAAAASUVORK5CYII=", // swagger
                        //"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAyMCI+ICAgIDxwYXRoIGQ9Ik0xMy40MTggNy44NTljLjI3MS0uMjY4LjcwOS0uMjY4Ljk3OCAwIC4yNy4yNjguMjcyLjcwMSAwIC45NjlsLTMuOTA4IDMuODNjLS4yNy4yNjgtLjcwNy4yNjgtLjk3OSAwbC0zLjkwOC0zLjgzYy0uMjctLjI2Ny0uMjctLjcwMSAwLS45NjkuMjcxLS4yNjguNzA5LS4yNjguOTc4IDBMMTAgMTFsMy40MTgtMy4xNDF6Ii8+PC9zdmc+", // swagger
                        //"data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg" // swagger
                        )
                    )
                    .ScriptSources(s => s.Self().CustomSources(
                        "https://cdn.jsdelivr.net/npm/graphql-playground-react/build/static/js/middleware.js",
                        "https://cdnjs.cloudflare.com/ajax/libs/es6-promise/4.1.1/es6-promise.auto.min.js",
                        "https://cdnjs.cloudflare.com/ajax/libs/fetch/2.0.3/fetch.min.js",
                        "https://cdnjs.cloudflare.com/ajax/libs/react/16.2.0/umd/react.production.min.js",
                        "https://cdnjs.cloudflare.com/ajax/libs/react-dom/16.2.0/umd/react-dom.production.min.js",
                        "https://unpkg.com/subscriptions-transport-ws@0.9.5/browser/client.js",
                        "https://cdn.jsdelivr.net/react/15.4.2/react.min.js",
                        "https://cdn.jsdelivr.net/react/15.4.2/react-dom.min.js",
                        "sha256-Gxpjf4M1AL2rbr8HklfQy5Jq23MvhM70WRkOHvkUnhM=",
                        "sha256-ack9GFEvsegjZzsiBBNPhpuQqa/7O0uPOpqqLryk0yU=",
                        "sha256-cwGTkITorIXUqHhYWvFFsjAzy9xE1VF2TzuCDkXU2E8=",
                        "sha256-Tui7QoFlnLXkJCSl1/JvEZdIXTmBttnWNxzJpXomQjg=", //swagger
                        "sha256-tXBBy2ddQE05YnhSpRixXZQBlFGnOa+tBLcTqT3nJ/I=" //swagger
                    ))
                );

            IList<CultureInfo> supportedCultures = StartupMethods.GetCultureInfosISO();

            app.UseRequestLocalization(new RequestLocalizationOptions
            {
                DefaultRequestCulture = new RequestCulture("en-US"),
                SupportedCultures = supportedCultures,
                SupportedUICultures = supportedCultures
            });

            app.UseStaticFiles();
            app.UseCors(builder =>
            {
                if (Environment.IsDevelopment())
                {
                    builder
                        .AllowAnyHeader()
                        .AllowAnyOrigin();
                }
                else
                {
                    builder.WithOrigins(GetPreviewOrigins());
                    builder
                        .SetIsOriginAllowedToAllowWildcardSubdomains()
                        .WithHeaders(
                            HeaderNames.ContentEncoding, // needed for compressed requests from clients
                            HeaderNames.CacheControl, 
                            HeaderNames.ContentLanguage, 
                            HeaderNames.ContentType, 
                            HeaderNames.Expires, 
                            HeaderNames.LastModified, 
                            HeaderNames.Pragma, 
                            HeaderNames.Authorization, 
                            "X-Requested-With", // signalR connection have the header X-Requested-With: XMLHttpRequest
                            "ServiceID", 
                            "func_map", 
                            "admin-verification-token", 
                            CoverGoHeaderNames.Tenant
                        )
                        .AllowAnyMethod()
                        .AllowCredentials(); // Needed for SignalR connection
                }
            });

            app.UseMiddleware<RequestDecompression>();

            app.UseAuthentication();
            app.UseCookiePolicy();

            // The tenantId and appId is populated as a global property using a middleware
            // to be able to recover the tenantid even when exception are thrown
            app.UseMiddleware<DisableTenantMiddleware>();
            app.UseMiddleware<LoggerPropertyProviderMiddleware>();
            app.UseMiddleware<TraceIdentifierHeaderMiddleware>();

            app.UseCoverGoMetrics();
            app.UseMiddleware<TenantIdMetricsMiddleware>();

            app.UseMiddleware<PermittedIpListMiddleware>();
            app.UseMiddleware<GatewayWelcomeMiddleware>();

            if (datacenterId == "dbs-hk")
                app.UseMiddleware<SerilogDBSMiddleware>();
            app.UseWebSockets();

            app.UseGraphQLWebSockets<CoverGoSchema>($"/{GraphQlDotnetPath}");
            app.UseGraphQL<CoverGoSchema>($"/{GraphQlDotnetPath}");

            //disable GraphQL UI for tenants in datacenterIdsToDisableGraphQLPlayground
            bool disablePlayground = Convert.ToBoolean(System.Environment.GetEnvironmentVariable("DISABLE_PLAYGROUND") ?? "false")
                                     || FeatureFlags.DatacenterIdsToDisableGraphQLPlayground.Contains(datacenterId);

            if (!disablePlayground)
            {
                app.UseGraphQLPlayground(new GraphQLPlaygroundOptions { Path = "/playground" });
            }

            //disable Swagger for tenants in datacenterIdsToDisableSwagger
            if (!FeatureFlags.DatacenterIdsToDisableSwagger.Contains(datacenterId))
            {
                app.UseSwagger();
                app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "CoverGo Gateway v1"));
            }

            app.Map(
                "/cms/components",
                app1 => app1.RunProxy(async context =>
                {
                    HttpResponseMessage response = await context
                        .ForwardTo($"{Configuration["serviceUrls:cms"]}cms/components")
                        .Send();

                    //response.Headers.CacheControl = new System.Net.Http.Headers.CacheControlHeaderValue { NoStore = true, NoCache = true };
                    response.Headers.Remove("Access-Control-Allow-Origin");
                    //response.Headers.Remove("cache-control");

                    // Not ajax calls so not needed
                    //string origin = context.Request.Scheme + "://" + context.Request.Host;
                    //if (Origins.Contains(origin))
                    //{
                    //response.Headers.Add("Access-Control-Allow-Origin", origin);
                    //}

                    return response;
                }));

            // Setup proxy for websockets
            app.Map("/notifications/chat", appInner =>
            {
                var endpointPath = $"{Configuration["serviceUrls:notifications"].Replace("http", "ws")}chat";

                appInner.UseWebSocketProxy(context => new Uri(endpointPath));
                appInner.RunProxy(context => context
                    .ForwardTo(endpointPath)
                    .AddXForwardedHeaders()
                    .Send());
            });

            // Proxy fallback protocols to chat
            app.Map(
                "/notifications/chat",
                app1 => app1.RunProxy(async context =>
                {
                    // : Authorize
                    HttpResponseMessage response = await context
                        .ForwardTo($"{Configuration["serviceUrls:notifications"]}chat")
                        // We need to forward origin IP to be able to do sticky session
                        .AddXForwardedHeaders()
                        .Send();

                    return response;
                }));
            app.UseRouting();

            var useSentry = Configuration.GetValue<bool>("UseSentry");
            if (useSentry)
            {
                app.UseSentryTracing();
            }
            app.UseAuthorization();
            app.UseTokenExchangeMiddleware<KeyCloakTokenExchangeMiddleware>(Configuration, Log.Logger);

            // Use IOptions<SsoSettings> to access the settings
            var ssoSettings = Configuration.GetSection("AxaHkSsoSettings").Get<AxaHkSsoSettings>();

            if (ssoSettings?.EnableSsoTokenExchange ?? false)
            {
                app.UseTokenExchangeMiddleware<AxaHkTokenExchangeMiddleware>(Configuration, Log.Logger);
            }

            // The equivalent of 'app.UseMvcWithDefaultRoute()'
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapDefaultControllerRoute();

                endpoints
                    .MapGraphQL($"/{HotChocolatePath}")
                    .WithOptions(
                        new GraphQLServerOptions
                        {
                            Tool = { Enable = !disablePlayground },
                        });

                endpoints.MapVersionEndpoint();

                MapIntegrationGateway(endpoints, Configuration);
            });

            app.UseGatewayApplicationMonitoring();

            Infrastructure.HttpClientExtensions.HttpContextAccessor = httpContextAccessor;
            CoverGo.Applications.Clients.HttpClientExtensions.HttpContextAccessor = httpContextAccessor;
        }

        static void AddHttpContextTimestamp(IApplicationBuilder app) =>
            app.Use((context, next) =>
            {
                context.Items.Add("RequestStartedOn", DateTime.UtcNow);
                return next();
            });

        string[] GetPreviewOrigins()
        {
            var origins = UrlValidator.AllowedOrigins;

            var previewUrlsRaw = System.Environment.GetEnvironmentVariable("PREVIEW_URLS") ?? null;
            if (string.IsNullOrEmpty(previewUrlsRaw))
                return origins.ToArray();

            var previewUrls = previewUrlsRaw.Split(",", StringSplitOptions.RemoveEmptyEntries);
            return origins.Concat(previewUrls).ToArray();
        }

        static async Task<bool> IsAuthWorking(string authHealthUrl)
        {
            try
            {
                using HttpClient client = new();
                return (await client.GetAsync(authHealthUrl)).IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }
    }

    public class RequestDecompression
    {
        private readonly RequestDelegate _next;

        public RequestDecompression(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            if (context.Request.Headers.ContainsKey("Content-Encoding"))
            {
                string encoding = context.Request.Headers["Content-Encoding"].ToString().ToLower();
                Stream originalBody = context.Request.Body;

                try
                {
                    if (encoding == "br" || encoding == "gzip" || encoding == "deflate")
                    {
                        MemoryStream decompressedMemoryStream;

                        await using (MemoryStream compressedSourceStream = new())
                        {
                            await originalBody.CopyToAsync(compressedSourceStream);
                            compressedSourceStream.Position = 0;

                            Stream decompressedTargetStream;
                            if (encoding == "br")
                            {
                                decompressedTargetStream = new BrotliStream(compressedSourceStream, CompressionMode.Decompress, leaveOpen: true);
                            }
                            else if (encoding == "gzip")
                            {
                                decompressedTargetStream = new GZipStream(compressedSourceStream, CompressionMode.Decompress, leaveOpen: true);
                            }
                            else // deflate
                            {
                                decompressedTargetStream = new DeflateStream(compressedSourceStream, CompressionMode.Decompress, leaveOpen: true);
                            }

                            await using (decompressedTargetStream)
                            {
                                decompressedMemoryStream = new();
                                await decompressedTargetStream.CopyToAsync(decompressedMemoryStream);
                                decompressedMemoryStream.Position = 0;
                            }
                        }

                        context.Request.Body = decompressedMemoryStream;

                        context.Request.Headers.Remove("Content-Encoding");

                        if (context.Request.Headers.ContainsKey("Content-Length"))
                        {
                            context.Request.Headers["Content-Length"] = decompressedMemoryStream.Length.ToString();
                        }
                    }
                }
                catch (Exception ex)
                {
                    context.Request.Body = originalBody;

                    if (context.RequestServices != null)
                    {
                        var logger = context.RequestServices.GetService(typeof(ILogger<RequestDecompression>)) as ILogger<RequestDecompression>;
                        logger?.LogError(ex, "RequestDecompression: Error decompressing request body with encoding: {Encoding}", encoding);
                    }
                }
            }

            await _next(context);
        }
    }
}
