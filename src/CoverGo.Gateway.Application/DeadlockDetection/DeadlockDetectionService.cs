﻿
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Threading;
using System;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Application.DeadlockDetection
{

    public class DeadlockDetectionService : BackgroundService
    {
        private readonly ILogger<DeadlockDetectionService> _logger;
        private readonly IHost _host;
        private readonly IRequestTracker _requestTracker;
        private readonly DeadlockHealthStatus _healthStatus;
        private readonly DeadlockDetectionOptions _options;

        public DeadlockDetectionService(
            ILogger<DeadlockDetectionService> logger,
            IOptions<DeadlockDetectionOptions> options,
            IHost host,
            IRequestTracker requestTracker,
            DeadlockHealthStatus healthStatus)
        {
            _logger = logger;
            _host = host;
            _requestTracker = requestTracker;
            _healthStatus = healthStatus;
            _options = options.Value;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            var timeoutThreshold = TimeSpan.FromMinutes(_options.QueryTimeoutMinutes);

            while (!stoppingToken.IsCancellationRequested)
            {
                var stuckRequestCount = _requestTracker.GetStuckRequestCount(timeoutThreshold);
                if (stuckRequestCount >= _options.QueryCountThreshold)
                {
                    _logger.LogCritical($"Detected {stuckRequestCount} stuck queries. Marking application as unhealthy.");
                    if(_options.AllowHealthCheckUpdate)
                    {
                        // Set the application as unhealthy
                        _healthStatus.IsUnhealthy = true;
                    }
                    
                }
                else
                {
                    if (_options.AllowHealthCheckUpdate)
                    {
                        // Reset the health status if the condition is not met
                        _healthStatus.IsUnhealthy = false;
                    }
                }


                _logger.LogInformation($"Detected {stuckRequestCount} stuck queries.");
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }
        }
    }
}

