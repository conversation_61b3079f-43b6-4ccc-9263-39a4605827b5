﻿using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Application.DeadlockDetection
{
    public class DeadlockHealthCheck : IHealthCheck
    {
        private readonly DeadlockHealthStatus _healthStatus;

        public DeadlockHealthCheck(DeadlockHealthStatus healthStatus)
        {
            _healthStatus = healthStatus;
        }


        public Task<HealthCheckResult> CheckHealthAsync(
            HealthCheckContext context,
            CancellationToken cancellationToken = default)
        {
            if (_healthStatus.IsUnhealthy)
            {
                return Task.FromResult(HealthCheckResult.Unhealthy("Deadlock detected, application is unhealthy."));
            }
            return Task.FromResult(HealthCheckResult.Healthy("No deadlock detected."));
        }
    }
}

