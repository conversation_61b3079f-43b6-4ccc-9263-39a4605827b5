﻿using Microsoft.AspNetCore.Http;
using System;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Application.DeadlockDetection
{
    public class RequestLoggingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IRequestTracker _requestTracker;

        public RequestLoggingMiddleware(RequestDelegate next, IRequestTracker requestTracker)
        {
            _next = next;
            _requestTracker = requestTracker;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var requestId = Guid.NewGuid();
            _requestTracker.AddRequest(requestId);

            try
            {
                await _next(context);
            }
            finally
            {
                _requestTracker.RemoveRequest(requestId);
            }
        }
    }

}
