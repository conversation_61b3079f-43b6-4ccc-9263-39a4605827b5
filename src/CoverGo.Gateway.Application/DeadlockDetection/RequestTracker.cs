﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Gateway.Application.DeadlockDetection
{
    public class RequestTracker : IRequestTracker
    {
        private static readonly ConcurrentDictionary<Guid, DateTime> _ongoingRequests = new();

        public void AddRequest(Guid requestId)
        {
            _ongoingRequests[requestId] = DateTime.UtcNow;
        }

        public void RemoveRequest(Guid requestId)
        {
            _ongoingRequests.TryRemove(requestId, out _);
        }

        public IReadOnlyDictionary<Guid, DateTime> GetOngoingRequests() => _ongoingRequests;

        public int GetStuckRequestCount(TimeSpan threshold)
        {
            var now = DateTime.UtcNow;
            return _ongoingRequests.Values.Count(startTime => now - startTime > threshold);
        }
    }


}
