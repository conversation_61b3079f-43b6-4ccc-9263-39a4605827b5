﻿using CoverGo.Applications.Monitoring;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace CoverGo.Gateway.Application.DeadlockDetection
{
    public static class DeadlockDetectionServiceExtensions
    {
        public static IServiceCollection AddDeadlockDetection(
            this IServiceCollection services, IConfiguration configuration)
        {
            var section = configuration.GetSection("DeadlockDetection");
            if (section.Exists())
            {
                services.AddSingleton(new DeadlockHealthStatus { IsUnhealthy = false });
                services.Configure<DeadlockDetectionOptions>(section);
                services.AddHostedService<DeadlockDetectionService>();
                services.AddSingleton<IRequestTracker, RequestTracker>();
                services.AddHealthChecks()
                            .AddCheck<DeadlockHealthCheck>("health_deadlock_check");
            }
            return services;
        }

        public static IApplicationBuilder UseDeadlockDetection(
            this IApplicationBuilder app)
        {
            app.UseMiddleware<RequestLoggingMiddleware>();

            return app;
        }
    }

    public static class ApplicationsMonitoringStartupExtensions
    {
        private const string ReadyCheckName = "ready";

        private const string HealthCheckName = "health";

        private const string StartupCheckName = "startup";

        public const string HealthCheckEndpoint = "/healthz";

        public const string ReadyCheckEndpoint = "/readyz";

        public const string StartupCheckEndpoint = "/startupz";

        //
        // Summary:
        //     Adds CoverGo monitoring services
        public static IServiceCollection AddGatewayApplicationMonitoring(this IServiceCollection services, IApplicationReady applicationReady = null, IApplicationStartup applicationStartup = null)
        {
            services.AddSingleton(applicationReady ?? new DummyApplicationReady());
            services.AddSingleton(applicationStartup ?? new DummyApplicationStartup());
            services.AddHealthChecks().AddCheck<ApplicationStartupCheck>("startup").AddCheck<ApplicationReadyCheck>("ready")
                .AddCheck<ApplicationHealthCheck>("health");
            return services;
        }

        //
        // Summary:
        //     Include CoverGo monitoring to request pipeline
        public static IApplicationBuilder UseGatewayApplicationMonitoring(this IApplicationBuilder app)
        {
            app.UseHealthChecks("/healthz", new HealthCheckOptions
            {
                Predicate = (HealthCheckRegistration registration) => registration.Name.StartsWith("health")
            });
            app.UseHealthChecks("/readyz", new HealthCheckOptions
            {
                Predicate = (HealthCheckRegistration registration) => registration.Name.Equals("ready")
            });
            app.UseHealthChecks("/startupz", new HealthCheckOptions
            {
                Predicate = (HealthCheckRegistration registration) => registration.Name.Equals("startup")
            });
            return app;
        }

        internal class DummyApplicationReady : IApplicationReady
        {
            public bool IsReady { get; set; } = true;

        }

        internal class DummyApplicationStartup : IApplicationStartup
        {
            public bool IsStarted { get; set; } = true;
        }
    }
}
