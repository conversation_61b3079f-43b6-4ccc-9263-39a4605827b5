{"Serilog": {"MinimumLevel": {"Default": "Information"}}, "serviceUrls": {"gateway": "http://localhost:8080/", "products": "http://covergo-products:8080/", "pricing": "http://covergo-pricing:8080/", "l10n": "http://covergo-l10n:8080/", "logging": "http://covergo-logging:9200/", "policies": "http://covergo-policies:8080/", "auth": "http://covergo-auth:8080/", "users": "http://covergo-users:8080/", "notifications": "http://covergo-notifications:8080/", "claims": "http://covergo-claims:8080/", "commissions": "http://covergo-commissions:80/", "advisor": "http://covergo-advisor:8080/", "transactions": "http://covergo-transactions:8080/", "cms": "http://covergo-cms:8080/", "templates": "http://covergo-templates:8080/", "cases": "http://covergo-cases:8080/", "filesystem": "http://covergo-filesystem:8080/", "education": "http://covergo-education:8080", "achievements": "http://covergo-achievements:8080", "scheduler": "http://covergo-scheduler:8080", "fubonfps": "http://covergo-fubonfps:8080", "claimInvestigation": "http://covergo-claim-investigation/", "premium": "http://covergo-premium:8080/", "finance": "http://covergo-finance:8080/", "etl": "http://covergo-etl:8080/", "payments": "http://covergo-payments:8080/", "middleEastIntegration": "http://covergo-middle-east-integration:8080/"}, "fileExtensionFilter": {"allowedExtensions": {".txt": "text/plain", ".jpg": "image/jpeg", ".jpeg": "image/jpeg", ".gif": "image/gif", ".png": "image/png", ".svg": "image/svg+xml", ".bmp": "image/bmp", ".tif": "image/tiff", ".tiff": "image/tiff", ".heif": "image/heif", ".heic": "image/heic", ".pdf": "application/pdf", ".doc": "application/msword", ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", ".xls": "application/vnd.ms-excel", ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", ".csv": "text/csv", ".ppt": "application/vnd.ms-powerpoint", ".pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation", ".avi": "video/x-msvideo", ".weba": "audio/webm", ".webm": "video/webm", ".mpeg": "video/mpeg", ".mp4": "video/mp4", ".mov": "video/quicktime", ".wmv": "video/x-ms-wmv", ".flv": "video/x-flv", ".js": "application/javascript", ".msg": "application/vnd.ms-outlook", ".eml": "message/rfc822", ".cgx": "application/octet-stream"}, "restrictedExtensions": {".txt": "text/plain", ".js": "application/javascript"}}}