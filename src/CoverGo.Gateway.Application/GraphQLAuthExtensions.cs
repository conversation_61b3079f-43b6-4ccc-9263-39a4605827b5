﻿using CoverGo.Gateway.Interfaces;

using GraphQL.Authorization;
using GraphQL.Validation;

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

using System;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Application
{
    public static class GraphQLAuthExtensions
    {
        public static void AddGraphQLAuth(this IServiceCollection services, Action<AuthorizationSettings> configure)
        {
            services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.TryAddSingleton<IAuthorizationEvaluator, AuthorizationEvaluator>();
            services.AddTransient<IValidationRule, AuthorizationValidationRule>();

            services.TryAddSingleton(s =>
            {
                var authSettings = new AuthorizationSettings();
                configure(authSettings);
                return authSettings;
            });
        }
    }

    public class ValidAuth : IAuthorizationRequirement
    {
        public Task Authorize(AuthorizationContext context)
        {
            bool isAuthenticated = (context.UserContext as GraphQLUserContext)?.User?.Claims?.Any() ?? false;
            if (!isAuthenticated)
                context.ReportError("invalid or expired token");

            return Task.CompletedTask;
        }
    }

}
