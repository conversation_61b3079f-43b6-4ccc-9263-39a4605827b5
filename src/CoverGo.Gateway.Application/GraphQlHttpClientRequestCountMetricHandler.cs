﻿using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Gateway.Application;

public class GraphQlHttpClientRequestCountMetricHandler : DelegatingHandler
{
    private readonly IHttpContextAccessor _accessor;
    private readonly MetricsReporter _metricsReporter;

    public GraphQlHttpClientRequestCountMetricHandler(IHttpContextAccessor accessor, MetricsReporter metricsReporter)
    {
        _accessor = accessor;
        _metricsReporter = metricsReporter;
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        GatewayGraphQlExecutionContext graphQlContext = _accessor.HttpContext?.RequestServices.GetService<GatewayGraphQlExecutionContext>();
        
        if (!bool.TryParse(Environment.GetEnvironmentVariable("PROMETHEUS_GRAPHQL_COMPLEXITY_METRICS_ENABLED"), out bool enabled) 
            || !enabled || graphQlContext?.GraphQlInfo == null)
            return await base.SendAsync(request, cancellationToken);

        string method = request.Method.ToString();
        string host = request.RequestUri?.Host ?? "unknown";

        _metricsReporter.RegisterGraphQlInitiatedHttpRequest(graphQlContext.GraphQlInfo, host, method);
        return await base.SendAsync(request, cancellationToken);
    }
}