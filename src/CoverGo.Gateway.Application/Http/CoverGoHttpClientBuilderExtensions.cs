using System;
using System.Net.Http;
using CoverGo.Gateway.Application;
using Polly;
using Polly.Extensions.Http;
using Prometheus;
using Sentry;

namespace Microsoft.Extensions.DependencyInjection;

public static class CoverGoHttpClientBuilderExtensions
{
    public static IHttpClientBuilder SetupResilienceAndTracing(this IHttpClientBuilder builder, bool useSentry)
    {
        if (useSentry)
        {
            builder.AddHttpMessageHandler<SentryHttpMessageHandler>();
        }

        builder
            .SetHandlerLifetime(TimeSpan.FromMinutes(5))
            .AddPolicyHandler(GetRetryPolicy());

        return builder;
    }

    public static IHttpClientBuilder SetupHttpClientMetrics(this IHttpClientBuilder builder)
    {
        return builder
            .UseHttpClientMetrics()
            .AddHttpMessageHandler<GraphQlHttpClientRequestCountMetricHandler>();
    }

    public static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy(int retryCount = 3)
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .WaitAndRetryAsync(
                retryCount,
                retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
    }
}
