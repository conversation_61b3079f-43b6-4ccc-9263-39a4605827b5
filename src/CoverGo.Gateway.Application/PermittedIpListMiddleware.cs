using CoverGo.Gateway.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Net;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Application
{
    public class PermittedIpListMiddleware
    {
        readonly RequestDelegate _request;
        private readonly PermittedIpList _permittedIplist;
        private readonly ILogger<PermittedIpListMiddleware> _logger;

        public PermittedIpListMiddleware(RequestDelegate request, PermittedIpList permittedIplist, ILogger<PermittedIpListMiddleware> logger)
        {
            _request = request ?? throw new ArgumentNullException(nameof(request));
            _permittedIplist = permittedIplist ?? throw new ArgumentNullException(nameof(permittedIplist));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task Invoke(HttpContext httpContext)
        {
            if (_permittedIplist.IsIncluded(
                httpContext.GetClaim("appId"),
                httpContext.GetTenantId(),
                httpContext.GetClientIpAddresses()))
            {
                await _request(httpContext);
                return;
            }

            httpContext.Response.StatusCode = (int)HttpStatusCode.Forbidden;
            _logger.LogInformation("error-8f0690fe: Forbidden, {ipAddress}", httpContext.GetClientIpAddress());
            await httpContext.Response.WriteAsync("error-8f0690fe: Forbidden");
        }
    }
}
