﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <UserSecretsId>fa33d24c-e92e-47f7-aad9-cb91e73e3635</UserSecretsId>
    <SourceRevisionId>build$([System.DateTime]::UtcNow.ToString("yyyy-MM-ddTHH:mm:ss:fffZ"))</SourceRevisionId>

    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileFile>./Dockerfile</DockerfileFile>
    <DockerfileContext>../../</DockerfileContext>
    <DockerfileTag>covergo-gateway</DockerfileTag>
    <DockerfileFastModeStage>base</DockerfileFastModeStage>
    <DockerComposeProjectPath>..\..\docker-compose\docker-compose.dcproj</DockerComposeProjectPath>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
    <DockerfileBuildArguments>-e GH_ACCOUNT=$(GH_ACCOUNT) -e GH_TOKEN=$(GH_TOKEN)</DockerfileBuildArguments>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="CoverGo.Applications.HealthCheck" />
    <PackageReference Include="CoverGo.Applications.Http.GraphQl.Schemas" />
    <PackageReference Include="CoverGo.Applications.Http.GraphQl.Services" />
    <PackageReference Include="CoverGo.Applications.Startup" />
    <PackageReference Include="CoverGo.Extensions.Hosting.Abstractions" />
    <PackageReference Include="CoverGo.FeatureManagement" />
    <PackageReference Include="CoverGo.HotChocolate.SchemaFederations" />
    <PackageReference Include="CoverGo.Sentry" />
    <PackageReference Include="CoverGo.RateLimiting" />
    <PackageReference Include="GraphQL" />
    <PackageReference Include="GraphQL.Authorization" />
    <PackageReference Include="GraphQL.Client.Serializer.Newtonsoft" />
    <PackageReference Include="GraphQL.Server.Transports.AspNetCore" />
    <PackageReference Include="GraphQL.Server.Transports.WebSockets" />
    <PackageReference Include="GraphQL.Server.Ui.GraphiQL" />
    <PackageReference Include="GraphQL.Server.Ui.Playground" />
    <PackageReference Include="GraphQL.Server.Ui.Voyager" />
    <PackageReference Include="HotChocolate" />
    <PackageReference Include="HotChocolate.AspNetCore" />
    <PackageReference Include="HotChocolate.AspNetCore.Authorization" />
    <PackageReference Include="HotChocolate.Subscriptions.Redis" />
    <PackageReference Include="HotChocolate.Stitching" />
    <PackageReference Include="HotChocolate.Types" />
    <PackageReference Include="HotChocolate.Types.Scalars" />
    <PackageReference Include="IdentityServer4.AccessTokenValidation" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" />
    <PackageReference Include="Microsoft.ApplicationInsights.Kubernetes" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection.StackExchangeRedis" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
    <PackageReference Include="Microsoft.AspNetCore.HeaderPropagation" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Microsoft.Extensions.Logging.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" />
    <PackageReference Include="Microsoft.FeatureManagement.AspNetCore" />
    <PackageReference Include="Microsoft.Identity.Web" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" />
    <PackageReference Include="NetEscapades.Configuration.Yaml" />
    <PackageReference Include="NWebsec.AspNetCore.Middleware" />
    <PackageReference Include="ProxyKit" />
    <PackageReference Include="Serilog.AspNetCore" />
    <PackageReference Include="Serilog.Enrichers.ClientInfo" />
    <PackageReference Include="Serilog.Extensions.Logging" />
    <PackageReference Include="Serilog.Formatting.Compact" />
    <PackageReference Include="Serilog.Settings.Configuration" />
    <PackageReference Include="Serilog.Sinks.Console" />
    <PackageReference Include="Serilog.Sinks.ElasticSearch" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
    <PackageReference Include="Serilog.Sinks.SyslogMessages" />
    <PackageReference Include="Serilog.Expressions" />
    <PackageReference Include="Serilog.Enrichers.Span" />
    <PackageReference Include="Yarp.ReverseProxy" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Common\Common.csproj" />
    <ProjectReference Include="..\CoverGo.Gateway.Domain\Domain.csproj" />
    <ProjectReference Include="..\CoverGo.Gateway.Infrastructure\Infrastructure.csproj" />
    <ProjectReference Include="..\CoverGo.Gateway.Interfaces\Interfaces.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.Staging.CI.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="secrets.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
