﻿using CoverGo.Gateway.Application.Logging;
using CoverGo.Gateway.Common;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Interfaces;
using CoverGo.Gateway.Interfaces.Auth;
using Ganss.XSS;
using GraphQL;
using GraphQL.Execution;
using GraphQL.Instrumentation;
using GraphQL.Server;
using GraphQL.Server.Internal;
using GraphQL.Types;
using GraphQL.Validation;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Serilog.Context;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Gateway.Application.GraphQl;
using GraphQL.Validation.Complexity;

namespace CoverGo.Gateway.Application
{
    public class InstrumentingGraphQLExecutor<TSchema> : DefaultGraphQLExecuter<TSchema>
            where TSchema : ISchema
    {
        private readonly GraphQLOptions _options;
        private readonly ILogger _logger;
        private readonly GraphQlComplexityAnalyzer _graphQlComplexityAnalyzer;
        private readonly MetricsReporter _metricsReporter;
        private readonly IAuthService _authService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly HtmlSanitizer _htmlSanitizer;

        public InstrumentingGraphQLExecutor(
            TSchema schema,
            IDocumentExecuter documentExecuter,
            IOptions<GraphQLOptions> options,
            IEnumerable<IDocumentExecutionListener> listeners,
            IEnumerable<IValidationRule> validationRules,
            IAuthService authService,
            IHttpContextAccessor httpContextAccessor,
            ILogger<InstrumentingGraphQLExecutor<TSchema>> logger,
            GraphQlComplexityAnalyzer graphQlComplexityAnalyzer,
            MetricsReporter metricsReporter)
            : base(schema, documentExecuter, options, listeners, validationRules)
        {
            _options = options.Value;
            _logger = logger;
            _graphQlComplexityAnalyzer = graphQlComplexityAnalyzer;
            _metricsReporter = metricsReporter;
            _authService = authService;
            _httpContextAccessor = httpContextAccessor;
            _htmlSanitizer = new HtmlSanitizer();
        }

        public override Task<ExecutionResult> ExecuteAsync(string operationName, string query, Inputs variables, object context, CancellationToken cancellationToken = default)
        {
            GraphQlBodyInfo graphQlInfo = new(operationName, query, variables);
            return operationName == "IntrospectionQuery" || (query?.StartsWith("query IntrospectionQuery") ?? false)
                ? ExecuteIntrospectionQuery(graphQlInfo, context, cancellationToken)
                : ExecuteStandardQuery(graphQlInfo, context, cancellationToken);
        }

        bool IsAiaRepeatedQuery(GraphQLUserContext graphQLUserContext) =>
            GetTenantId(graphQLUserContext) == "aia_uat"
            && _httpContextAccessor.HttpContext.UserAgent()
                .StartsWith("node", StringComparison.InvariantCultureIgnoreCase);

        ExecutionResult CreateInternalErrorResult(string message = null) =>
            new() { Errors = new ExecutionErrors { new(message ?? "Internal server error") } };

        async Task<ExecutionResult> ExecuteStandardQuery(GraphQlBodyInfo graphQlInfo, object context, CancellationToken cancellationToken)
        {
            if (IsAiaRepeatedQuery(context as GraphQLUserContext))
                return new ExecutionResult();

            ExecutionResult result;
            ComplexityResult complexityResult = null;
            try
            {
                complexityResult = PushComplexityMetrics(graphQlInfo);
                
                await HandleCredentials(context as GraphQLUserContext);
                SetupGatewayGraphQlExecutionContext(graphQlInfo);
                
                result = await base.ExecuteAsync(graphQlInfo.OperationName, graphQlInfo.Query, graphQlInfo.Variables, context, cancellationToken);

                _metricsReporter.RegisterGraphqlQueryPerformance(result);
                _metricsReporter.RegisterGraphQlResponseSize(graphQlInfo, result);
            }
            catch (Exception ex)
            {
                LogException(graphQlInfo, ex, complexityResult: complexityResult);
                return CreateInternalErrorResult(ex.Message);
            }

            if (result == null)
            {
                LogError(500, graphQlInfo, "query did not return result", complexityResult: complexityResult);
                return CreateInternalErrorResult();
            }

            HandleLongQueries(result, graphQlInfo);

            return HandleResultErrors(result, graphQlInfo, complexityResult) ?? result;
        }

        private void SetupGatewayGraphQlExecutionContext(GraphQlBodyInfo graphQlInfo)
        {
            GatewayGraphQlExecutionContext executionContext = _httpContextAccessor.HttpContext?
                .RequestServices.GetService<GatewayGraphQlExecutionContext>();
            
            if(executionContext == null) return;
            executionContext.GraphQlInfo = graphQlInfo;
        }

        private ComplexityResult PushComplexityMetrics(GraphQlBodyInfo graphQlInfo)
        {
            if (!bool.TryParse(Environment.GetEnvironmentVariable("PROMETHEUS_GRAPHQL_COMPLEXITY_METRICS_ENABLED"), out bool enabled) || !enabled)
                return null;

            if (string.IsNullOrWhiteSpace(graphQlInfo?.Query))
                return null;
            
            ComplexityResult complexityResult = _graphQlComplexityAnalyzer.TryAnalyze(graphQlInfo.Query);
            _metricsReporter.RegisterGraphQlComplexity(graphQlInfo, complexityResult);

            return complexityResult;
        }

        void HandleLongQueries(ExecutionResult result, GraphQlBodyInfo graphQlInfo)
        {
            double? duration = GetGraphqlDurationInSeconds(result);
            if (duration > 5)
                WithLogContextProperties(
                    _httpContextAccessor.HttpContext?.Response?.StatusCode ?? 500,
                    graphQlInfo,
                    () => _logger.LogInformation("Slow GraphQL query finished in {Duration:0.0} seconds", duration),
                    duration);
        }

        void HandleLongIntrospectionQueries(object context, ExecutionResult result, GraphQlBodyInfo graphQlInfo)
        {
            GraphQLUserContext ctx = (GraphQLUserContext)context;
            if (ctx.Headers.ContainsKey("User-Agent"))
                LogContext.PushProperty("User-Agent", ctx.Headers["User-Agent"]);
            HandleLongQueries(result, graphQlInfo);
        }

        private double? GetGraphqlDurationInSeconds(ExecutionResult result)
        {
            if (!_options.EnableMetrics) return GetQueryElapsedTime();
            
            if (result.Perf == null || !result.Perf.Any()) return null;
            double? duration = result.Perf?.Max(p => p.Duration);
            return duration / 1000;
        }

        ExecutionResult HandleResultErrors(ExecutionResult result, GraphQlBodyInfo queryArgs, ComplexityResult complexityResult)
        {
            if (result.Errors?.Any() ?? false)
            {
                IReadOnlyCollection<ExecutionError> authErrors = result.Errors.Where(e => e.Code == "authorization").ToArray();
                if (authErrors.Any())
                    _logger.LogInformation("GraphQL query finished with code {StatusCode} because of authentication error", 401);

                IReadOnlyCollection<ExecutionError> incorrectQueryErrors = FindIncorrectQueryErrors(result.Errors);

                if (incorrectQueryErrors.Any())
                {
                    LogError(400, queryArgs, "invalid query", complexityResult: complexityResult);

                    bool hideGraphqlSchema = Convert.ToBoolean(System.Environment.GetEnvironmentVariable("HIDE_GRAPHQL_SCHEMA") ?? "false");
                    if (Environment.GetEnvironmentVariable("datacenterId") == "polipocket-aws-sg" || hideGraphqlSchema)
                        return new ExecutionResult { Errors = new ExecutionErrors { new("You are not authorized to run this query.\ninvalid or expired token") { Code = "400" } } };
                }

                IReadOnlyCollection<ExecutionError> realErrors = result.Errors.Except(authErrors).Except(incorrectQueryErrors).ToArray();
                if (realErrors.Any())
                    foreach (ExecutionError error in realErrors)
                    {
                        string message = $"{error.Code}: {error.Message}";
                        if (error.InnerException == null)
                            LogError(500, queryArgs, message, GetGraphqlDurationInSeconds(result), complexityResult);
                        else
                            LogException(queryArgs, error.InnerException, message, GetGraphqlDurationInSeconds(result), complexityResult: complexityResult);
                    }

                // result to sanitize
                foreach (ExecutionError error in result.Errors)
                {
                    string sanitized = _htmlSanitizer.Sanitize(error.Message);
                    if (sanitized != error.Message)
                        return new ExecutionResult { Errors = new ExecutionErrors { new("nope.") } };
                }
            }
            else
            {
                WithLogContextProperties(
                    200,
                    queryArgs,
                    () => _logger.LogInformation("GraphQL query finished successfully"),
                    GetGraphqlDurationInSeconds(result),
                    logVariables: false,
                    complexityResult: complexityResult
                );
            }

            return null;
        }

        private bool IsAzureLogin(GraphQLUserContext graphQLUserContext)
        {
            string issuer = GetClaim(graphQLUserContext, "iss");
            _logger.LogInformation("AzureAD issuer:{issuer}", issuer);
            string isSSO = GetClaim(graphQLUserContext, "isSSOLogin") ?? "false";
            _logger.LogInformation("AzureAD isSSO:{isSSO}", isSSO);
            string isAzureAD = GetClaim(graphQLUserContext, "isAzureAD") ?? "false";
            _logger.LogInformation("AzureAD isAzureAD:{isAzureAD}", isAzureAD);

            _logger.LogInformation("Disable AzureAD:{flag}", Environment.GetEnvironmentVariable("DISABLE_AZURE_LOGIN"));

            return issuer.Contains("login.microsoftonline.com") && isAzureAD == "true" && isSSO == "true";
        }

        async Task HandleCredentials(GraphQLUserContext graphQLUserContext)
        {
            string tenantId = GetTenantId(graphQLUserContext);
            LoginConfig.IsAzureLogin = IsTenantAzureEnabled(tenantId) && IsAzureLogin(graphQLUserContext);
            _logger.LogInformation("tenantId:{tenant}", tenantId);
            _logger.LogInformation("Is Azure Login:{isAzureLogin}", LoginConfig.IsAzureLogin);
            string appId = GetAppId(graphQLUserContext);
            string loginId = GetClaim(graphQLUserContext, "sub");

            _logger.LogInformation("User Claims: {claims}",string.Join(",", graphQLUserContext?.User?.Claims?.Select(x=> $"{x?.Type}:{x?.Value}")));

            _logger.LogInformation("app Id:{appId}, login Id:{loginId}", appId,loginId);

            if (LoginConfig.IsAzureLogin)
            {
                loginId = GetClaim(graphQLUserContext, "userId");
                _logger.LogInformation("Azure Login Id:{loginId}", loginId);
                try
                {
                    loginId = (new System.Net.Mail.MailAddress(loginId)).User;
                }
                catch
                {
                    var headers = string.Join(",\r\n", graphQLUserContext?.Headers?.Select(x => $"{x.Key}: {x.Value}")?.ToArray() ?? new string[0]);
                    _logger.LogError($"Azure login token claim incorrect. userId claim must me email, current userId claim value: {loginId}, appId: {appId}, appId: {tenantId}, Request headers: {headers}");
                }
            }

            if (LoginConfig.AdminLoginRestrictionKey == null)
                _logger.LogWarning("AdminLoginRestriction key not found");
            if (LoginConfig.AdminLoginRestrictionIV == null)
                _logger.LogWarning("AdminLoginRestrictionIV key not found");

            if (LoginConfig.AdminLoginRestrictionKey != null && LoginConfig.AdminLoginRestrictionIV != null)
            {
                _logger.LogInformation("AdminLoginRestrictionKey value is:{AdminLoginRestrictionKey}", LoginConfig.AdminLoginRestrictionKey);
                _logger.LogInformation("AdminLoginRestrictionIV value is:{AdminLoginRestrictionIV}", LoginConfig.AdminLoginRestrictionIV);
                string clientId = graphQLUserContext?.User?.Claims?.FirstOrDefault(c => c.Type == "client_id")?.Value;
                _logger.LogInformation("Current user Claim client_id:{client_id}", clientId);
                if (clientId == "covergo_crm")
                {
                    string verificationToken = graphQLUserContext.Headers.FirstOrDefault(h => h.Key == "admin-verification-token").Value.ToString();
                    if (verificationToken?.Any() == false) throw new ValidationError("", "authorization", "Admin verification token is missing.");
                    string decryptedString = AesDecrypt(LoginConfig.AdminLoginRestrictionKey, verificationToken);
                    bool parseSuccessful = DateTime.TryParse(decryptedString, out DateTime parsedDate);
                    if (!parseSuccessful || DateTime.UtcNow.Subtract(parsedDate.ToUniversalTime()).TotalSeconds > (LoginConfig.AdminLoginRestrictionTokenLifespan ?? 3))
                        throw new ValidationError("", "authorization", "Admin verification token is invalid.");
                }
            }

            if (tenantId != null && loginId != null)
            {
                _logger.LogInformation("isAzureAD claim found :{flag}", graphQLUserContext.User.HasClaim("isAzureAD", "true"));
                if (LoginConfig.IsAzureLogin && (graphQLUserContext?.User?.HasClaim("isAzureAD", "true") ?? false))
                {
                    _logger.LogInformation("Handle Azure Login");
                    await HandleAzureLogin();
                    return;
                }

                if (graphQLUserContext.User.HasClaim("isSSOLogin", "true"))
                {
                    _logger.LogInformation("Handle SSO Login");
                    await HandleSSOLogin();
                    return;
                }

                _logger.LogInformation("Get Login By Id with LoginID:{loginId}, app Id:{appId}",loginId,appId);
                Login loginDao = await _authService.GetLoginById(tenantId, loginId, appId);
                
                PopulateGraphQLUserContext(graphQLUserContext, loginDao);
            }

            static void PopulateGraphQLUserContext(GraphQLUserContext graphQLUserContext, Login loginDao)
            {
                if (loginDao == null)
                    throw new ValidationError("", "authorization", "Login not found.");

                if (!loginDao.IsActive)
                    throw new ValidationError("", "authorization", $"Login `{loginDao.Id}` is inactive.");

                (graphQLUserContext.User.Identity as ClaimsIdentity)?.AddClaims(
                    loginDao.TargettedPermissions.SelectMany(tp =>
                        tp.Value.Select(i => new Claim(tp.Key, i))));
                graphQLUserContext.PermissionLazyLoadingRequired = loginDao.PermissionLazyLoadingRequired;
            }

            async Task HandleSSOLogin()
            {
                string emailClaim = GetClaim(graphQLUserContext, "email") ?? loginId;
                Login loginDao = (await _authService.GetLoginsAsync(tenantId, new LoginWhere { Email_in = new List<string> { emailClaim } })).FirstOrDefault();
                if (loginDao == null)
                    loginDao = await _authService.GetLoginById(tenantId, loginId, appId);
                PopulateGraphQLUserContext(graphQLUserContext, loginDao);
            }

            async Task HandleAzureLogin()
            {
                Login loginDao = await _authService.GetLoginByNameAsync(tenantId, loginId, appId);
                PopulateGraphQLUserContext(graphQLUserContext, loginDao);
            }
        }

        static string AesDecrypt(string hash, string encryptedString)
        {
            byte[] cipher = Convert.FromBase64String(encryptedString);
            byte[] iv = Convert.FromBase64String(LoginConfig.AdminLoginRestrictionIV);
            byte[] key = Encoding.UTF8.GetBytes(hash);

            using Aes aesAlg = Aes.Create();
            using ICryptoTransform decryptor = aesAlg.CreateDecryptor(key, iv);
            using MemoryStream msDecrypt = new(cipher);
            using CryptoStream csDecrypt = new(msDecrypt, decryptor, CryptoStreamMode.Read);
            using StreamReader srDecrypt = new(csDecrypt);
            return srDecrypt.ReadToEnd();
        }

        static string GetClaim(GraphQLUserContext graphQLUserContext, string type) =>
            graphQLUserContext?.User?.Claims.FirstOrDefault(c => c.Type == type)?.Value;

        static string GetTenantId(GraphQLUserContext graphQLUserContext) =>
            GetClaim(graphQLUserContext, "tenantId");

        static string GetAppId(GraphQLUserContext graphQLUserContext) =>
            LoginConfig.IsAzureLogin
                ? "covergo_crm"
                : GetClaim(graphQLUserContext, "appId") ?? GetClaim(graphQLUserContext, "client_id");

        readonly IReadOnlyCollection<string> _incorrectQueryCodes = new[] { "5.2.1", "INVALID_VALUE", "SYNTAX_ERROR", "5.7.5", "5.7.6", "5.2.3", "5.3.1", "*******", "*******" };

        IReadOnlyCollection<ExecutionError> FindIncorrectQueryErrors(IEnumerable<ExecutionError> errors) =>
            errors.Where(e => _incorrectQueryCodes.Contains(e.Code)).ToArray();

        async Task<ExecutionResult> ExecuteIntrospectionQuery(GraphQlBodyInfo graphqlInfo, object context, CancellationToken cancellationToken)
        {
            try
            {
                ExecutionResult result = await base.ExecuteAsync(graphqlInfo.OperationName, graphqlInfo.Query, graphqlInfo.Variables, context, cancellationToken);
                HandleLongIntrospectionQueries(context, result, graphqlInfo);

                return result;
            }
            catch (Exception ex)
            {
                LogException(graphqlInfo, ex);
            }

            return CreateInternalErrorResult();
        }

        void LogException(GraphQlBodyInfo graphQlInfo, Exception exception, string message = null, double? duration = null, ComplexityResult complexityResult = null) =>
            WithLogContextProperties(
                500,
                graphQlInfo,
                () => _logger.LogError(exception, "GraphQL query finished with exception ||| {message}", message),
                duration,
                complexityResult: complexityResult
            );

        void LogError(int statusCode, GraphQlBodyInfo graphQlInfo, string message, double? duration = null, ComplexityResult complexityResult = null) =>
            WithLogContextProperties(
                statusCode,
                graphQlInfo,
                () => _logger.LogError("GraphQL query finished with error ||| {Message}", message),
                duration,
                complexityResult: complexityResult
            );

        void WithLogContextProperties(int statusCode, GraphQlBodyInfo graphQlInfo, Action action, 
            double? graphqlDuration = null, bool logVariables = true, ComplexityResult complexityResult = null)
        {
            decimal? DoubleToRoundedDecimal(double? d) =>
                d.HasValue ? Math.Round(Convert.ToDecimal(d), 3) : null;
            
            string variables = graphQlInfo.Variables == null ? null : JsonConvert.SerializeObject(graphQlInfo.Variables, Formatting.None);
            string query = CutString(GraphQlQueryLoggingExtensions.MaskQuery(graphQlInfo.Query));
            double elapsed = GetQueryElapsedTime();
            string userAgent = _httpContextAccessor.HttpContext.UserAgent();

            Dictionary<string, object> properties = new()
            {
                { "statusCode", statusCode },
                { "operationName", graphQlInfo.OperationName },
                { "Elapsed", DoubleToRoundedDecimal(elapsed) },
                { "graphqlResolutionDuration", DoubleToRoundedDecimal(graphqlDuration) },
                { "queryHash", graphQlInfo.Query == null ? "" : Encryption.GetStringHash(graphQlInfo.Query) },
                { "variablesHash", variables == null ? "" : Encryption.GetStringHash(variables) },
                { "query", query },
                { "userAgent", userAgent }
            };
            
            GraphQlQueryLoggingExtensions.AddTargetIdsToLoggingProperties(graphQlInfo.Variables, properties);
            GraphQlQueryLoggingExtensions.AddPagingVariablesToLoggingProperties(graphQlInfo.Variables, properties);

            if (complexityResult != null)
            {
                properties.Add("queryComplexity", DoubleToRoundedDecimal(complexityResult.Complexity));
                properties.Add("queryDepth", complexityResult.TotalQueryDepth);
            }

            if (logVariables)
            {
                GraphQlQueryLoggingExtensions.MaskVariables(graphQlInfo.Variables);
                string maskedVariables = graphQlInfo.Variables == null ? null : JsonConvert.SerializeObject(graphQlInfo.Variables, Formatting.None);
                properties.Add("variables", CutString(maskedVariables));
            }

            if (!string.IsNullOrEmpty(_httpContextAccessor.HttpContext.Request.Headers.Authorization))
                properties.Add("authHeaderHash", Encryption.GetStringHash(_httpContextAccessor.HttpContext.Request.Headers.Authorization));

            using (_logger.BeginScope(properties))
                action();
        }

        private double GetQueryElapsedTime() => (DateTime.UtcNow - _httpContextAccessor.HttpContext.GetTimestamp()).TotalSeconds;

        static string CutString(string query) =>
            string.IsNullOrEmpty(query) || query.Length <= 5000
                ? query
                : query[..4999];

        protected override ExecutionOptions GetOptions(
            string operationName,
            string query,
            Inputs variables,
            object context,
            CancellationToken cancellationToken)
        {
            ExecutionOptions options = base.GetOptions(operationName, query, variables, context, cancellationToken);

            if (_options.EnableMetrics && operationName != "IntrospectionQuery")
            {
                // Add instrumentation to measure how long field resolvers take to execute.
                options.FieldMiddleware.Use<InstrumentFieldsMiddleware>();
            }

            options.FieldMiddleware.Use<WatchingLimitsFieldsMiddleware>();

            return options;
        }

        private bool IsTenantAzureEnabled(string tenantId) =>
            Environment.GetEnvironmentVariable("DISABLE_AZURE_LOGIN") == "false" && FeatureFlags.TenantIdsToUseAzureLogin.Contains(tenantId);

    }
}