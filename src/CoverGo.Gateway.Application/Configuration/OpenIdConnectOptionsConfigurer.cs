﻿using System;
using System.Linq;
using CoverGo.Gateway.Infrastructure.Auth;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;

namespace CoverGo.Gateway.Application.Configuration;

public class OpenIdConnectOptionsConfigurer : IPostConfigureOptions<OpenIdConnectOptions>
{
    private readonly string[] _schemes;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public OpenIdConnectOptionsConfigurer(string[] schemes, IHttpContextAccessor httpContextAccessor)
    {
        _schemes = schemes ?? throw new ArgumentNullException(nameof(schemes));
        _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
    }

    public void PostConfigure(string name, OpenIdConnectOptions options)
    {
        if (_schemes.Length == 0 || _schemes.Contains(name))
        {
            options.StateDataFormat = new DistributedCacheStateDataFormatter(_httpContextAccessor, name);
        }
    }
}