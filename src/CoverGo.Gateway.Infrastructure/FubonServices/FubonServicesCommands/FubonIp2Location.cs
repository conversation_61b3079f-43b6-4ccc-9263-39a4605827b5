using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;
using RestSharp;

namespace CoverGo.Gateway.Infrastructure.FubonServices.FubonServicesCommands
{
    public class FubonIp2Location : FubonCommand
    {
        public FubonIp2Location(int apiTimeoutSetting)
            : base(apiTimeoutSetting)
        {
        }
        
        public override string Name { get; } = "fubonIp2Location";
        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, IEnumerable<Claim> claims)
        {
            FubonIp2LocationInput fubonIp2LocationInput = inputJson.ToObject<FubonIp2LocationInput>();
            string key = tenantId == "fubon_uat" ? "NUJ23TF0TJ" : "IPCWQT9RNL";
            
            string keyFromEnv = Environment.GetEnvironmentVariable("FUBON_IP2_LOCATION_KEY");
            if (!String.IsNullOrWhiteSpace(keyFromEnv))
                key = keyFromEnv;

            string endpoint = $"https://api.ip2location.com/v2/?ip={fubonIp2LocationInput.Ip}&key={key}&package=WS1";
            return await ExecuteAsync(endpoint, new RestRequest(Method.GET));
        }
    }
    public class FubonIp2LocationInput{
        public string Ip {get;set;}
    }
}