using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;
using RestSharp;

namespace CoverGo.Gateway.Infrastructure.FubonServices.FubonServicesCommands
{
    public class FubonFpsQrCode : FubonCommand
    {
        private readonly string _fubonServiceUrl;
        public override string Name { get; } = "fubonFpsQRCode";

        public FubonFpsQrCode(string fubonServiceUrl, int apiTimeoutSetting)
            : base(apiTimeoutSetting)
        {
            _fubonServiceUrl = fubonServiceUrl;
        }
        
        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, IEnumerable<Claim> claims){
            FubonFpsQRCodeInput input = inputJson.ToObject<FubonFpsQRCodeInput>();
            
            RestRequest request = new(Method.POST);
            request.RequestFormat = DataFormat.Json;
            request.AddJsonBody(input!);

            string endpoint = $"{_fubonServiceUrl}/api/v1/qrCode";
            return await ExecuteAsync(endpoint, request);
        }
        
        public class FubonFpsQRCodeInput{
            public int imageWidth { get; set; }
            public int imageHeight { get; set; } 
            public decimal txnAmount { get; set; } 
            public string billNumber { get; set; }
        }
    }
}