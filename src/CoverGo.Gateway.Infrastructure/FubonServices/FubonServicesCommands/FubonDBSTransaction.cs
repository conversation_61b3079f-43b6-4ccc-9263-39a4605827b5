using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Transactions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Infrastructure.FubonServices.FubonServicesCommands;

public class FubonDBSTransaction : FubonCommand
{
    private const string DbsEndpoint = "https://dbshk.gateway.mastercard.com/api/rest/version/62/merchant/";
    
    public override string Name => "fubonDBSTransaction";

    private readonly ITransactionService _transactionService;
    
    public FubonDBSTransaction(
        ITransactionService transactionService,
        int apiTimeoutSetting) 
        : base(apiTimeoutSetting)
    {
        _transactionService = transactionService;
    }

    public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, IEnumerable<Claim> claims)
    {
        FubonDBSTransactionQuery input = inputJson.ToObject<FubonDBSTransactionQuery>();
        string transactionId = input.transactionId;
        
        if(!IsAuthorizedToReadTransactions(claims, transactionId))
            return Result<string>.Failure($"{tenantId}: You are not authorized to run this. You are missing 'readTransactions:{transactionId}' permission.\n");

        List<Transaction> transactions = await GetTransactionsByIdAsync(tenantId, transactionId);
        if (!transactions.Any())
            return Result<string>.Failure($"transaction {input.transactionId} not found");

        string orderId = transactions.First().ProviderTransactionId;
        return await GetOrderInfosFromDbsAsync(orderId);
    }

    private bool IsAuthorizedToReadTransactions(IEnumerable<Claim> claims, string transactionId)
    {
        List<string> allowedTxnIds = claims
            .Where(c => c.Type == "readTransactions")
            .Select(c => c.Value)
            .ToList();

        if (!allowedTxnIds.Any())
            return false;

        return allowedTxnIds.Contains("all") || allowedTxnIds.Contains(transactionId);
    }
    
    private async Task<List<Transaction>> GetTransactionsByIdAsync(string tenantId, string transactionId)
    {
        QueryArguments queryArgs = new() { Where = new TransactionWhere { Id = transactionId }} ;
        return await _transactionService.GetAsync(tenantId, queryArgs);
    }
    
    private async Task<Result<string>> GetOrderInfosFromDbsAsync(string orderId)
    {
        Result<string> result = await MakeRequestToDbsAsync(orderId);
        if (!result.IsSuccess)
            return result;

        ApiResponse resp = JsonConvert.DeserializeObject<ApiResponse>(result.Value);
        resp.transaction = resp.transaction
            .Where(t => t.result == "SUCCESS" && t.transaction.type == "PAYMENT")
            .ToList();
        
        return Result<string>.Success(JsonConvert.SerializeObject(resp));
    }

    private async Task<Result<string>> MakeRequestToDbsAsync(string orderId)
    {
        (string merchantId, string credentials) = GetMerchantIdAndCredentials();
        string endpoint = $"{DbsEndpoint}{merchantId}/order/{orderId}";

        RestRequest request = new(Method.GET);
        request.AddHeader("Authorization", "Basic " + credentials);
        return await ExecuteAsync(endpoint, request);
    }


    private (string, string) GetMerchantIdAndCredentials()
    {
        string merchantId = Environment.GetEnvironmentVariable("DBSHK_MERCHANTID");
        string apiKey = Environment.GetEnvironmentVariable("DBSHK_API_KEY");
        string credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"merchant.{merchantId}" + ":" + apiKey));

        return (merchantId, credentials);
    }
    
    public class FubonDBSTransactionQuery{
        public string transactionId { get; set; }
    }
    
    public class ApiResponse
    {
        public string id { get; set; }
        public decimal amount { get; set; }
        public DateTime creationTime { get; set; }
        public string currency { get; set; }
        public string merchant { get; set; }
        public List<TransactionFirstLevel> transaction { get; set; }
    }
    
    public class TransactionFirstLevel
    {
        public string result { get; set;} 
        public SourceOfFund sourceOfFunds { get; set; }
        public TransactionSecondLevel transaction { get; set; }
    }
    
    public class SourceOfFund
    {
        public Provided provided { get; set; }
    }
    
    public class Provided
    {
        public Card card { get; set; }
    }
    
    public class Card
    {
        public string nameOnCard { get; set; }
        public string number { get; set; }
        public string scheme { get; set; }
    }
    
    public class TransactionSecondLevel
    {
        public string authorizationCode { get; set; }
        public string type { get; set; }
    }
}
