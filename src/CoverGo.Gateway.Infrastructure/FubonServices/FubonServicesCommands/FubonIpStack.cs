using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;
using RestSharp;

namespace CoverGo.Gateway.Infrastructure.FubonServices.FubonServicesCommands
{
    public class FubonIpStack : FubonCommand
    {
        public FubonIpStack(int apiTimeoutSetting)
            : base(apiTimeoutSetting)
        {
        }
        
        public override string Name { get; } = "fubonIpStack";
        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, IEnumerable<Claim> claims){

            FubonIpStackInput fubonIpStackInput = inputJson.ToObject<FubonIpStackInput>();
            string key = tenantId == "fubon_uat" ? "9742895429fe414264f4b9fc76e8c724" : "6a62268ab9c67eac5a64275e672eeadc";

            string keyFromEnv = Environment.GetEnvironmentVariable("FUBON_IP_STACK_KEY");
            if (!String.IsNullOrWhiteSpace(keyFromEnv))
                key = keyFromEnv;

            string endpoint = $"http://api.ipstack.com/{fubonIpStackInput.Ip}?access_key={key}&format=1";
            return await ExecuteAsync(endpoint, new RestRequest(Method.GET));
        }
    }

      public class FubonIpStackInput{
        public string Ip {get;set;}
    }
}