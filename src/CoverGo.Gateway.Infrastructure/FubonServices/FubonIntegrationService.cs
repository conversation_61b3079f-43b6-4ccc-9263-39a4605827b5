using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Infrastructure.FubonServices
{
    public class FubonIntegrationService
    {
        private readonly IEnumerable<FubonCommand> _fubonCommands;

        public FubonIntegrationService(IEnumerable<FubonCommand> fubonCommands)
        {
            _fubonCommands = fubonCommands;
        }
        public async Task<Result<string>> ExecuteAsync(string tenantId, string commandType, JObject inputJson, IEnumerable<Claim> claims)
        {
            var command = _fubonCommands.FirstOrDefault(x => x.Name == commandType);

            if (command != null)
                return await command.ExecuteAsync(tenantId, inputJson, claims);

            return Result<string>.Failure($"No action for {commandType} found."); ;
        }
    }
}