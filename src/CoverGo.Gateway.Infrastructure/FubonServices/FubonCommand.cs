using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;
using RestSharp;

namespace CoverGo.Gateway.Infrastructure.FubonServices
{
    public abstract class FubonCommand
    {
        private readonly int _apiTimeoutSetting;
        
        public FubonCommand(int apiTimeoutSetting)
        {
            _apiTimeoutSetting = apiTimeoutSetting;
        }

        public abstract string Name { get; }
        public abstract Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, IEnumerable<Claim> claims);       

        protected virtual async Task<Result<string>> ExecuteAsync(string endpoint, RestRequest request)
        {
            try
            {
                RestClient client = new(endpoint)
                {
                    Timeout = _apiTimeoutSetting,
                };
                
                IRestResponse response = await client.ExecuteAsync(request);
                if (response.IsSuccessful != true)
                    return response.ResponseStatus == ResponseStatus.TimedOut ?
                    Result<string>.Failure($"Fubon API timeout (>{_apiTimeoutSetting / 1000}s). Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}") :
                    Result<string>.Failure($"Execute Fubon API failure. Response Status:{response.ResponseStatus}. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}. Response value decrypted: {response.Content}");
                return Result<string>.Success(response.Content);
            }
            catch (Exception ex)
            {
                return Result<string>.Failure(ex.Message);
            }
        }
    }
}