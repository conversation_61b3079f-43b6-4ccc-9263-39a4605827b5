﻿//using System;
//using System.Collections.Generic;
//using System.Net.Http;
//using System.Text;
//using System.Threading.Tasks;
//using CoverGo.Gateway.Domain.Pricing;
//using CoverGo.Gateway.Domain.Products.Motor;
//using Newtonsoft.Json;

//namespace CoverGo.Gateway.Infrastructure.Pricing.Motor
//{
//    public class CoverGoMotorPricingVenue : IPricingService<MotorPriceFactors>
//    {
//        private readonly HttpClient _client;

//        public CoverGoMotorPricingVenue(HttpClient client)
//        {
//            _client = client;
//        }

//        public async Task<IEnumerable<PriceDto>> GetPricingsAsync(string clientId, MotorPriceFactors factors)
//        {
//            HttpResponseMessage response = await _client.PostAsync(
//                $"api/v1/pricing/motor",
//                new StringContent(JsonConvert.SerializeObject(factors), Encoding.UTF8, "application/json"));

//            string json = await response.Content.ReadAsStringAsync();
//            return JsonConvert.DeserializeObject<IEnumerable<PriceDto>>(json);
//        }
//    }
//}
