﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Cms;

using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Infrastructure.Cms
{
    public class CoverGoCmsService : ICmsService
    {
        private readonly HttpClient _client;

        public CoverGoCmsService(HttpClient client)
        {
            _client = client;
        }

        public Task<long> GetTotalCountAsync(string tenantId, ComponentWhere where) => throw new System.NotImplementedException();

        public Task<IEnumerable<Component>> GetComponentsAsync(string tenantId, QueryArguments query) =>
            _client.GenericPostAsync<IEnumerable<Component>, QueryArguments>($"cms/api/{tenantId}/components/query", query);

        public Task<Result<string>> CreateComponentAsync(string tenantId, CreateComponentCommand command) =>
            _client.GenericPostAsync<Result<string>, CreateComponentCommand>($"cms/api/{tenantId}/components/create", command);

        public Task<Result> UpdateComponentAsync(string tenantId, string componentId, UpdateComponentCommand command) =>
            _client.GenericPostAsync<Result, UpdateComponentCommand>($"cms/api/{tenantId}/components/update/{componentId}", command);

        public Task<Result> DeleteComponentAsync(string tenantId, string componentId, DeleteComponentCommand command) =>
            _client.GenericPostAsync<Result, DeleteComponentCommand>($"cms/api/{tenantId}/components/delete/{componentId}", command);

        public Task<IEnumerable<CmsConfig>> GetConfigsAsync(string tenantId, QueryArguments query) =>
            _client.GenericPostAsync<IEnumerable<CmsConfig>, QueryArguments>($"cms/api/{tenantId}/configs/query", query);

        public Task<CmsConfig> GetConfigAsync(string tenantId, string appId) =>
            _client.GenericGetAsync<CmsConfig>($"cms/api/{tenantId}/configs/{appId}");

        public Task<Result> UpsertConfigAsync(string tenantId, string appId, CmsConfig config) =>
            _client.GenericPostAsync<Result, CmsConfig>($"cms/api/{tenantId}/configs/upsert/{appId}", config);

        public Task<Result> DeleteConfigAsync(string tenantId, string appId) =>
            _client.GenericDeleteAsync<Result>($"cms/api/{tenantId}/configs/delete/{appId}");

        public Task<Result> RebuildComponentModulesAsync(string tenantId, RebuildComponentModulesCommand command) =>
            _client.GenericPostAsync<Result, RebuildComponentModulesCommand>($"cms/api/{tenantId}/componentModules/rebuild", command);
    }
}
