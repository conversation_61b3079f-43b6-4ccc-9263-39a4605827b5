using System.Net.Http;
using CoverGo.Applications.Clients;
using CoverGo.Gateway.Domain.Claims;

namespace CoverGo.Gateway.Infrastructure.Claims
{
    public class CoverGoClaimRequestReasonService : CoverGoGenericGenericServiceRestClientBase<ClaimRequestReason, ClaimRequestReasonUpsert, ClaimRequestReasonFilter>
    {
        public CoverGoClaimRequestReasonService(HttpClient client) : base(client)
        {
        }

        protected override string ApiBaseUrlBuildInternal(string tenantId) => $"{tenantId}/api/v2/claims/requestReasons";
    }
}