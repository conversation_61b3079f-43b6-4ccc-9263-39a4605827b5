using System.Net.Http;
using CoverGo.Applications.Clients;
using CoverGo.Gateway.Domain.Claims;

namespace CoverGo.Gateway.Infrastructure.Claims
{
    public class CoverGoClaimRejectionReasonService : CoverGoGenericGenericServiceRestClientBase<ClaimRejectionReason, ClaimRejectionReasonUpsert, ClaimRejectionReasonFilter>
    {
        public CoverGoClaimRejectionReasonService(HttpClient client) : base(client)
        {
        }

        protected override string ApiBaseUrlBuildInternal(string tenantId) => $"{tenantId}/api/v2/claims/rejectionReasons";
    }
}