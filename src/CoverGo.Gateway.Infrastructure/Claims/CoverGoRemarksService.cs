using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Claims;

namespace CoverGo.Gateway.Infrastructure.Claims
{
    public class CoverGoRemarksService : IClaimRemarksService
    {
        private readonly HttpClient _httpClient;

        public CoverGoRemarksService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public Task DeleteAsync(string tenantId, string id) => _httpClient.DeleteAsync($"{tenantId}/api/v2/claims/remarks/{id}");
        public Task<IEnumerable<Remark>> GetAsync(string tenantId, QueryArguments<RemarkWhere> query) => _httpClient.GenericPostAsync<IEnumerable<Remark>, QueryArguments<RemarkWhere>>($"{tenantId}/api/v1/claims/remarks/query", query);
        public Task<long> GetTotalCountAsync(string tenantId, RemarkWhere where) => _httpClient.GenericPostAsync<long, RemarkWhere>($"{tenantId}/api/v2/claims/remarks/count", where);
        public Task UpsertAsync(string tenantId, RemarkUpsert remark) => _httpClient.PostAsJsonAsync($"{tenantId}/api/v2/claims/remarks", remark);
    }
}
