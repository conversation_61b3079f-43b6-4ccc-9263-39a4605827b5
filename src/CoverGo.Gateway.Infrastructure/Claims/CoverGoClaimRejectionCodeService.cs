using CoverGo.Applications.Clients;
using CoverGo.Gateway.Domain.Claims;

namespace CoverGo.Gateway.Infrastructure.Claims
{
    public class CoverGoClaimRejectionCodeService : CoverGoGenericGenericServiceRestClientBase<ClaimRejectionCode, ClaimRejectionCodeUpsert, ClaimRejectionCodeFilter>
    {
        public CoverGoClaimRejectionCodeService(System.Net.Http.HttpClient client) : base(client)
        {
        }

        protected override string ApiBaseUrlBuildInternal(string tenantId) => $"{tenantId}/api/v2/claims/rejectionCodes";
    }
}