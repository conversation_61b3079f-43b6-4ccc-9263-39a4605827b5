﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Claims;
using CoverGo.Gateway.Domain.Users;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Infrastructure.Claims
{
    public class CoverGoClaimService : IClaimService
    {
        private readonly HttpClient _client;

        public CoverGoClaimService(HttpClient client)
        {
            _client = client;
        }

        public Task<long> GetTotalCountAsync(string tenantId, ClaimWhere where) =>
             _client.GenericPostAsync<long, ClaimWhere>($"{tenantId}/api/v2/claims/totalCount", where);

        public Task<IEnumerable<Claim>> GetAsync(string tenantId, QueryArguments queryArguments) =>
             _client.GenericPostAsync<IEnumerable<Claim>, QueryArguments>($"{tenantId}/api/v2/claims/query", queryArguments);

        public Task<List<ClaimEventLog>> GetEventsAsync(string tenantId, EventQuery query) =>
          _client.GenericPostAsync<List<ClaimEventLog>, EventQuery>($"{tenantId}/api/v2/claims/events", query);

        public async Task<IDictionary<string, Claim>> GetDictionaryAsync(string tenantId, ClaimWhere where) =>
         (await GetAsync(tenantId, new QueryArguments { Where = where })).ToDictionary(x => x.Id);

        public async Task<ILookup<string, Claim>> GetByPolicyIdsLookupAsync(string tenantId, ClaimWhere where) =>
            (await GetAsync(tenantId, new QueryArguments { Where = where })).ToLookup(x => x.PolicyId);

        public Task<Result> GenerateAdhocClaimReport(string tenantId, ClaimReportOptions command) =>
            _client.GenericPostAsync<Result, ClaimReportOptions>($"{tenantId}/api/v2/claims/adhocReport/generate", command);


        public Task<Result<string>> CreateClaimAsync(string tenantId, CreateClaimCommand command, string accessToken = null) =>
             _client.GenericPostAsync<Result<string>, CreateClaimCommand>($"{tenantId}/api/v2/claims/create?accessToken={accessToken}", command);

        public Task<Result> UpdateClaimAsync(string tenantId, string claimId, UpdateClaimCommand command) =>
            _client.GenericPostAsync<Result, UpdateClaimCommand>($"{tenantId}/api/v2/claims/update/{claimId}", command);

        public Task<Result> DeleteAsync(string tenantId, string claimId, DeleteClaimCommand command) =>
            _client.GenericPostAsync<Result, DeleteClaimCommand>($"{tenantId}/api/v2/claims/delete/{claimId}", command);

        public Task<Result> RejectClaimAsync(string tenantId, string claimId, RejectClaimCommand command) =>
           _client.GenericPostAsync<Result, RejectClaimCommand>($"{tenantId}/api/v2/claims/reject/{claimId}", command);

        public Task<Result> AddBenefitToClaimAsync(string tenantId, string claimId, AddBenefitToClaimCommand command) =>
            _client.GenericPostAsync<Result, AddBenefitToClaimCommand>($"{tenantId}/api/v2/claims/addBenefit/{claimId}", command);

        public Task<Result> UpdateBenefitClaimAsync(string tenantId, string claimId, UpdateBenefitClaimCommand command) =>
            _client.GenericPostAsync<Result, UpdateBenefitClaimCommand>($"{tenantId}/api/v2/claims/updateBenefit/{claimId}", command);

        public Task<Result> RemoveBenefitFromClaimAsync(string tenantId, string claimId, RemoveBenefitFromClaimCommand command) =>
            _client.GenericPostAsync<Result, RemoveBenefitFromClaimCommand>($"{tenantId}/api/v2/claims/removeBenefit/{claimId}", command);

        public Task<IEnumerable<FactTemplate>> GetFactTemplatesAsync(string tenantId, IEnumerable<string> ids) =>
            _client.GenericPostAsync<IEnumerable<FactTemplate>, IEnumerable<string>>($"{tenantId}/api/v2/claims/factTemplate", ids);

        public Task<Result<CreatedStatus>> AddFactAsync(string tenantId, string claimId, AddFactCommand command) =>
             _client.GenericPostAsync<Result<CreatedStatus>, AddFactCommand>($"{tenantId}/api/v2/claims/addFact/{claimId}", command);
        public Task<Result> UpdateFactAsync(string tenantId, string claimId, UpdateFactCommand command) =>
             _client.GenericPostAsync<Result, UpdateFactCommand>($"{tenantId}/api/v2/claims/updateFact/{claimId}", command);
        public Task<Result> RemoveFactAsync(string tenantId, string claimId, RemoveFactCommand command) =>
             _client.GenericPostAsync<Result, RemoveFactCommand>($"{tenantId}/api/v2/claims/removeFact/{claimId}", command);
        public Task<Result> FactBatchAsync(string tenantId, string claimId, FactCommandBatch command) =>
          _client.GenericPostAsync<Result, FactCommandBatch>($"{tenantId}/api/v2/claims/{claimId}/factBatch", command);

        public Task<Result> AddNoteAsync(string tenantId, string claimId, AddNoteCommand command) =>
            _client.GenericPostAsync<Result, AddNoteCommand>($"{tenantId}/api/v2/claims/{claimId}/notes/add", command);
        public Task<Result> UpdateNoteAsync(string tenantId, string claimId, UpdateNoteCommand command) =>
            _client.GenericPostAsync<Result, UpdateNoteCommand>($"{tenantId}/api/v2/claims/{claimId}/notes/update", command);
        public Task<Result> RemoveNoteAsync(string tenantId, string claimId, RemoveNoteCommand command) =>
            _client.GenericPostAsync<Result, RemoveNoteCommand>($"{tenantId}/api/v2/claims/{claimId}/notes/remove", command);

        public Task<Result<string>> AddStakeholderToClaimAsync(string tenantId, string claimId, AddStakeholderCommand command)
          => _client.GenericPostAsync<Result<string>, AddStakeholderCommand>($"{tenantId}/api/v2/claims/{claimId}/stakeholders/add", command);
        public Task<Result> UpdateStakeholderOfClaimAsync(string tenantId, string claimId, UpdateStakeholderCommand command)
            => _client.GenericPostAsync<Result, UpdateStakeholderCommand>($"{tenantId}/api/v2/claims/{claimId}/stakeholders/update", command);
        public Task<Result> RemoveStakeholderFromClaimAsync(string tenantId, string claimId, RemoveStakeholderCommand command)
            => _client.GenericPostAsync<Result, RemoveStakeholderCommand>($"{tenantId}/api/v2/claims/{claimId}/stakeholders/remove", command);

        public Task<Result> AddGuaranteeOfPaymentToClaimAsync(string tenantId, string claimId, AddGuaranteeOfPaymentCommand command)
            => _client.GenericPostAsync<Result, AddGuaranteeOfPaymentCommand>($"{tenantId}/api/v2/claims/{claimId}/guaranteeOfPayments/add", command);

        public Task<Result> RemoveGuaranteeOfPaymentFromClaimAsync(string tenantId, string claimId, RemoveGuaranteeOfPaymentCommand command)
            => _client.GenericPostAsync<Result, RemoveGuaranteeOfPaymentCommand>($"{tenantId}/api/v2/claims/{claimId}/guaranteeOfPayments/remove", command);

        public async Task<ILookup<string, IEnumerable<GuaranteeOfPayment>>> GetAddedGuaranteeOfPaymentsByClaimIds(string tenantId, ClaimWhere where)
        {
            List<(string claimId, IEnumerable<GuaranteeOfPayment> guaranteeOfPayments)> linkedGuaranteeOfPayments =
                await GetAddedGuaranteeOfPaymentsByClaimIds(tenantId, new QueryArguments {Where = @where});
            return linkedGuaranteeOfPayments.ToLookup(x => x.claimId, x => x.guaranteeOfPayments);;
        }

        public async Task<List<(string claimId, IEnumerable<GuaranteeOfPayment> guaranteeOfPayments)>> GetAddedGuaranteeOfPaymentsByClaimIds(string tenantId, QueryArguments queryArguments)
            => await  _client.GenericPostAsync<List<(string claimId, IEnumerable<GuaranteeOfPayment> guaranteeOfPayments)>, QueryArguments>($"{tenantId}/api/v2/claims/query/addedGuaranteeOfPaymentsByClaims", queryArguments);

        public async Task<Result<CreatedStatus>> ClaimBatchAsync(string tenantId, ClaimBatchCommand claimBatchCommand, string accessToken = null)
            => await  _client.GenericPostAsync<Result<CreatedStatus>, ClaimBatchCommand>($"{tenantId}/api/v2/claims/batch?accessToken={accessToken}", claimBatchCommand);

        public Task<Result> AddAttachmentAsync(string tenantId, string claimId, AddAttachmentCommand command)
            => _client.GenericPostAsync<Result, AddAttachmentCommand>($"{tenantId}/api/v2/claims/{claimId}/attachments/add", command);

        public Task<Result> UpdateAttachmentAsync(string tenantId, string claimId, UpdateAttachmentCommand command)
            => _client.GenericPostAsync<Result, UpdateAttachmentCommand>($"{tenantId}/api/v2/claims/{claimId}/attachments/update", command);

        public Task<Result> RemoveAttachmentAsync(string tenantId, string claimId, RemoveAttachmentCommand command)
            => _client.GenericPostAsync<Result, RemoveAttachmentCommand>($"{tenantId}/api/v2/claims/{claimId}/attachments/remove", command);

        public Task<Result> ReverseClaim(string tenantId, string claimId, ReverseClaimCommand command) =>
            _client.GenericPostAsync<Result, ReverseClaimCommand>($"{tenantId}/api/v2/claims/{claimId}/reverse", command);
    }
}
