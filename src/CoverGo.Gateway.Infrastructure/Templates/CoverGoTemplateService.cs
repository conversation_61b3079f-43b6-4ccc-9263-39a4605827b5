﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Commissions;
using CoverGo.Gateway.Domain.Templates;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Infrastructure.Templates
{
    public class CoverGoTemplateService : ITemplateService
    {
        private readonly HttpClient _client;
        public CoverGoTemplateService(HttpClient client)
        {
            _client = client;
        }

        public Task<IEnumerable<Template>> GetAsync(string tenantId, QueryArguments queryArguments) =>
              _client.GenericPostAsync<IEnumerable<Template>, QueryArguments>($"{tenantId}/api/v1/templates/query", queryArguments);

        public Task<long> GetTotalCountAsync(string tenantId, TemplateWhere where) =>
             _client.GenericPostAsync<long, TemplateWhere>($"{tenantId}/api/v1/templates/totalCount", where);

        public Task<Result> AddEmailAttachmentTemplateAsync(string tenantId, string templateId, AddEmailAttachmentTemplateCommand command) =>
            _client.GenericPostAsync<Result, AddEmailAttachmentTemplateCommand>($"{tenantId}/api/v1/templates/email/addAttachmentTemplate/{templateId}", command);

        public Task<Result> AddEmailAttachmentReferenceAsync(string tenantId, string templateId, AddEmailAttachmentReferenceCommand command) =>
          _client.GenericPostAsync<Result, AddEmailAttachmentReferenceCommand>($"{tenantId}/api/v1/templates/email/addAttachmentReference/{templateId}", command);

        public Task<Result<string>> AddPdfDrawingAsync(string tenantId, string templateId, AddPdfDrawingCommand command) =>
            _client.GenericPostAsync<Result<string>, AddPdfDrawingCommand>($"{tenantId}/api/v1/templates/pdfDrawing/addDrawing/{templateId}", command);

        public Task<Result> CreateSmsTemplateAsync(string tenantId, CreateSmsTemplateCommand command) =>
            _client.GenericPostAsync<Result, CreateSmsTemplateCommand>($"{tenantId}/api/v1/templates/sms/create", command);

        public Task<Result> UpdateSmsTemplateAsync(string tenantId, string templateId, UpdateSmsTemplateCommand command) =>
            _client.GenericPostAsync<Result, UpdateSmsTemplateCommand>($"{tenantId}/api/v1/templates/sms/update/{templateId}", command);

        public Task<Result> CreateEmailMjmlTemplateAsync(string tenantId, CreateEmailMjmlTemplateCommand command) =>
            _client.GenericPostAsync<Result, CreateEmailMjmlTemplateCommand>($"{tenantId}/api/v1/templates/emailMjml/create", command);

        public Task<Result> CreatePdfDrawingTemplateAsync(string tenantId, CreatePdfDrawingTemplateCommand command) =>
            _client.GenericPostAsync<Result, CreatePdfDrawingTemplateCommand>($"{tenantId}/api/v1/templates/pdfDrawing/create", command);

        public Task<Result> DeleteTemplateAsync(string tenantId, string templateId, DeleteTemplateCommand command) =>
            _client.GenericPostAsync<Result, DeleteTemplateCommand>($"{tenantId}/api/v1/templates/delete/{templateId}", command);

        public Task<Result> RemoveEmailAttachmentTemplateAsync(string tenantId, string templateId, RemoveEmailAttachmentTemplateCommand command) =>
            _client.GenericPostAsync<Result, RemoveEmailAttachmentTemplateCommand>($"{tenantId}/api/v1/templates/email/removeAttachmentTemplate/{templateId}", command);

        public Task<Result> RemoveEmailAttachmentReferenceAsync(string tenantId, string templateId, RemoveCommand command) =>
            _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/templates/email/removeAttachmentReference/{templateId}", command);

        public Task<Result> RemovePdfDrawingAsync(string tenantId, string templateId, RemovePdfDrawingCommand command) =>
            _client.GenericPostAsync<Result, RemovePdfDrawingCommand>($"{tenantId}/api/v1/templates/pdfDrawing/removeDrawing/{templateId}", command);

        public Task<Result<string>> RenderEmailMjmlAsync(string tenantId, RenderEmailMjmlCommand renderEmailMjmlCommand) =>
            _client.GenericPostAsync<Result<string>, RenderEmailMjmlCommand>($"{tenantId}/api/v1/templates/email/render", renderEmailMjmlCommand);

        public Task<Result<EmailRendered>> RenderEmailAsync(string tenantId, string templateId, bool includeAttachments, RenderParameters body) =>
            _client.GenericPostAsync<Result<EmailRendered>, RenderParameters>($"{tenantId}/api/v1/templates/email/render/{templateId}?includeAttachments={includeAttachments}", body);

        public Task<Result<SmsRendered>> RenderSmsAsync(string tenantId, RenderSmsCommand command) =>
            _client.GenericPostAsync<Result<SmsRendered>, RenderSmsCommand>($"{tenantId}/api/v1/templates/sms/render", command);

        public Task<Result<SmsRendered>> RenderSmsAsync(string tenantId, string templateId, RenderParameters body) =>
            _client.GenericPostAsync<Result<SmsRendered>, RenderParameters>($"{tenantId}/api/v1/templates/sms/render/{templateId}", body);

        public Task<Result<byte[]>> RenderPdfDrawingAsync(string tenantId, string templateId, RenderParameters body) =>
            _client.GenericPostAsync<Result<byte[]>, RenderParameters>($"{tenantId}/api/v1/templates/pdfDrawing/render/{templateId}", body);

        public Task<Result<string>> RenderPdfDrawingV2Async(string tenantId, string templateId, RenderParameters body) =>
            _client.GenericPostAsync<Result<string>, RenderParameters>($"{tenantId}/api/v2/templates/pdfDrawing/render/{templateId}", body);

        public Task<Result> UpdateEmailAttachmentTemplateAsync(string tenantId, string templateId, UpdateEmailAttachmentTemplateCommand command) =>
            _client.GenericPostAsync<Result, UpdateEmailAttachmentTemplateCommand>($"{tenantId}/api/v1/templates/email/updateAttachmentTemplate/{templateId}", command);

        public Task<Result> UpdateEmailAttachmentReferenceAsync(string tenantId, string templateId, UpdateEmailAttachmentReferenceCommand command) =>
        _client.GenericPostAsync<Result, UpdateEmailAttachmentReferenceCommand>($"{tenantId}/api/v1/templates/email/updateAttachmentReference/{templateId}", command);

        public Task<Result> UpdateEmailMjmlTemplateAsync(string tenantId, string templateId, UpdateEmailMjmlTemplateCommand command) =>
            _client.GenericPostAsync<Result, UpdateEmailMjmlTemplateCommand>($"{tenantId}/api/v1/templates/emailMjml/update/{templateId}", command);

        public Task<Result> UpdatePdfDrawingAsync(string tenantId, string templateId, UpdatePdfDrawingCommand command) =>
            _client.GenericPostAsync<Result, UpdatePdfDrawingCommand>($"{tenantId}/api/v1/templates/pdfDrawing/updateDrawing/{templateId}", command);

        public Task<Result> UpdatePdfDrawingTemplateAsync(string tenantId, string templateId, UpdatePdfDrawingTemplateCommand command) =>
            _client.GenericPostAsync<Result, UpdatePdfDrawingTemplateCommand>($"{tenantId}/api/v1/templates/pdfDrawing/update/{templateId}", command);

        public Task<Result<string>> RenderWkhtmltopdfAsync(string tenantId, string templateId, string outputFilePath, RenderParameters body) =>
            _client.GenericPostAsync<Result<string>, RenderParameters>($"{tenantId}/api/v1/templates/wkhtmltopdf/render/{templateId}?p={outputFilePath}", body);

        public Task<Result<byte[]>> RenderWkhtmltopdfAsync(string tenantId, string templateId, RenderParameters body) =>
            _client.GenericPostAsync<Result<byte[]>, RenderParameters>($"{tenantId}/api/v1/templates/wkhtmltopdf/render/{templateId}", body);
        
        public Task<Result<string>> RenderWkhtmltopdfV2Async(string tenantId, string templateId, RenderParameters body) =>
            _client.GenericPostAsync<Result<string>, RenderParameters>($"{tenantId}/api/v2/templates/wkhtmltopdf/render/{templateId}", body);

        public Task<Result<byte[]>> RenderWkhtmltopdfAsync(string tenantId, RenderWkhtmltopdfCommand command) =>
            _client.GenericPostAsync<Result<byte[]>, RenderWkhtmltopdfCommand>($"{tenantId}/api/v1/templates/wkhtmltopdf/render/", command);

        public Task<Result<string>> RenderWkhtmltopdfV2Async(string tenantId, RenderWkhtmltopdfCommand command) =>
            _client.GenericPostAsync<Result<string>, RenderWkhtmltopdfCommand>($"{tenantId}/api/v2/templates/wkhtmltopdf/render/", command);

        public Task<Result<string>> CreateWkhtmltopdfTemplateAsync(string tenantId, CreateWkhtmltopdfTemplateCommand command) =>
            _client.GenericPostAsync<Result<string>, CreateWkhtmltopdfTemplateCommand>($"{tenantId}/api/v1/templates/wkhtmltopdf/create", command);

        public Task<Result> UpdateWkhtmltopdfTemplateAsync(string tenantId, string templateId, UpdateWkhtmltopdfTemplateCommand command) =>
            _client.GenericPostAsync<Result, UpdateWkhtmltopdfTemplateCommand>($"{tenantId}/api/v1/templates/wkhtmltopdf/update/{templateId}", command);

        public Task<Result<string>> AddPageObjectAsync(string tenantId, string templateId, AddPageObjectCommand command) =>
            _client.GenericPostAsync<Result<string>, AddPageObjectCommand>($"{tenantId}/api/v1/templates/wkhtmltopdf/{templateId}/addPageObject", command);

        public Task<Result> UpdatePageObjectAsync(string tenantId, string templateId, UpdatePageObjectCommand command) =>
            _client.GenericPostAsync<Result, UpdatePageObjectCommand>($"{tenantId}/api/v1/templates/wkhtmltopdf/{templateId}/updatePageObject", command);

        public Task<Result> RemovePageObjectAsync(string tenantId, string templateId, RemovePageObjectCommand command) =>
            _client.GenericPostAsync<Result, RemovePageObjectCommand>($"{tenantId}/api/v1/templates/wkhtmltopdf/{templateId}/removePageObject", command);

        public Task<Result<string>> RenderHtmlTemplateAsync(string tenantId, string templateId, RenderParameters renderParameters) =>
            _client.GenericPostAsync<Result<string>, RenderParameters>($"{tenantId}/api/v1/templates/html/render/{templateId}", renderParameters);
        public Task<Result<string>> RenderHtmlTemplateFromInstanceAsync(string tenantId, HtmlTemplateRenderFromInstanceCommand command) =>
            _client.GenericPostAsync<Result<string>, HtmlTemplateRenderFromInstanceCommand>($"{tenantId}/api/v1/templates/html/render/from-instance", command);

        public Task<Result<string>> RenderHtmlAsync(string tenantId, RenderHtmlCommand command) =>
            _client.GenericPostAsync<Result<string>, RenderHtmlCommand>($"{tenantId}/api/v1/templates/html/render/", command);
        public Task<Result<CreatedStatus>> CreateDynamicTemplateAsync(string tenantId, CreateDynamicTemplateCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreateDynamicTemplateCommand>($"{tenantId}/api/v1/templates/dynamic/create", command);

        public Task<Result> UpdateDynamicTemplateAsync(string tenantId, string templateId, UpdateDynamicTemplateCommand command) =>
            _client.GenericPostAsync<Result, UpdateDynamicTemplateCommand>($"{tenantId}/api/v1/templates/dynamic/update/{templateId}", command);

        public Task<Result> AddDynamicValueAsync(string tenantId, string templateId, AddDynamicValueCommand command) =>
            _client.GenericPostAsync<Result, AddDynamicValueCommand>($"{tenantId}/api/v1/templates/dynamic/addValue/{templateId}", command);

        public Task<Result> UpdateDynamicValueAsync(string tenantId, string templateId, UpdateDynamicValueCommand command) =>
            _client.GenericPostAsync<Result, UpdateDynamicValueCommand>($"{tenantId}/api/v1/templates/dynamic/updateValue/{templateId}", command);

        public Task<Result> RemoveDynamicValueAsync(string tenantId, string templateId, RemoveDynamicValueCommand command) =>
            _client.GenericPostAsync<Result, RemoveDynamicValueCommand>($"{tenantId}/api/v1/templates/dynamic/removeValue/{templateId}", command);

        public Task<Result<CreatedStatus>> CreateFunctionTemplateAsync(string tenantId, CreateFunctionTemplateCommand command) =>
           _client.GenericPostAsync<Result<CreatedStatus>, CreateFunctionTemplateCommand>($"{tenantId}/api/v1/templates/function/create", command);

        public Task<Result> UpdateFunctionTemplateAsync(string tenantId, string templateId, UpdateFunctionTemplateCommand command) =>
          _client.GenericPostAsync<Result, UpdateFunctionTemplateCommand>($"{tenantId}/api/v1/templates/function/{templateId}/update", command);

        public Task<Result<CreatedStatus>> AddInputToFunctionTemplateAsync(string tenantId, string templateId, AddFunctionInputCommand command) =>
          _client.GenericPostAsync<Result<CreatedStatus>, AddFunctionInputCommand>($"{tenantId}/api/v1/templates/function/{templateId}/inputs/add", command);
        public Task<Result> UpdateInputOfFunctionTemplateAsync(string tenantId, string templateId, UpdateFunctionInputCommand command) =>
          _client.GenericPostAsync<Result, UpdateFunctionInputCommand>($"{tenantId}/api/v1/templates/function/{templateId}/inputs/update", command);
        public Task<Result> RemoveInputFromFunctionTemplateAsync(string tenantId, string templateId, RemoveCommand command) =>
          _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/templates/function/{templateId}/inputs/remove", command);
        public Task<Result<CreatedStatus>> AddOutputToFunctionTemplateAsync(string tenantId, string templateId, AddFunctionOutputCommand command) =>
          _client.GenericPostAsync<Result<CreatedStatus>, AddFunctionOutputCommand>($"{tenantId}/api/v1/templates/function/{templateId}/outputs/add", command);
        public Task<Result> UpdateOutputOfFunctionTemplateAsync(string tenantId, string templateId, UpdateFunctionOutputCommand command) =>
          _client.GenericPostAsync<Result, UpdateFunctionOutputCommand>($"{tenantId}/api/v1/templates/function/{templateId}/outputs/update", command);
        public Task<Result> RemoveOutputFromFunctionTemplateAsync(string tenantId, string templateId, RemoveCommand command) =>
          _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/templates/function/{templateId}/outputs/remove", command);
        public Task<Result<IEnumerable<Output>>> RenderFunctionTemplateAsync(string tenantId, string templateId, RenderParameters renderParameters) =>
            _client.GenericPostAsync<Result<IEnumerable<Output>>, RenderParameters>($"{tenantId}/api/v1/templates/function/render/{templateId}", renderParameters);
        public Task<Result<CreatedStatus>> CreateNotificationTemplateAsync(string tenantId, CreateNotificationTemplateCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreateNotificationTemplateCommand>($"{tenantId}/api/v1/templates/notification/create", command);
        public Task<Result> UpdateNotificationTemplateAsync(string tenantId, string templateId, UpdateNotificationTemplateCommand command) =>
          _client.GenericPostAsync<Result, UpdateNotificationTemplateCommand>($"{tenantId}/api/v1/templates/notification/{templateId}/update", command);

        public Task<IEnumerable<TemplateRelationship>> GetTemplateRelationshipsAsync(string tenantId, TemplateRelationshipWhere where) =>
          _client.GenericPostAsync<IEnumerable<TemplateRelationship>, TemplateRelationshipWhere>($"{tenantId}/api/v1/templateRelationships/query", where);
        public Task<Result<CreatedStatus>> CreateTemplateRelationshipAsync(string tenantId, CreateTemplateRelationshipCommand command) =>
          _client.GenericPostAsync<Result<CreatedStatus>, CreateTemplateRelationshipCommand>($"{tenantId}/api/v1/templateRelationships/create", command);
        public Task<Result> DeleteTemplateRelationshipAsync(string tenantId, string templateRelationshipId, DeleteCommand command) =>
          _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/templateRelationships/delete/{templateRelationshipId}", command);

        public Task<Result> UpdateClauseHtmlTemplateAsync(string tenantId, string templateId, UpdateClauseHtmlTemplateCommand command) =>
            _client.GenericPostAsync<Result, UpdateClauseHtmlTemplateCommand>($"{tenantId}/api/v1/templates/clausehtml/update/{templateId}", command);
        public Task<Result<CreatedStatus>> CreateClauseHtmlTemplateAsync(string tenantId,  CreateClauseHtmlTemplateCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreateClauseHtmlTemplateCommand>($"{tenantId}/api/v1/templates/clausehtml/create", command);

        public Task<Result<string>> GetJobStatusAsync(string jobId) =>
            _client.GenericGetAsync<Result<string>>($"api/v1/backgroundjobs/{jobId}");
    }
}
