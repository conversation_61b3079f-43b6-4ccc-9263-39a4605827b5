﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Achievements;
using CoverGo.Gateway.Domain.Achievements.Achievements;
using CoverGo.Gateway.Domain.Achievements.AchievementTypes;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Infrastructure.Achievements
{
    public class CoverGoAchievementService : IAchievementService
    {
        private readonly HttpClient _client;

        public CoverGoAchievementService(HttpClient client)
        {
            _client = client;
        }

        public Task<long> GetAchievementTypesTotalCountAsync(string tenantId, AchievementTypeWhere where) =>
             _client.GenericPostAsync<long, AchievementTypeWhere>($"{tenantId}/api/v1/achievementTypes/totalCount", where);

        public Task<IEnumerable<AchievementType>> GetAchievementTypesAsync(string tenantId, QueryArguments queryArguments) =>
             _client.GenericPostAsync<IEnumerable<AchievementType>, QueryArguments>($"{tenantId}/api/v1/achievementTypes/query", queryArguments);

        public Task<Result<CreatedStatus>> CreateAchievementTypeAsync(string tenantId, CreateAchievementTypeCommand command) =>
             _client.GenericPostAsync<Result<CreatedStatus>, CreateAchievementTypeCommand>($"{tenantId}/api/v1/achievementTypes/create", command);

        public Task<Result> UpdateAchievementTypeAsync(string tenantId, string AchievementTypeId, UpdateAchievementTypeCommand command) =>
            _client.GenericPostAsync<Result, UpdateAchievementTypeCommand>($"{tenantId}/api/v1/achievementTypes/{AchievementTypeId}/update", command);

        public Task<Result> DeleteAchievementTypeAsync(string tenantId, string AchievementTypeId, DeleteCommand command) =>
            _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/achievementTypes/{AchievementTypeId}/delete", command);
        public Task<Result> ComputeAchievementTypesAsync(string tenantId, ComputeAchievementTypesCommand command) =>
    _client.GenericPostAsync<Result, ComputeAchievementTypesCommand>($"{tenantId}/api/v1/achievementTypes/computeAchievementTypes", command);

        public Task<long> GetAchievementsTotalCountAsync(string tenantId, AchievementWhere where) =>
             _client.GenericPostAsync<long, AchievementWhere>($"{tenantId}/api/v1/achievements/totalCount", where);

        public Task<IEnumerable<Achievement>> GetAchievementsAsync(string tenantId, QueryArguments queryArguments) =>
             _client.GenericPostAsync<IEnumerable<Achievement>, QueryArguments>($"{tenantId}/api/v1/achievements/query", queryArguments);
        public async Task<ILookup<string, Achievement>> GetAchievementsByEntityIdsLookupAsync(string tenantId, AchievementWhere where) =>
            (await GetAchievementsAsync(tenantId, new QueryArguments { Where = where })).ToLookup(x => x.EntityId);

        public Task<Result<CreatedStatus>> CreateAchievementAsync(string tenantId, CreateAchievementCommand command) =>
             _client.GenericPostAsync<Result<CreatedStatus>, CreateAchievementCommand>($"{tenantId}/api/v1/achievements/create", command);

        public Task<Result> UpdateAchievementAsync(string tenantId, string achievementId, UpdateAchievementCommand command) =>
            _client.GenericPostAsync<Result, UpdateAchievementCommand>($"{tenantId}/api/v1/achievements/{achievementId}/update", command);

        public Task<Result> DeleteAchievementAsync(string tenantId, string achievementId, DeleteCommand command) =>
            _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/achievements/{achievementId}/delete", command);

    }
}
