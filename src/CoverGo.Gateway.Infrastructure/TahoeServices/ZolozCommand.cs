﻿using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace CoverGo.Gateway.Infrastructure.TahoeServices
{
    public abstract class ZolozCommand
    {
        public ILogger _logger { get; set; }
        public abstract string Name { get; }
        public abstract Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson);
        private static readonly int _apiTimeoutSetting = 10000; // 10000 milliseconds == 10 seconds

        private readonly string _clientId = "2188447426858508"; //hardcoded value from zoloz dashboard

        private static readonly string _endpoint = "https://sg-sandbox-api.zoloz.com";

        protected virtual async Task<Result<string>> ExecuteAsync<T>(string tenantId, string endpointRoute, object requestObject)
             where T : ZolozResponseBody
        {
            try
            {
                RestRequest request = new(Method.POST)
                {
                    RequestFormat = DataFormat.Json
                };
                request.AddJsonBody(requestObject);

                PopulateRequestHeaders(request);

                RestClient client = new(_endpoint + endpointRoute)
                {
                    Timeout = _apiTimeoutSetting,
                };

                IRestResponse response = await client.ExecuteAsync(request);
                return HandleResponse(response, Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Zoloz {name} request failed. ||| Request Body: {requestBody}", Name, requestObject);
                return Result<string>.Failure(ex.Message);
            }

            void PopulateRequestHeaders(RestRequest request)
            {
                string requestTime = DateTime.UtcNow.ToLocalTime().ToString("yyyy-MM-ddTHH:mm:ss+0800");
                request.AddHeader("Request-Time", requestTime);
                request.AddHeader("Client-Id", _clientId);
                request.AddHeader("Signature", PopulateSignatureHeader());

                string PopulateSignatureHeader()
                {
                    string stringToEncrypt = $"POST {endpointRoute}\n{_clientId}.{requestTime}.{JsonConvert.SerializeObject(requestObject)}";
                    string encryptedSignature = Encrypt();
                    return $"algorithm=RSA256, signature={encryptedSignature}";

                    string Encrypt()
                    {
                        string privatekey = Environment.GetEnvironmentVariable("ZOLOZ_API_PRIVATE_KEY");
                        RSACryptoServiceProvider rsa = new(2048);
                        rsa.ImportPkcs8PrivateKey(Convert.FromBase64String(privatekey), out int _);

                        byte[] byteData = Encoding.ASCII.GetBytes(stringToEncrypt);
                        byte[] signed = rsa.SignData(byteData, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);

                        return HttpUtility.UrlEncode(Convert.ToBase64String(signed), Encoding.UTF8);
                    }
                }
            }

            Result<string> HandleResponse(IRestResponse response, string functionName)
            {
                string responseBody = response.Content;
                if (!response.IsSuccessful && responseBody?.Any() != true)
                {
                    string message = $"Zoloz {functionName} failed. ||| StatusCode: {response.StatusCode} ||| response: {responseBody}";
                    return Result<string>.Failure(message);
                }

                return MapToResult(responseBody);
            }

            Result<string> MapToResult(string responseString)
            {
                T responseBody = JsonConvert.DeserializeObject<T>(responseString);
                string combinedMessage = $"{responseBody.result.resultCode}: {responseBody.result.resultMessage}";

                return responseBody.result.resultStatus == "S"
                    ? Result<string>.Success(responseString)
                    : Result<string>.Failure($"Zoloz request failed with status '{responseBody.result.resultStatus}' ||| {combinedMessage}");
            }
        }
    }
}