﻿using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.TahoeServices
{
    public class ZolozDeletePrivacyInfoCommand : ZolozCommand
    {
        public override string Name => "deletePrivacyInfo";
        public string endpointRoute = "/api/v1/zoloz/privacyinfo/delete";

        public ZolozDeletePrivacyInfoCommand(ILogger logger)
        {
            _logger = logger;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            DeleteRequest request = inputJson.ToObject<DeleteRequest>();
            return await ExecuteAsync<DeleteResponse>(tenantId, endpointRoute, request);
        }

        private class DeleteRequest
        {
            public string transactionId { get; set; }
        }

        private class DeleteResponse : ZolozResponseBody
        {
        }
    }
}
