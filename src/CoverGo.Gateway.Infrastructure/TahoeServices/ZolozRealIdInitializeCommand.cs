﻿using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.TahoeServices
{
    public class ZolozRealIdInitializeCommand : ZolozCommand
    {
        public override string Name => "realIdInitialize";
        public string endpointRoute = "/api/v1/zoloz/realid/initialize";

        public ZolozRealIdInitializeCommand(ILogger logger)
        {
            _logger = logger;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            InitializeRequest initializeRequest = inputJson.ToObject<InitializeRequest>();

            return await ExecuteAsync<InitializeResponse>(tenantId, endpointRoute, initializeRequest);
        }

        private class InitializeRequest
        {
            public string bizId { get; set; } = Guid.NewGuid().ToString("N");
            public string metaInfo { get; set; } = "MOB_H5";
            public string flowType { get; set; } = "H5_REALIDLITE_KYC";
            public string docType { get; set; } = "08520000001";
            public string userId { get; set; } = Guid.NewGuid().ToString();
            public string operationMode { get; set; } = "CLOSED"; //Use while testing to ignore hard validation rules on id card recognition, remove when moving to production
            public h5ModeConfig h5ModeConfig { get; set; } = new();
        }

        private class h5ModeConfig
        {
            public string completeCallbackUrl { get; set; } = "https://tahoe-uat.quote.hk/en/brilliant-saver";
            public string interruptCallbackUrl { get; set; } = "https://tahoe-uat.quote.hk/en/brilliant-saver";
            public string isIframe { get; set; } = "Y";
            public string locale { get; set; } = "en";
        }

        private class InitializeResponse : ZolozResponseBody
        {
            public string transactionId { get; set; }
        }
    }
}
