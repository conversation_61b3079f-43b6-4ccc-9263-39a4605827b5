﻿using CsvHelper.Configuration;

namespace CoverGo.Gateway.Infrastructure.BOCServices
{
    public class BocRenewalRecord
    {
        public string date { get; set; }
        public string transactionDate { get; set; }
        public string transactionTime { get; set; }
        public string policyNo { get; set; }
        public string policyExpiryDate { get; set; }
        public string registrationNumber { get; set; }
        public string sumInsured { get; set; }
        public string email { get; set; }
    }

    public sealed class BocRenewalRecordMap : ClassMap<BocRenewalRecord>
    {
        public BocRenewalRecordMap()
        {
            Map(m => m.date);
            Map(m => m.transactionDate);
            Map(m => m.transactionTime);
            Map(m => m.policyNo);
            Map(m => m.policyExpiryDate);
            Map(m => m.registrationNumber);
            Map(m => m.sumInsured);
            Map(m => m.email);
        }
    }
}

