﻿using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.BOCServices
{
    public class BocIntegrationService
    {
        private readonly IEnumerable<BocCommand> _bocCommands;

        public BocIntegrationService(IEnumerable<BocCommand> bocCommands)
        {
            _bocCommands = bocCommands;
        }

        public async Task<Result<string>> ExecuteAsync(string tenantId, string commandType, JObject inputJson)
        {
            BocCommand command = _bocCommands.FirstOrDefault(x => x.Name == commandType);

            if (command == null) return Result<string>.Failure($"No action for {commandType} found.");

            Result<string> result = await command.ExecuteAsync(tenantId, inputJson);

            return result;
        }
    }
}
