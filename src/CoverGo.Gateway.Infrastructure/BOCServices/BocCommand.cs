﻿using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.BOCServices
{
    public abstract class BocCommand
    {
        public abstract string Name { get; }
        public abstract Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson);
        private static int _apiTimeoutSetting = 10000; // 10000 milliseconds == 10 seconds

        protected virtual async Task<Result<string>> ExecuteAsync(string endpoint, RestRequest request)
        {
            try
            {
                var client = new RestClient(endpoint)
                {
                    Timeout = _apiTimeoutSetting,
                };

                IRestResponse response = await client.ExecuteAsync(request);
                return HandleResponse(response, Name);
            }
            catch (Exception ex)
            {
                return Result<string>.Failure(ex.Message);
            }

            Result<string> HandleResponse(IRestResponse response, string functionName)
            {
                string responseBody = response.Content;
                if (!response.IsSuccessful && responseBody?.Any() != true)
                {
                    string message = $"BOC {functionName} failed. ||| StatusCode: {response.StatusCode} ||| response: {responseBody}";
                    return Result<string>.Failure(message);
                }

                return Result<string>.Success(responseBody);
            }
        }
    }
}
