﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Transactions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.BOCServices.BocCommands
{
    public class BocCreateTransactionFileCommand : BocCommand
    {

        public override string Name { get; } = "createTransactionFile";
        private readonly ITransactionService _transactionService;
        private readonly IPolicyService _policyService;
        private readonly IFileSystemService _fileSystemService;
        private readonly ILogger _logger;

        public BocCreateTransactionFileCommand(ITransactionService transactionService, IPolicyService policyService, IFileSystemService fileSystemService, ILogger<BocCreateTransactionFileCommand> logger)
        {
            _transactionService = transactionService;
            _policyService = policyService;
            _fileSystemService = fileSystemService;
            _logger = logger;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            var startDate = DateTime.Parse(inputJson.Value<string>("startDate"));
            var endDate = DateTime.Parse(inputJson.Value<string>("endDate"));

            IEnumerable<Transaction> transactions = await GetTransactionsInRangeAsync(tenantId, startDate, endDate);
            IEnumerable<Policy> policies = Enumerable.Empty<Policy>();
            if (transactions?.Any() == true)
                policies = await _policyService.GetAsync(tenantId, new Domain.QueryArguments { Where = new PolicyWhere { Id_in = transactions.Select(t => t.PolicyId)?.Where(v => v != null)?.ToList() } });

            Result<string> ret;
            try
            {
                ret = await CreateTxtAndUploadToFileSystemAsync(tenantId, transactions, policies);
            }
            catch (Exception e)
            {
                _logger.LogError("Error in processing transaction file, message: {message}", e.Message);
                return Result<string>.Failure(e.Message);
            }

            return ret;
        }

        private async Task<IEnumerable<Transaction>> GetTransactionsInRangeAsync(string tenantId, DateTime startDate, DateTime endDate) =>
            await _transactionService.GetAsync(tenantId, new Domain.QueryArguments
            {
                Where = new TransactionWhere
                {
                    And = new List<TransactionWhere>
                        {
                            new() { LastModifiedAt_gt = startDate },
                            new() { LastModifiedAt_lt = endDate },
                            new() { Status = TransactionStatus.Approved}
                        }
                }
            });

        private async Task<Result<string>> CreateTxtAndUploadToFileSystemAsync(string tenantId, IEnumerable<Transaction> data, IEnumerable<Policy> policies)
        {
            DateTime today = DateTime.Now;
            var builder = new StringBuilder();

            builder.AppendLine($"H{today:yyyyMMdd}{"",91}");

            data.ToList().ForEach(transaction =>
            {
                Policy linkedPolicy = policies.FirstOrDefault(f => f.Id == transaction.PolicyId);
                JToken policyReferenceNumber = linkedPolicy?.Facts?.LastOrDefault(f => f.Type == "referenceNumber")?.Value;
                if (policyReferenceNumber == null)
                    return;
                string authorizedCode = transaction.ProviderTransactionId ?? "000000";
                if (authorizedCode?.Length != 6)
                    authorizedCode = "0" + authorizedCode;
                string policyNumber = policyReferenceNumber.ToString();
                int paddingValue = GetPolicyNumberPaddingValue(policyNumber);
                builder.AppendLine($"D{"",16}{policyNumber.PadRight(paddingValue,' ')}{authorizedCode.ToString().PadLeft(6, '0')}{transaction.Amount?.ToString("0.00").Replace(".", "").PadLeft(11, '0')}{transaction.DateTime:yyyyMMdd}021001{"",27}");
            });
            builder.AppendLine($"T{today:yyyyMMdd}{data.Count().ToString().PadLeft(8, '0')}{"",83}");
            string textOutput = builder.ToString();

            string formattedDate = DateTime.Now.ToString("yyyyMMdd");
            string filePath = $"Daily Transaction/WEB_SETTLE_COVERGO_{formattedDate}.txt";

            Result uploadResult = await UploadFileToFileSystem(
                tenantId,
                filePath,
                Encoding.ASCII.GetBytes(textOutput));

            if (uploadResult.Status != "success")
            {
                _logger.LogInformation("Failure in uploading daily transaction");
                return Result<string>.Failure("Failure in uploading daily transaction");
            }

            return Result<string>.Success(filePath);         
        }

        private int GetPolicyNumberPaddingValue(string policyNumber)
        {
            int padThreshold = 25;
            int defaultPaddingLength = policyNumber.Length;
            int paddingToApply = padThreshold - defaultPaddingLength;

            return defaultPaddingLength + paddingToApply;
        }

        private async Task<Result> UploadFileToFileSystem(string tenantId, string key, byte[] content)
        {
            UploadFileCommand command = new()
            {
                Key = key,
                Content = content,
                IsPublic = false
            };

            return await _fileSystemService.UploadFileAsync(tenantId, null, command);
        }
    }
}
