﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.FileSystem;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.BOCServices.BocCommands
{
    public class BocCreateRenewalFileCommand : BocCommand
    {
        public override string Name { get; } = "createRenewalFile";
        private readonly IFileSystemService _fileSystemService;
        private readonly ILogger _logger;

        public BocCreateRenewalFileCommand(IFileSystemService fileSystemService, ILogger<BocCreateRenewalFileCommand> logger)
        {
            _fileSystemService = fileSystemService;
            _logger = logger;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            DateTime startDate = DateTime.Parse(inputJson.Value<string>("startDate"));
            DateTime endDate = DateTime.Parse(inputJson.Value<string>("endDate"));

            try
            {
                List<BocRenewalRecord> records = await GetRenewalRecords(tenantId, startDate, endDate);

                if (!records.Any())
                    return Result<string>.Failure($"No records found between {startDate} and {endDate}.");

                byte[] consolidatedRenewals = CreateConsolidatedRenewalFileAsync(records);

                string filePath = $"ConsolidatedRenewalLists/{DateTime.Now:dd-MM-yyyy}.csv";
                Result uploadResult = await _fileSystemService.UploadFileAsync(tenantId, null, new UploadFileCommand
                {
                    Key = filePath,
                    Content = consolidatedRenewals,
                    IsPublic = false
                });

                if (uploadResult.Status != "success")
                {
                    _logger.LogInformation("Failure in uploading endorsement file");
                    return Result<string>.Failure("Failure in uploading endorsement file");
                }

                return Result<string>.Success(filePath);
            }
            catch (Exception e)
            {
                _logger.LogError("Error in processing renewal file, message: {message}", e.Message);
                return Result<string>.Failure(e.Message);
            }
        }

        private async Task<List<BocRenewalRecord>> GetRenewalRecords(string tenantId, DateTime startDate, DateTime endDate)
        {
            Result<byte[]> endorsementFile = await _fileSystemService.GetFileAsync(tenantId, null, new GetFileCommand { Key = $"MakeRenewal/MakeRenewal.csv" });
            if (endorsementFile.Status == "failure")
                return new List<BocRenewalRecord> { };

            using var reader = new StreamReader(new MemoryStream(endorsementFile.Value));
            using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.CurrentCulture) { Delimiter = ",", HeaderValidated = null, BadDataFound = null, MissingFieldFound = null });
            {
                csv.Context.RegisterClassMap<BocRenewalRecordMap>();
                IEnumerable<BocRenewalRecord> records = csv.GetRecords<BocRenewalRecord>();

                IEnumerable<BocRenewalRecord> recordsInDateRange = records.Where(r =>
                {
                    string dateWithoutHiddenNewlineChar = char.IsLetterOrDigit(r.date.First()) ? r.date : r.date[1..];
                    DateTime dateTime = DateTime.Parse(dateWithoutHiddenNewlineChar).ToUniversalTime();
                    return startDate <= dateTime &&
                        dateTime <= endDate;
                });

                return recordsInDateRange.ToList();
            }
        }

        private byte[] CreateConsolidatedRenewalFileAsync(List<BocRenewalRecord> records)
        {
            MapDownloadDateToEachRecord();

            using var sFile = new MemoryStream();
            using (TextWriter writer = new StreamWriter(sFile, Encoding.UTF8))
            {
                var csv = new CsvWriter(writer, CultureInfo.CurrentCulture);
                csv.WriteRecords(records);
            }

            return sFile.ToArray();

            void MapDownloadDateToEachRecord()
            {
                foreach (BocRenewalRecord record in records)
                    record.date = DateTime.UtcNow.AddHours(8).ToString("yyyyMMdd");
            }
        }
    }
}
