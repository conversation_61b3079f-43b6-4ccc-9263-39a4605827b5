﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Infrastructure.DLVNServices.DLVNCommands;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.BOCServices.BocCommands
{
    public class BocGetRenewalInfoCommand : BocCommand
    {
        public override string Name => "getRenewalInfo";
        private readonly string _endpoint;
        private readonly IPolicyService _policyService;
        private readonly ILogger<BocGetRenewalInfoCommand> _logger;

        public BocGetRenewalInfoCommand(string endpoint, IPolicyService policyService, ILogger<BocGetRenewalInfoCommand> logger)
        {
            _endpoint = endpoint;
            _policyService = policyService;
            _logger = logger;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            string policyNumber = inputJson.Value<string>("issuerNumber");
            var policies = await _policyService.GetAsync(tenantId, new PolicyWhere
            {
                IssuerNumber_contains = policyNumber
            });

            var getRenewalRequest = new GetRenewalInfoRequest { policyNo = policyNumber, licenseNo = inputJson.Value<string>("licenseNo") };

            var request = new RestRequest(Method.POST)
            {
                RequestFormat = DataFormat.Json
            };
            request.AddJsonBody(getRenewalRequest);

            Result<string> response = await ExecuteAsync(_endpoint, request);

            _logger.LogInformation($"[BocGetRenewalInfoCommand] BOC API at endpoint : {_endpoint} ||| Request: {JsonConvert.SerializeObject(getRenewalRequest)} ||| Result: {response.Value}");
            GetRenewalInfoResult getRenewalInfoResult = JsonConvert.DeserializeObject<GetRenewalInfoResult>(response.Value);

            var policy = policies.FirstOrDefault();
            getRenewalInfoResult.basePolicy = policy != null ? new GetRenewalInfoResultPolicy
            {
                ProductId = policy.ProductId,
                StartDate = policy.StartDate,
                EndDate = policy.EndDate
            } : null;

            return response.Status != "success"
                ? Result<string>.Failure($"GetCoverNoteInfo failed" + " ||| Request: " + JsonConvert.SerializeObject(getRenewalRequest) + " ||| Result:" + response.Value)
                : Result<string>.Success(JsonConvert.SerializeObject(getRenewalInfoResult));
        }

        private class GetRenewalInfoRequest
        {
            public string reqMethod { get; set; } = "getRenewalInfo";
            public string policyNo { get; set; }
            public string licenseNo { get; set; }
        }

        private class GetRenewalInfoResult
        {
            public string rtnCode { get; set; }
            public string rtnMsg { get; set; }
            public GetRenewalInfoResultContent content { get; set; }
            public GetRenewalInfoResultPolicy basePolicy { get; set; }
        }

        private class GetRenewalInfoResultContent
        {
            public string policyNo { get; set; }
            public string expiryDate { get; set; }
            public string seatCapacity { get; set; }
            public string licenseNo { get; set; }
            public string vehicleValue { get; set; }
            public string Flag { get; set; }
            public string planType { get; set; }
            public string startDate { get; set; }
            public string premiumBZ { get; set; }
            public string mibPremiumBZ { get; set; }
            public string premium02 { get; set; }
            public string mibPremium02 { get; set; }
            public string sumInsured02 { get; set; }
            public string premium03 { get; set; }
            public string mibPremium03 { get; set; }
        }

        private class GetRenewalInfoResultPolicy
        {
            public ProductId ProductId { get; set; }
            public DateTime? StartDate { get; set; }
            public DateTime? EndDate { get; set; }
        }
    }
}
