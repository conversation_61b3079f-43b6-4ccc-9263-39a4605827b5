﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.FileSystem;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.BOCServices.BocCommands
{
    public class BocGetCarModelsCommand : BocCommand
    {
        public override string Name => "getCarModels";
        private readonly IFileSystemService _fileSystemService;
        private readonly ILogger _logger;

        public BocGetCarModelsCommand(IFileSystemService fileSystemService, ILogger<BocGetCarModelsCommand> logger)
        {
            _fileSystemService = fileSystemService;
            _logger = logger;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            try
            {
                string filePath = inputJson?.ToString() ?? "Imported Data/Vehicle Modals.csv";
                List<BocCarModelRecord> records = await GetRecords();

                return !records.Any()
                    ? Result<string>.Failure($"No car models found in file {filePath}.")
                    : Result<string>.Success(JsonConvert.SerializeObject(new BocCarModelList { Records = records }));

                async Task<List<BocCarModelRecord>> GetRecords()
                {
                    Result<byte[]> carModelFile = await _fileSystemService.GetFileAsync(tenantId, null, new GetFileCommand { Key = filePath });
                    if (carModelFile.Status == "failure")
                        return new List<BocCarModelRecord> { };

                    using var reader = new StreamReader(new MemoryStream(carModelFile.Value));
                    using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.CurrentCulture) { Delimiter = ",", HeaderValidated = null, BadDataFound = null, MissingFieldFound = null });
                    {
                        csv.Context.RegisterClassMap<BocCarModelRecordMap>();
                        return csv.GetRecords<BocCarModelRecord>().ToList();
                    }
                }
            }
            catch (Exception e)
            {
                _logger.LogError("Error in processing car model file, message: {message}", e.Message);
                return Result<string>.Failure(e.Message);
            }
        }

        private class BocCarModelList
        {
            public List<BocCarModelRecord> Records { get; set; }
        }

        private class BocCarModelRecord
        {
            public string Manufacturer { get; set; }
            public string Factory { get; set; }
            public string ModelName { get; set; }
            public string ModelCode { get; set; }
            public string RiskClass { get; set; }
        }

        private class BocCarModelRecordMap : ClassMap<BocCarModelRecord>
        {
            public BocCarModelRecordMap()
            {
                Map(m => m.Manufacturer);
                Map(m => m.Factory);
                Map(m => m.ModelName);
                Map(m => m.ModelCode);
                Map(m => m.RiskClass);
            }
        }
    }
}
