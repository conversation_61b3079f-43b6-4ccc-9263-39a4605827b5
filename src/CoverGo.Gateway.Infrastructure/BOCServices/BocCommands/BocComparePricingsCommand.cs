﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.BOCServices.BocCommands
{
    public class BocComparePricingsCommand : BocCommand
    {
        public override string Name => "comparePricings";

        private readonly IPricingService _pricingService;
        private readonly ILogger _logger;

        public BocComparePricingsCommand(IPricingService pricingService, ILogger logger)
        {
            _pricingService = pricingService;
            _logger = logger;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            try
            {
                JObject tariffProductIdJObject = inputJson.Value<JObject>("tariffProductId");
                ProductId tariffProductId = new()
                {
                    Plan = tariffProductIdJObject.Value<string>("plan"),
                    Version = tariffProductIdJObject.Value<string>("version"),
                    Type = tariffProductIdJObject.Value<string>("type")
                };

                string tariffPrice = (await _pricingService.GetPricingsAsync(tenantId, new PriceFilter
                {
                    Factors = inputJson.Value<JObject>("tariffValues"),
                    PricingDate = DateTime.UtcNow,
                    ProductIds = new List<ProductId> { tariffProductId }
                })).FirstOrDefault()?.Amount?.ToString("0.00");

                string GLMPrice = (await _pricingService.GetPricingsAsync(tenantId, new PriceFilter
                {
                    Factors = inputJson.Value<JObject>("GLMValues"),
                    PricingDate = DateTime.UtcNow,
                    ProductIds = new List<ProductId> { GetGLMProductId(tariffProductId) }
                })).FirstOrDefault()?.Amount?.ToString("0.00");

                Dictionary<string, string> dict = new()
                {
                    { "tariffPrice", tariffPrice },
                    { "GLMPrice", GLMPrice }
                };

                return Result<string>.Success(JsonConvert.SerializeObject(dict));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Boc compare pricings failed. ||| Input: {input}", inputJson);
                return Result<string>.Failure(ex.Message);
            }

            ProductId GetGLMProductId(ProductId tariffProductId)
            {
                return new ProductId
                {
                    Plan = GetGLMProductPlanFromProductPlan(),
                    Type = tariffProductId.Type,
                    Version = tariffProductId.Version
                };

                string GetGLMProductPlanFromProductPlan() => tariffProductId.Plan
                        switch
                {
                    "motor_insurance_third_party_tariff" => "motor_insurance_third_party",
                    "motor_insurance_comprehensive_tariff" => "motor_insurance_comprehensive",
                    _ => null
                };
            }
        }
    }
}
