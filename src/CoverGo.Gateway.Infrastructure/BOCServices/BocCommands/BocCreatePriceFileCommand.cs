﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Users.Domain.Individuals;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CoverGo.Threading.Tasks;
using Microsoft.Extensions.Options;
using CoverGo.Gateway.Domain.Users;

namespace CoverGo.Gateway.Infrastructure.BOCServices.BocCommands
{
    public class BocCreatePriceFileCommand : BocCommand
    {
        public override string Name => "createPriceFile";

        private static IPolicyService _policyService;
        private static IPricingService _pricingService;
        private static IFileSystemService _fileSystemService;
        private static ILogger _logger;

        public BocCreatePriceFileCommand(
            IPolicyService policyService,
            IPricingService pricingService,
            IFileSystemService fileSystemService,
            ILogger<BocCreatePriceFileCommand> logger
            )
        {
            _policyService = policyService;
            _pricingService = pricingService;
            _fileSystemService = fileSystemService;
            _logger = logger;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            var startDate = DateTime.Parse(inputJson.Value<string>("startDate"));
            var endDate = DateTime.Parse(inputJson.Value<string>("endDate"));

            try
            {
                List<Policy> policies = await _policyService.GetAsync(tenantId, new Domain.QueryArguments
                {
                    Where = new PolicyWhere
                    {
                        And = new List<PolicyWhere> {
                            new() { IssueDate_gt = startDate },
                            new() { IssueDate_lt = endDate }
                        }
                    }
                });

                var records = await policies.ParallelSelectAsync(async p => await MapRecord(p), new ParallelRunOptions(){MaxDegreeOfParallelism = 10});

                byte[] csvFile = CreateFileAsync(records);

                string filePath = $"PriceRecord/{DateTime.Now:dd-MM-yyyy}.csv";
                Result uploadResult = await _fileSystemService.UploadFileAsync(tenantId, null, new UploadFileCommand
                {
                    Key = filePath,
                    Content = csvFile,
                    IsPublic = false
                });

                if (uploadResult.Status != "success")
                {
                    _logger.LogInformation("Failure in uploading endorsement file");
                    return Result<string>.Failure("Failure in uploading endorsement file");
                }

                return Result<string>.Success(filePath);
            }
            catch (Exception e)
            {
                _logger.LogError("Error in processing renewal file, message: {message}", e.Message);
                return Result<string>.Failure(e.Message);
            }

            async Task<PriceRecord> MapRecord(Policy policy)
            {
                JToken insuredValue = policy.Values?.Value<JArray>("insureds")?.Children().FirstOrDefault();
                string manufactureYear = GetPolicyFactValue<string>("yearOfManufactory");
                var insured = policy.ContractInsured.FirstOrDefault() as Individual;
                return new PriceRecord
                {
                    SumAssured = insuredValue.Value<long?>("commercialThirdPartyLiability").GetValueOrDefault(),
                    VehicleSeats = GetPolicyFactValue<string>("seatingCapacity"),
                    VehicleCapacity = GetPolicyFactValue<string>("engineCapacity"),
                    VehicleAge = (DateTime.UtcNow.Year - int.Parse(manufactureYear)).ToString(),
                    DriverAge = (DateTime.UtcNow.Year - insured.DateOfBirth.GetValueOrDefault().Year).ToString(),
                    ClientType = GetHolderFactValue<string>("typeOfClient"),
                    NCD = GetPolicyFactValue<string>("ncd"),
                    TariffPrice = policy.Premium.Amount?.ToString("0.00"),
                    GLMPrice = (await _pricingService.GetPricingsAsync(tenantId, new PriceFilter { 
                        Factors = policy.Values, 
                        DiscountCodes = policy.Premium.DiscountCodes,
                        PricingDate = policy.IssueDate.GetValueOrDefault(),
                        ProductIds = new List<ProductId> { 
                            new() { Plan = GetGLMProductPlanFromProductPlan() , Version = policy.ProductId.Version, Type = policy.ProductId.Type } 
                        } 
                    })).FirstOrDefault()?.Amount?.ToString("0.00")
                };

                T GetHolderFactValue<T>(string type, string key = null)
                    => GetFactValue<T>(policy.ContractHolder?.Facts?.ToList(), type, key);

                T GetPolicyFactValue<T>(string type, string key = null)
                        => GetFactValue<T>(policy.Facts, type, key);

                T GetFactValue<T>(List<Fact> facts, string type, string key = null)
                {
                    Fact fact = facts?.FirstOrDefault(f => f.Type == type);
                    return fact?.Value == null
                           ? default(T)
                           : key == null
                               ? fact.Value.Value<T>()
                               : fact.Value.Value<T>(key);
                }

                string GetGLMProductPlanFromProductPlan() => (policy.ProductId.Plan)
                    switch
                {
                    "motor_insurance_third_party_tariff" => "motor_insurance_third_party",
                    "motor_insurance_comprehensive_tariff" => "motor_insurance_comprehensive",
                    _ => null
                };
            }
        }

        private static byte[] CreateFileAsync(IEnumerable<PriceRecord> records)
        {
            using var sFile = new MemoryStream();
            using (TextWriter writer = new StreamWriter(sFile, Encoding.UTF8))
            {
                var csv = new CsvWriter(writer, CultureInfo.CurrentCulture);
                csv.WriteRecords(records);
            }

            return sFile.ToArray();
        }

        private class PriceRecord
        {
            public long? SumAssured { get; set; }
            public string VehicleAge { get; set; }
            public string VehicleCapacity { get; set; }
            public string VehicleSeats { get; set; }
            public string DriverAge { get; set; }
            public string ClientType { get; set; }
            public string NCD { get; set; }
            public string GLMPrice { get; set; }
            public string TariffPrice { get; set; }
        }

        private sealed class PriceRecordMap : ClassMap<PriceRecord>
        {
            public PriceRecordMap()
            {
                Map(m => m.SumAssured);
                Map(m => m.VehicleAge);
                Map(m => m.VehicleCapacity);
                Map(m => m.VehicleSeats);
                Map(m => m.DriverAge);
                Map(m => m.ClientType);
                Map(m => m.NCD);
                Map(m => m.GLMPrice);
                Map(m => m.TariffPrice);
            }
        }
    }
}
