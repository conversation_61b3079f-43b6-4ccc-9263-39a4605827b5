﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Policies;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.BOCServices.BocCommands
{
    public class BocGetRenewalInfo2Command : BocCommand
    {
        public override string Name => "getRenewalInfo2";
        private readonly string _endpoint;
        private readonly IPolicyService _policyService;
        private readonly ILogger<BocGetRenewalInfo2Command> _logger;

        public BocGetRenewalInfo2Command(string endpoint, IPolicyService policyService, ILogger<BocGetRenewalInfo2Command> logger)
        {
            _endpoint = endpoint;
            _policyService = policyService;
            _logger = logger;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            string policyNumber = inputJson.Value<string>("policyNo");
            string licenseNo = inputJson.Value<string>("licenseNo");

            var getRenewalRequest = new GetRenewalInfo2Request { policyNo = policyNumber, licenseNo = licenseNo };

            var request = new RestRequest(Method.POST)
            {
                RequestFormat = DataFormat.Json
            };
            request.AddJsonBody(getRenewalRequest);

            Result<string> response = await ExecuteAsync(_endpoint, request);

            GetRenewalInfo2Result getRenewalInfoResult = JsonConvert.DeserializeObject<GetRenewalInfo2Result>(response.Value);

            _logger.LogInformation($"[BocGetRenewalInfo2Command] BOC API at endpoint : {_endpoint} ||| Request: {JsonConvert.SerializeObject(getRenewalRequest)} ||| Result: {response.Value}");
            return response.Status != "success"
                ? Result<string>.Failure($"GetRenewalInfo2 failed" + " ||| Request: " + JsonConvert.SerializeObject(getRenewalRequest) + " ||| Result:" + response.Value)
                : Result<string>.Success(JsonConvert.SerializeObject(getRenewalInfoResult));

        }


        public class GetRenewalInfo2Request
        {
            public string reqMethod { get; set; } = "getRenewalInfo2";
            public string policyNo { get; set; }
            public string licenseNo { get; set; }
        }

        public class GetRenewalInfo2Result
        {
            public string rtnCode { get; set; }
            public string rtnMsg { get; set; }
            public GetRenewalInfo2ResultContent content { get; set; }
        }

        public class GetRenewalInfo2ResultContent
        {
            public ProposedBaseInfo proposedBaseInfo { get; set; }
            public List<NamedDriverInfo> namedDriverInfo { get; set; }
            public HzmbInsuranceInfo hzmbInsuranceInfo { get; set; }
            public FeeInfo feeInfo { get; set; }
            public ProposerInfo proposerInfo { get; set; }
            public CarInfo carInfo { get; set; }
            public UwQuestions uwQuestions { get; set; }
            public bool assigned_to_specific_email { get; set; }
            public string specific_email { get; set; }
            public List<ExcessList> excessList { get; set; }
        }

        public class CarInfo
        {
            public string manufacturer { get; set; }
            public string model { get; set; }
            public string modelDesc { get; set; }
            public string manufactureYear { get; set; }
            public string vehicleAge { get; set; }
            public string vehicleValue { get; set; }
            public string licenseNo { get; set; }
            public string seatCapacity { get; set; }
            public string engineCapacity { get; set; }
            public string chassisNo { get; set; }
            public string engineNo { get; set; }
            public string vehicleClass { get; set; }
            public string bodyType { get; set; }
            public string hirePurchaseCode { get; set; }
            public string insuranceCertificate { get; set; }
            public string modelLoadRate { get; set; }
        }

        public class ExcessList
        {
            public string seqNo { get; set; }
            public string excessCode { get; set; }
            public string excessEName { get; set; }
            public string excessTName { get; set; }
            public string excessRate { get; set; }
            public string amount { get; set; }
        }

        public class FeeInfo
        {
            public string commissionRate { get; set; }
            public string commissionPremium { get; set; }
            public string discountRate { get; set; }
            public string discountPremium { get; set; }
            public string mibRate { get; set; }
            public string mibPremium { get; set; }
            public string iaRate { get; set; }
            public string iaPremium { get; set; }
            public string grossPremium { get; set; }
            public string netPremium { get; set; }
            public string totalPremium { get; set; }
        }

        public class HzmbInsuranceInfo
        {
            public string expiryDate { get; set; }
            public string flag { get; set; }
            public string mibPremium02 { get; set; }
            public string mibPremium03 { get; set; }
            public string mibPremiumBZ { get; set; }
            public string planType { get; set; }
            public string premium02 { get; set; }
            public string premium03 { get; set; }
            public string premiumBZ { get; set; }
            public string startDate { get; set; }
            public string sumInsured02 { get; set; }
            public bool assigned_to_specific_email { get; set; }
            public string specific_email { get; set; }
        }

        public class NamedDriverInfo
        {
            public string drivingExperience { get; set; }
            public string namedDriverBirthDay { get; set; }
            public string namedDriverGender { get; set; }
            public string namedDriverIdCard { get; set; }
            public string namedDriverName { get; set; }
            public string namedDriverOccupation { get; set; }
            public string namedDriverBirthday { get; set; }
            public string relWithProposer { get; set; }
        }

        public class ProposedBaseInfo
        {
            public string proposedType { get; set; }
            public string proposedCode { get; set; }
            public string agentNo { get; set; }
            public string inceptionDate { get; set; }
            public string expiryDate { get; set; }
            public string rebateRate { get; set; }
            public string ncd { get; set; }
            public string preCreditCardNo { get; set; }
            public string promotionCode { get; set; }
            public string b2bPolicyNo { get; set; }
        }

        public class ProposerInfo
        {
            public string proposerIdentity { get; set; }
            public string clientGroup { get; set; }
            public string proposerName { get; set; }
            public string proposerGender { get; set; }
            public string proposerIdCard { get; set; }
            public string proposerBirthDay { get; set; }
            public string proposerOccupation { get; set; }
            public string proposerOccupationN { get; set; }
            public string proposerMobile { get; set; }
            public string proposerEmail { get; set; }
            public string proposerAddress { get; set; }
            public string proposerDistrict { get; set; }
            public string directSalePromotion { get; set; }
            public string directSaleEmail { get; set; }
            public string directSaleSMS { get; set; }
            public string directSaleMail { get; set; }
            public string directSalePhone { get; set; }
        }

        public class UwQuestions
        {
            public string accidentNum { get; set; }
            public string drivingOffenceFlag { get; set; }
        }
    }
}
