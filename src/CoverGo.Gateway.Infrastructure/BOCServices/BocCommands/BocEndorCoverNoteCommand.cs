﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.FileSystem;
using CsvHelper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.BOCServices.BocCommands
{
    public class BocEndorCoverNoteCommand : BocCommand
    {
        public readonly string _endpoint;

        public override string Name { get; } = "endorCoverNote";
        private readonly IFileSystemService _fileSystemService;
        private readonly ILogger<BocEndorCoverNoteCommand> _logger;

        public BocEndorCoverNoteCommand(string endpoint, IFileSystemService fileSystemService, ILogger<BocEndorCoverNoteCommand> logger)
        {
            _endpoint = endpoint;
            _fileSystemService = fileSystemService;
            _logger = logger;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            var endorCoverNoteRequest = new EndorCoverNoteRequest { coverNoteNo = inputJson.Value<string>("issuerNumber"), licenseNo = inputJson.Value<string>("licenseNo") };

            var request = new RestRequest(Method.POST)
            {
                RequestFormat = DataFormat.Json
            };

            request.AddJsonBody(endorCoverNoteRequest);

            _logger.LogInformation($"[BocEndorCoverNoteCommand] sending request to BOC core system : {JsonConvert.SerializeObject(endorCoverNoteRequest)}");
            Result<string> response = await ExecuteAsync(_endpoint, request);
            _logger.LogInformation($"[BocEndorCoverNoteCommand] recieved response from BOC core system : {response.Value}, Status:{response.Status}");

            EndorCoverNoteResult endorsementResult = JsonConvert.DeserializeObject<EndorCoverNoteResult>(response.Value);
            var errors = new List<string> { };
            string policyNo = endorsementResult.content?.policyNo;
            _logger.LogInformation($"[BocEndorCoverNoteCommand] recieved policy number from BOC core system : {policyNo}");
            if (endorsementResult?.rtnCode != "0000" || endorsementResult.content?.policyNo == null)
                errors.Add($"EndorCoverNote failed" + " ||| Request: " + JsonConvert.SerializeObject(endorCoverNoteRequest) + " ||| Result:" + endorsementResult?.rtnMsg + " ||| Result Content: " + JsonConvert.SerializeObject(endorsementResult?.content));

            BocEndorsementRecord record = MapRecord();
            Result endorsementListUpdateResult = await AddToEndorsementList(tenantId, record);
            if (endorsementListUpdateResult.Errors?.Any() ?? false)
                errors = errors.Concat(endorsementListUpdateResult.Errors).ToList();

            return errors.Any()
                ? Result<string>.Failure(errors)
                : Result<string>.Success(policyNo);

            async Task<Result> AddToEndorsementList(string tenantId, BocEndorsementRecord record)
            {
                string fileName = $"EndorsementLists/{DateTime.UtcNow.AddHours(8):dd-MM-yyyy}/EndorsementList.csv";
                Result<byte[]> existingFile = await _fileSystemService.GetFileAsync(tenantId, null,
                    new GetFileCommand { IsPublic = false, Key = fileName });
                _logger.LogInformation($"[BocEndorCoverNoteCommand] existing endorsement File : {existingFile.Value} and content length: {existingFile.Value?.Length}");
                byte[] updatedExcel;

                using (var sFile = new MemoryStream())
                {
                    using (TextWriter writer = new StreamWriter(sFile, System.Text.Encoding.UTF8))
                    {
                        var csv = new CsvWriter(writer, CultureInfo.CurrentCulture);
                        if (existingFile.Value == null)
                            csv.WriteRecords(new List<BocEndorsementRecord> { record });
                        else
                        {
                            long endPoint = sFile.Length;
                            sFile.Seek(endPoint, SeekOrigin.Begin);
                            sFile.Write(existingFile.Value);
                            string text = string.Join(
                                ",",
                                typeof(BocEndorsementRecord).GetProperties().Select(prop => prop.GetValue(record)?.ToString() ?? "")
                            );
                            writer.WriteLine(text);
                        }
                    }

                    updatedExcel = sFile.ToArray();
                    _logger.LogInformation($"[BocEndorCoverNoteCommand] updated endorsement File content length: {updatedExcel.Length}");
                }

                Result uploadResult = await _fileSystemService.UploadFileAsync(tenantId, null,
                    new UploadFileCommand { IsPublic = false, Key = fileName, Content = updatedExcel });

                _logger.LogInformation($"[BocEndorCoverNoteCommand] Upload file result: {JsonConvert.SerializeObject(uploadResult)}");
                return uploadResult;
            }

            BocEndorsementRecord MapRecord()
            {
                var record = new BocEndorsementRecord();
                record.date = DateTime.UtcNow.ToString("dd/MM/yyyy");
                record.coverNoteNo = inputJson.Value<string>("issuerNumber");
                record.licenseNo = inputJson.Value<string>("licenseNo");
                record.idCard = inputJson.Value<string>("idCard");

                record.returnCode = "\t" + endorsementResult.rtnCode;
                record.returnMsg = endorsementResult.rtnMsg;
                var content = JObject.FromObject(endorsementResult.content);
                record.returnContent = content.ToString(Formatting.None);

                return record;
            }
        }

        private class EndorCoverNoteRequest
        {
            public string reqMethod { get; set; } = "endorCoverNote";
            public string coverNoteNo { get; set; }
            public string licenseNo { get; set; }
        }

        private class EndorCoverNoteResult
        {
            public string rtnCode { get; set; }
            public string rtnMsg { get; set; }
            public EndorCoverNoteResultContent content { get; set; }
        }

        private class EndorCoverNoteResultContent
        {
            public string coverNoteNo { get; set; }
            public string policyNo { get; set; }
        }
    }
}
