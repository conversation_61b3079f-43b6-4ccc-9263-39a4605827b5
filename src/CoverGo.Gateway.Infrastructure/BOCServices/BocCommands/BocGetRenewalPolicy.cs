﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static CoverGo.Gateway.Infrastructure.BOCServices.BocCommands.BocGetRenewalInfo2Command;
using UpdateIndividualCommand = CoverGo.Users.Domain.Individuals.UpdateIndividualCommand;
using Object = CoverGo.Users.Domain.Objects.Object;
using RestSharp;
using System.Globalization;
using CoverGo.Gateway.Infrastructure.TcbServices;
using CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands;

namespace CoverGo.Gateway.Infrastructure.BOCServices.BocCommands
{
    public class BocGetRenewalPolicyCommand : BocCommand
    {
        private readonly string _endpoint;
        private readonly IPolicyService _policyService;
        private readonly IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> _individualService;
        private readonly IEntityService<CoverGo.Users.Domain.Objects.Object, CreateObjectCommand, UpdateObjectCommand> _objectService;
        private readonly IPricingService _pricingService;
        protected readonly ILogger<BocCommand> _logger;
        public override string Name => "getRenewalPolicy";

        private string defaultVehicleAge = "0";
        private string defaultNcd = "0";
        private string defaultDrivingExperience = "1";
        private string defaultVehicleValue = "0";

        private string ThirdPartyPlan = "TP";
        private string ComprehensivePlan = "CO";
        private string extendedCoverage = "Y";

        private string policyNumber;
        private string licenseNo;
        public BocGetRenewalPolicyCommand(
            string endpoint,
            IPolicyService policyService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
            IEntityService<CoverGo.Users.Domain.Objects.Object, CreateObjectCommand, UpdateObjectCommand> objectService,
            IPricingService pricingService,
            ILogger<BocGetRenewalPolicyCommand> logger)
        {
            _endpoint = endpoint;
            _policyService = policyService;
            _individualService = individualService;
            _objectService = objectService;
            _pricingService = pricingService;
            _logger = logger;
        }

        public async virtual Task<GetRenewalInfo2Result> GetRenewalInfo2(GetRenewalInfo2Request getRenewalRequest)
        {
            var request = new RestRequest(Method.POST)
            {
                RequestFormat = DataFormat.Json
            };
            request.AddJsonBody(getRenewalRequest);

            Result<string> response = await ExecuteAsync(_endpoint, request);
            _logger.LogInformation("[BocGetRenewalPolicyCommand] GetRenewalInfo2 BOC API at endpoint : {endpoint} ||| Request: {getRenewalRequest} ||| Result: {result}", _endpoint, JsonConvert.SerializeObject(getRenewalRequest), response.Value);
            GetRenewalInfo2Result getRenewalInfoResult = JsonConvert.DeserializeObject<GetRenewalInfo2Result>(response.Value);

            return getRenewalInfoResult;

        }

        public async override Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            policyNumber = inputJson.Value<string>("policyNo");
            licenseNo = inputJson.Value<string>("licenseNo");
            string clientId = inputJson.Value<string>("clientId");
            string loginId = inputJson.Value<string>("loginIdFromToken");
            bool recreateRenewalPolicy = inputJson.Value<bool>("recreateRenewal");
            string password = $"{licenseNo}_{Guid.NewGuid().ToString()}";
            string email = $"renewPolicyCustomer_{Guid.NewGuid().ToString()}@renew.policy";

            bool? excludePricing = inputJson.Value<bool>("excludePricing");
            bool? excludePremium = inputJson.Value<bool>("excludePremium");
            bool? excludeContractInsureds = inputJson.Value<bool>("excludeContractInsureds");

            var getRenewalRequest = new GetRenewalInfo2Request { policyNo = policyNumber, licenseNo = licenseNo };

            GetRenewalInfo2Result getRenewalInfo2Result = await GetRenewalInfo2(getRenewalRequest);
            //GetRenewalInfo2Result getRenewalInfo2Result = JsonConvert.DeserializeObject<GetRenewalInfo2Result>(getRenewalInfo02JsonResponse);

            if (getRenewalInfo2Result?.rtnCode != "0000" || getRenewalInfo2Result?.content?.proposedBaseInfo == null)
                return Result<string>.Failure($"BocGetRenewalPolicy failed Request:{JsonConvert.SerializeObject(getRenewalRequest)} ||| Result:{getRenewalInfo2Result?.rtnCode} ||| rtnMsg:{getRenewalInfo2Result?.rtnMsg} ||| Result Content:{JsonConvert.SerializeObject(getRenewalInfo2Result?.content)}");

            ProductId productId = new ProductId()
            {
                Plan = MapPlanType(getRenewalInfo2Result.content.proposedBaseInfo.proposedType),
                Type = "motor",
                Version = "1"
            };

            Policy existingPolicy = (await _policyService.GetAsync(tenantId, new PolicyWhere()
            {
                IssuerNumber_in = new List<string>() { policyNumber },
                IsIssued = true
            })).FirstOrDefault();


            Policy policy = (await _policyService.GetAsync(tenantId, new PolicyWhere()
            {
                Fields = new FieldsWhere()
                {
                    Path = "fields.externalPolicyNumber",
                    Condition = FieldsWhereCondition.Equals,
                    Value = new ScalarValue() { StringValue = policyNumber }
                }
                ,
                IsIssued = false
            })).FirstOrDefault();

            getRenewalInfo2Result.content.excessList = GetFactListValue<ExcessList>(policy?.Facts, "excessList");


            // policy was already created for renwal, resume the same application of renewal
            if (policy != null)
            {
                if (!recreateRenewalPolicy)
                    return Result<string>.Success(JsonConvert.SerializeObject(new BOCGetRenewalPolicyResponse() { policyId = policy.Id, getRenewalInfo2Result = getRenewalInfo2Result }));

                else
                {
                    await _policyService.DeletePolicyAsync(tenantId, policy.Id, loginId);

                    var newPolicy = await _policyService.CreatePolicyAsync(tenantId, new CreatePolicyCommand()
                    {
                        ClientId = policy.ClientId,
                        ContractHolder = policy.ContractHolder,
                        ContractInsured = policy.ContractInsured,
                        CreatedById = policy.CreatedById,
                        Description = policy.Description,
                        EndDate = policy.EndDate,
                        Fields = policy.Fields.ToString(),
                        FieldsSchemaId = policy.FieldsSchemaId,
                        GeneratedFrom = policy.GeneratedFrom,
                        IsPremiumOverridden = policy.IsPremiumOverridden,
                        IsRenewal = policy.IsRenewal,
                        IssueDate = policy.IssueDate,
                        IssuerNumber = policy.IssuerNumber,
                        OriginalIssuerNumber = policy.OriginalIssuerNumber,
                        OtherContractHolders = policy.OtherContractHolders,
                        Premium = new PremiumInput()
                        {
                            Amount = policy.Premium.Amount,
                            AppliedDiscounts = policy.Premium.AppliedDiscounts,
                            AppliedTaxes = policy.Premium.AppliedTaxes,
                            CurrencyCode = policy.Premium.CurrencyCode,
                            DiscountCodes = policy.Premium.DiscountCodes,
                            Discounts = policy.Premium.Discounts,
                            IsPricedAtStartDate = policy.Premium.IsPricedAtStartDate,
                            Loadings = policy.Premium.Loadings,
                            Metadata = policy.Premium.Metadata,
                            OriginalPrice = policy.Premium.OriginalPrice,
                            PaymentFrequency = policy.Premium.PaymentFrequency
                        },
                        PreviousPolicyIds = policy.PreviousPolicyIds,
                        ProductId = policy.ProductId,
                        ReferralCode = policy.ReferralCode,
                        RenewalNumber = policy.RenewalNumber,
                        RenewalVersion = policy.RenewalVersion,
                        Source = policy.Source,
                        StartDate = policy.StartDate,
                        Status = policy.Status,
                        Values = policy.Values,
                    });

                    await _policyService.PolicyFactBatch(tenantId, newPolicy.Value.Id, new FactCommandBatch()
                    {
                        AddFactCommands = policy.Facts.Select(x => new AddFactCommand() { Type = x.Type, Value = x.Value }).ToList()
                    });

                    var pricing = await _pricingService.GetPricingsAsync(tenantId, new PriceFilter
                    {

                        Factors = policy.Values,
                        DiscountCodes = policy.Premium.DiscountCodes,
                        PricingDate = DateTime.Now,
                        ProductIds = new List<ProductId> {
                            policy.ProductId
                        }

                    });

                    if (pricing != null && pricing.Any())
                    {
                        var updatePolicy = new UpdatePolicyCommand();
                        updatePolicy.Premium = new PremiumToUpdate()
                        {
                            Amount = policy.Premium.Amount,
                            CurrencyCode = policy.Premium.CurrencyCode,
                            IsPricedAtStartDate = true
                        };

                        Result updatePolicyResult = await UpdatePolicyPremium(tenantId, newPolicy.Value.Id, pricing, updatePolicy);

                        if (!updatePolicyResult.IsSuccess)
                            Result<string>.Failure("getRenewalPolicy recreateRenewalPolicy failed while updating generated renewal Policy!");

                        var currentPremium = pricing.FirstOrDefault();
                        getRenewalInfo2Result.content.excessList = BocHelper.MapExcessList(JToken.Parse(currentPremium.Metadata).SelectToken("excessList"));

                        return Result<string>.Success(JsonConvert.SerializeObject(new BOCGetRenewalPolicyResponse() { policyId = newPolicy.Value.Id, getRenewalInfo2Result = getRenewalInfo2Result }));
                    }
                }

            }

            var policyCommand = new CreatePolicyCommand()
            {
                ClientId = clientId,
                Fields = $"{{ \"externalPolicyNumber\" : \"{policyNumber}\" }}",
                RenewalVersion = (int)(policy?.RenewalVersion == null ? 1 : policy.RenewalVersion + 1),
                IsRenewal = true,
                RenewalNumber = policyNumber,
                OriginalIssuerNumber = policyNumber,
                ProductId = productId,
                CreatedById = loginId,
                StartDate = !string.IsNullOrEmpty(getRenewalInfo2Result.content.proposedBaseInfo.inceptionDate) ? DateTime.ParseExact(getRenewalInfo2Result.content.proposedBaseInfo.inceptionDate, "yyyy-MM-dd", CultureInfo.InvariantCulture) : null,
                EndDate = !string.IsNullOrEmpty(getRenewalInfo2Result.content.proposedBaseInfo.expiryDate) ? DateTime.ParseExact(getRenewalInfo2Result.content.proposedBaseInfo.expiryDate, "yyyy-MM-dd", CultureInfo.InvariantCulture) : null
            };



            await MapResponseToPolicy();

            var policyCreated = await _policyService.CreatePolicyAsync(tenantId, policyCommand);

            if (!policyCreated.IsSuccess)
                Result<string>.Failure("getRenewalPolicy failed while generating renewal Policy!");

            policy = (await _policyService.GetAsync(tenantId, new PolicyWhere()
            {
                Id = policyCreated.Value.Id
            })).FirstOrDefault();

            if (!(excludePricing ?? false))
            {
                var updatePolicy = new UpdatePolicyCommand();
                updatePolicy.Premium = MapPremiumUpdate(getRenewalInfo2Result);

                var pricing = await _pricingService.GetPricingsAsync(tenantId, new PriceFilter
                {

                    Factors = policyCommand.Values,
                    DiscountCodes = policyCommand.Premium.DiscountCodes,
                    PricingDate = DateTime.Now,
                    ProductIds = new List<ProductId> {
                            productId
                },

                });

                if (pricing != null && pricing.Any())
                {
                    var currentPremium = pricing.FirstOrDefault();

                    Result updatePolicyResult = await UpdatePolicyPremium(tenantId, policy.Id, pricing, updatePolicy);

                    if (!updatePolicyResult.IsSuccess)
                        Result<string>.Failure("getRenewalPolicy failed while updating generated renewal Policy!");

                    getRenewalInfo2Result.content.excessList = BocHelper.MapExcessList(JToken.Parse(currentPremium.Metadata).SelectToken("excessList"));
                }


            }


            var policyFacts = MapPolicyFacts(getRenewalInfo2Result);


            var factBatchResult = await _policyService.PolicyFactBatch(tenantId, policyCreated.Value.Id, new FactCommandBatch()
            {
                AddFactCommands = policyFacts.Select(fact => new AddFactCommand() { Id = fact.Id, Type = fact.Type, Value = fact.Value }).ToList()
            });

            if (!factBatchResult.IsSuccess)
                Result<string>.Failure("getRenewalPolicy failed while generating renewal Policy facts!");

            return Result<string>.Success(JsonConvert.SerializeObject(new BOCGetRenewalPolicyResponse() { policyId = policyCreated.Value.Id, getRenewalInfo2Result = getRenewalInfo2Result }));

            IEnumerable<Tax> MapTaxes(PriceDto price)
            {
                return price?.AppliedTaxes?.Select(x => new Tax()
                {
                    Code = x.Code,
                    OriginalPrice = x.OriginalPrice,
                    NewPrice = x.NewPrice,
                    Ratio = x.Ratio,
                    Flat = x.Flat,
                    Order = x.Order,
                    CurrencyCode = x.CurrencyCode
                });
            }


            string MapPlanType(string planType) => planType switch
            {
                (string p) when p == ComprehensivePlan => "motor_insurance_comprehensive_tariff",
                (string p) when p == ThirdPartyPlan => "motor_insurance_third_party_tariff",
                _ => ""
            };

            async Task MapResponseToPolicy()
            {
                if (!(excludePremium ?? false))
                {
                    MapPremium();
                    MapValues();
                }

                if (!(excludeContractInsureds ?? false))
                {
                    Individual customer = await MapCustomer();
                    Object vehicle = await MapVehicle();

                    MapContractInsureds(customer, vehicle);
                }
            }

            void MapPremium()
            {
                policyCommand.Premium = new Domain.Pricing.PremiumInput()
                {
                    Amount = Convert.ToDecimal(getRenewalInfo2Result.content.feeInfo.totalPremium),
                    CurrencyCode = Domain.CurrencyCode.HKD,
                    GrossAmount = Convert.ToDecimal(getRenewalInfo2Result.content.feeInfo.grossPremium),
                    IsPricedAtStartDate = true
                };
            }

            void MapValues()
            {

                string vehicleType = !String.IsNullOrEmpty(getRenewalInfo2Result.content.carInfo.vehicleClass) ? getRenewalInfo2Result.content.carInfo.vehicleClass : "P2";
                int vehicleCapacity = Convert.ToInt32(getRenewalInfo2Result.content.carInfo.engineCapacity);
                var values = new List<KeyScalarValue>()
            {
                new KeyScalarValue() { Key = "channelCode" , Value = new ScalarValue() { StringValue = "BOCGI" } },
                new KeyScalarValue() { Key = "productCode" , Value = new ScalarValue() { StringValue = "MOTOR" } },
                new KeyScalarValue() { Key = "isMibEffective" , Value = new ScalarValue() { BooleanValue = BocHelper.MapIsMibEffective(getRenewalInfo2Result.content.proposedBaseInfo.inceptionDate) } },
                new KeyScalarValue() { Key = "insureds" , Value = new ScalarValue()
                                                                {
                                                                    ArrayValue = new List<ScalarValue>()
                                                                    {
                                                                        new ScalarValue()
                                                                        {
                                                                            ObjectValue = new List<KeyScalarValue>()
                                                                            {
                                                                                new KeyScalarValue() { Key="vehicleType" , Value = new ScalarValue() { StringValue = vehicleType } },
                                                                                new KeyScalarValue() { Key="vehicleCapacity" , Value = new ScalarValue() { NumberValue = vehicleCapacity } },
                                                                                new KeyScalarValue() { Key="clientGroup" , Value = new ScalarValue() { StringValue = getRenewalInfo2Result.content.proposerInfo.clientGroup } },
                                                                                new KeyScalarValue() { Key="vehicleAge" , Value = new ScalarValue() { NumberValue = Convert.ToInt32(!String.IsNullOrEmpty(getRenewalInfo2Result.content.carInfo.vehicleAge) ? getRenewalInfo2Result.content.carInfo.vehicleAge : defaultVehicleAge) + 1 } },
                                                                                new KeyScalarValue() { Key="driverAge" , Value = new ScalarValue() { NumberValue = GetAge(Convert.ToDateTime(getRenewalInfo2Result.content.namedDriverInfo?.FirstOrDefault()?.namedDriverBirthDay ?? getRenewalInfo2Result.content.proposerInfo.proposerBirthDay),Convert.ToDateTime(getRenewalInfo2Result.content.proposedBaseInfo.inceptionDate)) } },
                                                                                new KeyScalarValue() { Key="driverExperience" , Value = new ScalarValue() { NumberValue = Convert.ToInt32(getRenewalInfo2Result.content.namedDriverInfo.FirstOrDefault()?.drivingExperience ?? defaultDrivingExperience) } },
                                                                                new KeyScalarValue() { Key="NCD" , Value = new ScalarValue() { NumberValue = Convert.ToDecimal(!String.IsNullOrEmpty(getRenewalInfo2Result.content.proposedBaseInfo.ncd) ? getRenewalInfo2Result.content.proposedBaseInfo.ncd : defaultNcd)/(decimal)100 } },
                                                                                new KeyScalarValue() { Key="totalSumInsured" , Value = new ScalarValue() { NumberValue = Convert.ToDecimal(getRenewalInfo2Result.content.carInfo.vehicleValue ?? defaultVehicleValue) } },
                                                                                new KeyScalarValue() { Key="extendedCoverage" , Value = new ScalarValue() { BooleanValue = getRenewalInfo2Result.content.hzmbInsuranceInfo.flag == extendedCoverage } },
                                                                                new KeyScalarValue() { Key="seatCapacity" , Value = new ScalarValue() { NumberValue = Convert.ToInt32(getRenewalInfo2Result.content.carInfo.seatCapacity) } },

                                                                                new KeyScalarValue() { Key="planType" , Value = new ScalarValue() { StringValue = getRenewalInfo2Result.content.hzmbInsuranceInfo.planType } },
                                                                                // these values to be in Facts or Fields of Policy
                                                                                new KeyScalarValue() { Key="startDate" , Value = new ScalarValue() { StringValue = getRenewalInfo2Result.content.proposedBaseInfo.inceptionDate } },
                                                                                new KeyScalarValue() { Key="endDate" , Value = new ScalarValue() { StringValue = getRenewalInfo2Result.content.proposedBaseInfo.expiryDate } },
                                                                                // these values to be in Facts or Fields of Policy
                                                                                new KeyScalarValue() { Key="commercialThirdPartyLiability" , Value = new ScalarValue() { NumberValue = Convert.ToDecimal(getRenewalInfo2Result.content.hzmbInsuranceInfo.sumInsured02) } },
                                                                                new KeyScalarValue() { Key="passengerLiability" , Value = new ScalarValue() { NumberValue = Convert.ToDecimal(getRenewalInfo2Result.content.hzmbInsuranceInfo.premium03) } },


                                                                                new KeyScalarValue() { Key="mibPremium02" , Value = new ScalarValue() { NumberValue = Convert.ToDecimal(getRenewalInfo2Result.content.hzmbInsuranceInfo.mibPremium02) } },
                                                                                new KeyScalarValue() { Key="mibPremium03" , Value = new ScalarValue() { NumberValue = Convert.ToDecimal(getRenewalInfo2Result.content.hzmbInsuranceInfo.mibPremium03) } },
                                                                                new KeyScalarValue() { Key="mibPremiumBZ" , Value = new ScalarValue() { NumberValue = Convert.ToDecimal(getRenewalInfo2Result.content.hzmbInsuranceInfo.mibPremiumBZ) } },
                                                                                new KeyScalarValue() { Key="premium02" , Value = new ScalarValue() { NumberValue = Convert.ToDecimal(getRenewalInfo2Result.content.hzmbInsuranceInfo.premium02) } },

                                                                                new KeyScalarValue() { Key="vehicleModelCode" , Value = new ScalarValue() { StringValue = getRenewalInfo2Result.content.carInfo.model } },
                                                                                new KeyScalarValue() { Key="thirdPartyPropertyDamageClaim" , Value = new ScalarValue() { BooleanValue = (getRenewalInfo2Result.content.proposedBaseInfo.proposedType == ThirdPartyPlan) } },

                                                                            }
                                                                        }
                                                                    }
                                                                }
                }
            };

                policyCommand.Values = JToken.FromObject(values.ToDictionary(x => x.Key, x => x.Value.GetValue()));

            }

            async Task<Individual> MapCustomer()
            {
                Result<CreatedStatus> createCustomerResult = await CreateCustomer(tenantId, getRenewalInfo2Result);
                Individual customer = await GetCustomer(tenantId, createCustomerResult?.Value?.Id);
                policyCommand.ContractHolder = customer;

                return customer;
            }

            async Task<Object> MapVehicle()
            {
                Result<CreatedStatus> createVehicleResult = await CreateVechicleContractHolder(tenantId, getRenewalInfo2Result);
                Object vehicle = await GetVehicleContractor(tenantId, createVehicleResult?.Value?.Id);

                return vehicle;
            }

            void MapContractInsureds(Individual customer, Object vehicle)
            {
                policyCommand.ContractInsured = new List<Entity>()
                {
                    customer,
                    vehicle
                };
            }

        }

        private PremiumToUpdate MapPremiumUpdate(GetRenewalInfo2Result getRenewalInfo2Result)
        {
            PremiumToUpdate premiumToUpdate = new PremiumToUpdate()
            {
                Amount = Convert.ToDecimal(getRenewalInfo2Result.content.feeInfo.totalPremium),
                CurrencyCode = Domain.CurrencyCode.HKD,
                GrossAmount = Convert.ToDecimal(getRenewalInfo2Result.content.feeInfo.grossPremium),
                IsPricedAtStartDate = true
            };

            return premiumToUpdate;
        }

        private async Task<Individual> GetCustomer(string tenantId, string customerId)
        {
            if (customerId == null)
                return null;

            IEnumerable<Individual> individuals = await _individualService.GetAsync(tenantId, new Domain.QueryArguments()
            {
                Where = new IndividualWhere()
                {
                    Id = customerId
                }
            });

            return individuals?.FirstOrDefault();
        }

        private async Task<Object> GetVehicleContractor(string tenantId, string objectId)
        {
            if (objectId == null)
                return null;

            IEnumerable<Object> vehicles = await _objectService.GetAsync(tenantId, new Domain.QueryArguments()
            {
                Where = new ObjectWhere()
                {
                    Id = objectId
                }
            });

            return vehicles?.FirstOrDefault();
        }


        private async Task<Result<CreatedStatus>> CreateCustomer(string tenantId, GetRenewalInfo2Result getRenewalInfo2Result)
        {
            CreateIndividualCommand createIndividualCommand = new CreateIndividualCommand()
            {
                EnglishLastName = getRenewalInfo2Result.content.proposerInfo.proposerName,
                DateOfBirth = TcbHelper.GetCustomerDateOfBirth(getRenewalInfo2Result.content.proposerInfo.proposerBirthDay, getRenewalInfo2Result.content.namedDriverInfo?.FirstOrDefault()?.namedDriverBirthDay),
                Gender = getRenewalInfo2Result.content.proposerInfo.proposerBirthDay,
                Occupation = getRenewalInfo2Result.content.proposerInfo.proposerOccupationN,
                EnglishFirstName = getRenewalInfo2Result.content.proposerInfo.proposerName,
                Type = IndividualTypes.Customer,
                // -> ContractHolder Contacts
                Contacts = new List<Contact>()
                {
                    new Contact() { Id = Guid.NewGuid().ToString(), Type = "email", Value = getRenewalInfo2Result.content.proposerInfo.proposerEmail },
                    new Contact() { Id = Guid.NewGuid().ToString(), Type = "mobile", Value = getRenewalInfo2Result.content.proposerInfo.proposerMobile }
                },

                // -> ContractHolder Identities
                Identities = new List<Identity>()
                {
                    new Identity() { Id = Guid.NewGuid().ToString(), Type = "id", Value = getRenewalInfo2Result.content.proposerInfo.proposerIdCard }
                },

                // -> ContractHolder Addresses
                Addresses = new List<Address>()
                {
                    new Address() { Id = Guid.NewGuid().ToString(), Type = "Residential", Fields= new Dictionary<string, string> {
                        {"room" , getRenewalInfo2Result.content.proposerInfo.proposerAddress },
                        {"district", getRenewalInfo2Result.content.proposerInfo.proposerDistrict }
                    } }
                },
                // -> ContractHolder Facts
                Facts = new List<Fact>()
                {


                    new Fact() { Id = Guid.NewGuid().ToString(), Type= "typeOfClient", Value = getRenewalInfo2Result.content.proposerInfo.proposerIdentity},
                    new Fact() { Id = Guid.NewGuid().ToString(), Type= "experience", Value = getRenewalInfo2Result.content.namedDriverInfo.FirstOrDefault()?.drivingExperience ?? defaultDrivingExperience},
                    new Fact() { Id = Guid.NewGuid().ToString(), Type= "occupationName", Value = getRenewalInfo2Result.content.proposerInfo.proposerOccupationN },
                    new Fact() { Id = Guid.NewGuid().ToString(), Type= "chassisNo", Value = getRenewalInfo2Result.content.carInfo.chassisNo},
                    new Fact() { Id = Guid.NewGuid().ToString(), Type= "engineNo", Value = getRenewalInfo2Result.content.carInfo.engineNo},
                    new Fact() { Id = Guid.NewGuid().ToString(), Type= "vehicleRegistrationNumber", Value = getRenewalInfo2Result.content.carInfo.licenseNo},
                    //new Fact() { Id = Guid.NewGuid().ToString(), Type= "previousPolicyNumber", Value = getRenewalInfo2Result.content.carInfo.licenseNo},
                    new Fact() { Id = Guid.NewGuid().ToString(), Type= "expiryDateOfThePreviousPolicy", Value = getRenewalInfo2Result.content.hzmbInsuranceInfo.expiryDate},
                    new Fact() { Id = Guid.NewGuid().ToString(), Type= "vehicleRegistrationNumber", Value = getRenewalInfo2Result.content.carInfo.licenseNo},
                    new Fact() { Id = Guid.NewGuid().ToString(), Type= "vehicleRegistrationNumber", Value = getRenewalInfo2Result.content.carInfo.licenseNo},


                },


            };

            Result<CreatedStatus> createdStatus = await _individualService.CreateAsync(tenantId, createIndividualCommand);


            return createdStatus;

        }

        private async Task<Result<CreatedStatus>> CreateVechicleContractHolder(string tenantId, GetRenewalInfo2Result getRenewalInfo2Result)
        {
            CreateObjectCommand createObject = new CreateObjectCommand()
            {
                Facts = new List<Fact>()
                {
                    new Fact() { Id = Guid.NewGuid().ToString(), Type = "vehicleManufacturer", Value = getRenewalInfo2Result.content.carInfo.manufacturer },
                    new Fact() { Id = Guid.NewGuid().ToString(), Type = "vehicleModel", Value = getRenewalInfo2Result.content.carInfo.model },
                    new Fact() { Id = Guid.NewGuid().ToString(), Type = "vehicleModelDesc", Value = getRenewalInfo2Result.content.carInfo.modelDesc },
                    //new Fact() { Id = Guid.NewGuid().ToString(), Type = "vehicleFactory", Value = getRenewalInfo2Result.content.carInfo.manufacturer },
                    //new Fact() { Id = Guid.NewGuid().ToString(), Type = "yearOfManufactory", Value = getRenewalInfo2Result.content.carInfo.manufacturer },
                    new Fact() { Id = Guid.NewGuid().ToString(), Type = "valueOfVehicle", Value = getRenewalInfo2Result.content.carInfo.vehicleValue },
                    new Fact() { Id = Guid.NewGuid().ToString(), Type = "seatingCapacity", Value = getRenewalInfo2Result.content.carInfo.seatCapacity },
                    new Fact() { Id = Guid.NewGuid().ToString(), Type = "engineCapacity", Value = getRenewalInfo2Result.content.carInfo.engineCapacity },
                    new Fact() { Id = Guid.NewGuid().ToString(), Type = "registrationNumber", Value = getRenewalInfo2Result.content.carInfo.licenseNo },
                    new Fact() { Id = Guid.NewGuid().ToString(), Type = "hirePurchaseCode", Value = getRenewalInfo2Result.content.carInfo.hirePurchaseCode },
                    new Fact() { Id = Guid.NewGuid().ToString(), Type = "vehicleType", Value = getRenewalInfo2Result.content.carInfo.vehicleClass },
                    new Fact() { Id = Guid.NewGuid().ToString(), Type = "vehicleBodyType", Value = getRenewalInfo2Result.content.carInfo.bodyType }
                },
                Tags = new List<string>() { "Vehicle" }
            };

            Result<CreatedStatus> status = await _objectService.CreateAsync(tenantId, createObject);

            return status;
        }

        private async Task<Result> UpdatePolicyPremium(string tenantId, string policyId, IEnumerable<PriceDto> pricing, UpdatePolicyCommand updatePolicy)
        {
            if (pricing != null && pricing.Any())
            {
                var currentPremium = pricing.FirstOrDefault();

                updatePolicy.Premium.Amount = currentPremium?.Amount;
                updatePolicy.Premium.CurrencyCode = currentPremium?.CurrencyCode;
                updatePolicy.Premium.OriginalPrice = currentPremium?.OriginalPrice;
                updatePolicy.Premium.PaymentFrequency = currentPremium.PaymentFrequency;

                updatePolicy.Premium.AppliedDiscounts = currentPremium?.AppliedDiscounts?.ToList();
                updatePolicy.Premium.AppliedTaxes = MapTaxes(currentPremium)?.ToList();
                updatePolicy.Premium.Discounts = currentPremium?.Discounts?.ToList();
                updatePolicy.Premium.Loadings = currentPremium?.Loadings?.ToList();

                updatePolicy.Premium.Metadata = currentPremium?.Metadata;
                updatePolicy.Premium.IsPricedAtStartDate = true;

                updatePolicy.Premium.IsAmountChanged = true;
                updatePolicy.Premium.IsCurrencyCodeChanged = true;
                updatePolicy.Premium.IsGrossAmountChanged = true;
                updatePolicy.Premium.IsMetadataChanged = true;
                updatePolicy.Premium.IsAppliedTaxesChanged = true;
                updatePolicy.IsPremiumChanged = true;
            }

            Result updatePolicyResult = await _policyService.UpdatePolicyAsync(tenantId, policyId, updatePolicy);
            return updatePolicyResult;

            IEnumerable<Tax> MapTaxes(PriceDto price)
            {
                return price?.AppliedTaxes?.Select(x => new Tax()
                {
                    Code = x.Code,
                    OriginalPrice = x.OriginalPrice,
                    NewPrice = x.NewPrice,
                    Ratio = x.Ratio,
                    Flat = x.Flat,
                    Order = x.Order,
                    CurrencyCode = x.CurrencyCode
                });
            }
        }

        private List<Fact> MapPolicyFacts(GetRenewalInfo2Result getRenewalInfo2Result)
        {

            var policyFacts = new List<Fact>()
            {

                new Fact() { Id = Guid.NewGuid().ToString(), Type= "referenceNumber", Value = getRenewalInfo2Result.content.proposedBaseInfo.b2bPolicyNo},

                // car Info 
                new Fact() { Id = Guid.NewGuid().ToString(), Type= "vehicleBodyType", Value = getRenewalInfo2Result.content.carInfo.bodyType},
                new Fact() { Id = Guid.NewGuid().ToString(), Type= "chassisNo", Value = getRenewalInfo2Result.content.carInfo.chassisNo},
                new Fact() { Id = Guid.NewGuid().ToString(), Type= "engineCapacity", Value = getRenewalInfo2Result.content.carInfo.engineCapacity},
                new Fact() { Id = Guid.NewGuid().ToString(), Type= "engineNo", Value = getRenewalInfo2Result.content.carInfo.engineNo},
                new Fact() { Id = Guid.NewGuid().ToString(), Type= "registrationNumber", Value = getRenewalInfo2Result.content.carInfo.licenseNo},
                new Fact() { Id = Guid.NewGuid().ToString(), Type= "vehicleFactory", Value = getRenewalInfo2Result.content.carInfo.manufacturer},
                new Fact() { Id = Guid.NewGuid().ToString(), Type= "vehicleModel", Value = getRenewalInfo2Result.content.carInfo.model},
                new Fact() { Id = Guid.NewGuid().ToString(), Type= "vehicleModelDesc", Value = getRenewalInfo2Result.content.carInfo.modelDesc},
                new Fact() { Id = Guid.NewGuid().ToString(), Type= "vehicleModelLoadRate", Value = getRenewalInfo2Result.content.carInfo.modelLoadRate},
                new Fact() { Id = Guid.NewGuid().ToString(), Type= "seatingCapacity", Value = getRenewalInfo2Result.content.carInfo.seatCapacity},
                new Fact() { Id = Guid.NewGuid().ToString(), Type= "vehicleType", Value = getRenewalInfo2Result.content.carInfo.vehicleClass},
                new Fact() { Id = Guid.NewGuid().ToString(), Type= "valueOfVehicle", Value = getRenewalInfo2Result.content.carInfo.vehicleValue},
                new Fact() { Id = Guid.NewGuid().ToString(), Type= "vehicleAge", Value = Convert.ToInt32(!String.IsNullOrEmpty(getRenewalInfo2Result.content.carInfo.vehicleAge) ? getRenewalInfo2Result.content.carInfo.vehicleAge : defaultVehicleAge) + 1 },
                // uw Questions

                new Fact() { Id = Guid.NewGuid().ToString(), Type = "isDeductedOffencePoints", Value = getRenewalInfo2Result.content.uwQuestions.drivingOffenceFlag == "" },
                new Fact() { Id = Guid.NewGuid().ToString(), Type = "numberOfTrafficAccidents", Value = getRenewalInfo2Result.content.uwQuestions.accidentNum },

                new Fact() { Id = Guid.NewGuid().ToString(), Type="ncd" , Value = Convert.ToDecimal(!String.IsNullOrEmpty(getRenewalInfo2Result.content.proposedBaseInfo.ncd) ? getRenewalInfo2Result.content.proposedBaseInfo.ncd : defaultNcd) },

                //excess List
                new Fact() { Id = Guid.NewGuid().ToString(), Type = "excessList", Value = JToken.Parse(JsonConvert.SerializeObject(getRenewalInfo2Result.content.excessList)) },

                // policy and licenseNo input by user
                new Fact() { Id = Guid.NewGuid().ToString(), Type = "renewPolicyNumber", Value = policyNumber},
                new Fact() { Id = Guid.NewGuid().ToString(), Type = "licenseNo", Value = licenseNo}
            };

            return policyFacts;
        }

        private static int GetAge(DateTime dateOfBirth, DateTime policyStartDate)
        {
            var a = (policyStartDate.Year * 100 + policyStartDate.Month) * 100 + policyStartDate.Day;
            var b = (dateOfBirth.Year * 100 + dateOfBirth.Month) * 100 + dateOfBirth.Day;

            return (a - b) / 10000;
        }

        private List<T> GetFactListValue<T>(List<Fact> facts, string type)
        {
            Fact fact = facts?.FirstOrDefault(f => f.Type == type);
            if (fact?.Value == null)
                return new List<T> { };

            JArray factJArray = fact.Value.Value<JArray>();

            return factJArray.ToObject<List<T>>();
        }

        public class BOCGetRenewalPolicyResponse
        {
            public string policyId { get; set; }

            public GetRenewalInfo2Result getRenewalInfo2Result { get; set; }
        }
    }
}
