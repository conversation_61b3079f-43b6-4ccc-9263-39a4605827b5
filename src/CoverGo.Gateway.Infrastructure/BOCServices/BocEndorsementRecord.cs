﻿using CsvHelper.Configuration;

namespace CoverGo.Gateway.Infrastructure.BOCServices
{
    public class BocEndorsementRecord
    {
        public string date { get; set; }
        public string coverNoteNo { get; set; }
        public string idCard { get; set; }
        public string licenseNo { get; set; }

        public string returnContent { get; set; }
        public string returnCode { get; set; }
        public string returnMsg { get; set; }
    }

    public sealed class BocEndorsementRecordMap : ClassMap<BocEndorsementRecord>
    {
        public BocEndorsementRecordMap()
        {
            Map(m => m.date);
            Map(m => m.coverNoteNo);
            Map(m => m.idCard);
            Map(m => m.licenseNo);

            Map(m => m.returnContent);
            Map(m => m.returnCode);
            Map(m => m.returnMsg);
        }
    }
}
