﻿#nullable enable

using System;
using System.Collections.Generic;

namespace CoverGo.Gateway.Infrastructure.RemoteServices.InMemory;

public enum FilterMode { EnableOn, DisableOn }

public class RemoteServiceFilterDefinition
{
    public RemoteServiceFilterDefinition(
        FilterMode filterMode,
        HashSet<string?> values)
    {
        FilterMode = filterMode;
        Values = values ?? throw new ArgumentNullException(nameof(values));
    }

    public FilterMode FilterMode { get; }

    public HashSet<string?> Values { get; }

    /// <summary>
    /// Returns true is filter is passed.
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    public bool Filter(string? value)
    {
        switch (FilterMode)
        {
            case FilterMode.EnableOn:
                return Values.Contains(value);
            case FilterMode.DisableOn:
                return !Values.Contains(value);
        }

        return true;
    }
}
