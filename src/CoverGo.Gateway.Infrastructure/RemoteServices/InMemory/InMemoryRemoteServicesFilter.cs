﻿#nullable enable

using System.Collections.Generic;

using CoverGo.Gateway.RemoteSchemas.Domain;

namespace CoverGo.Gateway.Infrastructure.RemoteServices.InMemory;

/// <summary>
/// For now we only have in memory implementation,
/// but later we may want to have dynamic schema switching per tenant.
/// </summary>
public class InMemoryRemoteServicesFilter : IRemoteServicesFilter
{
    private readonly Dictionary<string, RemoteServiceFilterDefinition>? tenantFilters;

    public InMemoryRemoteServicesFilter(
        Dictionary<string, RemoteServiceFilterDefinition>? tenantFilters = default)
    {
        this.tenantFilters = tenantFilters;
    }

    public bool CanUseService(string serviceName, string? tenantId)
    {
        var canUseService = 
            tenantFilters?.TryGetValue(serviceName, out var filterDefinition) == null ||
            filterDefinition == null ||
            filterDefinition.Filter(tenantId);
        return canUseService;
    }
}
