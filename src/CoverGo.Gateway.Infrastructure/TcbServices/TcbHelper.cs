﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands;
using CoverGo.Users.Domain.Individuals;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.TcbServices
{
    public static class TcbHelper
    {
        const decimal totalIncomeMinValue = 105000;
        const decimal totalIncomeMaxValue = 100000000;

        const decimal totalExpensesMinValue = 60000;
        const decimal totalExpensesMaxValue = 100000000;

        const decimal netDisposableIncomeMinValue = 45000;
        const decimal netDisposableIncomeMaxValue = 100000000;

        const decimal salaryAndBonusMinValue = 0;
        const decimal salaryAndBonusMaxValue = 100000000;

        const decimal livingStandardMinValue = 43000;
        const decimal livingStandardMaxValue = 100000000;

        const decimal educationTargetAmountMinValue = 0;
        const decimal eductionTargetAmountMaxValue = 99999999;

        const decimal educationTargetDurationMinValue = 1;
        const decimal educationTargetDurationMaxValue = 99;

        const decimal savingTargetAmountMinValue = 0;
        const decimal savingTargetAmountMaxValue = 50000000;

        const decimal savingTargetDurationMinValue = 1;
        const decimal savingTargetDuratonMaxValue = 100;

        const decimal lifeProtectionTargetAmountMinValue = 0;
        const decimal lifeProtectionTargetAmountMaxValue = 99999999;

        const decimal retirementTargetAmountMinValue = 0;
        const decimal retirementTargetAmountMaxValue = 99999999;

        static Regex ValidEmailRegex = CreateValidEmailRegex();

        public static DateTime? GetCustomerDateOfBirth(string proposerBirthDay, string namedDriverBirthDay)
        {
            if (!String.IsNullOrEmpty(proposerBirthDay))
                return DateTime.ParseExact(proposerBirthDay, "yyyy-MM-dd", CultureInfo.InvariantCulture);

            if (!String.IsNullOrEmpty(namedDriverBirthDay))
                return DateTime.ParseExact(namedDriverBirthDay, "yyyy-MM-dd", CultureInfo.InvariantCulture);

            return null;
        }

        private static Result<string> Validate(List<FnaDatum> fnaData)
        {
            foreach (var fnaDataItem in fnaData ?? Enumerable.Empty<FnaDatum>())
            {
                Result<string> validateResult = Validate(fnaDataItem.financial);

                if (!validateResult.IsSuccess)
                    return validateResult;

                validateResult = Validate(fnaDataItem.objective);

                if (!validateResult.IsSuccess)
                    return validateResult;
            }

            return Result<string>.Success("success");
        }

        private static Result<string> Validate(List<Financial> financials)
        {


            if (TotalIncomeInValidRange())
                return Result<string>.Failure("inputJson.referLeadInput.fnData.financial.totalIncome is required");

            if (TotalExpensesInValidRange())
                return Result<string>.Failure("inputJson.referLeadInput.fnData.financial.totalExpenses is required");

            if (NetDisposableIncomeInValidRange())
                return Result<string>.Failure("inputJson.referLeadInput.fnData.financial.netDisposableIncome is required");

            foreach (var financial in financials ?? Enumerable.Empty<Financial>())
            {

                if (SalaryAndBonusInValidRange(financial?.incomes))
                    return Result<string>.Failure("inputJson.referLeadInput.fnData.financial.income.salaryAndBonus is required");

                if (LivingStandardInValidRange(financial?.expenses))
                    return Result<string>.Failure("inputJson.referLeadInput.fnData.financial.expense.livingStandard is required");
            }

            return Result<string>.Success("success");

            bool TotalIncomeInValidRange() => financials?.Any(x => x.totalIncome < totalIncomeMinValue || x.totalIncome > totalIncomeMaxValue) ?? true;

            bool TotalExpensesInValidRange() => financials?.Any(x => x.totalExpenses < totalExpensesMinValue || x.totalExpenses > totalExpensesMaxValue) ?? true;

            bool NetDisposableIncomeInValidRange() => financials?.Any(x => x.netDisposableIncome < netDisposableIncomeMinValue || x.netDisposableIncome > netDisposableIncomeMaxValue) ?? true;

            bool SalaryAndBonusInValidRange(IEnumerable<Income> incomes) => incomes?.Any(x => x.salaryAndBonus < salaryAndBonusMinValue || x.salaryAndBonus > salaryAndBonusMaxValue) ?? true;

            bool LivingStandardInValidRange(IEnumerable<Expense> expenses) => expenses?.Any(x => x.livingStandard < livingStandardMinValue || x.livingStandard > livingStandardMaxValue) ?? true;

        }

        private static Result<string> Validate(List<Objective> objectives)
        {
            foreach (var objective in objectives ?? Enumerable.Empty<Objective>())

            {
                Result<string> validateResult = Validate(objective.education);

                if (!validateResult.IsSuccess)
                    return validateResult;

                validateResult = Validate(objective.savings);
                if (!validateResult.IsSuccess)
                    return validateResult;

                validateResult = Validate(objective.lifeProtection);
                if (!validateResult.IsSuccess)
                    return validateResult;

                validateResult = Validate(objective.retirement);
                if (!validateResult.IsSuccess)
                    return validateResult;
            }

            return Result<string>.Success("success");
        }

        private static Result<string> Validate(List<TcbServicesCommands.Education> educations)
        {
            foreach (var education in educations ?? Enumerable.Empty<TcbServicesCommands.Education>())
            {
                if (education?.isSelected ?? false)
                {
                    if (TargetAmountInValidRange(education))
                        return Result<string>.Failure("inputJson.referLeadInput.fnData.objective.education.targetAmount is required");

                    if (TargetDurationInValidRange(education))
                        return Result<string>.Failure("inputJson.referLeadInput.fnData.objective.education.targetDuration is required");

                }
            }

            return Result<string>.Success("success");

            bool TargetAmountInValidRange(TcbServicesCommands.Education education) => (education?.targetAmount ?? 0) < educationTargetAmountMinValue || (education?.targetAmount ?? 0) > eductionTargetAmountMaxValue;
            bool TargetDurationInValidRange(TcbServicesCommands.Education education) => (education?.targetDuration ?? 0) < educationTargetDurationMinValue || (education?.targetDuration ?? 0) > educationTargetDurationMaxValue;
        }

        private static Result<string> Validate(List<Saving> savings)
        {
            foreach (var saving in savings ?? Enumerable.Empty<Saving>())
            {
                if (saving?.isSelected ?? false)
                {
                    if (TargetAmountInValidRange(saving))
                        return Result<string>.Failure("inputJson.referLeadInput.fnData.objective.saving.targetAmount is required");

                    if (TargetDurationInValidRange(saving))
                        return Result<string>.Failure("inputJson.referLeadInput.fnData.objective.saving.targetDuration is required");
                }
            }

            return Result<string>.Success("success");

            bool TargetAmountInValidRange(TcbServicesCommands.Saving saving) => (saving?.targetAmount ?? 0) < savingTargetAmountMinValue || (saving?.targetAmount ?? 0) > savingTargetAmountMaxValue;
            bool TargetDurationInValidRange(TcbServicesCommands.Saving saving) => (saving?.targetDuration ?? 0) < savingTargetDurationMinValue || (saving?.targetDuration ?? 0) > savingTargetDuratonMaxValue;
        }

        private static Result<string> Validate(List<LifeProtection> lifeProtections)
        {
            foreach (var lifeProtection in lifeProtections ?? Enumerable.Empty<LifeProtection>())
            {
                if (lifeProtection?.isSelected ?? false)
                {
                    if (TargetAmountInValidRange(lifeProtection))
                        return Result<string>.Failure("inputJson.referLeadInput.fnData.objective.lifeProtection.targetAmount is required");

                }
            }

            return Result<string>.Success("success");

            bool TargetAmountInValidRange(TcbServicesCommands.LifeProtection lifeProtection) => (lifeProtection?.targetAmount ?? 0) < lifeProtectionTargetAmountMinValue || (lifeProtection?.targetAmount ?? 0) > lifeProtectionTargetAmountMaxValue;
        }

        private static Result<string> Validate(List<Retirement> retirements)
        {
            foreach (var retirement in retirements ?? Enumerable.Empty<Retirement>())
            {
                if (retirement?.isSelected ?? false)
                {
                    if (TargetAmountInValidRange(retirement))
                        return Result<string>.Failure("inputJson.referLeadInput.fnData.objective.retirement.targetAmount is required");

                }
            }

            return Result<string>.Success("success");

            bool TargetAmountInValidRange(TcbServicesCommands.Retirement retirement) => (retirement?.targetAmount ?? 0) < retirementTargetAmountMinValue || (retirement?.targetAmount ?? 0) > retirementTargetAmountMaxValue;
        }

        public static Result<string> Validate(ReferLeadRequest referLeadRequest)
        {

            return Validate(referLeadRequest.referLeadInput);
        }

        public static Result<string> Validate(ReferLeadInput referLeadInput)
        {
            if (string.IsNullOrEmpty(referLeadInput?.bankerCode?.Trim()))
                return Result<string>.Failure($"inputJson.referLeadInput.bankerCode is required");

            if (string.IsNullOrEmpty(referLeadInput?.bankStaffName?.Trim()))
                return Result<string>.Failure($"inputJson.referLeadInput.bankStaffName is required");

            if (string.IsNullOrEmpty(referLeadInput?.consultChannel?.Trim()))
                return Result<string>.Failure($"inputJson.referLeadInput.consultChannel is required");

            return Validate(referLeadInput.fnaData);
        }


        public static async Task<Result<Login>> GetOrCreateLogin(IAuthService authService, string tenantId, string username, string password, string email)
        {
            Login loginFromUsername = await authService.GetLoginByNameAsync(tenantId, username);

            if (loginFromUsername == null)
            {
                Result<CreatedStatus> createLoginResult = await CreateNewLoginAccount(authService, tenantId, "covergo_crm", username, password, email);

                if (!createLoginResult.IsSuccess)
                {
                    //_logger.LogError($"[ItcbLifeToken] Create login failed | {string.Join(", ", createLoginResult.Errors)}");
                    return Result<Login>.Failure("Create login failed");
                }

                loginFromUsername = await authService.GetLoginByNameAsync(tenantId, username);
            }

            return Result<Login>.Success(loginFromUsername);
        }

        private static async Task<Result<CreatedStatus>> CreateNewLoginAccount(IAuthService authService, string tenantId, string clientId, string username, string password, string email)
        {
            Result<CreatedStatus> loginCreatedResult = await authService.CreateLoginAsync(tenantId, new CreateLoginCommand
            {
                ClientId = clientId,
                Email = email,
                Username = username,
                Password = password,
                IsEmailConfirmed = true
            });

            if (!loginCreatedResult.IsSuccess)
                return Result<CreatedStatus>.Failure(loginCreatedResult.Errors);


            await authService.AddTargettedPermissionsAsync(tenantId, loginCreatedResult.Value.Id, new List<AddTargettedPermissionCommand>
            {
                new() { AddedById = loginCreatedResult.Value.Id, Type = "clientId", Value = clientId }
            });

            return loginCreatedResult;
        }

        public static async Task<Result<(string, bool)>> GetOrCreatePermissionGroup(string tenantId, string permissionGroupName, IAuthService authService)
        {
            PermissionGroup permissionGroup = (await authService.GetPermissionGroupsAsync(tenantId, new PermissionGroupWhere()
            {
                Name = permissionGroupName
            }))?.FirstOrDefault();

            if (permissionGroup != null)
                return Result<(string, bool)>.Success((permissionGroup.Id, true));

            await authService.CreatePermissionGroupAsync(tenantId, new CreatePermissionGroupCommand()
            {
                Name = permissionGroupName,
                Description = $"{permissionGroupName} permission group"
            });

            var createdPermissionGroup = (await authService.GetPermissionGroupsAsync(tenantId, new PermissionGroupWhere() { Name = permissionGroupName }))?.FirstOrDefault();

            return Result<(string, bool)>.Success((createdPermissionGroup?.Id, false));
        }

        public static async Task<Result> AddDeploymentPermissionsToPermissionGroup(string tenantId, string permissionGroupId, IAuthService authService)
        {
            var deploymentPermissionsList = new List<string>()
            {
                "readCases",
                "writeCases",
                "updateCases",
                "deleteCases",
                "readIndividuals",
                "writeIndividuals",
                "readLogins",
                "writeLogins",
                "updateLoginLockout",
                "inviteEntityToLogin",
                "readPermissionGroups",
                "writePermissionGroups",
                "readTargettedPermissions",
                "writeTargettedPermissions",
                "writeProducts",
                "readProducts",
                "readFiles",
                "writeFiles",
                "readTemplates",
                "writeTemplates",
                "readJobSchedules",
                "writeJobSchedules",
                "readApps",
                "writeApps"
            };


            Result result = await AddPermissionsToPermissionGroup(tenantId, permissionGroupId, deploymentPermissionsList, authService);

            return result;
        }

        public static async Task<Result> AddOperationPermissionsToPermissionGroup(string tenantId, string permissionGroupId, IAuthService authService)
        {
            var operationPermissionsList = new List<string>()
            {
                "readIndividuals",
                "writeIndividuals",
                "readLogins",
                "writeLogins",
                "updateLoginLockout",
                "inviteEntityToLogin",
                "readPermissionGroups",
                "writePermissionGroups",
                "readTargettedPermissions",
                "writeTargettedPermissions",
                "readIndividualNotes",
                "writeIndividualNotes"
            };

            Result result = await AddPermissionsToPermissionGroup(tenantId, permissionGroupId, operationPermissionsList, authService);

            return result;
        }

        private static async Task<Result> AddPermissionsToPermissionGroup(string tenantId, string permissionGroupId, List<string> permissionIds, IAuthService authService)
        {
            var permissionGroup = await authService.GetPermissionGroupAsync(tenantId, permissionGroupId);
            var missingPermissionIdsToAdd = permissionIds.Where(x => permissionGroup.TargettedPermissions == null || !permissionGroup.TargettedPermissions.ContainsKey(x));

            var addPermissionToPermissionGroupCommands = missingPermissionIdsToAdd.Select(x => new AddPermissionToPermissionGroupCommand()
            {
                PermissionId = x,
                TargetId = "all"
            });

            List<string> errors = new List<string>();
            foreach (var permissionCommand in addPermissionToPermissionGroupCommands)
            {
                var ret = await authService.AddPermissionToPermissionGroupAsync(tenantId, permissionGroupId, permissionCommand);

                if (!ret.IsSuccess)
                {
                    var error = $"Add permission {permissionCommand.PermissionId} to permission group {permissionGroupId} failed: {string.Join(", ", ret.Errors)}";
                    errors.Add(error);
                }
            }

            return errors.Any() ? Result.Failure(errors) : Result.Success();
        }


        /// <summary>
        /// Taken from http://haacked.com/archive/2007/08/21/i-knew-how-to-validate-an-email-address-until-i.aspx
        /// </summary>
        /// <returns></returns>
        private static Regex CreateValidEmailRegex()
        {
            string validEmailPattern = @"^(?!\.)(""([^""\r\\]|\\[""\r\\])*""|"
                + @"([-a-z0-9!#$%&'*+/=?^_`{|}~]|(?<!\.)\.)*)(?<!\.)"
                + @"@[a-z0-9][\w\.-]*[a-z0-9]\.[a-z][a-z\.]*[a-z]$";

            return new Regex(validEmailPattern, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(200));
        }

        public static bool EmailIsValid(string emailAddress)
        {
            bool isValid = ValidEmailRegex.IsMatch(emailAddress);

            return isValid;
        }

        public static bool ValidateVietnamesePhoneNumber(string phoneNumber)
        {
            string phoneNumberPattern = @"^(?:\+?84)?0(?:3|5|7|8|9)[0-9]{8}$";
            Regex regex = new Regex(phoneNumberPattern, RegexOptions.None, TimeSpan.FromMilliseconds(500));
            bool isMatch = regex.IsMatch(phoneNumber);

            return isMatch;
        }

        public static async Task<string> ExtractBranchCodeFromUserSource(string tenantId, string loginId, IAuthService authService, IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService)
        {
            string extractedBranchCode = null;
            Login loginFromToken = await authService.GetLoginById(tenantId, loginId);
            string[] managersWithOutBranch = new string[] { "areaManager", "regionalManager" , "hoManager" };
            IEnumerable<Individual> individuals = await individualService.GetAsync(tenantId, new Domain.QueryArguments()
            {
                Where = new IndividualWhere()
                {
                    Id = loginFromToken.EntityId
                }
            });

            var customerObject = individuals.FirstOrDefault();

            var regionWithBranchCode = customerObject?.Source;

            if (regionWithBranchCode != null && Array.Exists(managersWithOutBranch, x =>  regionWithBranchCode.Contains(x)))
                return null;

            string[] parts = regionWithBranchCode.Split(new char[] { '-' }, StringSplitOptions.RemoveEmptyEntries);

            if (parts.Length > 2)
            {
                extractedBranchCode = parts[parts.Length - 2];
            }

            if(!string.IsNullOrEmpty(extractedBranchCode) && extractedBranchCode.Contains('_'))
            {
                var tempParts = extractedBranchCode.Split(new char[] { '_' }, StringSplitOptions.RemoveEmptyEntries);
                extractedBranchCode = tempParts[tempParts.Length - 1];
            }

            return extractedBranchCode;
        }
    }
}

