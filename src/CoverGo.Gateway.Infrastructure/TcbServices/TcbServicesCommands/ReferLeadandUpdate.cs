using System;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands
{
    public class ReferLeadandUpdate : TcbCommand
    {
        public override string Name { get; } = "referLeadandUpdate";
        private readonly string _referLeadEndpoint;
        private readonly string _updateLeadEndpoint;

        public ReferLeadandUpdate(string endpoint, ILogger<ReferLeadandUpdate> logger) : base(logger)
        {
            _referLeadEndpoint = endpoint +  (IsTcbIntegrationChanged ? "Banca/ReferLead" : "/services/iTCBLife/Banca/ReferLead");
            _updateLeadEndpoint = endpoint + (IsTcbIntegrationChanged ? "UpdateLead" : "/services/iTCBLife/UpdateLead");
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName)
        {
            ReferLeadandUpdateInput input = inputJson.ToObject<ReferLeadandUpdateInput>();

            Result<string> referLeadResponse = await ExecuteAsync(_referLeadEndpoint, input.referLeadinput);

            if (referLeadResponse.Status == "success")
            {
                try
                {
                    string referLeadResponseStatusCode = JObject.Parse(referLeadResponse.Value).Value<string>("statusCode");
                    if (referLeadResponseStatusCode == "0" || referLeadResponseStatusCode == "22002" || referLeadResponseStatusCode == "20400")
                    {
                        _logger.LogInformation($"ReferLead response success recieved with status code: {referLeadResponseStatusCode}. Sending UpdateLeadCommand");
                        Result<string> updateLeadResponse = await ExecuteAsync(_updateLeadEndpoint, input.updateLeadinput);
                        return (updateLeadResponse.Status == "success") ?
                            Result<string>.Success($"ReferLead Response: {referLeadResponse.Value}. UpdateLead Response: {updateLeadResponse.Value}") :
                            Result<string>.Failure($"ReferLead sucess, UpdateLead failure. ReferLead Response: {referLeadResponse.Value}. UpdateLead Response: {updateLeadResponse.Value}");
                    }
                    else
                    {
                        _logger.LogInformation($"ReferLead response failure recieved with status code: {referLeadResponseStatusCode}. Sending UpdateLeadCommand with conversation result: 50");
                        input.updateLeadinput["ConversationResult"] = "50";
                        Result<string> updateLeadResponse = await ExecuteAsync(_updateLeadEndpoint, input.updateLeadinput);
                        return (updateLeadResponse.Status == "success") ?
                            Result<string>.Success($"ReferLead failure, UpdateLead sucess. ReferLead Response: {referLeadResponse.Value}. UpdateLead Response: {updateLeadResponse.Value}") :
                            Result<string>.Failure($"ReferLead failure, UpdateLead failure. ReferLead Response: {referLeadResponse.Value}. UpdateLead Response: {updateLeadResponse.Value}");
                    }

                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in referLeadandUpdate with handling referLead API response");
                    return Result<string>.Failure("Error in referLeadandUpdate with handling referLead API response");
                }
            }

            return Result<string>.Failure("Failure with referLeadandUpdate referLead API did not return data.");
        }


    }

    public class ReferLeadandUpdateInput
    {
        [JsonRequiredAttribute]
        public JObject referLeadinput { get; set; }
        [JsonRequiredAttribute]
        public JObject updateLeadinput { get; set; }
    }
}