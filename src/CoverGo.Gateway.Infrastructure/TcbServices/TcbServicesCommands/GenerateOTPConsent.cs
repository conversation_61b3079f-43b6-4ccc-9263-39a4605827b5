﻿using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using RestSharp;
using System;

using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands
{
    public class GenerateOTPConsent : TcbCommand
    {
        public override string Name => "generateOTPConsent";
        private readonly string _endpoint;
        public GenerateOTPConsent(string endpoint, ILogger<GenerateOTPConsent> logger) : base(logger)
        {
            _endpoint = endpoint + "/consent/generate";
        }

        private bool IsSendOtpViaEmail(string sendMethod) => (sendMethod == "EMAIL");
        private bool IsSendOtpViaSms(string sendMethod) => (sendMethod == "SMS");
        

        public async override Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName)
        {
            if (_tcbCrmClientId == null)
                return Result<string>.Failure("TCB_CRM_CLIENT_ID is not configured");
            if (_tcbCrmClientSecret == null)
                return Result<string>.Failure("TCB_CRM_CLIENT_SECRET is not configured");

            GenerateOtpConsentRequestBody requestBody;
            try
            {
                requestBody = inputJson.ToObject<GenerateOtpConsentRequestBody>();
            }
            catch (Exception ex)
            {
                return Result<string>.Failure($"inputJson is not valid, exception: {ex.Message}");
            }

            if(requestBody.requestType == "01")
            {
                if (SendMethodNotProvided())
                    return Result<string>.Failure($"inputJson.sendMethod is required");

                if (IsSendOtpViaEmail(requestBody.sendMethod))
                {
                    if (string.IsNullOrEmpty(requestBody.poEmail))
                        return Result<string>.Failure($"inputJson.poEmail is required");
                    if(!TcbHelper.EmailIsValid(requestBody.poEmail))
                        return Result<string>.Failure($"inputJson.poEmail is Invalid");

                }

                if(IsSendOtpViaSms(requestBody.sendMethod))
                {
                    if (string.IsNullOrEmpty(requestBody.poPhone))
                        return Result<string>.Failure($"inputJson.poPhone is required");

                    if(!TcbHelper.ValidateVietnamesePhoneNumber(requestBody.poPhone))
                        return Result<string>.Failure($"inputJson.poPhone is Invalid");
                }

                bool SendMethodNotProvided()
                {
                    return string.IsNullOrEmpty(requestBody.sendMethod);
                }
            }

            GenerateOTPConsentRequest generateOTPConsentRequest = new GenerateOTPConsentRequest()
            {
                headerRequest = ConsentRequestHeader.CreateAndGet(),
                document = requestBody
            };

            IRestResponse response = await ExecuteApi(generateOTPConsentRequest);

            if (response.IsSuccessful != true)
            {
                string failureMessage = response.ResponseStatus == ResponseStatus.TimedOut
                    ? $"TCB generateOTPConsent API timeout (>{_apiTimeoutSetting / 1000}s) - maybe due to IP address not whitelisted. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}"
                    : $"Execute TCB generateOTPConsent API failure. Response Status:{response.ResponseStatus}. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}. Response value : {response.Content}";
                _logger.LogInformation(failureMessage);
                return Result<string>.Failure($"error occurred: {failureMessage}, request:{JsonConvert.SerializeObject(generateOTPConsentRequest, Formatting.None, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver() })}");
            }

            var data = JObject.Parse(response.Content).SelectToken("document.data");
            return Result<string>.Success(JsonConvert.SerializeObject(data));
        }

        public virtual async Task<IRestResponse> ExecuteApi(GenerateOTPConsentRequest generateOtpConsentRequest)
        {
            IRestResponse response = null;
            try
            {
                RestClient client = new(_endpoint) { Timeout = _apiTimeoutSetting, };

                string integratedApiDisableSsl = Environment.GetEnvironmentVariable("INTEGRATED_API_DISABLE_SSL");

                if (!string.IsNullOrEmpty(integratedApiDisableSsl) &&
                    bool.TryParse(integratedApiDisableSsl, out bool flag) && flag)
                    client.RemoteCertificateValidationCallback = AcceptAllCertifications;

                RestRequest request = new(Method.POST);
                request.AddHeader("client_id", _tcbCrmClientId);
                request.AddHeader("client_secret", _tcbCrmClientSecret);
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", JsonConvert.SerializeObject(generateOtpConsentRequest, Formatting.None, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                }),
                    ParameterType.RequestBody);
                _logger.LogInformation("generateOTPConsent TcbCommand sending request {request} to:{endpoint}", JsonConvert.SerializeObject(generateOtpConsentRequest), _endpoint);
                response = await client.ExecuteAsync(request);
                _logger.LogInformation("generateOTPConsent TcbCommand response recieved {response} from:{endpoint} with status code:{StatusCode} and error:{error}", response.Content, _endpoint, response.StatusCode, response.ErrorMessage);
                LogRequest(client, request, response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error with execute tcb generateOTPConsent api command");
            }

            return response;
        }
    }

    public class GenerateOTPConsentRequest
    {
        public ConsentRequestHeader headerRequest { get; set; }
        public GenerateOtpConsentRequestBody document { get; set; }
    }

    public class GenerateOtpConsentRequestBody
    {
        public string requestId { get; set; }
        public string cusId { get; set; }
        public string leadId { get; set; }
        public string citizenId { get; set; }
        public string citizenType { get; set; }
        public string poName { get; set; }
        public string conversationId { get; set; }
        public string sendMethod { get; set; }
        public string poEmail { get; set; }
        public string poPhone { get; set; }
        public string saleUserName { get; set; }
        public string saleAgentName { get; set; }
        public string saleAgentCode { get; set; }
        public string source { get; set; }
        public string partner { get; set; }
        public string requestType { get; set; }
    }
}
