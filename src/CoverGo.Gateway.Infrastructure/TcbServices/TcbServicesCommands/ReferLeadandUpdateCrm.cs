﻿using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands
{
    public class ReferLeadandUpdateCrm : TcbCommand
    {
        public override string Name { get; } = "referLeadandUpdateCrm";
        private readonly string _referLeadEndpoint;
        private readonly UpdateLeadCrm _updateLeadCrm;

        public ReferLeadandUpdateCrm(string endpoint,UpdateLeadCrm updateLeadCrm, ILogger<ReferLeadandUpdateCrm> logger) : base(useCrmEndPoint:false,logger)
        {
            _referLeadEndpoint = endpoint + (IsTcbIntegrationChanged ? "Banca/ReferLead" : "/internal-apigw-uat-aws/itcblife/Banca/ReferLead");
            _updateLeadCrm = updateLeadCrm;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName)
        {
            ReferLeadandUpdateInput input = inputJson.ToObject<ReferLeadandUpdateInput>();

            Result<string> validateResult = TcbHelper.Validate(input.referLeadinput.ToObject<ReferLeadInput>());

            if (!validateResult.IsSuccess)
                return validateResult;

            Result<string> referLeadResponse = await ExecuteAsync(_referLeadEndpoint, input.referLeadinput);

            if (referLeadResponse.IsSuccess)
            {
                try
                {
                    string referLeadResponseStatusCode = JObject.Parse(referLeadResponse.Value).Value<string>("statusCode");
                    if (IsLeadReferred(referLeadResponseStatusCode))
                    {
                        _logger.LogInformation("ReferLead response success recieved with status code: {referLeadResponseStatusCode}. Sending UpdateLeadCommand", referLeadResponseStatusCode);
                        Result<string> updateLeadResponse = await _updateLeadCrm.ExecuteAsync(tenantId, input.updateLeadinput, bucketName: null);

                        return updateLeadResponse.IsSuccess ?
                            Result<string>.Success($"ReferLead Response: {referLeadResponse.Value}. UpdateLead Response: {updateLeadResponse.Value}") :
                            Result<string>.Failure($"ReferLead sucess, UpdateLead failure. ReferLead Response: {referLeadResponse.Value}. UpdateLead Response: {updateLeadResponse.Value}");
                    }

                    _logger.LogInformation("ReferLead response failure recieved with status code: {referLeadResponseStatusCode}", referLeadResponseStatusCode);
                    return Result<string>.Failure($"ReferLead failure. ReferLead Response: {referLeadResponse.Value}.");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in referLeadandUpdate with handling referLead API response");
                    return Result<string>.Failure("Error in referLeadandUpdate with handling referLead API response");
                }
            }

            return Result<string>.Failure($"Failure with {Name}, referLead API did not return data. Response: {referLeadResponse}");
        }
        
        private static bool IsLeadReferred(string statusCode)
        {
            HashSet<string> successStatusCodes = new() { "0", "22002", "20400" };

            return successStatusCodes.Contains(statusCode);
        }
    }
}
