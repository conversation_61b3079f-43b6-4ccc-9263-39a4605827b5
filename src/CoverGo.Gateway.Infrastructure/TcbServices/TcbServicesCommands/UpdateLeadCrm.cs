﻿using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using RestSharp;
using System;
using System.Threading.Tasks;
using static CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands.CreateNewLeadCrm;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands
{
    public class UpdateLeadCrm : TcbCommand
    {
        public override string Name { get; } = "updateLeadCrm";
        private readonly string _endpoint;
        public UpdateLeadCrm(string endpoint, ILogger<UpdateLeadCrm> logger) : base(useCrmEndPoint:true,logger)
        {
            _endpoint = endpoint + "/leads";
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName)
        {
            if (_tcbCrmClientId == null)
                return Result<string>.Failure("TCB_CRM_CLIENT_ID is not configured");
            if (_tcbCrmClientSecret == null)
                return Result<string>.Failure("TCB_CRM_CLIENT_SECRET is not configured");

            UpdateleadRequestBody requestBody;
            try
            {
                requestBody = inputJson.ToObject<UpdateleadRequestBody>();
            }
            catch (Exception ex)
            {
                return Result<string>.Failure($"inputJson is not valid, exception: {ex.Message}");
            }

            if (requestBody?.crmId == null)
                return Result<string>.Failure($"inputJson.crmId is required");
            if (requestBody?.conversationResult == null)
                return Result<string>.Failure($"inputJson.conversationResult is required");
            if (requestBody?.productCode == null)
                return Result<string>.Failure($"inputJson.productCode is required");


            TcbCrmUpdateLeadRequest updateleadRequest = new TcbCrmUpdateLeadRequest()
            {
                headerRequest = HeaderRequest.CreateAndGet(),
                document = requestBody
            };

            try
            {
                RestClient client = new(_endpoint) { Timeout = _apiTimeoutSetting, };

                string integratedApiDisableSsl = Environment.GetEnvironmentVariable("INTEGRATED_API_DISABLE_SSL");

                if (!string.IsNullOrEmpty(integratedApiDisableSsl) &&
                    bool.TryParse(integratedApiDisableSsl, out bool flag) && flag)
                    client.RemoteCertificateValidationCallback = AcceptAllCertifications;

                RestRequest request = new(Method.PATCH);
                request.AddHeader("client_id", _tcbCrmClientId);
                request.AddHeader("client_secret", _tcbCrmClientSecret);
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", JsonConvert.SerializeObject(updateleadRequest, Formatting.None, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                }),
                    ParameterType.RequestBody);
                _logger.LogInformation("updateLeadCrm TcbCommand sending request {request} to:{endpoint}", JsonConvert.SerializeObject(updateleadRequest), _endpoint);
                IRestResponse response = await client.ExecuteAsync(request);
                _logger.LogInformation("updateLeadCrm TcbCommand response recieved {response} from:{endpoint} with status code:{StatusCode} and error:{error}", response.Content, _endpoint, response.StatusCode, response.ErrorMessage);
                LogRequest(client, request, response);

                if (response.IsSuccessful != true)
                {
                    string failureMessage = response.ResponseStatus == ResponseStatus.TimedOut
                        ? $"TCB updateLeadCrm API timeout (>{_apiTimeoutSetting / 1000}s) - maybe due to IP address not whitelisted. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}"
                        : $"Execute TCB updateLeadCrm API failure. Response Status:{response.ResponseStatus}. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}. Response value : {response.Content}";
                    _logger.LogInformation(failureMessage);
                    return Result<string>.Failure($"error occurred: {failureMessage}, request:{JsonConvert.SerializeObject(updateleadRequest, Formatting.None, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver() })}");
                }

                return Result<string>.Success(response.Content);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error with execute tcb updateLeadCrm api command");
                return Result<string>.Failure($"error occurred: {ex.Message}, request:{JsonConvert.SerializeObject(updateleadRequest, Formatting.None, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver() })}");
            }

        }

        class TcbCrmUpdateLeadRequest
        {
            public HeaderRequest headerRequest { get; set; }
            public UpdateleadRequestBody document { get; set; }

        }


        public class UpdateleadRequestBody
        {
            [JsonRequired]
            public string crmId { get; set; }
            [JsonRequired]
            public string conversationResult { get; set; }
            public string confirmedDate { get; set; }
            [JsonRequired]
            public string productCode { get; set; }
            public string agentCode { get; set; }
            public string staffId { get; set; }
            public string indirectRefferer { get; set; }
            public long? ape { get; set; }
        }

    }

}
