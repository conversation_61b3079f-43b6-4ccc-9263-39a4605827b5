using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands
{
    public class ReferLead : TcbCommand
    {
        public override string Name { get; } = "referLead";
        private readonly string _endpoint;

        public ReferLead(string endpoint, ILogger<ReferLead> logger) : base(logger)
        {
            _endpoint = endpoint + (IsTcbIntegrationChanged ? "Banca/ReferLead" : "/services/iTCBLife/Banca/ReferLead");
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName) =>
             await ExecuteAsync(_endpoint, inputJson);
    }
}