﻿using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands.CreateNewLeadCrm;
using static CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands.UpdateLeadCrm;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands
{
    public class CheckConsent : TcbCommand
    {
        public override string Name => "checkConsent";
        private readonly string _endpoint;
        public CheckConsent(string endpoint, ILogger<CheckConsent> logger) : base(logger)
        {
            _endpoint = endpoint + "/consent/check";
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName)
        {
            if (_tcbCrmClientId == null)
                return Result<string>.Failure("TCB_CRM_CLIENT_ID is not configured");
            if (_tcbCrmClientSecret == null)
                return Result<string>.Failure("TCB_CRM_CLIENT_SECRET is not configured");

            CheckConsentRequestBody requestBody;
            try
            {
                requestBody = inputJson.ToObject<CheckConsentRequestBody>();
            }
            catch (Exception ex)
            {
                return Result<string>.Failure($"inputJson is not valid, exception: {ex.Message}");
            }

            if (string.IsNullOrEmpty(requestBody?.citizenId))
                return Result<string>.Failure($"inputJson.citizenId is required");
            if (string.IsNullOrEmpty(requestBody?.citizenType))
                return Result<string>.Failure($"inputJson.citizenType is required");
            if (string.IsNullOrEmpty(requestBody?.poName))
                return Result<string>.Failure($"inputJson.poName is required");

            CheckConsentRequest checkConsentRequest = new CheckConsentRequest()
            {
                headerRequest = ConsentRequestHeader.CreateAndGet(),
                document = requestBody
            };

            IRestResponse response = await ExecuteApi(checkConsentRequest);

            if (response.IsSuccessful != true)
            {
                string failureMessage = response.ResponseStatus == ResponseStatus.TimedOut
                    ? $"TCB checkConsent API timeout (>{_apiTimeoutSetting / 1000}s) - maybe due to IP address not whitelisted. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}"
                    : $"Execute TCB checkConsent API failure. Response Status:{response.ResponseStatus}. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}. Response value : {response.Content}";
                _logger.LogInformation(failureMessage);
                return Result<string>.Failure($"error occurred: {failureMessage}, request:{JsonConvert.SerializeObject(checkConsentRequest, Formatting.None, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver() })}");
            }

            var data = JObject.Parse(response.Content).SelectToken("document.data");
            return Result<string>.Success(JsonConvert.SerializeObject(data));
        }

        public virtual async Task<IRestResponse> ExecuteApi(CheckConsentRequest checkConsentRequest)
        {
            IRestResponse response = null;
            try
            {
                RestClient client = new(_endpoint) { Timeout = _apiTimeoutSetting, };

                string integratedApiDisableSsl = Environment.GetEnvironmentVariable("INTEGRATED_API_DISABLE_SSL");

                if (!string.IsNullOrEmpty(integratedApiDisableSsl) &&
                    bool.TryParse(integratedApiDisableSsl, out bool flag) && flag)
                    client.RemoteCertificateValidationCallback = AcceptAllCertifications;

                RestRequest request = new(Method.POST);
                request.AddHeader("client_id", _tcbCrmClientId);
                request.AddHeader("client_secret", _tcbCrmClientSecret);
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", JsonConvert.SerializeObject(checkConsentRequest, Formatting.None, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                }),
                    ParameterType.RequestBody);
                _logger.LogInformation("checkConsent TcbCommand sending request {request} to:{endpoint}", JsonConvert.SerializeObject(checkConsentRequest), _endpoint);
                response = await client.ExecuteAsync(request);
                _logger.LogInformation("checkConsent TcbCommand response recieved {response} from:{endpoint} with status code:{StatusCode} and error:{error}", response.Content, _endpoint, response.StatusCode, response.ErrorMessage);
                LogRequest(client, request, response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error with execute tcb checkConsent api command");
            }

            return response;
        }
    }

    public class CheckConsentRequest
    {
        public ConsentRequestHeader headerRequest { get; set; }
        public CheckConsentRequestBody document { get; set; }
    }

    public class ConsentRequestHeader
    {
        public string messageId { get; set; }
        public DateTime createDate { get; set; }
        public string fromNm { get; set; }
        public string toNm { get; set; }

        public static ConsentRequestHeader CreateAndGet()
        {
            return new ConsentRequestHeader()
            {
                createDate = DateTime.UtcNow,
                fromNm = Environment.GetEnvironmentVariable("TCB_CONSENT_FROM_NM") ??  "iTCBLife",
                toNm = Environment.GetEnvironmentVariable("TCB_CONSENT_TO_NM") ??  "BANCA",
                messageId = Guid.NewGuid().ToString(),
            };
        }
    }

    public class CheckConsentRequestBody
    {
        public string cusId { get; set; }
        public string leadId { get; set; }
        public string citizenId { get; set; }
        public string citizenType { get; set; }
        public string poName { get; set; }
    }
}
