﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Net.Http.Headers;
using Newtonsoft.Json.Serialization;
using RestSharp;
using static CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands.CreateNewLeadCrm;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands
{
    public class GetLeadCrm : TcbCommand
    {
        public override string Name { get; } = "getLeadCrm";
        private readonly string _endpoint;
        public GetLeadCrm(string endpoint, ILogger<GetLeadCrm> logger) : base(useCrmEndPoint: true, logger)
        {
            _endpoint = endpoint + "/leads/inquiry";

        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName)
        {
            if (_tcbCrmClientId == null)
                return Result<string>.Failure("TCB_CRM_CLIENT_ID is not configured");
            if (_tcbCrmClientSecret == null)
                return Result<string>.Failure("TCB_CRM_CLIENT_SECRET is not configured");

            GetLeadRequestBody requestBody;
            try
            {
                requestBody = inputJson.ToObject<GetLeadRequestBody>();
            }
            catch (Exception ex)
            {
                return Result<string>.Failure($"inputJson is not valid, exception: {ex.Message}");
            }

            if (requestBody?.id == null)
                return Result<string>.Failure($"inputJson.id is required");
            if (requestBody?.poCitizenType == null)
                return Result<string>.Failure($"inputJson.poCitizenType is required");
            if (requestBody?.saleUserId == null)
                return Result<string>.Failure($"inputJson.saleUserId is required");

            TcbCrmInquiryLeadRequest getLeadRequest = new TcbCrmInquiryLeadRequest()
            {
                headerRequest = HeaderRequest.CreateAndGet(),
                document = requestBody
            };

            try
            {
                RestClient client = new(_endpoint) { Timeout = _apiTimeoutSetting, };

                string integratedApiDisableSsl = Environment.GetEnvironmentVariable("INTEGRATED_API_DISABLE_SSL");

                if (!string.IsNullOrEmpty(integratedApiDisableSsl) &&
                    bool.TryParse(integratedApiDisableSsl, out bool flag) && flag)
                    client.RemoteCertificateValidationCallback = AcceptAllCertifications;

                RestRequest request = new(Method.POST);
                request.AddHeader("client_id", _tcbCrmClientId);
                request.AddHeader("client_secret", _tcbCrmClientSecret);
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", JsonConvert.SerializeObject(getLeadRequest, Formatting.None, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                }),
                    ParameterType.RequestBody);
                _logger.LogInformation("getLeadCrm TcbCommand sending request {request} to:{endpoint}", JsonConvert.SerializeObject(getLeadRequest), _endpoint);
                IRestResponse response = await client.ExecuteAsync(request);
                _logger.LogInformation("getLeadCrm TcbCommand response recieved {response} from:{endpoint} with status code:{StatusCode} and error:{error}", response.Content, _endpoint, response.StatusCode, response.ErrorMessage);
                LogRequest(client, request, response);

                if (response.IsSuccessful != true)
                {
                    string failureMessage = response.ResponseStatus == ResponseStatus.TimedOut
                        ? $"TCB getLeadsCrm API timeout (>{_apiTimeoutSetting / 1000}s) - maybe due to IP address not whitelisted. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}"
                        : $"Execute TCB getLeadsCrm API failure. Response Status:{response.ResponseStatus}. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}. Response value : {response.Content}";
                    _logger.LogInformation(failureMessage);
                    return Result<string>.Failure(failureMessage);
                }

                return Result<string>.Success(response.Content);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error with execute tcb getLeadCrm api command");
                return Result<string>.Failure(ex.Message);
            }
        }

        class TcbCrmInquiryLeadRequest
        {
            public HeaderRequest headerRequest { get; set; }
            public GetLeadRequestBody document { get; set; }

        }


        public class GetLeadRequestBody
        {
            [JsonRequired]
            public string id { get; set; }
            [JsonRequired]
            public string poCitizenType { get; set; }
            [JsonRequired]
            public string saleUserId { get; set; }
        }
    }
}
