﻿using Azure.Core;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using Microsoft.Extensions.Logging;
using MongoDB.Driver.Core.Operations;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands
{
    public class tcbDeployOperationPermissionGroups : TcbCommand
    {
        private readonly IAuthService _authService;
        public override string Name => "tcbDeployOperationPermissionGroups";

        public tcbDeployOperationPermissionGroups(
            IAuthService authService,
            ILogger<tcbDeployOperationPermissionGroups> logger) : base(logger)
        {
            _authService = authService;
        }

        public async override Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson = null, string bucketNam = null)
        {
            Result<(string, bool)> deploymentPermissionGroupId = await TcbHelper.GetOrCreatePermissionGroup(tenantId, "deployment", _authService);
            Result<(string, bool)> operationPermissionGroupId = await TcbHelper.GetOrCreatePermissionGroup(tenantId, "operation", _authService);

            await TcbHelper.AddDeploymentPermissionsToPermissionGroup(tenantId, deploymentPermissionGroupId.Value.Item1, _authService);
            await TcbHelper.AddOperationPermissionsToPermissionGroup(tenantId, operationPermissionGroupId.Value.Item1, _authService);

            return Result<string>.Success("success");
        }


    }
}
