using System;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using RestSharp;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands;

public class GetServicingAgent : TcbCommand
{
    private static readonly string TcbManulifeClientId =
        Environment.GetEnvironmentVariable("TCB_MANULIFE_CLIENT_ID") ?? "fb512ed359934e76bb6e1f69b02e14be";
    private static readonly string TcbManulifeClientSecret =
        Environment.GetEnvironmentVariable("TCB_MANULIFE_CLIENT_SECRET") ?? "371726934dE74406b8679EbA269fCAd3";
    
    private readonly string _endpoint;
    
    
    public GetServicingAgent(string endpoint, ILogger<GetServicingAgent> logger) 
        : base(logger)
    {
        _endpoint = endpoint + "get-servicing-agent";
    }

    public override string Name => "getServicingAgent";

    public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName)
    {
        if (TcbManulifeClientId == null )
            return Result<string>.Failure("TCB_BANCA_CLIENT_ID is not configured");
        if (TcbManulifeClientSecret == null)
            return Result<string>.Failure("TCB_BANCA_CLIENT_SECRET is not configured");

        RequestBody requestBody;
        try
        {
            requestBody = inputJson.ToObject<RequestBody>();
        }
        catch(Exception ex)
        {
            return Result<string>.Failure($"inputJson is not valid, exception: {ex.Message}");
        }

        requestBody!.HeaderRequest ??= new Header();
        requestBody!.HeaderRequest.MessageId ??= Guid.NewGuid().ToString();
        
        try
        {
            RestClient client = new(_endpoint) { Timeout = _apiTimeoutSetting, };

            string integratedApiDisableSsl = Environment.GetEnvironmentVariable("INTEGRATED_API_DISABLE_SSL");

            if (!string.IsNullOrEmpty(integratedApiDisableSsl) &&
                bool.TryParse(integratedApiDisableSsl, out bool flag) && flag)
                client.RemoteCertificateValidationCallback = AcceptAllCertifications;

            RestRequest request = new(Method.POST);
            request.AddHeader("client_id", TcbManulifeClientId);
            request.AddHeader("client_secret", TcbManulifeClientSecret);
            request.AddHeader("Content-Type", "application/json");
            request.AddParameter("application/json", JsonConvert.SerializeObject(requestBody, Formatting.None, new JsonSerializerSettings 
                { 
                    ContractResolver = new CamelCasePropertyNamesContractResolver() 
                }),
                ParameterType.RequestBody);
            
            IRestResponse response = await client.ExecuteAsync(request);
            LogRequest(client, request, response);
            
            if (response.IsSuccessful != true)
            {
                string failureMessage = response.ResponseStatus == ResponseStatus.TimedOut
                    ? $"TCB API timeout (>{_apiTimeoutSetting / 1000}s) - maybe due to IP address not whitelisted. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}"
                    : $"Execute TCB API failure. Response Status:{response.ResponseStatus}. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}. Response value : {response.Content}";
                _logger.LogInformation(failureMessage);
                return Result<string>.Failure(failureMessage);
            }

            return Result<string>.Success(response.Content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error with execute tcb api command");
            return Result<string>.Failure(ex.Message);
        }
    }
    
    class RequestBody
    {
        public Header HeaderRequest { get; set; }
        public RequestBodyData Document { get; set; }
    }

    class RequestBodyData
    {
        public string AgentCode { get; set; }
        public string PolicyNumber { get; set; }
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
    }

    class Header
    {
        public string MessageId { get; set; }
        public string CreateDate { get; set; }
        public string FromNm { get; set; }
        public string ToNm { get; set; }
        public string ServiceType { get; set; }
    }
}