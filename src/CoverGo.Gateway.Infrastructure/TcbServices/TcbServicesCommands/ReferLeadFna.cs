using System.Threading.Tasks;
using System;
using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Domain.FileSystem;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands
{
    public class ReferLeadFna : TcbCommand
    {
        public override string Name { get; } = "referLeadFna";
        private readonly ITemplateService _templateService;
        private readonly IFileSystemService _fileSystemService;
        public ReferLeadFna(ITemplateService templateService, IFileSystemService fileSystemService, ILogger<ReferLeadFna> logger) : base(logger)
        {
            _templateService = templateService;
            _fileSystemService = fileSystemService;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName)
        {
            bucketName ??= Environment.GetEnvironmentVariable("TCB_FNA_BUCKET");
            ReferLeadFNAInput referLeadFNAInput = inputJson.ToObject<ReferLeadFNAInput>();

            // populate render paramaters content with the content json string

            referLeadFNAInput.input.Content = JObject.Parse(referLeadFNAInput.input?.ContentJsonString);

            // make call to template service with input data to get back fna pdf 
            
            Result<byte[]> FNAtemplateResult = await _templateService.RenderWkhtmltopdfAsync(tenantId, referLeadFNAInput.templateId, referLeadFNAInput.input);
            
            //encrypt this value 
            string tcbPublicKey = GetPublicKey();

            Result<byte[]> encryptedFNAtemplate = EncryptFNA(FNAtemplateResult.Value, tcbPublicKey);

            //make a call to filesystem to upload document with this input

            string filekey = "FnaData/" + Guid.NewGuid() + ".pdf.gpg";

            Result uploadFileResult = await _fileSystemService.UploadFileAsync(tenantId, bucketName, new UploadFileCommand { Key = filekey, Content = encryptedFNAtemplate.Value, IsPublic = false });

            //make a call to filesystem to get link of this document

            return await _fileSystemService.GenerateSharableUrlAsync(tenantId, bucketName, new GenerateSharableUrlCommand { ExpiresIn = 12, Key = filekey });
        }

        public Result<byte[]> EncryptFNA(byte[] input, string tcbPublicKey)
        {
            try
            {
                byte[] encryptedData = EncryptionHelper.EncryptFNA(input, tcbPublicKey);
                return Result<byte[]>.Success(encryptedData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error with TCB FNA data encryption. {tcbPublicKey}", tcbPublicKey);
                return Result<byte[]>.Failure("Error with TCB FNA data encryption. Error message: " + ex.Message);
            }
        }

        private string GetPublicKey()
        {
            return Environment.GetEnvironmentVariable("TCB_API_PUBLIC_KEY") ?? "\n-----BEGIN PGP PUBLIC KEY BLOCK-----\n\nmQENBGC4jtEBCACtrnz3FoaOnkk3VLOFwuZ7SabULhok3MD7PxnVtWr6kQRJ1t1p\nWiM2MDgbhkPUqRJKFJcnC+aQCt4WgVBeULjnl4gVGniEnjEeyvlV5SSsUcNky23h\nnOjsAAALWPs6vswJdXQBrKS3t7VRmcIZn46nUc7Wj1pV98Uf3y0qPyDZ8VUu+Idu\nOXZPnTBuvt+iqkRfWdvusvXHmdMjoVWtg8ZrstRBFJ7R76J/xI2oA8dSy/ZnlWgN\nl8eHpyJKB2zXmh4Q1AZUy1/I+/6mKKr9qscdqnVkymT+Opy/vs6U5dO1BkJ0Gknd\neWS3423urGbqeg6fCKSrVu6ZOcmZ48mTprtbABEBAAG0J3RlY2hjb21iYW5rIDxo\nYW50MjBAdGVjaGNvbWJhbmsuY29tLnZuPokBOQQTAQIAIwUCYLiO0QIbAwcLCQgH\nAwIBBhUIAgkKCwQWAgMBAh4BAheAAAoJEOTK8g5aTaEjGVAIAKcRC4OdL8GnfIG9\n92hqH9v7ebjpqYZS4jqm14S2q2vyMG5qKvlxBtl5d5nvAThLEkXZxCuznghKQ5KO\nlDffEdEmTYUHFhhPvCtFEJYq0SBZykFK7bEgjwS1MtVI1a4bgwa2g+XvA6zpfG6g\n8T+Yel/dh4umTuzWpeDzzdXaCwD5XGTk4E5o2ZvQYDr7bk6HKlxvqi04ENY50tuX\nm83QgC002lvf1MQG5XdI7eEEvO3sTVwD1+U08q94FD/70L/NSxmA6POfbDenC2zQ\n06G9OaFZT7E5wwFAK4ZeaB/iIjuC27dhAUVaFVmv7RhOykLDauT2Iz5MvrcywtwF\nBzm4rEe5AQ0EYLiO0QEIALn+iOcbOXylhrrCE8+s2/hfXjAay18vBLvDH4glc+pc\nL/VBCTQ1aA/9Ozy/b8/f/McYNo3YsXupR8WonxevxYnRpSlzcwPdLmChygUF2BBf\nlVJioI8tILW45BC+7emJEnXnP9SH2zSEkridkHAl1vnJQDzDUNt5KI40Nah77wIL\nbtaTtI9Qjo7CKy8iGSBWiIYlfvFdpeErCTqk+U2sPmFCzG4UMFiecRlg/gurtOT7\nCwyTkn790f5XZPahYOMwJMOQjSZSh045LZY/4pyM/3EfrhjktuXHrsx44ghZRIml\nooSGIH4/nfPgundYoUPkWluiTlcwZzOVdJmzmsbi/MEAEQEAAYkBHwQYAQIACQUC\nYLiO0QIbDAAKCRDkyvIOWk2hI13gCACkFpaYmAzj8WLO8WYOzKYxPOePYW3YaDy4\naurGo2T4WQQ+u34Vo2NmzS/3Ml39nTzPZ63GTqfWXBVI6xhLd2txPd+hJPno9K8Q\nPZDZilyIVasjg2Or3l2ls2s+5pl+G3R931/AfA4BJ/yRO3NjHdq6m/fGwsZ3WaHI\niEFLhqV3McZzw6G9+VsrWPjVdrrxdfDCa6MWabGG7QAyZeJ8ec5u7qyHHrQWqmgA\nko0E4VQPYJ8jeQ5dw9TReSq+seT0OsU0txX0ZmDDOlYjjj1fTASa34JLqFs/sexa\nTE1mkSAgJVWnhknGRJ5e+P2CBPGevtjyu+QvSCJvAHE0q9vowNVw\n=2s8Z\n-----END PGP PUBLIC KEY BLOCK-----\n";
        }
    }

    public class ReferLeadFNAInput
    {
        public string templateId { get; set; }
        public RenderParameters input { get; set; }
    }
}