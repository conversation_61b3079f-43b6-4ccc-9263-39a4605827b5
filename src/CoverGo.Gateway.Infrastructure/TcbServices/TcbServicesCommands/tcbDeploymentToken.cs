﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands
{
    public class tcbDeploymentToken : TcbCommand
    {
        private readonly IAuthService _authService;
        public override string Name => "tcbDeploymentToken";

        public tcbDeploymentToken(
            IAuthService authService,
            ILogger<tcbDeploymentToken> logger) : base(logger)
        {
            _authService = authService;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName)
        {
            string deploymentUsername = inputJson.Value<string>("tcbDeploymentUsername");
            string deploymentPassword = inputJson.Value<string>("tcbDeploymentPassword");

            string environmentDeploymentUsername = Environment.GetEnvironmentVariable("TCB_DEPLOYMENT_USERNAME");
            string environmentDeploymentPassword = Environment.GetEnvironmentVariable("TCB_DEPLOYMENT_PASSWORD");

            string username = $"tcb_deployment_token{new string(deploymentUsername.Take(5).ToArray())}";
            string email = $"tcb_deployment{new string(deploymentUsername.Take(5).ToArray())}@itcblife.com.vn";
            string pass = $"tcbDeployment@12345!{new string(deploymentPassword.Take(5).ToArray())}";

            if (deploymentUsername != environmentDeploymentUsername ||
                deploymentPassword != environmentDeploymentPassword)
                return Result<string>.Failure("DeploymentUsername or DeploymentPassword is not valid");

            Result<Login> loginResult = await TcbHelper.GetOrCreateLogin(_authService, tenantId, username, pass, email);
            Login loginFromUsername = loginResult.Value;


            if (!loginResult.IsSuccess)
            {
                _logger.LogError($"[tcbDeploymentToken] Create login failed | {string.Join(", ", loginResult.Errors)}");
                return Result<string>.Failure("Create login failed");
            }

            string clientIdForLogin = loginFromUsername.TargettedPermissions["clientId"].FirstOrDefault() ?? "covergo_crm";

            var deploymentPermissionGroup = (await _authService.GetPermissionGroupsAsync(tenantId, new PermissionGroupWhere()
            {
                Name = "deployment"
            })).FirstOrDefault();

            if(deploymentPermissionGroup == null)
                return Result<string>.Failure("deployment Permission group is not found.");

            bool deploymentPermissionGroupExists = loginFromUsername.TargettedPermissions.Any(x => x.Key == "groups" && x.Value.Any(y => y == deploymentPermissionGroup.Id));

            if (!deploymentPermissionGroupExists)
            {
                await _authService.AddTargettedPermissionsAsync(tenantId, loginResult.Value.Id, new List<AddTargettedPermissionCommand>()
                {
                    new AddTargettedPermissionCommand()
                    {
                        Type = "groups",
                        Value = deploymentPermissionGroup.Id
                    }
                });
            }
            Token accessToken = await _authService.GetWholeAccessTokenAsync(tenantId, clientIdForLogin, "", username, pass);

            return Result<string>.Success(JsonConvert.SerializeObject(new { accessToken = accessToken.AccessToken }));
        }
    }
}