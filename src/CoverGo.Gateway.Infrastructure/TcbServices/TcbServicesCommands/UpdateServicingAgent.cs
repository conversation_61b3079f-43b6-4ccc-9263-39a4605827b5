using System;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Users.GraphQl.Client;
using GraphQL;
using GraphQL.Client.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using RestSharp;
using Formatting = Newtonsoft.Json.Formatting;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands;

public class UpdateServicingAgent : TcbCommand
{
    private static readonly string TcbManulifeClientId =
        Environment.GetEnvironmentVariable("TCB_MANULIFE_CLIENT_ID") ?? "fb512ed359934e76bb6e1f69b02e14be";
    private static readonly string TcbManulifeClientSecret =
        Environment.GetEnvironmentVariable("TCB_MANULIFE_CLIENT_SECRET") ?? "371726934dE74406b8679EbA269fCAd3";

    private readonly string _endpoint;
    private readonly GraphQLHttpClient _usersGraphQlHttpClient;
    public UpdateServicingAgent(string endpoint, ILogger<UpdateServicingAgent> logger, GraphQLHttpClient usersGraphQlHttpClient) 
        : base(logger)
    {
        _usersGraphQlHttpClient = usersGraphQlHttpClient;
        _endpoint = endpoint + "update-servicing-agent";
    }

    public override string Name => "updateServicingAgent";

    public override Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName) => ExecuteAsync(tenantId, inputJson, bucketName, null);

    public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName, string accessToken)
    {
        RequestBody requestBody;
        try
        {
            requestBody = inputJson.ToObject<RequestBody>();
        }
        catch(Exception ex)
        {
            return Result<string>.Failure($"inputJson is not valid, exception: {ex.Message}");
        }
        
        requestBody!.HeaderRequest ??= new Header();
        requestBody!.HeaderRequest.MessageId ??= Guid.NewGuid().ToString();
    
        try
        {
            RestClient client = new(_endpoint) { Timeout = _apiTimeoutSetting, };

            string integratedApiDisableSsl = Environment.GetEnvironmentVariable("INTEGRATED_API_DISABLE_SSL");

            if (!string.IsNullOrEmpty(integratedApiDisableSsl) &&
                bool.TryParse(integratedApiDisableSsl, out bool flag) && flag)
                client.RemoteCertificateValidationCallback = AcceptAllCertifications;

            RestRequest request = new(Method.POST);
            request.AddHeader("client_id", TcbManulifeClientId);
            request.AddHeader("client_secret", TcbManulifeClientSecret);
            request.AddHeader("Content-Type", "application/json");
            request.AddParameter("application/json", JsonConvert.SerializeObject(requestBody, Formatting.None, new JsonSerializerSettings 
                { 
                    ContractResolver = new CamelCasePropertyNamesContractResolver() 
                }),
                ParameterType.RequestBody);
            
            IRestResponse response = await client.ExecuteAsync(request);
            LogRequest(client, request, response);
            
            await AddChangeLogs(requestBody, response, accessToken);
            
            if (response.IsSuccessful != true)
            {
                string failureMessage = response.ResponseStatus == ResponseStatus.TimedOut
                    ? $"TCB API timeout (>{_apiTimeoutSetting / 1000}s) - maybe due to IP address not whitelisted. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}"
                    : $"Execute TCB API failure. Response Status:{response.ResponseStatus}. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}. Response value : {response.Content}";
                _logger.LogInformation(failureMessage);
                return Result<string>.Failure(failureMessage);
            }
            
            return Result<string>.Success(response.Content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error with execute tcb api command");
            return Result<string>.Failure(ex.Message);
        }
    }

    private async Task AddChangeLogs(RequestBody requestBody, IRestResponse response, string accessToken)
    {
        ResponseBody responseData = JsonConvert.DeserializeObject<ResponseBody>(response.Content);
        
        string query = new _service_CoverGoMutationsRootBuilder()
            .servicingAgentChangeLogMutationBatch(
                new _service_CoverGoMutationsRootBuilder.servicingAgentChangeLogMutationBatchArgs()
                {
                    batch = new users_GenericServicingAgentChangeLog3BatchInput()
                    {
                        create = requestBody.Document.Select(x =>
                        {
                            ResponseData res = responseData?.Document?.Data?.FirstOrDefault(resData =>
                                resData.PolicyNumber == x.PolicyNumber && resData.ServicingAgentCode == x.NewAgentCode);
                            return new users_ServicingAgentChangeLogUpsertInput()
                            {
                                policyNo = x.PolicyNumber,
                                policyName = x.PolicyName,
                                currentServicingAgentCode = x.CurrentAgentCode,
                                currentServicingAgentName = x.CurrentAgentName,
                                newServicingAgentCode = x.NewAgentCode,
                                newServicingAgentName = x.NewAgentName,
                                status = response.IsSuccessful && res?.Status == "Success" ? "Success" : "Failed",
                                errorCode = res?.ErrorCode,
                                message = res?.Message
                            };
                        }).ToList()
                    }
                }, new users_ResultBuilder().WithAllFields()).Build();

        _usersGraphQlHttpClient.HttpClient.DefaultRequestHeaders.Remove("Authorization");
        _usersGraphQlHttpClient.HttpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", $"Bearer {accessToken}");
        GraphQLResponse<JToken> result =
            await _usersGraphQlHttpClient.SendMutationAsync<JToken>(new GraphQLHttpRequest(query));
    }
    class RequestBody
    {
        public Header HeaderRequest { get; set; }
        public RequestBodyData[] Document { get; set; }
    }

    class RequestBodyData
    {

        public string PolicyNumber { get; set; }
        
        [JsonIgnore]
        public string PolicyName { get; set; }
        [JsonProperty("PolicyName")]
        private string PolicyNameAlternateSetter
        {
            set { PolicyName = value; }
        }
        
        [JsonIgnore]
        public string CurrentAgentCode { get; set; }
        [JsonProperty("CurrentAgentCode")]
        private string CurrentAgentCodeAlternateSetter
        {
            set { CurrentAgentCode = value; }
        }
        
        [JsonIgnore]
        public string CurrentAgentName { get; set; }
        [JsonProperty("CurrentAgentName")]
        private string CurrentAgentNameAlternateSetter
        {
            set { CurrentAgentName = value; }
        }
        
        public string NewAgentCode { get; set; }
        
        [JsonIgnore]
        public string NewAgentName { get; set; }
        [JsonProperty("NewAgentName")]
        private string NewAgentNameAlternateSetter
        {
            set { NewAgentName = value; }
        }
    }
    
    class ResponseBody
    {
        public ResponseDocument Document { get; set; }
    }

    class ResponseDocument
    {
        public ResponseData[] Data { get; set; }
    }

    class ResponseData
    {
        public string PolicyNumber { get; set; }
        public string ServicingAgentCode { get; set; }
        public string Status { get; set; }
        public string ErrorCode { get; set; }
        public string Message { get; set; }
    }

    class ResponseError
    {
        public string ErrorCode { get; set; }
        public string Message { get; set; }
    }

    class Header
    {
        public string MessageId { get; set; }
        public string CreateDate { get; set; }
        public string FromNm { get; set; }
        public string ToNm { get; set; }
        public string ServiceType { get; set; }
    }
}