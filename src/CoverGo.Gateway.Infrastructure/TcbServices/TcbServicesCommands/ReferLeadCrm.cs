﻿using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands
{
    public class ReferLeadCrm : TcbCommand
    {
        public override string Name { get; } = "referLeadCrm";
        private readonly string _endpoint;

        public ReferLeadCrm(string endpoint, ILogger<ReferLeadCrm> logger) : base(useCrmEndPoint:true,logger)
        {
            _endpoint = endpoint + "/internal-apigw-uat-aws/itcblife/ReferLead";
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName)
        {
            if (_tcbCrmClientId == null)
                return Result<string>.Failure("TCB_CRM_CLIENT_ID is not configured");
            if (_tcbCrmClientSecret == null)
                return Result<string>.Failure("TCB_CRM_CLIENT_SECRET is not configured");
            
            ReferLeadInput referLeadInput = inputJson.ToObject<ReferLeadInput>();

            Result<string> validateResult = TcbHelper.Validate(referLeadInput);

            if (!validateResult.IsSuccess)
                return validateResult;
            
            try
            {
                RestClient client = new(_endpoint) { Timeout = _apiTimeoutSetting, };

                string integratedApiDisableSsl = Environment.GetEnvironmentVariable("INTEGRATED_API_DISABLE_SSL");

                if (!string.IsNullOrEmpty(integratedApiDisableSsl) &&
                    bool.TryParse(integratedApiDisableSsl, out bool flag) && flag)
                    client.RemoteCertificateValidationCallback = AcceptAllCertifications;

                RestRequest request = new(Method.POST);
                request.AddHeader("client_id", _tcbCrmClientId);
                request.AddHeader("client_secret", _tcbCrmClientSecret);
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", inputJson.ToString(Formatting.None),
                    ParameterType.RequestBody);

                IRestResponse response = await client.ExecuteAsync(request);
                LogRequest(client, request, response);

                if (response.IsSuccessful != true)
                {
                    string failureMessage = response.ResponseStatus == ResponseStatus.TimedOut
                        ? $"TCB getLeadsCrm API timeout (>{_apiTimeoutSetting / 1000}s) - maybe due to IP address not whitelisted. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}"
                        : $"Execute TCB getLeadsCrm API failure. Response Status:{response.ResponseStatus}. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}. Response value : {response.Content}";
                    _logger.LogInformation(failureMessage);
                    return Result<string>.Failure(failureMessage);
                }

                return Result<string>.Success(response.Content);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error with execute tcb getLeadsCrm api command");
                return Result<string>.Failure(ex.Message);
            }
        }
    }

    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    public class Affordability
    {
        public int? financialAffordability { get; set; }
        public int? estimatedPremiumDuration { get; set; }
        public decimal? financialAffordabilityAmount { get; set; }
        public string investmentProfile { get; set; }
    }

    public class Education
    {
        public bool isSelected { get; set; }
        public decimal? targetAmount { get; set; }
        public int? targetDuration { get; set; }
    }

    public class Expense
    {
        public decimal livingStandard { get; set; }
        public decimal? education { get; set; }
        public decimal? emergency { get; set; }
        public decimal? socializingAndEntertainment { get; set; }
        public decimal? insuranceFee { get; set; }
        public decimal? liabilities { get; set; }
        public decimal? other { get; set; }
    }

    public class Financial
    {
        public decimal totalIncome { get; set; }
        public decimal totalExpenses { get; set; }
        public decimal netDisposableIncome { get; set; }
        public List<Income> incomes { get; set; }
        public List<Expense> expenses { get; set; }
    }

    public class FnaDatum
    {
        public List<Financial> financial { get; set; }
        public List<Objective> objective { get; set; }
        public List<Affordability> affordability { get; set; }
    }

    public class HealthProtection
    {
        public bool isSelected { get; set; }
        public decimal? targetAmount { get; set; }
        public int? targetDuration { get; set; }
    }

    public class Income
    {
        public decimal? investmentAndBusiness { get; set; }
        public decimal salaryAndBonus { get; set; }
        public decimal? realEstateIncome { get; set; }
        public int? bankInterest { get; set; }
        public int? sharesAndDividends { get; set; }
        public int? other { get; set; }
    }

    public class InsuredDetail
    {
        public string insuredFullname { get; set; }
        public string insuredDob { get; set; }
        public string insuredGender { get; set; }
        public int relationPoInsured { get; set; }
        public string planCode { get; set; }
        public decimal premium { get; set; }
        public int premiumTerm { get; set; }
        public decimal? sumAssured { get; set; }
        public decimal? premiumAmount { get; set; }
        public string coverageType { get; set; }
        public string riderValue { get; set; }
    }

    public class LifeProtection
    {
        public bool isSelected { get; set; }
        public decimal? targetAmount { get; set; }
        public int? targetDuration { get; set; }
    }

    public class Objective
    {
        public List<Education> education { get; set; }
        public List<Retirement> retirement { get; set; }
        public List<Saving> savings { get; set; }
        public List<LifeProtection> lifeProtection { get; set; }
        public List<HealthProtection> healthProtection { get; set; }
    }

    public class Retirement
    {
        public bool isSelected { get; set; }
        public decimal? targetAmount { get; set; }
        public int? targetDuration { get; set; }
    }

    public class ReferLeadRequest
    {
        public ReferLeadInput referLeadInput { get; set; }
        public UpdateLeadInput updateLeadInput { get; set; }
    }

    public class ReferLeadInput
    {
        public string channel { get; set; }
        public string custId { get; set; }
        public string poFullname { get; set; }
        public string poCitizenId { get; set; }
        public string poCitizenType { get; set; }
        public string poPhoneNumber { get; set; }
        public string poEmail { get; set; }
        public string poGender { get; set; }
        public string poDOB { get; set; }
        public string poMaritalStatus { get; set; }
        public string poAddress { get; set; }
        public string poProvinceCode { get; set; }
        public string poDistrictCode { get; set; }
        public string poWardCode { get; set; }
        public string poIdIsssuedDate { get; set; }
        public string conversationId { get; set; }
        public string referCode { get; set; }
        public string referDate { get; set; }
        public string agentCode { get; set; }
        public string branchCode { get; set; }
        public string bankStaffName { get; set; }
        public string emailTcb { get; set; }
        public string indirectReferrer { get; set; }
        public string staffId { get; set; }
        public string link { get; set; }
        public List<InsuredDetail> insuredDetails { get; set; }
        public List<FnaDatum> fnaData { get; set; }
        public string bankerCode { get; set; }
        public string consultChannel { get; set; }
    }

    public class UpdateLeadInput
    {
        public string crmId { get; set; }
        public string conversationResult { get; set; }
        public string productCode { get; set; }
        public int? APE { get; set; }
        public bool? isAgentPO { get; set; }
        public string indirectRefferer { get; set; }
        public string agentCode { get; set; }
        public string staffId { get; set; }
    }
    public class Saving
    {
        public bool isSelected { get; set; }
        public decimal? targetAmount { get; set; }
        public int? targetDuration { get; set; }
    }


}
