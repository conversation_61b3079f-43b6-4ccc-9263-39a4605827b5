using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands
{
    public class GetLeads : TcbCommand
    {
        public override string Name { get; } = "getLeads";
        private readonly string _endpoint;
        private const string CaptchaSecret = "6LelyvIbAAAAACSr8bAKg1lkhWJXZOX0HaxGcNR0";
        private const string CaptchaEndpoint = "https://www.google.com/recaptcha/api/siteverify";

        public GetLeads(string endpoint, ILogger<GetLeads> logger) : base(logger)
        {
            _endpoint = endpoint +  (IsTcbIntegrationChanged ? "GetLeads" : "/services/iTCBLife/GetLeads");

        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName)
        {
            bool responseCaptchaPresent = inputJson.TryGetValue("responseCaptcha", out JToken captchaResponse);
            if (responseCaptchaPresent) inputJson.Property("responseCaptcha")?.Remove();

            if (Environment.GetEnvironmentVariable("DISABLE_CAPTCHA") != "true")
            {
                try
                {
                    HttpClient httpClient = new();
                    StringContent content = new($"secret={CaptchaSecret}&response={captchaResponse}");
                    content.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");
                    HttpResponseMessage response = await httpClient.PostAsync(CaptchaEndpoint, content, cancellationToken: new CancellationToken());
                    if (!response.IsSuccessStatusCode)
                        return new Result<string> { Status = "failure", Errors = new List<string> { $"ReCaptcha failure. Status code: {response.StatusCode}:{await response.Content.ReadAsStringAsync()}" } };

                    string responseString = await response.Content.ReadAsStringAsync();
                    RecaptchaResponse recaptchaResponse = JsonConvert.DeserializeObject<RecaptchaResponse>(responseString);

                    return recaptchaResponse.Success
                        ? await ExecuteAsync(_endpoint, inputJson)
                        : Result<string>.Failure(recaptchaResponse.ErrorCodes.Select(e => $"ReCaptcha error: {e}"));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "ReCaptcha failure.");
                    return Result<string>.Failure(ex.Message);
                }
            }

            return await ExecuteAsync(_endpoint, inputJson);
        }
    }
}