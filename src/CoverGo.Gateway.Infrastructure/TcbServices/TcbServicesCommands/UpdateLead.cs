using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands
{
    public class UpdateLead : TcbCommand
    {
        public override string Name { get; } = "updateLead";
        private readonly string _endpoint;

        public UpdateLead(string endpoint, ILogger<UpdateLead> logger) : base(logger)
        {
            _endpoint = endpoint + (IsTcbIntegrationChanged ? "UpdateLead" : "/services/iTCBLife/UpdateLead");
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName) =>
             await ExecuteAsync(_endpoint, inputJson);
    }
}