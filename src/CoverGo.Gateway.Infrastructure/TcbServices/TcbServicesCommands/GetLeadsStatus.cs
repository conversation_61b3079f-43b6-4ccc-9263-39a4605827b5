using System;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using RestSharp;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands;

public class GetLeadsStatus : TcbCommand
{
    private static readonly string _tcbManulifeClientId =
        Environment.GetEnvironmentVariable("TCB_MANULIFE_CLIENT_ID") ?? "fb512ed359934e76bb6e1f69b02e14be";
    private static readonly string _tcbManulifeClientSecret =
        Environment.GetEnvironmentVariable("TCB_MANULIFE_CLIENT_SECRET") ?? "371726934dE74406b8679EbA269fCAd3";
    
    public override string Name => "getLeadsStatus";
    
    private readonly string _endpoint;
    
    public GetLeadsStatus(string endpoint, ILogger<GetLeadsStatus> logger) 
        : base(logger)
    {
        _endpoint = endpoint + "get-leads-status";
    }

    public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName)
    {
        if (_tcbManulifeClientId == null )
            return Result<string>.Failure("TCB_MANULIFE_CLIENT_ID is not configured");
        if (_tcbManulifeClientSecret == null)
            return Result<string>.Failure("TCB_MANULIFE_CLIENT_SECRET is not configured");

        RequestBody requestBody;
        try
        {
            requestBody = inputJson.ToObject<RequestBody>();
        }
        catch(Exception ex)
        {
            return Result<string>.Failure($"inputJson is not valid, exception: {ex.Message}");
        }

        requestBody!.HeaderRequest.MessageId ??= Guid.NewGuid().ToString();
        
        try
        {
            RestClient client = new(_endpoint) { Timeout = _apiTimeoutSetting, };

            string integratedApiDisableSsl = Environment.GetEnvironmentVariable("INTEGRATED_API_DISABLE_SSL");

            if (!string.IsNullOrEmpty(integratedApiDisableSsl) &&
                bool.TryParse(integratedApiDisableSsl, out bool flag) && flag)
                client.RemoteCertificateValidationCallback = AcceptAllCertifications;

            RestRequest request = new(Method.POST);
            request.AddHeader("client_id", _tcbManulifeClientId);
            request.AddHeader("client_secret", _tcbManulifeClientSecret);
            request.AddHeader("Content-Type", "application/json");
            request.AddParameter("application/json", JsonConvert.SerializeObject(requestBody, Formatting.None, new JsonSerializerSettings 
                { 
                    ContractResolver = new CamelCasePropertyNamesContractResolver() 
                }),
                ParameterType.RequestBody);
            
            IRestResponse response = await client.ExecuteAsync(request);
            LogRequest(client, request, response);
            
            if (response.IsSuccessful != true)
            {
                string failureMessage = response.ResponseStatus == ResponseStatus.TimedOut
                    ? $"Get TCB API Auth timeout (>{_apiTimeoutSetting / 1000}s) - maybe due to IP address not whitelisted. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}"
                    : $"Execute TCB API failure. Response Status:{response.ResponseStatus}. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}. Response value : {response.Content}";
                _logger.LogInformation(failureMessage);
                return Result<string>.Failure(failureMessage);
            }

            return Result<string>.Success(response.Content);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error with execute tcb api command");
            return Result<string>.Failure(ex.Message);
        }
    }

    class RequestBody
    {
        public HeaderRequest HeaderRequest { get; set; }
        public Document Document { get; set; }
    }

    class Document
    {
        public string AgentCode { get; set; }
        public LeadIdItem[] LeadIds { get; set; }
    }

    class LeadIdItem
    {
        public string LeadId { get; set; }
    }
    
    class HeaderRequest
    {
        public string MessageId { get; set; }
        public string CreateDate { get; set; }
        public string FromNm { get; set; }
        public string ToNm { get; set; }
        public string ServiceType { get; set; }
    }
}