﻿using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using RestSharp;
using System;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands
{
    public class CreateNewLeadCrm : TcbCommand
    {
        public override string Name { get; } = "createLeadCrm";
        private readonly string _endpoint;

        public CreateNewLeadCrm(string endpoint, ILogger<CreateNewLeadCrm> logger) : base(useCrmEndPoint: true, logger)
        {
            _endpoint = endpoint + "/leads";
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName)
        {
            if (_tcbCrmClientId == null)
                return Result<string>.Failure("TCB_CRM_CLIENT_ID is not configured");
            if (_tcbCrmClientSecret == null)
                return Result<string>.Failure("TCB_CRM_CLIENT_SECRET is not configured");

            CreateLeadRequestBody requestBody;
            try
            {
                requestBody = inputJson.ToObject<CreateLeadRequestBody>();
            }
            catch (Exception ex)
            {
                return Result<string>.Failure($"inputJson is not valid, exception: {ex.Message}");
            }

            if (requestBody?.poFullName == null)
                return Result<string>.Failure($"inputJson.poFullName is required");
            if (requestBody?.productCode == null)
                return Result<string>.Failure($"inputJson.productCode is required");
            if (requestBody?.saleUserId == null)
                return Result<string>.Failure($"inputJson.saleUserId is required");

            TcbCrmCreateLeadRequest createLeadRequest = new TcbCrmCreateLeadRequest()
            {
                headerRequest = HeaderRequest.CreateAndGet(),
                document = requestBody
            };

            try
            {
                RestClient client = new(_endpoint) { Timeout = _apiTimeoutSetting, };

                string integratedApiDisableSsl = Environment.GetEnvironmentVariable("INTEGRATED_API_DISABLE_SSL");

                if (!string.IsNullOrEmpty(integratedApiDisableSsl) &&
                    bool.TryParse(integratedApiDisableSsl, out bool flag) && flag)
                    client.RemoteCertificateValidationCallback = AcceptAllCertifications;

                RestRequest request = new(Method.POST);
                request.AddHeader("client_id", _tcbCrmClientId);
                request.AddHeader("client_secret", _tcbCrmClientSecret);
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", JsonConvert.SerializeObject(createLeadRequest, Formatting.None, new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                }),
                    ParameterType.RequestBody);
                _logger.LogInformation($"{tenantId} createLeadCrm request {JsonConvert.SerializeObject(createLeadRequest)} to endpoint url: {_endpoint}");
                _logger.LogInformation("createLeadCrm TcbCommand sending request {request} to:{endpoint}", JsonConvert.SerializeObject(createLeadRequest), _endpoint);
                IRestResponse response = await client.ExecuteAsync(request);
                _logger.LogInformation("createLeadCrm TcbCommand response recieved {response} from:{endpoint} with status code:{StatusCode} and error:{error}", response.Content, _endpoint, response.StatusCode, response.ErrorMessage); 
                LogRequest(client, request, response);

                if (response.IsSuccessful != true)
                {
                    string failureMessage = response.ResponseStatus == ResponseStatus.TimedOut
                        ? $"TCB createLeadCrm API timeout (>{_apiTimeoutSetting / 1000}s) - maybe due to IP address not whitelisted. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}"
                        : $"Execute TCB createLeadCrm API failure. Response Status:{response.ResponseStatus}. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}. Response value : {response.Content}";
                    _logger.LogInformation(failureMessage);
                    return Result<string>.Failure(failureMessage);
                }

                return Result<string>.Success(response.Content);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error with execute tcb createLeadCrm api command");
                return Result<string>.Failure(ex.Message);
            }

        }



        class TcbCrmCreateLeadRequest
        {
            public HeaderRequest headerRequest { get; set; }
            public CreateLeadRequestBody document { get; set; }

        }

        public class CreateLeadRequestBody
        {
            [JsonRequired]
            public string poFullName { get; set; }
            public string poCitizenId { get; set; }
            public string poCitizenType { get; set; }
            public string poPhoneNumber { get; set; }
            public string poEmail { get; set; }
            [JsonRequired]
            public string productCode { get; set; }
            [JsonRequired]
            public string saleUserId { get; set; }
            public string relatedAccount { get; set; }
        }

        public class HeaderRequest
        {
            public string messageId { get; set; }
            public DateTime createDate { get; set; }
            public string from { get; set; }
            public string to { get; set; }
            public string serviceType { get; set; }

            public static HeaderRequest CreateAndGet()
            {
                return new HeaderRequest()
                {
                    createDate = DateTime.UtcNow,
                    from = "iTCBLife",
                    to = "SFDC",
                    messageId = Guid.NewGuid().ToString(),
                    serviceType = ""
                };
            }
        }
    }


}

