using System;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Infrastructure.TcbServices.TcbServicesCommands
{
    public class CreateNewLead : TcbCommand
    {
        public override string Name { get; } = "createLead";
        private readonly string _endpoint;

        public CreateNewLead(string endpoint, ILogger<CreateNewLead> logger) : base(logger)
        {
            _endpoint =  endpoint + (IsTcbIntegrationChanged ? "CreateLead" : "/services/iTCBLife/CreateLead");
            
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName) =>
             await ExecuteAsync(_endpoint, inputJson);
    }
}