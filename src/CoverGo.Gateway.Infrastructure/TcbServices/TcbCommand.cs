using System;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using static CoverGo.Gateway.Infrastructure.TcbServices.EncryptionHelper;

namespace CoverGo.Gateway.Infrastructure.TcbServices
{
    public abstract class TcbCommand
    {
        protected readonly ILogger<TcbCommand> _logger;
        public abstract string Name { get; }
        public abstract Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName);

        public virtual Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson, string bucketName,
            string accessToken) =>
            ExecuteAsync(tenantId, inputJson, bucketName);

        private static readonly string _tcbAuthTokenSecret = Environment.GetEnvironmentVariable("TCB_API_AUTH_SECRET") ?? "Basic T2F1dGhJVENCTElGRToxMjM0NTY3OA==";// TEC-515: temp for doing a rename
        private static readonly string _tcbCrmAuthTokenSecret = Environment.GetEnvironmentVariable("TCB_CRM_API_AUTH_SECRET") ?? "Basic ZjIxMzJmNzk4NDM3NDZkYjg5YjI3M2E4MjZkY2ZkNDZ8Y2Y1OTNCZUY3ZWZlNEYyY2E1MjdmRTc1MTJFMDZBQWE=";
        private static readonly string _authTokenbaseEndpoint = Environment.GetEnvironmentVariable("TCB_API_ENDPOINT") ?? "https://101.99.23.176:8457"; // TEC-515: temp for doing a rename
        private static readonly string _crmAuthTokenbaseEndpoint = Environment.GetEnvironmentVariable("TCB_CRM_API_ENDPOINT") ?? "https://101.99.23.176:9447";
        private static readonly string _tcbApiAuthScope = Environment.GetEnvironmentVariable("TCB_API_AUTH_SCOPE") ?? "test"; // TEC-515: temp for doing a rename
        private static readonly string _tcbIntegrationChanged = Environment.GetEnvironmentVariable("TCB_INTEGRATION_CHANGED");
        protected readonly bool IsTcbIntegrationChanged;
        protected static int _apiTimeoutSetting = 55000; // 55000 milliseconds == 55 seconds
        private readonly bool _useCrmEndPoint = false;
        protected static readonly string _tcbCrmClientId = Environment.GetEnvironmentVariable("TCB_CRM_CLIENT_ID") ?? "f2132f79843746db89b273a826dcfd46";
        protected static readonly string _tcbCrmClientSecret = Environment.GetEnvironmentVariable("TCB_CRM_CLIENT_SECRET") ?? "cf593BeF7efe4F2ca527fE7512E06AAa";

        protected TcbCommand(ILogger<TcbCommand> logger)
        {
            bool.TryParse(_tcbIntegrationChanged, out IsTcbIntegrationChanged);
            _logger = logger;
        }

        protected TcbCommand(bool useCrmEndPoint, ILogger<TcbCommand> logger) : this(logger)
        {
            _useCrmEndPoint = useCrmEndPoint;
        }

        protected virtual async Task<Result<string>> ExecuteAsync(string endpoint, JObject inputJson)
        {
            if (IsTcbIntegrationChanged) return await ExecuteAsyncAfterIntegrationChanged(endpoint, inputJson);
            
            bool conversationIdPresent = inputJson.TryGetValue("conversationId", out JToken conversationId);
            if (conversationIdPresent) inputJson.Property("conversationId")?.Remove();

            Result<string> getAccessToken = await GenerateAuthenticationToken();
            if (getAccessToken.Status != "success") return getAccessToken;

            Result<string> getInputEncrypted = Encrypt(inputJson.ToString(Formatting.None));
            if (getInputEncrypted.Status != "success") return getInputEncrypted;

            Result<string> getSignature = Sign(getInputEncrypted.Value);
            if (getSignature.Status != "success") return getSignature;

            try
            {
                RestClient client = new(endpoint)
                {
                    Timeout = _apiTimeoutSetting,
                };

                string integratedApiDisableSsl = Environment.GetEnvironmentVariable("INTEGRATED_API_DISABLE_SSL");

                if (!string.IsNullOrEmpty(integratedApiDisableSsl) && bool.TryParse(integratedApiDisableSsl, out bool flag) && flag)
                {
                    client.RemoteCertificateValidationCallback = AcceptAllCertifications;
                }

                RestRequest request = new(Method.POST);
                request.AddHeader("x-request-signature", getSignature.Value);
                request.AddHeader("Authorization", "Bearer " + getAccessToken.Value);
                request.AddHeader("Content-Type", "text/plain");
                request.AddParameter("text/plain", "{\"data\":\"" + getInputEncrypted.Value + "\"}", ParameterType.RequestBody);
                _logger.LogInformation($"Executing Request: Endpoint: {endpoint}. ConversationId: {conversationId}. Tcb API input encrypted: {getInputEncrypted.Value}. ");
                LogRequest(client, request, null);
                IRestResponse response = await client.ExecuteAsync(request);
                LogRequest(client, request, response);
                _logger.LogInformation($"Executed Request: Endpoint: {endpoint}. ConversationId: {conversationId}. Response Status:{response.ResponseStatus}. Status Code:{response.StatusCode}. Tcb API input encrypted: {getInputEncrypted.Value}. Response value data encrypted: {JObject.Parse(response.Content).Value<string>("data")}. ");

                if (response.IsSuccessful != true)
                {
                    string failureMessage = response.ResponseStatus == ResponseStatus.TimedOut
                        ? $"Get TCB API Auth timeout (>{_apiTimeoutSetting / 1000}s) - maybe due to IP address not whitelisted. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}"
                        : $"Execute TCB API failure. Response Status:{response.ResponseStatus}. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}. Response value decrypted: {Decrypt(JObject.Parse(response.Content).Value<string>("data")).Value}";
                    _logger.LogInformation(failureMessage);
                    return Result<string>.Failure(failureMessage);
                }
                Result<string> getDecryptedResponse = Decrypt(JObject.Parse(response.Content).Value<string>("data"));
                return getDecryptedResponse.Status != "success" ?
                    getDecryptedResponse :
                    Result<string>.Success(getDecryptedResponse.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error with excecute tcb api command.");
                return Result<string>.Failure(ex.Message);
            }
        }

        private async Task<Result<string>> ExecuteAsyncAfterIntegrationChanged(string endpoint, JObject inputJson)
        {
            bool conversationIdPresent = inputJson.TryGetValue("conversationId", out JToken conversationId);
            if (conversationIdPresent) inputJson.Property("conversationId")?.Remove();

            try
            {
                RestClient client = new(endpoint) { Timeout = _apiTimeoutSetting, };

                string integratedApiDisableSsl = Environment.GetEnvironmentVariable("INTEGRATED_API_DISABLE_SSL");

                if (!string.IsNullOrEmpty(integratedApiDisableSsl) &&
                    bool.TryParse(integratedApiDisableSsl, out bool flag) && flag)
                {
                    client.RemoteCertificateValidationCallback = AcceptAllCertifications;
                }

                string inputJsonString = inputJson.ToString(Formatting.None);
                
                RestRequest request = new(Method.POST);

                string authorization = _useCrmEndPoint ? _tcbCrmAuthTokenSecret : _tcbAuthTokenSecret;

                if (string.IsNullOrEmpty(authorization))
                {
                    _logger.LogCritical("[TcbCommand] Missing authorization header");
                    authorization = "Basic YXV0aGVudXNlcjphYmMxMjM0NQ==";
                }

                request.AddHeader("Authorization", authorization);
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("application/json", inputJsonString,
                    ParameterType.RequestBody);
                _logger.LogInformation(
                    $"Executing Request: Endpoint: {endpoint}. ConversationId: {conversationId}. Tcb API input: {inputJsonString}. ");
                LogRequest(client, request, null);
                IRestResponse response = await client.ExecuteAsync(request);
                LogRequest(client, request, response);
                _logger.LogInformation(
                    $"Executed Request: Endpoint: {endpoint}. ConversationId: {conversationId}. Response Status:{response.ResponseStatus}. Status Code:{response.StatusCode}. Tcb API input: {inputJsonString}. Response value data: {response.Content}. ");

                if (response.IsSuccessful != true)
                {
                    string failureMessage = response.ResponseStatus == ResponseStatus.TimedOut
                        ? $"Get TCB API Auth timeout (>{_apiTimeoutSetting / 1000}s) - maybe due to IP address not whitelisted. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}"
                        : $"Execute TCB API  failure. Response Status:{response.ResponseStatus}. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}. Response value : {response.Content}";
                    _logger.LogInformation(failureMessage);
                    return Result<string>.Failure(failureMessage);
                }

                return Result<string>.Success(response.Content);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error with excecute tcb api command.");
                return Result<string>.Failure(ex.Message);
            }
        }

        private async Task<Result<string>> GenerateAuthenticationToken()
        {
            RestClient client = new(_useCrmEndPoint ? _crmAuthTokenbaseEndpoint : _authTokenbaseEndpoint + "/oauth/getToken")
            {
                Timeout = _apiTimeoutSetting,
                RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true // Disable ssl certificate verification
            };
            RestRequest request = new(Method.POST);
            request.AddHeader("authorization", _useCrmEndPoint ? _tcbCrmAuthTokenSecret : _tcbAuthTokenSecret);
            request.AlwaysMultipartFormData = true;
            request.AddParameter("grant_type", "client_credentials");
            request.AddParameter("scope", _tcbApiAuthScope);


            _logger.LogInformation("Getting TCB Auth token");
            try
            {
                LogRequest(client, request, null);
                IRestResponse response = await client.ExecuteAsync(request);
                LogRequest(client, request, response);
                if (response.IsSuccessful != true)
                {
                    string failureMessage = response.ResponseStatus == ResponseStatus.TimedOut
                        ? $"Get TCB API Auth timeout - maybe due to IP address not whitelisted. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}"
                        : $"Get TCB API Auth token failure. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}";
                    _logger.LogInformation(failureMessage);
                    return Result<string>.Failure(failureMessage);

                }

                _logger.LogInformation($"Got TCB Auth token response {response.Content}");

                string authToken = JObject.Parse(response.Content).Value<string>("access_token");
                if (authToken == null)
                {
                    string failureMessage = $"No access_token found in response content. Response Content: {response.Content}";
                    _logger.LogInformation(failureMessage);
                    return Result<string>.Failure(failureMessage);
                }
                _logger.LogInformation($"Got TCB Auth token {authToken}");

                return Result<string>.Success(authToken);
            }
            catch (Exception ex)
            {
                return Result<string>.Failure($"Failed to get TCB Auth token {ex.Message}");
            }
        }

        /// <summary>
        /// In Short: the Method solves the Problem of broken Certificates.
        /// Sometime when requesting Data and the sending Webserverconnection
        /// is based on a SSL Connection, an Error is caused by Servers whoes
        /// Certificate(s) have Errors. Like when the Cert is out of date
        /// and much more... So at this point when calling the method,
        /// this behaviour is prevented
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="certification"></param>
        /// <param name="chain"></param>
        /// <param name="sslPolicyErrors"></param>
        /// <returns>true</returns>
        protected static bool AcceptAllCertifications(object sender, System.Security.Cryptography.X509Certificates.X509Certificate certification, System.Security.Cryptography.X509Certificates.X509Chain chain, System.Net.Security.SslPolicyErrors sslPolicyErrors)
        {
            return true;
        }
        
        protected void LogRequest(RestClient client, IRestRequest request, IRestResponse response)
        {
            if (request != null)
            {
                var requestToLog = new
                {
                    resource = request.Resource,
                    parameters = request.Parameters.Select(parameter => new
                    {
                        name = parameter.Name,
                        value = parameter.Value,
                        type = parameter.Type.ToString()
                    }),
                    method = request.Method.ToString(),
                    uri = client.BuildUri(request),
                };
                Console.WriteLine($"TcbCommand Request: {JsonConvert.SerializeObject(requestToLog)}");
            }

            if (response != null)
            {
                var responseToLog = new
                {
                    statusCode = response.StatusCode,
                    content = response.Content,
                    headers = response.Headers,
                    responseUri = response.ResponseUri,
                    errorMessage = response.ErrorMessage,
                };
                Console.WriteLine($"TcbCommand Response: {JsonConvert.SerializeObject(responseToLog)}");
            }
        }
    }
}