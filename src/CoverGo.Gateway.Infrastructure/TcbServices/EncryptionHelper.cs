using System;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using CoverGo.DomainUtils;
using Newtonsoft.Json;

namespace CoverGo.Gateway.Infrastructure.TcbServices
{
    public static class EncryptionHelper
    {
        public static Result<string> Encrypt(string plainText)
        {
            try
            {
                string encryptionKey = Environment.GetEnvironmentVariable("TCB_API_AES_TOKEN") ?? Environment.GetEnvironmentVariable("TCB_API_TOKEN"); // TEC-515: temp for doing a rename
                byte[] cipherData;

                Aes aes = Aes.Create();
                aes.Key = StringToByteArray(encryptionKey);
                aes.GenerateIV();
                aes.Mode = CipherMode.CBC;
                ICryptoTransform cipher = aes.CreateEncryptor(aes.Key, aes.IV);
                using (MemoryStream ms = new())
                {
                    using (CryptoStream cs = new(ms, cipher, CryptoStreamMode.Write))
                    {
                        using StreamWriter sw = new(cs);
                        sw.Write(plainText);
                    }
                    cipherData = ms.ToArray();
                }
                byte[] combinedData = new byte[aes.IV.Length + cipherData.Length];
                Array.Copy(aes.IV, 0, combinedData, 0, aes.IV.Length);
                Array.Copy(cipherData, 0, combinedData, aes.IV.Length, cipherData.Length);
                return Result<string>.Success(Convert.ToBase64String(combinedData));
            }
            catch (Exception ex)
            {
                return Result<string>.Failure("Error with TCB API data encryption. Error message: " + ex.Message);
            }
        }

        public static byte[] EncryptFNA(byte[] input, string tcbPublicKey)
        {
            return PGP.Encrypt(input, PGP.ReadPublicKey(PGP.GetStream(tcbPublicKey)),false,false);
        }

        public static Result<string> Decrypt(string combinedString, string primaryKey = "TCB_API_AES_TOKEN", string fallbackKey = "TCB_API_TOKEN")
        {
            try
            {
                string decryptionKey = Environment.GetEnvironmentVariable("TCB_API_AES_TOKEN") ?? Environment.GetEnvironmentVariable("TCB_API_TOKEN"); // TEC-515: temp for doing a rename
                string decrypted;
                byte[] combinedData = Convert.FromBase64String(combinedString);

                Aes aes = Aes.Create();
                aes.Key = StringToByteArray(decryptionKey);
                byte[] iv = new byte[aes.BlockSize / 8];
                byte[] cipherText = new byte[combinedData.Length - iv.Length];
                Array.Copy(combinedData, iv, iv.Length);
                Array.Copy(combinedData, iv.Length, cipherText, 0, cipherText.Length);
                aes.IV = iv;
                aes.Mode = CipherMode.CBC;
                ICryptoTransform decipher = aes.CreateDecryptor(aes.Key, aes.IV);
                using MemoryStream ms = new(cipherText);
                using (CryptoStream cs = new(ms, decipher, CryptoStreamMode.Read))
                {
                    using StreamReader sr = new(cs);
                    decrypted = sr.ReadToEnd();
                }
                return Result<string>.Success(decrypted);
            }
            catch (Exception ex)
            {
                return Result<string>.Failure("Error with TCB API data decryption. Error message: " + ex.Message);
            }
        }

        public static Result<string> Sign(string data)
        {
            try
            {
                string privatekey = Environment.GetEnvironmentVariable("TCB_API_PRIVATE_KEY");
                RSA tmp = RSA.Create();
                tmp.ImportPkcs8PrivateKey(Convert.FromBase64String(privatekey), out int byteRead);
                string jsonString = JsonConvert.SerializeObject(new { data = data });
                byte[] byteData = Encoding.ASCII.GetBytes(jsonString);
                RSACryptoServiceProvider privateKey = new();
                privateKey.ImportParameters(tmp.ExportParameters(true));
                byte[] signed = privateKey.SignData(byteData, "SHA256");
                return Result<string>.Success(Convert.ToBase64String(signed));
            }

            catch (Exception ex)
            {
                return Result<string>.Failure("Error with TCB API data encryption signature. Error message: " + ex.Message);
            }
        }

        public static byte[] StringToByteArray(string hex) =>
           Enumerable.Range(0, hex.Length)
              .Where(x => x % 2 == 0)
              .Select(x => Convert.ToByte(hex.Substring(x, 2), 16))
              .ToArray();
    }
}