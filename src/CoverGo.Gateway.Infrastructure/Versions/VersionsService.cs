using System;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace CoverGo.Gateway.Infrastructure.Versions;

public class VersionsService
{
    private static readonly string[] Applications = {
        "gateway",
        "auth",
        "users",
        "products",
        "pricing",
        "policies",
        "l10n",
        "notifications",
        "claims",
        "advisor",
        "transactions",
        "templates",
        "cases",
        "filesystem",
        "scheduler",
        "claimInvestigation",
        "achievements",
        "printing",
        "taskManagement",
        "education",
        "finance",
        "reference",
        "requestManager",
        "etl",
        "payments",
        "premium",
        "middleEastIntegration"
    };
    private static readonly VersionInfo DefaultVersionInfo = new("-", "-", default);
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<VersionsService> _logger;

    public VersionsService(
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<VersionsService> logger)
    {
        _httpClient = httpClient;
        _configuration = configuration;
        _logger = logger;
    }

    public Task<ApplicationVersionInfo[]> GetVersionsAsync(
        CancellationToken cancellationToken = default)
    {
        var tasks = Applications
            .Select(a => GetApplicationVersionAsync(a, cancellationToken));

        return Task.WhenAll(tasks);
    }

    private async Task<ApplicationVersionInfo> GetApplicationVersionAsync(
        string application, CancellationToken cancellationToken = default)
    {
        var versionInfo = DefaultVersionInfo;

        try
        {
            var baseUri = new Uri(_configuration[$"serviceUrls:{application}"]);
            var versionUri = new Uri(baseUri, "/version");
            var response = await _httpClient.GetAsync(versionUri, cancellationToken);
            versionInfo = await response.Content.ReadAsJsonAsync<VersionInfo>() ?? DefaultVersionInfo;
        }
        catch (Exception exception)
        {
            _logger.LogError(exception,
                "Failed while fetching version of {application}",
                application);
            versionInfo = DefaultVersionInfo;
        }

        return new ApplicationVersionInfo(
            application,
            versionInfo.Version,
            versionInfo.Revision,
            versionInfo.BuildDateTime);
    }

    private record VersionInfo(string Version, string Revision, DateTime BuildDateTime);

}