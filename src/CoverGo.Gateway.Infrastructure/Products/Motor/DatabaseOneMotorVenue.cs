﻿using CoverGo.Gateway.Domain.Products.Motor;

using Newtonsoft.Json;

using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.Products.Motor
{
    public class DatabaseOneMotorVenue : IMotorVenue
    {
        private readonly HttpClient _client;
        private const string key = "78944b592bb37e08b7a6f5f2d";

        public DatabaseOneMotorVenue(HttpClient client)
        {
            _client = client;
        }

        public async Task<IEnumerable<MotorMake>> GetAllMakesAsync()
        {
            HttpResponseMessage response = await _client.GetAsync($"?format=json&select=make&api_key={key}");

            string json = await response.Content.ReadAsStringAsync();
            RootDao<MotorMakeDao> makes = JsonConvert.DeserializeObject<RootDao<MotorMakeDao>>(json);

            return makes.Result.Select(mm => new MotorMake { Id = mm.Make_id, Name = mm.Make });
        }

        public async Task<IEnumerable<MotorMake>> GetMakesAsync(IEnumerable<string> makeIds)
        {
            HttpResponseMessage response = await _client.GetAsync($"?format=json&select=make&make_id={string.Join(',', makeIds)}&api_key={key}");

            string json = await response.Content.ReadAsStringAsync();
            RootDao<MotorMakeDao> makes = JsonConvert.DeserializeObject<RootDao<MotorMakeDao>>(json);

            return makes.Result.Select(mm => new MotorMake { Id = mm.Make_id, Name = mm.Make });
        }




        public async Task<IEnumerable<MotorModel>> GetAllModelsAsync()
        {
            HttpResponseMessage response = await _client.GetAsync($"?format=json&select=model&api_key={key}");

            string json = await response.Content.ReadAsStringAsync();
            RootDao<MotorModelDao> models = JsonConvert.DeserializeObject<RootDao<MotorModelDao>>(json);

            return models.Result.Select(mm => new MotorModel { Id = mm.Model_id, Name = mm.Model, MakeId = mm.Make_id });
        }

        public async Task<IEnumerable<MotorModel>> GetModelsAsync(string makeId)
        {
            HttpResponseMessage response = await _client.GetAsync($"?format=json&select=model&make_id={makeId}&api_key={key}");

            string json = await response.Content.ReadAsStringAsync();
            RootDao<MotorModelDao> models = JsonConvert.DeserializeObject<RootDao<MotorModelDao>>(json);

            return models.Result.Select(mm => new MotorModel { Id = mm.Model_id, Name = mm.Model, MakeId = mm.Make_id });
        }

        public async Task<IEnumerable<MotorModel>> GetModelsAsync(string makeId, IEnumerable<string> modelIds) =>
            await Task.WhenAll(modelIds.Select(modelId => GetModelAsync(makeId, modelId)).AsEnumerable());

        public async Task<MotorModel> GetModelAsync(string makeId, string modelId)
        {
            HttpResponseMessage response = await _client.GetAsync($"?format=json&select=model&make_id={makeId}&model_id={modelId}&api_key={key}");

            string json = await response.Content.ReadAsStringAsync();
            RootDao<MotorModelDao> models = JsonConvert.DeserializeObject<RootDao<MotorModelDao>>(json);

            return models.Result.Select(mm => new MotorModel { Id = mm.Model_id, Name = mm.Model, MakeId = makeId }).FirstOrDefault();
        }






        public async Task<IEnumerable<MotorGeneration>> GetAllGenerationsAsync()
        {
            HttpResponseMessage response = await _client.GetAsync($"?format=json&select=generation&api_key={key}");

            string json = await response.Content.ReadAsStringAsync();
            RootDao<MotorGenerationDao> generations = JsonConvert.DeserializeObject<RootDao<MotorGenerationDao>>(json);

            return generations.Result.Select(mm => new MotorGeneration { Id = mm.Generation_id, Name = mm.Generation, MakeId = mm.Make_id, ModelId = mm.Model_id });
        }

        public async Task<IEnumerable<MotorGeneration>> GetGenerationsAsync(string makeId)
        {
            HttpResponseMessage response = await _client.GetAsync($"?format=json&select=generation&make_id={makeId}&api_key={key}");

            string json = await response.Content.ReadAsStringAsync();
            RootDao<MotorGenerationDao> generations = JsonConvert.DeserializeObject<RootDao<MotorGenerationDao>>(json);

            return generations.Result.Select(mm => new MotorGeneration { Id = mm.Generation_id, Name = mm.Generation, MakeId = mm.Make_id, ModelId = mm.Model_id });
        }

        public async Task<IEnumerable<MotorGeneration>> GetGenerationsAsync(string makeId, string modelId)
        {
            HttpResponseMessage response = await _client.GetAsync($"?format=json&select=generation&make_id={makeId}&model_id={modelId}&api_key={key}");

            string json = await response.Content.ReadAsStringAsync();
            RootDao<MotorGenerationDao> generations = JsonConvert.DeserializeObject<RootDao<MotorGenerationDao>>(json);

            return generations.Result.Select(mm => new MotorGeneration { Id = mm.Generation_id, Name = mm.Generation, MakeId = makeId, ModelId = modelId });
        }

        public async Task<IEnumerable<MotorGeneration>> GetGenerationsAsync(string makeId, string modelId, IEnumerable<string> generationIds) =>
            await Task.WhenAll(generationIds.Select(generationId => GetGenerationAsync(makeId, modelId, generationId)).AsEnumerable());

        public async Task<MotorGeneration> GetGenerationAsync(string makeId, string modelId, string generationId)
        {
            HttpResponseMessage response = await _client.GetAsync($"?format=json&select=generation&make_id={makeId}&model_id={modelId}&generation_id={generationId}&api_key={key}");

            string json = await response.Content.ReadAsStringAsync();
            RootDao<MotorGenerationDao> generations = JsonConvert.DeserializeObject<RootDao<MotorGenerationDao>>(json);

            return generations.Result.Select(mm => new MotorGeneration { Id = mm.Generation_id, Name = mm.Generation, MakeId = makeId, ModelId = modelId }).FirstOrDefault();
        }






        public async Task<IEnumerable<MotorTrim>> GetAllTrimsAsync()
        {
            HttpResponseMessage response = await _client.GetAsync($"?format=json&select=trim&api_key={key}");

            string json = await response.Content.ReadAsStringAsync();
            RootDao<MotorTrimDao> trims = JsonConvert.DeserializeObject<RootDao<MotorTrimDao>>(json);

            return trims.Result.Select(mt => new MotorTrim { Id = mt.Trim_id, Name = mt.Trim, MakeId = mt.Make_id, ModelId = mt.Model_id, GenerationId = mt.Generation_id });
        }

        public async Task<IEnumerable<MotorTrim>> GetTrimsAsync(string makeId)
        {
            HttpResponseMessage response = await _client.GetAsync($"?format=json&select=trim&make_id={makeId}&api_key={key}");

            string json = await response.Content.ReadAsStringAsync();
            RootDao<MotorTrimDao> trims = JsonConvert.DeserializeObject<RootDao<MotorTrimDao>>(json);

            return trims.Result.Select(mt => new MotorTrim { Id = mt.Trim_id, Name = mt.Trim, MakeId = makeId, ModelId = mt.Model_id, GenerationId = mt.Generation_id });
        }

        public async Task<IEnumerable<MotorTrim>> GetTrimsAsync(string makeId, string modelId)
        {
            HttpResponseMessage response = await _client.GetAsync($"?format=json&select=trim&make_id={makeId}&model_id={modelId}&api_key={key}");

            string json = await response.Content.ReadAsStringAsync();
            RootDao<MotorTrimDao> trims = JsonConvert.DeserializeObject<RootDao<MotorTrimDao>>(json);

            return trims.Result.Select(mt => new MotorTrim { Id = mt.Trim_id, Name = mt.Trim, MakeId = makeId, ModelId = mt.Model_id, GenerationId = mt.Generation_id });
        }

        public async Task<IEnumerable<MotorTrim>> GetTrimsAsync(string makeId, string modelId, string generationId)
        {
            HttpResponseMessage response = await _client.GetAsync($"?format=json&select=trim&make_id={makeId}&model_id={modelId}&generation_id={generationId}&api_key={key}");

            string json = await response.Content.ReadAsStringAsync();
            RootDao<MotorTrimDao> trims = JsonConvert.DeserializeObject<RootDao<MotorTrimDao>>(json);

            return trims.Result.Select(mt => new MotorTrim { Id = mt.Trim_id, Name = mt.Trim, MakeId = makeId, ModelId = mt.Model_id, GenerationId = mt.Generation_id });
        }

        public async Task<IEnumerable<MotorTrim>> GetTrimsAsync(string makeId, string modelId, string generationId, IEnumerable<string> trimIds) =>
            await Task.WhenAll(trimIds.Select(trimId => GetTrimAsync(makeId, modelId, generationId, trimId)).AsEnumerable());

        public async Task<MotorTrim> GetTrimAsync(string makeId, string modelId, string generationId, string trimId)
        {
            HttpResponseMessage response = await _client.GetAsync($"?format=json&select=trim&make_id={makeId}&model_id={modelId}&generation_id={generationId}&trim_id={trimId}&api_key={key}");

            string json = await response.Content.ReadAsStringAsync();
            RootDao<MotorTrimDao> trims = JsonConvert.DeserializeObject<RootDao<MotorTrimDao>>(json);

            return trims.Result.Select(mt => new MotorTrim { Id = mt.Trim_id, Name = mt.Trim, MakeId = makeId, ModelId = modelId, GenerationId = generationId }).FirstOrDefault();
        }






        public async Task<MotorDetails> GetMotorDetails(string makeId, string modelId, string generationId, string trimId)
        {
            HttpResponseMessage response = await _client.GetAsync($"?format=json&select=detail&trim_id={trimId}&api_key={key}");

            string json = await response.Content.ReadAsStringAsync();
            RootDao<MotorDetailsDao> details = JsonConvert.DeserializeObject<RootDao<MotorDetailsDao>>(json);

            return new MotorDetails
            {
                Body = details.Result.FirstOrDefault()?.Body,
                EngineCapacity = int.Parse(details.Result.FirstOrDefault()?.Engine_volume),
                HorsePower = int.Parse(details.Result.FirstOrDefault()?.Engine_power),
                EngineType = details.Result.FirstOrDefault()?.Engine_type,
                Years = details.Result.Select(d => int.Parse(d.Year)),
                ImageUrl = $"https://databases.one/cardatabase-demo-advanced/img/{details.Result.FirstOrDefault()?.Image}.png",
                Drive = details.Result.FirstOrDefault()?.Drive,
                Gearbox = details.Result.FirstOrDefault()?.Gearbox,
            };
        }


        public class RootDao<T>
        {
            public IEnumerable<T> Result { get; set; }
        }

        public class MotorMakeDao
        {
            public string Make_id { get; set; }
            public string Make { get; set; }
        }


        public class MotorModelDao
        {
            public string Make_id { get; set; }
            public string Model_id { get; set; }
            public string Model { get; set; }
        }

        public class MotorGenerationDao
        {
            public string Make_id { get; set; }
            public string Model_id { get; set; }
            public string Generation_id { get; set; }
            public string Generation { get; set; }
        }

        public class MotorTrimDao
        {
            public string Make_id { get; set; }
            public string Model_id { get; set; }
            public string Generation_id { get; set; }
            public string Trim_id { get; set; }
            public string Trim { get; set; }
        }

        public class MotorDetailsDao
        {
            public string Trim_id { get; set; }
            public string Trim { get; set; }
            public string Body { get; set; }
            public string Drive { get; set; }
            public string Gearbox { get; set; }
            public string Engine_type { get; set; }
            public string Engine_volume { get; set; }
            public string Engine_power { get; set; }
            public string Year { get; set; }
            public string Image { get; set; }
        }
    }
}
