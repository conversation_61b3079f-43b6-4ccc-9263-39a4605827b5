﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Products;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;


namespace CoverGo.Gateway.Infrastructure.Products
{
    public class CoverGoProductVenue : IProductService
    {
        private readonly HttpClient _client;

        public CoverGoProductVenue(HttpClient client)
        {
            _client = client;
        }

        public async Task<Result> InitializeTenantAsync(string tenantId, InitializeTenantProductsCommand command) => await _client.GenericPostAsync<Result, InitializeTenantProductsCommand>($"{tenantId}/api/v1/products/initializeTenant", command);

        public async Task<Result> MigrateProductsAsync(string tenantId, MigrateProductsCommand command) => await _client.GenericPostAsync<Result, MigrateProductsCommand>($"{tenantId}/api/v1/products/migrateProducts", command);

        public async Task<Result> CleanProductTest(string tenantId, string typeId) => await _client.GenericPostAsync<Result, string>($"{tenantId}/api/v1/products/cleanProductTest", typeId);

        public Task<IEnumerable<Product2>> GetAsync(string tenantId, string clientId, ProductQuery query) =>
           _client.GenericPostAsync<IEnumerable<Product2>, ProductQuery>($"{tenantId}/api/v1/products/query2?clientId={clientId}", query);
        
        public Task<long> GetTotalCountAsync(string tenantId, string clientId, ProductWhere where) =>
            _client.GenericPostAsync<long, ProductWhere>($"{tenantId}/api/v1/products/totalCount?clientId={clientId}", where);

        public async Task<IDictionary<ProductId, Product2>> GetDictionaryAsync(string tenantId, string clientId, ProductWhere where, bool loadRepresentation) =>
            (await GetAsync(tenantId, clientId, new ProductQuery { Where = where, LoadRepresentation = loadRepresentation })).ToDictionary(x => x.Id, x => x);

        public Task<List<DetailedEventLog>> GetEventsAsync(string tenantId, Proxies.Product.EventQuery query) =>
            _client.GenericPostAsync<List<DetailedEventLog>, Proxies.Product.EventQuery>($"{tenantId}/api/v1/products/events", query);

        public Task<IEnumerable<ValidateResult>> ValidateProductsAsync(string tenantId, string clientId, ProductQuery query) =>
            _client.GenericPostAsync<IEnumerable<ValidateResult>, ProductQuery>($"{tenantId}/api/v1/products/validate?clientId={clientId}", query);

        public Task<Result> CreateAsync(string tenantId, CreateProductCommand command) =>
            _client.GenericPostAsync<Result, CreateProductCommand>($"{tenantId}/api/v1/products", command);

        public Task<Result> UpdateAsync(string tenantId, UpdateProductCommand command) =>
           _client.GenericPutAsync<Result, UpdateProductCommand>($"{tenantId}/api/v1/products", command);

        public Task<Result> CloneAsync(string tenantId, CloneProductCommand command) =>
           _client.GenericPostAsync<Result, CloneProductCommand>($"{tenantId}/api/v1/products/clone", command);

        public Task<IEnumerable<Script>> GetScriptsAsync(string tenantId, ScriptWhere where) =>
           _client.GenericPostAsync<IEnumerable<Script>, ScriptWhere>($"{tenantId}/api/v1/scripts/query", where);

        public Task<Result<CreatedStatus>> CreateScriptAsync(string tenantId, CreateScriptCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreateScriptCommand>($"{tenantId}/api/v1/scripts", command);

        public Task<Result> UpdateScriptAsync(string tenantId, UpdateScriptCommand command) =>
           _client.GenericPutAsync<Result, UpdateScriptCommand>($"{tenantId}/api/v1/scripts", command);

        public Task<Result> DeleteScriptAsync(string tenantId, DeleteCommand command) =>
            _client.GenericDeleteAsync<Result, DeleteCommand>($"{tenantId}/api/v1/scripts", command);

        public Task<Result> AddBenefitAsync(string tenantId, ProductId productId, AddBenefitCommand command) =>
            _client.GenericPostAsync<Result, AddBenefitCommand>($"{tenantId}/api/v1/products/{productId.ToString()}/addBenefit", command);

        public Task<Result> UpdateBenefitAsync(string tenantId, ProductId productId, UpdateBenefitCommand command) =>
           _client.GenericPostAsync<Result, UpdateBenefitCommand>($"{tenantId}/api/v1/products/{productId.ToString()}/benefits/update", command);

        public Task<Result> BenefitBatchAsync(string tenantId, ProductId productId, BenefitCommandBatch command) =>
            _client.GenericPostAsync<Result, BenefitCommandBatch>($"{tenantId}/api/v1/products/{productId.ToString()}/benefits/batch", command);

        public Task<Result> RemoveBenefitAsync(string tenantId, ProductId productId, RemoveBenefitCommand command) =>
            _client.GenericPostAsync<Result, RemoveBenefitCommand>($"{tenantId}/api/v1/products/{productId.ToString()}/removeBenefit", command);

        public Task<Result> AddScriptToProductAsync(string tenantId, AddScriptToProductCommand command) =>
            _client.GenericPostAsync<Result, AddScriptToProductCommand>($"{tenantId}/api/v1/products/scripts", command);

        public Task<Result> RemoveScriptFromProductAsync(string tenantId, RemoveScriptFromProductCommand command) =>
            _client.GenericDeleteAsync<Result, RemoveScriptFromProductCommand>($"{tenantId}/api/v1/products/scripts", command);

        public Task<Result> AddTemplateRelationshipToProductAsync(string tenantId, AddTemplateRelationshipToProductCommand command) =>
            _client.GenericPostAsync<Result, AddTemplateRelationshipToProductCommand>($"{tenantId}/api/v1/products/templateRelationships", command);

        public Task<Result> RemoveTemplateRelationshipFromProductAsync(string tenantId, RemoveTemplateRelationshipFromProductCommand command) =>
            _client.GenericDeleteAsync<Result, RemoveTemplateRelationshipFromProductCommand>($"{tenantId}/api/v1/products/templateRelationships", command);

        public Task<Result<CreatedStatus>> AddTagAsync(string tenantId, ProductId productId, AddTagCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, AddTagCommand>($"{tenantId}/api/v1/products/{productId.ToString()}/tags/add", command);

        public Task<Result> RemoveTagAsync(string tenantId, ProductId productId, RemoveCommand command) =>
            _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/products/{productId.ToString()}/tags/remove", command);

        public Task<Result<CreatedStatus>> AddFactAsync(string tenantId, ProductId productId, AddFactCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, AddFactCommand>($"{tenantId}/api/v1/products/{productId.ToString()}/facts/add", command);

        public Task<Result> UpdateFactAsync(string tenantId, ProductId productId, UpdateFactCommand command) =>
            _client.GenericPutAsync<Result, UpdateFactCommand>($"{tenantId}/api/v1/products/{productId.ToString()}/facts/update", command);

        public Task<Result> RemoveFactAsync(string tenantId, ProductId productId, RemoveCommand command) =>
            _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/products/{productId.ToString()}/facts/remove", command);

        public Task<Result> ProductFactBatchAsync(string tenantId, ProductId productId, FactCommandBatch command) =>
            _client.GenericPutAsync<Result, FactCommandBatch>($"{tenantId}/api/v1/products/{productId.ToString()}/facts/batch", command);

        public Task<Result<CreatedStatus>> AddInternalReviewAsync(string tenantId, ProductId productId, AddInternalReviewCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, AddInternalReviewCommand>($"{tenantId}/api/v1/products/{productId.ToString()}/internalreviews/add", command);

        public Task<Result> UpdateInternalReviewAsync(string tenantId, ProductId productId, UpdateInternalReviewCommand command) =>
            _client.GenericPutAsync<Result, UpdateInternalReviewCommand>($"{tenantId}/api/v1/products/{productId.ToString()}/internalreviews/update", command);

        public Task<Result> RemoveInternalReviewAsync(string tenantId, ProductId productId, RemoveCommand command) =>
            _client.GenericDeleteAsync<Result, RemoveCommand>($"{tenantId}/api/v1/products/{productId.ToString()}/internalreviews/remove", command);

        public Task<Result> DeleteAsync(string tenantId, DeleteProductCommand command) =>
            _client.GenericPostAsync<Result, DeleteProductCommand>($"{tenantId}/api/v1/products/delete", command);

        public Task<Dictionary<string, Dictionary<string, List<string>>>> GetBenefitInfosAsync(string tenantId, string clientId) =>
            _client.GenericGetAsync<Dictionary<string, Dictionary<string, List<string>>>>($"{tenantId}/api/v1/products/benefits/infos?clientId={clientId}");

        public Task<Dictionary<string, List<string>>> GetBenefitCategoriesAsync(string tenantId, string clientId) =>
            _client.GenericGetAsync<Dictionary<string, List<string>>>($"{tenantId}/api/v1/products/benefits/categories?clientId={clientId}");

        public Task<IEnumerable<ProductType>> GetTypesAsync(string tenantId, string clientId, ProductTypeWhere where)
            => _client.GenericPostAsync<IEnumerable<ProductType>, ProductTypeWhere>($"{tenantId}/api/v1/products/types/query?clientId={clientId}", where);

        public async Task<IDictionary<string, ProductType>> GetTypeDictionaryAsync(string tenantId, string clientId, ProductTypeWhere where) =>
         (await GetTypesAsync(tenantId, clientId, where)).ToDictionary(x => x.TypeId, x => x);

        public Task<Result> CreateTypeAsync(string tenantId, CreateProductTypeCommand command)
            => _client.GenericPostAsync<Result, CreateProductTypeCommand>($"{tenantId}/api/v1/products/types", command);

        public Task<Result> DeleteTypeAsync(string tenantId, string typeId, string deletedById)
            => _client.GenericDeleteAsync<Result>($"{tenantId}/api/v1/products/types/{typeId}/{deletedById}");

        public Task<Result> AddDataSchemaToProductTypeAsync(string tenantId, AddDataSchemaToProductTypeCommand command) =>
            _client.GenericPostAsync<Result, AddDataSchemaToProductTypeCommand>($"{tenantId}/api/v1/products/types/dataSchemas", command);
        public Task<Result> RemoveDataSchemaFromProductTypeAsync(string tenantId, RemoveDataSchemaFromProductTypeCommand command) =>
            _client.GenericDeleteAsync<Result, RemoveDataSchemaFromProductTypeCommand>($"{tenantId}/api/v1/products/types/dataSchemas", command);

        public Task<Result> AddUnderwritingVariableAsync(string tenantId, ProductId productId, AddUnderwritingVariableCommand command) =>
            _client.GenericPostAsync<Result, AddUnderwritingVariableCommand>($"{tenantId}/api/v1/products/{productId.ToString()}/underwritingVariables/add", command);

        public Task<Result> UpdateUnderwritingVariableAsync(string tenantId, ProductId productId, UpdateUnderwritingVariableCommand command) =>
            _client.GenericPostAsync<Result, UpdateUnderwritingVariableCommand>($"{tenantId}/api/v1/products/{productId.ToString()}/underwritingVariables/update", command);

        public Task<Result> RemoveUnderwritingVariableAsync(string tenantId, ProductId productId, RemoveCommand command) =>
            _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/products/{productId.ToString()}/underwritingVariables/remove", command);

        public Task<IEnumerable<ProductConfig>> GetProductConfigsAsync(string tenantId, QueryArguments queryArguments) =>
            _client.GenericPostAsync<IEnumerable<ProductConfig>, QueryArguments>($"{tenantId}/api/v1/products/configs/query", queryArguments);

        public Task<Result<CreatedStatus>> CreateConfigAsync(string tenantId, CreateProductConfigCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreateProductConfigCommand>($"{tenantId}/api/v1/products/configs/create", command);

        public Task<Result> UpdateConfigAsync(string tenantId, string id, UpdateProductConfigCommand command) =>
            _client.GenericPostAsync<Result, UpdateProductConfigCommand>($"{tenantId}/api/v1/products/configs/{id}/update", command);

        public Task<Result> DeleteConfigAsync(string tenantId, string id, DeleteCommand command) =>
            _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/products/configs/{id}/delete", command);

        public Task<IEnumerable<Illustration>> GetIllustrationsAsync(string tenantId, ProductQuery productQuery) =>
            _client.GenericPostAsync<IEnumerable<Illustration>, ProductQuery>($"{tenantId}/api/v1/products/illustrations", productQuery);

        public Task<IEnumerable<ProductUnderwritingJsonLogicRules>> GetUnderwrittingJsonLogicRulesAsync(string tenantId, ProductWhere where) =>
           _client.GenericPostAsync<IEnumerable<ProductUnderwritingJsonLogicRules>, ProductWhere>($"{tenantId}/api/v1/products/underwritingJsonLogicRules/query", where);

        public Task<IEnumerable<ProductUnderwritingJsonSchema>> GetUnderwrittingJsonSchemasAsync(string tenantId, ProductUnderwritingJsonSchemaQuery query) =>
           _client.GenericPostAsync<IEnumerable<ProductUnderwritingJsonSchema>, ProductUnderwritingJsonSchemaQuery>($"{tenantId}/api/v1/products/underwritingJsonSchema/query", query);

        public Task<IEnumerable<BenefitDefinition>> GetBenefitDefinitionsAsync(string tenantId, QueryArguments queryArguments) =>
           _client.GenericPostAsync<IEnumerable<BenefitDefinition>, QueryArguments>($"{tenantId}/api/v1/benefitdefinitions/query", queryArguments);

        public Task<long> CountBenefitDefinitionsAsync(string tenantId, QueryArguments queryArguments) =>
            _client.GenericPostAsync<long, QueryArguments>($"{tenantId}/api/v1/benefitdefinitions/count", queryArguments);

        public Task<Result<CreatedStatus>> CreateBenefitDefinitionAsync(string tenantId, CreateBenefitDefinitionCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreateBenefitDefinitionCommand>($"{tenantId}/api/v1/benefitdefinitions", command);

        public Task<Result> UpdateBenefitDefinitionAsync(string tenantId, UpdateBenefitDefinitionCommand command) =>
           _client.GenericPutAsync<Result, UpdateBenefitDefinitionCommand>($"{tenantId}/api/v1/benefitdefinitions", command);

        public Task<Result> DeleteBenefitDefinitionAsync(string tenantId, string benefitDefinitionId, DeleteCommand command) =>
            _client.GenericDeleteAsync<Result, DeleteCommand>($"{tenantId}/api/v1/benefitdefinitions/{benefitDefinitionId}", command);
        
        public Task<Result> BatchBenefitDefinitionAsync(string tenantId, BatchBenefitDefinitionCommand command) =>
            _client.GenericPostAsync<Result, BatchBenefitDefinitionCommand>($"{tenantId}/api/v1/benefitdefinitions/batch", command);

        public Task<IEnumerable<BenefitDefinitionType>> GetBenefitDefinitionTypesAsync(string tenantId, QueryArguments queryArguments) =>
           _client.GenericPostAsync<IEnumerable<BenefitDefinitionType>, QueryArguments>($"{tenantId}/api/v1/benefitdefinitiontypes/query", queryArguments);

        public Task<Result<CreatedStatus>> CreateBenefitDefinitionTypeAsync(string tenantId, CreateBenefitDefinitionTypeCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreateBenefitDefinitionTypeCommand>($"{tenantId}/api/v1/benefitdefinitiontypes", command);

        public Task<Result> UpdateBenefitDefinitionTypeAsync(string tenantId, UpdateBenefitDefinitionTypeCommand command) =>
           _client.GenericPutAsync<Result, UpdateBenefitDefinitionTypeCommand>($"{tenantId}/api/v1/benefitdefinitiontypes", command);

        public Task<Result> DeleteBenefitDefinitionTypeAsync(string tenantId, string benefitDefinitionTypeId, DeleteCommand command) =>
            _client.GenericDeleteAsync<Result, DeleteCommand>($"{tenantId}/api/v1/benefitdefinitiontypes/{benefitDefinitionTypeId}", command);
        
        public Task<Result> BatchBenefitDefinitionTypeAsync(string tenantId, BatchBenefitDefinitionTypeCommand command) =>
            _client.GenericPostAsync<Result, BatchBenefitDefinitionTypeCommand>($"{tenantId}/api/v1/benefitdefinitiontypes/batch", command);

        public Task<Result<string>> Evaluate(string tenantId, EvaluateScriptCommand command) =>
            _client.GenericPostAsync<Result<string>, EvaluateScriptCommand>($"{tenantId}/api/v1/scriptevaluation", command);

        public Task<Result<string>> Evaluate(string tenantId, string clientId, EvaluateProductScriptCommand command) =>
            _client.GenericPostAsync<Result<string>, EvaluateProductScriptCommand>($"{tenantId}/api/v1/scriptevaluation/{clientId}", command);

        public Task<long> GetJacketsTotalCountAsync(string tenantId, JacketWhere where) =>
            _client.GenericPostAsync<long, JacketWhere>($"{tenantId}/api/v1/jackets/totalCount", where);
        public Task<IEnumerable<Jacket>> GetJacketsAsync(string tenantId, QueryArguments queryArguments) =>
            _client.GenericPostAsync<IEnumerable<Jacket>, QueryArguments>($"{tenantId}/api/v1/jackets/query", queryArguments);
        public Task<Result<string>> CreateJacketAsync(string tenantId, CreateJacketCommand command) =>
            _client.GenericPostAsync<Result<string>, CreateJacketCommand>($"{tenantId}/api/v1/jackets/create", command);
        public Task<Result> UpdateJacketAsync(string tenantId, string jacketId, UpdateJacketCommand command) =>
            _client.GenericPostAsync<Result, UpdateJacketCommand>($"{tenantId}/api/v1/jackets/update/{jacketId}", command);
        public Task<Result> DeleteJacketAsync(string tenantId, string jacketId, DeleteCommand command) =>
            _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/jackets/delete/{jacketId}", command);
        public Task<Result<CreatedStatus>> CreateUiSchemaAsync(string tenantId, CreateUiSchemaCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreateUiSchemaCommand>($"{tenantId}/api/v1/uiSchemas", command);
        public Task<IEnumerable<UiSchema>> GetUiSchemasAsync(string tenantId, UiSchemaWhere where) =>
            _client.GenericPostAsync<IEnumerable<UiSchema>, UiSchemaWhere>($"{tenantId}/api/v1/uiSchemas/query", where);
        public Task<Result> UpdateUiSchemaAsync(string tenantId, UpdateUiSchemaCommand command) =>
           _client.GenericPutAsync<Result, UpdateUiSchemaCommand>($"{tenantId}/api/v1/uiSchemas", command);
        public Task<Result> DeleteUiSchemaAsync(string tenantId, string uiSchemaId, DeleteCommand command) =>
            _client.GenericDeleteAsync<Result, DeleteCommand>($"{tenantId}/api/v1/uiSchemas/{uiSchemaId}", command);
    }
}
