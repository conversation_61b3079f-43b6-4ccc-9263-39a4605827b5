using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Auth;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace CoverGo.Gateway.Infrastructure
{
    public class CaptchaVerificationService : ICaptchaVerificationService
    {
        private readonly ILogger<CaptchaVerificationService> logger;
        
        public CaptchaVerificationService(ILogger<CaptchaVerificationService> logger)
        {
            this.logger = logger;
        }

        public async Task<Result> IsCaptchaValid(string tenantId, string clientId, string token, string remoteIp)
        {
            try
            {
                string secret = GetCaptchaSecretKey(tenantId);
                if (string.IsNullOrEmpty(secret)) 
                    return new Result { Status = "failure", Errors = new List<string> { $"{tenantId}: No captcha is setup for clientId: '{clientId}'." } };

                string dataAsString = $"secret={secret}&response={token}";
                if (remoteIp != null)
                    dataAsString += $"&remoteip={remoteIp}";
                
                using var httpClient = new HttpClient();
                var content = new StringContent(dataAsString);
                content.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");
                        
                HttpResponseMessage response = await httpClient.PostAsync("https://www.google.com/recaptcha/api/siteverify", content);
                if (!response.IsSuccessStatusCode)
                    return new Result { Status = "failure", Errors = new List<string>
                    {
                        "Failed to process captcha validation.",
                        $"{response.StatusCode}:{await response.Content.ReadAsStringAsync()}"
                    } };

                string responseString = await response.Content.ReadAsStringAsync();
                RecaptchaResponse recaptchaResponse = JsonConvert.DeserializeObject<RecaptchaResponse>(responseString);
                
                if (!recaptchaResponse.Success)
                {
                    List<string> errorCodesList = new() { "Failed to process captcha validation." };
                    if (recaptchaResponse.ErrorCodes != null) errorCodesList.AddRange(recaptchaResponse.ErrorCodes);
                    return Result.Failure(errorCodesList);
                } 
            }
            catch (Exception e)
            {
                logger.LogError("Failed to process captcha validation", e);
                return Result.Failure($"Failed to process captcha validation, exception message: {e.Message}");
            }

            return Result.Success();
        }
        
        private string GetCaptchaSecretKey(string tenantId)
        {
            string secret = null;

            switch (tenantId)
            {
                case "coverHealth_dev":
                    secret = "6LcurpIcAAAAANpmfk8TKByZSvNN9i03Fo9Mxelb";
                    break;
                case "msig_uat":
                    secret = "6LfZr5IcAAAAAFnUA61dX4donFAQCfWY9DcfRtC2";
                    break;
                case "msig_dev":
                    secret = "6Lfgr5IcAAAAAP1EPhFi3pkWElBChfuws9Grokub";
                    break;
                case "msig_qa":
                    secret = "6LdYIpYcAAAAAHfBFCGIgibEbOmWE3HZVblyzb4k";
                    break;
                case "msig_prod":
                    secret = "6LfCIpYcAAAAAHFDWnnJMz6O2i26lRTPAEzvKhhS";
                    break;
                case "chubbId":
                case "chubbId_uat":
                    secret = "6LfMpcIUAAAAAMU9AaGc43zxpNiLIHwXBcKQvqJn";
                    break;
                case "tahoe_uat":
                    secret = "6LcI0LAZAAAAAOaOSzHxwRoyQZGGlianngs0EAI9";
                    break;
                case "tahoe":
                    secret = "6LeBEdIZAAAAADZU-xVt2dNpad10h5bJhyCQzI9j";
                    break;
                case "tcb_uat":
                case "tcb":
                    secret = "6LelyvIbAAAAACSr8bAKg1lkhWJXZOX0HaxGcNR0";
                    break;
            }

            return secret;
        }
    }
}