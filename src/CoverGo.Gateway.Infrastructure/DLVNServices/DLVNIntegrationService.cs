﻿using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.DLVNServices
{
    public class DLVNIntegrationService
    {
        private readonly IEnumerable<DLVNCommand> _dlvnCommands;

        public DLVNIntegrationService(IEnumerable<DLVNCommand> dlvnCommands)
        {
            _dlvnCommands = dlvnCommands;
        }

        public async Task<Result<string>> ExecuteAsync(string tenantId, string commandType, JObject inputJson)
        {
            DLVNCommand command = _dlvnCommands?.FirstOrDefault(x => x.Name == commandType);

            if (command == null) return Result<string>.Failure($"No action for {commandType} found.");

            Result<string> result = await command.ExecuteAsync(tenantId, inputJson);

            return result;
        }
    }
}

