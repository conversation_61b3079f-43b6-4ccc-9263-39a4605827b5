using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.Notifications;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using OtpNet;

namespace CoverGo.Gateway.Infrastructure.DLVNServices.DLVNCommands
{
    public class DLVNTotpCommand : DLVNCommand
    {
        public override string Name => "totp";

        private const string SecretKey = "q%3#mz&Tm3#M5J^5#9NFMVaDDbKHP2aUEbQz";
        private const int Counter = 300;

        private readonly INotificationService _notificationService;
        private readonly IDistributedCache _cache;

        public DLVNTotpCommand(INotificationService notificationService,
            IFileSystemService fileSystemService,
            IDistributedCache cache,
            ILogger<DLVNTotpCommand> logger) : base(fileSystemService, logger)
        {
            _notificationService = notificationService;
            _cache = cache;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            string action = inputJson.Value<string>("action");

            switch (action)
            {
                case "GENERATE":
                    var ret = await Generate(tenantId, inputJson);

                    if (!ret.IsSuccess)
                    {
                        return Result<string>.Failure(ret.Errors);
                    }
                    return Result<string>.Success(string.Empty);
                case "VERIFY":
                    bool result = Verify(inputJson.Value<string>("otp"));
                    return result ? Result<string>.Success(string.Empty) : Result<string>.Failure("Invalid OTP");
                default:
                    return Result<string>.Failure("Invalid action");
            }
        }

        private async Task<Result> Generate(string tenantId, JObject inputJson)
        {
            DateTime today = DateTime.Now;
            var totp = new Totp(Encoding.ASCII.GetBytes(SecretKey), step: 60);

            var cachedOtp = await _cache.GetAsync(GetCacheKey());
            if (cachedOtp != null)
            {
                return Result.Failure($"Previous OTP will expire in {totp.RemainingSeconds()} seconds. You may try again after that.");
            }

            var otp = totp.ComputeTotp();
            await _cache.SetAsync(GetCacheKey(), System.Text.Encoding.ASCII.GetBytes(otp), new DistributedCacheEntryOptions() { AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(60) }, System.Threading.CancellationToken.None);
            _logger.LogInformation($"[DLVNTotpCommand] GENERATE Current DateTime:{DateTime.UtcNow}");
            _logger.LogInformation($"[DLVNTotpCommand] Generate OTP:{otp}, remaining time: {totp.RemainingSeconds()}");
            var body = inputJson.Value<string>("content")?.Replace("[[otp]]", otp) ?? otp;
            TemplateRendering templateRendering = !string.IsNullOrEmpty(inputJson.Value<string>("templateId")) ? new()
            {
                TemplateId = inputJson.Value<string>("templateId"),
                Input = new()
                {
                    Name = "data",
                    Content = JObject.FromObject(new
                    {
                        otpNumber = otp
                    })
                }
            } : null;

            await UploadFileToFileSystem(
                tenantId,
                inputJson.Value<string>("bucketName"),
                $"systemLog/otp/{today.ToString("yyyyMMdd")}/{today.ToString("HHmmss")}-customer-{inputJson.Value<string>("loginIdFromToken")}.txt",
                System.Text.Encoding.ASCII.GetBytes(body));

            Result notificationResult;
            if (!string.IsNullOrEmpty(inputJson.Value<string>("phoneNumber")))
            {
                notificationResult = await _notificationService.SendAsync(tenantId,
                    new SendNotificationCommand
                    {
                        SmsMessage = new SmsMessage
                        {
                            From = inputJson.Value<string>("from"),
                            To = inputJson.Value<string>("phoneNumber"),
                            Body = templateRendering == null ? body : null,
                            TemplateRendering = templateRendering
                        },
                        UseConfig = true
                    }
                );
            }
            else if (!string.IsNullOrEmpty(inputJson.Value<string>("email")))
            {
                notificationResult = await _notificationService.SendAsync(tenantId,
                    new SendNotificationCommand
                    {
                        EmailMessage = new()
                        {
                            To = inputJson.Value<string>("email"),
                            Subject = templateRendering == null ? (inputJson.Value<string>("emailSubject") ?? "Your OTP") : null,
                            HtmlContent = templateRendering == null ? body : null,
                            TemplateRendering = templateRendering
                        },
                        UseConfig = true
                    }
                );
            }
            else
            {
                return Result.Failure("Missing notification destination");
            }

            if (!notificationResult.IsSuccess)
            {
                _logger.LogError($"[DLVNTotpCommand] Failed to send notification | {string.Join(", ", notificationResult.Errors)} ");
            }

            return notificationResult;

            string GetCacheKey() => $"otp-{inputJson.Value<string>("loginIdFromToken")}";
        }

        private bool Verify(string otp)
        {
            _logger.LogInformation($"[DLVNTotpCommand] Verify OTP:{otp}");
            _logger.LogInformation($"[DLVNTotpCommand] Verify OTP Current DateTime:{DateTime.UtcNow}");
            var totp = new Totp(Encoding.ASCII.GetBytes(SecretKey), step: 60);
            _logger.LogInformation($"[DLVNTotpCommand] Verify OTP:{otp}, remaining time: {totp.RemainingSeconds()}");
            var verifyResult = totp.VerifyTotp(otp, out long timeStepMatched, new VerificationWindow(1, 1));
            _logger.LogInformation($"[DLVNTotpCommand] Verify OTP result:{verifyResult} , timeStepMatched:{timeStepMatched}");

            return verifyResult;

        }
    }
}

