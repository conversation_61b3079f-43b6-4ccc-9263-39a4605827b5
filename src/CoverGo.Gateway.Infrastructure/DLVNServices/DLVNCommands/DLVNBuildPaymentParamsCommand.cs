using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Policies.PricingCache;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Infrastructure.Policies;
using CoverGo.Users.Domain.Individuals;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OtpNet;

namespace CoverGo.Gateway.Infrastructure.DLVNServices.DLVNCommands
{
    public class DLVNBuildPaymentParamsCommand : DLVNCommand
    {
        public override string Name => "buildPaymentParams";
        private readonly IPolicyService _policyService;
        private readonly CoverGoPolicyPricingCachesService _policyPricingCachesService;
        private readonly IProductService _productService;
        private readonly IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> _entityService;

        public DLVNBuildPaymentParamsCommand(IPolicyService policyService,
            CoverGoPolicyPricingCachesService policyPricingCachesService,
            IProductService productService,
            IFileSystemService fileSystemService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> entityService,
            ILogger<DLVNBuildPaymentParamsCommand> logger) : base(fileSystemService, logger)
        {
            _policyService = policyService;
            _policyPricingCachesService = policyPricingCachesService;
            _productService = productService;
            _entityService = entityService;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            string policyNumber = inputJson.Value<string>("policyNumber");

            if(IsOtpValidationEnabled() && !ValidOtp())
                return Result<string>.Failure("Invalid OTP");

            Policy policy = (await _policyService.GetAsync(tenantId, new PolicyWhere
            {
                IssuerNumber_contains = policyNumber
            })).FirstOrDefault();

            if (policy == null)
            {
                _logger.LogError($"[DLVNBuildPaymentParamsCommand] Cannot find policy number {policyNumber}");
                return Result<string>.Failure($"Cannot find policy number {policyNumber}");
            }

            Product2 product = (await _productService.GetAsync(tenantId, policy.ClientId, new ProductQuery
            {
                Where = new ProductWhere
                {
                    ProductId = new ProductIdWhere
                    {
                        Plan = policy.ProductId.Plan,
                        Version = policy.ProductId.Version,
                        Type = policy.ProductId.Type
                    }
                }
            })).FirstOrDefault();

            string paymentEncryptionMethod = GetProductFactsValue<string>(product?.Facts?.FirstOrDefault(x => x.Type == "paymentEncryptionMethod")?.Value) ?? string.Empty;

            string output;
            switch (paymentEncryptionMethod)
            {
                case "Sacombank":
                    var inputValidationResult = DLVNHelper.Validate(inputJson);

                    if (!inputValidationResult.IsSuccess)
                        return inputValidationResult;

                    DLVNBuildScbPaymentParamsRequest scbData = await BuildSacombankGatewayRequest(tenantId, inputJson, policy);

                    if (scbData == null)
                    {
                        _logger.LogError($"[DLVNBuildPaymentParamsCommand] Error populating request SCB data");
                        return Result<string>.Failure("Error populating request data");
                    }

                    var base64String = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(scbData)));
                    var secretKey = GetProductFactsValue<string>(product?.Facts?.FirstOrDefault(x => x.Type == "paymentEncryptionSecretKey")?.Value) ?? GetSacombankSecretKey(tenantId) ?? string.Empty;

                    output = JsonConvert.SerializeObject(new { data = base64String, checksum = GenerateCheckSum($"{base64String}{secretKey}") });
                    await CreateTxtAndUploadToFileSystemAsync(tenantId, inputJson.Value<string>("bucketName"), policyNumber, JsonConvert.SerializeObject(new { data = scbData, secretKey, requestBody = output }));

                    break;

                default:
                    DLVNBuildPaymentParamsRequest data = await BuildDefaultRequest(tenantId, inputJson, policy);

                    if (data == null)
                    {
                        _logger.LogError($"[DLVNBuildPaymentParamsCommand] Error populating request data");
                        return Result<string>.Failure("Error populating request data");
                    }

                    output = Encrypt(tenantId, JsonConvert.SerializeObject(data));
                    await CreateTxtAndUploadToFileSystemAsync(tenantId, inputJson.Value<string>("bucketName"), policyNumber, JsonConvert.SerializeObject(new { data, encryptedText = output }));

                    break;
            }

            return Result<string>.Success(output);

            bool IsOtpValidationEnabled() => Environment.GetEnvironmentVariable("DLVN_ENABLE_VALIDATE_PAYMENT_OTP") == "true";

            bool ValidOtp()
            {
                string otp = inputJson.Value<string>("otp");

                bool isValidOtp = string.IsNullOrEmpty(otp) ? false : VerifyOtp(otp);
                return isValidOtp;
            }
        }

        private bool VerifyOtp(string otp)
        {
            _logger.LogInformation("[DLVNBuildPaymentParamsCommand] Verify OTP:{otp}",otp);
            _logger.LogInformation("[DLVNBuildPaymentParamsCommand] Verify OTP Current DateTime:{currentDateTime}", DateTime.UtcNow);
            var totp = new Totp(Encoding.ASCII.GetBytes("q%3#mz&Tm3#M5J^5#9NFMVaDDbKHP2aUEbQz"), step: 60);
            _logger.LogInformation("[DLVNBuildPaymentParamsCommand] Verify OTP:{otp}, remaining time: {remainingSeconds}",otp, totp.RemainingSeconds());
            var verifyResult = totp.VerifyTotp(otp, out long timeStepMatched, new VerificationWindow(1, 1));
            _logger.LogInformation("[DLVNBuildPaymentParamsCommand] Verify OTP result:{result} , timeStepMatched:{timeStepMatched}", verifyResult,timeStepMatched);

            return verifyResult;

        }

        private async Task<DLVNBuildScbPaymentParamsRequest> BuildSacombankGatewayRequest(string tenantId, JObject inputJson, Policy policy)
        {
            var individual = (await _entityService.GetAsync(tenantId, new EntityWhere { Id = policy.ContractHolder?.Id }))?.FirstOrDefault();

            if (individual == null)
            {
                _logger.LogError($"[DLVNBuildPaymentParamsCommand] Error populating contract holder data");
                return null;
            }
            var pricingDataJToken = await CalculatePricing(tenantId, policy);

            var netPrice = await CalculateNetAmount(tenantId, pricingDataJToken) ?? 0;
            string selectedPlanId = policy.Fields.Value<JArray>("insureds")?.Children().FirstOrDefault()?.Value<string>("selectedPlanId");
            return new DLVNBuildScbPaymentParamsRequest
            {
                TransactionID = inputJson.Value<string>("transactionId"),
                TransactionDatetime = DateTime.UtcNow.ToString("yyyyMMddHHmmss"),
                AdditionalData = JsonConvert.SerializeObject(new {
                    Name = DLVNHelper.GetPlanName(selectedPlanId, pricingDataJToken),
                    Quantity = 1,
                    Amount = policy.Fields.Value<JArray>("insureds")?.Children().FirstOrDefault()?.Value<long>("sumAssured"),
                    Currency = "VND"
                }),
                AdditionalInformation = JsonConvert.SerializeObject(new {
                    ProductName = policy.ProductId?.Plan,
                    ProgramName = DLVNHelper.GetPlanName(selectedPlanId, pricingDataJToken),
                    InsurancePolicyTerm = policy.Fields.Value<JArray>("insureds")?.Children().FirstOrDefault()?.Value<string>("policyTerm"),
                    PremiumPaymentFrequency = policy.Fields.Value<JArray>("insureds")?.Children().FirstOrDefault()?.Value<string>("paymentFrequency"),
                    CodeAgentServeContract = inputJson.Value<string>("codeAgentServeContract"),
                    BranchCode_CustomerOpening = inputJson.Value<string>("branchCode_CustomerOpening"),
                    IntroduceCodeAgent = inputJson.Value<string>("introduceCodeAgent"),
                    BranchCode_AgentCode = inputJson.Value<string>("branchCode_AgentCode"),
                    MobileIntroduceCodeAgent = inputJson.Value<string>("mobileIntroduceCodeAgent")
                }),
                SubTotalAmount = decimal.ToInt64(netPrice),
                PaymentAmount = decimal.ToInt64(netPrice - await CalculateDiscount(tenantId, pricingDataJToken)),
                Currency = "VND",
                DeliveryFee = 0,
                Discount = 0,
                TaxAmount = 0,
                OrderId = inputJson.Value<string>("orderId"),
                PartnerOrderID = policy.Id,
                ProductCatL1 = "",
                ProductCatL2 = "",
                ProductCatL3 = "",
            };
        }

        private async Task<DLVNBuildPaymentParamsRequest> BuildDefaultRequest(string tenantId, JObject inputJson, Policy policy)
        {
            var individual = (await _entityService.GetAsync(tenantId, new EntityWhere { Id = policy.ContractHolder?.Id }))?.FirstOrDefault();

            if (individual == null)
            {
                _logger.LogError($"[DLVNBuildPaymentParamsCommand] Error populating contract holder data");
                return null;
            }

            return new DLVNBuildPaymentParamsRequest
            {
                Customer = await BuildCustomerRequest(tenantId, inputJson, policy, individual)
            };
        }

        private async Task<JToken> CalculatePricing(string tenantId, Policy policy)
        {
            string pricing = string.Empty;

            if (policy.IsPricingCacheAvailable)
            {
                List<PolicyPricingCache> caches = await _policyPricingCachesService.Query(tenantId, new QueryArguments<Filter<PolicyPricingCacheFilter>>
                {
                    Where = new Filter<PolicyPricingCacheFilter>
                    {
                        Where = new PolicyPricingCacheFilter
                        {
                            Id = policy.Id
                        }
                    }
                }, System.Threading.CancellationToken.None);

                if (caches.Any())
                    pricing = caches.FirstOrDefault()?.PricingCache;
            }

            if (string.IsNullOrEmpty(pricing))
            {
                Result<string> result = await _productService.Evaluate(tenantId, policy.ClientId, new EvaluateProductScriptCommand
                {
                    DataInput = policy.Fields?.ToString(),
                    ProductId = policy.ProductId,
                    ScriptType = ScriptTypeEnum.Pricing
                });

                if (result.Status != "success")
                {
                    _logger.LogError($"[DLVNBuildPaymentParamsCommand] Error evaluate script | {string.Join(", ", result.Errors)} ");
                    return null;
                }

                pricing = result.Value;
            }

            _logger.LogInformation($"[DLVNBuildPaymentParamsCommand] Calculate pricing for policy {policy.IssuerNumber} | {pricing}");

            if (string.IsNullOrEmpty(pricing))
                return null;

            var pricingJToken = JToken.Parse(pricing.Replace("\n", " "));
            var pricingDataJToken = pricingJToken?.Value<JToken>("data");

            return pricingDataJToken;
        }

        private async Task<decimal?> CalculateNetAmount(string tenantId, Policy policy)
        {

            JToken pricingDataJToken = await CalculatePricing(tenantId, policy);

            return await CalculateNetAmount(tenantId,pricingDataJToken);
        }

        private async Task<decimal?> CalculateNetAmount(string tenantId, JToken pricingDataJToken)
        {

            var aggregatePerContractJToken = pricingDataJToken?.Value<JToken>("aggregatePerContract");
            decimal? netPrice = aggregatePerContractJToken?.Value<decimal>("netPrice");

            return netPrice;
        }

        private async Task<decimal> CalculateDiscount(string tenantId, JToken pricingDataJToken)
        {


            var contractInfoJToken = pricingDataJToken?.Value<JToken>("contractInfo");
            decimal discountedAmount = contractInfoJToken?.Value<decimal>("discountedAmount") ?? 0;

            return discountedAmount;
        }

        private async Task<DLVNBuildPaymentParamsCustomerRequest> BuildCustomerRequest(string tenantId, JObject inputJson, Policy policy, Individual individual)
        {
            return new DLVNBuildPaymentParamsCustomerRequest
            {
                ClientID = "M",
                PolicyNo = policy.IssuerNumber,
                PayerName = GetFieldValue<string>(individual.Fields, "name"),
                Phone = GetFieldValue<string>(individual.Fields, "phoneNumber") ?? individual.Contacts?.FirstOrDefault(x => x.Type == "telephoneNumber")?.Value,
                Email = GetFieldValue<string>(individual.Fields, "email") ?? individual.Contacts?.FirstOrDefault(x => x.Type == "email")?.Value,
                PaymentType = inputJson.Value<string>("paymentType"),
                Amount = (await CalculateNetAmount(tenantId, policy))?.ToString(),
                APL = inputJson.Value<string>("apl") ?? "0",
                OPL = inputJson.Value<string>("opl") ?? "0",
                FromApp = inputJson.Value<string>("fromApp")
            };
        }

        private T GetFieldValue<T>(JToken fields, string type) =>
            fields != null
                ? fields.Value<T>(type) ?? default
                : default;

        private T GetFieldValue<T>(JToken fields, string type, string subType)
        {
            JToken typeValue = GetFieldValue<JToken>(fields, type);

            return GetFieldValue<T>(typeValue, subType);
        }

        private async Task CreateTxtAndUploadToFileSystemAsync(string tenantId, string bucketName, string policyNumber, string textOutput)
        {
            DateTime today = DateTime.Now;
            string filePath = $"systemLog/requestPayment/{today.ToString("yyyyMMdd")}/{policyNumber}-{today.ToString("HHmmss")}.txt";

            Result uploadResult = await UploadFileToFileSystem(
                tenantId,
                bucketName,
                filePath,
                Encoding.ASCII.GetBytes(textOutput));

            if (!uploadResult.IsSuccess)
            {
                _logger.LogError($"[DLVNBuildPaymentParamsCommand]Upload file failed | {string.Join(", ", uploadResult.Errors)}");
            }
        }

        private string Encrypt(string tenantId, string plainText)
        {
            try
            {
                string encryptionKey = GetEncryptionKey(tenantId);
                byte[] cipherData;

                Aes aes = Aes.Create();
                aes.Key = Convert.FromBase64String(encryptionKey);
                aes.GenerateIV();
                aes.Mode = CipherMode.CBC;
                ICryptoTransform cipher = aes.CreateEncryptor(aes.Key, aes.IV);
                using (MemoryStream ms = new())
                {
                    using (CryptoStream cs = new(ms, cipher, CryptoStreamMode.Write))
                    {
                        using StreamWriter sw = new(cs);
                        sw.Write(plainText);
                    }
                    cipherData = ms.ToArray();
                }
                byte[] combinedData = new byte[aes.IV.Length + cipherData.Length];
                Array.Copy(aes.IV, 0, combinedData, 0, aes.IV.Length);
                Array.Copy(cipherData, 0, combinedData, aes.IV.Length, cipherData.Length);
                return Convert.ToBase64String(combinedData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DLVNBuildPaymentParamsCommand] Encrypt Exception");
                return "";
            }
        }

        private string GenerateCheckSum(string input)
        {
            var output = SHA256.HashData(Encoding.UTF8.GetBytes(input));
            string hash = BitConverter.ToString(output).Replace("-", string.Empty);

            return hash;
        }

        private string GetSacombankSecretKey(string tenant)
            => tenant switch
            {
                "dlvn" => System.Environment.GetEnvironmentVariable("DLVN_SCB_PAYMENT_GATEWAY_SECRET_KEY"),
                _ => System.Environment.GetEnvironmentVariable("DLVN_UAT_SCB_PAYMENT_GATEWAY_SECRET_KEY")
            };

        private string GetEncryptionKey(string tenant)
            => tenant switch
            {
                "dlvn" => System.Environment.GetEnvironmentVariable("DLVN_API_ENCRYPTION_KEY"),
                _ => System.Environment.GetEnvironmentVariable("DLVN_UAT_API_ENCRYPTION_KEY") ?? "YWVzU09QZGx2bk9ubGluZVBheW1lbnRQcml2YXRlMDE="
            };

        private class DLVNBuildPaymentParamsRequest
        {
            public DLVNBuildPaymentParamsCustomerRequest Customer { get; set; }
        }

        private class DLVNBuildScbPaymentParamsRequest
        {
            public string TransactionID { get; set; }
            public string TransactionDatetime { get; set; }
            public string AdditionalData { get; set; }
            public string AdditionalInformation { get; set; }
            public Int64 SubTotalAmount { get; set; }
            public string Currency { get; set; }
            public Int64 DeliveryFee { get; set; }
            public Int64 Discount { get; set; }
            public Int64 TaxAmount { get; set; }
            public Int64 PaymentAmount { get; set; }
            public string OrderId { get; set; }
            public string PartnerOrderID { get; set; }
            public string ProductCatL1 { get; set; }
            public string ProductCatL2 { get; set; }
            public string ProductCatL3 { get; set; }
        }

        private class DLVNBuildPaymentParamsCustomerRequest
        {
            public string ClientID { get; set; } = "";
            public string PolicyNo { get; set; } = "";
            public string PayerName { get; set; } = "";
            public string Phone { get; set; } = "";
            public string Email { get; set; } = null;
            public string PaymentType { get; set; } = "";
            public string Amount { get; set; } = "";
            public string APL { get; set; } = "";
            public string OPL { get; set; } = "";
            public string FromApp { get; set; } = "";
        }
    }
}

