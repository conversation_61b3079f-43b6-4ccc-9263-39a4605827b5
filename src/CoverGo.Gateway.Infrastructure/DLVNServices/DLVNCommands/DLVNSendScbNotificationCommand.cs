using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.FileSystem;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.OpenSsl;
using Org.BouncyCastle.Security;
using RestSharp;

namespace CoverGo.Gateway.Infrastructure.DLVNServices.DLVNCommands
{
    public class DLVNSendScbNotificationCommand : DLVNCommand
    {
        public override string Name => "sendScbNotificationCommand";

        public DLVNSendScbNotificationCommand(
            IFileSystemService fileSystemService,
            ILogger<DLVNSendScbNotificationCommand> logger) : base(fileSystemService, logger)
        {
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            (DLVNSendScbNotificationRequest requestBody, DLVNSendScbNotificationDataRequest data) = BuildRequest(tenantId, inputJson);

            if (requestBody == null)
            {
                _logger.LogError($"[DLVNSendScbNotificationCommand] Error populating request data");
                return Result<string>.Failure("Error populating request data");
            }

            var md5 = CreateMD5(JsonConvert.SerializeObject(requestBody));
            string authorization = $"Basic {Convert.ToBase64String(Encoding.UTF8.GetBytes(GetScbCredentials(tenantId)))}";
            string signature = GenerateSignature(GetScbPrivateKey(tenantId), md5);

            await CreateTxtAndUploadToFileSystemAsync(tenantId, inputJson.Value<string>("bucketName"), requestBody.RequestID, JsonConvert.SerializeObject(new { requestBody, dataPreTripleDes = data, md5, authorization, signature }));

            Result<DLVNSendScbNotificationResponse> result = await RequestSendNotification(tenantId, authorization, signature, requestBody);

            if (!result.IsSuccess)
            {
                return Result<string>.Failure(result.Errors);
            }

            if (!string.IsNullOrEmpty(result.Value?.Data))
            {
                result.Value.DecryptedData = TrippleDESDecrypt(GetSacombankSecretKey(tenantId), result.Value.Data) ?? result.Value.Data;
            }

            return Result<string>.Success(JsonConvert.SerializeObject(result.Value));
        }

        private (DLVNSendScbNotificationRequest, DLVNSendScbNotificationDataRequest) BuildRequest(string tenantId, JObject inputJson)
        {
            string requestId = inputJson.Value<string>("requestId") ?? Guid.NewGuid().ToString();
            var data = new DLVNSendScbNotificationDataRequest
            {
                RefNumber = inputJson.Value<string>("refNumber") ?? GenerateRefNumber(),
                NotiType = inputJson.Value<string>("notiType"),
                OrderId = inputJson.Value<string>("orderId"),
                Mobile = inputJson.Value<string>("mobile"),
                FullName = inputJson.Value<string>("fullName"),
                Gender = inputJson.Value<string>("gender"),
                DOB = inputJson.Value<string>("dob"),
                Email = inputJson.Value<string>("email"),
                SSN = inputJson.Value<string>("ssn"),
                IdType = inputJson.Value<string>("idType")
            };

            return (new DLVNSendScbNotificationRequest
            {
                FunctionName = inputJson.Value<string>("functionName") ?? "ILPNotification",
                RequestDateTime = (inputJson.Value<DateTime?>("requestDateTime") ?? DateTime.UtcNow).ToString("yyyy-MM-ddTHH:mm:ssZ"),
                RequestID = requestId,
                Data = TrippleDESEncrypt(GetSacombankSecretKey(tenantId), JsonConvert.SerializeObject(data))//Encrypt3DS(tenantId, requestId, JsonConvert.SerializeObject(data))
            }, data);
        }

        private async Task<Result<DLVNSendScbNotificationResponse>> RequestSendNotification(string tenantId, string authoriation, string signature, DLVNSendScbNotificationRequest data)
        {
            var request = new RestRequest(Method.POST)
            {
                RequestFormat = DataFormat.Json
            };

            request.AddHeader("Authorization", authoriation);
            request.AddHeader("Signature", signature);
            request.AddJsonBody(data);

            return await ExecuteAsync<DLVNSendScbNotificationResponse>($"{GetScbIntegrationEndpoint(tenantId)}", request);
        }

        private async Task CreateTxtAndUploadToFileSystemAsync(string tenantId, string bucketName, string requestId, string data)
        {
            DateTime today = DateTime.Now;

            string filePath = $"systemLog/sendScbNotification/{today.ToString("yyyyMMdd")}/{requestId}-{today.ToString("HHmmss")}.txt";

            Result uploadResult = await UploadFileToFileSystem(
                tenantId,
                bucketName,
                filePath,
                Encoding.UTF8.GetBytes(data));

            if (!uploadResult.IsSuccess)
            {
                _logger.LogError($"[DLVNSubmitApplicationCommand]Upload file failed | {string.Join(", ", uploadResult.Errors)}");
            }
        }

        public string TrippleDESEncrypt(string key, string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                _logger.LogError("[DLVNSendScbNotificationCommand] Error encrypting 3DS, empty string");
                return string.Empty;
            }

            if (string.IsNullOrEmpty(key))
            {
                _logger.LogError("[DLVNSendScbNotificationCommand] Error encrypting 3DS, empty key");
                return string.Empty;
            }

            TripleDESCryptoServiceProvider cipher = new TripleDESCryptoServiceProvider
            {
                Key = Encoding.UTF8.GetBytes(key),
                Mode = CipherMode.ECB,
                Padding = PaddingMode.PKCS7
            };
            byte[] data = Encoding.UTF8.GetBytes(input);
            return Convert.ToBase64String(cipher.CreateEncryptor().TransformFinalBlock(data, 0, data.Length));
        }

        public string TrippleDESDecrypt(string key, string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                _logger.LogError("[DLVNSendScbNotificationCommand] Error decrypting 3DS, empty string");
                return string.Empty;
            }

            if (string.IsNullOrEmpty(key))
            {
                _logger.LogError("[DLVNSendScbNotificationCommand] Error decrypting 3DS, empty key");
                return string.Empty;
            }

            TripleDESCryptoServiceProvider cipher = new TripleDESCryptoServiceProvider
            {
                Key = Encoding.UTF8.GetBytes(key),
                Mode = CipherMode.ECB,
                Padding = PaddingMode.PKCS7
            };
            byte[] data = Convert.FromBase64String(input);
            return System.Text.Encoding.UTF8.GetString(cipher.CreateDecryptor().TransformFinalBlock(data, 0, data.Length));
        }

        public string GenerateSignature(string privateKey, string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                _logger.LogError("[DLVNSendScbNotificationCommand] Error generate signature, empty string");
                return string.Empty;
            }

            if (string.IsNullOrEmpty(privateKey))
            {
                _logger.LogError("[DLVNSendScbNotificationCommand] Error generate signature, empty private key");
                return string.Empty;
            }

            try
            {
                byte[] data = Encoding.ASCII.GetBytes(input);
                AsymmetricCipherKeyPair keyPair = (AsymmetricCipherKeyPair)
                    new PemReader(new StringReader(privateKey)).ReadObject();

                ISigner signer = SignerUtilities.GetSigner("SHA1withRSA");
                signer.Init(true, keyPair.Private);
                signer.BlockUpdate(data, 0, data.Length);
                byte[] output = signer.GenerateSignature();
                return Convert.ToBase64String(output);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DLVNSendScbNotificationCommand] Error generate signature");
                return string.Empty;
            }

        }

        private string CreateMD5(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                _logger.LogError("[DLVNSendScbNotificationCommand] Error hashing MD5, empty string");
                return string.Empty;
            }

            try
            {
                MD5 md5 = MD5.Create();
                byte[] hash = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
                StringBuilder buffer = new StringBuilder();
                for (int i = 0; i < hash.Length; i++)
                {
                    buffer.Append(hash[i].ToString("X2"));
                }
                return buffer.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DLVNSendScbNotificationCommand] Error hashing MD5");
                return string.Empty;
            }
        }

        private string GetScbCredentials(string tenant)
            => tenant switch
            {
                "dlvn" => System.Environment.GetEnvironmentVariable("DLVN_SCB_CREDENTIALS") ?? "08729c27-4adc-4ea1-868b-5798a00b3aa7:bb6422cb5c00b402",
                _ => System.Environment.GetEnvironmentVariable("DLVN_UAT_SCB_CREDENTIALS")
            };

        private string GetScbIntegrationEndpoint(string tenant)
            => tenant switch
            {
                "dlvn" => System.Environment.GetEnvironmentVariable("DLVN_SCB_INTEGRATION_ENDPOINT") ?? "https://partner.sacombank.com.vn/epaygw/stb/",
                _ => System.Environment.GetEnvironmentVariable("DLVN_UAT_SCB_INTEGRATION_ENDPOINT")
            };

        private string GetSacombankSecretKey(string tenant)
            => tenant switch
            {
                "dlvn" => System.Environment.GetEnvironmentVariable("DLVN_SCB_SECRET_KEY") ?? "b7773847251c131a",
                _ => System.Environment.GetEnvironmentVariable("DLVN_UAT_SCB_SECRET_KEY")
            };

        private string GetScbPrivateKey(string tenant)
            => tenant switch
            {
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                _ => System.Environment.GetEnvironmentVariable("DLVN_UAT_SCB_INTEGRATION_PRIVATE_KEY")
            };

        private string GenerateRefNumber()
        {
            DateTime now = DateTime.UtcNow;
            return $"DL{now.Year%10}{now.DayOfYear.ToString("D3")}{AlphaNumericStringGenerator.GetRandomNumericValue(6)}";
        }

        private class DLVNSendScbNotificationRequest
        {
            public string Data { get; set; }
            public string FunctionName { get; set; }
            public string RequestDateTime { get; set; }
            public string RequestID { get; set; }
        }

        private class DLVNSendScbNotificationDataRequest
        {
            public string RefNumber { get; set; }
            public string NotiType { get; set; }
            public string OrderId { get; set; }
            public string Mobile { get; set; }
            public string FullName { get; set; }
            public string Gender { get; set; }
            public string DOB { get; set; }
            public string Email { get; set; }
            public string SSN { get; set; }
            public string IdType { get; set; }
        }

        private class DLVNSendScbNotificationResponse
        {
            public string FunctionName { get; set; }
            public string RequestDateTime { get; set; }
            public string RequestID { get; set; }
            public string Description { get; set; }
            public string RespCode { get; set; }
            public string Data { get; set; }
            public string DecryptedData { get; set; }

        }
    }
}

