﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Policies.PricingCache;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Gateway.Infrastructure.Policies;
using CoverGo.Users.Domain.Individuals;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
namespace CoverGo.Gateway.Infrastructure.DLVNServices.DLVNCommands
{
    public class DLVNSubmitApplicationCommand : DLVNCommand
    {
        public override string Name => "submitApplication";
        private readonly IPolicyService _policyService;
        private readonly CoverGoPolicyPricingCachesService _policyPricingCachesService;
        private readonly IProductService _productService;
        private readonly IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> _entityService;

        public DLVNSubmitApplicationCommand(
            IPolicyService policyService,
            CoverGoPolicyPricingCachesService policyPricingCachesService,
            IProductService productService,
            IFileSystemService fileSystemService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> entityService,
            ILogger<DLVNSubmitApplicationCommand> logger) : base(fileSystemService, logger)
        {
            _policyService = policyService;
            _policyPricingCachesService = policyPricingCachesService;
            _productService = productService;
            _entityService = entityService;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            string policyNumber = inputJson.Value<string>("policyNumber");
            Policy policy = (await _policyService.GetAsync(tenantId, new PolicyWhere
            {
                IssuerNumber_contains = policyNumber
            })).FirstOrDefault();

            if (policy == null)
            {
                _logger.LogError($"[DLVNSubmitApplicationCommand] Cannot find policy number {policyNumber}");
                return Result<string>.Failure($"Cannot find policy number {policyNumber}");
            }

            if (GetFieldValue<string>(policy.Fields, "paymentStatus") != "PAID")
            {
                _logger.LogError($"[DLVNSubmitApplicationCommand] Invalid payment status");
                return Result<string>.Failure($"Cannot submit policy {policyNumber} with payment status {GetFieldValue<string>(policy.Fields, "paymentStatus")}");
            }

            if (!string.IsNullOrEmpty(GetFieldValue<string>(policy.Fields, "submitErrorMessage")))
            {
                _logger.LogError($"[DLVNSubmitApplicationCommand] Policy already submitted with error");
                return Result<string>.Failure($"Policy {policyNumber} already submitted before with error: {GetFieldValue<string>(policy.Fields, "submitErrorMessage")}");
            }

            if (!string.IsNullOrEmpty(GetFieldValue<string>(policy.Fields, "submitApplicationSuccessfullyAt")))
            {
                _logger.LogError($"[DLVNSubmitApplicationCommand] Policy already submitted");
                return Result<string>.Failure($"Policy {policyNumber} already submitted before at {GetFieldValue<string>(policy.Fields, "submitApplicationSuccessfullyAt")}");
            }

            Product2 product = (await _productService.GetAsync(tenantId, policy.ClientId, new ProductQuery
            {
                Where = new ProductWhere
                {
                    ProductId = new ProductIdWhere
                    {
                        Plan = policy.ProductId.Plan,
                        Version = policy.ProductId.Version,
                        Type = policy.ProductId.Type
                    }
                }
            })).FirstOrDefault();

            if (policy == null)
            {
                _logger.LogError($"[DLVNSubmitApplicationCommand] Cannot find product of {policyNumber}");
                return Result<string>.Failure($"Cannot find product");
            }

            DLVNSubmitApplicationRequest data = await BuildRequest(tenantId, inputJson, policy);

            if (data == null)
            {
                _logger.LogError($"[DLVNSubmitApplicationCommand] Error populating request data");
                return Result<string>.Failure("Error populating request data");
            }

            var token = await GetToken(tenantId, inputJson, product);

            data.token = token.Value;

            await CreateTxtAndUploadToFileSystemAsync(tenantId, inputJson.Value<string>("bucketName"), data);

            Result<DLVNClientVerifyResponse> result = await RequestSubmitApplication(tenantId, data, product);

            if (result.IsSuccess || result.Errors?.Any() == true)
            {
                var updatePolicyFieldResult = await _policyService.UpdatePolicy2Async(tenantId, policy.Id, new UpdatePolicyCommand
                {
                    FieldsPatch = result.IsSuccess
                    ? $"[{{\"op\":\"add\",\"path\":\"/submitApplicationSuccessfullyAt\",\"value\":\"{DateTime.UtcNow}\"}}]"
                    : $"[{{\"op\":\"add\",\"path\":\"/submitErrorMessage\",\"value\":\"{string.Join(", ", result.Errors)}\"}}]"
                });

                if (!updatePolicyFieldResult.IsSuccess)
                {
                    _logger.LogError($"[DLVNSubmitApplicationJob] Error updating policy field {policy.Id} | {string.Join(", ", updatePolicyFieldResult.Errors)} ");
                }
            }

            if (!result.IsSuccess)
            {
                return Result<string>.Failure(result.Errors);
            }

            return Result<string>.Success(result.Value != null ? JsonConvert.SerializeObject(result.Value) : "");
        }

        private async Task<Result<string>> GetToken(string tenantId, JObject inputJson, Product2 product)
        {
            Result<TokenResponse> tokenInfo = await GetToken(tenantId, inputJson.Value<string>("bucketName"), product);

            if (!tokenInfo.IsSuccess)
            {
                return Result<string>.Failure(tokenInfo.Errors);
            }

            if (tokenInfo.Value == null || tokenInfo.Value.message?.code != "00")
            {
                _logger.LogError($"[DLVNSubmitApplicationCommand] Error getting token | errorCode: {tokenInfo.Value?.message?.code}, errorMessage: {tokenInfo.Value?.message?.description}");
                return Result<string>.Failure(tokenInfo.Value?.message?.description ?? "Error getting token");
            }

            return Result<string>.Success(tokenInfo.Value.data.token);
        }

        private async Task<DLVNSubmitApplicationRequest> BuildRequest(string tenantId, JObject inputJson, Policy policy)
        {
            (DLVNSubmitApplicationProductPlanRequest productPlan, string rawData) = await BuildProductPlanRequest(tenantId, policy);

            if (productPlan == null || policy?.ContractHolder == null)
            {
                _logger.LogError($"[DLVNSubmitApplicationCommand] Error populating product plan data");
                return null;
            }

            var individual = (await _entityService.GetAsync(tenantId, new EntityWhere { Id = policy.ContractHolder?.Id }))?.FirstOrDefault();

            if (individual == null)
            {
                _logger.LogError($"[DLVNSubmitApplicationCommand] Error populating contract holder data");
                return null;
            }

            return new DLVNSubmitApplicationRequest
            {
                applicationId = policy.IssuerNumber,
                productPlan = productPlan,
                customerInfo = await BuildCustomerInfoRequest(tenantId, inputJson, individual),
                agentInfo = BuildAgentInfoRequest(policy, inputJson),
                benInfo = BuildBeneficiaryInfoRequest(tenantId, inputJson, policy).Result,
                rawData = rawData
            };
        }

        private async Task<(DLVNSubmitApplicationProductPlanRequest, string)> BuildProductPlanRequest(string tenantId, Policy policy)
        {
            string pricing = string.Empty;

            if (policy.IsPricingCacheAvailable)
            {
                List<PolicyPricingCache> caches = await _policyPricingCachesService.Query(tenantId, new QueryArguments<Filter<PolicyPricingCacheFilter>>
                {
                    Where = new Filter<PolicyPricingCacheFilter>
                    {
                        Where = new PolicyPricingCacheFilter
                        {
                            Id = policy.Id
                        }
                    }
                }, System.Threading.CancellationToken.None);

                if (caches.Any())
                    pricing = caches.FirstOrDefault()?.PricingCache;
            }

            if (string.IsNullOrEmpty(pricing))
            {
                Result<string> result = await _productService.Evaluate(tenantId, policy.ClientId, new EvaluateProductScriptCommand
                {
                    DataInput = policy.Fields?.ToString(),
                    ProductId = policy.ProductId,
                    ScriptType = ScriptTypeEnum.Pricing
                });

                if (result.Status != "success")
                {
                    _logger.LogError($"[DLVNSubmitApplicationJob] Error evaluate script | {string.Join(", ", result.Errors)} ");
                    return (null, null);
                }

                pricing = result.Value;
            }

            _logger.LogInformation($"[DLVNSubmitApplicationJob] Calculate pricing for policy {policy.IssuerNumber} | {pricing}");
            decimal ? netPrice = null;
            if (!string.IsNullOrEmpty(pricing))
            {
                var pricingJToken = JToken.Parse(pricing.Replace("\n", " "));
                var pricingDataJToken = pricingJToken?.Value<JToken>("data");
                var aggregatePerContractJToken = pricingDataJToken?.Value<JToken>("aggregatePerContract");
                netPrice = aggregatePerContractJToken?.Value<decimal>("netPrice");
            }
            
            return (new DLVNSubmitApplicationProductPlanRequest
            {
                productCode = policy.ProductId.Plan?.Replace("_FREE", ""),
                optionCode = GetFieldValue<string>(policy.Fields, "planId")?.Replace("_FREE", ""),
                packageValue = GetFieldValue<string>(policy.Fields, "packageValue"),
                frequency = GetFieldValue<string>(policy.Fields, "paymentFrequency"),
                term = GetFieldValue<string>(policy.Fields, "policyTerm"),
                termType = GetFieldValue<string>(policy.Fields, "termUnit"),
                receiptAmount = string.Format("{0:0}", netPrice ?? 0),
                receiptAmountBeforeDiscount = string.Format("{0:0}", netPrice ?? 0),
                riderList = new List<DLVNSubmitApplicationRiderRequest>(),
                uwRemainedSA = GetFieldValue<string>(policy.Fields, "underwritingRemainedSA"),
                uwResultCode = GetFieldValue<string>(policy.Fields, "underwritingResultCode"),
                uwResultMessage = GetFieldValue<string>(policy.Fields, "underwritingResultMessage"),
                registeredDate = (policy.IssueDate ?? policy.CreatedAt)?.ToString("dd/MM/yyyy") ?? "",
                createdDate = policy.CreatedAt?.ToString("dd/MM/yyyy") ?? ""
            }, pricing);
        }

        private async Task<DLVNSubmitApplicationCustomerInfoRequest> BuildCustomerInfoRequest(string tenantId, JObject inputJson, Individual individual)
        {
            Address address = individual.Addresses?.FirstOrDefault();

            string bucketName = inputJson.Value<string>("bucketName");
            if (string.IsNullOrEmpty(bucketName))
            {
                _logger.LogError($"[DLVNSubmitApplicationCommand] Missing bucketName");
            }

            var frontIDPhoto = await ConvertFileSystemDataToBase64(tenantId, bucketName, GetFieldValue<string>(individual.Fields, "frontIdPhotoURL"));
            var backIDPhoto = await ConvertFileSystemDataToBase64(tenantId, bucketName, GetFieldValue<string>(individual.Fields, "backIdPhotoURL"));

            return new DLVNSubmitApplicationCustomerInfoRequest
            {
                clientType = GetFieldValue<string>(individual.Fields, "clientType") ?? "LI",
                familyName = GetFieldValue<string>(individual.Fields, "lastName") ?? individual.EnglishLastName,
                givenName = GetFieldValue<string>(individual.Fields, "firstName") ?? individual.EnglishFirstName,
                gender = GetFieldValue<string>(individual.Fields, "gender") ?? individual.Gender,
                maritalStatus = GetFieldValue<string>(individual.Fields, "maritalStatus") ?? individual.MaritalStatus,
                occupationCode = GetFieldValue<string>(individual.Fields, "occupationCode") ?? individual.Occupation,
                dob = GetFieldValue<DateTime?>(individual.Fields, "dateOfBirth").HasValue ? GetFieldValue<DateTime?>(individual.Fields, "dateOfBirth").Value.ToString("dd/MM/yyyy") : (individual.DateOfBirth.HasValue ? individual.DateOfBirth.Value.ToString("dd/MM/yyyy") : null),
                submitAge = CalculateSubmitAge(GetFieldValue<DateTime?>(individual.Fields, "dateOfBirth") ?? individual.DateOfBirth),
                phone = GetFieldValue<string>(individual.Fields, "phoneNumber") ?? individual.Contacts?.FirstOrDefault(x => x.Type == "telephoneNumber")?.Value,
                email = GetFieldValue<string>(individual.Fields, "email") ?? individual.Contacts?.FirstOrDefault(x => x.Type == "email")?.Value,
                address = GetFieldValue<string>(individual.Fields, "address", "streetAddress") ?? getAddressField(address, "streetAddress"),
                addressZipCode = GetFieldValue<string>(individual.Fields, "address", "zipCode") ?? getAddressField(address, "zipCode"),
                nationality = GetFieldValue<string>(individual.Fields, "nationality") ?? individual.CountryOfResidency ?? "Viet Nam",
                id = GetFieldValue<string>(individual.Fields, "nationalId", "number") ?? individual.Identities?.FirstOrDefault(x => x.Type == "idNumber")?.Value,
                idType = GetFieldValue<string>(individual.Fields, "nationalId", "type") ?? individual.Identities?.FirstOrDefault(x => x.Type == "idType")?.Value,
                frontIDPhoto = frontIDPhoto.IsSuccess ? frontIDPhoto.Value : string.Empty,
                backIDPhoto = backIDPhoto.IsSuccess ? frontIDPhoto.Value : string.Empty,
                frontPhotoOCRError = GetFieldValue<string>(individual.Fields, "frontIdPhotoOCRError"),
                backPhotoOCRError = GetFieldValue<string>(individual.Fields, "backIdPhotoOCRError"),
                idExpirationCheck = GetFieldValue<string>(individual.Fields, "isIdExpired"),
                idInfoEdited = GetFieldValue<string>(individual.Fields, "idInfoEdited"),
                dlvnCustomerCode = GetFieldValue<string>(individual.Fields, "customerCode")
            };
        }

        private string CalculateSubmitAge(DateTime? birthDate)
        {
            if (!birthDate.HasValue)
                return "";

            DateTime today = DateTime.Today;
            int age = today.Year - birthDate.Value.Year;
            if (birthDate.Value.Date > today.AddYears(-age)) age--;

            return age.ToString();
        }

        private async Task<DLVNSubmitApplicationCustomerInfoRequest> BuildBeneficiaryInfoRequest(string tenantId, JObject inputJson, Policy policy)
        {
            if (policy.ContractBeneficiaryEligibilities == null || !policy.ContractBeneficiaryEligibilities.Any())
                return new DLVNSubmitApplicationCustomerInfoRequest();

            string individualId = policy.ContractBeneficiaryEligibilities.FirstOrDefault(i => i.ContractEntity != null)?.ContractEntity?.Id;

            if (string.IsNullOrEmpty(individualId))
                return new DLVNSubmitApplicationCustomerInfoRequest();

            var individual = (await _entityService.GetAsync(tenantId, new EntityWhere { Id = individualId }))?.FirstOrDefault();

            if (individual == null)
            {
                _logger.LogError($"[DLVNSubmitApplicationCommand] cannot find entity with id: {individualId}");
                return new DLVNSubmitApplicationCustomerInfoRequest();
            }

            DLVNSubmitApplicationCustomerInfoRequest beneficiary = await BuildCustomerInfoRequest(tenantId, inputJson, individual);
            beneficiary.relationWithPolicyholder = GetFieldValue<string>(individual.Fields, "relationshipWithHolder");

            return beneficiary;
        }

        private string getAddressField(Address address, string key) => address?.Fields?.FirstOrDefault(f => f.Key == key).Value;

        private T GetFieldValue<T>(JToken fields, string type) =>
            fields != null
                ? fields.Value<T>(type) ?? default
                : default;

        private T GetFieldValue<T>(JToken fields, string type, string subType)
        {
            JToken typeValue = GetFieldValue<JToken>(fields, type);

            return GetFieldValue<T>(typeValue, subType);
        }


        private DLVNSubmitApplicationAgentInfoRequest BuildAgentInfoRequest(Policy policy, JObject inputJson)
        {
            return new DLVNSubmitApplicationAgentInfoRequest
            {
                agentId = GetFieldValue<string>(policy.Fields, "agentId") ?? inputJson.Value<string>("agentId") ?? "8000156",
                branchCode = GetFieldValue<string>(policy.Fields, "remainedSA") ?? inputJson.Value<string>("branchCode") ?? ""
            };
        }

        private async Task<Result<DLVNClientVerifyResponse>> RequestSubmitApplication(string tenantId, DLVNSubmitApplicationRequest data, Product2 product)
        {
            var request = new RestRequest(Method.POST)
            {
                RequestFormat = DataFormat.Json
            };

            request.AddJsonBody(data);

            string submitDomainUrl = GetProductFactsValue<string>(product?.Facts?.FirstOrDefault(x => x.Type == "submitDomain")?.Value) ?? GetDefaultUwDomainUrl(tenantId);
            string submitPath = GetProductFactsValue<string>(product?.Facts?.FirstOrDefault(x => x.Type == "submitPath")?.Value) ?? "sopSubmitApplication";

            return await ExecuteAsync<DLVNClientVerifyResponse>($"{submitDomainUrl}/{submitPath}", request);
        }

        private async Task CreateTxtAndUploadToFileSystemAsync(string tenantId, string bucketName, DLVNSubmitApplicationRequest data)
        {
            DateTime today = DateTime.Now;
            string textOutput = JsonConvert.SerializeObject(data);

            string filePath = $"systemLog/submitApplication/{today.ToString("yyyyMMdd")}/{data.applicationId}-{today.ToString("HHmmss")}.txt";

            Result uploadResult = await UploadFileToFileSystem(
                tenantId,
                bucketName,
                filePath,
                Encoding.ASCII.GetBytes(textOutput));

            if (!uploadResult.IsSuccess)
            {
                _logger.LogError($"[DLVNSubmitApplicationCommand]Upload file failed | {string.Join(", ", uploadResult.Errors)}");
            }
        }

        private class DLVNSubmitApplicationRequest
        {
            public string token { get; set; } = "";
            public string applicationId { get; set; } = "";
            public DLVNSubmitApplicationProductPlanRequest productPlan { get; set; }
            public DLVNSubmitApplicationCustomerInfoRequest customerInfo { get; set; }
            public string refTxnId { get; set; } = "";
            public DLVNSubmitApplicationAgentInfoRequest agentInfo { get; set; }
            public DLVNSubmitApplicationCustomerInfoRequest benInfo { get; set; }
            public IEnumerable<DLVNSubmitApplicationVoucherRequest> voucher { get; set; } = new List<DLVNSubmitApplicationVoucherRequest>();
            public IEnumerable<DLVNSubmitApplicationDiscountRequest> discount { get; set; } = new List<DLVNSubmitApplicationDiscountRequest>();
            public string rawData { get; set; }
        }

        private class DLVNSubmitApplicationProductPlanRequest
        {
            public string productCode { get; set; } = "";
            public string optionCode { get; set; } = "";
            public string packageValue { get; set; } = "";
            public string frequency { get; set; } = "";
            public string term { get; set; } = "";
            public string termType { get; set; } = "";
            public string receiptAmount { get; set; } = "";
            public string receiptAmountBeforeDiscount { get; set; } = "";
            public IEnumerable<DLVNSubmitApplicationRiderRequest> riderList { get; set; } = new List<DLVNSubmitApplicationRiderRequest>();
            public string uwRemainedSA { get; set; } = "";
            public string uwResultCode { get; set; } = "";
            public string uwResultMessage { get; set; } = "";
            public string registeredDate { get; set; } = "";
            public string createdDate { get; set; } = "";
        }

        private class DLVNSubmitApplicationRiderRequest
        {
            public string riderCode { get; set; } = "";
            public string riderValue { get; set; } = "";
        }

        private class DLVNSubmitApplicationCustomerInfoRequest
        {
            public string clientType { get; set; } = "";
            public string familyName { get; set; } = "";
            public string givenName { get; set; } = "";
            public string gender { get; set; } = "";
            public string dob { get; set; } = null;
            public string submitAge { get; set; } = "";
            public string phone { get; set; } = "";
            public string email { get; set; } = "";
            public string maritalStatus { get; set; } = "";
            public string nationality { get; set; } = "";
            public string occupationCode { get; set; } = "";
            public string id { get; set; } = "";
            public string idType { get; set; } = "";
            public string address { get; set; } = "";
            public string addressZipCode { get; set; } = "";
            public string frontIDPhoto { get; set; } = "";
            public string backIDPhoto { get; set; } = "";
            public string frontPhotoOCRError { get; set; } = "";
            public string backPhotoOCRError { get; set; } = "";
            public string idExpirationCheck { get; set; } = "";
            public string idInfoEdited { get; set; } = "";
            public string dlvnCustomerCode { get; set; } = "";
            public string relationWithPolicyholder { get; set; } = null;
        }

        private class DLVNSubmitApplicationDiscountRequest
        {
            public string discountAmount { get; set; } = "";
            public string discountPercent { get; set; } = "";
            public string discountCode { get; set; } = "";
        }

        private class DLVNSubmitApplicationVoucherRequest
        {
            public string voucherAmount { get; set; } = "";
            public string voucherCode { get; set; } = "";
        }

        private class DLVNSubmitApplicationAgentInfoRequest
        {
            public string agentId { get; set; } = "";
            public string branchCode { get; set; } = "";
        }

        private class DLVNClientVerifyResponse
        {
            public DLVNResponseMessage message { get; set; }
        }
    }
}

