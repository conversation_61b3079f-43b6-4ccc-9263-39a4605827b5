﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Users.Domain.Individuals;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace CoverGo.Gateway.Infrastructure.DLVNServices.DLVNCommands
{
    public class DLVNCreateCustomerCommand : DLVNCommand
    {
        public override string Name => "createCustomer";
        private readonly IPolicyService _policyService;
        private readonly IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> _entityService;
        private readonly IAuthService _authService;

        public DLVNCreateCustomerCommand(
            IPolicyService policyService,
            IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> entityService,
            IAuthService authService,
            IFileSystemService fileSystemService,
            ILogger<DLVNCreateCustomerCommand> logger) : base(fileSystemService, logger)
        {
            _policyService = policyService;
            _entityService = entityService;
            _authService = authService;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            string nationalId = inputJson.Value<string>("nationalId");
            string clientId = inputJson.Value<string>("clientId");
            string loginIdFromToken = inputJson.Value<string>("loginIdFromToken");

            Login loginFromToken = await _authService.GetLoginById(tenantId, loginIdFromToken);

            if (string.IsNullOrEmpty(nationalId))
            {
                return Result<string>.Failure("Missing nationalId");
            }

            if (string.IsNullOrEmpty(clientId))
            {
                return Result<string>.Failure("Missing clientId");
            }

            Login nationalIdLogin = await _authService.GetLoginByNameAsync(tenantId, nationalId);

            if (loginFromToken.Username.ToLower().Contains("guest") || loginFromToken.Username.Contains("@"))
            {
                if (nationalIdLogin == null)
                {
                    Result<CreatedStatus> createLoginResult = await CreateNewLoginAccount(tenantId, clientId, nationalId);

                    if (!createLoginResult.IsSuccess)
                    {
                        _logger.LogError($"[DLVNCreateCustomerCommand] Create login failed | {string.Join(", ", createLoginResult.Errors)}");
                        return Result<string>.Failure("Create login failed");
                    }

                    nationalIdLogin = await _authService.GetLoginByNameAsync(tenantId, nationalId);
                }
            }
            else if (loginFromToken.Username != nationalId.Trim())
            {
                if (nationalIdLogin == null)
                {
                    var updateLoginResult = await UpdateLoginAccount(tenantId, loginIdFromToken, nationalId);

                    if (!updateLoginResult.IsSuccess)
                    {
                        _logger.LogError($"[DLVNCreateCustomerCommand] UpdateLogin failed | {string.Join(", ", updateLoginResult.Errors)}");
                        return Result<string>.Failure("Update login failed");
                    }

                    nationalIdLogin = await _authService.GetLoginByNameAsync(tenantId, nationalId);
                }
                else
                {
                    string policyNumber = inputJson.Value<string>("policyNumber");

                    var policyToDelete = (await _policyService.GetAsync(tenantId, new PolicyWhere
                    {
                        IssuerNumber_contains = policyNumber
                    }))?.FirstOrDefault();

                    if (policyToDelete != null)
                    {
                        var deletePolicyResult = await _policyService.DeletePolicyAsync(tenantId, policyToDelete.Id, loginIdFromToken);

                        if (!deletePolicyResult.IsSuccess)
                        {
                            _logger.LogError($"[DLVNCreateCustomerCommand] Delete policy failed | {string.Join(", ", deletePolicyResult.Errors)}");
                        }
                    }
                }
            }

            if (nationalIdLogin.PermissionGroups?.FirstOrDefault(x => x.Name.ToUpper() == "CUSTOMER") == null)
            {
                var permissionGroup = (await _authService.GetPermissionGroupsAsync(tenantId, new PermissionGroupWhere
                {
                    Name = "CUSTOMER"
                }))?.FirstOrDefault();

                if (permissionGroup == null)
                {
                    _logger.LogError($"[DLVNCreateCustomerCommand] No customer group found");
                    return Result<string>.Failure("No customer group found");
                }

                var addPermissionGroupResult = await _authService.AddLoginIdToPermissionGroupAsync(tenantId, permissionGroup.Id, new AddLoginPermissionsToPermissionGroupCommand
                {
                    LoginId = nationalIdLogin.Id
                });

                if (!addPermissionGroupResult.IsSuccess)
                {
                    _logger.LogError($"[DLVNCreateCustomerCommand] Update customer group failed | {string.Join(", ", addPermissionGroupResult.Errors)}");
                    return Result<string>.Failure("Update customer group failed");
                }

                var addTargetPermissionResult = await _authService.AddTargettedPermissionsAsync(tenantId, nationalIdLogin.Id, new List<AddTargettedPermissionCommand>
                {
                    new()
                    {
                        Type = "groups",
                        Value = permissionGroup.Id
                    }
                });

                if (!addTargetPermissionResult.IsSuccess)
                {
                    _logger.LogError($"[DLVNCreateCustomerCommand] Update target failed | {string.Join(", ", addTargetPermissionResult.Errors)}");
                    return Result<string>.Failure("Update target failed");
                }
            }

            Individual individual = (await _entityService.GetAsync(tenantId, new IndividualWhere
            {
                CreatedById = nationalIdLogin.Id,
                Type_in = new List<IndividualTypes> { IndividualTypes.Pipeline, IndividualTypes.Customer }
,            }))?.FirstOrDefault();

            string individualId = string.Empty;
            if (individual == null)
            {
                Result<CreatedStatus> createdIndividualResult = await CreateIndividual(tenantId, nationalIdLogin.Id, inputJson);

                if (!createdIndividualResult.IsSuccess)
                {
                    _logger.LogError($"[DLVNCreateCustomerCommand] Create individual failed | {string.Join(", ", createdIndividualResult.Errors)}");
                    return Result<string>.Failure("Create individual failed");
                }
                individualId = createdIndividualResult.Value.Id;
            }
            else
            {
                individualId = individual.Id;
            }

            Token token = await _authService.GetWholeAccessTokenAsync(tenantId, clientId, "", nationalId, nationalId);

            if (token?.ErrorDescription == "invalid_username_or_password")
            {
                var updateLoginResult = await UpdateLoginAccount(tenantId, nationalIdLogin.Id, nationalId);

                if (!updateLoginResult.IsSuccess)
                {
                    _logger.LogError($"[DLVNCreateCustomerCommand] UpdateLogin failed || {string.Join(", ", updateLoginResult.Errors)}");
                    return Result<string>.Failure("Update login failed");
                }

                token = await _authService.GetWholeAccessTokenAsync(tenantId, clientId, "", nationalId, nationalId);
            }

            return !string.IsNullOrEmpty(token?.AccessToken)
                                ? Result<string>.Success(JsonConvert.SerializeObject(new DLVNCreateCustomerResponse { accessToken = token.AccessToken, refreshToken = token.RefreshToken, individualId = individualId, loginId = nationalIdLogin.Id }))
                                : Result<string>.Failure(token?.Error);
        }

        private async Task<Result<CreatedStatus>> CreateNewLoginAccount(string tenantId, string clientId, string nationalId)
        {
            Result<CreatedStatus> loginCreatedResult = await _authService.CreateLoginAsync(tenantId, new CreateLoginCommand
            {
                ClientId = clientId,
                Username = nationalId,
                Password = nationalId,
                IsEmailConfirmed = true
            });

            if (!loginCreatedResult.IsSuccess)
                return Result<CreatedStatus>.Failure(loginCreatedResult.Errors);


            await _authService.AddTargettedPermissionsAsync(tenantId, loginCreatedResult.Value.Id, new List<AddTargettedPermissionCommand>
            {
                new() { AddedById = loginCreatedResult.Value.Id, Type = "clientId", Value = clientId }
            });

            return loginCreatedResult;
        }

        private async Task<Result> UpdateLoginAccount(string tenantId, string loginIdFromToken, string nationalId)
        {
            var updateLoginResult = await _authService.UpdateLoginAsync(tenantId, loginIdFromToken, new UpdateLoginCommand
            {
                IsUserNameChanged = true,
                UserName = nationalId,
                ModifiedById = loginIdFromToken
            });

            return updateLoginResult;
        }

        private async Task<Result<CreatedStatus>> CreateIndividual(string tenantId, string loginId, JObject inputJson)
        {
            Result<CreatedStatus> createdIndividualResult = await _entityService.CreateAsync(tenantId, new CreateIndividualCommand
            {
                Type = inputJson.Value<IndividualTypes?>("individualTypes") ?? IndividualTypes.Pipeline,
                CreatedById = loginId,
                EnglishFirstName = "",
                EnglishLastName = "",
                NameFormat = "{englishLastName} {englishFirstName}"
            });

            return createdIndividualResult;
        }

        private class DLVNCreateCustomerResponse
        {
            public string accessToken { get; set; }
            public string refreshToken { get; set; }
            public string individualId { get; set; }
            public string loginId { get; set; }
        }
    }
}

