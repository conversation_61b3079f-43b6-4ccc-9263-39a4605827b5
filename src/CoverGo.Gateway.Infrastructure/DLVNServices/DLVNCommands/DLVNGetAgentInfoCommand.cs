using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.Products;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using RestSharp;
using Fact = CoverGo.Gateway.Domain.Users.Fact;

namespace CoverGo.Gateway.Infrastructure.DLVNServices.DLVNCommands;

public class DLVNGetAgentInfoCommand : DLVNCommand
{
    public override string Name => "getAgentInfo";
    private readonly IProductService _productService;

    public DLVNGetAgentInfoCommand(
        IFileSystemService fileSystemService,
        ILogger<DLVNGetAgentInfoCommand> logger,
        IProductService productService) : base(fileSystemService, logger)
    {
        _productService = productService;
    }

    public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
    {
        CommandInput input = inputJson?.ToObject<CommandInput>();

        if (input is null || !input.IsValid())
        {
            _logger.LogError("[DLVNGetAgentInfoCommand] Invalid input - {input}", inputJson.ToString());
            return Result<string>.Failure("Bad request");
        }

        Product2 product = await GetProduct(tenantId, input);

        string tenantBucketName = await GetTenantBucketName(tenantId);
        if (string.IsNullOrEmpty(tenantBucketName))
        {
            _logger.LogError("[DLVNGetAgentInfoCommand] Tenant bucket name not found - {tenantId}", tenantId);
            return Result<string>.Failure("Tenant bucket name not found");
        }

        Result<TokenResponse> token = await GetToken(tenantId, tenantBucketName, product);
        if (!token.IsSuccess || token?.Value?.data.token is null)
        {
            _logger.LogError("[DLVNGetAgentInfoCommand] Token not found - {tenantId}", tenantId);
            return Result<string>.Failure("Unable to get token");
        }

        DLVNServiceRequest requestBody = DLVNServiceRequest.FromCommandInput(input, token.Value.data.token);

        Result<JObject> result = await GetAgentInfo(tenantId, product, requestBody);
        if (!result.IsSuccess)
        {
            _logger.LogError("[DLVNGetAgentInfoCommand] GetAgentInfo failed - {result}", JsonConvert.SerializeObject(result));
            return Result<string>.Failure(result.Errors);
        }

        return Result<string>.Success(result.Value?.ToString());
    }

    private async Task<string> GetTenantBucketName(string tenantId)
    {
        IEnumerable<FileSystemConfig> tenantFileConfigs = await _fileSystemService.GetConfigsAsync(tenantId,
            new Domain.QueryArguments { First = 1 });
        return tenantFileConfigs.FirstOrDefault()?.BucketName;
    }

    private async Task<Product2> GetProduct(string tenantId, CommandInput input)
    {
        if (string.IsNullOrEmpty(input.ProductIdPlan))
        {
            _logger.LogInformation("[DLVNGetAgentInfoCommand] ProductId is null or empty - {input}", input);
            return null;
        }

        IEnumerable<Product2> productResult = await _productService.GetAsync(tenantId, null, new()
        {
            Where = new()
            {
                ProductId = new()
                {
                    Plan = input.ProductIdPlan
                }
            },
            Limit = 1,
            OrderBy = new()
            {
                FieldName = "createdAt",
                Type = OrderByType.ASC
            }
        });

        if (!productResult.Any())
        {
            _logger.LogWarning("[DLVNGetAgentInfoCommand] Product not found - {productId}", input.ProductIdPlan);
        }

        return productResult?.FirstOrDefault();
    }

    private async Task<Result<JObject>> GetAgentInfo(string tenantId, Product2 product, DLVNServiceRequest requestBody)
    {
        RestRequest request = new(Method.POST);
        string serializedBody = JsonConvert.SerializeObject(requestBody, Formatting.None, new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver()
        });
        request.AddParameter(new JsonParameter("", serializedBody));
        _logger.LogInformation("[DLVNGetAgentInfoCommand] requestBody: {requestBody}", serializedBody);

        string underwritingDomainUrl = GetProductFactsValue<string>(GetUnderwritingDomainFact(product)?.Value)
            ?? GetDefaultUwDomainUrl(tenantId);

        return await ExecuteAsync<JObject>($"{underwritingDomainUrl}/getAgentInfo", request);
    }

    private Fact GetUnderwritingDomainFact(Product2 product)
    {
        string underwritingDomainFactType = "uwDomain";
        return product?.Facts?.FirstOrDefault(fact => fact.Type == underwritingDomainFactType);
    }

    public class CommandInput
    {
        public string AgentId { get; set; }
        public string ProductIdPlan { get; set; }

        public bool IsValid() => !string.IsNullOrEmpty(AgentId);
    }

    public class DLVNServiceRequest
    {
        public string Token { get; set; }
        public string AgentId { get; set; }
        public string ProductId { get; set; }

        public static DLVNServiceRequest FromCommandInput(CommandInput input, string token) => new()
        {
            Token = token,
            AgentId = input.AgentId,
            ProductId = input.ProductIdPlan
        };
    }
}