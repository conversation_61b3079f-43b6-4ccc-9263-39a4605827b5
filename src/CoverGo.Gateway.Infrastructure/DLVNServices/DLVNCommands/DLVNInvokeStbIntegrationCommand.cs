﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.FileSystem;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.OpenSsl;
using Org.BouncyCastle.Security;
using RestSharp;
using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.DLVNServices.DLVNCommands
{
    public class DLVNInvokeStbIntegrationCommand : DLVNCommand
    {
        public override string Name => "invokeStbIntegration";

        public DLVNInvokeStbIntegrationCommand(
           IFileSystemService fileSystemService,
           ILogger<DLVNInvokeStbIntegrationCommand> logger) : base(fileSystemService, logger)
        {
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            (DLVNInvokeStbIntegrationRequest requestBody, string data) = BuildRequest(tenantId, inputJson);

            if (requestBody == null)
            {
                _logger.LogError($"[DLVNInvokeStbIntegrationCommand] Error populating request data");
                return Result<string>.Failure("Error populating request data");
            }

            var md5 = CreateMD5(JsonConvert.SerializeObject(requestBody));
            string authorization = $"Basic {Convert.ToBase64String(Encoding.UTF8.GetBytes(GetScbCredentials(tenantId)))}";
            string signature = GenerateSignature(GetScbPrivateKey(tenantId), md5);

            await CreateTxtAndUploadToFileSystemAsync(tenantId, inputJson.Value<string>("bucketName"), requestBody.RequestID, JsonConvert.SerializeObject(new { requestBody, dataPreTripleDes = data, md5, authorization, signature }));

            if (string.IsNullOrEmpty(authorization))
                return Result<string>.Failure("no authorization found");

            if (string.IsNullOrEmpty(signature))
                return Result<string>.Failure("no signature found");

            Result<DLVNInvokeStbIntegrationResponse> result = await RequestDLVNInvokeStbIntegration(tenantId, authorization, signature, requestBody);

            if (!result.IsSuccess)
            {
                return Result<string>.Failure(result.Errors);
            }

            if (!string.IsNullOrEmpty(result.Value?.Data))
            {
                result.Value.DecryptedData = TrippleDESDecrypt(GetSacombankSecretKey(tenantId), result.Value.Data) ?? result.Value.Data;
            }

            return Result<string>.Success(JsonConvert.SerializeObject(result.Value));

        }

        private (DLVNInvokeStbIntegrationRequest, string) BuildRequest(string tenantId, JObject inputJson)
        {
            string requestId = inputJson.Value<string>("requestId") ?? Guid.NewGuid().ToString();
            string dataObject = inputJson.SelectToken("dataObject")?.ToString(Formatting.None);

            if (string.IsNullOrEmpty(dataObject))
            {
                _logger.LogError("[DLVNInvokeStbIntegrationCommand] Error populating request data, {dataObject} is empty", dataObject);
                return (null, null);
            }

            return (new DLVNInvokeStbIntegrationRequest
            {
                FunctionName = inputJson.Value<string>("FunctionName") ?? "PartnerPushNotification",
                RequestDateTime = (inputJson.Value<DateTime?>("requestDateTime") ?? DateTime.UtcNow).ToString("yyyy-MM-ddTHH:mm:ssZ"),
                RequestID = requestId,
                Data = TrippleDESEncrypt(GetSacombankSecretKey(tenantId), dataObject)//Encrypt3DS(tenantId, requestId, JsonConvert.SerializeObject(data))
            }, dataObject);
        }

        private async Task<Result<DLVNInvokeStbIntegrationResponse>> RequestDLVNInvokeStbIntegration(string tenantId, string authoriation, string signature, DLVNInvokeStbIntegrationRequest data)
        {
            var request = new RestRequest(Method.POST)
            {
                RequestFormat = DataFormat.Json
            };

            request.AddHeader("Authorization", authoriation);
            request.AddHeader("Signature", signature);
            request.AddJsonBody(data);

            return await ExecuteAsync<DLVNInvokeStbIntegrationResponse>($"{GetScbIntegrationEndpoint(tenantId)}", request);
        }

        private async Task CreateTxtAndUploadToFileSystemAsync(string tenantId, string bucketName, string requestId, string data)
        {
            DateTime today = DateTime.Now;

            string filePath = $"systemLog/invokeStbIntegration/{today.ToString("yyyyMMdd")}/{requestId}-{today.ToString("HHmmss")}.txt";

            Result uploadResult = await UploadFileToFileSystem(
                tenantId,
                bucketName,
                filePath,
                Encoding.UTF8.GetBytes(data));

            if (!uploadResult.IsSuccess)
            {
                _logger.LogError($"[DLVNInvokeStbIntegrationCommand]Upload file failed | {string.Join(", ", uploadResult.Errors)}");
            }
        }

        public string TrippleDESEncrypt(string key, string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                _logger.LogError("[DLVNInvokeStbIntegrationCommand] Error encrypting 3DS, empty string");
                return string.Empty;
            }

            if (string.IsNullOrEmpty(key))
            {
                _logger.LogError("[DLVNInvokeStbIntegrationCommand] Error encrypting 3DS, empty key");
                return string.Empty;
            }

            TripleDESCryptoServiceProvider cipher = new TripleDESCryptoServiceProvider
            {
                Key = Encoding.UTF8.GetBytes(key),
                Mode = CipherMode.ECB,
                Padding = PaddingMode.PKCS7
            };
            byte[] data = Encoding.UTF8.GetBytes(input);
            return Convert.ToBase64String(cipher.CreateEncryptor().TransformFinalBlock(data, 0, data.Length));
        }

        public string TrippleDESDecrypt(string key, string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                _logger.LogError("[DLVNInvokeStbIntegrationCommand] Error decrypting 3DS, empty string");
                return string.Empty;
            }

            if (string.IsNullOrEmpty(key))
            {
                _logger.LogError("[DLVNInvokeStbIntegrationCommand] Error decrypting 3DS, empty key");
                return string.Empty;
            }

            TripleDESCryptoServiceProvider cipher = new TripleDESCryptoServiceProvider
            {
                Key = Encoding.UTF8.GetBytes(key),
                Mode = CipherMode.ECB,
                Padding = PaddingMode.PKCS7
            };
            byte[] data = Convert.FromBase64String(input);
            return System.Text.Encoding.UTF8.GetString(cipher.CreateDecryptor().TransformFinalBlock(data, 0, data.Length));
        }

        private string GetScbIntegrationEndpoint(string tenant)
            => tenant switch
            {
                "dlvn" => System.Environment.GetEnvironmentVariable("DLVN_SCB_INTEGRATION_ENDPOINT") ?? "https://partner.sacombank.com.vn/epaygw/stb/",
                _ => System.Environment.GetEnvironmentVariable("DLVN_UAT_SCB_INTEGRATION_ENDPOINT")
            };

        private string GetSacombankSecretKey(string tenant)
            => tenant switch
            {
                "dlvn" => System.Environment.GetEnvironmentVariable("DLVN_SCB_SECRET_KEY") ?? "b86523a39d990ef9",
                _ => Environment.GetEnvironmentVariable("DLVN_UAT_SCB_SECRET_KEY")
            };

        private string GetScbPrivateKey(string tenant)
            => tenant switch
            {
                "dlvn" => System.Environment.GetEnvironmentVariable("DLVN_SCB_INTEGRATION_PRIVATE_KEY") ?? $"-----BEGIN RSA PRIVATE KEY-----MIIEpAIBAAKCAQEApn9q4gJDgJe2iMnBfhjaepFcG8d/xDtyBLdQKduHlT3XRDR0\nqh732DcR+rA3QTmTnllghgSkg5YstojQZOc3Yi9TA7Jh1oxx1cYj3V039hYbh5Jh\nIsWhAm+RnS/tENpJYleO83TQUNQeYRGngsTeFDrYc4nRLWZiZs24kaG8d2zQh6Xk\n7YRUTKEHhp3fk0c1S3ZgW5xmlIPf9OKSgy4UsBlGyzZfKIh4kn3t4VEdLdA6704S\nE3BE9n9NI0LLceRoGakxFqE3EHsrqTobRSXn05DXF3ZuzSEbwzRb6Gxrzii/25XK\nXIoRhbmoAYZInqNA7M9yBZcNrpl8Kuiscm9dWwIDAQABAoIBAACmK4UBDEFSlhgX\nbUUwFJ34thq3nuRldO7Wxh27qN4olQ/DgpbtMvspT80h5b0/27b40zsChp+qtLbr\ndqCGFD2yQIkZp27+Mi1qrdYdmGvBr07Kf81yZsvLIo6x0qNHrpSPEdQaJvvBr/Kh\n7nzAhrsedZyRs3qRVR3gfHE457KxfMWV8nMrh4e+HET0WSjbr3aweVV7xYD28ri0\nkdWeUt93QM5DPy5DSBtjpHiXIvGKtmdE1qsFJrjBqH7dlKMnMXaSqhNXpbQEpN3+\n9tm63AkcTabXO4pTbX/cFONIgvK8MbKvR7/Jh0HZ37H499rxPSmSAdzVUqLK8mIk\ny8TWM8ECgYEA+ADB4Z0poMPam7gic5quaAPgJ/1HsXUDPG4IzDcThBWH/ne79atz\nY82i+IQ2YVj7is0a6K0ZqG6JKJBov41sz4vMxW4xKDhvXhNuQPStr7F/6eOAKbBs\nNwElat9hKD1oDXPMETBRnhHsq1lDfBtV2lLDshYhaPKXQtrn6l26vUECgYEAq93X\nc+qtKX4dFBH/EJYPNhwQb4TM9IK+ktc3au5DJxuxkvS9mZKpgE7gG7Sq+0blxrI9\nPpUcQXIJ7fF12Q84WysKXJ8yZFhzHpp5WF3JHJv3HScNbyLAFxGh+zC43x9jlPgX\ncPbWtzSL0Dlhh/fvnOYGTl9+MOSBS8IL3gCyB5sCgYEAvjrmfQaujbBtmRCO7Jnz\nublvUX1IZuhYiRvmB68feyuA20JAnNrcceukXHgdtmIo4HkfcOaGTdorvz+1+Wij\n1Ddp5O73KeDQHBtPcOe96ox+j4uAHXpEJ5TrfKTHw7QGgnsWRwEHOfaJ0Y1w5Fub\naRIfJhUvn26ldBSx2+X1fYECgYBbhzt0uigaDtXBmIvz81aKhIukOF9GYFLJtOAI\njHcq3q7FCp3mqZqngFoNpzvfsjQwiz/ekl6H5AmxtIQEYvyqYNOV3BpkIk68n5u8\nGcY+/DiwR9n5s9IE9xkCw0HRYdAQx8cEHBDhZJSJAeYVGU98iWwWVPjJZQgOpnrX\nchtdvQKBgQCLMEYTeG3wicyuPFDxqMIZT2gnQ0Y6Df+wrw8RcBcyFIAQTU60FNNp\nQDTJ+QHbRiEytMkQfEjreP4y62XfciTE3p5HbFWIGrrPMGkGvaBPZosTOuUfDHxU\nULwCKYbAuecfwnCWedyMnL/OU719ZSnewlYP+uL4+5P267esc7I21w==\n-----END RSA PRIVATE KEY-----",
                _ => System.Environment.GetEnvironmentVariable("DLVN_UAT_SCB_INTEGRATION_PRIVATE_KEY")
            };

        private string GetScbCredentials(string tenant)
            => tenant switch
            {
                "dlvn" => System.Environment.GetEnvironmentVariable("DLVN_SCB_CREDENTIALS") ?? "9a814f9f-51db-4ad4-beb1-58f29a7a537d:b34573c38954a4a8",
                _ => System.Environment.GetEnvironmentVariable("DLVN_UAT_SCB_CREDENTIALS")
            };

        public string GenerateSignature(string privateKey, string input)
        {
            _logger.LogInformation("[DLVNInvokeStbIntegrationCommand] Generate signature private key:{privateKey}", privateKey);
            if (string.IsNullOrEmpty(input))
            {
                _logger.LogError("[DLVNInvokeStbIntegrationCommand] Error generate signature, empty string");
                return string.Empty;
            }

            if (string.IsNullOrEmpty(privateKey))
            {
                _logger.LogError("[DLVNInvokeStbIntegrationCommand] Error generate signature, empty private key");
                return string.Empty;
            }

            try
            {
                byte[] data = Encoding.ASCII.GetBytes(input);
                AsymmetricCipherKeyPair keyPair = (AsymmetricCipherKeyPair)
                    new PemReader(new StringReader(privateKey)).ReadObject();

                ISigner signer = SignerUtilities.GetSigner("SHA1withRSA");
                signer.Init(true, keyPair.Private);
                signer.BlockUpdate(data, 0, data.Length);
                byte[] output = signer.GenerateSignature();
                _logger.LogInformation("[DLVNInvokeStbIntegrationCommand] Generate signature:{signature}", Convert.ToBase64String(output));
                return Convert.ToBase64String(output);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DLVNInvokeStbIntegrationCommand] Error generate signature");
                return string.Empty;
            }

        }

        private string CreateMD5(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                _logger.LogError("[DLVNInvokeStbIntegrationCommand] Error hashing MD5, empty string");
                return string.Empty;
            }

            try
            {
                MD5 md5 = MD5.Create();
                byte[] hash = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
                StringBuilder buffer = new StringBuilder();
                for (int i = 0; i < hash.Length; i++)
                {
                    buffer.Append(hash[i].ToString("X2"));
                }
                return buffer.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DLVNInvokeStbIntegrationCommand] Error hashing MD5");
                return string.Empty;
            }
        }

        private class DLVNInvokeStbIntegrationRequest
        {
            public string Data { get; set; }
            public string FunctionName { get; set; }
            public string RequestDateTime { get; set; }
            public string RequestID { get; set; }
        }

        private class NotiParam
        {
            public string Name { get; set; }
            public string Value { get; set; }
        }

        private class DLVNInvokeStbIntegrationResponse
        {
            public string FunctionName { get; set; }
            public string RequestDateTime { get; set; }
            public string RequestID { get; set; }
            public string Description { get; set; }
            public string RespCode { get; set; }
            public string Data { get; set; }
            public string DecryptedData { get; set; }
            public string RefNumber { get; set; }

        }
    }
}
