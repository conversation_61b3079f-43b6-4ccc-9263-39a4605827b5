using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.InsuredNomadsServices
{
    public class InsuredNomadsIntegrationService
    {
        private readonly IEnumerable<InsuredNomadsCommand> _insuredNomadsCommand;

        public InsuredNomadsIntegrationService(IEnumerable<InsuredNomadsCommand> insuredNomadsCommand)
        {
            _insuredNomadsCommand = insuredNomadsCommand;
        }

        public async Task<Result<string>> ExecuteAsync(string tenantId, string commandType, JObject inputJson)
        {
            if (commandType == "postPaymentProcess")
            {
                Result<string> result = await _insuredNomadsCommand.First(x => x.Name == "processPolicyAndMembers").ExecuteAsync(tenantId, inputJson);
                if (!result.IsSuccess) return result;

                string[] commandsInOrder = { "passcreatorCreate", "safetureSubscriptionAdd"};

                IEnumerable<Task<Result<string>>> tasks = _insuredNomadsCommand.Where(x => commandsInOrder.Contains(x.Name))
                    .Select(x => x.ExecuteAsync(tenantId, inputJson));

                Result<string>[] results = await Task.WhenAll(tasks);
                List<string> errors = results.Where(x => !x.IsSuccess && x.Errors != null)
                    .SelectMany(x => x.Errors).ToList();

                return errors.Any() ? Result<string>.Failure(errors) : result;
            }

            InsuredNomadsCommand command = _insuredNomadsCommand.FirstOrDefault(x => x.Name == commandType);

            if (command != null)
                return await command.ExecuteAsync(tenantId, inputJson);

            return Result<string>.Failure($"No action for {commandType} found."); ;
        }

        public async Task<Result<string>> IssuePolicyForActiveMember(string tenantId, JObject inputJson) =>
            await ExecuteAsync(tenantId, "issuePolicyForActiveMember", inputJson);
    }
}