using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.InsuredNomadsServices.InsuredNomadsServicesCommands
{
    public class PasscreatorCreate : InsuredNomadsCommand
    {
        public override string Name { get; } = "passcreatorCreate";
        private const string CoverGoTestPassTemplateId = "b35aabef-3123-428f-8b3e-a971e66e39ee"; // "Test WalletPass for CoverGo"
        private const string CoverGoTestPassCreatorApiKey = "MioW!4vWJNHMZ7uBMSj9Fdr/E5fSFE788m1zzcwjucIkpZD0QSXnGr3dAOuh&Ji80MG6JU7hOBif.0PU";
        private static readonly JsonSerializerOptions _serializeOptions = new() { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
        public PasscreatorCreate(ILogger<PasscreatorCreate> logger) : base(logger) { }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            string passCreatorApiKey = Environment.GetEnvironmentVariable($"{tenantId.ToUpper()}_PASSCREATOR_API_KEY") ?? CoverGoTestPassCreatorApiKey;

            try
            {
                PassCreatorCreateBatchAdd batchAddInput = inputJson.GetValue("passCreatorCreateInputs").ToObject<PassCreatorCreateBatchAdd>();

                string passtemplateId = batchAddInput.TemplateId ?? CoverGoTestPassTemplateId;

                foreach (PassCreatorCreateInput passCreatorCreateInput in batchAddInput.Inputs)
                {
                    Result<string> result = await AddPasscreatorPass(passCreatorCreateInput, passCreatorApiKey, passtemplateId);
                    if (result.Status != "success") return result;

                }
                return Result<string>.Success("Passcreator create sucess");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Passcreator Create Failure");
                return Result<string>.Failure(ex.Message);
            }
        }

        private Task<Result<string>> AddPasscreatorPass(PassCreatorCreateInput passCreatorCreateInput, string passCreatorApiKey, string passtemplateId)
        {
            string body = UpdateDeductible2PropertyName(JsonSerializer.Serialize(passCreatorCreateInput, _serializeOptions), passtemplateId);
            RestRequest request = new(Method.POST)
            {
                RequestFormat = DataFormat.Json
            };
            request.AddHeader("Authorization", passCreatorApiKey);
            request.AddParameter("text/plain", body, ParameterType.RequestBody);

            string endpoint = $"https://app.passcreator.com/api/pass?passtemplate={passtemplateId}&zapierStyle=falsetor.com/api/pass-template";
            _logger.LogInformation("InsuredNomadsIntegration Passcreator. PasstemplateId: {passtemplateId}. Passcreator create pass input: {passCreateInput}",
                passtemplateId, JsonSerializer.Serialize(passCreatorCreateInput, _serializeOptions));

            return ExecuteAsync(endpoint, request);
        }

        private static string UpdateDeductible2PropertyName(string jsonBody, string passTemplateId) =>
            passTemplateId != CoverGoTestPassTemplateId && !string.IsNullOrEmpty(jsonBody)
                ? jsonBody.Replace("5f2d48fe149481.48563719", "61cde6c093eb71.89194885")
                : jsonBody;
    }

    public class PassCreatorCreateInput
    {
        [JsonPropertyName("secondaryFields_1_Insured")]
        public string Deductible { get; set; }
        [JsonPropertyName("secondaryFields_0_Insured")]
        public string MoreInfo { get; set; }
        public string ExpirationDate { get; set; }
        public string ValidAsOfDate { get; set; }
        [JsonPropertyName("5f088f61511ff3.67734685")]
        public string PolicyNumber { get; set; }
        [JsonPropertyName("5f089024e5ad26.40596254")]
        public string StartDate { get; set; }
        [JsonPropertyName("5f08bcd23b9600.77212287")]
        public string EndDate { get; set; }
        [JsonPropertyName("5f08c6ee42ead6.79728835")]
        public string Name { get; set; }
        [JsonPropertyName("5f2d48fe1492c3.39310288")]
        public string PlanName { get; set; }
        [JsonPropertyName("5f2d48fe149481.48563719")]
        public string Deductible2 { get; set; }
        [JsonPropertyName("605a2e482467d1.79740739")]
        public string Amount { get; set; }
        public string UserProvidedId { get; set; }
    }

    public class PassCreatorCreateBatchAdd
    {
        public List<PassCreatorCreateInput> Inputs { get; set; }
        public string TemplateId { get; set; }
    }
}