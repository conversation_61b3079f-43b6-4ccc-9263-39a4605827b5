using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Gateway.Domain.FileSystem;
using CoverGo.Gateway.Domain.Notifications;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Templates;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Policies.Client;

using Microsoft.Extensions.Logging;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

using AddEmailAttachmentReferenceCommand = CoverGo.Gateway.Domain.Templates.AddEmailAttachmentReferenceCommand;
using AddEmailAttachmentTemplateCommand = CoverGo.Gateway.Domain.Templates.AddEmailAttachmentTemplateCommand;
using Exception = System.Exception;
using IssuePolicyCommand = CoverGo.Policies.Client.IssuePolicyCommand;
using Policy = CoverGo.Policies.Client.Policy;
using PolicyMember = CoverGo.Policies.Client.PolicyMember;
using PolicyMembersWhere = CoverGo.Policies.Client.PolicyMembersWhere;
using PolicyWhere = CoverGo.Policies.Client.PolicyWhere;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;
using RenderParameters = CoverGo.Gateway.Domain.Templates.RenderParameters;
using UpdatePolicyCommand = CoverGo.Policies.Client.UpdatePolicyCommand;

namespace CoverGo.Gateway.Infrastructure.InsuredNomadsServices.InsuredNomadsServicesCommands
{
    public class ProcessPolicyAndMembers : InsuredNomadsCommand
    {
        public override string Name { get; } = "processPolicyAndMembers";
        private readonly IAuthService _authService;
        private readonly IEntityService _entityService;
        private readonly IFileSystemService _fileSystemService;
        private readonly INotificationService _notificationService;
        private readonly IPoliciesClient _policyService;
        private readonly ITemplateService _templateService;
        private readonly ITransactionService _transactionService;


        public ProcessPolicyAndMembers(
            IAuthService authService,
            IEntityService entityService,
            IFileSystemService fileSystemService,
            INotificationService notificationService,
            IPoliciesClient policyService,
            ITemplateService templateService,
            ITransactionService transactionService,
            ILogger<ProcessPolicyAndMembers> logger) : base(logger)
        {
            _authService = authService;
            _entityService = entityService;
            _fileSystemService = fileSystemService;
            _notificationService = notificationService;
            _policyService = policyService;
            _templateService = templateService;
            _transactionService = transactionService;
        }
        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            try
            {
                string transactionId = inputJson.Value<string>("transactionId");
                if (transactionId == null) throw new Exception("TransactionId cannot be null");

                Transaction transaction = (await _transactionService.GetAsync(tenantId, new QueryArguments { Where = new TransactionWhere() { Id = transactionId } })).FirstOrDefault();
                if (transaction == null) throw new Exception($"Transaction not found with transactionId: {transactionId}");

                string policyId = transaction.PolicyId;

                Policy policy = (await _policyService.Policy_GetPoliciesAsync(tenantId, new QueryArgumentsOfPolicyWhere { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();
                if (policy == null) throw new Exception($"Policy not found with policyId: {policyId}");

                InsuredNomadsPolicyExtraFieldsFields insuredNomadsPolicyExtraFieldsFields = null;
                if (policy.ExtraFields != null)
                {
                    insuredNomadsPolicyExtraFieldsFields = JObject.Parse(((JObject)policy.ExtraFields).Value<string>("fields")).ToObject<InsuredNomadsPolicyExtraFieldsFields>();
                }

                IReadOnlyCollection<PolicyMember> policyMembers = await _policyService.PolicyMembers_QueryAsync(tenantId, new PolicyMembersWhere { PolicyId_In = new List<string> { policyId } });
                if (policyMembers == null) throw new Exception($"PolicyMembers to invite to login not found on policy with policyId: {policyId}");

                Result updatePolicyResult = await UpdatePolicyStartEndDatesAndMembers(tenantId, policyId, insuredNomadsPolicyExtraFieldsFields, policyMembers);
                if (updatePolicyResult.Status != "success") throw new Exception($"Policy update failure. Errors: {updatePolicyResult.GetAllErrors()}");

                ResultOfPolicyStatus issuePolicyResult = await _policyService.Policy_IssuePolicyAsync(tenantId, new IssuePolicyCommand { PolicyId = policyId }, null);
                var errors = ResultExtensions.GetAllErrors(issuePolicyResult?.Errors, issuePolicyResult?.Errors_2);
                if (issuePolicyResult?.Status != "success") throw new Exception($"Policy issuance failure. Errors: {errors}");

                Policy policyUpdated = (await _policyService.Policy_GetPoliciesAsync(tenantId, new QueryArgumentsOfPolicyWhere { Where = new PolicyWhere { Id = policyId } })).FirstOrDefault();

                IEnumerable<PolicyMember> primaryPolicyMembers = policyMembers.Where(x => JToken.FromObject(x.Fields).ToObject<InsuredNomadsPolicyMemberFields>().MemberType == "primary");
                Result invitePrimaryPolicyMembersToLoginResult = await InvitePrimaryPolicyMembersToLogin(tenantId, primaryPolicyMembers, insuredNomadsPolicyExtraFieldsFields, policyUpdated, policyMembers);
                if (invitePrimaryPolicyMembersToLoginResult.Status != "success") throw new Exception($"InvitePrimaryPolicyMembersToLogin failure. Errors {invitePrimaryPolicyMembersToLoginResult.GetAllErrors()}");

                if (((JObject)policyUpdated?.Fields)?.Value<string>("clientType") == "company")
                {
                    Result emailGroupTravelContantPersonResult = await EmailGroupTravelContantPerson(tenantId, policyUpdated, primaryPolicyMembers, insuredNomadsPolicyExtraFieldsFields, policyMembers);
                    if (emailGroupTravelContantPersonResult.Status != "success") throw new Exception($"EmailGroupTravelContantPerson failure. Errors {emailGroupTravelContantPersonResult.GetAllErrors()}");
                }

                MapDataToInputForSafetureIntegrations(inputJson, policyUpdated, insuredNomadsPolicyExtraFieldsFields, policyMembers);
                MapDataToInputForPasscreatorIntegrations(inputJson, policyUpdated, insuredNomadsPolicyExtraFieldsFields, policyMembers);

                return Result<string>.Success(policyUpdated.IssuerNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error with insured nomads processPolicyAndMembers");
                return Result<string>.Failure(new List<string> { "Error with insured nomads processPolicyAndMembers.", ex.Message });
            }
        }

        private async Task<Result> UpdatePolicyStartEndDatesAndMembers(string tenantId, string policyId, InsuredNomadsPolicyExtraFieldsFields insuredNomadsPolicyExtraFieldsFields, IReadOnlyCollection<PolicyMember> policyMembers)
        {
            List<string> policyMemberIds = new(policyMembers.Select(x => JToken.FromObject(x.Fields).ToObject<InsuredNomadsPolicyMemberFields>().IndividualId));

            UpdatePolicyCommand updatePolicyCommand = new()
            {
                InsuredIds = policyMemberIds,
                IsInsuredIdsChanged = true
            };

            if (insuredNomadsPolicyExtraFieldsFields is { StartDate: not null })
            {
                updatePolicyCommand.StartDate = DateTime.Parse(insuredNomadsPolicyExtraFieldsFields.StartDate);
                updatePolicyCommand.IsStartDateChanged = true;
            }

            if (insuredNomadsPolicyExtraFieldsFields is { EndDate: not null })
            {
                updatePolicyCommand.EndDate = DateTime.Parse(insuredNomadsPolicyExtraFieldsFields.EndDate);
                updatePolicyCommand.IsEndDateChanged = true;
            }

            return await _policyService.Policy_UpdatePolicyPUTAsync(tenantId, policyId, updatePolicyCommand);
        }

        private void MapDataToInputForPasscreatorIntegrations(JObject inputJson, Policy policy, InsuredNomadsPolicyExtraFieldsFields insuredNomadsPolicyExtraFieldsFields, IReadOnlyCollection<PolicyMember> policyMembers)
        {
            // Passcreator
            PassCreatorCreateBatchAdd passCreatorCreateBatchAdd = new() { TemplateId = insuredNomadsPolicyExtraFieldsFields.PasscreatorTemplateId, Inputs = new List<PassCreatorCreateInput> { } };

            foreach (PolicyMember policyMember in policyMembers)
            {
                IEnumerable<string> validPlanNameComponents = new string[]
                {
                    insuredNomadsPolicyExtraFieldsFields.ProductName, insuredNomadsPolicyExtraFieldsFields.PlanName
                }.Where(x => !string.IsNullOrWhiteSpace(x));
                passCreatorCreateBatchAdd.Inputs.Add(new PassCreatorCreateInput
                {
                    Name = ((JObject)policyMember?.Fields)?.Value<string>("name") ?? "Contract holder: " + policy.ContractHolder.Name,
                    StartDate = policy.StartDate?.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture),
                    EndDate = policy.EndDate?.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture),
                    ValidAsOfDate = (policyMember.StartDate ?? policy.StartDate).Value.ToString("yyyy-MM-dd H:mm"),
                    ExpirationDate = (policyMember.EndDate ?? policy.EndDate).Value.ToString("yyyy-MM-dd H:mm"),
                    PolicyNumber = policyMember.MemberId,
                    PlanName = string.Join(" - ", validPlanNameComponents),
                    Deductible = insuredNomadsPolicyExtraFieldsFields.Deductible,
                    Deductible2 = insuredNomadsPolicyExtraFieldsFields.Deductible,
                    Amount = insuredNomadsPolicyExtraFieldsFields.Deductible
                });
            }
            inputJson["passCreatorCreateInputs"] = JToken.FromObject(passCreatorCreateBatchAdd);
        }

#nullable enable
        private void MapDataToInputForSafetureIntegrations(JObject inputJson, Policy policy, InsuredNomadsPolicyExtraFieldsFields insuredNomadsPolicyExtraFieldsFields, IReadOnlyCollection<PolicyMember> policyMembers)
        {
            // Safeture
            const string safetureTypeId = "COMPANYINTERVAL";
            string? clientType = (policy.Fields as JObject)?.Value<string>("clientType");
            string? productName = insuredNomadsPolicyExtraFieldsFields?.ProductName;

            SafetureSubscriptionBatchAdd safetureSubscriptionBatchAdd = new() { Inputs = new List<SafetureSubscriptionAddInput>(), policyId = policy.Id };

            DateTime GetSafetureEndDate(PolicyMember? policyMember)
            {
                DateTime oneYearLater = DateTime.UtcNow.AddYears(1).AddDays(-1);

                if (policyMember != null && policyMember.EndDate > oneYearLater) return policyMember.EndDate.Value;
                return policy.EndDate > oneYearLater ? policy.EndDate.Value : oneYearLater;
            }

            SafetureSubscriptionAddInput PrepareSafetureSubscriptionAddInput(string subscriptionId, string description, DateTime endDate, int maxNoOfReceivers)
            {
                return new SafetureSubscriptionAddInput
                {
                    appsubscriptionid = subscriptionId,
                    description = description,
                    starttime = DateTime.UtcNow,
                    endtime = endDate,
                    maxnbrofreceivers = maxNoOfReceivers,
                    typeid = safetureTypeId
                };
            }

            switch (clientType)
            {
                case "individual":
                    // Identity the primary insured policy member (i.e., not dependent of anyone)
                    PolicyMember? primaryPolicyMember = policyMembers.FirstOrDefault(x => string.IsNullOrWhiteSpace(x.DependentOf));
                    safetureSubscriptionBatchAdd.Inputs.Add(
                        PrepareSafetureSubscriptionAddInput(
                            primaryPolicyMember?.MemberId ?? "",
                            $"Product: {productName}, Type: Individual(s), # Policy Members: {policyMembers.Count}, Primary Policy Number: {policy.IssuerNumber}",
                            GetSafetureEndDate(null),
                            policyMembers.Count)
                        );
                    break;
                case "company":
                    string? companyName = policy.ContractHolder?.Name;
                    foreach (PolicyMember policyMember in policyMembers)
                    {
                        safetureSubscriptionBatchAdd.Inputs.Add(
                            PrepareSafetureSubscriptionAddInput(
                                policyMember.MemberId ?? "",
                                $"Product: {productName}, Type: Company ({companyName}), Primary Policy Number: {policy.IssuerNumber}",
                                GetSafetureEndDate(policyMember),
                                1)
                        );
                    }
                    break;
            }

            _logger.LogInformation($"Safeture subscription batch add count: {safetureSubscriptionBatchAdd.Inputs.Count}");

            inputJson["safetureSubscriptionBatchAdd"] = JToken.FromObject(safetureSubscriptionBatchAdd);
        }
#nullable disable
        private async Task<Result> InvitePrimaryPolicyMembersToLogin(string tenantId, IEnumerable<PolicyMember> primaryPolicyMembers, InsuredNomadsPolicyExtraFieldsFields insuredNomadsPolicyExtraFieldsFields, Policy policy, IReadOnlyCollection<PolicyMember> policyMembers)
        {
            if (!primaryPolicyMembers.Any()) return Result.Failure("No primary policy members in input");
            foreach (PolicyMember primaryPolicyMember in primaryPolicyMembers)
            {
                const string clientId = "member_portal";
                const string memberPermissionGroup = "5ffe7a14ff68b200016bcc95";
                const string entityType = "individual";
                const string bccForTrustPilotIntegration = "<EMAIL>";
                const string bccForInsuredNoamds = "<EMAIL>";

                InsuredNomadsPolicyMemberFields primaryPolicyMemberFields = JToken.FromObject(primaryPolicyMember.Fields).ToObject<InsuredNomadsPolicyMemberFields>();

                Login existingLogin = await _authService.GetLoginByNameAsync(tenantId, primaryPolicyMemberFields.Email);
                List<string> bccsForAllEmails = tenantId == "insuredNomads" ? new List<string> { bccForTrustPilotIntegration, bccForInsuredNoamds, "<EMAIL>" } : new List<string> { bccForTrustPilotIntegration };
                const string datesFormat = "MM/dd/yyyy";

                EmailMessage invitationLoginToInsuredNomads = new()
                {
                    To = primaryPolicyMemberFields.Email,
                    Bccs = bccsForAllEmails,
                    From = "<EMAIL>",
                    FromName = "Insured Nomads",
                    Subject = "Welcome to Insured Nomads",
                    TemplateRendering = new TemplateRendering
                    {
                        TemplateId = insuredNomadsPolicyExtraFieldsFields.EmailTemplateId,
                        Input = new RenderParameters
                        {
                            Name = "data",
                            Content = JObject.Parse(JsonConvert.SerializeObject(new
                            {
                                name = primaryPolicyMemberFields.Name,
                                userName = HttpUtility.UrlEncode(primaryPolicyMemberFields.Email),
                                productName = insuredNomadsPolicyExtraFieldsFields.ProductName,
                                startDate = (DateTime.Parse(insuredNomadsPolicyExtraFieldsFields.StartDate)).ToString(datesFormat),
                                endDate = (DateTime.Parse(insuredNomadsPolicyExtraFieldsFields.EndDate)).ToString(datesFormat),
                                incMemberShipEndDate = (DateTime.Parse(insuredNomadsPolicyExtraFieldsFields.StartDate).AddYears(1).AddDays(-1)).ToString(datesFormat),
                                policyNumber = policy.IssuerNumber,
                                totalPremiumTotalInsureds = insuredNomadsPolicyExtraFieldsFields.TotalPremiumTotalInsureds,
                                totalPremiumSummary = insuredNomadsPolicyExtraFieldsFields.TotalPremiumSummary,
                                purchaseDate = DateTime.UtcNow.ToString(datesFormat),
                                plan = insuredNomadsPolicyExtraFieldsFields.PlanName,
                                formIssued = "PCU02012021",
                                members = policyMembers.Select(x => new { memberId = x.MemberId, fields = x.Fields }),
                                contractHolderFields = policy.ContractHolder.Fields,
                                policyProposalFields = insuredNomadsPolicyExtraFieldsFields.DataInput["policy"],
                                additionalFields = insuredNomadsPolicyExtraFieldsFields.AdditionalFields
                            }
                            )),
                            OverrideAttachmentReferences = insuredNomadsPolicyExtraFieldsFields.OverrideAttachmentReferences,
                            OverrideAttachmentTemplates = insuredNomadsPolicyExtraFieldsFields.OverrideAttachmentTemplates
                        }
                    }
                };

                if (existingLogin == null)
                {
                    CreateLoginCommand loginCommand = new()
                    {
                        ClientId = clientId,
                        Email = primaryPolicyMemberFields.Email,
                        Username = primaryPolicyMemberFields.Email,
                        TelephoneNumber = primaryPolicyMemberFields.PhoneNumber,
                        AppIdsToBeGrantedAccessTo = new List<string> { clientId },
                        EntityId = primaryPolicyMemberFields.IndividualId,
                        EntityType = entityType,
                        SendNotificationCommand = new SendNotificationCommand { EmailMessage = invitationLoginToInsuredNomads }
                    };
                    Result<CreatedStatus> loginCreatedResult = await _authService.CreateLoginAsync(tenantId, loginCommand);
                    if (loginCreatedResult.Status == "failure") return Result.Failure(loginCreatedResult.Errors);

                    Result result = await _authService.AddTargettedPermissionsAsync(tenantId, loginCreatedResult.Value.Id, new List<AddTargettedPermissionCommand>
                        {
                            new()
                            {
                                Type = "groups",
                                Value = memberPermissionGroup,
                                AddedById = "insuredNomadsProcess"
                            }
                        }
                    );
                    if (result.Status == "failure") return result;
                }
                else
                {
                    if (insuredNomadsPolicyExtraFieldsFields.WelcomeBackEmailTemplateId != null) invitationLoginToInsuredNomads.TemplateRendering.TemplateId = insuredNomadsPolicyExtraFieldsFields.WelcomeBackEmailTemplateId;
                    SendNotificationCommand sendNotificationCommand = new() { EmailMessage = invitationLoginToInsuredNomads };
                    Result result = await _notificationService.SendAsync(tenantId, sendNotificationCommand);
                    if (result.Status == "failure") return result;
                }

                if ((insuredNomadsPolicyExtraFieldsFields.OverrideAttachmentReferences?.Any() ?? false)
                    || (insuredNomadsPolicyExtraFieldsFields.OverrideAttachmentTemplates?.Any() ?? false))
                    await RenderAndUploadEmailAttachments(tenantId, invitationLoginToInsuredNomads, $"MemberPolicyDocument", policy.IssuerNumber);
                else
                    await RenderAndUploadEmailAttachment(tenantId, invitationLoginToInsuredNomads, $"MemberPolicyDocument/{policy.IssuerNumber}.pdf");
            }
            return Result.Success();
        }

        private async Task<Result> EmailGroupTravelContantPerson(string tenantId, Policy policy, IEnumerable<PolicyMember> primaryPolicyMembers, InsuredNomadsPolicyExtraFieldsFields insuredNomadsPolicyExtraFieldsFields, IReadOnlyCollection<PolicyMember> policyMembers)
        {
            PolicyMember primaryPolicyMember = primaryPolicyMembers.FirstOrDefault();
            const string bccForInsuredNoamds = "<EMAIL>";
            List<string> bccsForAllEmails = tenantId == "insuredNomads" ? new List<string> { bccForInsuredNoamds, "<EMAIL>" } : new List<string> { };
            InsuredNomadsPolicyMemberFields primaryPolicyMemberFields = JToken.FromObject(primaryPolicyMember?.Fields)?.ToObject<InsuredNomadsPolicyMemberFields>();
            const string datesFormat = "MM/dd/yyyy";

            EmailMessage invitationLoginToInsuredNomads = new()
            {
                To = ((JObject)policy.ContractHolder.Fields).Value<string>("email"),
                Bccs = bccsForAllEmails,
                From = "<EMAIL>",
                FromName = "Insured Nomads",
                Subject = "Welcome to Insured Nomads",
                TemplateRendering = new TemplateRendering
                {
                    TemplateId = insuredNomadsPolicyExtraFieldsFields.GroupCertificateEmailTemplateId,
                    Input = new RenderParameters
                    {
                        Name = "data",
                        Content = JObject.Parse(JsonConvert.SerializeObject(new
                        {
                            name = primaryPolicyMemberFields.Name,
                            userName = HttpUtility.UrlEncode(primaryPolicyMemberFields.Email),
                            productName = insuredNomadsPolicyExtraFieldsFields.ProductName,
                            startDate = (DateTime.Parse(insuredNomadsPolicyExtraFieldsFields.StartDate)).ToString(datesFormat),
                            endDate = (DateTime.Parse(insuredNomadsPolicyExtraFieldsFields.EndDate)).ToString(datesFormat),
                            incMemberShipEndDate = (DateTime.Parse(insuredNomadsPolicyExtraFieldsFields.StartDate).AddYears(1).AddDays(-1)).ToString(datesFormat),
                            policyNumber = policy.IssuerNumber,
                            totalPremiumTotalInsureds = insuredNomadsPolicyExtraFieldsFields.TotalPremiumTotalInsureds,
                            totalPremiumSummary = insuredNomadsPolicyExtraFieldsFields.TotalPremiumSummary,
                            purchaseDate = DateTime.UtcNow.ToString(datesFormat),
                            plan = insuredNomadsPolicyExtraFieldsFields.PlanName,
                            formIssued = "PCU02012021",
                            members = policyMembers.Select(x => new { memberId = x.MemberId, fields = x.Fields }),
                            contractHolderFields = policy.ContractHolder.Fields,
                            policyProposalFields = insuredNomadsPolicyExtraFieldsFields.DataInput["policy"],
                            additionalFields = insuredNomadsPolicyExtraFieldsFields.AdditionalFields
                        }
                        )),
                        OverrideAttachmentReferences = insuredNomadsPolicyExtraFieldsFields.OverrideAttachmentReferences,
                        OverrideAttachmentTemplates = insuredNomadsPolicyExtraFieldsFields.OverrideAttachmentTemplates
                    }
                }
            };

            if ((insuredNomadsPolicyExtraFieldsFields.OverrideAttachmentReferences?.Any() ?? false)
                || (insuredNomadsPolicyExtraFieldsFields.OverrideAttachmentTemplates?.Any() ?? false))
                await RenderAndUploadEmailAttachments(tenantId, invitationLoginToInsuredNomads, $"GroupPolicyDocument", policy.IssuerNumber);
            else
                await RenderAndUploadEmailAttachment(tenantId, invitationLoginToInsuredNomads, $"GroupPolicyDocument/{policy.IssuerNumber}.pdf");


            SendNotificationCommand sendNotificationCommand = new() { EmailMessage = invitationLoginToInsuredNomads };
            return await _notificationService.SendAsync(tenantId, sendNotificationCommand);
        }

        private async Task RenderAndUploadEmailAttachments(string tenantId, EmailMessage email, string directoryPath, string policyNumber)
        {
            Result<EmailRendered> emailRenderedResult = await _templateService.RenderEmailAsync(tenantId, email.TemplateRendering.TemplateId, true, email.TemplateRendering.Input);
            if (emailRenderedResult.Status != "success") _logger.LogWarning("Failed to render email template for upload");

            foreach (EmailAttachment attachment in emailRenderedResult.Value.Attachments)
            {
                if (attachment.Bytes != null)
                    await _fileSystemService.UploadFileAsync(tenantId, null, new UploadFileCommand { Key = $"{directoryPath}/{policyNumber}_{attachment.FileName}", Content = attachment.Bytes, IsPublic = false });
            }
        }

        private async Task RenderAndUploadEmailAttachment(string tenantId, EmailMessage email, string fileKey)
        {
            Result<EmailRendered> emailRenderedResult = await _templateService.RenderEmailAsync(tenantId, email.TemplateRendering.TemplateId, true, email.TemplateRendering.Input);
            if (emailRenderedResult.Status != "success")
            {
                _logger.LogWarning("Failed to render email template for upload");
            }

            byte[] attachmentFile = emailRenderedResult.Value.Attachments.FirstOrDefault()?.Bytes;
            if (attachmentFile == null) return;

            await _fileSystemService.UploadFileAsync(tenantId, null, new UploadFileCommand { Key = fileKey, Content = attachmentFile, IsPublic = false });
        }
    }

    public class InsuredNomadsPolicyExtraFieldsFields
    {
        public JToken DataInput { get; set; }
        public string Locale { get; set; }
        public string CurrencyCode { get; set; }
        public string StartDate { get; set; }
        public string EndDate { get; set; }
        public string IndividualId { get; set; }
        public string Deductible { get; set; }
        public string PlanName { get; set; }
        public string EmailTemplateId { get; set; }
        public List<AddEmailAttachmentTemplateCommand> OverrideAttachmentTemplates { get; set; }
        public List<AddEmailAttachmentReferenceCommand> OverrideAttachmentReferences { get; set; }
        public string WelcomeBackEmailTemplateId { get; set; }
        public string MemberPortalUrl { get; set; }
        public string ProductName { get; set; }
        public string TotalPremiumSummary { get; set; }
        public string TotalPremiumTotalInsureds { get; set; }
        public string PasscreatorTemplateId { get; set; }
        public string GroupCertificateEmailTemplateId { get; set; }
        public JToken AdditionalFields { get; set; }
    }

    public class InsuredNomadsPolicyMemberFields
    {
        public bool HaveInsuranceInHomeCountry { get; set; }
        public string MemberType { get; set; }
        public int mMxBenefit { get; set; }
        public string DeductibleAmount { get; set; }
        public string Name { get; set; }
        public string DateOfBirth { get; set; }
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public string PassportCountry { get; set; }
        public string Relationship { get; set; }
        public string DependentOf { get; set; }
        public string IndividualId { get; set; }
    }
}