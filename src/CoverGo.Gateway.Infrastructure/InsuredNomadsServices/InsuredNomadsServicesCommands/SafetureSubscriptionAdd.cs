using CoverGo.DomainUtils;
using CoverGo.Policies.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.InsuredNomadsServices.InsuredNomadsServicesCommands
{
    public class SafetureSubscriptionAdd : InsuredNomadsCommand
    {
        public override string Name { get; } = "safetureSubscriptionAdd";
        private readonly IPoliciesClient _policyService;

        public SafetureSubscriptionAdd(
            IPoliciesClient policyService,
            ILogger<SafetureSubscriptionAdd> logger) : base(logger)
        {
            _policyService = policyService;
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            string safetureApiKey = Environment.GetEnvironmentVariable($"{tenantId.ToUpper()}_SAFETURE_API_KEY") ?? "StagingCoverGoEnvINCPass123#!";
            string safetureBaseEndpoint = tenantId == "insuredNomads" ? "https://api.safeture.com" : "https://vapi.safeture.com";

            try
            {
                SafetureSubscriptionBatchAdd batchAddInput = inputJson.GetValue("safetureSubscriptionBatchAdd").ToObject<SafetureSubscriptionBatchAdd>();

                bool saveSafetureIdToPolicyMember = true;
                string policyId = batchAddInput.policyId;
                if (policyId == null) saveSafetureIdToPolicyMember = false; // throw new Exception("policyId in SafetureSubscriptionBatchAdd is null."); NOTE: After FE update master enable this for all.

                IReadOnlyCollection<PolicyMember> policyMembers = await _policyService.PolicyMembers_QueryAsync(tenantId, new PolicyMembersWhere { PolicyId_In = new List<string> { policyId } });
                if (policyMembers == null) throw new Exception($"PolicyMembers to invite to login not found on policy with policyId: {policyId}");

                Result<string> accessTokenRequest = await GenerateSafetureAuthenticationToken(safetureBaseEndpoint, safetureApiKey);
                if (accessTokenRequest.Status == "failure") return accessTokenRequest;

                var memberIdsAdded = new List<string>();
                foreach (SafetureSubscriptionAddInput addinput in batchAddInput.Inputs)
                {
                    try
                    {
                        Result<string> memberAddResult = await AddSafetureSubscription(addinput, safetureBaseEndpoint, accessTokenRequest.Value);
                        if (memberAddResult.Status == "success")
                        {
                            memberIdsAdded.Add(addinput.appsubscriptionid);
                            if (saveSafetureIdToPolicyMember)
                            {
                                _logger.LogInformation("Executing update safeture id into policy member");
                                Result updatePolicyMemberFields = await AddSafetureIdToMemberFields(tenantId, policyMembers, addinput.appsubscriptionid, JObject.Parse(memberAddResult.Value)["data"]["id"].ToString());
                                if (updatePolicyMemberFields.Status != "success") _logger.LogError($"Failed to update policy member fields for memberId: {addinput.appsubscriptionid}, safeture internal id: {memberAddResult.Value}");
                                else _logger.LogInformation("Executed update safeture id into policy member successfully for memberId: {MemberId}, safeture internal id: {SafetureId}", addinput.appsubscriptionid, memberAddResult.Value);
                            }
                        }
                        else
                        {
                            _logger.LogError($"Failed to add safeture subscribtion: {string.Join(",", memberAddResult.Errors)}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error with SafetureSubscriptionAdd API");
                    }
                }
                return Result<string>.Success($"Successfully added members: {String.Join(", ", memberIdsAdded.ToArray())}");

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error with SafetureSubscriptionAdd API");
                return Result<string>.Failure(new List<string> { "Error with SafetureSubscriptionAdd API", ex.Message });
            }
        }

        private async Task<Result<string>> AddSafetureSubscription(SafetureSubscriptionAddInput input, string safetureBaseEndpoint, string accessToken)
        {
            RestRequest request = new(Method.POST);
            request.AddHeader("Authorization", "Bearer " + accessToken);
            request.AddHeader("Content-Type", "application/json");
            request.RequestFormat = DataFormat.Json;
            request.AddJsonBody(input);
            string endpoint = safetureBaseEndpoint + "/subscriptions";
            Result<string> safetureResult = await ExecuteAsync(endpoint, request);
            if (safetureResult.Status == "success")
                _logger.LogInformation($"InsuredNomadsIntegration Safeture. endpoint: {endpoint}. Safeture create input: {JsonConvert.SerializeObject(input)}. Response: {safetureResult.Value}.");
            else _logger.LogError($"InsuredNomadsIntegration Safeture Failure. endpoint: {endpoint}. Safeture create input: {JsonConvert.SerializeObject(input)}. Response: {safetureResult.Value}. Errors: {String.Join(". ", safetureResult.Errors)}.");
            return safetureResult;
        }

        public static async Task<Result<string>> GenerateSafetureAuthenticationToken(string safetureBaseEndpoint, string safetureApiKey)
        {
            RestClient client = new(safetureBaseEndpoint + "/accesstoken")
            {
                Timeout = ApiTimeoutSetting,
            };
            RestRequest request = new(Method.POST);
            request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
            request.AddParameter("userid", "api-juvo-covergo");
            request.AddParameter("password", safetureApiKey);
            IRestResponse response = await client.ExecuteAsync(request);
            if (response.IsSuccessful != true)
            {
                return response.ResponseStatus == ResponseStatus.TimedOut ?
                Result<string>.Failure($"Get Safeture API Auth timeout (>{ApiTimeoutSetting / 1000}s). Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}") :
                Result<string>.Failure($"Get Safeture API Auth token failure. Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}");
            }
            string authToken = JObject.Parse(response.Content).Value<string>("data");
            return (authToken == null) ?
                Result<string>.Failure($"Safeture API: No access_token found in response content. Response Content: {response.Content}") :
                Result<string>.Success(authToken);
        }

        private async Task<Result> AddSafetureIdToMemberFields(string tenantId, IReadOnlyCollection<PolicyMember> policyMembers, string memberId, string safetureId)
        {
            try
            {
                PolicyMember policyMember = policyMembers.FirstOrDefault(m => m.MemberId == memberId);
                if (policyMember?.Fields == null)
                    return Result.Success();
                
                JToken memberFields = JToken.FromObject(policyMember.Fields);
                memberFields["safetureInternalId"] = safetureId;
                return await _policyService.PolicyMembers_UpdateAsync(tenantId,
                    new PolicyMemberUpdateCommand
                    {
                        PolicyId = policyMember.PolicyId,
                        MemberId = memberId,
                        Fields = JsonConvert.SerializeObject(memberFields),
                        Timestamp = policyMember.Timestamp,
                        CreatedAt = policyMember.CreatedAt,
                        ModifiedById = policyMember.CreatedById,
                        PlanId = policyMember.PlanId,
                        LastModifiedAt = policyMember.LastModifiedAt,
                        StartDate = policyMember.StartDate,
                        EndDate = policyMember.EndDate
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to update policy member fields for memberId: {memberId}, safeture internal id: {safetureId}. Member (appsubscriptionId) may not be found on the policy");
                throw new Exception($"Failed to update policy member fields for memberId: {memberId}, safeture internal id: {safetureId}. Member (appsubscriptionId) may not be found on the policy", ex);
            }
        }

    }

    public class SafetureSubscriptionAddInput
    {
        [JsonRequiredAttribute]
        public string appsubscriptionid { get; set; }
        [JsonRequiredAttribute]
        public DateTime? starttime { get; set; }
        [JsonRequiredAttribute]
        public DateTime? endtime { get; set; }
        public string description { get; set; }
        [JsonRequiredAttribute]
        public string typeid { get; set; }
        [JsonRequiredAttribute]
        public int? maxnbrofreceivers { get; set; }
        public bool? disabled { get; set; }
    }

    public class SafetureSubscriptionBatchAdd
    {
        public List<SafetureSubscriptionAddInput> Inputs { get; set; }
        public string policyId { get; set; }
    }
}