using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using CoverGo.CryptoUtils;
using CoverGo.DomainUtils;
using CoverGo.Policies.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;

namespace CoverGo.Gateway.Infrastructure.InsuredNomadsServices.InsuredNomadsServicesCommands;

public class IssuePolicyForActiveMember : InsuredNomadsCommand
{
    private readonly IPoliciesClient _policiesClient;
    public IssuePolicyForActiveMember(ILogger<InsuredNomadsCommand> logger, IPoliciesClient policiesClient) : base(logger)
    {
        _policiesClient = policiesClient ?? throw new ArgumentNullException(nameof(policiesClient));
    }

    public override string Name => "issuePolicyForActiveMember";

    public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
    {
        IssuePolicyForActiveMemberInput input = IssuePolicyForActiveMemberInput.FromJson(inputJson);

        if (string.IsNullOrWhiteSpace(input.MainInsuredId))
        {
            _logger.LogInformation("MainInsuredId is empty");
            return Result<string>.Failure("MainInsuredId is empty"); // Validation error
        }

        if (!input.IsStatusUpdate())
        {
            _logger.LogInformation("Member status is not update - {status}", input.Status);
            return Result<string>.Success("Member status is not update");
        }

        if (!await IsMainInsuredActive(tenantId, input.MainInsuredId))
        {
            _logger.LogInformation("MainInsured is not yet active - {mainInsuredId}", input.MainInsuredId);
            return Result<string>.Success("MainInsured is not yet active");
        }

        PolicyMember primaryInsured = await GetPrimaryInsured(tenantId, input.MainInsuredId);

        if (primaryInsured is null)
        {
            _logger.LogError("Primary insured not found for {mainInsuredId}", input.MainInsuredId);
            return Result<string>.Failure("Primary insured not found");
        }

        ResultOfPolicyStatus policyIssuanceResult = await IssuePolicy(tenantId, primaryInsured);

        if (!(policyIssuanceResult?.IsSuccess ?? false))
        {
            _logger.LogError("Error while issuing policy {policyId} for member {memberId}", primaryInsured.PolicyId, primaryInsured.MemberId);
        }

        await NotifyInsuredNomads(tenantId, primaryInsured);

        return Result<string>.Success("Issued policy");
    }

    private async Task<PolicyMember> GetPrimaryInsured(string tenantId, string mainInsuredId) =>
        (await _policiesClient.PolicyMembers_QueryAsync(tenantId, new()
        {
            Where = new()
            {
                Where = new()
                {
                    Fields = new()
                    {
                        Path = "mainInsuredId",
                        Condition = FieldsWhereCondition.Equals,
                        Value = new()
                        {
                            StringValue = mainInsuredId
                        }
                    }
                }
            }
        })).FirstOrDefault();

    private async Task<bool> IsMainInsuredActive(string tenantId, string mainInsuredId)
    {
        string expaTpaBaseUrl = Environment.GetEnvironmentVariable($"{tenantId}_ExpaTPA_Base_Url");
        string expaTpaCcode = Environment.GetEnvironmentVariable($"{tenantId}_ExpaTPA_Ccode");
        string expaTpaSkey = Environment.GetEnvironmentVariable($"{tenantId}_ExpaTPA_Skey");
        string expaTpaOwner = Environment.GetEnvironmentVariable($"{tenantId}_ExpaTPA_Owner");
        string expaTpaAesKey = Environment.GetEnvironmentVariable($"{tenantId}_ExpaTPA_AesKey");
        string proxyKey = Environment.GetEnvironmentVariable($"{tenantId}_ExpaTPA_ProxyKey");

        if (string.IsNullOrWhiteSpace(expaTpaBaseUrl) || string.IsNullOrWhiteSpace(expaTpaCcode) ||
            string.IsNullOrWhiteSpace(expaTpaSkey) || string.IsNullOrWhiteSpace(expaTpaOwner) ||
            string.IsNullOrWhiteSpace(expaTpaAesKey))
        {
            _logger.LogError("ExpaTPA environment variables not found for tenant {tenantId}", tenantId);
            throw new InvalidOperationException("ExpaTPA environment variables not found");
        }

        Result<string> authResult = await GetExpaTpaAuthToken(expaTpaBaseUrl, expaTpaCcode, expaTpaSkey, expaTpaOwner, proxyKey);

        if (!authResult.IsSuccess)
        {
            _logger.LogError("Error while getting ExpaTPA auth token for tenant {tenantId} - {result}", tenantId, JsonConvert.SerializeObject(authResult));
            return false;
        }

        string authToken = JToken.Parse(authResult.Value).SelectToken("token")?.ToString();
        if (string.IsNullOrWhiteSpace(authToken))
        {
            _logger.LogError("ExpaTPA auth token not found for tenant {tenantId}", tenantId);
            throw new Exception("ExpaTPA auth token not found");
        }

        Result<string> memberPolicyStatusResult = await GetMemberPolicyStatus(expaTpaBaseUrl, expaTpaCcode, expaTpaAesKey, authToken, mainInsuredId, proxyKey);

        if (!memberPolicyStatusResult.IsSuccess)
        {
            _logger.LogError("Error while getting ExpaTPA member details for tenant {tenantId} - {result}", tenantId, JsonConvert.SerializeObject(memberPolicyStatusResult));
            return false;
        }

        return memberPolicyStatusResult.Value.ToLower() == "active";
    }

    private async Task<Result<string>> GetExpaTpaAuthToken(string expaTpaBaseUrl, string expaTpaCcode, string expaTpaSkey, string expaTpaOwner, string proxyKey)
    {
        string expaTpaAuthEndpoint = $"{expaTpaBaseUrl}/v1/core/auth";
        RestRequest request = new(Method.GET)
        {
            RequestFormat = DataFormat.Json
        };
        Dictionary<string, string> headers = new()
        {
            { "CCode", expaTpaCcode },
            { "SKey", expaTpaSkey },
            { "Owner", expaTpaOwner }
        };
        AddProxyKeyIfAvailable(headers, proxyKey);
        request.AddHeaders(headers);

        return await ExecuteAsync(expaTpaAuthEndpoint, request);
    }

    private async Task<Result<string>> GetMemberPolicyStatus(string expaTpaBaseUrl, string expaTpaCcode, string expaTpaAesKey,
        string authToken, string mainInsuredId, string proxyKey)
    {
        using Aes aesAlg = Aes.Create();
        aesAlg.Key = Convert.FromBase64String(expaTpaAesKey);
        aesAlg.IV = new byte[16];

        string expaTpaMemberEndpoint = $"{expaTpaBaseUrl}/v1/core/policymemberdetail";
        ShowMemberDetailInput input = new()
        {
            MainInsuredId = mainInsuredId
        };
        string encryptedInput = Convert.ToBase64String(AesHelpers.Encrypt(JsonConvert.SerializeObject(input), aesAlg));
        RestRequest request = new(Method.POST)
        {
            RequestFormat = DataFormat.Json
        };
        Dictionary<string, string> headers = new()
        {
            { "CCode", expaTpaCcode },
            { "Token", authToken }
        };
        AddProxyKeyIfAvailable(headers, proxyKey);
        request.AddHeaders(headers);
        request.AddJsonBody(new { value = encryptedInput });

        Result<string> result = await ExecuteAsync(expaTpaMemberEndpoint, request);

        if (!result.IsSuccess)
        {
            return result;
        }

        string encryptedResult = result.Value;
        byte[] cipher = Convert.FromBase64String(encryptedResult);

        string decryptedResult = AesHelpers.Decrypt(cipher, aesAlg);
        _logger.LogInformation("Decrypted member details: {decryptedResult}", decryptedResult);

        ShowMemberDetailResponse memberDetail = JsonConvert.DeserializeObject<ShowMemberDetailResponse>(decryptedResult);
        return Result<string>.Success(memberDetail.PolicyStatus);
    }

    private Task<ResultOfPolicyStatus> IssuePolicy(string tenantId, PolicyMember policyMember) =>
        _policiesClient.Policy_IssuePolicyAsync(tenantId, new IssuePolicyCommand()
        {
            PolicyId = policyMember.PolicyId
        }, null);

    private async Task NotifyInsuredNomads(string tenantId, PolicyMember primaryInsured)
    {
        var policy = await _policiesClient.Policy_GetPolicyAsync(tenantId, primaryInsured.PolicyId, null);
        string insuredNomadsCrmBaseUrl = Environment.GetEnvironmentVariable($"{tenantId}_CRM_Base_Url");

        if (string.IsNullOrWhiteSpace(insuredNomadsCrmBaseUrl))
        {
            _logger.LogError("InsuredNomads CRM environment variables not found for tenant {tenantId}", tenantId);
            throw new InvalidOperationException("InsuredNomads CRM environment variables not found");
        }

        RestRequest request = new(Method.POST)
        {
            RequestFormat = DataFormat.Json
        };
        request.AddJsonBody(new PolicyIssuanceNotification
        {
            PolicyNumber = policy.IssuerNumber,
            MemberId = primaryInsured.MemberId,
            Status = "active",
            Env = tenantId.EndsWith("uat") ? "UAT" : "PROD"
        });

        Result<string> result = await ExecuteAsync(insuredNomadsCrmBaseUrl, request);

        if (!result.IsSuccess)
        {
            _logger.LogError("Error while notifying InsuredNomads CRM for tenant {tenantId} - {result}", tenantId, JsonConvert.SerializeObject(result));
        }
    }

    private void AddProxyKeyIfAvailable(Dictionary<string, string> headers, string proxyKey)
    {
        if (!string.IsNullOrWhiteSpace(proxyKey))
        {
            headers.Add("Authorization", $"Basic {proxyKey}");
        }
    }

    public sealed class IssuePolicyForActiveMemberInput
    {
        public string MainInsuredId { get; set; }
        public string Status { get; set; }
        public bool IsStatusUpdate() => !string.IsNullOrWhiteSpace(Status) && Status.ToLower() == "update";
        public static IssuePolicyForActiveMemberInput FromJson(JObject json) => json.ToObject<IssuePolicyForActiveMemberInput>();
    }

    public sealed class ShowMemberDetailInput
    {
        public string MainInsuredId { get; set; }
    }

    public sealed class ShowMemberDetailResponse
    {
        public string PolicyStatus { get; set; }
    }

    public sealed class PolicyIssuanceNotification
    {
        public string PolicyNumber { get; set; }
        public string MemberId { get; set; }
        public string Status { get; set; }
        public string Env { get; set; }
    }
}