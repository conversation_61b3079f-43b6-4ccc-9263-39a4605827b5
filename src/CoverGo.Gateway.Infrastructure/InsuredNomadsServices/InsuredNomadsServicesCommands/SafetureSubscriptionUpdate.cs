using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using CoverGo.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.InsuredNomadsServices.InsuredNomadsServicesCommands
{
    public class SafetureSubscriptionUpdate : InsuredNomadsCommand
    {
        private static readonly JsonSerializerOptions _serializeOptions = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase, DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull };

        public override string Name { get; } = "safetureSubscriptionUpdate";

        public SafetureSubscriptionUpdate(ILogger<SafetureSubscriptionUpdate> logger) : base(logger)
        { }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            SafetureSubscriptionBatchInput batchInput = inputJson.GetValue("safetureSubscriptionBatch")?.ToObject<SafetureSubscriptionBatchInput>();
            SafetureSubscriptionUpdateInput updateInput = inputJson.GetValue("safetureSubscriptionUpdate")?.ToObject<SafetureSubscriptionUpdateInput>();
            
            if (updateInput != null)
                return await Update(tenantId, updateInput);
            if (batchInput != null)
                return await Batch(tenantId, batchInput);
            
            return Result<string>.Failure("inputJson.safetureSubscriptionBatch or inputJson.safetureSubscriptionUpdate must be given");
        }


        private async Task<Result<string>> Update(string tenantId, SafetureSubscriptionUpdateInput input)
        {
            string safetureApiKey = Environment.GetEnvironmentVariable($"{tenantId.ToUpper()}_SAFETURE_API_KEY") ?? "StagingCoverGoEnvINCPass123#!";
            string safetureBaseEndpoint = tenantId == "insuredNomads" ? "https://api.safeture.com" : "https://vapi.safeture.com";
            
            try
            {
                if (input.SafetureInternalId == null)
                    throw new Exception("SafetureInternalId cannot be null");

                Result<string> accessTokenRequest =
                    await SafetureSubscriptionAdd.GenerateSafetureAuthenticationToken(safetureBaseEndpoint,
                        safetureApiKey);
                if (accessTokenRequest.Status == "failure") return accessTokenRequest;

                Result<string> result = await UpdateSafetureSubscription(input, safetureBaseEndpoint,
                    accessTokenRequest.Value);

                return result;

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error with SafetureSubscriptionUpdate API");
                return Result<string>.Failure(new List<string> { "Error with SafetureSubscriptionUpdate API", ex.Message });
            }
        }
        
        private async Task<Result<string>> Batch(string tenantId, SafetureSubscriptionBatchInput input)
        {
            try
            {
                if (input.SafetureSubscriptionUpdates == null) return Result<string>.Failure("inputJson.safetureSubscriptionBatch.safetureSubscriptionUpdates must be given");
                if (!input.SafetureSubscriptionUpdates.Any()) return Result<string>.Success("SafetureSubscriptionBatch succeed");

                Result<string>[] results = await input.SafetureSubscriptionUpdates.ParallelSelectAsync(
                    safetureSubscriptionUpdateInput => Update(tenantId, safetureSubscriptionUpdateInput),
                    new ParallelRunOptions() { MaxDegreeOfParallelism = 5 });

                if(results.All(x => x.IsSuccess)) return Result<string>.Success("SafetureSubscriptionBatch succeed");

                IEnumerable<string> errors = results.Where(x => !x.IsSuccess).SelectMany(x => x.Errors);
                
                return Result<string>.Failure(errors);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error with SafetureSubscriptionUpdate API");
                return Result<string>.Failure(new List<string> { "Error with SafetureSubscriptionUpdate API", ex.Message });
            }
        }

        private async Task<Result<string>> UpdateSafetureSubscription(SafetureSubscriptionUpdateInput input, string safetureBaseEndpoint, string accessToken)
        {
            string body = System.Text.Json.JsonSerializer.Serialize(input, _serializeOptions);

            RestRequest request = new(Method.PUT);
            request.AddHeader("Authorization", "Bearer " + accessToken);
            request.AddHeader("Content-Type", "application/json");
            request.RequestFormat = DataFormat.Json;
            request.AddParameter("text/plain", body, ParameterType.RequestBody);

            string endpoint = safetureBaseEndpoint + $"/subscriptions/{input.SafetureInternalId}";

            Result<string> safetureResult = await ExecuteAsync(endpoint, request);
            if (safetureResult.Status == "success")
                _logger.LogInformation($"InsuredNomadsIntegration Safeture. endpoint: {endpoint}. Safeture update input: {JsonSerializer.Serialize(input)}. Response: {safetureResult.Value}.");
            else _logger.LogError($"InsuredNomadsIntegration Safeture Failure. endpoint: {endpoint}. Safeture update input: {JsonSerializer.Serialize(input)}. Response: {safetureResult.Value}. Errors: {String.Join(". ", safetureResult.Errors)}.");
            return safetureResult;
        }

        public class SafetureSubscriptionUpdateInput
        {
            [JsonIgnore(Condition = JsonIgnoreCondition.Always)]
            public string SafetureInternalId { get; set; }
            public string appsubscriptionid { get; set; }
            public DateTime? starttime { get; set; }
            public DateTime? endtime { get; set; }
            public string description { get; set; }
            public string typeid { get; set; }
            public int? maxnbrofreceivers { get; set; }
            public bool? disabled { get; set; }
        }
        
        public class SafetureSubscriptionBatchInput
        {
            public List<SafetureSubscriptionUpdateInput> SafetureSubscriptionUpdates { get; set; }
        }
    }
}