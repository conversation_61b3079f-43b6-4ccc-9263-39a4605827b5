using System;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using RestSharp;

namespace CoverGo.Gateway.Infrastructure.InsuredNomadsServices.InsuredNomadsServicesCommands
{
    public class PasscreatorGetTemplates : InsuredNomadsCommand
    {
        public override string Name { get; } = "passcreatorGetTemplates";
        public PasscreatorGetTemplates(ILogger<PasscreatorCreate> logger) : base(logger)
        {

        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            string passCreatorApiKey = Environment.GetEnvironmentVariable($"{tenantId.ToUpper()}_PASSCREATOR_API_KEY") ?? "MioW!4vWJNHMZ7uBMSj9Fdr/E5fSFE788m1zzcwjucIkpZD0QSXnGr3dAOuh&Ji80MG6JU7hOBif.0PU";

            RestRequest request = new(Method.GET);
            request.AddHeader("Authorization", passCreator<PERSON>piKey);

            string endpoint = "https://app.passcreator.com/api/pass-template";

            return await ExecuteAsync(endpoint, request);
        }
    }
}