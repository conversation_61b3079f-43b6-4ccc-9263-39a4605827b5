using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.InsuredNomadsServices
{
    public abstract class InsuredNomadsCommand
    {
        protected readonly ILogger<InsuredNomadsCommand> _logger;
        public abstract string Name { get; }
        public abstract Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson);
        protected static int ApiTimeoutSetting = 10000; // 10000 milliseconds == 10 seconds
        protected InsuredNomadsCommand(ILogger<InsuredNomadsCommand> logger)
        {
            _logger = logger;
        }

        public virtual async Task<Result<string>> ExecuteAsync(string endpoint, RestRequest request)
        {
            try
            {
                RestClient client = new(endpoint)
                {
                    Timeout = ApiTimeoutSetting,
                };

                IRestResponse response = await client.ExecuteAsync(request);
                if (response.IsSuccessful != true)
                    return response.ResponseStatus == ResponseStatus.TimedOut ?
                    Result<string>.Failure($"InsuredNomad API timeout (>{ApiTimeoutSetting / 1000}s). Status Code:{response.StatusCode}. ErrorException {response.ErrorException}. ErrorMessage: {response.ErrorMessage}") :
                    new Result<string> { Status = "failure", Value = response.Content, Errors = new List<string> { response.ErrorMessage, response.StatusDescription } };
                return Result<string>.Success(response.Content);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InsuredNomad API integration error");
                return Result<string>.Failure(ex.Message);
            }
        }
    }
}