﻿using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.L10n;
using CoverGo.Gateway.Domain.Products;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.L10n
{
    public class CoverGoMarketingService : IMarketingService
    {
        private readonly HttpClient _client;

        public CoverGoMarketingService(HttpClient client)
        {
            _client = client;
        }

        public async Task<IDictionary<ProductId, IEnumerable<Tag>>> GetTagsPerProductIdDictionaryAsync(string tenantId, string clientId, IEnumerable<ProductId> ids)
        {
            IEnumerable<ProductTagMapping> tagMappings = await _client.GenericPostAsync<IEnumerable<ProductTagMapping>, IEnumerable<ProductId>>($"{tenantId}/api/v1/marketing/{clientId}/tags", ids);

            return tagMappings?.ToDictionary(a => a.ProductId, v => v.Tags) ?? new Dictionary<ProductId, IEnumerable<Tag>> { };
        }

    }
}
