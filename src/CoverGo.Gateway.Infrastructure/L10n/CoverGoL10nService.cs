﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.L10n;
using MoreLinq;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.L10n
{
    public class CoverGoL10nService : IL10nService
    {
        private readonly HttpClient _client;

        public CoverGoL10nService(HttpClient client)
        {
            _client = client;
        }

        public async Task<Result> MigrateL10ns(string tenantId) => await _client.GenericGetAsync<Result>($"{tenantId}/api/v1/l10n/migrate");

        public Task<IEnumerable<string>> GetKeysFromValueContainsAsync(string tenantId, string locale, string valueContains, string keyStartsWith, string keyEndsWith) =>
            _client.GenericGetAsync<IEnumerable<string>>($"{tenantId}/api/v1/l10n/keysFromValueContains/{locale}/{System.Uri.EscapeDataString(valueContains)}/{keyStartsWith}/{keyEndsWith}");

        public async Task<IDictionary<string, string>> GetL10nsAsync(string tenantId, string locale, IEnumerable<string> ids)
        {
            ids = ids.Distinct();

            IEnumerable<IEnumerable<string>> batches = ids.Batch(1000);

            IEnumerable<Task<IDictionary<string, string>>> tasks = batches.Select(b => _client.GenericPostAsync<IDictionary<string, string>, IEnumerable<string>>(
                $"{tenantId}/api/v1/l10n/{locale}", b));

            IDictionary<string, string>[] responses = await Task.WhenAll(tasks);

            return responses.SelectMany(dict => dict).ToDictionary(pair => pair.Key, pair => pair.Value);
        }

        public Task UpsertAsync(string tenantId, UpsertL10nCommand command) =>
            _client.GenericPostAsync<object, UpsertL10nCommand>($"{tenantId}/api/v1/l10n", command);

        public Task<Result> DeleteAsync(string tenantId, DeleteL10nCommand command) =>
            _client.GenericPostAsync<Result, DeleteL10nCommand>($"{tenantId}/api/v1/l10n/delete", command);

        public Task<Result> DeleteAllAsync(string tenantId, DeleteAllL10nCommand command) =>
            _client.GenericPostAsync<Result, DeleteAllL10nCommand>($"{tenantId}/api/v1/l10n/deleteall", command);
    }
}
