﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Binder;
using CoverGo.Gateway.Domain.Cases;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Users;

using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Infrastructure.Cases
{
    public class CoverGoCaseService : ICaseService
    {
        private readonly HttpClient _client;

        public CoverGoCaseService(HttpClient client)
        {
            _client = client;
        }

        public Task<IEnumerable<Case>> GetAsync(string tenantId, QueryArguments queryArguments)
            => _client.GenericPostAsync<IEnumerable<Case>, QueryArguments>($"{tenantId}/api/v1/cases/query", queryArguments);

        public Task<IEnumerable<CasesReport>> GetReportAsync(string tenantId, QueryArguments queryArguments) 
            => _client.GenericPostAsync<IEnumerable<CasesReport>, QueryArguments>($"{tenantId}/api/v1/cases/queryReport", queryArguments);

        public Task<long> GetTotalCountAsync(string tenantId, CaseWhere where)
            => _client.GenericPostAsync<long, CaseWhere>($"{tenantId}/api/v1/cases/totalCount", where);
        
        public async Task<IDictionary<string, Proposal>> GetProposalDictionaryAsync(string tenantId, CaseWhere where)
        {
            IEnumerable<Case> cases = await GetAsync(tenantId, new QueryArguments { Where = where });
            IEnumerable<Proposal> proposals = cases?.SelectMany(c => c.Proposals).Where(p => where.Proposals_contains.Id_in.Contains(p.Id));
            var dict = proposals?.ToDictionary(p => p.Id);

            return dict;
        }

        public async Task<IDictionary<string, Proposal>> GetProposalDictionaryByPolicyIdAsync(string tenantId, CaseWhere where)
        {
            IEnumerable<Case> cases = await GetAsync(tenantId, new QueryArguments { Where = where });
            IEnumerable<Proposal> proposals = cases?.SelectMany(c => c.Proposals);
            var dict = proposals?.SelectMany(a => a.PolicyIds?.Select(p => new KeyValuePair<string, Proposal>(p, a)))?.ToDictionary(pair => pair.Key, pair => pair.Value);

            return dict;
        }

        public Task<IEnumerable<string>> GetIdsAsync(string tenantId, QueryArguments queryArguments) =>
            _client.GenericPostAsync<IEnumerable<string>, QueryArguments>($"{tenantId}/api/v1/cases/queryids", queryArguments);
        public Task<List<DetailedEventLog>> GetEventsAsync(string tenantId, EventQuery query)
            => _client.GenericPostAsync<List<DetailedEventLog>, EventQuery>($"{tenantId}/api/v1/cases/events", query);

        public Task<Result<string>> CreateCaseAsync(string tenantId, CreateCaseCommand command)
            => _client.GenericPostAsync<Result<string>, CreateCaseCommand>($"{tenantId}/api/v1/cases/create", command);

        public Task<Result> UpdateCaseAsync(string tenantId, string caseId, UpdateCaseCommand command)
            => _client.GenericPostAsync<Result, UpdateCaseCommand>($"{tenantId}/api/v1/cases/{caseId}/update", command);
        public Task<Result> DeleteCaseAsync(string tenantId, string caseId, DeleteCaseCommand command)
            => _client.GenericPostAsync<Result, DeleteCaseCommand>($"{tenantId}/api/v1/cases/{caseId}/delete", command);

        public Task<Result<string>> AddProposalAsync(string tenantId, string caseId, AddProposalCommand command, string accessToken = null)
            => _client.GenericPostAsync<Result<string>, AddProposalCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/add?accessToken={accessToken}", command);

        public Task<Result<string>> CopyProposalAsync(string tenantId, string caseId, CopyProposalCommand command) =>
            _client.GenericPostAsync<Result<string>, CopyProposalCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/copy", command);
        public Task<Result<string>> RenewProposalAsync(string tenantId, string caseId, RenewProposalCommand command, string accessToken) =>
            _client.GenericPostAsync<Result<string>, RenewProposalCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/renew?accessToken={accessToken}", command);

        public Task<Result> IssueProposalAsync(string tenantId, string caseId, IssueProposalCommand command)
          => _client.GenericPostAsync<Result, IssueProposalCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/issue", command);
        public Task<Result> RejectProposalAsync(string tenantId, string caseId, RejectProposalCommand command)
          => _client.GenericPostAsync<Result, RejectProposalCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/reject", command);
        public Task<Result> UpdateProposalAsync(string tenantId, string caseId, UpdateProposalCommand command)
            => _client.GenericPostAsync<Result, UpdateProposalCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/update", command);
        public Task<Result> RemoveProposalAsync(string tenantId, string caseId, RemoveCommand command)
            => _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/remove", command);

        public Task<Result<string>> AddOfferAsync(string tenantId, string caseId, AddOfferCommand command, string accessToken = null)
            => _client.GenericPostAsync<Result<string>, AddOfferCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/add?accessToken={accessToken}", command);
        public Task<Result> UpdateOfferAsync(string tenantId, string caseId, UpdateOfferCommand command)
            => _client.GenericPostAsync<Result, UpdateOfferCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/update", command);
        public Task<Result> RemoveOfferAsync(string tenantId, string caseId, RemoveOfferFromProposalCommand command)
            => _client.GenericPostAsync<Result, RemoveOfferFromProposalCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/remove", command);

        public Task<Result<CreatedStatus>> AddFactToCaseAsync(string tenantId, string caseId, AddFactCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddFactCommand>($"{tenantId}/api/v1/cases/{caseId}/facts/add", command);
        public Task<Result> UpdateFactOfCaseAsync(string tenantId, string caseId, UpdateFactCommand command)
            => _client.GenericPostAsync<Result, UpdateFactCommand>($"{tenantId}/api/v1/cases/{caseId}/facts/update", command);
        public Task<Result> RemoveFactFromCaseAsync(string tenantId, string caseId, RemoveFactCommand command)
            => _client.GenericPostAsync<Result, RemoveFactCommand>($"{tenantId}/api/v1/cases/{caseId}/facts/remove", command);
        public Task<Result> CaseFactBatchAsync(string tenantId, string caseId, FactCommandBatch batch)
            => _client.GenericPostAsync<Result, FactCommandBatch>($"{tenantId}/api/v1/cases/{caseId}/facts/batch", batch);

        public Task<Result> AddNoteToCaseAsync(string tenantId, string caseId, AddNoteCommand command)
            => _client.GenericPostAsync<Result, AddNoteCommand>($"{tenantId}/api/v1/cases/{caseId}/notes/add", command);
        public Task<Result> UpdateNoteOfCaseAsync(string tenantId, string caseId, UpdateNoteCommand command)
            => _client.GenericPostAsync<Result, UpdateNoteCommand>($"{tenantId}/api/v1/cases/{caseId}/notes/update", command);
        public Task<Result> RemoveNoteFromCaseAsync(string tenantId, string caseId, RemoveNoteCommand command)
            => _client.GenericPostAsync<Result, RemoveNoteCommand>($"{tenantId}/api/v1/cases/{caseId}/notes/remove", command);

        public Task<Result<string>> AddStakeholderToCaseAsync(string tenantId, string caseId, AddStakeholderCommand command)
            => _client.GenericPostAsync<Result<string>, AddStakeholderCommand>($"{tenantId}/api/v1/cases/{caseId}/stakeholders/add", command);
        public Task<Result> UpdateStakeholderOfCaseAsync(string tenantId, string caseId, UpdateStakeholderCommand command)
            => _client.GenericPostAsync<Result, UpdateStakeholderCommand>($"{tenantId}/api/v1/cases/{caseId}/stakeholders/update", command);
        public Task<Result> RemoveStakeholderFromCaseAsync(string tenantId, string caseId, RemoveStakeholderCommand command)
            => _client.GenericPostAsync<Result, RemoveStakeholderCommand>($"{tenantId}/api/v1/cases/{caseId}/stakeholders/remove", command);

        public Task<Result<string>> AddStakeholderToProposalAsync(string tenantId, string caseId, AddStakeholderCommand command)
          => _client.GenericPostAsync<Result<string>, AddStakeholderCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/stakeholders/add", command);
        public Task<Result> UpdateStakeholderOfProposalAsync(string tenantId, string caseId, UpdateStakeholderCommand command)
            => _client.GenericPostAsync<Result, UpdateStakeholderCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/stakeholders/update", command);
        public Task<Result> RemoveStakeholderFromProposalAsync(string tenantId, string caseId, RemoveStakeholderCommand command)
            => _client.GenericPostAsync<Result, RemoveStakeholderCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/stakeholders/remove", command);

        public Task<Result<CreatedStatus>> GeneratePoliciesFromProposalAsync(string tenantId, string caseId, GeneratePoliciesFromProposalCommand command, string accessToken = null)
            => _client.GenericPostAsync<Result<CreatedStatus>, GeneratePoliciesFromProposalCommand>($"{tenantId}/api/v1/cases/{caseId}/generatePolicies?accessToken={accessToken}", command);


        public Task<Result<string>> AddDiscountToProposalOfferAsync(string tenantId, string caseId, AddDiscountCommand command)
            => _client.GenericPostAsync<Result<string>, AddDiscountCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/discounts/add", command);
        public Task<Result> UpdateDiscountOfProposalOfferAsync(string tenantId, string caseId, UpdateDiscountCommand command)
            => _client.GenericPostAsync<Result, UpdateDiscountCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/discounts/update", command);
        public Task<Result> RemoveDiscountFromProposalOfferAsync(string tenantId, string caseId, RemoveDiscountFromOfferCommand command)
            => _client.GenericPostAsync<Result, RemoveDiscountFromOfferCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/discounts/remove", command);

        public Task<Result<string>> AddLoadingToProposalOfferAsync(string tenantId, string caseId, AddLoadingCommand command)
            => _client.GenericPostAsync<Result<string>, AddLoadingCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/loadings/add", command);
        public Task<Result> UpdateLoadingOfProposalOfferAsync(string tenantId, string caseId, UpdateLoadingCommand command)
            => _client.GenericPostAsync<Result, UpdateLoadingCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/loadings/update", command);
        public Task<Result> RemoveLoadingFromProposalOfferAsync(string tenantId, string caseId, RemoveLoadingCommand command)
            => _client.GenericPostAsync<Result, RemoveLoadingCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/loadings/remove", command);

        public Task<Result<CreatedStatus>> AddExclusionToProposalOfferAsync(string tenantId, string caseId, AddExclusionCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddExclusionCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/exclusions/add", command);
        public Task<Result> RemoveExclusionFromProposalOfferAsync(string tenantId, string caseId, RemoveExclusionCommand command)
            => _client.GenericPostAsync<Result, RemoveExclusionCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/exclusions/remove", command);

        public Task<Result<string>> AddClauseToOfferAsync(string tenantId, string caseId, AddClauseCommand command)
            => _client.GenericPostAsync<Result<string>, AddClauseCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/clauses/add", command);
        public Task<Result> UpdateClauseOfOfferAsync(string tenantId, string caseId, UpdateClauseCommand command)
            => _client.GenericPostAsync<Result, UpdateClauseCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/clauses/update", command);
        public Task<Result> RemoveClauseFromOfferAsync(string tenantId, string caseId, RemoveClauseCommand command)
            => _client.GenericPostAsync<Result, RemoveClauseCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/clauses/remove", command);
        public Task<Result> ProposalOfferClauseBatchAsync(string tenantId, string caseId, ClauseBatchCommand batch)
            => _client.GenericPostAsync<Result, ClauseBatchCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/clauses/batch", batch);

        public Task<Result> ProposalOfferJacketBatchAsync(string tenantId, string caseId, JacketInstanceBatchCommand instanceBatch)
            => _client.GenericPostAsync<Result, JacketInstanceBatchCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/jackets/batch", instanceBatch);
        public Task<Result> RemoveJacketFromOfferAsync(string tenantId, string caseId, RemoveJacketInstanceCommand instanceCommand)
            => _client.GenericPostAsync<Result, RemoveJacketInstanceCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/jackets/remove", instanceCommand);

        public Task<Result> ProposalOfferFactBatchAsync(string tenantId, string caseId, FactCommandBatch batch)
         => _client.GenericPostAsync<Result, FactCommandBatch>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/facts/batch", batch);
        public Task<Result<string>> AddFactToProposalOfferAsync(string tenantId, string caseId, AddFactCommand command)
           => _client.GenericPostAsync<Result<string>, AddFactCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/facts/add", command);
        public Task<Result> UpdateFactOfProposalOfferAsync(string tenantId, string caseId, UpdateFactCommand command)
            => _client.GenericPostAsync<Result, UpdateFactCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/facts/update", command);
        public Task<Result> RemoveFactFromProposalOfferAsync(string tenantId, string caseId, RemoveFactCommand command)
            => _client.GenericPostAsync<Result, RemoveFactCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/facts/remove", command);

        public Task<Result> UpsertBenefitOptionOfProposalOfferAsync(string tenantId, string caseId, UpsertBenefitOptionCommand command)
            => _client.GenericPostAsync<Result, UpsertBenefitOptionCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/benefitOptions/upsert", command);
        public Task<Result> RemoveBenefitOptionFromProposalOfferAsync(string tenantId, string caseId, RemoveBenefitOptionCommand command)
            => _client.GenericPostAsync<Result, RemoveBenefitOptionCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/benefitOptions/remove", command);

        public Task<Result<string>> AddStakeholderToProposalOfferAsync(string tenantId, string caseId, AddStakeholderCommand command)
           => _client.GenericPostAsync<Result<string>, AddStakeholderCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/stakeholders/add", command);
        public Task<Result> UpdateStakeholderOfProposalOfferAsync(string tenantId, string caseId, UpdateStakeholderCommand command)
            => _client.GenericPostAsync<Result, UpdateStakeholderCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/stakeholders/update", command);
        public Task<Result> RemoveStakeholderFromProposalOfferAsync(string tenantId, string caseId, RemoveStakeholderCommand command)
            => _client.GenericPostAsync<Result, RemoveStakeholderCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/stakeholders/remove", command);

        public Task<Result> AddAssociatedContractToProposalOfferAsync(string tenantId, string caseId, AddAssociatedContractCommand command)
         => _client.GenericPostAsync<Result, AddAssociatedContractCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/associatedContracts/add", command);
        public Task<Result> RemoveAssociatedContractFromProposalOfferAsync(string tenantId, string caseId, RemoveAssociatedContractCommand command)
        => _client.GenericPostAsync<Result, RemoveAssociatedContractCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/associatedContracts/remove", command);
        public Task<Result> AddCommissionToOfferAsync(string tenantId, string caseId, AddCommissionCommand command) =>
            _client.GenericPostAsync<Result, AddCommissionCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/commissions/add", command);
        public Task<Result> UpdateCommissionOfOfferAsync(string tenantId, string caseId, UpdateCommissionCommand command) =>
            _client.GenericPostAsync<Result, UpdateCommissionCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/commissions/update", command);
        public Task<Result> RemoveCommissionFromOfferAsync(string tenantId, string caseId, RemoveCommissionCommand command) =>
            _client.GenericPostAsync<Result, RemoveCommissionCommand>($"{tenantId}/api/v1/cases/{caseId}/proposals/offers/commissions/remove", command);

        public Task<Result<CreatedStatus>> AddBeneficiaryEligibilityAsync(string tenantId, string caseId, AddBeneficiaryEligibilityCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, AddBeneficiaryEligibilityCommand>($"{tenantId}/api/v1/cases/{caseId}/beneficiaryEligibilities/add", command);

        public Task<Result> UpdateBeneficiaryEligibilityAsync(string tenantId, string caseId, UpdateBeneficiaryEligibilityCommand command) =>
            _client.GenericPostAsync<Result, UpdateBeneficiaryEligibilityCommand>($"{tenantId}/api/v1/cases/{caseId}/beneficiaryEligibilities/update", command);

        public Task<Result> RemoveBeneficiaryEligibilityAsync(string tenantId, string caseId, RemoveCommand command) =>
            _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/cases/{caseId}/beneficiaryEligibilities/remove", command);

        public Task<Result<CreatedStatus>> AddPaymentInfoAsync(string tenantId, string caseId, AddPaymentInfoCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, AddPaymentInfoCommand>($"{tenantId}/api/v1/cases/{caseId}/paymentInfos/add", command);

        public Task<Result> UpdatePaymentInfoAsync(string tenantId, string caseId, UpdatePaymentInfoCommand command) =>
            _client.GenericPostAsync<Result, UpdatePaymentInfoCommand>($"{tenantId}/api/v1/cases/{caseId}/paymentInfos/update", command);

        public Task<Result> RemovePaymentInfoAsync(string tenantId, string caseId, RemoveCommand command) =>
            _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/cases/{caseId}/paymentInfos/remove", command);

        public Task<IReadOnlyCollection<DataSchema>> GetDataSchemasAsync(string tenantId, DataSchemaWhere where) =>
           _client.GenericPostAsync<IReadOnlyCollection<DataSchema>, DataSchemaWhere>($"{tenantId}/api/v1/dataschemas/query", where);

        public Task<Result<CreatedStatus>> CreateDataSchemaAsync(string tenantId, CreateDataSchemaCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreateDataSchemaCommand>($"{tenantId}/api/v1/dataschemas", command);

        public Task<Result> UpdateDataSchemaAsync(string tenantId, UpdateDataSchemaCommand command) =>
           _client.GenericPutAsync<Result, UpdateDataSchemaCommand>($"{tenantId}/api/v1/dataschemas", command);

        public Task<Result> DeleteDataSchemaAsync(string tenantId, string dataSchemaId, DeleteCommand command) =>
            _client.GenericDeleteAsync<Result, DeleteCommand>($"{tenantId}/api/v1/dataschemas/{dataSchemaId}", command);
        
        public Task<Result> AddUiSchemaToDataSchemaAsync(string tenantId, AddUiSchemaToDataSchemaCommand command) =>
            _client.GenericPostAsync<Result, AddUiSchemaToDataSchemaCommand>($"{tenantId}/api/v1/dataschemas/uiSchemas", command);
        
        public Task<Result> RemoveUiSchemaFromDataSchemaAsync(string tenantId, RemoveUiSchemaFromDataSchemaCommand command) =>
            _client.GenericDeleteAsync<Result, RemoveUiSchemaFromDataSchemaCommand>($"{tenantId}/api/v1/dataschemas/uiSchemas", command);
    }
}
