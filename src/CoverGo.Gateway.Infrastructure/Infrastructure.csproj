<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>CoverGo.Gateway.Infrastructure</AssemblyName>
    <RootNamespace>CoverGo.Gateway.Infrastructure</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="CoverGo.FeatureManagement" />
    <PackageReference Include="CoverGo.Threading.Tasks" />
    <PackageReference Include="CsvHelper" />
    <PackageReference Include="Fluid.Core" />
    <PackageReference Include="IdentityModel" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" />
    <PackageReference Include="Microsoft.FeatureManagement" />
    <PackageReference Include="morelinq" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="Pipedrive.net" />
    <PackageReference Include="RestSharp" />
    <PackageReference Include="System.ServiceModel.Http" />
    <PackageReference Include="BouncyCastle" />
    <PackageReference Include="Otp.NET" />
		<PackageReference Include="OpenSSL.PrivateKeyDecoder" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.Applications.Clients" />
    <PackageReference Include="CoverGo.Proxies.Product" />
    <PackageReference Include="CoverGo.CryptoUtils" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CoverGo.Gateway.Domain\Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.ServiceModel" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="BouncyCastle" />
  </ItemGroup>
</Project>
