﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Education;
using CoverGo.Gateway.Domain.Education.CourseProgressions;
using CoverGo.Gateway.Domain.Education.Courses;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Infrastructure.Education
{
    public class CoverGoEducationService : IEducationService
    {
        private readonly HttpClient _client;

        public CoverGoEducationService(HttpClient client)
        {
            _client = client;
        }

        public Task<long> GetCoursesTotalCountAsync(string tenantId, CourseWhere where) =>
             _client.GenericPostAsync<long, CourseWhere>($"{tenantId}/api/v1/courses/totalCount", where);

        public Task<IEnumerable<Course>> GetCoursesAsync(string tenantId, QueryArguments queryArguments) =>
             _client.GenericPostAsync<IEnumerable<Course>, QueryArguments>($"{tenantId}/api/v1/courses/query", queryArguments);

        public Task<Result<CreatedStatus>> CreateCourseAsync(string tenantId, CreateCourseCommand command) =>
             _client.GenericPostAsync<Result<CreatedStatus>, CreateCourseCommand>($"{tenantId}/api/v1/courses/create", command);

        public Task<Result> UpdateCourseAsync(string tenantId, string courseId, UpdateCourseCommand command) =>
            _client.GenericPostAsync<Result, UpdateCourseCommand>($"{tenantId}/api/v1/courses/{courseId}/update", command);

        public Task<Result> DeleteCourseAsync(string tenantId, string courseId, DeleteCommand command) =>
            _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/courses/{courseId}/delete", command);

        public Task<Result<CreatedStatus>> AddSectionToCourseAsync(string tenantId, string courseId, AddSectionToCourseCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, AddSectionToCourseCommand>($"{tenantId}/api/v1/courses/{courseId}/addSection", command);
        public Task<Result> RemoveSectionFromCourseAsync(string tenantId, string courseId, RemoveSectionFromCourseCommand command) =>
            _client.GenericPostAsync<Result, RemoveSectionFromCourseCommand>($"{tenantId}/api/v1/courses/{courseId}/removeSection", command);
        public Task<Result<CreatedStatus>> AddSectionToSectionAsync(string tenantId, string courseId, AddSectionToSectionCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, AddSectionToSectionCommand>($"{tenantId}/api/v1/courses/{courseId}/addSectionToSection", command);
        public Task<Result<CreatedStatus>> AddLessonToSectionAsync(string tenantId, string courseId, AddLessonToSectionCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, AddLessonToSectionCommand>($"{tenantId}/api/v1/courses/{courseId}/addLesson", command);
        public Task<Result> UpdateLessonOfSectionAsync(string tenantId, string courseId, UpdateLessonOfSectionCommand command) =>
            _client.GenericPostAsync<Result, UpdateLessonOfSectionCommand>($"{tenantId}/api/v1/courses/{courseId}/updateLesson", command);
        public Task<Result> RemoveLessonFromSectionAsync(string tenantId, string courseId, RemoveLessonFromSectionCommand command) =>
            _client.GenericPostAsync<Result, RemoveLessonFromSectionCommand>($"{tenantId}/api/v1/courses/{courseId}/removeLesson", command);
        public Task<Result<CreatedStatus>> AddLessonItemAsync(string tenantId, string courseId, AddLessonItemCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, AddLessonItemCommand>($"{tenantId}/api/v1/courses/{courseId}/addLessonItem", command);
        public Task<Result> UpdateLessonItemAsync(string tenantId, string courseId, UpdateLessonItemCommand command) =>
            _client.GenericPostAsync<Result, UpdateLessonItemCommand>($"{tenantId}/api/v1/courses/{courseId}/updateLessonItem", command);
        public Task<Result> RemoveLessonItemAsync(string tenantId, string courseId, RemoveLessonItemCommand command) =>
            _client.GenericPostAsync<Result, RemoveLessonItemCommand>($"{tenantId}/api/v1/courses/{courseId}/removeLessonItem", command);
        public Task<Result<CreatedStatus>> AddOptionToLessonItemAsync(string tenantId, string courseId, AddOptionToLessonItemCommand command) =>
    _client.GenericPostAsync<Result<CreatedStatus>, AddOptionToLessonItemCommand>($"{tenantId}/api/v1/courses/{courseId}/addOptionToLessonItem", command);
        public Task<Result> UpdateOptionOfLessonItemAsync(string tenantId, string courseId, UpdateOptionOfLessonItemCommand command) =>
            _client.GenericPostAsync<Result, UpdateOptionOfLessonItemCommand>($"{tenantId}/api/v1/courses/{courseId}/updateOptionOfLessonItem", command);
        public Task<Result> RemoveOptionFromLessonItemAsync(string tenantId, string courseId, RemoveOptionFromLessonItemCommand command) =>
            _client.GenericPostAsync<Result, RemoveOptionFromLessonItemCommand>($"{tenantId}/api/v1/courses/{courseId}/removeOptionFromLessonItem", command);

        public Task<long> GetCourseProgressionsTotalCountAsync(string tenantId, CourseProgressionWhere where) =>
             _client.GenericPostAsync<long, CourseProgressionWhere>($"{tenantId}/api/v1/courseProgressions/totalCount", where);
        public async Task<ILookup<string, CourseProgression>> GetCourseProgressionsByEntityIdsLookupAsync(string tenantId, CourseProgressionWhere where) =>
            (await GetCourseProgressionsAsync(tenantId, new QueryArguments { Where = where })).ToLookup(x => x.EntityId);

        public Task<IEnumerable<CourseProgression>> GetCourseProgressionsAsync(string tenantId, QueryArguments queryArguments) =>
             _client.GenericPostAsync<IEnumerable<CourseProgression>, QueryArguments>($"{tenantId}/api/v1/courseProgressions/query", queryArguments);

        public Task<Result<CreatedStatus>> CreateCourseProgressionAsync(string tenantId, CreateCourseProgressionCommand command) =>
             _client.GenericPostAsync<Result<CreatedStatus>, CreateCourseProgressionCommand>($"{tenantId}/api/v1/courseProgressions/create", command);

        public Task<Result> UpdateCourseProgressionAsync(string tenantId, string progressionId, UpdateCourseProgressionCommand command) =>
            _client.GenericPostAsync<Result, UpdateCourseProgressionCommand>($"{tenantId}/api/v1/courseProgressions/{progressionId}/update", command);

        public Task<Result> DeleteCourseProgressionAsync(string tenantId, string progressionId, DeleteCommand command) =>
            _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/courseProgressions/{progressionId}/delete", command);

        public Task<Result<CreatedStatus>> AddLessonCompletionToCourseProgressionAsync(string tenantId, string progressionId, AddLessonCompletionToCourseProgressionCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, AddLessonCompletionToCourseProgressionCommand>($"{tenantId}/api/v1/courseProgressions/{progressionId}/addLessonCompletion", command);
        public Task<Result> UpdateLessonCompletionOfCourseProgressionAsync(string tenantId, string progressionId, UpdateLessonCompletionOfCourseProgressionCommand command) =>
            _client.GenericPostAsync<Result, UpdateLessonCompletionOfCourseProgressionCommand>($"{tenantId}/api/v1/courseProgressions/{progressionId}/updateLessonCompletion", command);
        public Task<Result> RemoveLessonCompletionFromCourseProgressionAsync(string tenantId, string progressionId, RemoveLessonCompletionFromCourseProgressionCommand command) =>
            _client.GenericPostAsync<Result, RemoveLessonCompletionFromCourseProgressionCommand>($"{tenantId}/api/v1/courseProgressions/{progressionId}/removeLessonCompletion", command);

    }
}
