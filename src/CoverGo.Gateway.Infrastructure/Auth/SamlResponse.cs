﻿using System;
using System.IO;
using System.Xml;
using System.Security.Cryptography.Xml;
using System.Text;
using CoverGo.Gateway.Domain.Auth;
using System.Collections.Generic;
using System.Security.Cryptography.X509Certificates;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Infrastructure.Auth;

public class SamlResponse
{
    private XmlDocument _xmlDoc;
    private Certificate _certificate;
    private readonly AppSetting _appSetting;
    private readonly AccountSetting _accountSetting;

   
    public SamlResponse(AccountSetting accountSettings, AppSetting appSetting)
    {
        _appSetting = appSetting;
        _accountSetting = accountSettings;
        _certificate = new Certificate();

        if (_accountSetting.Certificate != null)
        {
            _certificate.LoadCertificate(_accountSetting.Certificate);
        }

    }
    
    public string LoadXmlFromBase64(string response)
    {

        var xml = GetStringFromBase64(response);

        if (!string.IsNullOrEmpty(xml))
        {
            LoadXml(xml);

            return xml;
        }

        return string.Empty;
    }

    public IList<SamlAttributes> GetAttributes()
    {
        var attributes = new List<SamlAttributes>();

        XmlNamespaceManager manager = new XmlNamespaceManager(_xmlDoc.NameTable);
        manager.AddNamespace("saml", "urn:oasis:names:tc:SAML:2.0:assertion");
        manager.AddNamespace("samlp", "urn:oasis:names:tc:SAML:2.0:protocol");

        var nodes = _xmlDoc.SelectSingleNode("/samlp:Response/saml:Assertion/saml:AttributeStatement", manager);
        foreach (XmlNode node in nodes)
        {
            var idx = node.Attributes[0].Value.LastIndexOf("/");
            var value = node.ChildNodes[0].InnerText;
            var name = node.Attributes[0].Value.Substring(idx + 1);

            attributes.Add(new SamlAttributes() { Name = name, Value = value});
        }

        return attributes;
    }
    public string GetNameID()
    {
        XmlNamespaceManager manager = new XmlNamespaceManager(_xmlDoc.NameTable);
        manager.AddNamespace("ds", SignedXml.XmlDsigNamespaceUrl);
        manager.AddNamespace("saml", "urn:oasis:names:tc:SAML:2.0:assertion");
        manager.AddNamespace("samlp", "urn:oasis:names:tc:SAML:2.0:protocol");

        XmlNode node = _xmlDoc.SelectSingleNode("/samlp:Response/saml:Assertion/saml:Subject/saml:NameID", manager);
        return node.InnerText;
    }
    public Result<SamlSSOResponse> IsValid()
    {
        XmlNamespaceManager manager = new XmlNamespaceManager(_xmlDoc.NameTable);
        manager.AddNamespace("ds", SignedXml.XmlDsigNamespaceUrl);
        manager.AddNamespace("saml", "urn:oasis:names:tc:SAML:2.0:assertion");
        manager.AddNamespace("samlp", "urn:oasis:names:tc:SAML:2.0:protocol");

        if (string.IsNullOrEmpty(_accountSetting.Certificate))
        {
            return Result<SamlSSOResponse>.Failure("Missing Account Certificate Value.");
        }

        byte[] bytes = Convert.FromBase64String(_accountSetting.Certificate);
        X509Certificate2 certificate = new X509Certificate2(bytes);

        var validSignature = IsValidSignature(_xmlDoc, manager, certificate);
        if (!validSignature)
        {
            return Result<SamlSSOResponse>.Failure("Invalid Certificate Signature.");
        }

        var issuer = _xmlDoc.SelectSingleNode("//saml:Issuer", manager).InnerText;
        var success = _xmlDoc.SelectSingleNode("//samlp:StatusCode", manager).Attributes[0].Value.Contains("success", StringComparison.OrdinalIgnoreCase);

        if(!success && !issuer.Equals(_accountSetting.IDPIssuerUrl, StringComparison.OrdinalIgnoreCase))
        {
            return Result<SamlSSOResponse>.Failure($"Success Code {success}. Invalid Issuer {issuer}");
        }

        var notBefore = GetConditionTimeStamp("NotBefore");
        var status = !notBefore.HasValue || (notBefore <= DateTime.Now);

        if(!status)
        {
            return Result<SamlSSOResponse>.Failure($"Invalid NotBefore Timestamp {notBefore}");
        }

        var notOnOrAfter = GetConditionTimeStamp("NotOnOrAfter");
        status &= !notOnOrAfter.HasValue || (notOnOrAfter > DateTime.Now);

        return !status
            ? Result<SamlSSOResponse>.Failure($"Invalid NotOnOrAfter Timestamp {notOnOrAfter}")
            : Result<SamlSSOResponse>.Success(new SamlSSOResponse());


    }

    private bool IsValidSignature(XmlDocument xdoc, XmlNamespaceManager xmlnsm, X509Certificate2 certificate)
    {
        XmlNodeList nodeList = xdoc.SelectNodes("//ds:Signature", xmlnsm);

        if (nodeList.Count == 0) return false;

        SignedXml signedXml = new SignedXml(xdoc);
        signedXml.LoadXml((XmlElement)nodeList[0]);
        return signedXml.CheckSignature(certificate, true);
    }

    private static string GetStringFromBase64(string data)
    {
        try
        {
            var enc = new UTF8Encoding();
            var base64String = Convert.FromBase64String(data);
            return enc.GetString(base64String);
        }
        catch (Exception)
        {
            return string.Empty;
        }
    }

    private DateTime? GetConditionTimeStamp(string attribute)
    {
        XmlNamespaceManager manager = new XmlNamespaceManager(_xmlDoc.NameTable);
        manager.AddNamespace("saml", "urn:oasis:names:tc:SAML:2.0:assertion");
        manager.AddNamespace("samlp", "urn:oasis:names:tc:SAML:2.0:protocol");

        var node = _xmlDoc.SelectSingleNode("/samlp:Response/saml:Assertion/saml:Conditions", manager);
        var value = string.Empty;

        if (node != null && node.Attributes != null && node.Attributes[attribute] != null)
        {
            value = node.Attributes[attribute].Value;
        }

        return !string.IsNullOrEmpty(value) ? DateTime.Parse(value) : (DateTime?)null;
    }
    
    private void LoadXml(string xml)
    {
        try
        {
            _xmlDoc = new XmlDocument();
            _xmlDoc.PreserveWhitespace = true;
            _xmlDoc.XmlResolver = null;
            _xmlDoc.LoadXml(xml);
        }
        catch (Exception)
        {
            throw;
        }

    }

}