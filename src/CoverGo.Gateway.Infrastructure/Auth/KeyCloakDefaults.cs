﻿namespace CoverGo.Gateway.Infrastructure.Auth;

public static class KeyCloakDefaults
{
    /// <summary>
    /// The default value used for OpenIdConnectOptions.AuthenticationScheme.
    /// </summary>
    public const string AuthenticationScheme = "KeyCloak";

    /// <summary>
    /// The default value for the display name.
    /// </summary>
    public const string DisplayName = "KeyCloak";

    /// <summary>
    /// The default value for KeyCloak authentication endpoint
    /// </summary>
    public const string AuthEndpointTemplate = "{0}/protocol/openid-connect/auth";
    
    /// <summary>
    /// The default value for KeyCloak metadata endpoint
    /// </summary>
    public const string MetadataEndpointTemplate = "{0}/.well-known/openid-configuration";

    /// <summary>
    /// The url parameter to let keycloak redirect directly to idp.
    /// </summary>
    public const string IdpHintUrlParameter = "kc_idp_hint";
}