using System.Net.Http;
using System.Threading.Tasks;
using CoverGo.Gateway.Domain.Auth;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;

namespace CoverGo.Gateway.Infrastructure.Auth;
public class LoginProvider : ILoginProvider
{ 
    private readonly HttpClient _client;
    private readonly IFeatureManager _featureManager;

    public LoginProvider(HttpClient client, IFeatureManager featureManager)
    {
        _client = client;
        _featureManager = featureManager;
    }

    public async Task<Login> GetLoginById(string tenantId, string loginId, string appId = null)
    {
        var queryBuilder = new QueryBuilder
        {
            { "appId", appId ?? "" },
            { "version", await GetLoginApiVersion() }
        };

        var url = $"{tenantId}/api/v1/auth/logins/fromLoginId/{loginId}{queryBuilder.ToQueryString()}";

        return await _client.GenericGetAsync<Login>(url);
    }

    public async Task<Login> GetLoginByNameAsync(string tenantId, string username, string appId = null)
    {
        HttpResponseMessage response = await _client.GetAsync(
            $"{tenantId}/api/v1/auth/logins/fromUsername/{username}?appId={appId}");

        string json = await response.Content.ReadAsStringAsync();
        return JsonConvert.DeserializeObject<Login>(json);
    }

    private async Task<string> GetLoginApiVersion()
    {
        return await _featureManager.IsEnabledAsync("PermissionV2") ? "2" : "1";
    }
}