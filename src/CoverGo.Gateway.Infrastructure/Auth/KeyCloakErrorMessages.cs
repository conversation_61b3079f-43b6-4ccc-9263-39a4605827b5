﻿namespace CoverGo.Gateway.Infrastructure.Auth;

public static class KeyCloakErrorMessages
{
    public const string InvalidAudience = "OIDC Authentication: Invalid Audience";
    public const string InvalidIssuer = "OIDC Authentication: Invalid Issuer";
    public const string TokenExpired = "OIDC Authentication: Token Expired";
    public const string InvalidTokenSignature = "OIDC Authentication: Invalid Token Signature";
    public const string TokenNoExpiration = "OIDC Authentication: Token Without Expiration";
    public const string TokenReplayDetected = "OIDC Authentication: Token Replay Detected";
    public const string TokenNotYetValid = "OIDC Authentication: Token Not Yet Valid";
    public const string TokenValidation = "OIDC Authentication: Token Validation Issue";
    public const string Unknown = "OIDC Authentication: Unhandled Exception";
    public const string AccessDenied = "OIDC Authentication: Access Denied";
}