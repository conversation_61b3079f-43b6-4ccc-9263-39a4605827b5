﻿using System;
using System.IO;
using System.Xml;
using System.IO.Compression;
using System.Text;
using CoverGo.Gateway.Domain.Auth;

namespace CoverGo.Gateway.Infrastructure.Auth;

public class SamlAuthRequest
{
    public enum AuthRequestFormat
    {
        Base64 = 1
    }

    public string _id => "_" + Guid.NewGuid().ToString();
    private string _issue_instant => DateTime.Now.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ", System.Globalization.CultureInfo.InvariantCulture);
    private readonly string _issuer;

    public SamlAuthRequest(AppSetting appSetting)
    {
        _issuer = appSetting.Issuer;
    }

    public string GetRequest(AuthRequestFormat format, string host)
    {
        using (StringWriter sw = new StringWriter())
        {
            XmlWriterSettings xws = new XmlWriterSettings();
            xws.OmitXmlDeclaration = true;

            using (XmlWriter xw = XmlWriter.Create(sw, xws))
            {
                xw.WriteStartElement("samlp", "AuthnRequest", "urn:oasis:names:tc:SAML:2.0:protocol");
                xw.WriteAttributeString("ID", _id);
                xw.WriteAttributeString("Version", "2.0");
                xw.WriteAttributeString("IssueInstant", _issue_instant);
                xw.WriteAttributeString("ProtocolBinding", "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST");
                xw.WriteAttributeString("AssertionConsumerServiceURL", $"{host}/consume");

                xw.WriteStartElement("saml", "Issuer", "urn:oasis:names:tc:SAML:2.0:assertion");
                xw.WriteString(_issuer);
                xw.WriteEndElement();

                xw.WriteStartElement("samlp", "NameIDPolicy", "urn:oasis:names:tc:SAML:2.0:protocol");
                xw.WriteAttributeString("Format", "urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified");
                xw.WriteAttributeString("AllowCreate", "true");
                xw.WriteEndElement();

                xw.WriteEndElement();
            }

            if (format == AuthRequestFormat.Base64)
            {
                var memoryStream = new MemoryStream();
                var writer = new StreamWriter(new DeflateStream(memoryStream, CompressionMode.Compress, true), new UTF8Encoding(false));
                writer.Write(sw.ToString());
                writer.Close();
                string result = Convert.ToBase64String(memoryStream.GetBuffer(), 0, (int)memoryStream.Length, Base64FormattingOptions.None);
                return result;
            }

            return null;
        }
    }

    public string GetLogoutRequest(AuthRequestFormat format, string host)
    {
        using (StringWriter sw = new StringWriter())
        {
            XmlWriterSettings xws = new XmlWriterSettings();
            xws.OmitXmlDeclaration = true;

            using (XmlWriter xw = XmlWriter.Create(sw, xws))
            {
                xw.WriteStartElement("samlp", "LogoutRequest", "urn:oasis:names:tc:SAML:2.0:protocol");
                xw.WriteAttributeString("ID", _id);
                xw.WriteAttributeString("Version", "2.0");
                xw.WriteAttributeString("IssueInstant", _issue_instant);
                xw.WriteAttributeString("ProtocolBinding", "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST");
                xw.WriteAttributeString("Destination", $"{host}/consume");

                xw.WriteStartElement("saml", "Issuer", "urn:oasis:names:tc:SAML:2.0:assertion");
                xw.WriteString(_issuer);
                xw.WriteEndElement();

                xw.WriteStartElement("saml", "NameID");
                xw.WriteAttributeString("SPNameQualifier", $"{host}/logout");
                xw.WriteAttributeString("Format", "urn:oasis:names:tc:SAML:2.0:nameid-format:transient");
                
                xw.WriteEndElement();

                xw.WriteEndElement();
            }

            if (format == AuthRequestFormat.Base64)
            {
                var memoryStream = new MemoryStream();
                var writer = new StreamWriter(new DeflateStream(memoryStream, CompressionMode.Compress, true), new UTF8Encoding(false));
                writer.Write(sw.ToString());
                writer.Close();
                string result = Convert.ToBase64String(memoryStream.GetBuffer(), 0, (int)memoryStream.Length, Base64FormattingOptions.None);
                return result;
            }

            return null;
        }
    }

    public string GetMetadata(string host)
    {
        var xml = @"<?xml version=""1.0""?>
                    <md:EntityDescriptor xmlns:md=""urn:oasis:names:tc:SAML:2.0:metadata"" validUntil=""{0}"" cacheDuration=""PT1678284591S"" entityID=""{1}"">
                      <md:IDPSSODescriptor WantAuthnRequestsSigned=""false"" protocolSupportEnumeration=""urn:oasis:names:tc:SAML:2.0:protocol"">
                        <md:SingleLogoutService Binding=""urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"" Location=""{2}""/>
                        <md:NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified</md:NameIDFormat>
                        <md:SingleSignOnService Binding=""urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"" Location=""{3}""/>
                      </md:IDPSSODescriptor>
                    </md:EntityDescriptor>";
        xml = string.Format(xml, _issue_instant, _issuer, $"{host}/logout", $"{host}/consume");

        return xml;
    }
}
