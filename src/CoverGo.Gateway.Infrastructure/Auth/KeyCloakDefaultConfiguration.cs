﻿namespace CoverGo.Gateway.Infrastructure.Auth;

public class KeyCloakDefaultConfiguration
{
    public string Authority { get; set; }
    public string ClientId { get; set; }
    public string ClientSecret { get; set; }
    public string AuthEndpointTemplate { get; set; }
    public string MetadataEndpointTemplate { get; set; }
    
    public bool IsAuthorityValid() => !string.IsNullOrWhiteSpace(Authority);
}