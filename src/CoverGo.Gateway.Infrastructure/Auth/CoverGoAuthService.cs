using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using CoverGo.Applications.Clients;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using IdentityModel.Client;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

using EventLog = CoverGo.Gateway.Domain.EventLog;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Infrastructure.Auth
{
    public class CoverGoAuthService : IAuthService
    {
        private readonly HttpClient _client;
        private readonly ILoginProvider _loginProvider;
        private readonly ILogger<CoverGoAuthService> _logger;

        public CoverGoAuthService(
            HttpClient client,
            ILoginProvider loginProvider,
            ILogger<CoverGoAuthService> logger)
        {
            _client = client;
            _loginProvider = loginProvider;
            _logger = logger;
        }

        public async Task<Token> GetWholeAccessTokenAsync(string tenantId, string clientId, string clientSecret, string username, string password, Dictionary<string, string> parameters)
        {
            TokenResponse response = await _client.RequestPasswordTokenAsync(new PasswordTokenRequest
            {
                Address = $"{tenantId}/connect/token",
                //Address = $"connect/token",
                ClientId = clientId,
                UserName = username,
                Password = password,
                //Scope = "openid profile email",
                Scope = "custom_profile offline_access",
                Parameters = new Parameters(parameters)
            });

            return new Token
            {
                AccessToken = response.AccessToken,
                Error = response.Error,
                ErrorDescription = response.ErrorDescription,
                ExpiresIn = response.ExpiresIn,
                IdentityToken = response.IdentityToken,
                RefreshToken = response.RefreshToken,
                TokenType = response.TokenType,
                RequiresTwoFactor = response.Json.ValueKind != JsonValueKind.Undefined && response.Json.TryGetProperty("requiresTwoFactor", out JsonElement requiresTwoFactorElement) && requiresTwoFactorElement.GetBoolean()
            };
        }

        public async Task<Token> RefreshTokenAsync(string tenantId, string clientId, string clientSecret, string refreshToken)
        {
            TokenResponse response = await _client.RequestRefreshTokenAsync(new RefreshTokenRequest
            {
                Address = $"{tenantId}/connect/token",
                ClientId = clientId,
                RefreshToken = refreshToken,
                Scope = "custom_profile all_user_claims"
            });

            return new Token
            {
                AccessToken = response.AccessToken,
                Error = response.Error,
                ErrorDescription = response.ErrorDescription,
                ExpiresIn = response.ExpiresIn,
                IdentityToken = response.IdentityToken,
                RefreshToken = response.RefreshToken,
                TokenType = response.TokenType
            };
        }

        public async Task<Result<PreOtpLogin>> CreatePreOtpLoginAsync(string tenantId, CreatePreOtpLoginCommand command)
            => await _client.GenericPostAsync<Result<PreOtpLogin>, CreatePreOtpLoginCommand>($"{tenantId}/api/v1/otpLogin/preOtpLogin", command);

        public async Task<Result<OtpLogin>> CreateOtpLoginAsync(string tenantId, CreateOtpLoginCommand command)
            => await _client.GenericPostAsync<Result<OtpLogin>, CreateOtpLoginCommand>($"{tenantId}/api/v1/otpLogin/otpLogin", command);

        public async Task<Result<Token>> CreateAccessTokenFromOtpLoginAsync(string tenantId, CreateAccessTokenFromOtpLoginCommand command)
            => await _client.GenericPostAsync<Result<Token>, CreateAccessTokenFromOtpLoginCommand>($"{tenantId}/api/v1/otpLogin/accessTokenFromOtp", command);
        public Task<TenantSettings> GetTenantSettingsAsync(string tenantId)
            => _client.GenericGetAsync<TenantSettings>($"{tenantId}/api/v1/auth/tenantSettings");

        public Task<Result> AddHostToTenantSettingsAsync(string tenantId, string host)
            => _client.GenericPostAsync<Result, string>($"{tenantId}/api/v1/auth/tenantSettings/hosts/add", host);

        public Task<Result> RemoveHostFromTenantSettingsAsync(string tenantId, string host)
           => _client.GenericPostAsync<Result, string>($"{tenantId}/api/v1/auth/tenantSettings/hosts/remove", host);

        public async Task<Result<CreatedStatus>> CreateLoginAsync(string tenantId, CreateLoginCommand command)
        {
            HttpResponseMessage response = await _client.PostAsync(
                $"{tenantId}/api/v1/auth/logins",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result<CreatedStatus>>(json);
        }

        public Task<Result> UpdateLoginAsync(string tenantId, string loginId, UpdateLoginCommand command) =>
            _client.GenericPostAsync<Result, UpdateLoginCommand>($"{tenantId}/api/v1/auth/logins/{loginId}/update", command);

        public Task<Result> DeleteLoginAsync(string tenantId, string loginId, DeleteCommand command)
             => _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/auth/logins/{loginId}/delete", command);

        public Task<Result> AddTargettedPermissionsAsync(string tenantId, string loginId, IEnumerable<AddTargettedPermissionCommand> commands) =>
            _client.GenericPostAsync<Result, IEnumerable<AddTargettedPermissionCommand>>($"{tenantId}/api/v1/auth/permissions/{loginId}", commands);

        public async Task<Result> RemoveTargettedPermissionAsync(string tenantId, string loginId, string type, string value, string removedBy)
        {
            HttpResponseMessage response = await _client.DeleteAsync(
                $"{tenantId}/api/v1/auth/permissions/{loginId}/{type}/{value}?removedById={removedBy}");

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public Task<Result> RemoveTargettedPermissionsAsync(string tenantId, string loginId, IEnumerable<RemoveTargettedPermissionCommand> commands) =>
            _client.GenericPostAsync<Result, IEnumerable<RemoveTargettedPermissionCommand>>($"{tenantId}/api/v1/auth/{loginId}/permissions/remove", commands);

        public async Task<IEnumerable<Login>> GetLoginsAsync(string tenantId, LoginWhere filter) =>
            await GetLoginsAsync(tenantId, new QueryArguments { Where = filter });

        public async Task<IEnumerable<Login>> GetLoginsAsync(string tenantId, QueryArguments queryArguments) =>
            await _client.GenericPostAsync<IEnumerable<Login>, QueryArguments>($"{tenantId}/api/v1/auth/logins/filter", queryArguments);

        public Task<IEnumerable<EventLog>> GetLoginEventsAsync(string tenantId, EventQuery query)
         => _client.GenericPostAsync<IEnumerable<EventLog>, EventQuery>($"{tenantId}/api/v1/auth/logins/events", query);

        public Task<IEnumerable<DetailedEventLog>> GetLoginEventsV2Async(string tenantId, QueryArguments<LoginEventWhere> queryArguments) =>
            _client.GenericPostAsync<IEnumerable<DetailedEventLog>, QueryArguments<LoginEventWhere>>($"{tenantId}/api/v2/auth/logins/events", queryArguments);

        public Task<long> GetLoginTotalCountAsync(string tenantId, LoginWhere filter) =>
            _client.GenericPostAsync<long, LoginWhere>($"{tenantId}/api/v1/auth/logins/totalCount", filter);

        public async Task<ILookup<string, Login>> GetLoginsByEntityIdsLookupAsync(string tenantId, IEnumerable<string> entityIds) =>
            (await GetLoginsAsync(tenantId, new LoginWhere { EntityIds = entityIds })).ToLookup(x => x.EntityId);

        public async Task<ILookup<string, Login>> GetLoginsByPermissionGroupIdsLookupAsync(string tenantId, IEnumerable<string> permissionGroupIds)
        {
            IEnumerable<Login> logins = await GetLoginsAsync(tenantId, new LoginWhere { PermissionGroupIds = permissionGroupIds });
            IEnumerable<KeyValuePair<string, Login>> keyValuePairs = logins.SelectMany(l => l.PermissionGroups.ToDictionary(x => x.Id, v => l));
            return keyValuePairs.ToLookup(x => x.Key, v => v.Value);
        }

        public async Task<IDictionary<string, Login>> GetLoginsDictionaryAsync(string tenantId, IEnumerable<string> ids)
        {
            var where = new LoginWhere
            {
                Ids = ids,
                ExcludePermissions = true
            };
            return (await GetLoginsAsync(tenantId, where)).ToDictionary(x => x.Id, x => x);
        }

        public Task<Login> GetLoginById(string tenantId, string loginId, string appId = null) =>
            _loginProvider.GetLoginById(tenantId, loginId, appId);

        public Task<Login> GetLoginByNameAsync(string tenantId, string username, string appId = null) =>
            _loginProvider.GetLoginByNameAsync(tenantId, username, appId);

        public Task<Result> ConfirmEmailAsync(string tenantId, string loginId, ConfirmEmailCommand command) =>
            _client.GenericPostAsync<Result, ConfirmEmailCommand>($"{tenantId}/api/v1/auth/logins/{loginId}/confirmEmail", command);

        public async Task<Result> ResetPasswordAsync(string tenantId, string loginId, ResetPasswordCommand command)
        {
            HttpResponseMessage response = await _client.PostAsync(
                $"{tenantId}/api/v1/auth/logins/{loginId}/resetPassword",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public async Task<Result> ForgotPasswordAsync(string tenantId, ForgotPasswordCommand command)
        {
            HttpResponseMessage response = await _client.PostAsync(
                $"{tenantId}/api/v1/auth/logins/forgotPassword",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public Task<Result> ValidatePasswordAsync(string tenantId, string password) =>
            _client.GenericPostAsync<Result, ValidatePasswordCommand>($"{tenantId}/api/v1/auth/passwordValidators/validate", new ValidatePasswordCommand { Password = password });

        public async Task<Result> ChangePasswordAsync(string tenantId, string loginId, ChangePasswordCommand command)
        {
            HttpResponseMessage response = await _client.PostAsync(
                $"{tenantId}/api/v1/auth/logins/{loginId}/changepassword",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public async Task<Result> ChangeExpiredPasswordAsync(string tenantId, ChangeExpiredPasswordCommand command)
        {
            HttpResponseMessage response = await _client.PostAsync(
                $"{tenantId}/api/v1/auth/logins/changeExpiredPassword",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public Task<Result> ResendConfirmationEmailAsync(string tenantId, string loginId, ResendEmailCommand command) =>
            _client.GenericPostAsync<Result, ResendEmailCommand>($"{tenantId}/api/v1/auth/logins/{loginId}/resendConfirmationEmail", command);

        public Task<Result> UpdateLockoutEndDate(string tenantId, string loginId, ChangeUserLockoutDateCommand command)
           => _client.GenericPostAsync<Result, ChangeUserLockoutDateCommand>($"{tenantId}/api/v1/auth/logins/{loginId}/updateLockoutEndDate", command);

        public Task<IEnumerable<TargetGroup>> GetAllTargetGroups(string tenantId) =>
            _client.GenericGetAsync<IEnumerable<TargetGroup>>($"{tenantId}/api/v1/auth/targetgroups");

        public Task<TargetGroup> GetTargetGroupAsync(string tenantId, string id) =>
            _client.GenericGetAsync<TargetGroup>($"{tenantId}/api/v1/auth/targetgroups/{id}");

        public Task<Result> CreateTargetGroupAsync(string tenantId, CreateTargetGroupCommand command) =>
            _client.GenericPostAsync<Result, CreateTargetGroupCommand>($"{tenantId}/api/v1/auth/targetgroups", command);

        public Task<Result> UpdateTargetGroupAsync(string tenantId, string id, UpdateTargetGroupCommand command) =>
            _client.GenericPutAsync<Result, UpdateTargetGroupCommand>($"{tenantId}/api/v1/auth/targetgroups/{id}", command);

        public Task<Result> DeleteTargetGroupAsync(string tenantId, string id, DeleteCommand command) =>
            _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/auth/targetgroups/{id}", command);

        public Task<Result> AddUserToTargetGroupAsync(string tenantId, string id, string targetId) =>
            _client.GenericPostAsync<Result, object>($"{tenantId}/api/v1/auth/targetgroups/{id}/user/{targetId}", null);

        public Task<Result> RemoveUserFromTargetGroupAsync(string tenantId, string id, string targetId) =>
            _client.GenericDeleteAsync<Result>($"{tenantId}/api/v1/auth/targetgroups/{id}/user/{targetId}");

        public Task<Result> AddTargetGroupToTargetGroupAsync(string tenantId, string id, string targetGroupId) =>
            _client.GenericPostAsync<Result, object>($"{tenantId}/api/v1/auth/targetgroups/{id}/targetgroup/{targetGroupId}", null);

        public Task<Result> RemoveTargetGroupFromTargetGroupAsync(string tenantId, string id, string targetGroupId) =>
            _client.GenericDeleteAsync<Result>($"{tenantId}/api/v1/auth/targetgroups/{id}/targetgroup/{targetGroupId}");


        public Task<IEnumerable<string>> GetAllPermissionsAsync(string tenantId) =>
            _client.GenericGetAsync<IEnumerable<string>>($"{tenantId}/api/v1/auth/permissions");

        public Task<IEnumerable<PermissionGroup>> GetPermissionGroupsAsync(string tenantId, PermissionGroupWhere where) =>
            _client.GenericPostAsync<IEnumerable<PermissionGroup>, PermissionGroupWhere>($"{tenantId}/api/v1/auth/permissiongroups/query", where);


        public Task<PermissionGroup> GetPermissionGroupAsync(string tenantId, string id) =>
            _client.GenericGetAsync<PermissionGroup>($"{tenantId}/api/v1/auth/permissiongroups/{id}");

        public Task<IEnumerable<EventLog>> GetPermissionGroupEventsAsync(string tenantId, EventQuery query)
         => _client.GenericPostAsync<IEnumerable<EventLog>, EventQuery>($"{tenantId}/api/v1/auth/permissionGroups/events", query);

        public Task<Result> CreatePermissionGroupAsync(string tenantId, CreatePermissionGroupCommand command) =>
            _client.GenericPostAsync<Result, CreatePermissionGroupCommand>($"{tenantId}/api/v1/auth/permissiongroups", command);

        public Task<Result> UpdatePermissionGroupAsync(string tenantId, string id, UpdatePermissionGroupCommand command) =>
            _client.GenericPutAsync<Result, UpdatePermissionGroupCommand>($"{tenantId}/api/v1/auth/permissiongroups/{id}", command);

        public Task<Result> DeletePermissionGroupAsync(string tenantId, string id, DeleteCommand command)
            => _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/auth/permissiongroups/delete/{id}", command);

        public Task<Result> AddPermissionToPermissionGroupAsync(string tenantId, string id, AddPermissionToPermissionGroupCommand command) =>
            _client.GenericPostAsync<Result, AddPermissionToPermissionGroupCommand>($"{tenantId}/api/v1/auth/permissiongroups/{id}/permissions/add", command);

        public Task<Result> RemovePermissionFromPermissionGroupAsync(string tenantId, string id, RemovePermissionFromPermissionGroupCommand command) =>
            _client.GenericPostAsync<Result, RemovePermissionFromPermissionGroupCommand>($"{tenantId}/api/v1/auth/permissiongroups/{id}/permissions/remove", command);

        public Task<Result> AddPermissionGroupToPermissionGroupAsync(string tenantId, string id, AddPermissionGroupToPermissionGroupCommand command) =>
             _client.GenericPostAsync<Result, AddPermissionGroupToPermissionGroupCommand>($"{tenantId}/api/v1/auth/permissiongroups/{id}/permissionGroups/add", command);

        public Task<Result> RemovePermissionGroupFromPermissionGroupAsync(string tenantId, string id, RemovePermissionGroupFromPermissionGroupCommand command) =>
            _client.GenericPostAsync<Result, RemovePermissionGroupFromPermissionGroupCommand>($"{tenantId}/api/v1/auth/permissiongroups/{id}/permissionGroups/remove", command);

        public Task<Result> AddLoginIdToPermissionGroupAsync(string tenantId, string id, AddLoginPermissionsToPermissionGroupCommand command) =>
             _client.GenericPostAsync<Result, AddLoginPermissionsToPermissionGroupCommand>($"{tenantId}/api/v1/auth/permissiongroups/{id}/loginPermissions/add", command);

        public Task<Result> RemoveLoginIdFromPermissionGroupAsync(string tenantId, string id, RemoveLoginPermissionsFromPermissionGroupCommand command) =>
            _client.GenericPostAsync<Result, RemoveLoginPermissionsFromPermissionGroupCommand>($"{tenantId}/api/v1/auth/permissiongroups/{id}/loginPermissions/remove", command);

        public Task<Result> SendCodeAsync(string tenantId, string loginId, SendCodeCommand command) =>
            _client.GenericPostAsync<Result, SendCodeCommand>($"{tenantId}/api/v1/auth/logins/{loginId}/sendcode", command);

        public Task<Result> VerifyCodeAsync(string tenantId, string loginId, VerifyCodeCommand command) =>
            _client.GenericPostAsync<Result, VerifyCodeCommand>($"{tenantId}/api/v1/auth/logins/{loginId}/verifyCode", command);

        public Task<Result> VerifyResetPasswordCodeAsync(string tenantId, string loginId, VerifyCodeCommand command) =>
            _client.GenericPostAsync<Result, VerifyCodeCommand>($"{tenantId}/api/v1/auth/logins/{loginId}/verifyResetPasswordCode", command);

        public Task<PasswordValidators> GetPasswordValidators(string tenantId, string clientId) =>
            _client.GenericGetAsync($"{tenantId}/api/v1/auth/passwordvalidators?clientId={clientId}", async (response) =>
            {
                if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    Random random = new Random(HashCode.Combine(tenantId, clientId));
                    return new PasswordValidators()
                    {
                        RequireConfirmedEmail = random.Next() % 2 == 0,
                        RequireConfirmPhoneNumber = random.Next() % 2 == 0,
                        RequireDigit = random.Next() % 2 == 0,
                        RequireLength = random.Next(20),
                        RequireLetter = random.Next() % 2 == 0,
                        RequireLowercase = random.Next() % 2 == 0,
                        RequireNonAlphanumeric = random.Next() % 2 == 0,
                        RequireUniqueChars = random.Next(10),
                        RequireUppercase = random.Next() % 2 == 0,
                    };
                }
                else
                {
                    throw new ApiException
                    {
                        StatusCode = (int)response.StatusCode,
                        Content = await response.Content.ReadAsStringAsync()
                    };
                }
            });

        public Task<Result> CreateTenantAsync(string tenantId, CreateTenantCommand command) =>
            _client.GenericPostAsync<Result, CreateTenantCommand>($"{tenantId}/api/v1/auth/createTenant", command);

        public async Task<Result> InitializeTenantAsync(InitializeTenantCommand command)
        {
            Result result = await _client.GenericPostAsync<Result, InitializeTenantCommand>("tenants/initialize", command);

            return result;
        }

        public Task<Result<TenantIdAndAppId>> GetTenantIdAndAppIdByUrlAsync(string url)
        {
            return _client.GenericGetAsync<Result<TenantIdAndAppId>>(
                $"api/v2/tenants/identify/{HttpUtility.UrlEncode(url)}",
                async (response) => {
                    string content = await response.Content.ReadAsStringAsync();
                    var error = new Error
                    {
                        Code = response.StatusCode.ToString(),
                        Message = content
                    };

                    return response.StatusCode < HttpStatusCode.InternalServerError
                        ? Result<TenantIdAndAppId>.Failure(error)
                        : throw new ApiException
                        {
                            StatusCode = (int)response.StatusCode,
                            Content = content
                        };

                });
        }

        public Task<IEnumerable<App>> GetAppsAsync(string tenantId, QueryArguments queryArguments)
            => _client.GenericPostAsync<IEnumerable<App>, QueryArguments>($"{tenantId}/api/v1/auth/apps/query", queryArguments);

        public async Task<IDictionary<string, App>> GetAppsDictionaryAsync(string tenantId, AppWhere where) =>
              (await GetAppsAsync(tenantId, new QueryArguments { Where = where })).ToDictionary(x => x.AppId);

        public Task<IEnumerable<EventLog>> GetAppEventsAsync(string tenantId, EventQuery query)
            => _client.GenericPostAsync<IEnumerable<EventLog>, EventQuery>($"{tenantId}/api/v1/auth/apps/events", query);

        public Task<long> GetTotalCountAsync(string tenantId, AppWhere where)
            => _client.GenericPostAsync<long, AppWhere>($"{tenantId}/api/v1/auth/apps/totalCount", where);

        public Task<Result> CreateAppAsync(string tenantId, CreateAppCommand command)
            => _client.GenericPostAsync<Result, CreateAppCommand>($"{tenantId}/api/v1/auth/apps/create", command);

        public Task<Result> UpdateAppAsync(string tenantId, string appId, UpdateAppCommand command)
            => _client.GenericPostAsync<Result, UpdateAppCommand>($"{tenantId}/api/v1/auth/apps/{appId}/update", command);

        public Task<Result> DeleteAppAsync(string tenantId, string appId, DeleteCommand command)
           => _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/auth/apps/{appId}/delete", command);

        public Task<long> GetTotalCount(string tenantId, PermissionSchemaWhere where) =>
            _client.GenericPostAsync<long, PermissionSchemaWhere>($"{tenantId}/api/v1/auth/permissionSchemas/totalCount", where);

        public Task<IEnumerable<PermissionSchema>> GetPermissionSchemas(string tenantId, QueryArguments queryArguments) =>
            _client.GenericPostAsync<IEnumerable<PermissionSchema>, QueryArguments>($"{tenantId}/api/v1/auth/permissionSchemas/query", queryArguments);

        public Task<Result<CreatedStatus>> CreatePermissionSchema(string tenantId, CreatePermissionSchemaCommand command) =>
           _client.GenericPostAsync<Result<CreatedStatus>, CreatePermissionSchemaCommand>($"{tenantId}/api/v1/auth/permissionSchemas/create", command);

        public Task<Result> UpdatePermissionSchema(string tenantId, UpdatePermissionSchemaCommand command) =>
           _client.GenericPostAsync<Result, UpdatePermissionSchemaCommand>($"{tenantId}/api/v1/auth/permissionSchemas/update", command);

        public Task<Result> DeletePermissionSchema(string tenantId, string permissionSchemaId, DeleteCommand command) =>
           _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/auth/permissionSchemas/{permissionSchemaId}/delete", command);

        public Task<Result> AddTargetedPermissionSchemaToLogin(string tenantId, AddTargetedPermissionSchemaToLoginCommand command) =>
            _client.GenericPostAsync<Result, AddTargetedPermissionSchemaToLoginCommand>($"{tenantId}/api/v1/auth/logins/targetedPermissionSchemas/add", command);

        public Task<Result> RemoveTargetedPermissionSchemaFromLogin(string tenantId, RemoveTargetedPermissionSchemaFromLoginCommand command) =>
            _client.GenericPostAsync<Result, RemoveTargetedPermissionSchemaFromLoginCommand>($"{tenantId}/api/v1/auth/logins/targetedPermissionSchemas/remove", command);

        public Task<IReadOnlyCollection<TargetedPermissionSchema>> GetTargetedPermissionSchemas(string tenantId, IReadOnlyCollection<string> targetedPermissionSchemaIds) =>
            _client.GenericPostAsync<IReadOnlyCollection<TargetedPermissionSchema>, IReadOnlyCollection<string>>($"{tenantId}/api/v1/auth/logins/targetedPermissionSchemas", targetedPermissionSchemaIds);

        public Task<Result<IReadOnlyCollection<UserStorageItem>>> UserStorageQueryAsync(string tenantId, string loginId, QueryArguments<UserStorageItemWhere> query) =>
            _client.GenericPostAsync<Result<IReadOnlyCollection<UserStorageItem>>, QueryArguments<UserStorageItemWhere>>($"{tenantId}/api/v1/auth/storage/{loginId}/query", query);
        public Task<Result> UserStorageCreateAsync(string tenantId, string loginId, CreateUserStorageItemCommand command) =>
            _client.GenericPostAsync<Result, CreateUserStorageItemCommand>($"{tenantId}/api/v1/auth/storage/{loginId}", command);
        public Task<Result> UserStorageUpdateAsync(string tenantId, string loginId, UpdateUserStorageItemCommand command) =>
            _client.GenericPutAsync<Result, UpdateUserStorageItemCommand>($"{tenantId}/api/v1/auth/storage/{loginId}", command);
        public Task<Result> UserStorageDeleteAsync(string tenantId, string loginId, string storageItemKey) =>
            _client.GenericDeleteAsync<Result>($"{tenantId}/api/v1/auth/storage/{loginId}/{storageItemKey}");
        public Task<Result<long>> UserStorageCountAsync(string tenantId, string loginId, QueryArguments<UserStorageItemWhere> query) =>
            _client.GenericPostAsync<Result<long>, QueryArguments<UserStorageItemWhere>>($"{tenantId}/api/v1/auth/storage/{loginId}/count", query);

        public Task<Dictionary<string, List<string>>> GetPermissionTargetIdsAsync(PermissionTargetIdQuery query)
        {
            var queryBuilder = new QueryBuilder { { "names", query.PermissionNames } };
            var url = $"{query.TenantId}/api/v1/logins/{query.LoginId}/permissions{queryBuilder.ToQueryString()}";

            return _client.GenericGetAsync<Dictionary<string, List<string>>>(url);
        }

        public Dictionary<string, List<string>> GetPermissionTargetIds(PermissionTargetIdQuery query)
        {
            var queryBuilder = new QueryBuilder { { "names", query.PermissionNames } };
            var url = $"{query.TenantId}/api/v1/logins/{query.LoginId}/permissions{queryBuilder.ToQueryString()}";

            if (_logger.IsEnabled(LogLevel.Debug))
            {
                _logger.LogDebug("Quering permission targetIds: {query}",
                    string.Join(',', query.PermissionNames));
            }

            return _client.GenericGet<Dictionary<string, List<string>>>(url);
        }

        public Task<Token> GetInternalAccessTokenAsync(string tenantId, string asLoginId, string appId,
            IDictionary<string, string> additionalClaims, CancellationToken cancellationToken = default) =>
            _client.GenericPostAsync<Token, IDictionary<string, string>>($"{tenantId}/api/v1/auth/tokenasloginid/{asLoginId}/{appId}",
                additionalClaims, cancellationToken);
    }
}
