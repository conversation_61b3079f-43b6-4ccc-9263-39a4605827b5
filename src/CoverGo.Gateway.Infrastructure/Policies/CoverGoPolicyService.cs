﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Binder;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Pricing;
using CoverGo.Gateway.Domain.Products;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using CoverGo.Users.Domain.Objects;

using EventLog = CoverGo.Gateway.Domain.EventLog;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Infrastructure.Policies
{
    public class CoverGoPolicyService : IPolicyService
    {
        private readonly HttpClient _client;

        public CoverGoPolicyService(HttpClient client)
        {
            _client = client;
        }

        public Task<IEnumerable<string>> GetIdsAsync(string tenantId, QueryArguments queryArguments) =>
            _client.GenericPostAsync<IEnumerable<string>, QueryArguments>($"{tenantId}/api/v1/policies/queryids", queryArguments);

        public Task<IEnumerable<string>> GetIdsAsync(string tenantId, PolicyWhere where) =>
            GetIdsAsync(tenantId, new QueryArguments { Where = where });

        public Task<List<Policy>> GetAsync(string tenantId, QueryArguments queryArguments) =>
            _client.GenericPostAsync<List<Policy>, QueryArguments>($"{tenantId}/api/v1/policies/query", queryArguments);

        public Task<long> GetTotalCountAsync(string tenantId, PolicyWhere where) =>
            _client.GenericPostAsync<long, PolicyWhere>($"{tenantId}/api/v1/policies/totalCount", where);

        public Task<List<EventLog>> GetEventsAsync(string tenantId, EventQuery query) =>
           _client.GenericPostAsync<List<EventLog>, EventQuery>($"{tenantId}/api/v1/policies/events", query);

        public Task<List<Policy>> GetAsync(string tenantId, PolicyWhere where) =>
            GetAsync(tenantId, new QueryArguments { Where = where });

        public Task<Policy> GetAsync(string tenantId, string policyId, Domain.AsOf? asOf = null)
        {
            return _client.GenericPostAsync<Policy, Domain.AsOf>($"{tenantId}/api/v1/policies/query/{policyId}", asOf);
        }

        public Task<Result<bool>> CheckIssuedStatusAsync(string tenantId, PolicyWhere where)
        {
            return _client.GenericPostAsync<Result<bool>, QueryArguments>($"{tenantId}/api/v1/policies/checkIssuedStatus", new QueryArguments { Where = where });
        }

        public Task<Result> CheckForAddEndorsement(string tenantId, string type, PolicyWhere where)
        {
            return _client.GenericPostAsync<Result, QueryArguments>($"{tenantId}/api/v1/policies/checkForAddEndorsement/{type}", new QueryArguments { Where = where });
        }

        public async Task<IDictionary<string, Policy>> GetDictionaryAsync(string tenantId, PolicyWhere where) =>
            (await GetAsync(tenantId, new QueryArguments { Where = where })).ToDictionary(x => x.Id);

        public async Task<ILookup<string, Policy>> GetLookupAsync(string tenantId, PolicyWhere where) =>
            (await GetAsync(tenantId, new QueryArguments { Where = where })).ToLookup(x => x.Id);

        [Obsolete("old policy flow")]
        public Task<Result<CreatedStatus>> AddOfferAsync(string tenantId, string policyId, AddOfferCommand command) =>
          _client.GenericPostAsync<Result<CreatedStatus>, AddOfferCommand>($"{tenantId}/api/v1/policies/offers/{policyId}", command);

        [Obsolete("old policy flow")]
        public Task<Result> RemoveOfferAsync(string tenantId, string policyId, RemoveCommand command) =>
            _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/policies/offers/{policyId}/remove", command);

        [Obsolete("old policy flow")]
        public Task<Result> UpdateOfferAsync(string tenantId, string policyId, UpdateOfferCommand command) =>
          _client.GenericPutAsync<Result, UpdateOfferCommand>($"{tenantId}/api/v1/policies/offers/{policyId}", command);

        [Obsolete("old policy flow")]
        public Task<Result> ConvertOfferAsync(string tenantId, string policyId, ConvertOfferCommand command) =>
            _client.GenericPostAsync<Result, ConvertOfferCommand>($"{tenantId}/api/v1/policies/offers/{policyId}/convert", command);

        public Task<Result<PolicyStatus>> CreatePolicyAsync(string tenantId, CreatePolicyCommand command) =>
            _client.GenericPostAsync<Result<PolicyStatus>, CreatePolicyCommand>($"{tenantId}/api/v1/policies", command);

        public Task<Result> UpdatePolicyAsync(string tenantId, string id, UpdatePolicyCommand command)
        {
            return _client.GenericPutAsync<Result, UpdatePolicyCommand>($"{tenantId}/api/v1/policies/policies/{id}", command);
        }

        public async Task<Result> AddOtherHolderAsync(string tenantId, string policyId, AddEntityCommand command)
            => await _client.GenericPostAsync<Result, AddEntityCommand>($"{tenantId}/api/v1/policies/otherHolders/{policyId}", command);
        public async Task<Result> RemoveOtherHolderAsync(string tenantId, string policyId, RemoveCommand command)
            => await _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/policies/otherHolders/{policyId}/remove", command);

        public async Task<Result> AddInsuredAsync(string tenantId, string policyId, AddEntityCommand command)
          => await _client.GenericPostAsync<Result, AddEntityCommand>($"{tenantId}/api/v1/policies/insureds/{policyId}", command);
        public async Task<Result> RemoveInsuredAsync(string tenantId, string policyId, RemoveCommand command)
            => await _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/policies/insureds/{policyId}/remove", command);

        public async Task<Result> UpdateContractHolderIndividualAsync(string tenantId, string policyId, UpdateIndividualCommand command)
          => await _client.GenericPutAsync<Result, UpdateIndividualCommand>($"{tenantId}/api/v1/policies/contractHolderIndividual/{policyId}", command);

        public async Task<Result> UpdateContractHolderCompanyAsync(string tenantId, string policyId, UpdateCompanyCommand command)
           => await _client.GenericPutAsync<Result, UpdateCompanyCommand>($"{tenantId}/api/v1/policies/contractHolderCompany/{policyId}", command);

        public async Task<Result> UpdateContractInsuredIndividualAsync(string tenantId, string policyId, UpdateIndividualCommand command) =>
            await _client.GenericPutAsync<Result, UpdateIndividualCommand>($"{tenantId}/api/v1/policies/contractInsuredIndividual/{policyId}", command);

        public async Task<Result> UpdateContractInsuredCompanyAsync(string tenantId, string policyId, UpdateCompanyCommand command) =>
            await _client.GenericPutAsync<Result, UpdateCompanyCommand>($"{tenantId}/api/v1/policies/contractInsuredCompany/{policyId}", command);

        public async Task<Result> UpdateContractInsuredObjectAsync(string tenantId, string policyId, UpdateObjectCommand command) =>
            await _client.GenericPutAsync<Result, UpdateObjectCommand>($"{tenantId}/api/v1/policies/contractInsuredObject/{policyId}", command);

        public async Task<Result<PolicyStatus>> IssuePolicyAsync(string tenantId, IssuePolicyCommand command) =>
            await _client.GenericPostAsync<Result<PolicyStatus>, IssuePolicyCommand>($"{tenantId}/api/v1/policies/issue", command);

        public Task<Result> CancelPolicyAsync(string tenantId, string policyId, CancelPolicyCommand command) =>
            _client.GenericPostAsync<Result, CancelPolicyCommand>($"{tenantId}/api/v1/policies/cancel/{policyId}", command);

        public Task<Result> RejectPolicyAsync(string tenantId, string policyId, RejectQuoteCommand command) =>
            _client.GenericPostAsync<Result, RejectQuoteCommand>($"{tenantId}/api/v1/policies/reject/{policyId}", command);

        public Task<Result> DeletePolicyAsync(string tenantId, string id, string deletedById) => _client.GenericDeleteAsync<Result>($"{tenantId}/api/v1/policies/{id}/{deletedById}");
        public Task<Result> AddAttachmentAsync(string tenantId, string policyId, AddAttachmentCommand command) => _client.GenericPostAsync<Result, AddAttachmentCommand>($"{tenantId}/api/v1/policies/attachments/{policyId}", command);
        public Task<Result> UpdateAttachmentAsync(string tenantId, string policyId, UpdateAttachmentCommand command) => _client.GenericPutAsync<Result, UpdateAttachmentCommand>($"{tenantId}/api/v1/policies/attachments/{policyId}", command);
        public async Task<Result> RemoveAttachmentAsync(string tenantId, string policyId, RemoveAttachmentCommand command) =>
            await _client.GenericPostAsync<Result, RemoveAttachmentCommand>($"{tenantId}/api/v1/policies/attachments/{policyId}/remove", command);

        public Task<Result> AddNoteAsync(string tenantId, string policyId, AddNoteCommand command) => _client.GenericPostAsync<Result, AddNoteCommand>($"{tenantId}/api/v1/policies/notes/{policyId}", command);
        public Task<Result> UpdateNoteAsync(string tenantId, string policyId, UpdateNoteCommand command) => _client.GenericPutAsync<Result, UpdateNoteCommand>($"{tenantId}/api/v1/policies/notes/{policyId}", command);

        public async Task<Result> RemoveNoteAsync(string tenantId, string policyId, RemoveCommand command) =>
            await _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/policies/notes/{policyId}/remove", command);

        public Task<Result> AddBeneficiaryEligibilityAsync(string tenantId, string policyId, AddBeneficiaryEligibilityCommand command) =>
          _client.GenericPostAsync<Result, AddBeneficiaryEligibilityCommand>($"{tenantId}/api/v1/policies/beneficiaryEligibilities/{policyId}", command);

        public Task<Result> UpdateBeneficiaryEligibilityAsync(string tenantId, string policyId, UpdateBeneficiaryEligibilityCommand command) =>
            _client.GenericPutAsync<Result, UpdateBeneficiaryEligibilityCommand>($"{tenantId}/api/v1/policies/beneficiaryEligibilities/{policyId}", command);

        public Task<Result> RemoveBeneficiaryEligibilityAsync(string tenantId, string policyId, RemoveCommand command) =>
            _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/policies/beneficiaryEligibilities/{policyId}/remove", command);

        public Task<Result> UpsertBenefitOptionAsync(string tenantId, string policyId, UpsertBenefitOptionCommand command) =>
           _client.GenericPutAsync<Result, object>($"{tenantId}/api/v1/policies/benefitOptions/{policyId}", command);

        public Task<Result> RemoveBenefitOptionAsync(string tenantId, string policyId, RemoveBenefitOptionCommand command) =>
            _client.GenericPostAsync<Result, RemoveBenefitOptionCommand>($"{tenantId}/api/v1/policies/benefitOptions/{policyId}/remove", command);

        public Task<Result> UpsertBenefitOptionBatchAsync(string tenantId, string policyId, UpsertBenefitOptionCommandBatch command) =>
             _client.GenericPostAsync<Result, UpsertBenefitOptionCommandBatch>($"{tenantId}/api/v1/policies/benefitOptions/{policyId}/batch", command);

        public Task<Result> AddPaymentInfoAsync(string tenantId, string policyId, AddPaymentInfoCommand command) =>
            _client.GenericPostAsync<Result, AddPaymentInfoCommand>($"{tenantId}/api/v1/policies/paymentInfos/{policyId}", command);

        public Task<Result> UpdatePaymentInfoAsync(string tenantId, string policyId, UpdatePaymentInfoCommand command) =>
            _client.GenericPutAsync<Result, UpdatePaymentInfoCommand>($"{tenantId}/api/v1/policies/paymentInfos/{policyId}", command);

        public Task<Result> RemovePaymentInfoAsync(string tenantId, string policyId, RemoveCommand command) =>
            _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/policies/paymentInfos/{policyId}/remove", command);

        public Task<Result> AddCommissionAsync(string tenantId, string policyId, AddCommissionCommand command) =>
             _client.GenericPostAsync<Result, AddCommissionCommand>($"{tenantId}/api/v1/policies/commissions/{policyId}/add", command);

        public Task<Result> UpdateCommissionAsync(string tenantId, string policyId, UpdateCommissionCommand command) =>
             _client.GenericPostAsync<Result, UpdateCommissionCommand>($"{tenantId}/api/v1/policies/commissions/{policyId}/update", command);

        public Task<Result> RemoveCommissionAsync(string tenantId, string policyId, RemoveCommissionCommand command) =>
             _client.GenericPostAsync<Result, RemoveCommissionCommand>($"{tenantId}/api/v1/policies/commissions/{policyId}/remove", command);


        public Task<IEnumerable<Policy>> GetBeneficiariesOfAsync(string tenantId, IEnumerable<string> beneficiaryIds) =>
            _client.GenericPostAsync<IEnumerable<Policy>, IEnumerable<string>>($"{tenantId}/api/v1/policies/beneficiaries", beneficiaryIds);

        public Task<IEnumerable<Policy>> GetPayorsOfAsync(string tenantId, IEnumerable<string> payersIds) =>
            _client.GenericPostAsync<IEnumerable<Policy>, IEnumerable<string>>($"{tenantId}/api/v1/policies/payors", payersIds);

        public async Task<ILookup<string, Policy>> GetBeneficiariesOfLookupAsync(string tenantId, IEnumerable<string> beneficiaryIds) =>
            (await GetBeneficiariesOfAsync(tenantId, beneficiaryIds)).ToLookup(x => x.Id, x => x);

        public async Task<ILookup<string, Policy>> GetPayorsOfLookupAsync(string tenantId, IEnumerable<string> payersIds) =>
            (await GetPayorsOfAsync(tenantId, payersIds)).ToLookup(x => x.Id, x => x);

        public async Task<ILookup<string, Policy>> GetInsuredsOfLookupAsync(string tenantId, IEnumerable<string> insuredIds)
        {
            List<Policy> policies = await GetAsync(tenantId, new PolicyWhere { ContractInsured_some = new EntityWhere { Id_in = insuredIds.ToList() } });

            ILookup<string, Policy> lookup = policies
                .SelectMany(p => p.ContractInsured.Select(ci => new KeyValuePair<string, Policy>(ci.Id, p)))
                .ToLookup(x => x.Key, x => x.Value);

            return lookup;
        }

        public async Task<ILookup<string, Policy>> GetContractTerminatedsOfLookupAsync(string tenantId, IEnumerable<string> insuredIds)
        {
            List<Policy> policies = await GetAsync(tenantId, new PolicyWhere {  ContractTerminated_some = new EntityWhere { Id_in = insuredIds.ToList() } });

            ILookup<string, Policy> lookup = policies
                .SelectMany(p => p.ContractTerminated.Select(ci => new KeyValuePair<string, Policy>(ci.Id, p)))
                .ToLookup(x => x.Key, x => x.Value);

            return lookup;
        }

        public Task<IEnumerable<PolicyUpdateRequest>> GetUpdateRequestsAsync(string tenantId, PolicyUpdateRequestFilter filter) =>
            _client.GenericPostAsync<IEnumerable<PolicyUpdateRequest>, PolicyUpdateRequestFilter>($"{tenantId}/api/v1/policies/requests/filter", filter);

        public async Task<ILookup<string, PolicyUpdateRequest>> GetUpdateRequestLookupAsync(string tenantId, PolicyUpdateRequestFilter filter) =>
            (await GetUpdateRequestsAsync(tenantId, filter)).ToLookup(x => filter.Ids == null ? "all" : x.PolicyId);

        public async Task<IDictionary<string, PolicyUpdateRequest>> GetUpdateRequestDictionaryAsync(string tenantId, PolicyUpdateRequestFilter filter) =>
            (await GetUpdateRequestsAsync(tenantId, filter)).ToDictionary(x => x.Id, x => x);

        public Task<Result<CreatedStatus>> CreateUpdateRequestAsync(string tenantId, string policyId, CreatePolicyUpdateRequestCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreatePolicyUpdateRequestCommand>($"{tenantId}/api/v1/policies/requests/{policyId}", command);

        public Task<Result> UpdatePolicyAsync(string tenantId, string policyId, AddUpdatePolicyCommandToRequestCommand command) =>
            _client.GenericPostAsync<Result, AddUpdatePolicyCommandToRequestCommand>($"{tenantId}/api/v1/policies/requests/{policyId}/policy", command);

        public Task<Result> UpsertBenefitOptionAsync(string tenantId, string policyId, AddUpsertBenefitOptionCommandToRequestCommand command) =>
            _client.GenericPostAsync<Result, AddUpsertBenefitOptionCommandToRequestCommand>($"{tenantId}/api/v1/policies/requests/{policyId}/benefitOption", command);

        public Task<Result> UpdateBeneficiaryEligibilityAsync(string tenantId, string policyId, AddUpdateBeneficiaryEligibilityCommandToRequestCommand command) =>
           _client.GenericPostAsync<Result, AddUpdateBeneficiaryEligibilityCommandToRequestCommand>($"{tenantId}/api/v1/policies/requests/{policyId}/beneficiaryEligibilities", command);

        public Task<Result> UpdatePaymentInfoAsync(string tenantId, string policyId, AddUpdatePaymentInfoCommandToRequestCommand command) =>
            _client.GenericPostAsync<Result, AddUpdatePaymentInfoCommandToRequestCommand>($"{tenantId}/api/v1/policies/requests/{policyId}/paymentInfo", command);

        public Task<Result> ReviewUpdateRequestAsync(string tenantId, string policyId, string requestId, PolicyRequestReview command) =>
            _client.GenericPostAsync<Result, PolicyRequestReview>($"{tenantId}/api/v1/policies/requests/{policyId}/review", command);

        public Task<Result> RemoveUpdateRequestAsync(string tenantId, string policyId, string requestId, string removedById) =>
            _client.GenericDeleteAsync<Result>($"{tenantId}/api/v1/policies/requests/{policyId}/{requestId}/{removedById}");

        [Obsolete("old policy flow")]
        public Task<Result> AddLoadingToOfferAsync(string tenantId, string policyId, AddLoadingCommand command)
            => _client.GenericPostAsync<Result, AddLoadingCommand>($"{tenantId}/api/v1/policies/offers/{policyId}/loadings/add", command);
        [Obsolete("old policy flow")]
        public Task<Result> UpdateLoadingOfOfferAsync(string tenantId, string policyId, UpdateLoadingCommand command)
           => _client.GenericPostAsync<Result, UpdateLoadingCommand>($"{tenantId}/api/v1/policies/offers/{policyId}/loadings/update", command);
        [Obsolete("old policy flow")]
        public Task<Result> RemoveLoadingFromOfferAsync(string tenantId, string policyId, RemoveLoadingCommand command)
            => _client.GenericPostAsync<Result, RemoveLoadingCommand>($"{tenantId}/api/v1/policies/offers/{policyId}/loadings/remove", command);
        [Obsolete("old policy flow")]
        public Task<Result> AddDiscountToOfferAsync(string tenantId, string policyId, AddDiscountCommand command) =>
            _client.GenericPostAsync<Result, AddDiscountCommand>($"{tenantId}/api/v1/policies/offers/{policyId}/discounts/add", command);
        [Obsolete("old policy flow")]
        public Task<Result> UpdateDiscountOfOfferAsync(string tenantId, string policyId, UpdateDiscountCommand command) =>
            _client.GenericPostAsync<Result, UpdateDiscountCommand>($"{tenantId}/api/v1/policies/offers/{policyId}/discounts/update", command);
        [Obsolete("old policy flow")]
        public Task<Result> RemoveDiscountFromOfferAsync(string tenantId, string policyId, RemoveDiscountFromOfferCommand command) =>
            _client.GenericPostAsync<Result, RemoveDiscountFromOfferCommand>($"{tenantId}/api/v1/policies/offers/{policyId}/discounts/remove", command);
        [Obsolete("old policy flow")]
        public Task<Result> AddExclusionToOfferAsync(string tenantId, string policyId, AddExclusionCommand command)
            => _client.GenericPostAsync<Result, AddExclusionCommand>($"{tenantId}/api/v1/policies/offers/{policyId}/exclusions/add", command);
        [Obsolete("old policy flow")]
        public Task<Result> RemoveExclusionFromOfferAsync(string tenantId, string policyId, RemoveExclusionCommand command)
            => _client.GenericPostAsync<Result, RemoveExclusionCommand>($"{tenantId}/api/v1/policies/offers/{policyId}/exclusions/remove", command);
        [Obsolete("for old clients for backward compatibility,for new ones use ValidatePolicyAsync instead.")]
        public Task<Result> ValidatePolicy_2Async(string tenantId, string policyId) =>
                   _client.GenericGetAsync<Result>($"{tenantId}/api/v1/policies/validate/{policyId}");
        public Task<ValidatePolicyResult> ValidatePolicyAsync(string tenantId, string policyId) =>
                   _client.GenericGetAsync<ValidatePolicyResult>($"{tenantId}/api/v1/policies/validate/{policyId}");
        public Task<Result<string>> AddStakeholderToPolicyAsync(string tenantId, string policyId, AddStakeholderCommand command)
            => _client.GenericPostAsync<Result<string>, AddStakeholderCommand>($"{tenantId}/api/v1/policies/{policyId}/stakeholders/add", command);
        public Task<Result> UpdateStakeholderOfPolicyAsync(string tenantId, string policyId, UpdateStakeholderCommand command)
            => _client.GenericPostAsync<Result, UpdateStakeholderCommand>($"{tenantId}/api/v1/policies/{policyId}/stakeholders/update", command);
        public Task<Result> RemoveStakeholderFromPolicyAsync(string tenantId, string policyId, RemoveStakeholderCommand command)
            => _client.GenericPostAsync<Result, RemoveStakeholderCommand>($"{tenantId}/api/v1/policies/{policyId}/stakeholders/remove", command);

        public Task<Result<string>> AddClauseToPolicyAsync(string tenantId, string policyId, AddClauseCommand command)
            => _client.GenericPostAsync<Result<string>, AddClauseCommand>($"{tenantId}/api/v1/policies/{policyId}/clauses/add", command);
        public Task<Result> UpdateClauseOfPolicyAsync(string tenantId, string policyId, UpdateClauseCommand command)
            => _client.GenericPostAsync<Result, UpdateClauseCommand>($"{tenantId}/api/v1/policies/{policyId}/clauses/update", command);
        public Task<Result> RemoveClauseFromPolicyAsync(string tenantId, string policyId, RemoveClauseCommand command)
            => _client.GenericPostAsync<Result, RemoveClauseCommand>($"{tenantId}/api/v1/policies/{policyId}/clauses/remove", command);

        public Task<Result<CreatedStatus>> PolicyClauseBatchAsync(string tenantId, string policyId, ClauseBatchCommand batch) =>
            _client.GenericPostAsync<Result<CreatedStatus>, ClauseBatchCommand>($"{tenantId}/api/v1/policies/{policyId}/clauses/batch", batch);


        public Task<Result<CreatedStatus>> AddTagToPolicyAsync(string tenantId, string policyId, AddTagCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddTagCommand>($"{tenantId}/api/v1/policies/{policyId}/tags/add", command);
        public Task<Result> RemoveTagFromPolicyAsync(string tenantId, string policyId, RemoveCommand command)
            => _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/policies/{policyId}/tags/remove", command);

        #region endorsements
        public Task<Result<CreatedStatus>> AddEndorsementAsync(string tenantId, string policyId, AddEndorsementCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddEndorsementCommand>($"{tenantId}/api/v1/policies/{policyId}/endorsements/add", command);
        public Task<Result> RemoveEndorsementAsync(string tenantId, string policyId, RemoveCommand command)
            => _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/policies/{policyId}/endorsements/remove", command);
        public Task<Result> AcceptEndorsementAsync(string tenantId, string policyId, AcceptEndorsementCommand command)
            => _client.GenericPostAsync<Result, AcceptEndorsementCommand>($"{tenantId}/api/v1/policies/{policyId}/endorsements/accept", command);
        public Task<Result> RejectEndorsementAsync(string tenantId, string policyId, RejectEndorsementCommand command)
            => _client.GenericPostAsync<Result, RejectEndorsementCommand>($"{tenantId}/api/v1/policies/{policyId}/endorsements/reject", command);
        public Task<Policy> PreviewEndorsementAsync(string tenantId, string policyId, string endorsementId)
            => _client.GenericGetAsync<Policy>($"{tenantId}/api/v1/policies/{policyId}/endorsements/{endorsementId}/preview");

        public Task<Result<CreatedStatus>> UpdatePolicy2Async(string tenantId, string policyId, UpdatePolicyCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdatePolicyCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdatePolicy", command);

        public Task<Result<CreatedStatus>> UpdatePolicyProductAsync(string tenantId, string policyId, UpdatePolicyProductCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdatePolicyProductCommand>($"{tenantId}/api/v1/policies/{policyId}/updateProduct", command);
        public Task<Result<CreatedStatus>> AddDiscountAsync(string tenantId, string policyId, AddDiscountCommand command)
         => _client.GenericPostAsync<Result<CreatedStatus>, AddDiscountCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddDiscount", command);
        public Task<Result<CreatedStatus>> UpdateDiscountAsync(string tenantId, string policyId, UpdateDiscountCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateDiscountCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateDiscount", command);
        public Task<Result<CreatedStatus>> RemoveDiscountAsync(string tenantId, string policyId, RemoveCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, RemoveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemoveDiscount", command);

        public Task<Result<CreatedStatus>> AddLoadingAsync(string tenantId, string policyId, AddLoadingCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddLoadingCommand>($"{tenantId}/api/v1/policies/{policyId}/loadings/add", command);
        public Task<Result<CreatedStatus>> UpdateLoadingAsync(string tenantId, string policyId, UpdateLoadingCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateLoadingCommand>($"{tenantId}/api/v1/policies/{policyId}/loadings/update", command);
        public Task<Result<CreatedStatus>> RemoveLoadingAsync(string tenantId, string policyId, RemoveCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, RemoveCommand>($"{tenantId}/api/v1/policies/{policyId}/loadings/remove", command);

        public Task<Result<CreatedStatus>> AddExclusionAsync(string tenantId, string policyId, AddExclusionCommand command)
           => _client.GenericPostAsync<Result<CreatedStatus>, AddExclusionCommand>($"{tenantId}/api/v1/policies/{policyId}/exclusions/add", command);
        public Task<Result> RemoveExclusionAsync(string tenantId, string policyId, RemoveCommand command)
           => _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/policies/{policyId}/exclusions/remove", command);

        public Task<Result<CreatedStatus>> AddPolicyFactAsync(string tenantId, string policyId, AddFactCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddFactCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddPolicyFact", command);
        public Task<Result<CreatedStatus>> UpdatePolicyFactAsync(string tenantId, string policyId, UpdateFactCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateFactCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdatePolicyFact", command);
        public Task<Result<CreatedStatus>> RemovePolicyFactAsync(string tenantId, string policyId, RemoveCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, RemoveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemovePolicyFact", command);
        public Task<Result> PolicyFactBatch(string tenantId, string policyId, FactCommandBatch batch)
            => _client.GenericPostAsync<Result, FactCommandBatch>($"{tenantId}/api/v1/policies/{policyId}/policyFactBatch", batch);

        public Task<Result<CreatedStatus>> UpsertBenefitOptionOfPolicyAsync(string tenantId, string policyId, UpsertBenefitOptionCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpsertBenefitOptionCommand>($"{tenantId}/api/v1/policies/{policyId}/upsertBenefitOption", command);
        public Task<Result<CreatedStatus>> RemoveBenefitOptionFromPolicyAsync(string tenantId, string policyId, RemoveBenefitOptionCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, RemoveBenefitOptionCommand>($"{tenantId}/api/v1/policies/{policyId}/removeBenefitOption", command);

        public Task<Result<CreatedStatus>> AddOtherContractHolderAsync(string tenantId, string policyId, AddEntityCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddEntityCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddOtherContractHolder", command);
        public Task<Result<CreatedStatus>> AddContractInsuredAsync(string tenantId, string policyId, AddEntityCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddEntityCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddContractInsured", command);
        public Task<Result<CreatedStatus>> RemoveOtherContractHolderAsync(string tenantId, string policyId, RemoveCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, RemoveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemoveOtherContractHolder", command);
        public Task<Result<CreatedStatus>> RemoveContractInsuredAsync(string tenantId, string policyId, RemoveCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, RemoveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemoveContractInsured", command);

        public Task<Result<CreatedStatus>> UpdateOtherContractHolderIndividualAsync(string tenantId, string policyId, UpdateIndividualCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateIndividualCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateOtherContractHolderIndividual", command);
        public Task<Result<CreatedStatus>> UpdateContractInsuredIndividual2Async(string tenantId, string policyId, UpdateIndividualCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateIndividualCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateContractInsuredIndividual", command);

        public Task<Result<CreatedStatus>> UpdateOtherContractHolderCompanyAsync(string tenantId, string policyId, UpdateCompanyCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateCompanyCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateOtherContractHolderCompany", command);
        public Task<Result<CreatedStatus>> UpdateContractInsuredCompany2Async(string tenantId, string policyId, UpdateCompanyCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateCompanyCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateContractInsuredCompany", command);

        public Task<Result<CreatedStatus>> UpdateContractInsuredObject2Async(string tenantId, string policyId, UpdateObjectCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateObjectCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateContractInsuredObject", command);

        public Task<Result> AddContractHolderFactBatch(string tenantId, string policyId, FactCommandBatch batch)
            => _client.GenericPostAsync<Result, FactCommandBatch>($"{tenantId}/api/v1/policies/{policyId}/addContractHolderFactBatch", batch);
        public Task<Result<CreatedStatus>> AddContractHolderFactAsync(string tenantId, string policyId, AddFactCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddFactCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddContractHolderFact", command);
        public Task<Result<CreatedStatus>> AddOtherContractHolderFactAsync(string tenantId, string policyId, AddFactCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddFactCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddOtherContractHolderFact", command);
        public Task<Result<CreatedStatus>> AddContractInsuredFactAsync(string tenantId, string policyId, AddFactCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddFactCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddContractInsuredFact", command);
        public Task<Result<CreatedStatus>> UpdateContractHolderFactAsync(string tenantId, string policyId, UpdateFactCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateFactCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateContractHolderFact", command);
        public Task<Result<CreatedStatus>> UpdateOtherContractHolderFactAsync(string tenantId, string policyId, UpdateFactCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateFactCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateOtherContractHolderFact", command);
        public Task<Result<CreatedStatus>> UpdateContractInsuredFact2Async(string tenantId, string policyId, UpdateFactCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateFactCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateContractInsuredFact", command);
        public Task<Result<CreatedStatus>> RemoveContractHolderFactAsync(string tenantId, string policyId, RemoveCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, RemoveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemoveContractHolderFact", command);
        public Task<Result<CreatedStatus>> RemoveOtherContractHolderFactAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, RemoveEntityPrimitiveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemoveOtherContractHolderFact", command);
        public Task<Result<CreatedStatus>> RemoveContractInsuredFactAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, RemoveEntityPrimitiveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemoveContractInsuredFact", command);

        public Task<Result<CreatedStatus>> AddContractHolderIdentityAsync(string tenantId, string policyId, AddIdentityCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddIdentityCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddContractHolderIdentity", command);
        public Task<Result<CreatedStatus>> AddOtherContractHolderIdentityAsync(string tenantId, string policyId, AddIdentityCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddIdentityCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddOtherContractHolderIdentity", command);
        public Task<Result<CreatedStatus>> AddContractInsuredIdentityAsync(string tenantId, string policyId, AddIdentityCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddIdentityCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddContractInsuredIdentity", command);
        public Task<Result<CreatedStatus>> UpdateContractHolderIdentityAsync(string tenantId, string policyId, UpdateIdentityCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateIdentityCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateContractHolderIdentity", command);
        public Task<Result<CreatedStatus>> UpdateOtherContractHolderIdentityAsync(string tenantId, string policyId, UpdateIdentityCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateIdentityCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateOtherContractHolderIdentity", command);
        public Task<Result<CreatedStatus>> UpdateContractInsuredIdentityAsync(string tenantId, string policyId, UpdateIdentityCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateIdentityCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateContractInsuredIdentity", command);
        public Task<Result<CreatedStatus>> RemoveContractHolderIdentityAsync(string tenantId, string policyId, RemoveCommand command)
           => _client.GenericPostAsync<Result<CreatedStatus>, RemoveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemoveContractHolderIdentity", command);
        public Task<Result<CreatedStatus>> RemoveOtherContractHolderIdentityAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, RemoveEntityPrimitiveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemoveOtherContractHolderIdentity", command);
        public Task<Result<CreatedStatus>> RemoveContractInsuredIdentityAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, RemoveEntityPrimitiveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemoveContractInsuredIdentity", command);

        public Task<Result<CreatedStatus>> AddContractHolderContactAsync(string tenantId, string policyId, AddContactCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddContactCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddContractHolderContact", command);
        public Task<Result<CreatedStatus>> AddOtherContractHolderContactAsync(string tenantId, string policyId, AddContactCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddContactCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddOtherContractHolderContact", command);
        public Task<Result<CreatedStatus>> AddContractInsuredContactAsync(string tenantId, string policyId, AddContactCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddContactCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddContractInsuredContact", command);
        public Task<Result<CreatedStatus>> UpdateContractHolderContactAsync(string tenantId, string policyId, UpdateContactCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateContactCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateContractHolderContact", command);
        public Task<Result<CreatedStatus>> UpdateOtherContractHolderContactAsync(string tenantId, string policyId, UpdateContactCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateContactCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateOtherContractHolderContact", command);
        public Task<Result<CreatedStatus>> UpdateContractInsuredContactAsync(string tenantId, string policyId, UpdateContactCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateContactCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateContractInsuredContact", command);
        public Task<Result<CreatedStatus>> RemoveContractHolderContactAsync(string tenantId, string policyId, RemoveCommand command)
           => _client.GenericPostAsync<Result<CreatedStatus>, RemoveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemoveContractHolderContact", command);
        public Task<Result<CreatedStatus>> RemoveOtherContractHolderContactAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, RemoveEntityPrimitiveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemoveOtherContractHolderContact", command);
        public Task<Result<CreatedStatus>> RemoveContractInsuredContactAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, RemoveEntityPrimitiveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemoveContractInsuredContact", command);

        public Task<Result<CreatedStatus>> AddContractHolderAddressAsync(string tenantId, string policyId, AddAddressCommand command)
          => _client.GenericPostAsync<Result<CreatedStatus>, AddAddressCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddContractHolderAddress", command);
        public Task<Result<CreatedStatus>> AddOtherContractHolderAddressAsync(string tenantId, string policyId, AddAddressCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddAddressCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddOtherContractHolderAddress", command);
        public Task<Result<CreatedStatus>> AddContractInsuredAddressAsync(string tenantId, string policyId, AddAddressCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddAddressCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddContractInsuredAddress", command);
        public Task<Result<CreatedStatus>> UpdateContractHolderAddressAsync(string tenantId, string policyId, UpdateAddressCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateAddressCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateContractHolderAddress", command);
        public Task<Result<CreatedStatus>> UpdateOtherContractHolderAddressAsync(string tenantId, string policyId, UpdateAddressCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateAddressCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateOtherContractHolderAddress", command);
        public Task<Result<CreatedStatus>> UpdateContractInsuredAddressAsync(string tenantId, string policyId, UpdateAddressCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, UpdateAddressCommand>($"{tenantId}/api/v1/policies/{policyId}/addUpdateContractInsuredAddress", command);
        public Task<Result<CreatedStatus>> RemoveContractHolderAddressAsync(string tenantId, string policyId, RemoveCommand command)
           => _client.GenericPostAsync<Result<CreatedStatus>, RemoveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemoveContractHolderAddress", command);
        public Task<Result<CreatedStatus>> RemoveOtherContractHolderAddressAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, RemoveEntityPrimitiveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemoveOtherContractHolderAddress", command);
        public Task<Result<CreatedStatus>> RemoveContractInsuredAddressAsync(string tenantId, string policyId, RemoveEntityPrimitiveCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, RemoveEntityPrimitiveCommand>($"{tenantId}/api/v1/policies/{policyId}/addRemoveContractInsuredAddress", command);

        public Task<Result> AddAssociatedContractToPolicyAsync(string tenantId, string policyId, AddAssociatedContractCommand command)
          => _client.GenericPostAsync<Result, AddAssociatedContractCommand>($"{tenantId}/api/v1/policies/{policyId}/associatedContracts/add", command);
        public Task<Result> RemoveAssociatedContractFromPolicyAsync(string tenantId, string policyId, RemoveAssociatedContractCommand command)
            => _client.GenericPostAsync<Result, RemoveAssociatedContractCommand>($"{tenantId}/api/v1/policies/{policyId}/associatedContracts/remove", command);

        public Task<Result<CreatedStatus>> AddAddClauseToEndorsementAsync(string tenantId, string policyId, AddClauseCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, AddClauseCommand>($"{tenantId}/api/v1/policies/{policyId}/addAddClause", command);

        public Task<Result> RemoveCommandFromEndorsementAsync(string tenantId, string policyId, RemoveCommand command)
            => _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/policies/{policyId}/endorsements/removeCommand", command);

        #endregion
        public Task<Result> InitializeTenantAsync(string tenantId, InitializeTenantPoliciesCommand command)
            => _client.GenericPostAsync<Result, InitializeTenantPoliciesCommand>($"{tenantId}/api/v1/policies/initializeTenant", command);

        public Task<Result> BatchIntegrateAsync(string tenantId, BatchIntegrateCommand command) =>
            _client.GenericPostAsync<Result, BatchIntegrateCommand>($"{tenantId}/api/v1/policies/batchIntegrate", command);

        public Task<Result> TriggerIssuerFunctionAsync(string tenantId, string policyId, TriggerIssuerFunctionCommand command) =>
            _client.GenericPostAsync<Result, TriggerIssuerFunctionCommand>($"{tenantId}/api/v1/policies/triggerIssuerFunction/{policyId}", command);

        public Task<Result> PolicyJacketBatchAsync(string tenantId, string policyId,
            PolicyJacketInstanceBatchCommand instanceBatch) =>
            _client.GenericPostAsync<Result, PolicyJacketInstanceBatchCommand>($"{tenantId}/api/v1/policies/{policyId}/jackets/batch", instanceBatch);

        public Task<Result> RemoveJacketFromPolicyAsync(string tenantId, string policyId,
            RemovePolicyJacketInstanceCommand instanceCommand) =>
            _client.GenericPostAsync<Result,RemovePolicyJacketInstanceCommand>($"{tenantId}/api/v1/policies/{policyId}/jackets/remove", instanceCommand);
        public Task<Result> UpdateEndorsementAsync(string tenantId, string policyId, UpdateEndorsementCommand command) => _client.GenericPostAsync<Result, UpdateEndorsementCommand>($"{tenantId}/api/v1/policies/{policyId}/endorsements/update", command);
    }
}