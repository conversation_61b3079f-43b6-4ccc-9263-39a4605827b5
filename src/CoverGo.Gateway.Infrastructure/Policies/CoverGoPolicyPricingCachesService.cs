﻿using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Clients;
using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Policies.PricingCache;

namespace CoverGo.Gateway.Infrastructure.Policies;

public class CoverGoPolicyPricingCachesService
{
    private readonly HttpClient _client;
    public CoverGoPolicyPricingCachesService(HttpClient client)
    {
        _client = client;
    }

    public Task<List<PolicyPricingCache>> Query(string tenantId, QueryArguments<Filter<PolicyPricingCacheFilter>> queryArguments, CancellationToken cancellationToken) => 
        _client.GenericPostAsync<List<PolicyPricingCache>, QueryArguments<Filter<PolicyPricingCacheFilter>>>($"{tenantId}/api/v1/policies/pricingCache/query", queryArguments, cancellationToken);

    public Task<long> Count(string tenantId, QueryArguments<Filter<PolicyPricingCacheFilter>> queryArguments, CancellationToken cancellationToken) => 
        _client.GenericPostAsync<long, QueryArguments<Filter<PolicyPricingCacheFilter>>>($"{tenantId}/api/v1/policies/pricingCache/count", queryArguments, cancellationToken);
}
