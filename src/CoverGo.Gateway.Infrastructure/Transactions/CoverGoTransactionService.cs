﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Transactions;
using CoverGo.Gateway.Domain.Users;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using EventLog = CoverGo.Gateway.Domain.EventLog;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Infrastructure.Transactions
{
    public class CoverGoTransactionService : ITransactionService
    {
        private readonly HttpClient _client;

        public CoverGoTransactionService(HttpClient client)
        {
            _client = client;
        }

        public Task<List<Transaction>> GetAsync(string tenantId, QueryArguments queryArguments) =>
            _client.GenericPostAsync<List<Transaction>, QueryArguments>($"{tenantId}/api/v1/transactions/query", queryArguments);

        public Task<long> GetTotalCountAsync(string tenantId, TransactionWhere where) =>
            _client.GenericPostAsync<long, TransactionWhere>($"{tenantId}/api/v1/transactions/totalcount", where);

        public Task<IEnumerable<EventLog>> GetEventsAsync(string tenantId, EventQuery eventQuery) =>
            _client.GenericPostAsync<IEnumerable<EventLog>, EventQuery>($"{tenantId}/api/v1/transactions/events", eventQuery);

        public Task<PaymentConfig> GetPaymentConfigAsync(string tenantId, string id) =>
            _client.GenericPostAsync<PaymentConfig, string>($"{tenantId}/api/v1/transactions/config", id);

        public Task<Result<CreatedStatus>> CreateAsync(string tenantId, CreateTransactionCommand command, string accessToken = null) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreateTransactionCommand>($"{tenantId}/api/v1/transactions/create?&accessToken={accessToken}", command);

        public Task<Result> UpdateAsync(string tenantId, UpdateTransactionCommand command) =>
            _client.GenericPostAsync<Result, UpdateTransactionCommand>($"{tenantId}/api/v1/transactions/update", command);

        public Task<Result> DeleteAsync(string tenantId, DeleteTransactionCommand command) =>
            _client.GenericPostAsync<Result, DeleteTransactionCommand>($"{tenantId}/api/v1/transactions/delete", command);

        public Task<Result> RefundAsync(string tenantId, RefundCommand command) =>
            _client.GenericPostAsync<Result, RefundCommand>($"{tenantId}/api/v1/transactions/refund", command);

        public Task<Result> SetStripeDestinationUserIdAsync(string tenantId, string authorizationCode) =>
            _client.GenericPostAsync<Result, StripeConnectCommand>($"{tenantId}/api/v1/transactions/stripeconnect", new StripeConnectCommand { AuthorizationCode = authorizationCode });


        public async Task<ILookup<string, Transaction>> GetByPolicyIdsLookupAsync(string tenantId, TransactionWhere where) =>
            (await GetAsync(tenantId, new QueryArguments { Where = where })).ToLookup(x => x.PolicyId);

        public async Task<ILookup<string, Transaction>> GetByEndorsementIdsLookupAsync(string tenantId, TransactionWhere where) =>
           (await GetAsync(tenantId, new QueryArguments { Where = where })).ToLookup(x => x.EndorsementId);

        public async Task<ILookup<string, Transaction>> GetByClaimIdsLookupAsync(string tenantId, TransactionWhere where) =>
            (await GetAsync(tenantId, new QueryArguments { Where = where })).ToLookup(x => x.ClaimId);

        public async Task<ILookup<string, Transaction>> GetByProposalIdsLookupAsync(string tenantId, TransactionWhere where) =>
            (await GetAsync(tenantId, new QueryArguments { Where = where })).ToLookup(x => x.ProposalId);

        public async Task<ILookup<string, Transaction>> GetBySubscriptionIdsLookupAsync(string tenantId, TransactionWhere where) =>
           (await GetAsync(tenantId, new QueryArguments { Where = where })).ToLookup(x => x.SubscriptionId);

        public Task<Result<TransactionRedirection>> InitializeAsync(string tenantId, InitializeTransactionCommand command) =>
            _client.GenericPostAsync<Result<TransactionRedirection>, InitializeTransactionCommand>($"{tenantId}/api/v1/transactions/initialize", command);

        public Task<Result> ProcessAsync(string tenantId, string transactionId, ProcessTransactionCommand command) =>
            _client.GenericPostAsync<Result, ProcessTransactionCommand>($"{tenantId}/api/v1/transactions/process/{transactionId}", command);

        public Task<Result<CreatedStatus>> AddFactAsync(string tenantId, string transactionId, AddFactCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, AddFactCommand>($"{tenantId}/api/v1/transactions/{transactionId}/facts/add", command);

        public Task<Result> UpdateFactAsync(string tenantId, string transactionId, UpdateFactCommand command) =>
            _client.GenericPostAsync<Result, UpdateFactCommand>($"{tenantId}/api/v1/transactions/{transactionId}/facts/update", command);

        public Task<Result> RemoveFactAsync(string tenantId, string transactionId, RemoveFactCommand command) =>
            _client.GenericPostAsync<Result, RemoveFactCommand>($"{tenantId}/api/v1/transactions/{transactionId}/facts/remove", command);

        public Task<Result> AddNoteAsync(string tenantId, string transactionId, AddNoteCommand command) =>
            _client.GenericPostAsync<Result, AddNoteCommand>($"{tenantId}/api/v1/transactions/{transactionId}/notes/add", command);

        public Task<Result> UpdateNoteAsync(string tenantId, string transactionId, UpdateNoteCommand command) =>
            _client.GenericPostAsync<Result, UpdateNoteCommand>($"{tenantId}/api/v1/transactions/{transactionId}/notes/update", command);

        public Task<Result> RemoveNoteAsync(string tenantId, string transactionId, RemoveNoteCommand command) =>
            _client.GenericPostAsync<Result, RemoveNoteCommand>($"{tenantId}/api/v1/transactions/{transactionId}/notes/remove", command);

        public Task<Result<string>> AddStakeholderToTransactionAsync(string tenantId, string transactionId, AddStakeholderCommand command)
            => _client.GenericPostAsync<Result<string>, AddStakeholderCommand>($"{tenantId}/api/v1/transactions/{transactionId}/stakeholders/add", command);
        public Task<Result> UpdateStakeholderOfTransactionAsync(string tenantId, string transactionId, UpdateStakeholderCommand command)
            => _client.GenericPostAsync<Result, UpdateStakeholderCommand>($"{tenantId}/api/v1/transactions/{transactionId}/stakeholders/update", command);
        public Task<Result> RemoveStakeholderFromTransactionAsync(string tenantId, string transactionId, RemoveStakeholderCommand command)
            => _client.GenericPostAsync<Result, RemoveStakeholderCommand>($"{tenantId}/api/v1/transactions/{transactionId}/stakeholders/remove", command);

        public Task<IEnumerable<PaymentMethod>> GetPaymentMethodsAsync(string tenantId, QueryArguments queryArguments) =>
            _client.GenericPostAsync<IEnumerable<PaymentMethod>, QueryArguments>($"{tenantId}/api/v1/paymentMethods/query", queryArguments);

        public async Task<IDictionary<string, PaymentMethod>> GetPaymentMethodDictionaryAsync(string tenantId, PaymentMethodWhere where) =>
            (await GetPaymentMethodsAsync(tenantId, new QueryArguments { Where = where })).ToDictionary(x => x.Id);

        public async Task<ILookup<string, PaymentMethod>> GetPaymentMethodsByEntityIdsLookupAsync(string tenantId, PaymentMethodWhere where) =>
            (await GetPaymentMethodsAsync(tenantId, new QueryArguments { Where = where })).ToLookup(x => x.EntityId);

        public Task<Result<CreatedStatus>> CreatePaymentMethodFromTokenAsync(string tenantId, CreatePaymentMethodFromTokenCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreatePaymentMethodFromTokenCommand>($"{tenantId}/api/v1/paymentMethods/createFromToken", command);
        public Task<Result<CreatedStatus>> CreateCardPaymentMethodAsync(string tenantId, CreateCardPaymentMethodCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreateCardPaymentMethodCommand>($"{tenantId}/api/v1/paymentMethods/card/create", command);
        public Task<Result<CreatedStatus>> CreateBankPaymentMethodAsync(string tenantId, CreateBankPaymentMethodCommand command) =>
        _client.GenericPostAsync<Result<CreatedStatus>, CreateBankPaymentMethodCommand>($"{tenantId}/api/v1/paymentMethods/bank/create", command);
        public Task<Result> DeletePaymentMethodAsync(string tenantId, string id, DeleteCommand command) =>
            _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/paymentMethods/{id}/delete", command);

        public Task<IEnumerable<Subscription>> GetSubscriptionsAsync(string tenantId, QueryArguments queryArguments) =>
            _client.GenericPostAsync<IEnumerable<Subscription>, QueryArguments>($"{tenantId}/api/v1/subscriptions/query", queryArguments);
        public async Task<IDictionary<string, Subscription>> GetSubscriptionsDictionaryAsync(string tenantId, SubscriptionWhere where) =>
            (await GetSubscriptionsAsync(tenantId, new QueryArguments { Where = where })).ToDictionary(x => x.Id);
        public async Task<ILookup<string, Subscription>> GetSubscriptionsByEntityIdsLookupAsync(string tenantId, SubscriptionWhere where) =>
            (await GetSubscriptionsAsync(tenantId, new QueryArguments { Where = where })).ToLookup(x => x.EntityId);

        public Task<Result<CreatedStatus>> CreateSubscriptionAsync(string tenantId, CreateSubscriptionCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreateSubscriptionCommand>($"{tenantId}/api/v1/subscriptions/create", command);
        public Task<Result> UpdateSubscriptionAsync(string tenantId, string subscriptionId, UpdateSubscriptionCommand command) =>
            _client.GenericPostAsync<Result, UpdateSubscriptionCommand>($"{tenantId}/api/v1/subscriptions/{subscriptionId}/update", command);
        public Task<Result> AddOfferToSubscriptionAsync(string tenantId, string subscriptionId, AddOfferCommand command) =>
            _client.GenericPostAsync<Result, AddOfferCommand>($"{tenantId}/api/v1/subscriptions/{subscriptionId}/offers/add", command);
        public Task<Result> UpdateOfferOfSubscriptionAsync(string tenantId, string subscriptionId, UpdateOfferCommand command) =>
           _client.GenericPostAsync<Result, UpdateOfferCommand>($"{tenantId}/api/v1/subscriptions/{subscriptionId}/offers/update", command);
        public Task<Result> RemoveOfferFromSubscriptionAsync(string tenantId, string subscriptionId, RemoveCommand command) =>
            _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/subscriptions/{subscriptionId}/offers/remove", command);
        public Task<Result> CancelSubscriptionAsync(string tenantId, string id, CancelSubscriptionCommand command) =>
          _client.GenericPostAsync<Result, CancelSubscriptionCommand>($"{tenantId}/api/v1/subscriptions/{id}/cancel", command);
        public Task<Result> DeleteSubscriptionAsync(string tenantId, string id, DeleteCommand command) =>
            _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/subscriptions/{id}/delete", command);

        public Task<Result> AddAttachment(string tenantId, string transactionId, AddAttachmentCommand command)
            => _client.GenericPostAsync<Result, AddAttachmentCommand>($"{tenantId}/api/v1/transactions/{transactionId}/attachments/add", command);

        public Task<Result> RemoveAttachment(string tenantId, string transactionId, RemoveAttachmentCommand command)
            => _client.GenericPostAsync<Result, RemoveAttachmentCommand>($"{tenantId}/api/v1/transactions/{transactionId}/attachments/remove", command);

        //TODO: needs spike
        //public Task<Result> RetrySubscriptionInvoicePaymentAsync(string tenantId, string id, RetrySubscriptionInvoicePaymentCommand command) =>
        //   _client.GenericPostAsync<Result, RetrySubscriptionInvoicePaymentCommand>($"{tenantId}/api/v1/subscriptions/{id}/invoices/retryPayment", command);

        public Task<Result> InitializeTenantAsync(string tenantId, InitializeTenantTransactionsCommand command) =>
            _client.GenericPostAsync<Result, InitializeTenantTransactionsCommand>($"{tenantId}/api/v1/transactions/initializeTenant", command);
    }
}
