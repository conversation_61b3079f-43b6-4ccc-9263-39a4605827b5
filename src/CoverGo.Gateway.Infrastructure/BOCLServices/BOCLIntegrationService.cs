using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Auth;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.BoclServices;

public class BoclIntegrationService
{
    private readonly IEnumerable<BoclCommand> _boclCommands;

    public BoclIntegrationService(IEnumerable<BoclCommand> boclCommands)
    {
        _boclCommands = boclCommands;
    }

    public async Task<Result<string>> ExecuteAsync(string tenantId, string commandType, JObject inputJson)
    {
        BoclCommand command = _boclCommands.FirstOrDefault(x => x.Name == commandType);

        if (command == null)
            return Result<string>.Failure($"No action for {commandType} found.");

        Result<string> result = await command.ExecuteAsync(tenantId, inputJson);
        return result;
    }

    public async Task<Result<string>> GetAuthToken(string tenantId, RemarkInputParam inputJson) =>
        await ExecuteAsync(tenantId, "getAuthToken", JObject.FromObject(inputJson));
}
