using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using CoverGo.Gateway.Domain.Users;
using System.Threading.Tasks;
using CoverGo.Gateway.Domain.Auth;
using CoverGo.Users.Domain.Individuals;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using GraphQL.Client.Http;
using CoverGo.Users.GraphQl.Client;
using GraphQL;
using GraphQL.Client.Abstractions.Utilities;

namespace CoverGo.Gateway.Infrastructure.BoclServices.BoclCommands;

public class BoclGetAuthTokenCommand : BoclCommand
{
    public override string Name => "getAuthToken";
    private readonly IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> _individualService;
    private readonly IAuthService _authService;
    private readonly ILogger<BoclGetAuthTokenCommand> _logger;
    private readonly GraphQLHttpClient _usersGraphQlHttpClient;

    public BoclGetAuthTokenCommand(IEntityService<Individual, CreateIndividualCommand, UpdateIndividualCommand> individualService,
        IAuthService authService,
        ILogger<BoclGetAuthTokenCommand> logger,
        GraphQLHttpClient usersGraphQlHttpClient)
    {
        _individualService = individualService;
        _authService = authService;
        _logger = logger;
        _usersGraphQlHttpClient = usersGraphQlHttpClient;
    }

    public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
    {
        RemarkInputParam param = inputJson.ToObject<RemarkInputParam>();
        Login login = await _authService.GetLoginByNameAsync(tenantId, param.UserId);
        
        if (login != null)
        {
            Token token = await _authService.GetInternalAccessTokenAsync(tenantId,
                    login.Id, param.ClientId, GetAdditionalClaims(param));
            Individual individual = (await _individualService.GetAsync(tenantId, new EntityWhere
            {
                Id = login.EntityId
            })).FirstOrDefault();

            if (individual.Integrations is null || !individual.Integrations.Any())
                await AddIntegrationInfo(login.EntityId, param, token);

            return Result<string>.Success(JsonConvert.SerializeObject(token));
        }
        else
        {
            Result<CreatedStatus> createIndividualResult = await CreateIndividual(tenantId);
            string individualId = createIndividualResult.IsSuccess ? createIndividualResult.Value.Id : null;
            
            if (individualId is null)
                return Result<string>.Success(JsonConvert.SerializeObject(new Token
                {
                    AccessToken = null,
                    Error = "Unable to create individual.",
                }));

            Result<CreatedStatus> createLoginResult = await CreateMemberLogin(tenantId, individualId, param);

            if (!createLoginResult.IsSuccess)
                return Result<string>.Success(JsonConvert.SerializeObject(new Token
                {
                    AccessToken = null,
                    Error = "Unable to create login.",
                }));

            Token token = await _authService.GetInternalAccessTokenAsync(tenantId,
                createLoginResult.Value.Id, param.ClientId, GetAdditionalClaims(param));
            await AddIntegrationInfo(individualId, param, token);
            
            return Result<string>.Success(JsonConvert.SerializeObject(token));
        }
    }

    private IDictionary<string, string> GetAdditionalClaims(RemarkInputParam input) =>
        new Dictionary<string, string>
        {
            {nameof(input.VoucherCode).ToCamelCase(), input.VoucherCode},
            {nameof(input.LangPref).ToCamelCase(), input.LangPref}
        };

    private async Task<Result<CreatedStatus>> CreateMemberLogin(string tenantId, string individualId, RemarkInputParam input)
    {
        Result<CreatedStatus> createLoginResult = await _authService.CreateLoginAsync(tenantId, new CreateLoginCommand
        {
            ClientId = input.ClientId,
            Username = input.UserId,
            AppIdsToBeGrantedAccessTo = new List<string> { input.ClientId },
            IsEmailConfirmed = true,
            IgnorePasswordValidation = true,
            EntityId = individualId,
            EntityType = EntityTypes.Individual.ToString().ToLowerInvariant()
        });

        if (!createLoginResult.IsSuccess)
            return createLoginResult;

        var permissionGroup = (await _authService.GetPermissionGroupsAsync(tenantId, new PermissionGroupWhere
        {
            Name = "Member"
        }))?.FirstOrDefault();

        if (permissionGroup == null)
            _logger.LogError("No Member group found");

        var addTargetPermissionResult = await _authService.AddTargettedPermissionsAsync(tenantId, createLoginResult.Value.Id, new List<AddTargettedPermissionCommand>
        {
            new()
            {
                Type = "groups",
                Value = permissionGroup.Id
            }
        });

        if (!addTargetPermissionResult.IsSuccess)
            _logger.LogError("Update login failed | {errors}", string.Join(", ", addTargetPermissionResult.Errors));

        return createLoginResult;
    }

    private async Task<Result<CreatedStatus>> CreateIndividual(string tenantId)
    {
        Result<CreatedStatus> createdIndividualResult = await _individualService.CreateAsync(tenantId, new CreateIndividualCommand
        {
            Type = IndividualTypes.Customer,
            EnglishFirstName = "",
            EnglishLastName = "",
            NameFormat = "{englishFirstName} {englishLastName}"
        });

        if (!createdIndividualResult.IsSuccess)
            _logger.LogError("Individual creation failed | {errors}", string.Join(", ", createdIndividualResult.Errors));

        return createdIndividualResult;
    }

    private async Task AddIntegrationInfo(string individualId, RemarkInputParam input, Token token)
    {
        string mutation =
            new _service_CoverGoMutationsRootBuilder()
                .individualMutationAddIntegration(new()
                {
                    command = new()
                    {
                        entityId = individualId,
                        systemId = "remark",
                        externalEntityId = input.UserId,
                        externalProductId = input.ProductId
                    }
                }, new users_ResultBuilder().WithAllFields())
                .Build();

        _usersGraphQlHttpClient.HttpClient.DefaultRequestHeaders.Remove("Authorization");
        _usersGraphQlHttpClient.HttpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", $"Bearer {token.AccessToken}");

        GraphQLResponse<JToken> result =
            await _usersGraphQlHttpClient.SendMutationAsync<JToken>(new GraphQLHttpRequest(mutation));
        
        _logger.LogInformation("Integration info addition result {result}", JsonConvert.SerializeObject(result));
    }
}