﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Scheduler;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.Scheduler
{
    public class CoverGoSchedulerService : ISchedulerService
    {
        public CoverGoSchedulerService(HttpClient client) => 
            _client = client;

        readonly HttpClient _client;

        public Task<IReadOnlyCollection<JobSchedule>> GetJobSchedules(string tenantId, QueryArguments<JobScheduleWhere> queryArguments) =>
            _client.GenericPostAsync<IReadOnlyCollection<JobSchedule>, QueryArguments<JobScheduleWhere>>($"{tenantId}/api/v1/scheduler/jobSchedules/filter", queryArguments);

        public Task<Result> CreateJobSchedule(string tenantId, JobSchedule jobSchedule) => 
            _client.GenericPostAsync<Result, JobSchedule>($"{tenantId}/api/v1/scheduler/jobSchedules/create", jobSchedule);

        public Task<Result> DeleteJobSchedule(string tenantId, string id) => 
            _client.GenericDeleteAsync<Result>($"{tenantId}/api/v1/scheduler/jobSchedules/{id}/delete");

        public Task<Result> TriggerJobScheduleNow(string tenantId, string id) =>
            _client.GenericPostAsync<Result, JobSchedule>($"{tenantId}/api/v1/scheduler/jobSchedules/{id}/trigger", default);
    }
}