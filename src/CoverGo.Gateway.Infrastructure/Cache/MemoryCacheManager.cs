﻿using System;
using System.Threading.Tasks;
using CoverGo.Gateway.Domain.Cache;
using Microsoft.Extensions.Caching.Memory;

namespace CoverGo.Gateway.Infrastructure.Cache;

public class MemoryCacheManager : ICacheManager
{
    private readonly IMemoryCache _memoryCache;

    public MemoryCacheManager(IMemoryCache memoryCache)
    {
        _memoryCache = memoryCache;
    }

    public async Task<T> GetOrSet<T>(string cacheKey, Func<Task<T>> getItemCallback, TimeSpan cacheDuration)
    {
        if (_memoryCache.TryGetValue(cacheKey, out T cachedItem)) return cachedItem;

        T item = await getItemCallback();

        if (item == null) return default;
        
        _memoryCache.Set(cacheKey, item, new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = cacheDuration
        });

        return item;
    }
}