using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.WfpServices
{
    public class WfpIntegrationService
    {
        private readonly IEnumerable<WfpCommand> _wfpCommands;

        public WfpIntegrationService(IEnumerable<WfpCommand> wfpCommands)
        {
            _wfpCommands = wfpCommands;
        }

        public async Task<Result<string>> ExecuteAsync(string tenantId, string commandType, JObject inputJson)
        {
            WfpCommand command = _wfpCommands.FirstOrDefault(x => x.Name == commandType);

            if (command != null)
                return await command.ExecuteAsync(tenantId, inputJson);

            return Result<string>.Failure($"No action for {commandType} found."); ;
        }
    }
}