using CoverGo.DomainUtils;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.WfpServices
{
    public abstract class WfpCommand
    {
        protected readonly IConfiguration _configuration;
        protected readonly ILogger<WfpCommand> _logger;
        public abstract string Name { get; }
        public abstract Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson);
        protected readonly TimeSpan _apiTimeout;
        protected WfpCommand(ILogger<WfpCommand> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _apiTimeout = configuration.GetValue<TimeSpan>("serviceTimeouts:wfp", TimeSpan.FromMilliseconds(10000));
        }

        protected virtual async Task<Result<string>> ExecuteAsync(string endpoint, RestRequest request)
        {
            try
            {
                RestClient client = new(endpoint)
                {
                    Timeout = (int)_apiTimeout.TotalMilliseconds,
                };

                IRestResponse response = await client.ExecuteAsync(request);
                if (response.IsSuccessful != true)
                    return response.ResponseStatus == ResponseStatus.TimedOut
                    ? Result<string>.Failure(
                        $"WFP API timeout (>{_apiTimeout.TotalSeconds}s). " +
                        "Status Code:{response.StatusCode}. " +
                        "ErrorException {response.ErrorException}. " + "ErrorMessage: {response.ErrorMessage}")
                    : new Result<string>
                    {
                        Status = "failure",
                        Value = response.Content,
                        Errors = new List<string>
                        {
                            response.ErrorMessage,
                            response.StatusDescription,
                        }
                    };

                return Result<string>.Success(response.Content);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WFP API integration error");
                return Result<string>.Failure(ex.Message);
            }
        }
    }
}