﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.WfpServices
{
    public class FaceRecongitionService
    {
        private readonly ILogger<FaceRecongitionService> _logger;
        private readonly IConfiguration _configuration;

        public FaceRecongitionService(ILogger<FaceRecongitionService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<Result<FaceRecognitionOutput>> AnalyzeAsync(string tenantId, FaceRecognitionInput input)
        {
            string lapetusBaseUrl = _configuration.GetSection(tenantId).GetValue<string>("LapetusBaseUrl", "chronos.uat.lapetussolutions.com");
            string lapetusVersion = _configuration.GetSection(tenantId).GetValue<string>("LapetusVersion", "v1");
            string lapetusApiKey = _configuration.GetSection(tenantId).GetValue<string>("LapetusApiKey", "tFrf474LtEKW04boBaJEHHzgKlsJWl4r");
            string lapetusOrgId = _configuration.GetSection(tenantId).GetValue<string>("LapetusOrgId", "wfp");
            string endpoint = $"https://{lapetusBaseUrl}/{lapetusVersion}/o/{lapetusOrgId}/selfie";

            using (var client = new HttpClient())
            {
                client.DefaultRequestHeaders.Add("Authorization", lapetusApiKey);

                // using (FileStream stream = File.OpenRead(Path.GetFullPath(Path.Combine(System.AppDomain.CurrentDomain.BaseDirectory, "0.jpg"))))
                using (var stream = new MemoryStream(input.Image))
                {
                    using (var content = new StreamContent(stream))
                    {
                        content.Headers.Add("Content-Type", "image/jpeg");
                        HttpResponseMessage httpResponse = await client.PostAsync(endpoint, content);

                        string responseContent = await httpResponse.Content.ReadAsStringAsync();

                        try
                        {
                            // Sometimes Lapetus responds with corrupted json
                            if (responseContent.EndsWith("\","))
                            {
                                responseContent = responseContent[..^1] + "}";
                            }

                            return new Result<FaceRecognitionOutput>
                            {
                                Status = "success",
                                Value = new FaceRecognitionOutput
                                {
                                    Value = JObject.Parse(responseContent)
                                }
                            };
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "error-ca4bf2fa: {response}", responseContent);
                            return Result<FaceRecognitionOutput>.Failure(ex.Message);
                        }
                    }
                }
            }
        }
    }
}
