using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Threading.Tasks;
using RestSharp;
using Microsoft.Extensions.Configuration;

namespace CoverGo.Gateway.Infrastructure.WfpServices.WfpServicesCommands
{
    public sealed class WealthSpanAssessment : WfpCommand
    {
        public override string Name => "wealthSpanAssessment";

        public WealthSpanAssessment(ILogger<WealthSpanAssessment> logger, IConfiguration configuration) : base(logger, configuration)
        {
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            string lapetusBaseUrl = _configuration.GetSection(tenantId).GetValue<string>("LapetusBaseUrl", "chronos.uat.lapetussolutions.com");
            string lapetusVersion = _configuration.GetSection(tenantId).GetValue<string>("LapetusVersion", "v1");
            string lapetusApiKey = _configuration.GetSection(tenantId).GetValue<string>("LapetusApiKey", "tFrf474LtEKW04boBaJEHHzgKlsJWl4r");
            string lapetusOrgId = _configuration.GetSection(tenantId).GetValue<string>("LapetusOrgId", "wfp");

            try
            {
                string input = inputJson.GetValue("lapetusInput")?.ToString(Formatting.None);
                RestRequest request = new(Method.POST);
                request.AddHeader("Authorization", lapetusApiKey);
                request.AddHeader("Accept", "application/json");
                request.AddHeader("Content-Type", "application/json");
                request.AddParameter("text/plain", input, ParameterType.RequestBody);

                return await ExecuteAsync($"https://{lapetusBaseUrl}/{lapetusVersion}/o/{lapetusOrgId}/ws", request);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WealthSpanAssessment failure. InputJson: {InputJson}", inputJson.ToString(Formatting.None));
                return Result<string>.Failure(ex.Message);
            }
        }
    }
}