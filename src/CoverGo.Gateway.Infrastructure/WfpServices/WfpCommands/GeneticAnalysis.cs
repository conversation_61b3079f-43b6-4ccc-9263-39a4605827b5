using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.IO;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using CoverGo.Gateway.Domain;
using RestSharp;
using Microsoft.Extensions.Configuration;
using JsonSerializer = Newtonsoft.Json.JsonSerializer;

namespace CoverGo.Gateway.Infrastructure.WfpServices.WfpServicesCommands
{
    public sealed class GeneticAnalysis : WfpCommand
    {
        public override string Name => "geneticAnalysis";

        public GeneticAnalysis(ILogger<GeneticAnalysis> logger, IConfiguration configuration) : base(logger, configuration)
        {
        }

        public override async Task<Result<string>> ExecuteAsync(string tenantId, JObject inputJson)
        {
            string lapetusBaseUrl = _configuration.GetSection(tenantId).GetValue<string>("LapetusBaseUrl", "chronos.uat.lapetussolutions.com");
            string lapetusVersion = _configuration.GetSection(tenantId).GetValue<string>("LapetusVersion", "v1");
            string lapetusApiKey = _configuration.GetSection(tenantId).GetValue<string>("LapetusApiKey", "tFrf474LtEKW04boBaJEHHzgKlsJWl4r");
            string lapetusOrgId = _configuration.GetSection(tenantId).GetValue<string>("LapetusOrgId", "wfp");
            string endpoint = $"https://{lapetusBaseUrl}/{lapetusVersion}/o/{lapetusOrgId}/genetic";

            try
            {
                string fileInput = inputJson.Value<string>("fileInput");
                byte[] input=JsonConvert.DeserializeObject<byte[]>(fileInput);
                using HttpClient client = new();
                client.DefaultRequestHeaders.Add("Authorization", lapetusApiKey);
                using MemoryStream stream = new(input);
                using StreamContent content = new(stream);
                content.Headers.Add("Content-Type", "image/jpeg");
                HttpResponseMessage httpResponse = await client.PostAsync(endpoint, content);
                string responseContent = await httpResponse.Content.ReadAsStringAsync();
                try
                {
                    // Sometimes Lapetus responds with corrupted json
                    if (responseContent.EndsWith("\","))
                    {
                        responseContent = responseContent[..^1] + "}";
                    }

                    return new Result<string>
                    {
                        Status = "success",
                        Value = responseContent
                    };
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "error-ca4bf2ga: {response}", responseContent);
                    return Result<string>.Failure(ex.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GeneticAnalysis failure. InputJson: {InputJson}", inputJson.ToString(Formatting.None));
                return Result<string>.Failure(ex.Message);
            }
        }
    }
}