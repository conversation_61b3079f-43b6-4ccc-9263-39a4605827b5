﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.FileSystem;

using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure.FileSystem
{
    public class CoverGoFileSystemService : IFileSystemService
    {
        private readonly HttpClient _client;

        public CoverGoFileSystemService(HttpClient client)
        {
            _client = client;
        }
        public Task<Result> InitializeTenantAsync(string tenantId, InitializeTenantFileSystemCommand command) =>
            _client.GenericPostAsync<Result, InitializeTenantFileSystemCommand>($"{tenantId}/api/v1/filesystem/initializeTenant", command);

        public Task<Result<IEnumerable<ObjectSummary>>> QueryAsync(string tenantId, string bucketName, FileWhere where) =>
            _client.GenericPostAsync<Result<IEnumerable<ObjectSummary>>, FileWhere>($"{tenantId}/api/v1/filesystem/query?bucketName={bucketName}", where);

        public Task<Result<ObjectListing>> ListAsync(string tenantId, string bucketName, ListFilesCommand command) =>
            _client.GenericPostAsync<Result<ObjectListing>, ListFilesCommand>($"{tenantId}/api/v1/filesystem/list?bucketName={bucketName}", command);

        public Task<Result<byte[]>> GetFileAsync(string tenantId, string bucketName, GetFileCommand command) =>
            _client.GenericPostAsync<Result<byte[]>, GetFileCommand>($"{tenantId}/api/v1/filesystem/get?bucketName={bucketName}", command);

        public Task<Result> UploadFileAsync(string tenantId, string bucketName, UploadFileCommand command) =>
            _client.GenericPostAsync<Result, UploadFileCommand>($"{tenantId}/api/v1/filesystem/upload?bucketName={bucketName}", command);

        public Task<Result> CopyFileAsync(string tenantId, string bucketName, CopyFileCommand command) =>
            _client.GenericPostAsync<Result, CopyFileCommand>($"{tenantId}/api/v1/filesystem/copy?bucketName={bucketName}", command);

        public Task<Result> DeleteFileAsync(string tenantId, string bucketName, DeleteFileCommand command) =>
            _client.GenericPostAsync<Result, DeleteFileCommand>($"{tenantId}/api/v1/filesystem/delete?bucketName={bucketName}", command);

        public Task<IEnumerable<FileSystemConfig>> GetConfigsAsync(string tenantId, FileSystemConfigWhere where) =>
            _client.GenericPostAsync<IEnumerable<FileSystemConfig>, FileSystemConfigWhere>($"{tenantId}/api/v1/filesystem/configs", where);

        public Task<IEnumerable<FileSystemConfig>> GetConfigsAsync(string tenantId, Domain.QueryArguments queryArguments) =>
            _client.GenericPostAsync<IEnumerable<FileSystemConfig>, Domain.QueryArguments>($"{tenantId}/api/v1/filesystem/configs/query", queryArguments);

        public Task<Result<CreatedStatus>> CreateConfigAsync(string tenantId, CreateFileSystemConfigCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreateFileSystemConfigCommand>($"{tenantId}/api/v1/filesystem/configs/create", command);

        public Task<Result> UpdateConfigAsync(string tenantId, string id, UpdateFileSystemConfigCommand command) =>
            _client.GenericPostAsync<Result, UpdateFileSystemConfigCommand>($"{tenantId}/api/v1/filesystem/configs/{id}/update", command);

        public Task<Result> DeleteConfigAsync(string tenantId, string id, DeleteCommand command) =>
            _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/filesystem/configs/{id}/delete", command);

        public Task<Result> CreateFolderAsync(string tenantId, string bucketName, CreateFolderCommand command) =>
            _client.GenericPostAsync<Result, CreateFolderCommand>($"{tenantId}/api/v1/filesystem/createFolder?bucketName={bucketName}", command);

        public Task<Result> DeleteFolderAsync(string tenantId, string bucketName, DeleteFolderCommand command) =>
            _client.GenericPostAsync<Result, DeleteFolderCommand>($"{tenantId}/api/v1/filesystem/deleteFolder?bucketName={bucketName}", command);

        public Task<Result<bool>> IsPublicAsync(string tenantId, string bucketName, IsPublicCommand command) =>
            _client.GenericPostAsync<Result<bool>, IsPublicCommand>($"{tenantId}/api/v1/filesystem/isPublic?bucketName={bucketName}", command);

        public Task<Result> SetAclAsync(string tenantId, string bucketName, SetAclCommand command) =>
            _client.GenericPostAsync<Result, SetAclCommand>($"{tenantId}/api/v1/filesystem/setAcl?bucketName={bucketName}", command);

        public Task<Result<string>> GenerateSharableUrlAsync(string tenantId, string bucketName, GenerateSharableUrlCommand command) =>
            _client.GenericPostAsync<Result<string>, GenerateSharableUrlCommand>($"{tenantId}/api/v1/filesystem/getSharableUrl?bucketName={bucketName}", command);

        public Task<Result> AddMetadataToFileAsync(string tenantId, string bucketName, AddMetadataToFileCommand command) =>
            _client.GenericPostAsync<Result, AddMetadataToFileCommand>($"{tenantId}/api/v1/filesystem/addMetaData?bucketName={bucketName}", command);

        public Task<Result> RemoveMetaDataFromFileAsync(string tenantId, string bucketName, RemoveMetadataFromFileCommand command) =>
            _client.GenericPostAsync<Result, RemoveMetadataFromFileCommand>($"{tenantId}/api/v1/filesystem/removeMetaData?bucketName={bucketName}", command);
        public Task<Result> LockFileAsync(string tenantId, string bucketName, LockFileCommand command) => 
            _client.GenericPostAsync<Result, LockFileCommand>($"{tenantId}/api/v1/filesystem/lock?bucketName={bucketName}", command);
        public Task<Result> UnlockFileAsync(string tenantId, string bucketName, UnlockFileCommand command) => 
            _client.GenericPostAsync<Result, UnlockFileCommand>($"{tenantId}/api/v1/filesystem/unlock?bucketName={bucketName}", command);
    }
}
