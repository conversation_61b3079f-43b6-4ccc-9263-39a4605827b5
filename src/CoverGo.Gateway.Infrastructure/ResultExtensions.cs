﻿using System;
using System.Collections.Generic;
using System.Linq;
using CoverGo.DomainUtils;

namespace CoverGo.Gateway.Infrastructure;

public static class ResultExtensions
{
    public static string GetAllErrors(this IResult result)
    {
        if (result == null || result.IsSuccess) return string.Empty;

        return GetAllErrors(result.Errors, result.Errors_2);
    }

    public static string GetAllErrors(IEnumerable<string> errors, IEnumerable<Error> errors2)
    {
        errors ??= Enumerable.Empty<string>();
        errors2 ??= Enumerable.Empty<Error>();
        errors = errors.Where(e => errors2.All(e2 => e2.Message != e));

        return string.Join(Environment.NewLine, errors.Union(errors2.Select(e => $"{e.Code}: {e.Message}")));
    }

}