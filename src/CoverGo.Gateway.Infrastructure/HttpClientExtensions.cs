﻿using CoverGo.Gateway.Domain.Policies;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Users.Domain.Companies;
using CoverGo.Users.Domain.Individuals;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Gateway.Infrastructure
{
    public static class HttpClientExtensions
    {
        private static readonly JsonSerializer s_serializer;
        private static readonly JsonSerializerSettings s_settings;

        static HttpClientExtensions()
        {
            s_settings = new JsonSerializerSettings
            {
                TypeNameHandling = TypeNameHandling.Auto,
                SerializationBinder = new KnownTypesBinder(),
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
            };

            s_settings.Converters.Add(new StringEnumConverter());

            s_serializer = JsonSerializer.Create(s_settings);
        }

        public static IHttpContextAccessor HttpContextAccessor { get; set; }

        public static async Task<T> GenericPostAsync<T, U>(this HttpClient client, string uri, U command)
        {
            using (HttpResponseMessage response = await client.GenericSendAsync(HttpMethod.Post, uri, command))
            {
                if (!response.IsSuccessStatusCode)
                    throw new ApiException
                    {
                        StatusCode = (int)response.StatusCode,
                        Content = await response.Content.ReadAsStringAsync(CancellationToken)
                    };

                return await response.Content.ReadAsJsonAsync<T>();
            }
        }

        public static async Task<T> GenericGetAsync<T>(this HttpClient client, string uri)
        {
            using (HttpResponseMessage response = await client.GenericSendAsync(HttpMethod.Get, uri, (object)null))
            {
                if (!response.IsSuccessStatusCode)
                    throw new ApiException
                    {
                        StatusCode = (int)response.StatusCode,
                        Content = await response.Content.ReadAsStringAsync(CancellationToken)
                    };

                return await response.Content.ReadAsJsonAsync<T>();
            }
        }

        public static T GenericGet<T>(this HttpClient client, string uri)
        {
            using (HttpResponseMessage response = client.GenericSend(HttpMethod.Get, uri, (object)null))
            {
                var stream = response.Content.ReadAsStream();
                if (!response.IsSuccessStatusCode)
                {
                    var reader = new StreamReader(stream);

                    throw new ApiException
                    {
                        StatusCode = (int)response.StatusCode,
                        Content = reader.ReadToEnd(),
                    };
                }

                return DeserializeFromStream<T>(stream);
            }
        }

        public static async Task<T> GenericGetAsync<T>(this HttpClient client, string uri, Func<HttpResponseMessage, Task<T>> onUnsuccessfulResponse)
        {
            using (HttpResponseMessage response = await client.GenericSendAsync(HttpMethod.Get, uri, (object)null))
            {
                return response.IsSuccessStatusCode ?
                    await response.Content.ReadAsJsonAsync<T>() :
                    await onUnsuccessfulResponse(response);
            }
        }

        public static async Task<T> GenericDeleteAsync<T>(this HttpClient client, string uri)
        {
            using (HttpResponseMessage response = await client.GenericSendAsync(HttpMethod.Delete, uri, (object)null))
            {
                if (!response.IsSuccessStatusCode)
                    throw new ApiException
                    {
                        StatusCode = (int)response.StatusCode,
                        Content = await response.Content.ReadAsStringAsync(CancellationToken)
                    };

                return await response.Content.ReadAsJsonAsync<T>();
            }
        }

        public static async Task<T> GenericDeleteAsync<T, U>(this HttpClient client, string uri, U command)
        {
            using (HttpResponseMessage response = await client.GenericSendAsync(HttpMethod.Delete, uri, command))
            {
                if (!response.IsSuccessStatusCode)
                    throw new ApiException
                    {
                        StatusCode = (int)response.StatusCode,
                        Content = await response.Content.ReadAsStringAsync()
                    };

                return await response.Content.ReadAsJsonAsync<T>();
            }
        }

        public static async Task<T> GenericPutAsync<T, U>(this HttpClient client, string uri, U command)
        {
            using (HttpResponseMessage response = await client.GenericSendAsync(HttpMethod.Put, uri, command))
            {
                if (!response.IsSuccessStatusCode)
                    throw new ApiException
                    {
                        StatusCode = (int)response.StatusCode,
                        Content = await response.Content.ReadAsStringAsync(CancellationToken)
                    };

                return await response.Content.ReadAsJsonAsync<T>();
            }
        }

        private static async Task<HttpResponseMessage> GenericSendAsync<T>(this HttpClient httpClient, HttpMethod method, string url, T data)
        {
            using (var request = new HttpRequestMessage()
            {
                RequestUri = new Uri(httpClient.BaseAddress + url),
                Method = method,
                Content = GetContent(data)
            })
            {
                if (HttpContextAccessor?.HttpContext?.Request?.Headers?.TryGetValue("Authorization", out var authorization) ?? false)
                {
                    request.Headers.TryAddWithoutValidation("Authorization", (IEnumerable<string>)authorization);
                }

                request.Headers.AcceptLanguage.TryParseAdd(CultureInfo.CurrentCulture.Name);
                HttpResponseMessage response = await httpClient.SendAsync(request, CancellationToken);
                return response;
            }
        }

        private static HttpResponseMessage GenericSend<T>(this HttpClient httpClient, HttpMethod method, string url, T data)
        {
            using (var request = new HttpRequestMessage()
                   {
                       RequestUri = new Uri(httpClient.BaseAddress + url),
                       Method = method,
                       Content = GetContent(data)
                   })
            {
                if (HttpContextAccessor.HttpContext.Request.Headers.TryGetValue("Authorization", out var authorization))
                {
                    request.Headers.TryAddWithoutValidation("Authorization", (IEnumerable<string>)authorization);
                }

                request.Headers.AcceptLanguage.TryParseAdd(CultureInfo.CurrentCulture.Name);
                HttpResponseMessage response = httpClient.Send(request);
                return response;
            }
        }

        public static async Task<T> ReadAsJsonAsync<T>(this HttpContent content)
        {
            using (Stream stream = await content.ReadAsStreamAsync(CancellationToken))
                return DeserializeFromStream<T>(stream);
        }

        private static T DeserializeFromStream<T>(Stream stream)
        {
            using (var sr = new StreamReader(stream))
            using (var jsonTextReader = new JsonTextReader(sr))
            {
                return s_serializer.Deserialize<T>(jsonTextReader);
            }
        }

        private static StringContent GetContent<T>(T data)
        {
            if (data == null)
            {
                return new StringContent("", Encoding.UTF8, "application/json");
            }

            string dataAsString = JsonConvert.SerializeObject(data, s_settings);
            var content = new StringContent(dataAsString);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            return content;
        }

        public class KnownTypesBinder : ISerializationBinder
        {
            public IList<Type> KnownTypes { get; } = new List<Type>
            {
                typeof(Individual),
                typeof(Internal),
                typeof(Company),
                typeof(CoverGo.Users.Domain.Objects.Object),
                typeof(Entity),
                typeof(PolicyWhere),
                typeof(TransactionWhere),
            };

            public Type BindToType(string assemblyName, string typeName) =>
                KnownTypes.SingleOrDefault(t => t.Name == typeName.Split('.')?.LastOrDefault());

            public void BindToName(Type serializedType, out string assemblyName, out string typeName)
            {
                assemblyName = null;
                typeName = serializedType.Name;
            }
        }

        static CancellationToken CancellationToken =>
            HttpContextAccessor?.HttpContext?.RequestAborted ?? CancellationToken.None;
    }

    public class ApiException : Exception
    {
        public int StatusCode { get; set; }
        public string Content { get; set; }
        public override string Message => $"StatusCode: {StatusCode}, Content: {Content}";
    }
}
