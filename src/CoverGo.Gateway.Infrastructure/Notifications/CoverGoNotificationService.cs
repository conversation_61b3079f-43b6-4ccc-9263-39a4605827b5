﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain.Notifications;
using CoverGo.Gateway.Domain.Products;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Infrastructure.Notifications
{
    public class CoverGoNotificationService : INotificationService
    {
        private readonly HttpClient _client;

        public CoverGoNotificationService(HttpClient client)
        {
            _client = client;
        }

        public Task<IEnumerable<Notification>> GetAsync(string tenantId, QueryArguments queryArguments) =>
            _client.GenericPostAsync<IEnumerable<Notification>, QueryArguments>($"{tenantId}/api/v1/notifications/query", queryArguments);

        public Task<long> GetTotalCountAsync(string tenantId, NotificationWhere where) =>
            _client.GenericPostAsync<long, NotificationWhere>($"{tenantId}/api/v1/notifications/totalCount", where);

        public Task<Result> DeleteAsync(string tenantId, string notificationId) =>
            _client.GenericDeleteAsync<Result>($"{tenantId}/api/v1/notifications/users/{notificationId}");

        public Task<Result> SendAsync(string tenantId, SendNotificationCommand command) =>
            _client.GenericPostAsync<Result, SendNotificationCommand>($"{tenantId}/api/v1/notifications/users", command);

        public Task UpdateStatusAsync(string tenantId, string notificationId, UpdateNotificationStatusCommand command) =>
            _client.GenericPutAsync<object, UpdateNotificationStatusCommand>($"{tenantId}/api/v1/notifications/users/{notificationId}", command);

        public Task SaveDeviceAsync(string tenantId, SaveDeviceCommand command) =>
            _client.GenericPostAsync<object, SaveDeviceCommand>($"{tenantId}/api/v1/notifications/devices", command);

        public Task<IEnumerable<NotificationTrigger>> GetNotificationTriggersAsync(string tenantId, NotificationTriggerWhere where) =>
            _client.GenericPostAsync<IEnumerable<NotificationTrigger>, NotificationTriggerWhere>($"{tenantId}/api/v1/notifications/triggers/filter", where);

        public Task<Result> CreateTriggerAsync(string tenantId, CreateTriggerCommand command) =>
            _client.GenericPostAsync<Result, CreateTriggerCommand>($"{tenantId}/api/v1/notifications/triggers", command);

        public Task<Result> DeleteTriggerAsync(string tenantId, string triggerId) =>
            _client.GenericDeleteAsync<Result>($"{tenantId}/api/v1/notifications/triggers/{triggerId}");

        
        public Task<Result> InitializeTenantAsync(string tenantId, InitializeTenantNotificationsCommand command) =>
            _client.GenericPostAsync<Result, InitializeTenantNotificationsCommand>($"{tenantId}/api/v1/notifications/initializeTenant", command);

        public Task<IEnumerable<NotificationConfig>> GetNotificationConfigsAsync (string tenantId, NotificationConfigWhere where) =>
            _client.GenericPostAsync<IEnumerable<NotificationConfig>, NotificationConfigWhere>($"{tenantId}/api/v1/notifications/configs/query", where);

        public Task<Result> CreateNotificationConfigAsync(string tenantId, CreateNotificationConfigCommand command) =>
            _client.GenericPostAsync<Result, CreateNotificationConfigCommand>($"{tenantId}/api/v1/notifications/configs", command);
        public Task<Result> DeleteNotificationConfigAsync(string tenantId, DeleteNotificationConfigCommand command) =>
            _client.GenericDeleteAsync<Result,DeleteNotificationConfigCommand>($"{tenantId}/api/v1/notifications/configs", command);

        public Task<IEnumerable<NotificationSubscription>> GetNotificationSubscriptionsAsync(string tenantId, QueryArguments queryArguments) =>
            _client.GenericPostAsync<IEnumerable<NotificationSubscription>, QueryArguments>($"{tenantId}/api/v1/notifications/subscriptions", queryArguments);
        public Task<long> GetNotificationSubscriptionsTotalCountAsync(string tenantId, NotificationSubscriptionWhere where) =>
            _client.GenericPostAsync<long, NotificationSubscriptionWhere>($"{tenantId}/api/v1/notifications/subscriptions/totalCount", where);
        public Task<Result<CreatedStatus>> CreateNotificationSubscriptionAsync(string tenantId, CreateNotificationSubscriptionCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, CreateNotificationSubscriptionCommand>($"{tenantId}/api/v1/notifications/subscriptions/create", command);
        public Task<Result> DeleteNotificationSubscriptionAsync(string tenantId, string id) =>
            _client.GenericDeleteAsync<Result>($"{tenantId}/api/v1/notifications/subscriptions/{id}/delete");
        public Task<Result> AddEntityToNotificationSubscriptionAsync(string tenantId, string id, AddEntityToNotificationSubscriptionCommand command) =>
            _client.GenericPostAsync<Result, AddEntityToNotificationSubscriptionCommand>($"{tenantId}/api/v1/notifications/subscriptions/{id}/addEntity", command);
        public Task<Result> RemoveEntityFromNotificationSubscriptionAsync(string tenantId, string id, RemoveEntityFromNotificationCommand command) =>
            _client.GenericPostAsync<Result, RemoveEntityFromNotificationCommand>($"{tenantId}/api/v1/notifications/subscriptions/{id}/removeEntity", command);

        public Task<Result<CreatedStatus>> AddTagToNotificationSubscriptionAsync(string tenantId, string id, AddTagCommand command) =>
            _client.GenericPostAsync<Result<CreatedStatus>, AddTagCommand>($"{tenantId}/api/v1/notifications/subscriptions/{id}/tags/add", command);
        public Task<Result> RemoveTagFromNotificationSubscriptionAsync(string tenantId, string id, RemoveCommand command) =>
            _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/notifications/subscriptions/{id}/tags/remove", command);

        public Task<ChatRoomUserList> GetUsersInRoomAsync(string tenantId, string id) =>
            _client.GenericGetAsync<ChatRoomUserList>($"{tenantId}/api/v1/notifications/chatRooms/{id}/users");
        public Task<Result> JoinChatRoomAsync(string tenantId, string id, JoinChatRoomCommand command) =>
            _client.GenericPostAsync<Result, JoinChatRoomCommand>($"{tenantId}/api/v1/notifications/chatRooms/{id}/join", command);
        public Task<Result> LeaveChatRoomAsync(string tenantId, string id, LeaveChatRoomCommand command) =>
            _client.GenericPostAsync<Result, LeaveChatRoomCommand>($"{tenantId}/api/v1/notifications/chatRooms/{id}/leave", command);
    }
}
