﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Domain;
using CoverGo.Gateway.Domain.Encryptions;
using CoverGo.Gateway.Domain.Users;
using CoverGo.Users.Domain.NegotiatedRate;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using EventLog = CoverGo.DomainUtils.EventLog;
using QueryArguments = CoverGo.Gateway.Domain.QueryArguments;

namespace CoverGo.Gateway.Infrastructure.Users
{
    public class CoverGoEntityService : IEntityService
    {
        private readonly HttpClient _client;

        public CoverGoEntityService(HttpClient client)
        {
            _client = client;
        }

        public Task<IEnumerable<Entity>> GenericQueryAsync(string tenantId, EntityWhere where) =>
            _client.GenericPostAsync<IEnumerable<Entity>, EntityWhere>($"{tenantId}/api/v1/entities/genericQuery", where);

        public Task<IEnumerable<string>> GenericQueryIds(string tenantId, EntityWhere where) =>
          _client.GenericPostAsync<IEnumerable<string>, EntityWhere>($"{tenantId}/api/v1/entities/genericQueryIds", where);

        public Task<IEnumerable<EntityId>> GenericQueryIdsAndTypesAsync(string tenantId, EntityWhere where) =>
          _client.GenericPostAsync<IEnumerable<EntityId>, EntityWhere>($"{tenantId}/api/v1/entities/genericQueryIdsAndTypes", where);

        public async Task<Result<CreatedStatus>> AddLinkAsync(string tenantId, AddLinkCommand command) =>
            await _client.GenericPostAsync<Result<CreatedStatus>, AddLinkCommand>($"{tenantId}/api/v1/entities/links", command);

        public async Task<Result<CreatedStatus>> AddLinksAsync(string tenantId, List<AddLinkCommand> commands) =>
            await _client.GenericPostAsync<Result<CreatedStatus>, List<AddLinkCommand>>($"{tenantId}/api/v1/entities/links/batches", commands);

        public async Task<Result<CreatedStatus>> AddAddressAsync(string tenantId, string entityId, EntityTypes type, AddAddressCommand command)
        {
            HttpResponseMessage response = await _client.PostAsync(
                $"{tenantId}/api/v1/entities/addresses/{type}/{entityId}",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result<CreatedStatus>>(json);
        }

        public async Task<Result> UpdateAddressAsync(string tenantId, string entityId, EntityTypes type, UpdateAddressCommand command)
        {
            HttpResponseMessage response = await _client.PutAsync(
                $"{tenantId}/api/v1/entities/addresses/{type}/{entityId}",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public async Task<Result> RemoveAddressAsync(string tenantId, string entityId, EntityTypes type, string id, string removedById)
        {
            HttpResponseMessage response = await _client.DeleteAsync(
                $"{tenantId}/api/v1/entities/addresses/{type}/{entityId}/{id}/{removedById}");

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public async Task<Result<CreatedStatus>> AddContactAsync(string tenantId, string entityId, EntityTypes type, AddContactCommand command)
        {
            HttpResponseMessage response = await _client.PostAsync(
                $"{tenantId}/api/v1/entities/contacts/{type}/{entityId}",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result<CreatedStatus>>(json);
        }

        public async Task<Result> UpdateContactAsync(string tenantId, string entityId, EntityTypes type, UpdateContactCommand command)
        {
            HttpResponseMessage response = await _client.PutAsync(
                $"{tenantId}/api/v1/entities/contacts/{type}/{entityId}",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public async Task<Result> RemoveContactAsync(string tenantId, string entityId, EntityTypes type, string id, string removedById)
        {
            HttpResponseMessage response = await _client.DeleteAsync(
                $"{tenantId}/api/v1/entities/contacts/{type}/{entityId}/{id}/{removedById}");

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public async Task<Result<CreatedStatus>> AddIdentityAsync(string tenantId, string entityId, EntityTypes type, AddIdentityCommand command)
        {
            HttpResponseMessage response = await _client.PostAsync(
                $"{tenantId}/api/v1/entities/identities/{type}/{entityId}",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result<CreatedStatus>>(json);
        }


        public async Task<Result> UpdateIdentityAsync(string tenantId, string entityId, EntityTypes type, UpdateIdentityCommand command)
        {
            HttpResponseMessage response = await _client.PutAsync(
                $"{tenantId}/api/v1/entities/identities/{type}/{entityId}",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public async Task<Result> RemoveIdentityAsync(string tenantId, string entityId, EntityTypes type, string id, string removedById)
        {
            HttpResponseMessage response = await _client.DeleteAsync(
                $"{tenantId}/api/v1/entities/identities/{type}/{entityId}/{id}/{removedById}");

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public async Task<Result<CreatedStatus>> AddFactAsync(string tenantId, string entityId, EntityTypes type, AddFactCommand command)
        {
            HttpResponseMessage response = await _client.PostAsync(
                $"{tenantId}/api/v1/entities/facts/{type}/{entityId}",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result<CreatedStatus>>(json);
        }

        public async Task<Result> UpdateFactAsync(string tenantId, string entityId, EntityTypes type, UpdateFactCommand command)
        {
            HttpResponseMessage response = await _client.PutAsync(
                $"{tenantId}/api/v1/entities/facts/{type}/{entityId}",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public async Task<Result> RemoveFactAsync(string tenantId, string entityId, EntityTypes type, string id, string removedById)
        {
            HttpResponseMessage response = await _client.DeleteAsync(
                $"{tenantId}/api/v1/entities/facts/{type}/{entityId}/{id}/{removedById}");

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public async Task<Result> FactBatchAsync(string tenantId, EntityTypes type, string entityId, FactCommandBatch batch) =>
            await _client.GenericPostAsync<Result, FactCommandBatch>($"{tenantId}/api/v1/entities/facts/{type}/{entityId}/batch", batch);

        public Task<IEnumerable<Relationships>> GetRelationshipsAsync(string tenantId, QueryArguments queryArguments) =>
          _client.GenericPostAsync<IEnumerable<Relationships>, QueryArguments>($"{tenantId}/api/v1/entities/links/query", queryArguments);

        public Task<long> GetRelationshipsTotalCountAsync(string tenantId, RelationshipWhere where) =>
         _client.GenericPostAsync<long, RelationshipWhere>($"{tenantId}/api/v1/entities/links/totalCount", where);

        
        public Task<Result> AddAttachmentAsync(string tenantId, string entityId, EntityTypes type, AddAttachmentCommand command) =>
            _client.GenericPostAsync<Result, AddAttachmentCommand>($"{tenantId}/api/v1/entities/attachments/{type}/{entityId}/add", command);

        
        public Task<Result> UpdateAttachmentAsync(string tenantId, string entityId, EntityTypes type, UpdateAttachmentCommand command) =>
            _client.GenericPostAsync<Result, UpdateAttachmentCommand>($"{tenantId}/api/v1/entities/attachments/{type}/{entityId}/update", command);

        
        public Task<Result> RemoveAttachmentAsync(string tenantId, string entityId, EntityTypes type, RemoveAttachmentCommand command) =>
            _client.GenericPostAsync<Result, RemoveAttachmentCommand>($"{tenantId}/api/v1/entities/attachments/{type}/{entityId}/remove", command);

        public async Task<Result> AddNoteAsync(string tenantId, string entityId, EntityTypes type, AddNoteCommand command)
        {
            HttpResponseMessage response = await _client.PostAsync(
                $"{tenantId}/api/v1/entities/notes/{type}/{entityId}",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public async Task<Result> UpdateNoteAsync(string tenantId, string entityId, EntityTypes type, UpdateNoteCommand command)
        {
            HttpResponseMessage response = await _client.PutAsync(
                $"{tenantId}/api/v1/entities/notes/{type}/{entityId}",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public async Task<Result> RemoveNoteAsync(string tenantId, string entityId, EntityTypes type, string id, string removedById)
        {
            HttpResponseMessage response = await _client.DeleteAsync(
                $"{tenantId}/api/v1/entities/notes/{type}/{entityId}/{id}/{removedById}");

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }

        public Task<Result> RemoveLinkAsync(string tenantId, string id, DeleteCommand command) =>
            _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/entities/links/{id}/remove", command);

        [Obsolete("delete with linkId instead")]
        public Task<Result> RemoveLinkAsync(string tenantId, RemoveLinkCommand command) =>
            _client.GenericDeleteAsync<Result>($"{tenantId}/api/v1/entities/links/{command.SourceId}/{command.Link}/{command.TargetId}/{command.RemovedById}");

        public async Task<Result> InitializeTenantEncryptionsAsync(string tenantId, InitializeTenantEncryptionsCommand command)
        {
            HttpResponseMessage response = await _client.PostAsync(
                $"{tenantId}/api/v1/entities/initializeTenantEncryptions",
                new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

            string json = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<Result>(json);
        }


        //=> _client.GenericPostAsync<Result, InitializeTenantEncryptionsCommand>($"{tenantId}/api/v1/entities/initializeTenantEncryptions", command);
        public Task<Result<CreatedStatus>> CreateServiceItemAsync(string tenantId, CreateServiceItemCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, CreateServiceItemCommand>($"{tenantId}/api/v1/ServiceItems", command);

        public Task<IEnumerable<ServiceItem>> GetServiceItemsAsync(string tenantId, ServiceItemWhere where)
             => _client.GenericPostAsync<IEnumerable<ServiceItem>, ServiceItemWhere>($"{tenantId}/api/v1/serviceItems/query", where);

        public Task<Result> UpdateServiceItemAsync(string tenantId, UpdateServiceItemCommand command)
            => _client.GenericPutAsync<Result, UpdateServiceItemCommand>($"{tenantId}/api/v1/ServiceItems", command);

        public Task<Result> DeleteServiceItemAsync(string tenantId, string ServiceItemId, DeleteCommand command)
            => _client.GenericDeleteAsync<Result, DeleteCommand>($"{tenantId}/api/v1/ServiceItems/{ServiceItemId}", command);

        public Task<Result<CreatedStatus>> CreateServiceItemAgreedFeeAsync(string tenantId, CreateServiceItemAgreedFeeCommand command)
            => _client.GenericPostAsync<Result<CreatedStatus>, CreateServiceItemAgreedFeeCommand>($"{tenantId}/api/v1/serviceItemAgreedFees", command);

        public Task<IEnumerable<ServiceItemAgreedFee>> GetServiceItemAgreedFeesAsync(string tenantId, ServiceItemAgreedFeeWhere where)
            => _client.GenericPostAsync<IEnumerable<ServiceItemAgreedFee>, ServiceItemAgreedFeeWhere>($"{tenantId}/api/v1/serviceItemAgreedFees/query", where);

        public Task<Result> UpdateServiceItemAgreedFeeAsync(string tenantId, UpdateServiceItemAgreedFeeCommand command)
            => _client.GenericPutAsync<Result, UpdateServiceItemAgreedFeeCommand>($"{tenantId}/api/v1/serviceItemAgreedFees", command);

        public Task<Result> DeleteServiceItemAgreedFeeAsync(string tenantId, string serviceItemAgreedFeeId, DeleteCommand command)
            => _client.GenericDeleteAsync<Result, DeleteCommand>($"{tenantId}/api/v1/serviceItemAgreedFees/{serviceItemAgreedFeeId}", command);

        public Task<long> GetTotalCountAsync(string tenantId, PanelProviderTierWhere where) =>
             _client.GenericPostAsync<long, PanelProviderTierWhere>($"{tenantId}/api/v1/panelProviderTiers/totalCount", where);

        public Task<IEnumerable<PanelProviderTier>> GetAsync(string tenantId, Domain.QueryArguments queryArguments) =>
             _client.GenericPostAsync<IEnumerable<PanelProviderTier>, Domain.QueryArguments>($"{tenantId}/api/v1/panelProviderTiers/query", queryArguments);

        public Task<List<EventLog>> GetEventsAsync(string tenantId, EventQuery query) =>
          _client.GenericPostAsync<List<EventLog>, EventQuery>($"{tenantId}/api/v1/panelProviderTiers/events", query);

        public Task<Result<CreatedStatus>> CreatePanelProviderTierAsync(string tenantId, PanelProviderTierCreateCommand command) =>
             _client.GenericPostAsync<Result<CreatedStatus>, PanelProviderTierCreateCommand>($"{tenantId}/api/v1/panelProviderTiers/create", command);

        public Task<Result> UpdatePanelProviderTierAsync(string tenantId, string panelProviderTierId, PanelProviderTierUpdateCommand command) =>
            _client.GenericPostAsync<Result, PanelProviderTierUpdateCommand>($"{tenantId}/api/v1/panelProviderTiers/update/{panelProviderTierId}", command);

        public Task<Result> DeletePanelProviderTierAsync(string tenantId, string panelProviderTierId, DeleteCommand command) =>
            _client.GenericPostAsync<Result, DeleteCommand>($"{tenantId}/api/v1/panelProviderTiers/delete/{panelProviderTierId}", command);

        public Task<Result> AddServiceItemAgreedFeeAsync(string tenantId, string panelProviderTierId, ServiceItemAgreedFeeAddCommand command) =>
            _client.GenericPostAsync<Result, ServiceItemAgreedFeeAddCommand>($"{tenantId}/api/v1/panelProviderTiers/{panelProviderTierId}/serviceItemAgreedFees/add", command);

        public Task<Result> RemoveServiceItemAgreedFeeAsync(string tenantId, string panelProviderTierId, ServiceItemAgreedFeeRemoveCommand command) =>
            _client.GenericPostAsync<Result, ServiceItemAgreedFeeRemoveCommand>($"{tenantId}/api/v1/panelProviderTiers/{panelProviderTierId}/serviceItemAgreedFees/remove", command);

        public Task<Result> ServiceItemAgreedFeeBatchAsync(string tenantId, string panelProviderTierId, ServiceItemAgreedFeeBatchCommand command) =>
            _client.GenericPostAsync<Result, ServiceItemAgreedFeeBatchCommand>($"{tenantId}/api/v1/panelProviderTiers/{panelProviderTierId}/serviceItemAgreedFees/batch", command);

        public async Task<ILookup<string, IEnumerable<ServiceItemAgreedFee>>> GetServiceItemAgreedFeesByTierIds(string tenantId, PanelProviderTierWhere where)
        {
            List<(string panelProviderTierId, IEnumerable<ServiceItemAgreedFee> addedServiceItemAgreedFee)> serviceItemAgreedFees =
                await GetServiceItemAgreedFeesByTierIds(tenantId, new QueryArguments {Where = where});
            return serviceItemAgreedFees.ToLookup(x => x.panelProviderTierId, x => x.addedServiceItemAgreedFee);
        }

        public async Task<List<(string panelProviderTierId, IEnumerable<ServiceItemAgreedFee> serviceItemAgreedFees)>> GetServiceItemAgreedFeesByTierIds(string tenantId, QueryArguments queryArguments)
            => await  _client.GenericPostAsync<List<(string panelProviderTierId, IEnumerable<ServiceItemAgreedFee> serviceItemAgreedFees)>, QueryArguments>($"{tenantId}/api/v1/panelProviderTiers/getServiceItemAgreedFeesByTiers", queryArguments);

        public Task<Result> AddAttachmentAsync(string tenantId, string panelProviderTierId, AddAttachmentCommand command)
            => _client.GenericPostAsync<Result, AddAttachmentCommand>($"{tenantId}/api/v1/panelProviderTiers/{panelProviderTierId}/attachments/add", command);

        public Task<Result> RemoveAttachmentAsync(string tenantId, string panelProviderTierId, RemoveAttachmentCommand command)
            => _client.GenericPostAsync<Result, RemoveAttachmentCommand>($"{tenantId}/api/v1/panelProviderTiers/{panelProviderTierId}/attachments/remove", command);
    }
}

