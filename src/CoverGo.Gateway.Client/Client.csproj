﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <Copyright>CoverGo</Copyright>
    <PackageProjectUrl>https://github.com/CoverGo/Gateway</PackageProjectUrl>
    <RepositoryUrl>https://github.com/CoverGo/Gateway</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <AssemblyName>CoverGo.Gateway.Client</AssemblyName>
    <RootNamespace>CoverGo.Gateway.Client</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" />
  </ItemGroup>

</Project>
