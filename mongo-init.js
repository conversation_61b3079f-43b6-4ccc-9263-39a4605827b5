db = db.getSiblingDB("auth");

db.createCollection("_tenants");
db.getCollection("_tenants").insert(
    { "tenantId": "fubon_uat" }
);

db.createCollection("fubon_uat-clients");
db.getCollection("fubon_uat-clients").insert(
    { "enabled": true, "clientId": "covergo_crm", "protocolType": "oidc", "clientSecrets": [], "requireClientSecret": false, "clientName": "", "requireConsent": true, "allowRememberConsent": true, "allowedGrantTypes": ["password"], "requirePkce": false, "allowPlainTextPkce": false, "allowAccessTokensViaBrowser": false, "redirectUris": [], "postLogoutRedirectUris": [], "frontChannelLogoutSessionRequired": true, "backChannelLogoutSessionRequired": true, "allowOfflineAccess": true, "allowedScopes": ["custom_profile", "all_user_claims"], "alwaysIncludeUserClaimsInIdToken": true, "identityTokenLifetime": 300, "accessTokenLifetime": 86400, "authorizationCodeLifetime": 300, "absoluteRefreshTokenLifetime": 2592000, "slidingRefreshTokenLifetime": 1296000, "refreshTokenUsage": "OneTimeOnly", "updateAccessTokenClaimsOnRefresh": false, "refreshTokenExpiration": "Absolute", "accessTokenType": "Jwt", "enableLocalLogin": true, "identityProviderRestrictions": [], "includeJwtId": false, "claims": [], "alwaysSendClientClaims": true, "clientClaimsPrefix": "client_", "deviceCodeLifetime": 300, "allowedCorsOrigins": [], "properties": { "email": "<EMAIL>", "senderName": "CoverGo" }, "requires2FA": false, "useNotificationConfig": false, "emailConfirmationTokenLifespan": "1.00:00:00" }
);

db.createCollection("fubon_uat-users");
db.getCollection("fubon_uat-users").insert(
    { "userName": "<EMAIL>", "normalizedUserName": "<EMAIL>", "securityStamp": "2JLCXJN67RBXOTTED3NMHQVZHHWUNNCJ", "email": "<EMAIL>", "normalizedEmail": "<EMAIL>", "emailConfirmed": true, "phoneNumberConfirmed": false, "twoFactorEnabled": false, "lockoutEnabled": false, "accessFailedCount": 0, "roles": [], "passwordHash": "AQAAAAEAACcQAAAAECN4dB0zdq+ug4QO47c8faaxUkp5fcmV7Q08x7UXUY2BatCtAzZahGoTmrg6BNyKSg==", "logins": [], "claims": [{ "type": "role", "value": "admin" }], "tokens": [], "isTemporaryPassword": false }
);