version: '2.4'

services:
  covergo-gateway:
    image: ghcr.io/covergo/gateway:master
    build:
      dockerfile: ./Dockerfile
      args:
        GH_ACCOUNT: ${GH_ACCOUNT}
        GH_TOKEN: ${GH_TOKEN}
      target: runtime
      context: .
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging.CI
      - datacenterId=covergo-dockerComposeOnJ<PERSON>kins-hk
      - PROXY_GRAPHQL_PRODUCTBUILDER_ENABLED=true
      - REDIS_CONNECTION_STRING=redis:6379
    ports:
      - "60060:8080" # To access localhost:60060/graphql
    depends_on:
      covergo-products:
        condition: service_started
      covergo-scripts:
        condition: service_started
      covergo-l10n:
        condition: service_started
      covergo-users:
        condition: service_started
      covergo-policies:
        condition: service_started
      covergo-pricing:
        condition: service_started
      covergo-templates:
        condition: service_started
      covergo-transactions:
        condition: service_started
      covergo-cases:
        condition: service_started
      covergo-notifications:
        condition: service_started
      covergo-auth:
        condition: service_started
      covergo-auth-health:
        condition: service_healthy
      covergo-filesystem-health:
        condition: service_healthy
      covergo-scheduler:
        condition: service_started
      covergo-claim-investigation:
        condition: service_started
      covergo-advisor:
        condition: service_started
      covergo-product-builder:
        condition: service_started
      covergo-fubonfps:
        condition: service_started
      covergo-claims:
        condition: service_started
      covergo-task-management:
        condition: service_started
      covergo-reference:
        condition: service_started
      redis:
        condition: service_started

  covergo-gateway-dapr:
    container_name: covergo-gateway-dapr
    image: "daprio/daprd:edge"
    network_mode: "service:covergo-gateway"
    command: ["./daprd",
              "--app-id", "covergo-gateway",
              "--app-port", "60060",
              "-resources-path", "/components"
    ]
    volumes:
      - "./dapr/components/:/components"
      - "./dapr/configuration/:/configuration"

  covergo-mongo:
    image: ghcr.io/covergo/gateway-mongo:latest
    restart: always
    build:
      dockerfile: ./Dockerfile
      context: .
      target: mongo-gateway
    environment:
      - MONGO_INITDB_ROOT_USERNAME=root
      - MONGO_INITDB_ROOT_PASSWORD=local_dev
      - MONGO_INITDB_DATABASE=auth
    ports:
      - 27017:27017

  eventstore:
    image: eventstore/eventstore:21.10.5-buster-slim
    container_name: eventstore
    restart: always
    healthcheck:
      test: [ "CMD-SHELL", "curl -sf http://localhost:2113/stats || exit 1" ]
      interval: 5s
      timeout: 2s
    environment:
      - EVENTSTORE_CLUSTER_SIZE=1
      - EVENTSTORE_RUN_PROJECTIONS=All
      - EVENTSTORE_START_STANDARD_PROJECTIONS=true
      - EVENTSTORE_EXT_TCP_PORT=1113
      - EVENTSTORE_HTTP_PORT=2113
      - EVENTSTORE_INSECURE=true
      - EVENTSTORE_ENABLE_EXTERNAL_TCP=true
      - EVENTSTORE_ENABLE_ATOM_PUB_OVER_HTTP=true
    ports:
      - "1113:1113"
      - "2113:2113"

  redis:
    image: redis:latest
    restart: always
    ports:
      - "6379:6379"

  oidc-token-test-service:
    image: spectare/oidc-token-test-service:latest
    container_name: oidc-token-test-service
    restart: always
    environment:
      - BIND=0.0.0.0
      - PORT=9090
      - EXPOSED_HOST=http://oidc-token-test-service:9090
    ports:
      - "9090:9090"

  covergo-gateway-tests:
    image: ghcr.io/covergo/gateway-test:latest
    build:
      dockerfile: ./Dockerfile
      args:
        GH_ACCOUNT: ${GH_ACCOUNT}
        GH_TOKEN: ${GH_TOKEN}
      context: .
      target: tests
    environment:
      - DATABASE_CONNECT_STRING=**************************************
      - GATEWAY_URL=http://covergo-gateway:8080/graphql
      - GATEWAY_INTEGRATION_TEST-GatewayGraphQLUrl=http://covergo-gateway:8080/graphql
      - GATEWAY_INTEGRATION_TEST-OidcTokenTestServiceUrl=http://oidc-token-test-service:9090
    depends_on:
      - covergo-gateway
      - oidc-token-test-service

  covergo-auth:
    image: ghcr.io/covergo/auth:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - isDeployment=true
      - datacenterId=developer-machine
      - DBCONFIG-providerId=mongoDb
      - DATABASE_CONNECT_STRING=**************************************
      - OTP_LOGIN_CIPHER_KEY=zaWgPou46nNmfMrYivTS2waJO9VKI277iLlPkGL56yc=
      - OTP_LOGIN_CIPHER_IV=94jCf53NO1acZ3pO7UE+gA==
      - OTP_LOGIN_HASHER_KEY=key
      - COVERGO_PASSWORD=V9K&KobcZO3
      - WAIT_HOSTS=covergo-mongo:27017
      - WAIT_TIMEOUT=90
      - PROMETHEUS_ENABLED=false
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-claims:
    image: ghcr.io/covergo/claims:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DATABASE_CONNECT_STRING=**************************************
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  #alpine-based asp.net core does not have neither apk neither curl\wget,
  #so need to replace it somehow
  covergo-auth-health:
    image: busybox
    entrypoint: ["sleep","100m"]
    healthcheck:
      test: "wget http://covergo-auth:8080/hc -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 100

  covergo-products:
    image: ghcr.io/covergo/products:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DATABASE_CONNECT_STRING=**************************************
    ports:
      - "8080"
    depends_on:
      - covergo-mongo
      - covergo-product-builder
      - covergo-filesystem
      - covergo-l10n

  covergo-scripts:
    image: ghcr.io/covergo/scripts:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DATABASE_CONNECT_STRING=**************************************
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-l10n:
    image: ghcr.io/covergo/l10n:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-users:
    image: ghcr.io/covergo/users:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-policies:
    image: ghcr.io/covergo/policies:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - isDeployment=true
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DATABASE_DRIVER=mongoDb
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
      - FeatureManagement__PersistIssuancePricing__EnabledFor__0__Parameters__Tenants__1=covergo_test
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-pricing:
    image: ghcr.io/covergo/pricing:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - isDeployment=true
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DATABASE_DRIVER=mongoDb
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-templates:
    image: ghcr.io/covergo/templates:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DATABASE_DRIVER=mongoDb
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
      - DISABLE_HANGFIRE=true
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-transactions:
    image: ghcr.io/covergo/transactions:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DATABASE_DRIVER=mongoDb
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-cases:
    image: ghcr.io/covergo/cases:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DATABASE_DRIVER=mongoDb
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
    ports:
      - "80"
    depends_on:
      - covergo-mongo

  covergo-notifications:
    image: ghcr.io/covergo/notifications:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=developer-machine
      - DATABASE_DRIVER=mongoDb
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-filesystem-health:
    image: busybox
    entrypoint: ["sleep","100m"]
    healthcheck:
      test: "wget http://covergo-filesystem:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 100

  covergo-filesystem:
    image: ghcr.io/covergo/filesystem:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=developer-machine
      - DATABASE_DRIVER=mongoDb
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-scheduler:
    image: ghcr.io/covergo/scheduler:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=developer-machine
      - DATABASE_DRIVER=mongoDb
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-fubonfps:
    image: ghcr.io/covergo/fubonfps:latest
    environment:
      - FPS_VALUE=123456789

  covergo-claim-investigation:
    image: ghcr.io/covergo/claim-investigation:latest
    environment:
      - EVENTSTORE_CONNECT_STRING=esdb://admin:changeit@eventstore?tls=false
      - DATABASE_CONNECT_STRING=**************************************
    ports:
      - "80"
    depends_on:
      - eventstore
      - covergo-mongo

  covergo-advisor:
    image: ghcr.io/covergo/advisor:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=developer-machine
      - DATABASE_DRIVER=mongoDb
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-product-builder:
    image: ghcr.io/covergo/product-builder:master
    environment:
      - EVENTSTORE_CONNECT_STRING=esdb://admin:changeit@eventstore?tls=false
      - DATABASE_CONNECT_STRING=**************************************
      - HOST_ENVIRONMENT=Staging
    ports:
      - "80"
    depends_on:
      - eventstore
      - covergo-mongo

  covergo-task-management:
    image: ghcr.io/covergo/task-management:master
    environment:
      - DATABASE_CONNECT_STRING=**************************************
      - DATABASE_DRIVER=mongo
      - HOST_ENVIRONMENT=Staging
      - WAIT_HOSTS=covergo-mongo:27017,covergo-auth:8080,covergo-reference:80
    ports:
      - "80"
    depends_on:
      - covergo-mongo
      - covergo-auth
      - covergo-reference

  covergo-reference:
    image: ghcr.io/covergo/reference:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - isDeployment=true
      - datacenterId=developer-machine
      - DATABASE_DRIVER=mongo
      - DATABASE_CONNECT_STRING=**************************************
      - WAIT_HOSTS=covergo-mongo:27017,covergo-auth:8080
    ports:
      - "80"
    depends_on:
      - covergo-mongo
